import{S as F,i as G,s as H,a as x,y as Z,z as A,g as I,Y as J,u as d,t as g,B as E,Z as D,a9 as K,j as M,a7 as m,Q as h,D as P,c as f,R as Q,e as p,f as S,q as b,r as z,h as v,P as C,V as w,W as y,X as R,T as N}from"./SpinnerAugment-BJ4-L7QR.js";import{B as O}from"./BaseButton-C6Dhmpxa.js";const U=s=>({}),T=s=>({}),_=s=>({}),V=s=>({});function W(s){let t,l;const c=s[10].iconLeft,o=C(c,s,s[20],V);return{c(){t=h("div"),o&&o.c(),f(t,"class","c-button--icon svelte-psg43r")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&w(o,c,i,i[20],l?R(c,i[20],a,_):y(i[20]),V)},i(i){l||(d(o,i),l=!0)},o(i){g(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function X(s){let t,l,c;return l=new N({props:{size:s[0],weight:s[1]==="ghost"?"regular":"medium",$$slots:{default:[tt]},$$scope:{ctx:s}}}),{c(){t=h("div"),Z(l.$$.fragment),f(t,"class","c-button--text svelte-psg43r")},m(o,i){p(o,t,i),A(l,t,null),c=!0},p(o,i){const a={};1&i&&(a.size=o[0]),2&i&&(a.weight=o[1]==="ghost"?"regular":"medium"),1048576&i&&(a.$$scope={dirty:i,ctx:o}),l.$set(a)},i(o){c||(d(l.$$.fragment,o),c=!0)},o(o){g(l.$$.fragment,o),c=!1},d(o){o&&v(t),E(l)}}}function tt(s){let t;const l=s[10].default,c=C(l,s,s[20],null);return{c(){c&&c.c()},m(o,i){c&&c.m(o,i),t=!0},p(o,i){c&&c.p&&(!t||1048576&i)&&w(c,l,o,o[20],t?R(l,o[20],i,null):y(o[20]),null)},i(o){t||(d(c,o),t=!0)},o(o){g(c,o),t=!1},d(o){c&&c.d(o)}}}function Y(s){let t,l;const c=s[10].iconRight,o=C(c,s,s[20],T);return{c(){t=h("div"),o&&o.c(),f(t,"class","c-button--icon svelte-psg43r")},m(i,a){p(i,t,a),o&&o.m(t,null),l=!0},p(i,a){o&&o.p&&(!l||1048576&a)&&w(o,c,i,i[20],l?R(c,i[20],a,U):y(i[20]),T)},i(i){l||(d(o,i),l=!0)},o(i){g(o,i),l=!1},d(i){i&&v(t),o&&o.d(i)}}}function it(s){let t,l,c,o,i,a=s[9].iconLeft&&W(s),r=s[9].default&&X(s),u=s[9].iconRight&&Y(s);return{c(){t=h("div"),a&&a.c(),l=P(),r&&r.c(),c=P(),u&&u.c(),f(t,"class",o=Q(`c-button--content c-button--size-${s[0]}`)+" svelte-psg43r")},m(n,$){p(n,t,$),a&&a.m(t,null),S(t,l),r&&r.m(t,null),S(t,c),u&&u.m(t,null),i=!0},p(n,$){n[9].iconLeft?a?(a.p(n,$),512&$&&d(a,1)):(a=W(n),a.c(),d(a,1),a.m(t,l)):a&&(b(),g(a,1,1,()=>{a=null}),z()),n[9].default?r?(r.p(n,$),512&$&&d(r,1)):(r=X(n),r.c(),d(r,1),r.m(t,c)):r&&(b(),g(r,1,1,()=>{r=null}),z()),n[9].iconRight?u?(u.p(n,$),512&$&&d(u,1)):(u=Y(n),u.c(),d(u,1),u.m(t,null)):u&&(b(),g(u,1,1,()=>{u=null}),z()),(!i||1&$&&o!==(o=Q(`c-button--content c-button--size-${n[0]}`)+" svelte-psg43r"))&&f(t,"class",o)},i(n){i||(d(a),d(r),d(u),i=!0)},o(n){g(a),g(r),g(u),i=!1},d(n){n&&v(t),a&&a.d(),r&&r.d(),u&&u.d()}}}function ot(s){let t,l;const c=[{size:s[0]},{variant:s[1]},{color:s[2]},{highContrast:s[3]},{disabled:s[4]},{loading:s[6]},{alignment:s[7]},{radius:s[5]},s[8]];let o={$$slots:{default:[it]},$$scope:{ctx:s}};for(let i=0;i<c.length;i+=1)o=x(o,c[i]);return t=new O({props:o}),t.$on("click",s[11]),t.$on("keyup",s[12]),t.$on("keydown",s[13]),t.$on("mousedown",s[14]),t.$on("mouseover",s[15]),t.$on("focus",s[16]),t.$on("mouseleave",s[17]),t.$on("blur",s[18]),t.$on("contextmenu",s[19]),{c(){Z(t.$$.fragment)},m(i,a){A(t,i,a),l=!0},p(i,[a]){const r=511&a?I(c,[1&a&&{size:i[0]},2&a&&{variant:i[1]},4&a&&{color:i[2]},8&a&&{highContrast:i[3]},16&a&&{disabled:i[4]},64&a&&{loading:i[6]},128&a&&{alignment:i[7]},32&a&&{radius:i[5]},256&a&&J(i[8])]):{};1049091&a&&(r.$$scope={dirty:a,ctx:i}),t.$set(r)},i(i){l||(d(t.$$.fragment,i),l=!0)},o(i){g(t.$$.fragment,i),l=!1},d(i){E(t,i)}}}function st(s,t,l){const c=["size","variant","color","highContrast","disabled","radius","loading","alignment"];let o=D(t,c),{$$slots:i={},$$scope:a}=t;const r=K(i);let{size:u=2}=t,{variant:n="solid"}=t,{color:$="accent"}=t,{highContrast:k=!1}=t,{disabled:B=!1}=t,{radius:L="medium"}=t,{loading:j=!1}=t,{alignment:q="center"}=t;return s.$$set=e=>{t=x(x({},t),M(e)),l(8,o=D(t,c)),"size"in e&&l(0,u=e.size),"variant"in e&&l(1,n=e.variant),"color"in e&&l(2,$=e.color),"highContrast"in e&&l(3,k=e.highContrast),"disabled"in e&&l(4,B=e.disabled),"radius"in e&&l(5,L=e.radius),"loading"in e&&l(6,j=e.loading),"alignment"in e&&l(7,q=e.alignment),"$$scope"in e&&l(20,a=e.$$scope)},[u,n,$,k,B,L,j,q,o,r,i,function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},function(e){m.call(this,s,e)},a]}class et extends F{constructor(t){super(),G(this,t,st,ot,H,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5,loading:6,alignment:7})}}export{et as B};
