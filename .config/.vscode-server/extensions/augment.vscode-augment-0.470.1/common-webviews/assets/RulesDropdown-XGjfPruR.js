import{S as H,i as j,s as L,w as q,x as I,y as d,z as m,A as B,u as p,t as u,B as g,n as A,C as T,D as S,e as h,h as y,E as Y,q as M,r as z,F as _,G as C,H as G}from"./SpinnerAugment-BJ4-L7QR.js";import{e as D}from"./BaseButton-C6Dhmpxa.js";import{D as w}from"./index-C-g0ZorP.js";import{B as k}from"./ButtonAugment-HnJOGilM.js";import{C as J}from"./chevron-down-B88L5wkj.js";function N(l,n,o){const e=l.slice();return e[13]=n[o],e}function K(l){let n,o=l[0]?"Always":"Manual";return{c(){n=C(o)},m(e,r){h(e,n,r)},p(e,r){1&r&&o!==(o=e[0]?"Always":"Manual")&&G(n,o)},d(e){e&&y(n)}}}function O(l){let n,o;return n=new J({props:{slot:"iconRight"}}),{c(){d(n.$$.fragment)},m(e,r){m(n,e,r),o=!0},p:A,i(e){o||(p(n.$$.fragment,e),o=!0)},o(e){u(n.$$.fragment,e),o=!1},d(e){g(n,e)}}}function P(l){let n,o;return n=new k({props:{color:l[0]?"accent":"neutral",size:1,$$slots:{iconRight:[O],default:[K]},$$scope:{ctx:l}}}),{c(){d(n.$$.fragment)},m(e,r){m(n,e,r),o=!0},p(e,r){const t={};1&r&&(t.color=e[0]?"accent":"neutral"),65537&r&&(t.$$scope={dirty:r,ctx:e}),n.$set(t)},i(e){o||(p(n.$$.fragment,e),o=!0)},o(e){u(n.$$.fragment,e),o=!1},d(e){g(n,e)}}}function Q(l){let n,o=l[13].label+"";return{c(){n=C(o)},m(e,r){h(e,n,r)},p:A,d(e){e&&y(n)}}}function E(l){let n,o;return n=new w.Item({props:{onSelect:function(){return l[8](l[13])},highlight:l[3].label===l[13].label,$$slots:{default:[Q]},$$scope:{ctx:l}}}),{c(){d(n.$$.fragment)},m(e,r){m(n,e,r),o=!0},p(e,r){l=e;const t={};8&r&&(t.highlight=l[3].label===l[13].label),65536&r&&(t.$$scope={dirty:r,ctx:l}),n.$set(t)},i(e){o||(p(n.$$.fragment,e),o=!0)},o(e){u(n.$$.fragment,e),o=!1},d(e){g(n,e)}}}function F(l){let n,o,e,r;return n=new w.Separator({}),e=new w.Label({props:{$$slots:{default:[U]},$$scope:{ctx:l}}}),{c(){d(n.$$.fragment),o=S(),d(e.$$.fragment)},m(t,i){m(n,t,i),h(t,o,i),m(e,t,i),r=!0},p(t,i){const a={};65560&i&&(a.$$scope={dirty:i,ctx:t}),e.$set(a)},i(t){r||(p(n.$$.fragment,t),p(e.$$.fragment,t),r=!0)},o(t){u(n.$$.fragment,t),u(e.$$.fragment,t),r=!1},d(t){t&&y(o),g(n,t),g(e,t)}}}function U(l){let n,o=(l[4]!==void 0?l[5][l[4]].description:l[3].description)+"";return{c(){n=C(o)},m(e,r){h(e,n,r)},p(e,r){24&r&&o!==(o=(e[4]!==void 0?e[5][e[4]].description:e[3].description)+"")&&G(n,o)},d(e){e&&y(n)}}}function V(l){let n,o,e,r=D(l[5]),t=[];for(let s=0;s<r.length;s+=1)t[s]=E(N(l,r,s));const i=s=>u(t[s],1,1,()=>{t[s]=null});let a=(l[4]!==void 0||l[3])&&F(l);return{c(){for(let s=0;s<t.length;s+=1)t[s].c();n=S(),a&&a.c(),o=Y()},m(s,c){for(let $=0;$<t.length;$+=1)t[$]&&t[$].m(s,c);h(s,n,c),a&&a.m(s,c),h(s,o,c),e=!0},p(s,c){if(104&c){let $;for(r=D(s[5]),$=0;$<r.length;$+=1){const b=N(s,r,$);t[$]?(t[$].p(b,c),p(t[$],1)):(t[$]=E(b),t[$].c(),p(t[$],1),t[$].m(n.parentNode,n))}for(M(),$=r.length;$<t.length;$+=1)i($);z()}s[4]!==void 0||s[3]?a?(a.p(s,c),24&c&&p(a,1)):(a=F(s),a.c(),p(a,1),a.m(o.parentNode,o)):a&&(M(),u(a,1,1,()=>{a=null}),z())},i(s){if(!e){for(let c=0;c<r.length;c+=1)p(t[c]);p(a),e=!0}},o(s){t=t.filter(Boolean);for(let c=0;c<t.length;c+=1)u(t[c]);u(a),e=!1},d(s){s&&(y(n),y(o)),_(t,s),a&&a.d(s)}}}function W(l){let n,o,e,r;return n=new w.Trigger({props:{$$slots:{default:[P]},$$scope:{ctx:l}}}),e=new w.Content({props:{side:"bottom",align:"start",$$slots:{default:[V]},$$scope:{ctx:l}}}),{c(){d(n.$$.fragment),o=S(),d(e.$$.fragment)},m(t,i){m(n,t,i),h(t,o,i),m(e,t,i),r=!0},p(t,i){const a={};65537&i&&(a.$$scope={dirty:i,ctx:t}),n.$set(a);const s={};65560&i&&(s.$$scope={dirty:i,ctx:t}),e.$set(s)},i(t){r||(p(n.$$.fragment,t),p(e.$$.fragment,t),r=!0)},o(t){u(n.$$.fragment,t),u(e.$$.fragment,t),r=!1},d(t){t&&y(o),g(n,t),g(e,t)}}}function X(l){let n,o,e,r;function t(s){l[9](s)}function i(s){l[10](s)}let a={$$slots:{default:[W]},$$scope:{ctx:l}};return l[2]!==void 0&&(a.requestClose=l[2]),l[1]!==void 0&&(a.focusedIndex=l[1]),n=new w.Root({props:a}),q.push(()=>I(n,"requestClose",t)),q.push(()=>I(n,"focusedIndex",i)),{c(){d(n.$$.fragment)},m(s,c){m(n,s,c),r=!0},p(s,[c]){const $={};65561&c&&($.$$scope={dirty:c,ctx:s}),!o&&4&c&&(o=!0,$.requestClose=s[2],B(()=>o=!1)),!e&&2&c&&(e=!0,$.focusedIndex=s[1],B(()=>e=!1)),n.$set($)},i(s){r||(p(n.$$.fragment,s),r=!0)},o(s){u(n.$$.fragment,s),r=!1},d(s){g(n,s)}}}function Z(l,n,o){let e,r,t=A,i=()=>(t(),t=T(x,f=>o(4,r=f)),x);l.$$.on_destroy.push(()=>t());let{onSave:a}=n,{alwaysApply:s}=n;const c={label:"Always",description:"Rules will always be followed, though individual context will still be respected."},$={label:"Manual",description:"Rules will only be followed when manually triggered. You can trigger them from the prompt by @ tagging the active file."},b=[c,$];let x;i();let v=()=>{};function R(f){o(0,s=f.label==="Always"),a(s),v()}return l.$$set=f=>{"onSave"in f&&o(7,a=f.onSave),"alwaysApply"in f&&o(0,s=f.alwaysApply)},l.$$.update=()=>{1&l.$$.dirty&&o(3,e=s?c:$)},[s,x,v,e,r,b,R,a,f=>R(f),function(f){v=f,o(2,v)},function(f){x=f,i(o(1,x))}]}class le extends H{constructor(n){super(),j(this,n,Z,X,L,{onSave:7,alwaysApply:0})}}export{le as R};
