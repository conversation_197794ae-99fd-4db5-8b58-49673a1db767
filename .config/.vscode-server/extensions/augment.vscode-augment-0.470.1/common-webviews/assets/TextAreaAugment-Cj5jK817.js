import{S as E,i as L,s as B,Q as _,y as H,c as j,e as k,z as T,u as M,t as Q,h as I,B as S,Z as b,a0 as Y,a as $,j as Z,a1 as w,a2 as u,a3 as g,a4 as p,a5 as q,g as A,a6 as C,a7 as f,w as D}from"./SpinnerAugment-BJ4-L7QR.js";import"./BaseButton-C6Dhmpxa.js";import{B as F}from"./index-C-g0ZorP.js";function G(e){let t,n,l,i,r=[{spellcheck:"false"},{class:n=`c-text-area__input c-base-text-input__input ${e[8]}`},e[7]],o={};for(let s=0;s<r.length;s+=1)o=$(o,r[s]);return{c(){t=_("textarea"),w(t,o),u(t,"c-textarea--resize-none",e[5]==="none"),u(t,"c-textarea--resize-both",e[5]==="both"),u(t,"c-textarea--resize-horizontal",e[5]==="horizontal"),u(t,"c-textarea--resize-vertical",e[5]==="vertical"),u(t,"svelte-17sbhhs",!0)},m(s,h){k(s,t,h),t.autofocus&&t.focus(),e[18](t),g(t,e[1]),l||(i=[p(t,"input",e[19]),q(e[9].call(null,t)),p(t,"click",e[10]),p(t,"focus",e[11]),p(t,"keydown",e[12]),p(t,"input",e[13]),p(t,"keyup",e[14]),p(t,"blur",e[15]),p(t,"select",e[16]),p(t,"mouseup",e[17])],l=!0)},p(s,h){w(t,o=A(r,[{spellcheck:"false"},256&h&&n!==(n=`c-text-area__input c-base-text-input__input ${s[8]}`)&&{class:n},128&h&&s[7]])),2&h&&g(t,s[1]),u(t,"c-textarea--resize-none",s[5]==="none"),u(t,"c-textarea--resize-both",s[5]==="both"),u(t,"c-textarea--resize-horizontal",s[5]==="horizontal"),u(t,"c-textarea--resize-vertical",s[5]==="vertical"),u(t,"svelte-17sbhhs",!0)},d(s){s&&I(t),e[18](null),l=!1,C(i)}}}function J(e){let t,n,l;return n=new F({props:{type:e[6],variant:e[2],size:e[3],color:e[4],$$slots:{default:[G]},$$scope:{ctx:e}}}),{c(){t=_("div"),H(n.$$.fragment),j(t,"class","c-text-area svelte-17sbhhs")},m(i,r){k(i,t,r),T(n,t,null),l=!0},p(i,[r]){const o={};64&r&&(o.type=i[6]),4&r&&(o.variant=i[2]),8&r&&(o.size=i[3]),16&r&&(o.color=i[4]),4194723&r&&(o.$$scope={dirty:r,ctx:i}),n.$set(o)},i(i){l||(M(n.$$.fragment,i),l=!0)},o(i){Q(n.$$.fragment,i),l=!1},d(i){i&&I(t),S(n)}}}function K(e,t,n){let l,i;const r=["variant","size","color","resize","textInput","type","value"];let o=b(t,r),{variant:s="surface"}=t,{size:h=2}=t,{color:d}=t,{resize:m="none"}=t,{textInput:c}=t,{type:y="default"}=t,{value:z=""}=t;function v(){if(!c)return;n(0,c.style.height="auto",c);const a=.8*window.innerHeight,x=Math.min(c.scrollHeight,a);n(0,c.style.height=`${x}px`,c),n(0,c.style.overflowY=c.scrollHeight>a?"auto":"hidden",c)}return Y(()=>{if(c){v();const a=()=>v();return window.addEventListener("resize",a),()=>{window.removeEventListener("resize",a)}}}),e.$$set=a=>{t=$($({},t),Z(a)),n(21,o=b(t,r)),"variant"in a&&n(2,s=a.variant),"size"in a&&n(3,h=a.size),"color"in a&&n(4,d=a.color),"resize"in a&&n(5,m=a.resize),"textInput"in a&&n(0,c=a.textInput),"type"in a&&n(6,y=a.type),"value"in a&&n(1,z=a.value)},e.$$.update=()=>{n(8,{class:l,...i}=o,l,(n(7,i),n(21,o)))},[c,z,s,h,d,m,y,i,l,function(a){v();const x=()=>v();return a.addEventListener("input",x),setTimeout(v,0),{destroy(){a.removeEventListener("input",x)}}},function(a){f.call(this,e,a)},function(a){f.call(this,e,a)},function(a){f.call(this,e,a)},function(a){f.call(this,e,a)},function(a){f.call(this,e,a)},function(a){f.call(this,e,a)},function(a){f.call(this,e,a)},function(a){f.call(this,e,a)},function(a){D[a?"unshift":"push"](()=>{c=a,n(0,c)})},function(){z=this.value,n(1,z)}]}class R extends E{constructor(t){super(),L(this,t,K,J,B,{variant:2,size:3,color:4,resize:5,textInput:0,type:6,value:1})}}export{R as T};
