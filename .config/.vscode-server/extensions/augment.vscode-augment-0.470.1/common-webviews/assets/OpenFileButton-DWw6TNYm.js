import{S as J,i as K,s as U,w as io,x as to,Q as N,y as S,c as P,e as D,z as V,A as lo,u as m,t as d,h as I,B as F,Z as M,a9 as oo,a as j,j as ao,E as no,q as _,r as A,a7 as h,g as eo,Y as so,P as L,V as z,W as T,X as b,D as H,n as co,ae as ro}from"./SpinnerAugment-BJ4-L7QR.js";import{C as uo,l as po,R as $o,O as fo}from"./open-in-new-window-DMlqLwqy.js";import"./BaseButton-C6Dhmpxa.js";import{T as mo}from"./Content-Czt02SJi.js";import{B as ho}from"./ButtonAugment-HnJOGilM.js";import{I as Co}from"./IconButtonAugment-Certjadv.js";import{T as go}from"./TextTooltipAugment-Bkzart3o.js";const yo=s=>({}),Q=s=>({}),ko=s=>({}),W=s=>({slot:"iconRight"}),vo=s=>({}),X=s=>({}),xo=s=>({}),Y=s=>({});function Oo(s){let o,e;const n=[s[7],{color:s[3]},{variant:s[6]}];let c={$$slots:{iconRight:[Do],iconLeft:[bo],default:[Lo]},$$scope:{ctx:s}};for(let i=0;i<n.length;i+=1)c=j(c,n[i]);return o=new ho({props:c}),o.$on("click",s[9]),o.$on("keyup",s[28]),o.$on("keydown",s[29]),o.$on("mousedown",s[30]),o.$on("mouseover",s[31]),o.$on("focus",s[32]),o.$on("mouseleave",s[33]),o.$on("blur",s[34]),o.$on("contextmenu",s[35]),{c(){S(o.$$.fragment)},m(i,t){V(o,i,t),e=!0},p(i,t){const a=200&t[0]?eo(n,[128&t[0]&&so(i[7]),8&t[0]&&{color:i[3]},64&t[0]&&{variant:i[6]}]):{};1030&t[0]|64&t[1]&&(a.$$scope={dirty:t,ctx:i}),o.$set(a)},i(i){e||(m(o.$$.fragment,i),e=!0)},o(i){d(o.$$.fragment,i),e=!1},d(i){F(o,i)}}}function wo(s){let o,e;const n=[s[7],{color:s[3]},{variant:s[6]}];let c={$$slots:{default:[Io]},$$scope:{ctx:s}};for(let i=0;i<n.length;i+=1)c=j(c,n[i]);return o=new Co({props:c}),o.$on("click",s[9]),o.$on("keyup",s[20]),o.$on("keydown",s[21]),o.$on("mousedown",s[22]),o.$on("mouseover",s[23]),o.$on("focus",s[24]),o.$on("mouseleave",s[25]),o.$on("blur",s[26]),o.$on("contextmenu",s[27]),{c(){S(o.$$.fragment)},m(i,t){V(o,i,t),e=!0},p(i,t){const a=200&t[0]?eo(n,[128&t[0]&&so(i[7]),8&t[0]&&{color:i[3]},64&t[0]&&{variant:i[6]}]):{};64&t[1]&&(a.$$scope={dirty:t,ctx:i}),o.$set(a)},i(i){e||(m(o.$$.fragment,i),e=!0)},o(i){d(o.$$.fragment,i),e=!1},d(i){F(o,i)}}}function Lo(s){let o;const e=s[19].default,n=L(e,s,s[37],null);return{c(){n&&n.c()},m(c,i){n&&n.m(c,i),o=!0},p(c,i){n&&n.p&&(!o||64&i[1])&&z(n,e,c,c[37],o?b(e,c[37],i,null):T(c[37]),null)},i(c){o||(m(n,c),o=!0)},o(c){d(n,c),o=!1},d(c){n&&n.d(c)}}}function zo(s){let o;const e=s[19].iconLeft,n=L(e,s,s[37],Q);return{c(){n&&n.c()},m(c,i){n&&n.m(c,i),o=!0},p(c,i){n&&n.p&&(!o||64&i[1])&&z(n,e,c,c[37],o?b(e,c[37],i,yo):T(c[37]),Q)},i(c){o||(m(n,c),o=!0)},o(c){d(n,c),o=!1},d(c){n&&n.d(c)}}}function To(s){let o,e;return o=new uo({}),{c(){S(o.$$.fragment)},m(n,c){V(o,n,c),e=!0},p:co,i(n){e||(m(o.$$.fragment,n),e=!0)},o(n){d(o.$$.fragment,n),e=!1},d(n){F(o,n)}}}function bo(s){let o,e,n,c;const i=[To,zo],t=[];function a(r,p){return r[10].iconLeft&&r[2]==="success"&&r[1]?0:1}return e=a(s),n=t[e]=i[e](s),{c(){o=N("svelte.fragment"),n.c(),P(o,"slot","iconLeft")},m(r,p){D(r,o,p),t[e].m(o,null),c=!0},p(r,p){let u=e;e=a(r),e===u?t[e].p(r,p):(_(),d(t[u],1,1,()=>{t[u]=null}),A(),n=t[e],n?n.p(r,p):(n=t[e]=i[e](r),n.c()),m(n,1),n.m(o,null))},i(r){c||(m(n),c=!0)},o(r){d(n),c=!1},d(r){r&&I(o),t[e].d()}}}function Do(s){let o;const e=s[19].iconRight,n=L(e,s,s[37],W);return{c(){n&&n.c()},m(c,i){n&&n.m(c,i),o=!0},p(c,i){n&&n.p&&(!o||64&i[1])&&z(n,e,c,c[37],o?b(e,c[37],i,ko):T(c[37]),W)},i(c){o||(m(n,c),o=!0)},o(c){d(n,c),o=!1},d(c){n&&n.d(c)}}}function Io(s){let o,e,n;const c=s[19].iconLeft,i=L(c,s,s[37],Y),t=s[19].default,a=L(t,s,s[37],null),r=s[19].iconRight,p=L(r,s,s[37],X);return{c(){i&&i.c(),o=H(),a&&a.c(),e=H(),p&&p.c()},m(u,f){i&&i.m(u,f),D(u,o,f),a&&a.m(u,f),D(u,e,f),p&&p.m(u,f),n=!0},p(u,f){i&&i.p&&(!n||64&f[1])&&z(i,c,u,u[37],n?b(c,u[37],f,xo):T(u[37]),Y),a&&a.p&&(!n||64&f[1])&&z(a,t,u,u[37],n?b(t,u[37],f,null):T(u[37]),null),p&&p.p&&(!n||64&f[1])&&z(p,r,u,u[37],n?b(r,u[37],f,vo):T(u[37]),X)},i(u){n||(m(i,u),m(a,u),m(p,u),n=!0)},o(u){d(i,u),d(a,u),d(p,u),n=!1},d(u){u&&(I(o),I(e)),i&&i.d(u),a&&a.d(u),p&&p.d(u)}}}function Ro(s){let o,e,n,c;const i=[wo,Oo],t=[];function a(r,p){return r[0]?0:1}return o=a(s),e=t[o]=i[o](s),{c(){e.c(),n=no()},m(r,p){t[o].m(r,p),D(r,n,p),c=!0},p(r,p){let u=o;o=a(r),o===u?t[o].p(r,p):(_(),d(t[u],1,1,()=>{t[u]=null}),A(),e=t[o],e?e.p(r,p):(e=t[o]=i[o](r),e.c()),m(e,1),e.m(n.parentNode,n))},i(r){c||(m(e),c=!0)},o(r){d(e),c=!1},d(r){r&&I(n),t[o].d(r)}}}function So(s){let o,e,n,c;function i(a){s[36](a)}let t={onOpenChange:s[8],content:s[5],triggerOn:[mo.Hover],$$slots:{default:[Ro]},$$scope:{ctx:s}};return s[4]!==void 0&&(t.requestClose=s[4]),e=new go({props:t}),io.push(()=>to(e,"requestClose",i)),{c(){o=N("div"),S(e.$$.fragment),P(o,"class","c-successful-button svelte-1dvyzw2")},m(a,r){D(a,o,r),V(e,o,null),c=!0},p(a,r){const p={};32&r[0]&&(p.content=a[5]),1231&r[0]|64&r[1]&&(p.$$scope={dirty:r,ctx:a}),!n&&16&r[0]&&(n=!0,p.requestClose=a[4],lo(()=>n=!1)),e.$set(p)},i(a){c||(m(e.$$.fragment,a),c=!0)},o(a){d(e.$$.fragment,a),c=!1},d(a){a&&I(o),F(e)}}}function Vo(s,o,e){let n,c,i;const t=["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","replaceIconOnSuccess"];let a=M(o,t),{$$slots:r={},$$scope:p}=o;const u=oo(r);let f,v,{defaultColor:x}=o,{tooltip:C}=o,{stateVariant:g}=o,{onClick:O}=o,{tooltipDuration:$=1500}=o,{icon:y=!1}=o,{stickyColor:w=!0}=o,{persistOnTooltipClose:R=!1}=o,{replaceIconOnSuccess:B=!1}=o,k="neutral",E=x,q=C==null?void 0:C.neutral;return s.$$set=l=>{o=j(j({},o),ao(l)),e(39,a=M(o,t)),"defaultColor"in l&&e(11,x=l.defaultColor),"tooltip"in l&&e(12,C=l.tooltip),"stateVariant"in l&&e(13,g=l.stateVariant),"onClick"in l&&e(14,O=l.onClick),"tooltipDuration"in l&&e(15,$=l.tooltipDuration),"icon"in l&&e(0,y=l.icon),"stickyColor"in l&&e(16,w=l.stickyColor),"persistOnTooltipClose"in l&&e(17,R=l.persistOnTooltipClose),"replaceIconOnSuccess"in l&&e(1,B=l.replaceIconOnSuccess),"$$scope"in l&&e(37,p=l.$$scope)},s.$$.update=()=>{e(18,{variant:n,...c}=a,n,(e(7,c),e(39,a))),270340&s.$$.dirty[0]&&e(6,i=(g==null?void 0:g[k])??n),2052&s.$$.dirty[0]&&e(3,E=k==="success"?"success":k==="failure"?"error":x)},[y,B,k,E,f,q,i,c,function(l){R||l||(clearTimeout(v),v=void 0,e(5,q=C==null?void 0:C.neutral),w||e(2,k="neutral"))},async function(l){try{e(2,k=await O(l)??"neutral")}catch{e(2,k="failure")}e(5,q=C==null?void 0:C[k]),clearTimeout(v),v=setTimeout(()=>{f==null||f(),w||e(2,k="neutral")},$)},u,x,C,g,O,$,w,R,n,r,function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){h.call(this,s,l)},function(l){f=l,e(4,f)},p]}class Fo extends J{constructor(o){super(),K(this,o,Vo,So,U,{defaultColor:11,tooltip:12,stateVariant:13,onClick:14,tooltipDuration:15,icon:0,stickyColor:16,persistOnTooltipClose:17,replaceIconOnSuccess:1},null,[-1,-1])}}const Po=s=>({}),Z=s=>({});function G(s){let o,e,n,c;return e=new Fo({props:{defaultColor:s[1],stickyColor:s[3],size:s[0],variant:s[2],tooltip:{neutral:"Open File In Editor",success:"Opening file..."},stateVariant:{success:"soft"},onClick:s[4],icon:!s[6].text,$$slots:{iconLeft:[qo],default:[jo]},$$scope:{ctx:s}}}),{c(){o=N("span"),S(e.$$.fragment),P(o,"class",n="c-open-file-button-container c-open-file-button__size--"+s[0]+" svelte-pdfhuj")},m(i,t){D(i,o,t),V(e,o,null),c=!0},p(i,t){const a={};2&t&&(a.defaultColor=i[1]),8&t&&(a.stickyColor=i[3]),1&t&&(a.size=i[0]),4&t&&(a.variant=i[2]),16&t&&(a.onClick=i[4]),64&t&&(a.icon=!i[6].text),2048&t&&(a.$$scope={dirty:t,ctx:i}),e.$set(a),(!c||1&t&&n!==(n="c-open-file-button-container c-open-file-button__size--"+i[0]+" svelte-pdfhuj"))&&P(o,"class",n)},i(i){c||(m(e.$$.fragment,i),c=!0)},o(i){d(e.$$.fragment,i),c=!1},d(i){i&&I(o),F(e)}}}function jo(s){let o;const e=s[10].text,n=L(e,s,s[11],Z);return{c(){n&&n.c()},m(c,i){n&&n.m(c,i),o=!0},p(c,i){n&&n.p&&(!o||2048&i)&&z(n,e,c,c[11],o?b(e,c[11],i,Po):T(c[11]),Z)},i(c){o||(m(n,c),o=!0)},o(c){d(n,c),o=!1},d(c){n&&n.d(c)}}}function qo(s){let o,e;return o=new fo({props:{slot:"iconLeft"}}),{c(){S(o.$$.fragment)},m(n,c){V(o,n,c),e=!0},p:co,i(n){e||(m(o.$$.fragment,n),e=!0)},o(n){d(o.$$.fragment,n),e=!1},d(n){F(o,n)}}}function No(s){let o,e,n=!s[5]&&G(s);return{c(){n&&n.c(),o=no()},m(c,i){n&&n.m(c,i),D(c,o,i),e=!0},p(c,[i]){c[5]?n&&(_(),d(n,1,1,()=>{n=null}),A()):n?(n.p(c,i),32&i&&m(n,1)):(n=G(c),n.c(),m(n,1),n.m(o.parentNode,o))},i(c){e||(m(n),e=!0)},o(c){d(n),e=!1},d(c){c&&I(o),n&&n.d(c)}}}function _o(s,o,e){let n,{$$slots:c={},$$scope:i}=o;const t=oo(c);let{path:a}=o,{start:r=0}=o,{stop:p=0}=o,{size:u=0}=o,{color:f="neutral"}=o,{variant:v="ghost-block"}=o,{stickyColor:x=!1}=o,{onOpenLocalFile:C=async function($){var w,R;if((w=$==null?void 0:$.stopPropagation)==null||w.call($),(R=$==null?void 0:$.preventDefault)==null||R.call($),!a)return;const y=await(g==null?void 0:g.extensionClient.resolvePath({rootPath:"",relPath:a}));return g==null||g.extensionClient.openFile({repoRoot:(y==null?void 0:y.repoRoot)??"",pathName:(y==null?void 0:y.pathName)??"",range:{start:Math.max(r,0),stop:Math.max(p,0)}}),"success"}}=o;const g=po(),O=ro($o.key);return s.$$set=$=>{"path"in $&&e(7,a=$.path),"start"in $&&e(8,r=$.start),"stop"in $&&e(9,p=$.stop),"size"in $&&e(0,u=$.size),"color"in $&&e(1,f=$.color),"variant"in $&&e(2,v=$.variant),"stickyColor"in $&&e(3,x=$.stickyColor),"onOpenLocalFile"in $&&e(4,C=$.onOpenLocalFile),"$$scope"in $&&e(11,i=$.$$scope)},e(5,n=!!(O!=null&&O.isActive)),[u,f,v,x,C,n,t,a,r,p,c,i]}class Yo extends J{constructor(o){super(),K(this,o,_o,No,U,{path:7,start:8,stop:9,size:0,color:1,variant:2,stickyColor:3,onOpenLocalFile:4})}}export{Yo as O,Fo as S};
