import{S as we,i as ye,s as Me,Q as G,y as C,D as F,c as H,a2 as T,e as _,z as S,f as O,u,q as E,t as g,r as q,h as R,B as k,ae as ne,af as J,a0 as Ce,a5 as V,ab as ge,F as Se,a6 as ke,n as D,C as K,w as xe,E as j}from"./SpinnerAugment-BJ4-L7QR.js";import{e as W,u as Le,o as _e}from"./BaseButton-C6Dhmpxa.js";import{G as Re,g as Ie,t as Ee,a as qe,M as ve,A as De,b as be,R as fe,c as Ae,S as Fe,d as Ne,e as He,f as je,h as Be,C as ze,i as Te,U as We,j as de,E as Ge,k as Ue,l as Pe}from"./RemoteAgentRetry-CoFGWWoM.js";import"./Content-Czt02SJi.js";import{R as Qe,S as X,i as re,a as Je,b as Ke,c as Oe,d as Ve,e as Xe,f as Ye,g as Ze,h as et,j as tt,k as nt,E as rt}from"./open-in-new-window-DMlqLwqy.js";import"./folder-BJI1Q8_7.js";import"./isObjectLike-DflaizF0.js";import{S as ot}from"./main-panel-CLAFkah5.js";import{aq as st,ar as at}from"./AugmentMessage-DIzdCIMv.js";import"./types-LfaCSdmF.js";import"./MaterialIcon-DIlB9c-0.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./Keybindings-4L2d2tRE.js";import"./pen-to-square-Bm4lF9Yl.js";import"./exclamation-triangle-Dn4fXX3v.js";import"./CardAugment-BxTO-shY.js";import"./TextTooltipAugment-Bkzart3o.js";import"./IconButtonAugment-Certjadv.js";import"./index-C-g0ZorP.js";import"./augment-logo-D_UKSkj8.js";import"./ButtonAugment-HnJOGilM.js";import"./expand--BB_Hn_b.js";import"./folder-opened-DzrGzNBt.js";import"./diff-utils-y96qaWKK.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-MyvMQzjq.js";import"./layer-group-CZFSGU8L.js";import"./github-C1PQK5DH.js";import"./types-a569v5Ol.js";import"./chat-types-NgqNgjwU.js";import"./globals-D0QH3NT1.js";import"./test_service_pb-B6vKXZrG.js";import"./file-paths-BcSg4gks.js";import"./types-BSMhNRWH.js";import"./TextAreaAugment-Cj5jK817.js";import"./design-system-init-DA68MSAy.js";import"./StatusIndicator-CAJYwjQb.js";import"./index-CGbmuyBX.js";import"./await_block-CvQ_3xaW.js";import"./ellipsis-BWy9xWah.js";import"./Filespan-BC4kxbfx.js";import"./lodash-ChYFUhWY.js";import"./terminal-BQIj5vJ0.js";import"./VSCodeCodicon-CvBJfpPi.js";import"./chat-flags-model-IiDhbRsI.js";import"./mcp-logo-B9nTLE-q.js";import"./IconFilePath-C-3qORpY.js";import"./LanguageIcon-BH9BM7T7.js";import"./next-edit-types-904A5ehg.js";import"./magnifying-glass-LWYs47rB.js";import"./chevron-down-B88L5wkj.js";function oe(o,t,n){const e=o.slice();e[36]=t[n],e[39]=n;const r=e[39]+1===e[11].length;return e[37]=r,e}function se(o,t,n){const e=o.slice();e[40]=t[n].turn,e[41]=t[n].idx;const r=e[41]+1===e[12].length;return e[42]=r,e}function ae(o){let t,n;return t=new De({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function lt(o){let t,n,e,r;const s=[dt,ft],m=[];function $(l,i){return l[16].enableRichCheckpointInfo?0:1}return t=$(o),n=m[t]=s[t](o),{c(){n.c(),e=j()},m(l,i){m[t].m(l,i),_(l,e,i),r=!0},p(l,i){let a=t;t=$(l),t===a?m[t].p(l,i):(E(),g(m[a],1,1,()=>{m[a]=null}),q(),n=m[t],n?n.p(l,i):(n=m[t]=s[t](l),n.c()),u(n,1),n.m(e.parentNode,e))},i(l){r||(u(n),r=!0)},o(l){g(n),r=!1},d(l){l&&R(e),m[t].d(l)}}}function it(o){let t,n;return t=new ze({props:{group:o[36],chatModel:o[1],turn:o[40],turnIndex:o[41],isLastTurn:o[42],messageListContainer:o[0]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.group=e[36]),2&r[0]&&(s.chatModel=e[1]),2048&r[0]&&(s.turn=e[40]),2048&r[0]&&(s.turnIndex=e[41]),6144&r[0]&&(s.isLastTurn=e[42]),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function mt(o){let t,n;return t=new Te({props:{stage:o[40].stage,iterationId:o[40].iterationId,stageCount:o[40].stageCount}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.stage=e[40].stage),2048&r[0]&&(s.iterationId=e[40].iterationId),2048&r[0]&&(s.stageCount=e[40].stageCount),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ut(o){let t,n;return t=new We({props:{chatModel:o[1],msg:o[40].response_text??""}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),2048&r[0]&&(s.msg=e[40].response_text??""),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ct(o){let t,n;return t=new st({props:{group:o[36],markdown:o[40].response_text??"",messageListContainer:o[0]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.group=e[36]),2048&r[0]&&(s.markdown=e[40].response_text??""),1&r[0]&&(s.messageListContainer=e[0]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function $t(o){let t,n;function e(){return o[29](o[40])}return t=new de({props:{turn:o[40],preamble:ot,resendTurn:e,$$slots:{default:[ht]},$$scope:{ctx:o}}}),{c(){C(t.$$.fragment)},m(r,s){S(t,r,s),n=!0},p(r,s){o=r;const m={};2048&s[0]&&(m.turn=o[40]),2052&s[0]&&(m.resendTurn=e),34816&s[0]|16384&s[1]&&(m.$$scope={dirty:s,ctx:o}),t.$set(m)},i(r){n||(u(t.$$.fragment,r),n=!0)},o(r){g(t.$$.fragment,r),n=!1},d(r){k(t,r)}}}function pt(o){let t,n;return t=new Ge({props:{flagsModel:o[13],turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};8192&r[0]&&(s.flagsModel=e[13]),2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function gt(o){let t,n;return t=new de({props:{turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ft(o){let t,n;return t=new Ue({props:{turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function dt(o){let t,n;return t=new Pe({props:{turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ht(o){let t,n;return t=new at({props:{conversationModel:o[15],turn:o[40]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};32768&r[0]&&(s.conversationModel=e[15]),2048&r[0]&&(s.turn=e[40]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function le(o){let t,n,e,r;function s(){return o[30](o[40])}return{c(){t=G("div"),H(t,"class","c-msg-list__turn-seen")},m(m,$){_(m,t,$),e||(r=V(n=Ae.call(null,t,{onSeen:s,track:o[40].seen_state!==X.seen})),e=!0)},p(m,$){o=m,n&&ge(n.update)&&2048&$[0]&&n.update.call(null,{onSeen:s,track:o[40].seen_state!==X.seen})},d(m){m&&R(t),e=!1,r()}}}function ie(o,t){let n,e,r,s,m,$,l,i,a,p,c,f,y,h,I=re(t[40]);const b=[gt,pt,$t,ct,ut,mt,it,lt],L=[];function N(w,M){return 2048&M[0]&&(e=null),2048&M[0]&&(r=null),2048&M[0]&&(s=null),2048&M[0]&&(m=null),2048&M[0]&&($=null),2048&M[0]&&(l=null),2048&M[0]&&(i=null),2048&M[0]&&(a=null),e==null&&(e=!!Je(w[40])),e?0:(r==null&&(r=!!Ke(w[40])),r?1:(s==null&&(s=!!Oe(w[40])),s?2:(m==null&&(m=!!Ve(w[40])),m?3:($==null&&($=!!Xe(w[40])),$?4:(l==null&&(l=!!Ye(w[40])),l?5:(i==null&&(i=!!(Ze(w[40])||et(w[40])||tt(w[40]))),i?6:(a==null&&(a=!(!nt(w[40])||w[40].status!==rt.success)),a?7:-1)))))))}~(p=N(t,[-1,-1]))&&(c=L[p]=b[p](t));let x=I&&le(t);return{key:o,first:null,c(){n=j(),c&&c.c(),f=F(),x&&x.c(),y=j(),this.first=n},m(w,M){_(w,n,M),~p&&L[p].m(w,M),_(w,f,M),x&&x.m(w,M),_(w,y,M),h=!0},p(w,M){let v=p;p=N(t=w,M),p===v?~p&&L[p].p(t,M):(c&&(E(),g(L[v],1,1,()=>{L[v]=null}),q()),~p?(c=L[p],c?c.p(t,M):(c=L[p]=b[p](t),c.c()),u(c,1),c.m(f.parentNode,f)):c=null),2048&M[0]&&(I=re(t[40])),I?x?x.p(t,M):(x=le(t),x.c(),x.m(y.parentNode,y)):x&&(x.d(1),x=null)},i(w){h||(u(c),h=!0)},o(w){g(c),h=!1},d(w){w&&(R(n),R(f),R(y)),~p&&L[p].d(w),x&&x.d(w)}}}function me(o){let t,n,e,r,s;const m=[kt,St,Ct,Mt,yt,wt],$=[];function l(a,p){return a[8]?0:a[5].retryMessage?1:a[5].showResumingRemoteAgent?2:a[5].showGeneratingResponse?3:a[5].showAwaitingUserInput?4:a[5].showStopped?5:-1}~(t=l(o))&&(n=$[t]=m[t](o));let i=o[5].showRunningSpacer&&ue();return{c(){n&&n.c(),e=F(),i&&i.c(),r=j()},m(a,p){~t&&$[t].m(a,p),_(a,e,p),i&&i.m(a,p),_(a,r,p),s=!0},p(a,p){let c=t;t=l(a),t===c?~t&&$[t].p(a,p):(n&&(E(),g($[c],1,1,()=>{$[c]=null}),q()),~t?(n=$[t],n?n.p(a,p):(n=$[t]=m[t](a),n.c()),u(n,1),n.m(e.parentNode,e)):n=null),a[5].showRunningSpacer?i||(i=ue(),i.c(),i.m(r.parentNode,r)):i&&(i.d(1),i=null)},i(a){s||(u(n),s=!0)},o(a){g(n),s=!1},d(a){a&&(R(e),R(r)),~t&&$[t].d(a),i&&i.d(a)}}}function wt(o){let t,n;return t=new Fe({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function yt(o){let t,n;return t=new Ne({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Mt(o){let t,n;return t=new He({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Ct(o){let t,n;return t=new je({}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p:D,i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function St(o){let t,n;return t=new Be({props:{message:o[5].retryMessage}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};32&r[0]&&(s.message=e[5].retryMessage),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function kt(o){let t,n;return t=new fe({props:{error:o[8].error,onRetry:o[8].onRetry,onDelete:o[8].onDelete}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};256&r[0]&&(s.error=e[8].error),256&r[0]&&(s.onRetry=e[8].onRetry),256&r[0]&&(s.onDelete=e[8].onDelete),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function ue(o){let t;return{c(){t=G("div"),H(t,"class","c-agent-running-spacer svelte-t9khzq")},m(n,e){_(n,t,e)},d(n){n&&R(t)}}}function xt(o){let t,n,e,r=[],s=new Map,m=W(o[36]);const $=i=>i[40].request_id??`no-request-id-${i[41]}`;for(let i=0;i<m.length;i+=1){let a=se(o,m,i),p=$(a);s.set(p,r[i]=ie(p,a))}let l=o[37]&&me(o);return{c(){for(let i=0;i<r.length;i+=1)r[i].c();t=F(),l&&l.c(),n=j()},m(i,a){for(let p=0;p<r.length;p+=1)r[p]&&r[p].m(i,a);_(i,t,a),l&&l.m(i,a),_(i,n,a),e=!0},p(i,a){8501255&a[0]&&(m=W(i[36]),E(),r=Le(r,a,$,1,i,m,s,t.parentNode,_e,ie,t,se),q()),i[37]?l?(l.p(i,a),2048&a[0]&&u(l,1)):(l=me(i),l.c(),u(l,1),l.m(n.parentNode,n)):l&&(E(),g(l,1,1,()=>{l=null}),q())},i(i){if(!e){for(let a=0;a<m.length;a+=1)u(r[a]);u(l),e=!0}},o(i){for(let a=0;a<r.length;a+=1)g(r[a]);g(l),e=!1},d(i){i&&(R(t),R(n));for(let a=0;a<r.length;a+=1)r[a].d(i);l&&l.d(i)}}}function ce(o){let t,n;return t=new be({props:{class:"c-msg-list__item--grouped",chatModel:o[1],isLastItem:o[37],userControlsScroll:o[3],requestId:o[36][0].turn.request_id,releaseScroll:o[31],messageListContainer:o[0],minHeight:o[37]?o[7]:0,$$slots:{default:[xt]},$$scope:{ctx:o}}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};2&r[0]&&(s.chatModel=e[1]),2048&r[0]&&(s.isLastItem=e[37]),8&r[0]&&(s.userControlsScroll=e[3]),2048&r[0]&&(s.requestId=e[36][0].turn.request_id),8&r[0]&&(s.releaseScroll=e[31]),1&r[0]&&(s.messageListContainer=e[0]),2176&r[0]&&(s.minHeight=e[37]?e[7]:0),112935&r[0]|16384&r[1]&&(s.$$scope={dirty:r,ctx:e}),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function $e(o){let t,n;return t=new fe({props:{error:o[8].error,onRetry:o[8].onRetry,onDelete:o[8].onDelete}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};256&r[0]&&(s.error=e[8].error),256&r[0]&&(s.onRetry=e[8].onRetry),256&r[0]&&(s.onDelete=e[8].onDelete),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function Lt(o){let t,n,e,r,s,m,$,l=o[9]&&ae(),i=W(o[11]),a=[];for(let f=0;f<i.length;f+=1)a[f]=ce(oe(o,i,f));const p=f=>g(a[f],1,1,()=>{a[f]=null});let c=!o[12].length&&o[8]&&$e(o);return{c(){t=G("div"),l&&l.c(),n=F();for(let f=0;f<a.length;f+=1)a[f].c();e=F(),c&&c.c(),H(t,"class","c-msg-list svelte-t9khzq"),T(t,"c-msg-list--minimal",!o[16].fullFeatured)},m(f,y){_(f,t,y),l&&l.m(t,null),O(t,n);for(let h=0;h<a.length;h+=1)a[h]&&a[h].m(t,null);O(t,e),c&&c.m(t,null),o[32](t),s=!0,m||($=[V(Ee.call(null,t,{onScrollIntoBottom:o[20],onScrollAwayFromBottom:o[21],onScroll:o[33]})),V(r=qe.call(null,t,{onHeightChange:o[34]}))],m=!0)},p(f,y){if(f[9]?l?512&y[0]&&u(l,1):(l=ae(),l.c(),u(l,1),l.m(t,n)):l&&(E(),g(l,1,1,()=>{l=null}),q()),8501679&y[0]){let h;for(i=W(f[11]),h=0;h<i.length;h+=1){const I=oe(f,i,h);a[h]?(a[h].p(I,y),u(a[h],1)):(a[h]=ce(I),a[h].c(),u(a[h],1),a[h].m(t,e))}for(E(),h=i.length;h<a.length;h+=1)p(h);q()}!f[12].length&&f[8]?c?(c.p(f,y),4352&y[0]&&u(c,1)):(c=$e(f),c.c(),u(c,1),c.m(t,null)):c&&(E(),g(c,1,1,()=>{c=null}),q()),r&&ge(r.update)&&16&y[0]&&r.update.call(null,{onHeightChange:f[34]}),(!s||65536&y[0])&&T(t,"c-msg-list--minimal",!f[16].fullFeatured)},i(f){if(!s){u(l);for(let y=0;y<i.length;y+=1)u(a[y]);u(c),s=!0}},o(f){g(l),a=a.filter(Boolean);for(let y=0;y<a.length;y+=1)g(a[y]);g(c),s=!1},d(f){f&&R(t),l&&l.d(),Se(a,f),c&&c.d(),o[32](null),m=!1,ke($)}}}function pe(o){let t,n;return t=new ve({props:{messageListElement:o[0],showScrollDown:o[6]}}),{c(){C(t.$$.fragment)},m(e,r){S(t,e,r),n=!0},p(e,r){const s={};1&r[0]&&(s.messageListElement=e[0]),64&r[0]&&(s.showScrollDown=e[6]),t.$set(s)},i(e){n||(u(t.$$.fragment,e),n=!0)},o(e){g(t.$$.fragment,e),n=!1},d(e){k(t,e)}}}function _t(o){let t,n,e,r;n=new Re({props:{$$slots:{default:[Lt]},$$scope:{ctx:o}}});let s=o[10]&&pe(o);return{c(){t=G("div"),C(n.$$.fragment),e=F(),s&&s.c(),H(t,"class","c-msg-list-container svelte-t9khzq"),H(t,"data-testid","chat-message-list"),T(t,"c-msg-list--minimal",!o[16].fullFeatured)},m(m,$){_(m,t,$),S(n,t,null),O(t,e),s&&s.m(t,null),r=!0},p(m,$){const l={};113599&$[0]|16384&$[1]&&(l.$$scope={dirty:$,ctx:m}),n.$set(l),m[10]?s?(s.p(m,$),1024&$[0]&&u(s,1)):(s=pe(m),s.c(),u(s,1),s.m(t,null)):s&&(E(),g(s,1,1,()=>{s=null}),q()),(!r||65536&$[0])&&T(t,"c-msg-list--minimal",!m[16].fullFeatured)},i(m){r||(u(n.$$.fragment,m),u(s),r=!0)},o(m){g(n.$$.fragment,m),g(s),r=!1},d(m){m&&R(t),k(n),s&&s.d()}}}function Rt(o,t,n){let e,r,s,m,$,l,i,a,p,c,f,y,h,I,b,L,N,x=D,w=D,M=()=>(w(),w=K(B,d=>n(28,L=d)),B),v=D;o.$$.on_destroy.push(()=>x()),o.$$.on_destroy.push(()=>w()),o.$$.on_destroy.push(()=>v());let{chatModel:B}=t;M();let{onboardingWorkspaceModel:U}=t,{msgListElement:z}=t;const he=ne("agentConversationModel"),{agentExchangeStatus:Y,isCurrConversationAgentic:Z}=he;J(o,Y,d=>n(27,b=d)),J(o,Z,d=>n(26,I=d));const ee=ne(Qe.key);J(o,ee,d=>n(25,h=d));let A=!1;function P(){n(3,A=!0)}Ce(()=>{var d;((d=y.lastExchange)==null?void 0:d.seen_state)===X.unseen&&P()});let Q=0;const te=d=>y.markSeen(d);return o.$$set=d=>{"chatModel"in d&&M(n(1,B=d.chatModel)),"onboardingWorkspaceModel"in d&&n(2,U=d.onboardingWorkspaceModel),"msgListElement"in d&&n(0,z=d.msgListElement)},o.$$.update=()=>{268435456&o.$$.dirty[0]&&(n(14,e=L.currentConversationModel),x(),x=K(e,d=>n(15,y=d))),268435456&o.$$.dirty[0]&&(n(13,r=L.flags),v(),v=K(r,d=>n(16,N=d))),503316480&o.$$.dirty[0]&&n(24,s=Ie(L,b,I,h)),16777216&o.$$.dirty[0]&&n(12,m=s.chatHistory),16777216&o.$$.dirty[0]&&n(11,$=s.groupedChatHistory),16777216&o.$$.dirty[0]&&n(5,l=s.lastGroupConfig),16777216&o.$$.dirty[0]&&n(10,i=s.doShowFloatingButtons),16777216&o.$$.dirty[0]&&n(9,a=s.doShowAgentSetupLogs),32&o.$$.dirty[0]&&n(8,p=l.remoteAgentErrorConfig),16&o.$$.dirty[0]&&n(7,c=Q),8&o.$$.dirty[0]&&n(6,f=A)},[z,B,U,A,Q,l,f,c,p,a,i,$,m,r,e,y,N,Y,Z,ee,function(){n(3,A=!1)},function(){n(3,A=!0)},P,te,s,h,I,b,L,d=>U.retryProjectSummary(d),d=>te(d),()=>n(3,A=!0),function(d){xe[d?"unshift":"push"](()=>{z=d,n(0,z)})},d=>{d<=1&&P()},d=>n(4,Q=d)]}class In extends we{constructor(t){super(),ye(this,t,Rt,_t,Me,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{In as default};
