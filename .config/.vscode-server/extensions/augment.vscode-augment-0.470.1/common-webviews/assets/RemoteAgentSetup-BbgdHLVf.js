import{S as se,i as ce,s as ie,a as ae,b as We,I as Xe,J as Je,K as Qe,L as Ye,h as b,d as ke,M as Ze,g as et,n as z,j as Se,P as Q,Q as S,D as M,E as ue,c as y,e as _,f as E,V as Y,W as Z,X as ee,u as p,q as j,t as d,r as O,a9 as dt,T as ze,y as C,z as A,B as R,G as T,H as te,ap as Pe,a4 as Le,a6 as ft,w as he,x as we,A as ve,a2 as ge,ae as Ce,a0 as tt,_ as an,al as nt,a7 as Oe,aa as Ln,a3 as xt,ax as Ve,af as ut,ac as ln,ad as He}from"./SpinnerAugment-BJ4-L7QR.js";import{B as Cn,A as un,a as mt,b as $t,C as An,g as Rn}from"./main-panel-CLAFkah5.js";import{d as _t,T as kn}from"./Content-Czt02SJi.js";import{R as ot}from"./open-in-new-window-DMlqLwqy.js";import{R as Sn}from"./types-BSMhNRWH.js";import{G as gt}from"./folder-BJI1Q8_7.js";import{B as Ue}from"./ButtonAugment-HnJOGilM.js";import{C as rt,P as In}from"./pen-to-square-Bm4lF9Yl.js";import{T as De}from"./TextTooltipAugment-Bkzart3o.js";import{f as qe}from"./index-CGbmuyBX.js";import{M as ht,R as mn}from"./magnifying-glass-LWYs47rB.js";import{C as $n,G as wt,T as Fn}from"./github-C1PQK5DH.js";import{e as Ke,u as pn,o as dn}from"./BaseButton-C6Dhmpxa.js";import{D as le,C as Nn,T as En}from"./index-C-g0ZorP.js";import{T as fn}from"./terminal-BQIj5vJ0.js";import{A as Dn}from"./arrow-up-right-from-square-CuUnyQRL.js";import{I as vt}from"./IconButtonAugment-Certjadv.js";import{T as gn}from"./Keybindings-4L2d2tRE.js";import{a as Lt}from"./types-LfaCSdmF.js";import{E as Pn}from"./exclamation-triangle-Dn4fXX3v.js";import{T as Un}from"./StatusIndicator-CAJYwjQb.js";import"./layer-group-CZFSGU8L.js";import"./design-system-init-DA68MSAy.js";import"./test_service_pb-B6vKXZrG.js";import"./chat-types-NgqNgjwU.js";import"./diff-utils-y96qaWKK.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-MyvMQzjq.js";import"./isObjectLike-DflaizF0.js";import"./globals-D0QH3NT1.js";import"./await_block-CvQ_3xaW.js";import"./CardAugment-BxTO-shY.js";import"./ellipsis-BWy9xWah.js";import"./keypress-DD1aQVr0.js";import"./file-paths-BcSg4gks.js";import"./Filespan-BC4kxbfx.js";import"./folder-opened-DzrGzNBt.js";import"./lodash-ChYFUhWY.js";import"./MaterialIcon-DIlB9c-0.js";import"./types-a569v5Ol.js";import"./autofix-state-d-ymFdyn.js";import"./VSCodeCodicon-CvBJfpPi.js";import"./augment-logo-D_UKSkj8.js";import"./chat-flags-model-IiDhbRsI.js";import"./TextAreaAugment-Cj5jK817.js";function Ct(r){const e=r.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(e)return{owner:e[1],name:e[2]}}function At(r){return r.replace(/^origin\//,"")}function Bn(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=ae(o,t[s]);return{c(){e=We("svg"),n=new Xe(!0),this.h()},l(s){e=Je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ye(c,!0),c.forEach(b),this.h()},h(){n.a=null,ke(e,o)},m(s,c){Ze(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M472 224c13.3 0 24-10.7 24-24V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v80.1l-20-23.5C387 63.4 325.1 32 256 32 132.3 32 32 132.3 32 256s100.3 224 224 224c50.4 0 97-16.7 134.4-44.8 10.6-8 12.7-23 4.8-33.6s-23-12.7-33.6-4.8C332.2 418.9 295.7 432 256 432c-97.2 0-176-78.8-176-176S158.8 80 256 80c54.3 0 102.9 24.6 135.2 63.4l.1.2 27.6 32.4H328c-13.3 0-24 10.7-24 24s10.7 24 24 24z"/>',e)},p(s,[c]){ke(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:z,o:z,d(s){s&&b(e)}}}function Mn(r,e,n){return r.$$set=t=>{n(0,e=ae(ae({},e),Se(t)))},[e=Se(e)]}class zn extends se{constructor(e){super(),ce(this,e,Mn,Bn,ie,{})}}const Tn=r=>({}),Rt=r=>({}),Gn=r=>({}),kt=r=>({}),jn=r=>({}),St=r=>({}),On=r=>({}),It=r=>({});function Vn(r){let e;return{c(){e=T(r[0])},m(n,t){_(n,e,t)},p(n,t){1&t&&te(e,n[0])},d(n){n&&b(e)}}}function Ft(r){let e,n;const t=r[3].subtitle,o=Q(t,r,r[4],kt),s=o||function(c){let i,a;return i=new ze({props:{size:2,$$slots:{default:[Hn]},$$scope:{ctx:c}}}),{c(){C(i.$$.fragment)},m(l,u){A(i,l,u),a=!0},p(l,u){const m={};18&u&&(m.$$scope={dirty:u,ctx:l}),i.$set(m)},i(l){a||(p(i.$$.fragment,l),a=!0)},o(l){d(i.$$.fragment,l),a=!1},d(l){R(i,l)}}}(r);return{c(){e=S("div"),s&&s.c(),y(e,"class","c-card-button__subtitle svelte-z367s9")},m(c,i){_(c,e,i),s&&s.m(e,null),n=!0},p(c,i){o?o.p&&(!n||16&i)&&Y(o,t,c,c[4],n?ee(t,c[4],i,Gn):Z(c[4]),kt):s&&s.p&&(!n||2&i)&&s.p(c,n?i:-1)},i(c){n||(p(s,c),n=!0)},o(c){d(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Hn(r){let e;return{c(){e=T(r[1])},m(n,t){_(n,e,t)},p(n,t){2&t&&te(e,n[1])},d(n){n&&b(e)}}}function Nt(r){let e,n;const t=r[3].iconRight,o=Q(t,r,r[4],Rt);return{c(){e=S("div"),o&&o.c(),y(e,"class","c-card-button__icon-right svelte-z367s9")},m(s,c){_(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||16&c)&&Y(o,t,s,s[4],n?ee(t,s[4],c,Tn):Z(s[4]),Rt)},i(s){n||(p(o,s),n=!0)},o(s){d(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function qn(r){let e,n,t,o,s,c,i,a;const l=r[3].iconLeft,u=Q(l,r,r[4],It),m=r[3].title,g=Q(m,r,r[4],St),w=g||function($){let v,x;return v=new ze({props:{size:2,$$slots:{default:[Vn]},$$scope:{ctx:$}}}),{c(){C(v.$$.fragment)},m(I,D){A(v,I,D),x=!0},p(I,D){const U={};17&D&&(U.$$scope={dirty:D,ctx:I}),v.$set(U)},i(I){x||(p(v.$$.fragment,I),x=!0)},o(I){d(v.$$.fragment,I),x=!1},d(I){R(v,I)}}}(r);let h=r[1]&&Ft(r),f=r[2].iconRight&&Nt(r);return{c(){e=S("div"),u&&u.c(),n=M(),t=S("div"),o=S("div"),w&&w.c(),s=M(),h&&h.c(),c=M(),f&&f.c(),i=ue(),y(e,"class","c-card-button__icon-left svelte-z367s9"),y(o,"class","c-card-button__title svelte-z367s9"),y(t,"class","c-card-button__content svelte-z367s9")},m($,v){_($,e,v),u&&u.m(e,null),_($,n,v),_($,t,v),E(t,o),w&&w.m(o,null),E(t,s),h&&h.m(t,null),_($,c,v),f&&f.m($,v),_($,i,v),a=!0},p($,[v]){u&&u.p&&(!a||16&v)&&Y(u,l,$,$[4],a?ee(l,$[4],v,On):Z($[4]),It),g?g.p&&(!a||16&v)&&Y(g,m,$,$[4],a?ee(m,$[4],v,jn):Z($[4]),St):w&&w.p&&(!a||1&v)&&w.p($,a?v:-1),$[1]?h?(h.p($,v),2&v&&p(h,1)):(h=Ft($),h.c(),p(h,1),h.m(t,null)):h&&(j(),d(h,1,1,()=>{h=null}),O()),$[2].iconRight?f?(f.p($,v),4&v&&p(f,1)):(f=Nt($),f.c(),p(f,1),f.m(i.parentNode,i)):f&&(j(),d(f,1,1,()=>{f=null}),O())},i($){a||(p(u,$),p(w,$),p(h),p(f),a=!0)},o($){d(u,$),d(w,$),d(h),d(f),a=!1},d($){$&&(b(e),b(n),b(t),b(c),b(i)),u&&u.d($),w&&w.d($),h&&h.d(),f&&f.d($)}}}function Kn(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=dt(t);let{title:c="Select an option"}=e,{subtitle:i=""}=e;return r.$$set=a=>{"title"in a&&n(0,c=a.title),"subtitle"in a&&n(1,i=a.subtitle),"$$scope"in a&&n(4,o=a.$$scope)},[c,i,s,t,o]}class hn extends se{constructor(e){super(),ce(this,e,Kn,qn,ie,{title:0,subtitle:1})}}const Wn=r=>({}),Et=r=>({slot:"iconLeft"}),Xn=r=>({}),Dt=r=>({slot:"iconRight"});function Pt(r,e,n){const t=r.slice();return t[19]=e[n],t}const Jn=r=>({}),Ut=r=>({}),Qn=r=>({}),Bt=r=>({}),Yn=r=>({}),Mt=r=>({slot:"iconLeft"}),Zn=r=>({}),zt=r=>({slot:"title"}),eo=r=>({}),Tt=r=>({slot:"iconRight"});function to(r){let e,n,t,o,s;return n=new hn({props:{title:r[3],subtitle:r[4],$$slots:{iconRight:[ro],iconLeft:[oo]},$$scope:{ctx:r}}}),{c(){e=S("button"),C(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"type","button"),e.disabled=r[10]},m(c,i){_(c,e,i),A(n,e,null),t=!0,o||(s=[Le(e,"click",r[16]),Le(e,"keydown",r[17])],o=!0)},p(c,i){const a={};8&i&&(a.title=c[3]),16&i&&(a.subtitle=c[4]),262144&i&&(a.$$scope={dirty:i,ctx:c}),n.$set(a),(!t||1024&i)&&(e.disabled=c[10])},i(c){t||(p(n.$$.fragment,c),t=!0)},o(c){d(n.$$.fragment,c),t=!1},d(c){c&&b(e),R(n),o=!1,ft(s)}}}function no(r){let e,n,t;function o(c){r[15](c)}let s={onOpenChange:r[9],$$slots:{default:[fo]},$$scope:{ctx:r}};return r[1]!==void 0&&(s.requestClose=r[1]),e=new le.Root({props:s}),he.push(()=>we(e,"requestClose",o)),{c(){C(e.$$.fragment)},m(c,i){A(e,c,i),t=!0},p(c,i){const a={};512&i&&(a.onOpenChange=c[9]),263641&i&&(a.$$scope={dirty:i,ctx:c}),!n&&2&i&&(n=!0,a.requestClose=c[1],ve(()=>n=!1)),e.$set(a)},i(c){t||(p(e.$$.fragment,c),t=!0)},o(c){d(e.$$.fragment,c),t=!1},d(c){R(e,c)}}}function oo(r){let e;const n=r[13].iconLeft,t=Q(n,r,r[18],Et);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Y(t,n,o,o[18],e?ee(n,o[18],s,Wn):Z(o[18]),Et)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function ro(r){let e;const n=r[13].iconRight,t=Q(n,r,r[18],Dt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Y(t,n,o,o[18],e?ee(n,o[18],s,Xn):Z(o[18]),Dt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function so(r){let e;const n=r[13].iconLeft,t=Q(n,r,r[18],Mt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||262144&s)&&Y(t,n,o,o[18],e?ee(n,o[18],s,Yn):Z(o[18]),Mt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function co(r){let e;const n=r[13].title,t=Q(n,r,r[18],zt),o=t||function(s){let c;return{c(){c=T(s[3])},m(i,a){_(i,c,a)},p(i,a){8&a&&te(c,i[3])},d(i){i&&b(c)}}}(r);return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t?t.p&&(!e||262144&c)&&Y(t,n,s,s[18],e?ee(n,s[18],c,Zn):Z(s[18]),zt):o&&o.p&&(!e||8&c)&&o.p(s,e?c:-1)},i(s){e||(p(o,s),e=!0)},o(s){d(o,s),e=!1},d(s){o&&o.d(s)}}}function io(r){let e;const n=r[13].iconRight,t=Q(n,r,r[18],Tt),o=t||function(s){let c,i;return c=new $n({}),{c(){C(c.$$.fragment)},m(a,l){A(c,a,l),i=!0},i(a){i||(p(c.$$.fragment,a),i=!0)},o(a){d(c.$$.fragment,a),i=!1},d(a){R(c,a)}}}();return{c(){o&&o.c()},m(s,c){o&&o.m(s,c),e=!0},p(s,c){t&&t.p&&(!e||262144&c)&&Y(t,n,s,s[18],e?ee(n,s[18],c,eo):Z(s[18]),Tt)},i(s){e||(p(o,s),e=!0)},o(s){d(o,s),e=!1},d(s){o&&o.d(s)}}}function ao(r){let e,n,t,o;return n=new hn({props:{subtitle:r[4],$$slots:{iconRight:[io],title:[co],iconLeft:[so]},$$scope:{ctx:r}}}),{c(){e=S("div"),C(n.$$.fragment),y(e,"class","c-card-button__display svelte-1km5ln2"),y(e,"role","button"),y(e,"tabindex",t=r[10]?-1:0),ge(e,"disabled",r[10])},m(s,c){_(s,e,c),A(n,e,null),o=!0},p(s,c){const i={};16&c&&(i.subtitle=s[4]),262152&c&&(i.$$scope={dirty:c,ctx:s}),n.$set(i),(!o||1024&c&&t!==(t=s[10]?-1:0))&&y(e,"tabindex",t),(!o||1024&c)&&ge(e,"disabled",s[10])},i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){d(n.$$.fragment,s),o=!1},d(s){s&&b(e),R(n)}}}function lo(r){let e,n;return e=new le.Label({props:{$$slots:{default:[mo]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};262400&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function uo(r){let e,n,t=[],o=new Map,s=Ke(r[6]);const c=i=>i[7](i[19]);for(let i=0;i<s.length;i+=1){let a=Pt(r,s,i),l=c(a);o.set(l,t[i]=Gt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=ue()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);_(i,e,a),n=!0},p(i,a){2241&a&&(s=Ke(i[6]),j(),t=pn(t,a,c,1,i,s,o,e.parentNode,dn,Gt,e,Pt),O())},i(i){if(!n){for(let a=0;a<s.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)d(t[a]);n=!1},d(i){i&&b(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function mo(r){let e;return{c(){e=T(r[8])},m(n,t){_(n,e,t)},p(n,t){256&t&&te(e,n[8])},d(n){n&&b(e)}}}function $o(r){let e,n,t=r[7](r[19])+"";return{c(){e=T(t),n=M()},m(o,s){_(o,e,s),_(o,n,s)},p(o,s){192&s&&t!==(t=o[7](o[19])+"")&&te(e,t)},d(o){o&&(b(e),b(n))}}}function Gt(r,e){let n,t,o;function s(){return e[14](e[19])}return t=new le.Item({props:{onSelect:s,highlight:e[0]===e[19],$$slots:{default:[$o]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=ue(),C(t.$$.fragment),this.first=n},m(c,i){_(c,n,i),A(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=s),65&i&&(a.highlight=e[0]===e[19]),262336&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&b(n),R(t,c)}}}function po(r){let e,n,t,o,s;const c=r[13]["dropdown-top"],i=Q(c,r,r[18],Bt),a=r[13]["dropdown-content"],l=Q(a,r,r[18],Ut),u=l||function(m){let g,w,h,f;const $=[uo,lo],v=[];function x(I,D){return I[6].length>0?0:1}return g=x(m),w=v[g]=$[g](m),{c(){w.c(),h=ue()},m(I,D){v[g].m(I,D),_(I,h,D),f=!0},p(I,D){let U=g;g=x(I),g===U?v[g].p(I,D):(j(),d(v[U],1,1,()=>{v[U]=null}),O(),w=v[g],w?w.p(I,D):(w=v[g]=$[g](I),w.c()),p(w,1),w.m(h.parentNode,h))},i(I){f||(p(w),f=!0)},o(I){d(w),f=!1},d(I){I&&b(h),v[g].d(I)}}}(r);return{c(){e=S("div"),n=S("div"),i&&i.c(),t=M(),o=S("div"),u&&u.c(),y(n,"class","c-card-button__dropdown-top svelte-1km5ln2"),y(o,"class","c-card-button__dropdown-content svelte-1km5ln2"),y(e,"class","c-card__dropdown-contents svelte-1km5ln2")},m(m,g){_(m,e,g),E(e,n),i&&i.m(n,null),E(e,t),E(e,o),u&&u.m(o,null),s=!0},p(m,g){i&&i.p&&(!s||262144&g)&&Y(i,c,m,m[18],s?ee(c,m[18],g,Qn):Z(m[18]),Bt),l?l.p&&(!s||262144&g)&&Y(l,a,m,m[18],s?ee(a,m[18],g,Jn):Z(m[18]),Ut):u&&u.p&&(!s||449&g)&&u.p(m,s?g:-1)},i(m){s||(p(i,m),p(u,m),s=!0)},o(m){d(i,m),d(u,m),s=!1},d(m){m&&b(e),i&&i.d(m),u&&u.d(m)}}}function fo(r){let e,n,t,o;return e=new le.Trigger({props:{$$slots:{default:[ao]},$$scope:{ctx:r}}}),t=new le.Content({props:{align:"start",side:"bottom",$$slots:{default:[po]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment),n=M(),C(t.$$.fragment)},m(s,c){A(e,s,c),_(s,n,c),A(t,s,c),o=!0},p(s,c){const i={};263192&c&&(i.$$scope={dirty:c,ctx:s}),e.$set(i);const a={};262593&c&&(a.$$scope={dirty:c,ctx:s}),t.$set(a)},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),R(e,s),R(t,s)}}}function go(r){let e,n,t,o;const s=[no,to],c=[];function i(a,l){return a[2]==="dropdown"?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=S("div"),t.c(),y(e,"class","c-card-button svelte-1km5ln2")},m(a,l){_(a,e,l),c[n].m(e,null),o=!0},p(a,[l]){let u=n;n=i(a),n===u?c[n].p(a,l):(j(),d(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function ho(r,e,n){let{$$slots:t={},$$scope:o}=e,{type:s="button"}=e,{title:c="Select an option"}=e,{subtitle:i=""}=e,{onClick:a=()=>{}}=e,{items:l=[]}=e,{selectedItem:u}=e,{formatItemLabel:m=x=>(x==null?void 0:x.toString())||""}=e,{noItemsLabel:g="No items found"}=e,{onDropdownOpenChange:w=()=>{}}=e,{requestClose:h=()=>{}}=e,{disabled:f=!1}=e;function $(x){n(0,u=x),v("select",x)}const v=Pe();return r.$$set=x=>{"type"in x&&n(2,s=x.type),"title"in x&&n(3,c=x.title),"subtitle"in x&&n(4,i=x.subtitle),"onClick"in x&&n(5,a=x.onClick),"items"in x&&n(6,l=x.items),"selectedItem"in x&&n(0,u=x.selectedItem),"formatItemLabel"in x&&n(7,m=x.formatItemLabel),"noItemsLabel"in x&&n(8,g=x.noItemsLabel),"onDropdownOpenChange"in x&&n(9,w=x.onDropdownOpenChange),"requestClose"in x&&n(1,h=x.requestClose),"disabled"in x&&n(10,f=x.disabled),"$$scope"in x&&n(18,o=x.$$scope)},[u,h,s,c,i,a,l,m,g,w,f,$,v,t,x=>$(x),function(x){h=x,n(1,h)},()=>{a(),v("click")},x=>{x.key!=="Enter"&&x.key!==" "||(a(),v("click"))},o]}class wn extends se{constructor(e){super(),ce(this,e,ho,go,ie,{type:2,title:3,subtitle:4,onClick:5,items:6,selectedItem:0,formatItemLabel:7,noItemsLabel:8,onDropdownOpenChange:9,requestClose:1,disabled:10,selectItem:11})}get selectItem(){return this.$$.ctx[11]}}function wo(r){let e,n;return e=new wn({props:{type:"dropdown",title:"Connected to your GitHub account",$$slots:{"dropdown-content":[Ao],iconLeft:[bo]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};4100&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function vo(r){let e,n;return e=new wn({props:{type:"button",title:r[1]?"Cancel":"Connect to GitHub",onClick:r[4],$$slots:{iconRight:[Io],iconLeft:[Ro]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};2&o&&(s.title=t[1]?"Cancel":"Connect to GitHub"),4098&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function bo(r){let e,n;return e=new wt({props:{slot:"iconLeft"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function yo(r){let e,n;return e=new ze({props:{size:1,weight:"medium",$$slots:{default:[_o]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function xo(r){let e,n,t,o;return e=new nt({props:{slot:"iconLeft",useCurrentColor:!0,size:1}}),t=new ze({props:{size:1,weight:"medium",$$slots:{default:[Lo]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment),n=M(),C(t.$$.fragment)},m(s,c){A(e,s,c),_(s,n,c),A(t,s,c),o=!0},i(s){o||(p(e.$$.fragment,s),p(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),R(e,s),R(t,s)}}}function _o(r){let e;return{c(){e=T("Revoke Access")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function Lo(r){let e;return{c(){e=T("Revoking...")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function Co(r){let e,n,t,o;const s=[xo,yo],c=[];function i(a,l){return a[2]?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),_(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e!==u&&(j(),d(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n||(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Ao(r){let e,n,t;return n=new le.Item({props:{color:"error",onSelect:r[6],$$slots:{default:[Co]},$$scope:{ctx:r}}}),{c(){e=S("div"),C(n.$$.fragment),y(e,"slot","dropdown-content")},m(o,s){_(o,e,s),A(n,e,null),t=!0},p(o,s){const c={};4100&s&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),R(n)}}}function Ro(r){let e,n;return e=new wt({props:{slot:"iconLeft"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function ko(r){let e,n;return e=new Nn({}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function So(r){let e,n;return e=new nt({props:{size:1,useCurrentColor:!0}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Io(r){let e,n,t,o;const s=[So,ko],c=[];function i(a,l){return a[1]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=S("div"),t.c(),y(e,"slot","iconRight")},m(a,l){_(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n!==u&&(j(),d(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t||(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function Fo(r){let e,n,t,o,s;const c=[vo,wo],i=[];function a(l,u){return l[0]?1:0}return t=a(r),o=i[t]=c[t](r),{c(){e=S("div"),n=S("div"),o.c(),y(n,"class","github-auth-button"),y(e,"class","github-auth-card svelte-zdlnsr")},m(l,u){_(l,e,u),E(e,n),i[t].m(n,null),s=!0},p(l,[u]){let m=t;t=a(l),t===m?i[t].p(l,u):(j(),d(i[m],1,1,()=>{i[m]=null}),O(),o=i[t],o?o.p(l,u):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null))},i(l){s||(p(o),s=!0)},o(l){d(o),s=!1},d(l){l&&b(e),i[t].d()}}}function No(r,e,n){const t=Pe(),o=Ce(gt.key);let s=!1,c=!1,i=!1,a=null,l=null;async function u(){if(!i){n(2,i=!0);try{const m=await o.revokeGithubAccess();m.success?(n(0,s=!1),t("authStateChange",{isAuthenticated:!1})):console.error("Failed to revoke GitHub access:",m.message)}catch(m){console.error("Error revoking GitHub access:",m)}finally{n(2,i=!1)}}}return tt(async()=>{await async function(){try{const m=await o.isGithubAuthenticated();m!==s?(n(0,s=m),t("authStateChange",{isAuthenticated:s})):n(0,s=m)}catch(m){console.error("Failed to check GitHub authentication status:",m),n(0,s=!1),t("authStateChange",{isAuthenticated:!1})}}()}),an(()=>{a&&(clearTimeout(a),a=null),l&&(clearInterval(l),l=null)}),[s,c,i,()=>{},async function(){if(c)return n(1,c=!1),void(a&&(clearTimeout(a),a=null));n(1,c=!0);try{await o.authenticateGithub(),l=setInterval(async()=>{await o.isGithubAuthenticated()&&(n(0,s=!0),n(1,c=!1),t("authStateChange",{isAuthenticated:!0}),l&&clearInterval(l),a&&(clearTimeout(a),a=null))},5e3),a=setTimeout(()=>{l&&clearInterval(l),n(1,c=!1),a=null},6e4)}catch(m){console.error("Failed to authenticate with GitHub:",m),n(1,c=!1)}},u,()=>{u()}]}class Eo extends se{constructor(e){super(),ce(this,e,No,Fo,ie,{})}}const Do=r=>({}),jt=r=>({});function Ot(r,e,n){const t=r.slice();return t[27]=e[n],t[29]=n,t}const Po=r=>({item:64&r}),Vt=r=>({item:r[27]}),Uo=r=>({}),Ht=r=>({}),Bo=r=>({}),qt=r=>({}),Mo=r=>({}),Kt=r=>({}),zo=r=>({}),Wt=r=>({});function To(r){let e,n,t,o,s,c,i,a,l,u,m,g,w,h;const f=[Oo,jo],$=[];function v(U,P){return U[4]?0:1}o=v(r),s=$[o]=f[o](r);const x=[Ho,Vo],I=[];function D(U,P){return U[17].title?0:1}return a=D(r),l=I[a]=x[a](r),g=new $n({}),{c(){e=S("div"),n=S("div"),t=S("div"),s.c(),c=M(),i=S("span"),l.c(),u=M(),m=S("div"),C(g.$$.fragment),y(t,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(i,"class","c-searchable-dropdown__button-text svelte-jowwyu"),y(n,"class","c-searchable-dropdown__icon-text svelte-jowwyu"),y(m,"class","c-searchable-dropdown__chevron svelte-jowwyu"),y(e,"class","c-searchable-dropdown__button svelte-jowwyu"),y(e,"role","button"),y(e,"tabindex",w=r[5]?-1:0),ge(e,"c-searchable-dropdown__button--disabled",r[5])},m(U,P){_(U,e,P),E(e,n),E(n,t),$[o].m(t,null),E(n,c),E(n,i),I[a].m(i,null),E(e,u),E(e,m),A(g,m,null),h=!0},p(U,P){let k=o;o=v(U),o===k?$[o].p(U,P):(j(),d($[k],1,1,()=>{$[k]=null}),O(),s=$[o],s?s.p(U,P):(s=$[o]=f[o](U),s.c()),p(s,1),s.m(t,null));let K=a;a=D(U),a===K?I[a].p(U,P):(j(),d(I[K],1,1,()=>{I[K]=null}),O(),l=I[a],l?l.p(U,P):(l=I[a]=x[a](U),l.c()),p(l,1),l.m(i,null)),(!h||32&P&&w!==(w=U[5]?-1:0))&&y(e,"tabindex",w),(!h||32&P)&&ge(e,"c-searchable-dropdown__button--disabled",U[5])},i(U){h||(p(s),p(l),p(g.$$.fragment,U),h=!0)},o(U){d(s),d(l),d(g.$$.fragment,U),h=!1},d(U){U&&b(e),$[o].d(),I[a].d(),R(g)}}}function Go(r){let e,n,t,o,s,c,i;const a=r[18].searchIcon,l=Q(a,r,r[25],Wt),u=l||function(m){let g;const w=m[18].icon,h=Q(w,m,m[25],Kt);return{c(){h&&h.c()},m(f,$){h&&h.m(f,$),g=!0},p(f,$){h&&h.p&&(!g||33554432&$)&&Y(h,w,f,f[25],g?ee(w,f[25],$,Mo):Z(f[25]),Kt)},i(f){g||(p(h,f),g=!0)},o(f){d(h,f),g=!1},d(f){h&&h.d(f)}}}(r);return{c(){e=S("div"),n=S("div"),u&&u.c(),t=M(),o=S("input"),y(n,"class","c-searchable-dropdown__icon svelte-jowwyu"),y(o,"type","text"),y(o,"class","c-searchable-dropdown__trigger-input svelte-jowwyu"),y(o,"placeholder",r[3]),y(e,"class","c-searchable-dropdown__input-container svelte-jowwyu")},m(m,g){_(m,e,g),E(e,n),u&&u.m(n,null),E(e,t),E(e,o),xt(o,r[0]),s=!0,c||(i=[Le(o,"input",r[21]),Le(o,"input",r[22]),Le(o,"click",Ve(r[19])),Le(o,"mousedown",Ve(r[20]))],c=!0)},p(m,g){l?l.p&&(!s||33554432&g)&&Y(l,a,m,m[25],s?ee(a,m[25],g,zo):Z(m[25]),Wt):u&&u.p&&(!s||33554432&g)&&u.p(m,s?g:-1),(!s||8&g)&&y(o,"placeholder",m[3]),1&g&&o.value!==m[0]&&xt(o,m[0])},i(m){s||(p(u,m),s=!0)},o(m){d(u,m),s=!1},d(m){m&&b(e),u&&u.d(m),c=!1,ft(i)}}}function jo(r){let e;const n=r[18].icon,t=Q(n,r,r[25],qt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Y(t,n,o,o[25],e?ee(n,o[25],s,Bo):Z(o[25]),qt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Oo(r){let e,n;return e=new nt({props:{size:1,useCurrentColor:!0}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Vo(r){let e,n=(r[4]?r[11]:r[2])+"";return{c(){e=T(n)},m(t,o){_(t,e,o)},p(t,o){2068&o&&n!==(n=(t[4]?t[11]:t[2])+"")&&te(e,n)},i:z,o:z,d(t){t&&b(e)}}}function Ho(r){let e;const n=r[18].title,t=Q(n,r,r[25],Ht);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Y(t,n,o,o[25],e?ee(n,o[25],s,Uo):Z(o[25]),Ht)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function qo(r){let e,n,t,o;const s=[Go,To],c=[];function i(a,l){return a[12]?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),_(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(j(),d(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Xt(r){let e,n;return e=new le.Content({props:{side:"bottom",align:"start",$$slots:{default:[Zo]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};33689298&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Ko(r){let e,n;return e=new le.Item({props:{disabled:!0,$$slots:{default:[Jo]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};33555456&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Wo(r){let e,n,t=[],o=new Map,s=Ke(r[6]);const c=i=>i[27]===null?`null-item-${i[29]}`:i[8](i[27]);for(let i=0;i<s.length;i+=1){let a=Ot(r,s,i),l=c(a);o.set(l,t[i]=Jt(l,a))}return{c(){for(let i=0;i<t.length;i+=1)t[i].c();e=ue()},m(i,a){for(let l=0;l<t.length;l+=1)t[l]&&t[l].m(i,a);_(i,e,a),n=!0},p(i,a){33620930&a&&(s=Ke(i[6]),j(),t=pn(t,a,c,1,i,s,o,e.parentNode,dn,Jt,e,Ot),O())},i(i){if(!n){for(let a=0;a<s.length;a+=1)p(t[a]);n=!0}},o(i){for(let a=0;a<t.length;a+=1)d(t[a]);n=!1},d(i){i&&b(e);for(let a=0;a<t.length;a+=1)t[a].d(i)}}}function Xo(r){let e,n;return e=new le.Item({props:{disabled:!0,$$slots:{default:[Yo]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};33556480&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Jo(r){let e;return{c(){e=T(r[10])},m(n,t){_(n,e,t)},p(n,t){1024&t&&te(e,n[10])},d(n){n&&b(e)}}}function Qo(r){let e,n;const t=r[18].item,o=Q(t,r,r[25],Vt),s=o||function(c){let i,a=c[7](c[27])+"";return{c(){i=T(a)},m(l,u){_(l,i,u)},p(l,u){192&u&&a!==(a=l[7](l[27])+"")&&te(i,a)},d(l){l&&b(i)}}}(r);return{c(){s&&s.c(),e=M()},m(c,i){s&&s.m(c,i),_(c,e,i),n=!0},p(c,i){o?o.p&&(!n||33554496&i)&&Y(o,t,c,c[25],n?ee(t,c[25],i,Po):Z(c[25]),Vt):s&&s.p&&(!n||192&i)&&s.p(c,n?i:-1)},i(c){n||(p(s,c),n=!0)},o(c){d(s,c),n=!1},d(c){c&&b(e),s&&s.d(c)}}}function Jt(r,e){let n,t,o;function s(){return e[23](e[27])}return t=new le.Item({props:{onSelect:s,highlight:e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27]),$$slots:{default:[Qo]},$$scope:{ctx:e}}}),{key:r,first:null,c(){n=ue(),C(t.$$.fragment),this.first=n},m(c,i){_(c,n,i),A(t,c,i),o=!0},p(c,i){e=c;const a={};64&i&&(a.onSelect=s),706&i&&(a.highlight=e[9]?e[9](e[27],e[1]):!!e[1]&&e[7](e[1])===e[7](e[27])),33554624&i&&(a.$$scope={dirty:i,ctx:e}),t.$set(a)},i(c){o||(p(t.$$.fragment,c),o=!0)},o(c){d(t.$$.fragment,c),o=!1},d(c){c&&b(n),R(t,c)}}}function Yo(r){let e,n,t,o,s,c;return n=new nt({props:{size:1,useCurrentColor:!0}}),{c(){e=S("div"),C(n.$$.fragment),t=M(),o=S("span"),s=T(r[11]),y(e,"class","c-searchable-dropdown__loading svelte-jowwyu")},m(i,a){_(i,e,a),A(n,e,null),E(e,t),E(e,o),E(o,s),c=!0},p(i,a){(!c||2048&a)&&te(s,i[11])},i(i){c||(p(n.$$.fragment,i),c=!0)},o(i){d(n.$$.fragment,i),c=!1},d(i){i&&b(e),R(n)}}}function Qt(r){let e;const n=r[18].footer,t=Q(n,r,r[25],jt);return{c(){t&&t.c()},m(o,s){t&&t.m(o,s),e=!0},p(o,s){t&&t.p&&(!e||33554432&s)&&Y(t,n,o,o[25],e?ee(n,o[25],s,Do):Z(o[25]),jt)},i(o){e||(p(t,o),e=!0)},o(o){d(t,o),e=!1},d(o){t&&t.d(o)}}}function Zo(r){let e,n,t,o,s,c;const i=[Xo,Wo,Ko],a=[];function l(m,g){return m[4]?0:m[6].length>0?1:2}e=l(r),n=a[e]=i[e](r);let u=r[17].footer&&Qt(r);return{c(){n.c(),t=M(),u&&u.c(),o=M(),s=S("div"),Ln(s,"margin-bottom","var(--ds-spacing-2)")},m(m,g){a[e].m(m,g),_(m,t,g),u&&u.m(m,g),_(m,o,g),_(m,s,g),c=!0},p(m,g){let w=e;e=l(m),e===w?a[e].p(m,g):(j(),d(a[w],1,1,()=>{a[w]=null}),O(),n=a[e],n?n.p(m,g):(n=a[e]=i[e](m),n.c()),p(n,1),n.m(t.parentNode,t)),m[17].footer?u?(u.p(m,g),131072&g&&p(u,1)):(u=Qt(m),u.c(),p(u,1),u.m(o.parentNode,o)):u&&(j(),d(u,1,1,()=>{u=null}),O())},i(m){c||(p(n),p(u),c=!0)},o(m){d(n),d(u),c=!1},d(m){m&&(b(t),b(o),b(s)),a[e].d(m),u&&u.d(m)}}}function er(r){let e,n,t,o;e=new le.Trigger({props:{$$slots:{default:[qo]},$$scope:{ctx:r}}});let s=!r[5]&&Xt(r);return{c(){C(e.$$.fragment),n=M(),s&&s.c(),t=ue()},m(c,i){A(e,c,i),_(c,n,i),s&&s.m(c,i),_(c,t,i),o=!0},p(c,i){const a={};33691709&i&&(a.$$scope={dirty:i,ctx:c}),e.$set(a),c[5]?s&&(j(),d(s,1,1,()=>{s=null}),O()):s?(s.p(c,i),32&i&&p(s,1)):(s=Xt(c),s.c(),p(s,1),s.m(t.parentNode,t))},i(c){o||(p(e.$$.fragment,c),p(s),o=!0)},o(c){d(e.$$.fragment,c),d(s),o=!1},d(c){c&&(b(n),b(t)),R(e,c),s&&s.d(c)}}}function tr(r){let e,n,t,o;function s(i){r[24](i)}let c={onOpenChange:r[14],$$slots:{default:[er]},$$scope:{ctx:r}};return r[13]!==void 0&&(c.requestClose=r[13]),n=new le.Root({props:c}),he.push(()=>we(n,"requestClose",s)),{c(){e=S("div"),C(n.$$.fragment),y(e,"class","c-searchable-dropdown svelte-jowwyu")},m(i,a){_(i,e,a),A(n,e,null),o=!0},p(i,[a]){const l={};33693439&a&&(l.$$scope={dirty:a,ctx:i}),!t&&8192&a&&(t=!0,l.requestClose=i[13],ve(()=>t=!1)),n.$set(l)},i(i){o||(p(n.$$.fragment,i),o=!0)},o(i){d(n.$$.fragment,i),o=!1},d(i){i&&b(e),R(n)}}}function nr(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=dt(t);let{title:c=""}=e,{placeholder:i="Search..."}=e,{isLoading:a=!1}=e,{disabled:l=!1}=e,{searchValue:u=""}=e,{items:m=[]}=e,{selectedItem:g=null}=e,{itemLabelFn:w=k=>(k==null?void 0:k.toString())||""}=e,{itemKeyFn:h=k=>(k==null?void 0:k.toString())||""}=e,{isItemSelected:f}=e,{noItemsLabel:$="No items found"}=e,{loadingLabel:v="Loading..."}=e,x=!1,I=()=>{};const D=Pe();function U(k){n(0,u=k),D("search",k)}function P(k){n(1,g=k),D("select",k),I()}return r.$$set=k=>{"title"in k&&n(2,c=k.title),"placeholder"in k&&n(3,i=k.placeholder),"isLoading"in k&&n(4,a=k.isLoading),"disabled"in k&&n(5,l=k.disabled),"searchValue"in k&&n(0,u=k.searchValue),"items"in k&&n(6,m=k.items),"selectedItem"in k&&n(1,g=k.selectedItem),"itemLabelFn"in k&&n(7,w=k.itemLabelFn),"itemKeyFn"in k&&n(8,h=k.itemKeyFn),"isItemSelected"in k&&n(9,f=k.isItemSelected),"noItemsLabel"in k&&n(10,$=k.noItemsLabel),"loadingLabel"in k&&n(11,v=k.loadingLabel),"$$scope"in k&&n(25,o=k.$$scope)},[u,g,c,i,a,l,m,w,h,f,$,v,x,I,function(k){if(!l){if(n(12,x=k),k&&g){const K=w(g);n(0,u=K),D("search",""),setTimeout(()=>{const V=document.querySelector(".c-searchable-dropdown__trigger-input");V&&V.select()},0)}else k&&(n(0,u=""),D("search",""));D("openChange",k)}},U,P,s,t,function(k){Oe.call(this,r,k)},function(k){Oe.call(this,r,k)},function(){u=this.value,n(0,u)},k=>U(k.currentTarget.value),k=>P(k),function(k){I=k,n(13,I)},o]}class pt extends se{constructor(e){super(),ce(this,e,nr,tr,ie,{title:2,placeholder:3,isLoading:4,disabled:5,searchValue:0,items:6,selectedItem:1,itemLabelFn:7,itemKeyFn:8,isItemSelected:9,noItemsLabel:10,loadingLabel:11})}}function or(r){let e,n,t;return n=new rt({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[lr]},$$scope:{ctx:r}}}),{c(){e=S("div"),C(n.$$.fragment),y(e,"class","c-commit-ref-selector__error svelte-14w5nl7")},m(o,s){_(o,e,s),A(n,e,null),t=!0},p(o,s){const c={};8195&s[0]|8&s[2]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),R(n)}}}function rr(r){var h,f;let e,n,t,o,s,c,i,a,l;function u($){r[30]($)}let m={title:((h=r[2])==null?void 0:h.name)||"Choose repository...",placeholder:"Search repositories...",itemKeyFn:hr,isLoading:r[5],disabled:!r[6].length,items:r[7],selectedItem:r[2],itemLabelFn:wr,noItemsLabel:"No repositories found",loadingLabel:"Loading repositories...",$$slots:{searchIcon:[mr],icon:[ur]},$$scope:{ctx:r}};function g($){r[35]($)}r[11]!==void 0&&(m.searchValue=r[11]),t=new pt({props:m}),he.push(()=>we(t,"searchValue",u)),t.$on("openChange",r[31]),t.$on("search",r[32]),t.$on("select",r[33]);let w={title:((f=r[3])==null?void 0:f.name)||"Choose branch...",itemKeyFn:vr,placeholder:"Search branches...",isLoading:r[16],disabled:r[14],items:r[8],selectedItem:r[3],itemLabelFn:br,noItemsLabel:"No branches found",loadingLabel:"Loading branches...",$$slots:{footer:[fr],searchIcon:[pr],icon:[$r]},$$scope:{ctx:r}};return r[12]!==void 0&&(w.searchValue=r[12]),i=new pt({props:w}),he.push(()=>we(i,"searchValue",g)),i.$on("openChange",r[36]),i.$on("search",r[37]),i.$on("select",r[38]),{c(){e=S("div"),n=S("div"),C(t.$$.fragment),s=M(),c=S("div"),C(i.$$.fragment),y(n,"class","c-commit-ref-selector__selector svelte-14w5nl7"),y(c,"class","c-commit-ref-selector__selector svelte-14w5nl7"),y(e,"class","c-commit-ref-selector__selectors-container svelte-14w5nl7")},m($,v){_($,e,v),E(e,n),A(t,n,null),E(e,s),E(e,c),A(i,c,null),l=!0},p($,v){var D,U;const x={};4&v[0]&&(x.title=((D=$[2])==null?void 0:D.name)||"Choose repository..."),32&v[0]&&(x.isLoading=$[5]),64&v[0]&&(x.disabled=!$[6].length),128&v[0]&&(x.items=$[7]),4&v[0]&&(x.selectedItem=$[2]),8&v[2]&&(x.$$scope={dirty:v,ctx:$}),!o&&2048&v[0]&&(o=!0,x.searchValue=$[11],ve(()=>o=!1)),t.$set(x);const I={};8&v[0]&&(I.title=((U=$[3])==null?void 0:U.name)||"Choose branch..."),65536&v[0]&&(I.isLoading=$[16]),16384&v[0]&&(I.disabled=$[14]),256&v[0]&&(I.items=$[8]),8&v[0]&&(I.selectedItem=$[3]),1552&v[0]|8&v[2]&&(I.$$scope={dirty:v,ctx:$}),!a&&4096&v[0]&&(a=!0,I.searchValue=$[12],ve(()=>a=!1)),i.$set(I)},i($){l||(p(t.$$.fragment,$),p(i.$$.fragment,$),l=!0)},o($){d(t.$$.fragment,$),d(i.$$.fragment,$),l=!1},d($){$&&b(e),R(t),R(i)}}}function sr(r){let e,n;return e=new Ue({props:{variant:"ghost",color:"warning",size:1,loading:r[1],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[ar],default:[ir]},$$scope:{ctx:r}}}),e.$on("click",r[21]),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};2&o[0]&&(s.loading=t[1]),8&o[2]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function cr(r){let e,n;return e=new Eo({}),e.$on("authStateChange",r[39]),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function ir(r){let e;return{c(){e=T("Reload available repos and branches")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function ar(r){let e,n,t;return n=new mn({}),{c(){e=S("span"),C(n.$$.fragment),y(e,"slot","iconLeft"),y(e,"class","svelte-14w5nl7")},m(o,s){_(o,e,s),A(n,e,null),t=!0},p:z,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),R(n)}}}function lr(r){let e,n,t,o,s,c,i;const a=[cr,sr],l=[];function u(m,g){return m[13]?1:0}return s=u(r),c=l[s]=a[s](r),{c(){e=S("div"),n=S("div"),t=T(r[0]),o=M(),c.c(),y(n,"class","c-commit-ref-selector__error-message svelte-14w5nl7"),y(e,"class","c-commit-ref-selector__error-content svelte-14w5nl7")},m(m,g){_(m,e,g),E(e,n),E(n,t),E(e,o),l[s].m(e,null),i=!0},p(m,g){(!i||1&g[0])&&te(t,m[0]);let w=s;s=u(m),s===w?l[s].p(m,g):(j(),d(l[w],1,1,()=>{l[w]=null}),O(),c=l[s],c?c.p(m,g):(c=l[s]=a[s](m),c.c()),p(c,1),c.m(e,null))},i(m){i||(p(c),i=!0)},o(m){d(c),i=!1},d(m){m&&b(e),l[s].d()}}}function ur(r){let e,n;return e=new wt({props:{slot:"icon"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function mr(r){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function $r(r){let e,n;return e=new Cn({props:{slot:"icon"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function pr(r){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Yt(r){let e,n,t,o,s,c,i,a,l;return t=new zn({}),c=new ze({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[dr]},$$scope:{ctx:r}}}),{c(){e=S("button"),n=S("div"),C(t.$$.fragment),o=M(),s=S("div"),C(c.$$.fragment),y(n,"class","c-commit-ref-selector__item-icon svelte-14w5nl7"),y(s,"class","c-commit-ref-selector__item-content svelte-14w5nl7"),y(e,"type","button"),y(e,"class","c-commit-ref-selector__item c-commit-ref-selector__item--loading svelte-14w5nl7")},m(u,m){_(u,e,m),E(e,n),A(t,n,null),E(e,o),E(e,s),A(c,s,null),i=!0,a||(l=Le(e,"click",r[34]),a=!0)},p(u,m){const g={};8&m[2]&&(g.$$scope={dirty:m,ctx:u}),c.$set(g)},i(u){i||(p(t.$$.fragment,u),p(c.$$.fragment,u),i=!0)},o(u){d(t.$$.fragment,u),d(c.$$.fragment,u),i=!1},d(u){u&&b(e),R(t),R(c),a=!1,l()}}}function dr(r){let e;return{c(){e=T("Load more branches")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function fr(r){let e,n,t=r[10]&&!r[4]&&Yt(r);return{c(){t&&t.c(),e=ue()},m(o,s){t&&t.m(o,s),_(o,e,s),n=!0},p(o,s){o[10]&&!o[4]?t?(t.p(o,s),1040&s[0]&&p(t,1)):(t=Yt(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(j(),d(t,1,1,()=>{t=null}),O())},i(o){n||(p(t),n=!0)},o(o){d(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function gr(r){let e,n,t,o,s;const c=[rr,or],i=[];function a(l,u){return l[15]?l[15]?1:-1:0}return~(t=a(r))&&(o=i[t]=c[t](r)),{c(){e=S("div"),n=S("div"),o&&o.c(),y(n,"class","c-commit-ref-selector__content svelte-14w5nl7"),y(e,"class","c-commit-ref-selector svelte-14w5nl7")},m(l,u){_(l,e,u),E(e,n),~t&&i[t].m(n,null),s=!0},p(l,u){let m=t;t=a(l),t===m?~t&&i[t].p(l,u):(o&&(j(),d(i[m],1,1,()=>{i[m]=null}),O()),~t?(o=i[t],o?o.p(l,u):(o=i[t]=c[t](l),o.c()),p(o,1),o.m(n,null)):o=null)},i(l){s||(p(o),s=!0)},o(l){d(o),s=!1},d(l){l&&b(e),~t&&i[t].d()}}}const hr=r=>`${r.owner}-${r.name}`,wr=r=>`${r.owner}/${r.name}`,vr=r=>`${r.name}-${r.commit.sha}`,br=r=>r.name.replace("origin/","");function yr(r,e,n){let t,o,s,c;const i=Ce(gt.key),a=Pe(),l=Ce(ot.key);let u,m,{errorMessage:g=""}=e,{isLoading:w=!1}=e,{lastUsedBranchName:h=null}=e,{lastUsedRepoUrl:f=null}=e,$=[],v=$,x=[],I=x,D=!1,U=!1,P=0,k=!1,K=!1,V=!1,X="",ne="";function re(F){n(11,X=F),K=!0,vn(F)}function me(F){n(12,ne=F),n(29,V=!0),bn(F)}const $e={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote with 'git push -u origin <branch>'.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};async function be(){n(5,U=!0),n(4,D=!0);const{repos:F,error:G,isDevDeploy:W}=await i.listUserRepos();if(W)return await async function(){console.warn("Fetching branches from local git environment.");const{remoteUrl:fe,error:Re}=await i.getRemoteUrl();n(1,w=!0);const Ee=Ct(fe);if(!Ee||Re)return L(Re??$e.failedToParseRemoteUrl),void n(1,w=!1);n(2,u={name:Ee.name,owner:Ee.owner,html_url:fe}),n(6,$=[u]),n(7,v=$);const at=function(Fe){const je=Fe.find(_e=>_e.isCurrentBranch),lt=Fe.find(_e=>_e.isDefault),_n=!!je&&(je==null?void 0:je.name)===(lt==null?void 0:lt.name.replace("origin/",""));return Fe.filter(_e=>(!_n||!_e.isDefault)&&(!_e.isCurrentBranch||!_e.isRemote)&&!!_e.isRemote&&_e.isRemote)}((await i.listBranches()).branches),Ge=at.find(Fe=>Fe.isDefault);n(3,m={name:Ge!=null&&Ge.name?At(Ge.name):at[0].name,commit:{sha:"",url:""},protected:!1}),n(28,x=at.map(Fe=>({name:At(Fe.name),commit:{sha:"",url:""},protected:!1}))),n(8,I=x),N(),t||de(),n(1,w=!1)}(),void n(5,U=!1);if(G)return L(`An error occured while fetching your repos. If this continues, please contact support. Error: ${G}`),n(1,w=!1),void n(5,U=!1);if(n(6,$=F),n(7,v=$),!u&&f){const fe=$.find(Re=>Re.html_url===f);fe&&n(2,u=fe)}const{remoteUrl:Me,error:yn}=await i.getRemoteUrl(),xn=Ct(Me);if(yn)return n(1,w=!1),void n(5,U=!1);const{owner:st,name:ct}=xn||{},it=$.find(fe=>fe.name===ct&&fe.owner===st);if(it&&!u)n(2,u=it);else if(!it&&ct&&st){const fe={name:ct,owner:st,html_url:Me};try{const{repo:Re,error:Ee}=await i.getGithubRepo(fe);Ee?(console.warn("Failed to fetch GitHub repo details:",Ee),n(2,u=$[0])):(n(2,u=Re),n(6,$=[u,...$]))}catch(Re){console.error("Error fetching GitHub repo:",Re),n(2,u=$[0])}}else if(!u)return n(1,w=!1),void n(5,U=!1);n(5,U=!1)}async function H(F){if(!u)return;n(4,D=!0);const G=u;do{if(G!==u){n(4,D=!1),n(28,x=[]);break}const W=await i.listRepoBranches(u,F);if(W.error)return L(`Failed to fetch branches for the repo ${u.owner}/${u.name}. Please make sure you have access to this repo on GitHub. If this continues, please contact support. Error: ${W.error}`),void n(1,w=!1);if(n(28,x=[...x,...W.branches]),n(10,k=W.hasNextPage),Ne(),!k)break;F=W.nextPage,n(9,P++,P),oe()}while(P%20!=0&&k);n(8,I=x),n(4,D=!1)}function Ne(){if(u&&!m){if(h){const F=x.find(G=>G.name===h);if(F)return n(3,m=F),void de()}if(u.default_branch){const F=u.default_branch,G=I.find(W=>W.name===F);if(G)return n(3,m=G),void de()}D||n(3,m=x[0]),de()}}function Ie(){u&&async function(){u&&(n(9,P=0),await H(P+1))}().then(()=>{oe(),n(1,w=!1),N(),t||de()}).catch(F=>{console.error("Error fetching all branches:",F),L(`Failed to fetch branches: ${F instanceof Error?F.message:String(F)}`)})}tt(async()=>{await ye()});let J=!0;const pe=async()=>{try{n(13,J=await i.isGithubAuthenticated()),J||L("Please authenticate with GitHub to use this feature.")}catch(F){console.error("Failed to check GitHub authentication status:",F),L("Please authenticate with GitHub to use this feature."),n(13,J=!1)}};async function ye(){n(1,w=!0);try{await async function(){n(1,w=!0),B();try{if(await pe(),!J)return void n(1,w=!1);await be(),t||Ie(),N(),t||de()}catch(F){console.error("Error fetching git data:",F),L($e.failedToFetchBranches)}finally{n(1,w=!1)}}()}catch(F){console.error("Error fetching and syncing branches:",F),L("Failed to fetch repos and branches. Please try again. If this continues, please contact support.")}finally{n(1,w=!1)}}async function N(){if(!t&&!o)try{if(!o&&u&&!m&&x.length===0)return void L($e.noRemoteBranches);B()}catch(F){console.error("Error checking git repository:",F),L($e.failedToFetchFromRemote)}}function L(F){console.error("Error:",F),n(15,t=!0),n(0,g=F)}function B(){n(15,t=!1),n(0,g="")}async function oe(F=""){n(15,t=!1);try{if(V&&ne.trim()!=="")n(8,(G=F||ne,I=x.filter(W=>W.name.includes(G.toLowerCase()))));else{let W;n(8,I=x.filter(Me=>Me.name!==(u==null?void 0:u.default_branch)||(W=Me,!1))),W?I.unshift(W):u!=null&&u.default_branch&&I.unshift({name:u.default_branch,commit:{sha:"",url:""},protected:!1})}N()}catch(W){console.error("Error fetching branches:",W),n(8,I=[]),L($e.failedToFetchBranches)}var G}async function q(F){n(3,m=F),n(29,V=!1),Be((m==null?void 0:m.name)??""),oe();const G=l.creationMetrics;l.setCreationMetrics({changedRepo:(G==null?void 0:G.changedRepo)??!1,changedBranch:!0}),de()}async function xe(F){n(4,D=!0),n(2,u=F),n(3,m=void 0),n(28,x=[]),n(8,I=[]),K=!1,Te(""),n(7,v=$),Ie();const G=l.creationMetrics;l.setCreationMetrics({changedRepo:!0,changedBranch:(G==null?void 0:G.changedBranch)??!1})}function Ae(F,G){F||(G==="repo"?K=!1:(G==="branch"||(K=!1),n(29,V=!1)))}function de(){if(!(u!=null&&u.html_url)||!m)return;const F={github_commit_ref:{repository_url:u.html_url,git_ref:m.name}};a("commitRefChange",{commitRef:F,selectedBranch:m})}const Be=F=>{n(12,ne=F)},Te=F=>{n(11,X=F)},vn=_t(async function(F=""){n(15,t=!1);try{K?n(7,(G=F||X,v=$.filter(W=>W.name.includes(G.toLowerCase())||W.owner.includes(G.toLowerCase())))):n(7,v=$)}catch(W){console.error("Error fetching repos:",W),n(7,v=[]),L($e.failedToFetchFromRemote)}var G},300,{leading:!1,trailing:!0}),bn=_t(oe,300,{leading:!1,trailing:!0});function bt(F){F&&c||Ae(F,"branch")}function yt(F){F&&!$.length||Ae(F,"repo")}return r.$$set=F=>{"errorMessage"in F&&n(0,g=F.errorMessage),"isLoading"in F&&n(1,w=F.isLoading),"lastUsedBranchName"in F&&n(26,h=F.lastUsedBranchName),"lastUsedRepoUrl"in F&&n(27,f=F.lastUsedRepoUrl)},r.$$.update=()=>{1&r.$$.dirty[0]&&n(15,t=g!==""),50&r.$$.dirty[0]&&(o=w||D||U),28&r.$$.dirty[0]&&n(16,s=u&&D&&!m),536870920&r.$$.dirty[0]&&Be(V?"":(m==null?void 0:m.name)??""),805306372&r.$$.dirty[0]&&n(14,c=!u||!V&&!x.length)},[g,w,u,m,D,U,$,v,I,P,k,X,ne,J,c,t,s,re,me,H,pe,ye,q,xe,bt,yt,h,f,x,V,function(F){X=F,n(11,X)},F=>yt(F.detail),F=>re(F.detail),F=>xe(F.detail),()=>{H(P+1)},function(F){ne=F,n(12,ne)},F=>bt(F.detail),F=>me(F.detail),F=>q(F.detail),async()=>{await pe(),J&&await ye()}]}class xr extends se{constructor(e){super(),ce(this,e,yr,gr,ie,{errorMessage:0,isLoading:1,lastUsedBranchName:26,lastUsedRepoUrl:27},null,[-1,-1,-1])}}function _r(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=ae(o,t[s]);return{c(){e=We("svg"),n=new Xe(!0),this.h()},l(s){e=Je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ye(c,!0),c.forEach(b),this.h()},h(){n.a=null,ke(e,o)},m(s,c){Ze(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',e)},p(s,[c]){ke(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&s[0]]))},i:z,o:z,d(s){s&&b(e)}}}function Lr(r,e,n){return r.$$set=t=>{n(0,e=ae(ae({},e),Se(t)))},[e=Se(e)]}class Cr extends se{constructor(e){super(),ce(this,e,Lr,_r,ie,{})}}function Ar(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=ae(o,t[s]);return{c(){e=We("svg"),n=new Xe(!0),this.h()},l(s){e=Je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ye(c,!0),c.forEach(b),this.h()},h(){n.a=null,ke(e,o)},m(s,c){Ze(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M234.7 42.7 197 56.8c-3 1.1-5 4-5 7.2s2 6.1 5 7.2l37.7 14.1 14.1 37.7c1.1 3 4 5 7.2 5s6.1-2 7.2-5l14.1-37.7L315 71.2c3-1.1 5-4 5-7.2s-2-6.1-5-7.2l-37.7-14.1L263.2 5c-1.1-3-4-5-7.2-5s-6.1 2-7.2 5zM461.4 48 496 82.6 386.2 192.3l-34.6-34.6zM80 429.4l237.7-237.7 34.6 34.6L114.6 464zM427.4 14.1 46.1 395.4c-18.7 18.7-18.7 49.1 0 67.9l34.6 34.6c18.7 18.7 49.1 18.7 67.9 0l381.3-381.4c18.7-18.7 18.7-49.1 0-67.9l-34.6-34.5c-18.7-18.7-49.1-18.7-67.9 0M7.5 117.2C3 118.9 0 123.2 0 128s3 9.1 7.5 10.8L64 160l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L128 160l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L128 96l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L64 96zm352 256c-4.5 1.7-7.5 6-7.5 10.8s3 9.1 7.5 10.8L416 416l21.2 56.5c1.7 4.5 6 7.5 10.8 7.5s9.1-3 10.8-7.5L480 416l56.5-21.2c4.5-1.7 7.5-6 7.5-10.8s-3-9.1-7.5-10.8L480 352l-21.2-56.5c-1.7-4.5-6-7.5-10.8-7.5s-9.1 3-10.8 7.5L416 352z"/>',e)},p(s,[c]){ke(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 576 512"},1&c&&s[0]]))},i:z,o:z,d(s){s&&b(e)}}}function Rr(r,e,n){return r.$$set=t=>{n(0,e=ae(ae({},e),Se(t)))},[e=Se(e)]}class kr extends se{constructor(e){super(),ce(this,e,Rr,Ar,ie,{})}}const Sr=r=>({}),Zt=r=>({});function en(r){let e,n;const t=r[12].icon,o=Q(t,r,r[11],Zt);return{c(){e=S("div"),o&&o.c(),y(e,"class","c-setup-script-selector__icon svelte-udt6j8")},m(s,c){_(s,e,c),o&&o.m(e,null),n=!0},p(s,c){o&&o.p&&(!n||2048&c)&&Y(o,t,s,s[11],n?ee(t,s[11],c,Sr):Z(s[11]),Zt)},i(s){n||(p(o,s),n=!0)},o(s){d(o,s),n=!1},d(s){s&&b(e),o&&o.d(s)}}}function Ir(r){let e,n,t,o,s;return{c(){e=S("span"),n=T(r[0]),t=M(),o=S("span"),s=T(r[1]),y(e,"class","c-setup-script-selector__script-name svelte-udt6j8"),y(o,"class","c-setup-script-selector__script-path svelte-udt6j8")},m(c,i){_(c,e,i),E(e,n),_(c,t,i),_(c,o,i),E(o,s)},p(c,i){1&i&&te(n,c[0]),2&i&&te(s,c[1])},i:z,o:z,d(c){c&&(b(e),b(t),b(o))}}}function Fr(r){let e,n,t,o,s,c,i,a,l;function u(h){r[15](h)}function m(h){r[16](h)}let g={size:1,variant:"surface"};r[6]!==void 0&&(g.value=r[6]),r[5]!==void 0&&(g.textInput=r[5]),t=new En({props:g}),he.push(()=>we(t,"value",u)),he.push(()=>we(t,"textInput",m)),t.$on("keydown",r[8]),t.$on("blur",r[9]);let w=r[7]&&function(h){let f;return{c(){f=S("span"),f.textContent=`${h[7]}`,y(f,"class","c-setup-script-selector__extension svelte-udt6j8")},m($,v){_($,f,v)},p:z,d($){$&&b(f)}}}(r);return{c(){e=S("div"),n=S("div"),C(t.$$.fragment),c=M(),w&&w.c(),y(n,"class","c-setup-script-selector__rename-input-container svelte-udt6j8"),y(n,"role","presentation"),y(e,"class","c-setup-script-selector__rename-input svelte-udt6j8"),y(e,"role","presentation")},m(h,f){_(h,e,f),E(e,n),A(t,n,null),E(n,c),w&&w.m(n,null),i=!0,a||(l=[Le(e,"click",Ve(r[13])),Le(e,"mousedown",Ve(r[14]))],a=!0)},p(h,f){const $={};!o&&64&f&&(o=!0,$.value=h[6],ve(()=>o=!1)),!s&&32&f&&(s=!0,$.textInput=h[5],ve(()=>s=!1)),t.$set($),h[7]&&w.p(h,f)},i(h){i||(p(t.$$.fragment,h),i=!0)},o(h){d(t.$$.fragment,h),i=!1},d(h){h&&b(e),R(t),w&&w.d(),a=!1,ft(l)}}}function Nr(r){let e,n,t,o,s,c,i,a,l=r[10].icon&&en(r);const u=[Fr,Ir],m=[];function g(f,$){return f[3]?0:1}o=g(r),s=m[o]=u[o](r);const w=r[12].default,h=Q(w,r,r[11],null);return{c(){e=S("div"),l&&l.c(),n=M(),t=S("div"),s.c(),c=M(),i=S("div"),h&&h.c(),y(t,"class","c-setup-script-selector__script-info svelte-udt6j8"),y(i,"class","c-setup-script-selector__script-actions svelte-udt6j8"),y(e,"class","c-setup-script-selector__script-item-content svelte-udt6j8"),y(e,"role","presentation"),ge(e,"c-setup-script-selector__script-item-content--renaming",r[3]),ge(e,"c-setup-script-selector__script-item-content--is-path",r[2]),ge(e,"c-setup-script-selector__script-item-content--selected",r[4])},m(f,$){_(f,e,$),l&&l.m(e,null),E(e,n),E(e,t),m[o].m(t,null),E(e,c),E(e,i),h&&h.m(i,null),a=!0},p(f,[$]){f[10].icon?l?(l.p(f,$),1024&$&&p(l,1)):(l=en(f),l.c(),p(l,1),l.m(e,n)):l&&(j(),d(l,1,1,()=>{l=null}),O());let v=o;o=g(f),o===v?m[o].p(f,$):(j(),d(m[v],1,1,()=>{m[v]=null}),O(),s=m[o],s?s.p(f,$):(s=m[o]=u[o](f),s.c()),p(s,1),s.m(t,null)),h&&h.p&&(!a||2048&$)&&Y(h,w,f,f[11],a?ee(w,f[11],$,null):Z(f[11]),null),(!a||8&$)&&ge(e,"c-setup-script-selector__script-item-content--renaming",f[3]),(!a||4&$)&&ge(e,"c-setup-script-selector__script-item-content--is-path",f[2]),(!a||16&$)&&ge(e,"c-setup-script-selector__script-item-content--selected",f[4])},i(f){a||(p(l),p(s),p(h,f),a=!0)},o(f){d(l),d(s),d(h,f),a=!1},d(f){f&&b(e),l&&l.d(),m[o].d(),h&&h.d(f)}}}function Er(r,e,n){let{$$slots:t={},$$scope:o}=e;const s=dt(t);let{name:c}=e,{path:i}=e,{isPath:a=!1}=e,{isRenaming:l=!1}=e,{isSelected:u=!1}=e;const m=Pe(),{baseName:g,extension:w}=function($){const v=$.lastIndexOf(".");return v===-1?{baseName:$,extension:""}:{baseName:$.substring(0,v),extension:$.substring(v)}}(c);let h,f=g;return r.$$set=$=>{"name"in $&&n(0,c=$.name),"path"in $&&n(1,i=$.path),"isPath"in $&&n(2,a=$.isPath),"isRenaming"in $&&n(3,l=$.isRenaming),"isSelected"in $&&n(4,u=$.isSelected),"$$scope"in $&&n(11,o=$.$$scope)},r.$$.update=()=>{40&r.$$.dirty&&l&&h&&setTimeout(()=>{h==null||h.focus(),h==null||h.select()},0)},[c,i,a,l,u,h,f,w,function($){if($.key!=="ArrowLeft"&&$.key!=="ArrowRight"&&$.key!=="ArrowUp"&&$.key!=="ArrowDown")if($.key==="Enter")if($.preventDefault(),f.trim()&&f!==g){const v=f.trim()+w;m("rename",{oldName:c,newName:v})}else m("cancelRename");else $.key==="Escape"&&($.preventDefault(),$.stopPropagation(),m("cancelRename"));else $.stopPropagation()},function(){m("cancelRename")},s,o,t,function($){Oe.call(this,r,$)},function($){Oe.call(this,r,$)},function($){f=$,n(6,f)},function($){h=$,n(5,h)}]}class Dr extends se{constructor(e){super(),ce(this,e,Er,Nr,ie,{name:0,path:1,isPath:2,isRenaming:3,isSelected:4})}}function tn(r){let e,n,t,o,s,c,i,a,l,u;function m(w){r[34](w)}let g={placeholder:"Search scripts...",isLoading:r[1],disabled:!1,items:r[7],selectedItem:r[2],itemLabelFn:ms,itemKeyFn:$s,isItemSelected:ps,noItemsLabel:"No scripts found",loadingLabel:"Loading scripts...",$$slots:{item:[Yr,({item:w})=>({45:w}),({item:w})=>[0,w?16384:0]],searchIcon:[jr],icon:[Gr],title:[Tr]},$$scope:{ctx:r}};return r[3]!==void 0&&(g.searchValue=r[3]),t=new pt({props:g}),he.push(()=>we(t,"searchValue",m)),t.$on("openChange",r[35]),t.$on("search",r[36]),t.$on("select",r[37]),i=new De({props:{content:r[10],$$slots:{default:[ns]},$$scope:{ctx:r}}}),l=new De({props:{content:"Open a new file for you to write a setup script that you can edit directly.",$$slots:{default:[cs]},$$scope:{ctx:r}}}),{c(){e=S("div"),n=S("div"),C(t.$$.fragment),s=M(),c=S("div"),C(i.$$.fragment),a=M(),C(l.$$.fragment),y(c,"class","c-setup-script-selector__action-buttons svelte-3cd2r2"),y(n,"class","c-setup-script-selector__script-line svelte-3cd2r2"),y(e,"class","c-setup-script-selector__script-line-container svelte-3cd2r2")},m(w,h){_(w,e,h),E(e,n),A(t,n,null),E(n,s),E(n,c),A(i,c,null),E(c,a),A(l,c,null),u=!0},p(w,h){const f={};2&h[0]&&(f.isLoading=w[1]),128&h[0]&&(f.items=w[7]),4&h[0]&&(f.selectedItem=w[2]),884&h[0]|49152&h[1]&&(f.$$scope={dirty:h,ctx:w}),!o&&8&h[0]&&(o=!0,f.searchValue=w[3],ve(()=>o=!1)),t.$set(f);const $={};1024&h[0]&&($.content=w[10]),2048&h[0]|32768&h[1]&&($.$$scope={dirty:h,ctx:w}),i.$set($);const v={};32768&h[1]&&(v.$$scope={dirty:h,ctx:w}),l.$set(v)},i(w){u||(p(t.$$.fragment,w),p(i.$$.fragment,w),p(l.$$.fragment,w),u=!0)},o(w){d(t.$$.fragment,w),d(i.$$.fragment,w),d(l.$$.fragment,w),u=!1},d(w){w&&b(e),R(t),R(i),R(l)}}}function Pr(r){let e,n;return e=new gn({props:{$$slots:{text:[Br]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};16&o[0]|32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Ur(r){let e,n;return e=new gn({props:{$$slots:{grayText:[zr],text:[Mr]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};768&o[0]|32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Br(r){let e,n;return{c(){e=S("span"),n=T(r[4]),y(e,"slot","text")},m(t,o){_(t,e,o),E(e,n)},p(t,o){16&o[0]&&te(n,t[4])},d(t){t&&b(e)}}}function Mr(r){let e,n;return{c(){e=S("span"),n=T(r[9]),y(e,"slot","text")},m(t,o){_(t,e,o),E(e,n)},p(t,o){512&o[0]&&te(n,t[9])},d(t){t&&b(e)}}}function zr(r){let e,n;return{c(){e=S("span"),n=T(r[8]),y(e,"slot","grayText")},m(t,o){_(t,e,o),E(e,n)},p(t,o){256&o[0]&&te(n,t[8])},d(t){t&&b(e)}}}function Tr(r){let e,n,t,o;const s=[Ur,Pr],c=[];function i(a,l){return a[5]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=S("div"),t.c(),y(e,"slot","title")},m(a,l){_(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n===u?c[n].p(a,l):(j(),d(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t?t.p(a,l):(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function Gr(r){let e,n;return e=new fn({props:{slot:"icon"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function jr(r){let e,n;return e=new ht({props:{slot:"searchIcon"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Or(r){var t;let e,n;return e=new Dr({props:{name:r[45].name,path:r[45].path,isPath:!0,isRenaming:((t=r[6])==null?void 0:t.path)===r[45].path,isSelected:!(!r[2]||r[2].path!==r[45].path),$$slots:{default:[Qr]},$$scope:{ctx:r}}}),e.$on("rename",function(...o){return r[33](r[45],...o)}),e.$on("cancelRename",r[21]),{c(){C(e.$$.fragment)},m(o,s){A(e,o,s),n=!0},p(o,s){var i;r=o;const c={};16384&s[1]&&(c.name=r[45].name),16384&s[1]&&(c.path=r[45].path),64&s[0]|16384&s[1]&&(c.isRenaming=((i=r[6])==null?void 0:i.path)===r[45].path),4&s[0]|16384&s[1]&&(c.isSelected=!(!r[2]||r[2].path!==r[45].path)),49152&s[1]&&(c.$$scope={dirty:s,ctx:r}),e.$set(c)},i(o){n||(p(e.$$.fragment,o),n=!0)},o(o){d(e.$$.fragment,o),n=!1},d(o){R(e,o)}}}function Vr(r){let e,n,t,o;return n=new fn({}),{c(){e=S("div"),C(n.$$.fragment),t=T(`
                  Use basic environment`),y(e,"class","c-setup-script-selector__basic-option svelte-3cd2r2")},m(s,c){_(s,e,c),A(n,e,null),E(e,t),o=!0},p:z,i(s){o||(p(n.$$.fragment,s),o=!0)},o(s){d(n.$$.fragment,s),o=!1},d(s){s&&b(e),R(n)}}}function Hr(r){let e,n;return e=new Dn({}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function qr(r){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Hr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[30](r[45],...t)}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Kr(r){let e,n;return e=new Cr({}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Wr(r){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Kr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[31](r[45],...t)}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Xr(r){let e,n;return e=new Fn({}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Jr(r){let e,n;return e=new vt({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[Xr]},$$scope:{ctx:r}}}),e.$on("click",function(...t){return r[32](r[45],...t)}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){r=t;const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Qr(r){let e,n,t,o,s,c;return e=new De({props:{content:"Open script in editor",$$slots:{default:[qr]},$$scope:{ctx:r}}}),t=new De({props:{content:"Rename script",$$slots:{default:[Wr]},$$scope:{ctx:r}}}),s=new De({props:{content:"Delete script",$$slots:{default:[Jr]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment),n=M(),C(t.$$.fragment),o=M(),C(s.$$.fragment)},m(i,a){A(e,i,a),_(i,n,a),A(t,i,a),_(i,o,a),A(s,i,a),c=!0},p(i,a){const l={};49152&a[1]&&(l.$$scope={dirty:a,ctx:i}),e.$set(l);const u={};49152&a[1]&&(u.$$scope={dirty:a,ctx:i}),t.$set(u);const m={};49152&a[1]&&(m.$$scope={dirty:a,ctx:i}),s.$set(m)},i(i){c||(p(e.$$.fragment,i),p(t.$$.fragment,i),p(s.$$.fragment,i),c=!0)},o(i){d(e.$$.fragment,i),d(t.$$.fragment,i),d(s.$$.fragment,i),c=!1},d(i){i&&(b(n),b(o)),R(e,i),R(t,i),R(s,i)}}}function Yr(r){let e,n,t,o;const s=[Vr,Or],c=[];function i(a,l){return a[45]===null?0:1}return e=i(r),n=c[e]=s[e](r),{c(){n.c(),t=ue()},m(a,l){c[e].m(a,l),_(a,t,l),o=!0},p(a,l){let u=e;e=i(a),e===u?c[e].p(a,l):(j(),d(c[u],1,1,()=>{c[u]=null}),O(),n=c[e],n?n.p(a,l):(n=c[e]=s[e](a),n.c()),p(n,1),n.m(t.parentNode,t))},i(a){o||(p(n),o=!0)},o(a){d(n),o=!1},d(a){a&&b(t),c[e].d(a)}}}function Zr(r){let e,n;return{c(){e=T("Auto-generate"),n=S("span"),n.textContent="a script",y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(t,o){_(t,e,o),_(t,n,o)},p:z,d(t){t&&(b(e),b(n))}}}function es(r){let e,n;return e=new kr({props:{slot:"iconLeft"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function ts(r){let e,n;return e=new un({props:{slot:"iconRight"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function ns(r){let e,n;return e=new Ue({props:{variant:"soft",color:"neutral",size:1,disabled:r[11],$$slots:{iconRight:[ts],iconLeft:[es],default:[Zr]},$$scope:{ctx:r}}}),e.$on("click",r[14]),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};2048&o[0]&&(s.disabled=t[11]),32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function os(r){let e,n,t;return{c(){e=T("Write "),n=S("span"),n.textContent="a script",t=T("by hand"),y(n,"class","c-setup-script-selector__long-text svelte-3cd2r2")},m(o,s){_(o,e,s),_(o,n,s),_(o,t,s)},p:z,d(o){o&&(b(e),b(n),b(t))}}}function rs(r){let e,n;return e=new In({props:{slot:"iconLeft"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function ss(r){let e,n;return e=new un({props:{slot:"iconRight"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function cs(r){let e,n;return e=new Ue({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[ss],iconLeft:[rs],default:[os]},$$scope:{ctx:r}}}),e.$on("click",r[15]),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};32768&o[1]&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function nn(r){let e,n,t;return n=new rt({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[ls]},$$scope:{ctx:r}}}),{c(){e=S("div"),C(n.$$.fragment),y(e,"class","c-setup-script-selector__error svelte-3cd2r2")},m(o,s){_(o,e,s),A(n,e,null),t=!0},p(o,s){const c={};3&s[0]|32768&s[1]&&(c.$$scope={dirty:s,ctx:o}),n.$set(c)},i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),R(n)}}}function is(r){let e;return{c(){e=T("Refresh")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function as(r){let e,n,t;return n=new mn({}),{c(){e=S("span"),C(n.$$.fragment),y(e,"slot","iconLeft")},m(o,s){_(o,e,s),A(n,e,null),t=!0},p:z,i(o){t||(p(n.$$.fragment,o),t=!0)},o(o){d(n.$$.fragment,o),t=!1},d(o){o&&b(e),R(n)}}}function ls(r){let e,n,t,o,s,c;return s=new Ue({props:{variant:"ghost",color:"warning",size:1,loading:r[1],$$slots:{iconLeft:[as],default:[is]},$$scope:{ctx:r}}}),s.$on("click",r[17]),{c(){e=S("div"),n=S("div"),t=T(r[0]),o=M(),C(s.$$.fragment),y(n,"class","c-setup-script-selector__error-message svelte-3cd2r2"),y(e,"class","c-setup-script-selector__error-content svelte-3cd2r2")},m(i,a){_(i,e,a),E(e,n),E(n,t),E(e,o),A(s,e,null),c=!0},p(i,a){(!c||1&a[0])&&te(t,i[0]);const l={};2&a[0]&&(l.loading=i[1]),32768&a[1]&&(l.$$scope={dirty:a,ctx:i}),s.$set(l)},i(i){c||(p(s.$$.fragment,i),c=!0)},o(i){d(s.$$.fragment,i),c=!1},d(i){i&&b(e),R(s)}}}function us(r){let e,n,t,o,s=(!r[12]||r[0]===r[16].noScriptsFound)&&tn(r),c=r[12]&&r[0]!==r[16].noScriptsFound&&nn(r);return{c(){e=S("div"),n=S("div"),s&&s.c(),t=M(),c&&c.c(),y(n,"class","c-setup-script-selector__content svelte-3cd2r2"),y(e,"class","c-setup-script-selector svelte-3cd2r2")},m(i,a){_(i,e,a),E(e,n),s&&s.m(n,null),E(n,t),c&&c.m(n,null),o=!0},p(i,a){i[12]&&i[0]!==i[16].noScriptsFound?s&&(j(),d(s,1,1,()=>{s=null}),O()):s?(s.p(i,a),4097&a[0]&&p(s,1)):(s=tn(i),s.c(),p(s,1),s.m(n,t)),i[12]&&i[0]!==i[16].noScriptsFound?c?(c.p(i,a),4097&a[0]&&p(c,1)):(c=nn(i),c.c(),p(c,1),c.m(n,null)):c&&(j(),d(c,1,1,()=>{c=null}),O())},i(i){o||(p(s),p(c),o=!0)},o(i){d(s),d(c),o=!1},d(i){i&&b(e),s&&s.d(),c&&c.d()}}}const ms=r=>(r==null?void 0:r.name)||"",$s=r=>`${r==null?void 0:r.path}-${r==null?void 0:r.location}-${r==null?void 0:r.name}`,ps=(r,e)=>r===null&&e===null||!(!r||!e)&&r.path===e.path;function ds(r,e,n){var ye;let t,o,s,c,i,a,l,u,{errorMessage:m=""}=e,{isLoading:g=!1}=e,{lastUsedScriptPath:w=null}=e,{disableNewAgentCreation:h=!1}=e;const f=Ce(ot.key),$=Pe(),v=Ce("chatModel").extensionClient,x=N=>{v.openFile({repoRoot:"",pathName:N.path,allowOutOfWorkspace:!0,openLocalUri:N.location==="home"})};let I=[],D=((ye=f.newAgentDraft)==null?void 0:ye.setupScript)??null,U="",P=null,k=I,K=!0;const V={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function X(){n(0,m="");try{const N=D==null?void 0:D.path;if(n(28,I=await f.listSetupScripts()),K)if(w&&I.length>0){const L=I.find(B=>B.path===w);L&&(n(2,D=L),pe())}else w===null&&(n(2,D=null),pe());else if(N){const L=I.find(B=>B.path===N);L&&n(2,D=L)}K=!1,I.length===0?n(0,m=V.noScriptsFound):n(0,m="")}catch(N){console.error("Error fetching setup scripts:",N),n(0,m=V.failedToFetchScripts)}}async function ne(N,L){L&&L.stopPropagation();try{const B=await f.deleteSetupScript(N.name,N.location);B.success?((D==null?void 0:D.path)===N.path&&J(null),await X()):(console.error("Failed to delete script:",B.error),be(`Failed to delete script: ${B.error||"Unknown error"}`))}catch(B){console.error("Error deleting script:",B),be(`Error deleting script: ${B instanceof Error?B.message:String(B)}`)}}async function re(N,L){L&&L.stopPropagation(),n(6,P=N)}async function me(N,L){const{oldName:B,newName:oe}=L.detail;try{const q=await f.renameSetupScript(B,oe,N.location);if(q.success){await X();const xe=I.find(Ae=>Ae.path===q.path);xe&&J(xe)}else console.error("Failed to rename script:",q.error),be(`Failed to rename script: ${q.error||"Unknown error"}`)}catch(q){console.error("Error renaming script:",q),be(`Error renaming script: ${q instanceof Error?q.message:String(q)}`)}finally{$e()}}function $e(){n(6,P=null)}function be(N){n(0,m=N)}function H(N){n(3,U=N)}function Ne(N){J(N)}function Ie(N){N&&(X(),n(3,U=""))}async function J(N){n(2,D=N),pe(),f.saveLastRemoteAgentSetup(null,null,(D==null?void 0:D.path)||null)}function pe(){$("setupScriptChange",{script:D})}return tt(async()=>{var N;await X(),w===null?J(null):(N=f.newAgentDraft)!=null&&N.setupScript&&!D&&J(f.newAgentDraft.setupScript)}),r.$$set=N=>{"errorMessage"in N&&n(0,m=N.errorMessage),"isLoading"in N&&n(1,g=N.isLoading),"lastUsedScriptPath"in N&&n(26,w=N.lastUsedScriptPath),"disableNewAgentCreation"in N&&n(27,h=N.disableNewAgentCreation)},r.$$.update=()=>{var N,L;if(1&r.$$.dirty[0]&&n(12,t=m!==""),134217728&r.$$.dirty[0]&&n(11,o=h||((N=f.newAgentDraft)==null?void 0:N.isDisabled)||!f.newAgentDraft),134217728&r.$$.dirty[0]&&n(10,s=f.newAgentDraft?(L=f.newAgentDraft)!=null&&L.isDisabled?"Please resolve the issues with your workspace selection":h?"Agent limit reached or other restrictions apply":"An AI agent will automatically generate a setup script for your project.":"Please select a repository and branch first"),268435464&r.$$.dirty[0])if(U.trim()!==""){const B="Use basic environment".toLowerCase().includes(U.toLowerCase()),oe=I.filter(q=>q.name.toLowerCase().includes(U.toLowerCase())||q.path.toLowerCase().includes(U.toLowerCase()));n(7,k=B?[null,...oe]:oe)}else n(7,k=[null,...I]);6&r.$$.dirty[0]&&n(29,c=()=>g?"...":D?D.isGenerateOption?D.name:D.location==="home"?"~/.augment/env/"+D.name:D.path:"Use basic environment"),536870912&r.$$.dirty[0]&&n(4,i=c()),4&r.$$.dirty[0]&&n(5,a=!!(D!=null&&D.path)),48&r.$$.dirty[0]&&n(9,l=a?i.split("/").pop():i),48&r.$$.dirty[0]&&n(8,u=a?i.slice(0,i.lastIndexOf("/")):"")},[m,g,D,U,i,a,P,k,u,l,s,o,t,x,async()=>{try{const N=f.newAgentDraft;N&&f.setNewAgentDraft({...N,isSetupScriptAgent:!0});const L=await f.createRemoteAgentFromDraft("SETUP_MODE");return L&&f.setCurrentAgent(L),L}catch(N){console.error("Failed to select setup script generation:",N)}},async()=>{try{const N="setup.sh",L=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,B=await f.saveSetupScript(N,L,"home");if(B.success&&B.path){await X();const oe=I.find(q=>q.path===B.path);oe&&(J(oe),x(oe))}else console.error("Failed to create manual setup script:",B.error),n(0,m=`Failed to create manual setup script: ${B.error||"Unknown error"}`)}catch(N){console.error("Error creating manual setup script:",N),n(0,m=`Error creating manual setup script: ${N instanceof Error?N.message:String(N)}`)}},V,X,ne,re,me,$e,H,Ne,Ie,J,w,h,I,c,(N,L)=>{L.stopPropagation(),x(N),J(N)},(N,L)=>{L.stopPropagation(),re(N)},(N,L)=>{L.stopPropagation(),ne(N)},(N,L)=>me(N,L),function(N){U=N,n(3,U)},N=>Ie(N.detail),N=>H(N.detail),N=>Ne(N.detail)]}class fs extends se{constructor(e){super(),ce(this,e,ds,us,ie,{errorMessage:0,isLoading:1,lastUsedScriptPath:26,disableNewAgentCreation:27},null,[-1,-1])}}function gs(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},r[0]],o={};for(let s=0;s<t.length;s+=1)o=ae(o,t[s]);return{c(){e=We("svg"),n=new Xe(!0),this.h()},l(s){e=Je(s,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=Qe(e);n=Ye(c,!0),c.forEach(b),this.h()},h(){n.a=null,ke(e,o)},m(s,c){Ze(s,e,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M48 112v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48zm224 0v288h48V112zm-48 0c0-26.5 21.5-48 48-48h48c26.5 0 48 21.5 48 48v288c0 26.5-21.5 48-48 48h-48c-26.5 0-48-21.5-48-48z"/>',e)},p(s,[c]){ke(e,o=et(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 320 512"},1&c&&s[0]]))},i:z,o:z,d(s){s&&b(e)}}}function hs(r,e,n){return r.$$set=t=>{n(0,e=ae(ae({},e),Se(t)))},[e=Se(e)]}class ws extends se{constructor(e){super(),ce(this,e,hs,gs,ie,{})}}function on(r){let e,n;return e=new rt({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[Ls],default:[_s]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};16414&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function vs(r){let e;return{c(){e=T(r[4])},m(n,t){_(n,e,t)},p(n,t){16&t&&te(e,n[4])},d(n){n&&b(e)}}}function bs(r){let e,n;return e=new Un({}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function ys(r){let e,n;return e=new ws({}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function xs(r){let e,n,t,o;const s=[ys,bs],c=[];function i(a,l){return a[1]?0:1}return n=i(r),t=c[n]=s[n](r),{c(){e=S("div"),t.c(),y(e,"slot","iconLeft")},m(a,l){_(a,e,l),c[n].m(e,null),o=!0},p(a,l){let u=n;n=i(a),n!==u&&(j(),d(c[u],1,1,()=>{c[u]=null}),O(),t=c[n],t||(t=c[n]=s[n](a),t.c()),p(t,1),t.m(e,null))},i(a){o||(p(t),o=!0)},o(a){d(t),o=!1},d(a){a&&b(e),c[n].d()}}}function _s(r){let e,n,t,o,s,c,i=(r[2]?mt:$t).replace("%MAX_AGENTS%",(r[2]?r[3].maxRemoteAgents:r[3].maxActiveRemoteAgents).toString())+"";return s=new Ue({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[xs],default:[vs]},$$scope:{ctx:r}}}),s.$on("click",r[11]),{c(){e=S("div"),n=S("p"),t=T(i),o=M(),C(s.$$.fragment),y(n,"class","svelte-f3wuoa"),y(e,"class","agent-limit-message svelte-f3wuoa")},m(a,l){_(a,e,l),E(e,n),E(n,t),E(e,o),A(s,e,null),c=!0},p(a,l){(!c||12&l)&&i!==(i=(a[2]?mt:$t).replace("%MAX_AGENTS%",(a[2]?a[3].maxRemoteAgents:a[3].maxActiveRemoteAgents).toString())+"")&&te(t,i);const u={};16402&l&&(u.$$scope={dirty:l,ctx:a}),s.$set(u)},i(a){c||(p(s.$$.fragment,a),c=!0)},o(a){d(s.$$.fragment,a),c=!1},d(a){a&&b(e),R(s)}}}function Ls(r){let e,n;return e=new Pn({props:{slot:"icon"}}),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p:z,i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Cs(r){let e,n,t=!!r[0]&&on(r);return{c(){t&&t.c(),e=ue()},m(o,s){t&&t.m(o,s),_(o,e,s),n=!0},p(o,[s]){o[0]?t?(t.p(o,s),1&s&&p(t,1)):(t=on(o),t.c(),p(t,1),t.m(e.parentNode,e)):t&&(j(),d(t,1,1,()=>{t=null}),O())},i(o){n||(p(t),n=!0)},o(o){d(t),n=!1},d(o){o&&b(e),t&&t.d(o)}}}function rn(r){if(!r)return;const e=r.is_setup_script_agent?"Setup script generation":r.session_summary||"";return{id:r.remote_agent_id,title:e.length>30?e.substring(0,27)+"...":e}}function sn(r,e){return r.replace("%MAX_AGENTS%",e.toString())}function As(r,e,n){let t,o,s,{agentLimitErrorMessage:c}=e;const i=Ce(ot.key);ut(r,i,$=>n(3,s=$));let a,l,u,m=!1,g=[];function w(){return s.agentOverviews.sort(($,v)=>new Date($.started_at).getTime()-new Date(v.started_at).getTime())}async function h(){if(!m&&(a!=null&&a.id))try{m=!0,await i.deleteAgent(a.id)}catch($){console.error("Failed to delete oldest agent:",$)}finally{m=!1}}async function f(){if(!m&&(l!=null&&l.id))try{m=!0,await i.pauseRemoteAgentWorkspace(l.id)}catch($){console.error("Failed to pause oldest active agent:",$)}finally{m=!1}}return r.$$set=$=>{"agentLimitErrorMessage"in $&&n(0,c=$.agentLimitErrorMessage)},r.$$.update=()=>{if(8&r.$$.dirty&&n(2,t=!!s.maxRemoteAgents&&s.agentOverviews.length>=s.maxRemoteAgents),8&r.$$.dirty&&n(1,o=!!s.maxActiveRemoteAgents&&s.agentOverviews.filter($=>$.workspace_status===Lt.workspaceRunning).length>=s.maxActiveRemoteAgents),1806&r.$$.dirty)if(t)n(10,g=w()),n(8,a=rn(g[0])),n(0,c=sn(mt,s.maxRemoteAgents)),n(4,u="Delete Oldest Agent"+(a?`: ${a.title}`:""));else if(o){n(10,g=w());const $=g.filter(v=>v.workspace_status===Lt.workspaceRunning);n(9,l=rn($[0])),n(0,c=sn($t,s.maxActiveRemoteAgents)),n(4,u="Pause Oldest Agent"+(l?`: ${l.title}`:""))}else n(8,a=void 0),n(0,c=void 0)},[c,o,t,s,u,i,h,f,a,l,g,()=>{o?f():h()}]}class Rs extends se{constructor(e){super(),ce(this,e,As,Cs,ie,{agentLimitErrorMessage:0})}}function cn(r){let e,n,t,o;return n=new rt({props:{color:"error",variant:"soft",size:2,$$slots:{default:[ks]},$$scope:{ctx:r}}}),{c(){e=S("div"),C(n.$$.fragment),y(e,"class","error-message svelte-1klrgvd")},m(s,c){_(s,e,c),A(n,e,null),o=!0},p(s,c){const i={};33554496&c&&(i.$$scope={dirty:c,ctx:s}),n.$set(i)},i(s){o||(p(n.$$.fragment,s),s&&ln(()=>{o&&(t||(t=He(e,qe,{y:10},!0)),t.run(1))}),o=!0)},o(s){d(n.$$.fragment,s),s&&(t||(t=He(e,qe,{y:10},!1)),t.run(0)),o=!1},d(s){s&&b(e),R(n),s&&t&&t.end()}}}function ks(r){let e,n=r[6].remoteAgentCreationError+"";return{c(){e=T(n)},m(t,o){_(t,e,o)},p(t,o){64&o&&n!==(n=t[6].remoteAgentCreationError+"")&&te(e,n)},d(t){t&&b(e)}}}function Ss(r){let e;return{c(){e=T("Create agent")},m(n,t){_(n,e,t)},d(n){n&&b(e)}}}function Is(r){let e,n;return e=new Ue({props:{variant:"solid",color:"accent",size:2,loading:r[10],disabled:r[11],$$slots:{default:[Ss]},$$scope:{ctx:r}}}),e.$on("click",r[16]),{c(){C(e.$$.fragment)},m(t,o){A(e,t,o),n=!0},p(t,o){const s={};1024&o&&(s.loading=t[10]),2048&o&&(s.disabled=t[11]),33554432&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(p(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){R(e,t)}}}function Fs(r){var pe,ye,N;let e,n,t,o,s,c,i,a,l,u,m,g,w,h,f,$,v,x,I,D,U,P,k,K,V,X,ne,re,me;function $e(L){r[18](L)}let be={};r[2]!==void 0&&(be.agentLimitErrorMessage=r[2]),i=new Rs({props:be}),he.push(()=>we(i,"agentLimitErrorMessage",$e));let H=r[6].remoteAgentCreationError&&cn(r);function Ne(L){r[19](L)}function Ie(L){r[20](L)}let J={lastUsedRepoUrl:r[7],lastUsedBranchName:r[8]};return r[0]!==void 0&&(J.errorMessage=r[0]),r[1]!==void 0&&(J.isLoading=r[1]),f=new xr({props:J}),he.push(()=>we(f,"errorMessage",Ne)),he.push(()=>we(f,"isLoading",Ie)),f.$on("commitRefChange",r[14]),P=new fs({props:{lastUsedScriptPath:r[9],disableNewAgentCreation:!!r[2]||!((pe=r[3])!=null&&pe.name)||!((N=(ye=r[4])==null?void 0:ye.github_commit_ref)!=null&&N.repository_url)}}),P.$on("setupScriptChange",r[15]),V=new An({props:{editable:!0,hasSendButton:!1}}),re=new De({props:{class:"full-width-button",content:r[5],triggerOn:[kn.Hover],$$slots:{default:[Is]},$$scope:{ctx:r}}}),{c(){e=S("div"),n=S("div"),t=S("div"),t.innerHTML=`<p>Kick off a remote agent to work <strong class="svelte-1klrgvd">in parallel</strong>, in an
        <strong class="svelte-1klrgvd">isolated environment</strong>
        that will keep running, <strong class="svelte-1klrgvd">even when you shut off your laptop</strong>.</p>`,o=M(),s=S("div"),c=S("div"),C(i.$$.fragment),u=M(),H&&H.c(),m=M(),g=S("div"),g.textContent="Start from any GitHub repo and branch:",w=M(),h=S("div"),C(f.$$.fragment),x=M(),I=S("div"),I.textContent=`Select a setup script to prepare the remote environment, so the agent can make better
        changes by running scripts, tests, and building your code:`,D=M(),U=S("div"),C(P.$$.fragment),k=M(),K=S("div"),C(V.$$.fragment),X=M(),ne=S("div"),C(re.$$.fragment),y(t,"class","main-description svelte-1klrgvd"),y(c,"class","error-message svelte-1klrgvd"),y(g,"class","description svelte-1klrgvd"),y(h,"class","commit-ref-selector svelte-1klrgvd"),y(I,"class","description svelte-1klrgvd"),y(U,"class","setup-script svelte-1klrgvd"),y(K,"class","chat svelte-1klrgvd"),y(ne,"class","create-button svelte-1klrgvd"),y(s,"class","form-fields"),y(n,"class","content svelte-1klrgvd"),y(e,"class","remote-agent-setup svelte-1klrgvd")},m(L,B){_(L,e,B),E(e,n),E(n,t),E(n,o),E(n,s),E(s,c),A(i,c,null),E(s,u),H&&H.m(s,null),E(s,m),E(s,g),E(s,w),E(s,h),A(f,h,null),E(s,x),E(s,I),E(s,D),E(s,U),A(P,U,null),E(s,k),E(s,K),A(V,K,null),E(s,X),E(s,ne),A(re,ne,null),me=!0},p(L,[B]){var de,Be,Te;const oe={};!a&&4&B&&(a=!0,oe.agentLimitErrorMessage=L[2],ve(()=>a=!1)),i.$set(oe),L[6].remoteAgentCreationError?H?(H.p(L,B),64&B&&p(H,1)):(H=cn(L),H.c(),p(H,1),H.m(s,m)):H&&(j(),d(H,1,1,()=>{H=null}),O());const q={};128&B&&(q.lastUsedRepoUrl=L[7]),256&B&&(q.lastUsedBranchName=L[8]),!$&&1&B&&($=!0,q.errorMessage=L[0],ve(()=>$=!1)),!v&&2&B&&(v=!0,q.isLoading=L[1],ve(()=>v=!1)),f.$set(q);const xe={};512&B&&(xe.lastUsedScriptPath=L[9]),28&B&&(xe.disableNewAgentCreation=!!L[2]||!((de=L[3])!=null&&de.name)||!((Te=(Be=L[4])==null?void 0:Be.github_commit_ref)!=null&&Te.repository_url)),P.$set(xe);const Ae={};32&B&&(Ae.content=L[5]),33557504&B&&(Ae.$$scope={dirty:B,ctx:L}),re.$set(Ae)},i(L){me||(p(i.$$.fragment,L),L&&ln(()=>{me&&(l||(l=He(c,qe,{y:10},!0)),l.run(1))}),p(H),p(f.$$.fragment,L),p(P.$$.fragment,L),p(V.$$.fragment,L),p(re.$$.fragment,L),me=!0)},o(L){d(i.$$.fragment,L),L&&(l||(l=He(c,qe,{y:10},!1)),l.run(0)),d(H),d(f.$$.fragment,L),d(P.$$.fragment,L),d(V.$$.fragment,L),d(re.$$.fragment,L),me=!1},d(L){L&&b(e),R(i),L&&l&&l.end(),H&&H.d(),R(f),R(P),R(V),R(re)}}}function Ns(r,e,n){let t,o,s,c,i,a,l,u,m;const g=Ce(ot.key);ut(r,g,P=>n(6,m=P));const w=Ce("chatModel");ut(r,w,P=>n(22,u=P));const h=Ce(gt.key);let f,$="",v=!1,x=null,I=null,D=null;tt(async()=>{try{const P=await g.getLastRemoteAgentSetup();n(7,x=P.lastRemoteAgentGitRepoUrl),n(8,I=P.lastRemoteAgentGitBranch),n(9,D=P.lastRemoteAgentSetupScript),await g.reportRemoteAgentEvent({eventName:Sn.setupPageOpened,remoteAgentId:"",eventData:{setupPageOpened:{}}})}catch(P){console.error("Failed to load last remote agent setup:",P)}}),an(()=>{g.setNewAgentDraft(null),g.setCreationMetrics(void 0)});const U=Rn(g,u.currentConversationModel,h);return r.$$.update=()=>{var P,k,K;64&r.$$.dirty&&n(4,t=((P=m.newAgentDraft)==null?void 0:P.commitRef)??null),64&r.$$.dirty&&n(3,o=((k=m.newAgentDraft)==null?void 0:k.selectedBranch)??null),64&r.$$.dirty&&(s=((K=m.newAgentDraft)==null?void 0:K.setupScript)??null),31&r.$$.dirty&&n(5,a=(()=>{var ne;const V=(ne=t==null?void 0:t.github_commit_ref)==null?void 0:ne.repository_url,X=o==null?void 0:o.name;return $||f||(v?"Loading repos and branches...":"")||!V&&"Please select a repository"||!X&&"Please select a branch"||(!(!v&&V&&X)&&V&&X?"Loading branch data...":"")||""})()),32&r.$$.dirty&&n(17,l=!!a),131072&r.$$.dirty&&n(11,c=l),64&r.$$.dirty&&n(10,i=m.isCreatingAgent),131136&r.$$.dirty&&g.newAgentDraft&&!m.isCreatingAgent&&g.newAgentDraft.isDisabled!==l&&g.setNewAgentDraft({...g.newAgentDraft,isDisabled:l})},[$,v,f,o,t,a,m,x,I,D,i,c,g,w,async function(P){g.setRemoteAgentCreationError(null);const k=g.newAgentDraft;k?g.setNewAgentDraft({...k,commitRef:P.detail.commitRef,selectedBranch:P.detail.selectedBranch}):g.setNewAgentDraft({commitRef:P.detail.commitRef,selectedBranch:P.detail.selectedBranch,setupScript:null,isDisabled:l,enableNotification:!0})},function(P){g.setRemoteAgentCreationError(null);const k=g.newAgentDraft;k?g.setNewAgentDraft({...k,setupScript:P.detail.script}):g.setNewAgentDraft({commitRef:null,selectedBranch:null,setupScript:P.detail.script,isDisabled:l,enableNotification:!0})},async function(){try{U(),g.saveLastRemoteAgentSetup((t==null?void 0:t.github_commit_ref.repository_url)||null,(o==null?void 0:o.name)||null,(s==null?void 0:s.path)||null)}catch(P){console.error("Failed to create agent:",P)}},l,function(P){f=P,n(2,f)},function(P){$=P,n(0,$)},function(P){v=P,n(1,v)}]}class Cc extends se{constructor(e){super(),ce(this,e,Ns,Fs,ie,{})}}export{Cc as default};
