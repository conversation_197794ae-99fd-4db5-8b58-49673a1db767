var Ye=Object.defineProperty;var Ze=(t,e,s)=>e in t?Ye(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s;var o=(t,e,s)=>Ze(t,typeof e!="symbol"?e+"":e,s);import{p as Je,A as Qe,a as et,T as P,W as tt,b as v,d as he,g as st,r as ge,s as nt,e as fe,S as at,h as it}from"./test_service_pb-B6vKXZrG.js";import{W as d,S as rt}from"./BaseButton-C6Dhmpxa.js";import{P as q,C as ot}from"./chat-types-NgqNgjwU.js";import{n as lt}from"./file-paths-BcSg4gks.js";var C,c;function ee(t,e){return!(t===null||typeof t!="object"||!("$typeName"in t)||typeof t.$typeName!="string")&&(e===void 0||e.typeName===t.$typeName)}function ct(){let t=0,e=0;for(let n=0;n<28;n+=7){let i=this.buf[this.pos++];if(t|=(127&i)<<n,!(128&i))return this.assertBounds(),[t,e]}let s=this.buf[this.pos++];if(t|=(15&s)<<28,e=(112&s)>>4,!(128&s))return this.assertBounds(),[t,e];for(let n=3;n<=31;n+=7){let i=this.buf[this.pos++];if(e|=(127&i)<<n,!(128&i))return this.assertBounds(),[t,e]}throw new Error("invalid varint")}(function(t){t[t.Canceled=1]="Canceled",t[t.Unknown=2]="Unknown",t[t.InvalidArgument=3]="InvalidArgument",t[t.DeadlineExceeded=4]="DeadlineExceeded",t[t.NotFound=5]="NotFound",t[t.AlreadyExists=6]="AlreadyExists",t[t.PermissionDenied=7]="PermissionDenied",t[t.ResourceExhausted=8]="ResourceExhausted",t[t.FailedPrecondition=9]="FailedPrecondition",t[t.Aborted=10]="Aborted",t[t.OutOfRange=11]="OutOfRange",t[t.Unimplemented=12]="Unimplemented",t[t.Internal=13]="Internal",t[t.Unavailable=14]="Unavailable",t[t.DataLoss=15]="DataLoss",t[t.Unauthenticated=16]="Unauthenticated"})(C||(C={})),function(t){t[t.DOUBLE=1]="DOUBLE",t[t.FLOAT=2]="FLOAT",t[t.INT64=3]="INT64",t[t.UINT64=4]="UINT64",t[t.INT32=5]="INT32",t[t.FIXED64=6]="FIXED64",t[t.FIXED32=7]="FIXED32",t[t.BOOL=8]="BOOL",t[t.STRING=9]="STRING",t[t.BYTES=12]="BYTES",t[t.UINT32=13]="UINT32",t[t.SFIXED32=15]="SFIXED32",t[t.SFIXED64=16]="SFIXED64",t[t.SINT32=17]="SINT32",t[t.SINT64=18]="SINT64"}(c||(c={}));const j=4294967296;function me(t){const e=t[0]==="-";e&&(t=t.slice(1));const s=1e6;let n=0,i=0;function a(r,l){const u=Number(t.slice(r,l));i*=s,n=n*s+u,n>=j&&(i+=n/j|0,n%=j)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),e?Re(n,i):te(n,i)}function ye(t,e){if({lo:t,hi:e}=function(u,g){return{lo:u>>>0,hi:g>>>0}}(t,e),e<=2097151)return String(j*e+t);const s=16777215&(t>>>24|e<<8),n=e>>16&65535;let i=(16777215&t)+6777216*s+6710656*n,a=s+8147497*n,r=2*n;const l=1e7;return i>=l&&(a+=Math.floor(i/l),i%=l),a>=l&&(r+=Math.floor(a/l),a%=l),r.toString()+pe(a)+pe(i)}function te(t,e){return{lo:0|t,hi:0|e}}function Re(t,e){return e=~e,t?t=1+~t:e+=1,te(t,e)}const pe=t=>{const e=String(t);return"0000000".slice(e.length)+e};function ut(){let t=this.buf[this.pos++],e=127&t;if(!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<7,!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<14,!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<21,!(128&t))return this.assertBounds(),e;t=this.buf[this.pos++],e|=(15&t)<<28;for(let s=5;128&t&&s<10;s++)t=this.buf[this.pos++];if(128&t)throw new Error("invalid varint");return this.assertBounds(),e>>>0}var be={};const S=dt();function dt(){const t=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof t.getBigInt64=="function"&&typeof t.getBigUint64=="function"&&typeof t.setBigInt64=="function"&&typeof t.setBigUint64=="function"&&(typeof process!="object"||typeof be!="object"||be.BUF_BIGINT_DISABLE!=="1")){const e=BigInt("-9223372036854775808"),s=BigInt("9223372036854775807"),n=BigInt("0"),i=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(a){const r=typeof a=="bigint"?a:BigInt(a);if(r>s||r<e)throw new Error(`invalid int64: ${a}`);return r},uParse(a){const r=typeof a=="bigint"?a:BigInt(a);if(r>i||r<n)throw new Error(`invalid uint64: ${a}`);return r},enc(a){return t.setBigInt64(0,this.parse(a),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},uEnc(a){return t.setBigInt64(0,this.uParse(a),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},dec:(a,r)=>(t.setInt32(0,a,!0),t.setInt32(4,r,!0),t.getBigInt64(0,!0)),uDec:(a,r)=>(t.setInt32(0,a,!0),t.setInt32(4,r,!0),t.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:e=>(typeof e!="string"&&(e=e.toString()),_e(e),e),uParse:e=>(typeof e!="string"&&(e=e.toString()),Se(e),e),enc:e=>(typeof e!="string"&&(e=e.toString()),_e(e),me(e)),uEnc:e=>(typeof e!="string"&&(e=e.toString()),Se(e),me(e)),dec:(e,s)=>function(n,i){let a=te(n,i);const r=2147483648&a.hi;r&&(a=Re(a.lo,a.hi));const l=ye(a.lo,a.hi);return r?"-"+l:l}(e,s),uDec:(e,s)=>ye(e,s)}}function _e(t){if(!/^-?[0-9]+$/.test(t))throw new Error("invalid int64: "+t)}function Se(t){if(!/^[0-9]+$/.test(t))throw new Error("invalid uint64: "+t)}function O(t,e){switch(t){case c.STRING:return"";case c.BOOL:return!1;case c.DOUBLE:case c.FLOAT:return 0;case c.INT64:case c.UINT64:case c.SFIXED64:case c.FIXED64:case c.SINT64:return e?"0":S.zero;case c.BYTES:return new Uint8Array(0);default:return 0}}const k=Symbol.for("reflect unsafe local");function Ae(t,e){const s=t[e.localName].case;return s===void 0?s:e.fields.find(n=>n.localName===s)}function ht(t,e){const s=e.localName;if(e.oneof)return t[e.oneof.localName].case===s;if(e.presence!=2)return t[s]!==void 0&&Object.prototype.hasOwnProperty.call(t,s);switch(e.fieldKind){case"list":return t[s].length>0;case"map":return Object.keys(t[s]).length>0;case"scalar":return!function(n,i){switch(n){case c.BOOL:return i===!1;case c.STRING:return i==="";case c.BYTES:return i instanceof Uint8Array&&!i.byteLength;default:return i==0}}(e.scalar,t[s]);case"enum":return t[s]!==e.enum.values[0].number}throw new Error("message field with implicit presence")}function Fe(t,e){if(e.oneof){const s=t[e.oneof.localName];return s.case===e.localName?s.value:void 0}return t[e.localName]}function De(t,e,s){e.oneof?t[e.oneof.localName]={case:e.localName,value:s}:t[e.localName]=s}function E(t){return t!==null&&typeof t=="object"&&!Array.isArray(t)}function J(t,e){var s,n,i,a;if(E(t)&&k in t&&"add"in t&&"field"in t&&typeof t.field=="function"){if(e!==void 0){const r=e,l=t.field();return r.listKind==l.listKind&&r.scalar===l.scalar&&((s=r.message)===null||s===void 0?void 0:s.typeName)===((n=l.message)===null||n===void 0?void 0:n.typeName)&&((i=r.enum)===null||i===void 0?void 0:i.typeName)===((a=l.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function Q(t,e){var s,n,i,a;if(E(t)&&k in t&&"has"in t&&"field"in t&&typeof t.field=="function"){if(e!==void 0){const r=e,l=t.field();return r.mapKey===l.mapKey&&r.mapKind==l.mapKind&&r.scalar===l.scalar&&((s=r.message)===null||s===void 0?void 0:s.typeName)===((n=l.message)===null||n===void 0?void 0:n.typeName)&&((i=r.enum)===null||i===void 0?void 0:i.typeName)===((a=l.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function se(t,e){return E(t)&&k in t&&"desc"in t&&E(t.desc)&&t.desc.kind==="message"&&(e===void 0||t.desc.typeName==e.typeName)}function Pe(t){const e=t.fields[0];return xe(t.typeName)&&e!==void 0&&e.fieldKind=="scalar"&&e.name=="value"&&e.number==1}function xe(t){return t.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(t.substring(16))}const gt=999,ft=998,L=2;function ne(t,e){if(ee(e,t))return e;const s=function(n){let i;if(function(a){switch(a.file.edition){case gt:return!1;case ft:return!0;default:return a.fields.some(r=>r.presence!=L&&r.fieldKind!="message"&&!r.oneof)}}(n)){const a=we.get(n);let r,l;if(a)({prototype:r,members:l}=a);else{r={},l=new Set;for(const u of n.members)u.kind!="oneof"&&(u.fieldKind!="scalar"&&u.fieldKind!="enum"||u.presence!=L&&(l.add(u),r[u.localName]=G(u)));we.set(n,{prototype:r,members:l})}i=Object.create(r),i.$typeName=n.typeName;for(const u of n.members)if(!l.has(u)){if(u.kind=="field"&&(u.fieldKind=="message"||(u.fieldKind=="scalar"||u.fieldKind=="enum")&&u.presence!=L))continue;i[u.localName]=G(u)}}else{i={$typeName:n.typeName};for(const a of n.members)a.kind!="oneof"&&a.presence!=L||(i[a.localName]=G(a))}return i}(t);return e!==void 0&&function(n,i,a){for(const r of n.members){let l,u=a[r.localName];if(u!=null){if(r.kind=="oneof"){const g=Ae(a,r);if(!g)continue;l=g,u=Fe(a,g)}else l=r;switch(l.fieldKind){case"message":u=ae(l,u);break;case"scalar":u=Ue(l,u);break;case"list":u=yt(l,u);break;case"map":u=mt(l,u)}De(i,l,u)}}}(t,s,e),s}function Ue(t,e){return t.scalar==c.BYTES?ie(e):e}function mt(t,e){if(E(e)){if(t.scalar==c.BYTES)return ve(e,ie);if(t.mapKind=="message")return ve(e,s=>ae(t,s))}return e}function yt(t,e){if(Array.isArray(e)){if(t.scalar==c.BYTES)return e.map(ie);if(t.listKind=="message")return e.map(s=>ae(t,s))}return e}function ae(t,e){if(t.fieldKind=="message"&&!t.oneof&&Pe(t.message))return Ue(t.message.fields[0],e);if(E(e)){if(t.message.typeName=="google.protobuf.Struct"&&t.parent.typeName!=="google.protobuf.Value")return e;if(!ee(e,t.message))return ne(t.message,e)}return e}function ie(t){return Array.isArray(t)?new Uint8Array(t):t}function ve(t,e){const s={};for(const n of Object.entries(t))s[n[0]]=e(n[1]);return s}const pt=Symbol(),we=new WeakMap;function G(t){if(t.kind=="oneof")return{case:void 0};if(t.fieldKind=="list")return[];if(t.fieldKind=="map")return{};if(t.fieldKind=="message")return pt;const e=t.getDefaultValue();return e!==void 0?t.fieldKind=="scalar"&&t.longAsString?e.toString():e:t.fieldKind=="scalar"?O(t.scalar,t.longAsString):t.enum.values[0].number}class A extends Error{constructor(e,s,n="FieldValueInvalidError"){super(s),this.name=n,this.field=()=>e}}const X=Symbol.for("@bufbuild/protobuf/text-encoding");function Oe(){if(globalThis[X]==null){const t=new globalThis.TextEncoder,e=new globalThis.TextDecoder;globalThis[X]={encodeUtf8:s=>t.encode(s),decodeUtf8:s=>e.decode(s),checkUtf8(s){try{return encodeURIComponent(s),!0}catch{return!1}}}}return globalThis[X]}var T;(function(t){t[t.Varint=0]="Varint",t[t.Bit64=1]="Bit64",t[t.LengthDelimited=2]="LengthDelimited",t[t.StartGroup=3]="StartGroup",t[t.EndGroup=4]="EndGroup",t[t.Bit32=5]="Bit32"})(T||(T={}));const bt=34028234663852886e22,_t=-34028234663852886e22,St=4294967295,vt=2147483647,wt=-2147483648;class Tt{constructor(e,s=Oe().decodeUtf8){this.decodeUtf8=s,this.varint64=ct,this.uint32=ut,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength)}tag(){let e=this.uint32(),s=e>>>3,n=7&e;if(s<=0||n<0||n>5)throw new Error("illegal tag: field no "+s+" wire type "+n);return[s,n]}skip(e,s){let n=this.pos;switch(e){case T.Varint:for(;128&this.buf[this.pos++];);break;case T.Bit64:this.pos+=4;case T.Bit32:this.pos+=4;break;case T.LengthDelimited:let i=this.uint32();this.pos+=i;break;case T.StartGroup:for(;;){const[a,r]=this.tag();if(r===T.EndGroup){if(s!==void 0&&a!==s)throw new Error("invalid end group tag");break}this.skip(r,a)}break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(n,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let e=this.uint32();return e>>>1^-(1&e)}int64(){return S.dec(...this.varint64())}uint64(){return S.uDec(...this.varint64())}sint64(){let[e,s]=this.varint64(),n=-(1&e);return e=(e>>>1|(1&s)<<31)^n,s=s>>>1^n,S.dec(e,s)}bool(){let[e,s]=this.varint64();return e!==0||s!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return S.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return S.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),s=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(s,s+e)}string(){return this.decodeUtf8(this.bytes())}}function Te(t,e,s){const n=re(t,s);if(n!==!0)return new A(t,`list item #${e+1}: ${K(t,s,n)}`)}function re(t,e){return t.scalar!==void 0?Be(e,t.scalar):t.enum!==void 0?t.enum.open?Number.isInteger(e):t.enum.values.some(s=>s.number===e):se(e,t.message)}function Be(t,e){switch(e){case c.DOUBLE:return typeof t=="number";case c.FLOAT:return typeof t=="number"&&(!(!Number.isNaN(t)&&Number.isFinite(t))||!(t>bt||t<_t)||`${t.toFixed()} out of range`);case c.INT32:case c.SFIXED32:case c.SINT32:return!(typeof t!="number"||!Number.isInteger(t))&&(!(t>vt||t<wt)||`${t.toFixed()} out of range`);case c.FIXED32:case c.UINT32:return!(typeof t!="number"||!Number.isInteger(t))&&(!(t>St||t<0)||`${t.toFixed()} out of range`);case c.BOOL:return typeof t=="boolean";case c.STRING:return typeof t=="string"&&(Oe().checkUtf8(t)||"invalid UTF8");case c.BYTES:return t instanceof Uint8Array;case c.INT64:case c.SFIXED64:case c.SINT64:if(typeof t=="bigint"||typeof t=="number"||typeof t=="string"&&t.length>0)try{return S.parse(t),!0}catch{return`${t} out of range`}return!1;case c.FIXED64:case c.UINT64:if(typeof t=="bigint"||typeof t=="number"||typeof t=="string"&&t.length>0)try{return S.uParse(t),!0}catch{return`${t} out of range`}return!1}}function K(t,e,s){return s=typeof s=="string"?`: ${s}`:`, got ${V(e)}`,t.scalar!==void 0?`expected ${function(n){switch(n){case c.STRING:return"string";case c.BOOL:return"boolean";case c.INT64:case c.SINT64:case c.SFIXED64:return"bigint (int64)";case c.UINT64:case c.FIXED64:return"bigint (uint64)";case c.BYTES:return"Uint8Array";case c.DOUBLE:return"number (float64)";case c.FLOAT:return"number (float32)";case c.FIXED32:case c.UINT32:return"number (uint32)";case c.INT32:case c.SFIXED32:case c.SINT32:return"number (int32)"}}(t.scalar)}`+s:t.enum!==void 0?`expected ${t.enum.toString()}`+s:`expected ${qe(t.message)}`+s}function V(t){switch(typeof t){case"object":return t===null?"null":t instanceof Uint8Array?`Uint8Array(${t.length})`:Array.isArray(t)?`Array(${t.length})`:J(t)?Le(t.field()):Q(t)?$e(t.field()):se(t)?qe(t.desc):ee(t)?`message ${t.$typeName}`:"object";case"string":return t.length>30?"string":`"${t.split('"').join('\\"')}"`;case"boolean":case"number":return String(t);case"bigint":return String(t)+"n";default:return typeof t}}function qe(t){return`ReflectMessage (${t.typeName})`}function Le(t){switch(t.listKind){case"message":return`ReflectList (${t.message.toString()})`;case"enum":return`ReflectList (${t.enum.toString()})`;case"scalar":return`ReflectList (${c[t.scalar]})`}}function $e(t){switch(t.mapKind){case"message":return`ReflectMap (${c[t.mapKey]}, ${t.message.toString()})`;case"enum":return`ReflectMap (${c[t.mapKey]}, ${t.enum.toString()})`;case"scalar":return`ReflectMap (${c[t.mapKey]}, ${c[t.scalar]})`}}function oe(t,e,s=!0){return new je(t,e,s)}class je{get sortedFields(){var e;return(e=this._sortedFields)!==null&&e!==void 0?e:this._sortedFields=this.desc.fields.concat().sort((s,n)=>s.number-n.number)}constructor(e,s,n=!0){this.lists=new Map,this.maps=new Map,this.check=n,this.desc=e,this.message=this[k]=s??ne(e),this.fields=e.fields,this.oneofs=e.oneofs,this.members=e.members}findNumber(e){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(s=>[s.number,s]))),this._fieldsByNumber.get(e)}oneofCase(e){return x(this.message,e),Ae(this.message,e)}isSet(e){return x(this.message,e),ht(this.message,e)}clear(e){x(this.message,e),function(s,n){const i=n.localName;if(n.oneof){const a=n.oneof.localName;s[a].case===i&&(s[a]={case:void 0})}else if(n.presence!=2)delete s[i];else switch(n.fieldKind){case"map":s[i]={};break;case"list":s[i]=[];break;case"enum":s[i]=n.enum.values[0].number;break;case"scalar":s[i]=O(n.scalar,n.longAsString)}}(this.message,e)}get(e){x(this.message,e);const s=Fe(this.message,e);switch(e.fieldKind){case"list":let n=this.lists.get(e);return n&&n[k]===s||this.lists.set(e,n=new Mt(e,s,this.check)),n;case"map":let i=this.maps.get(e);return i&&i[k]===s||this.maps.set(e,i=new It(e,s,this.check)),i;case"message":return ce(e,s,this.check);case"scalar":return s===void 0?O(e.scalar,!1):ue(e,s);case"enum":return s??e.enum.values[0].number}}set(e,s){if(x(this.message,e),this.check){const i=function(a,r){const l=a.fieldKind=="list"?J(r,a):a.fieldKind=="map"?Q(r,a):re(a,r);if(l===!0)return;let u;switch(a.fieldKind){case"list":u=`expected ${Le(a)}, got ${V(r)}`;break;case"map":u=`expected ${$e(a)}, got ${V(r)}`;break;default:u=K(a,r,l)}return new A(a,u)}(e,s);if(i)throw i}let n;n=e.fieldKind=="message"?le(e,s):Q(s)||J(s)?s[k]:de(e,s),De(this.message,e,n)}getUnknown(){return this.message.$unknown}setUnknown(e){this.message.$unknown=e}}function x(t,e){if(e.parent.typeName!==t.$typeName)throw new A(e,`cannot use ${e.toString()} with message ${t.$typeName}`,"ForeignFieldError")}class Mt{field(){return this._field}get size(){return this._arr.length}constructor(e,s,n){this._field=e,this._arr=this[k]=s,this.check=n}get(e){const s=this._arr[e];return s===void 0?void 0:W(this._field,s,this.check)}set(e,s){if(e<0||e>=this._arr.length)throw new A(this._field,`list item #${e+1}: out of range`);if(this.check){const n=Te(this._field,e,s);if(n)throw n}this._arr[e]=Me(this._field,s)}add(e){if(this.check){const s=Te(this._field,this._arr.length,e);if(s)throw s}this._arr.push(Me(this._field,e))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const e of this._arr)yield W(this._field,e,this.check)}*entries(){for(let e=0;e<this._arr.length;e++)yield[e,W(this._field,this._arr[e],this.check)]}}class It{constructor(e,s,n=!0){this.obj=this[k]=s??{},this.check=n,this._field=e}field(){return this._field}set(e,s){if(this.check){const n=function(i,a,r){const l=Be(a,i.mapKey);if(l!==!0)return new A(i,`invalid map key: ${K({scalar:i.mapKey},a,l)}`);const u=re(i,r);return u!==!0?new A(i,`map entry ${V(a)}: ${K(i,r,u)}`):void 0}(this._field,e,s);if(n)throw n}return this.obj[$(e)]=function(n,i){return n.mapKind=="message"?le(n,i):de(n,i)}(this._field,s),this}delete(e){const s=$(e),n=Object.prototype.hasOwnProperty.call(this.obj,s);return n&&delete this.obj[s],n}clear(){for(const e of Object.keys(this.obj))delete this.obj[e]}get(e){let s=this.obj[$(e)];return s!==void 0&&(s=z(this._field,s,this.check)),s}has(e){return Object.prototype.hasOwnProperty.call(this.obj,$(e))}*keys(){for(const e of Object.keys(this.obj))yield Ie(e,this._field.mapKey)}*entries(){for(const e of Object.entries(this.obj))yield[Ie(e[0],this._field.mapKey),z(this._field,e[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const e of Object.values(this.obj))yield z(this._field,e,this.check)}forEach(e,s){for(const n of this.entries())e.call(s,n[1],n[0],this)}}function le(t,e){return se(e)?xe(e.message.$typeName)&&!t.oneof&&t.fieldKind=="message"?e.message.value:e.desc.typeName=="google.protobuf.Struct"&&t.parent.typeName!="google.protobuf.Value"?Ve(e.message):e.message:e}function ce(t,e,s){return e!==void 0&&(Pe(t.message)&&!t.oneof&&t.fieldKind=="message"?e={$typeName:t.message.typeName,value:ue(t.message.fields[0],e)}:t.message.typeName=="google.protobuf.Struct"&&t.parent.typeName!="google.protobuf.Value"&&E(e)&&(e=Ke(e))),new je(t.message,e,s)}function Me(t,e){return t.listKind=="message"?le(t,e):de(t,e)}function W(t,e,s){return t.listKind=="message"?ce(t,e,s):ue(t,e)}function z(t,e,s){return t.mapKind=="message"?ce(t,e,s):e}function $(t){return typeof t=="string"||typeof t=="number"?t:String(t)}function Ie(t,e){switch(e){case c.STRING:return t;case c.INT32:case c.FIXED32:case c.UINT32:case c.SFIXED32:case c.SINT32:{const s=Number.parseInt(t);if(Number.isFinite(s))return s;break}case c.BOOL:switch(t){case"true":return!0;case"false":return!1}break;case c.UINT64:case c.FIXED64:try{return S.uParse(t)}catch{}break;default:try{return S.parse(t)}catch{}}return t}function ue(t,e){switch(t.scalar){case c.INT64:case c.SFIXED64:case c.SINT64:"longAsString"in t&&t.longAsString&&typeof e=="string"&&(e=S.parse(e));break;case c.FIXED64:case c.UINT64:"longAsString"in t&&t.longAsString&&typeof e=="string"&&(e=S.uParse(e))}return e}function de(t,e){switch(t.scalar){case c.INT64:case c.SFIXED64:case c.SINT64:"longAsString"in t&&t.longAsString?e=String(e):typeof e!="string"&&typeof e!="number"||(e=S.parse(e));break;case c.FIXED64:case c.UINT64:"longAsString"in t&&t.longAsString?e=String(e):typeof e!="string"&&typeof e!="number"||(e=S.uParse(e))}return e}function Ke(t){const e={$typeName:"google.protobuf.Struct",fields:{}};if(E(t))for(const[s,n]of Object.entries(t))e.fields[s]=Ge(n);return e}function Ve(t){const e={};for(const[s,n]of Object.entries(t.fields))e[s]=He(n);return e}function He(t){switch(t.kind.case){case"structValue":return Ve(t.kind.value);case"listValue":return t.kind.value.values.map(He);case"nullValue":case void 0:return null;default:return t.kind.value}}function Ge(t){const e={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:0};else if(Array.isArray(t)){const s={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(t))for(const n of t)s.values.push(Ge(n));e.kind={case:"listValue",value:s}}else e.kind={case:"structValue",value:Ke(t)}}return e}const ke={readUnknownFields:!0};function kt(t,e,s){const n=oe(t,void 0,!1);return Xe(n,new Tt(e),function(i){return i?Object.assign(Object.assign({},ke),i):ke}(s),!1,e.byteLength),n.message}function Xe(t,e,s,n,i){var a;const r=n?e.len:e.pos+i;let l,u;const g=(a=t.getUnknown())!==null&&a!==void 0?a:[];for(;e.pos<r&&([l,u]=e.tag(),!n||u!=T.EndGroup);){const f=t.findNumber(l);if(f)Nt(t,e,f,u,s);else{const h=e.skip(u,l);s.readUnknownFields&&g.push({no:l,wireType:u,data:h})}}if(n&&(u!=T.EndGroup||l!==i))throw new Error("invalid end group tag");g.length>0&&t.setUnknown(g)}function Nt(t,e,s,n,i){switch(s.fieldKind){case"scalar":t.set(s,R(e,s.scalar));break;case"enum":t.set(s,R(e,c.INT32));break;case"message":t.set(s,Y(e,i,s,t.get(s)));break;case"list":(function(a,r,l,u){var g;const f=l.field();if(f.listKind==="message")return void l.add(Y(a,u,f));const h=(g=f.scalar)!==null&&g!==void 0?g:c.INT32;if(!(r==T.LengthDelimited&&h!=c.STRING&&h!=c.BYTES))return void l.add(R(a,h));const p=a.uint32()+a.pos;for(;a.pos<p;)l.add(R(a,h))})(e,n,t.get(s),i);break;case"map":(function(a,r,l){const u=r.field();let g,f;const h=a.pos+a.uint32();for(;a.pos<h;){const[m]=a.tag();switch(m){case 1:g=R(a,u.mapKey);break;case 2:switch(u.mapKind){case"scalar":f=R(a,u.scalar);break;case"enum":f=a.int32();break;case"message":f=Y(a,l,u)}}}if(g===void 0&&(g=O(u.mapKey,!1)),f===void 0)switch(u.mapKind){case"scalar":f=O(u.scalar,!1);break;case"enum":f=u.enum.values[0].number;break;case"message":f=oe(u.message,void 0,!1)}r.set(g,f)})(e,t.get(s),i)}}function Y(t,e,s,n){const i=s.delimitedEncoding,a=n??oe(s.message,void 0,!1);return Xe(a,t,e,i,i?s.number:t.uint32()),a}function R(t,e){switch(e){case c.STRING:return t.string();case c.BOOL:return t.bool();case c.DOUBLE:return t.double();case c.FLOAT:return t.float();case c.INT32:return t.int32();case c.INT64:return t.int64();case c.UINT64:return t.uint64();case c.FIXED64:return t.fixed64();case c.BYTES:return t.bytes();case c.FIXED32:return t.fixed32();case c.SFIXED32:return t.sfixed32();case c.SFIXED64:return t.sfixed64();case c.SINT64:return t.sint64();case c.UINT32:return t.uint32();case c.SINT32:return t.sint32()}}function Ne(t){const e=C[t];return typeof e!="string"?t.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,s=>"_"+s.toLowerCase())}class N extends Error{constructor(e,s=C.Unknown,n,i,a){super(function(r,l){return r.length?`[${Ne(l)}] ${r}`:`[${Ne(l)}]`}(e,s)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=s,this.metadata=new Headers(n??{}),this.details=i??[],this.cause=a}static from(e,s=C.Unknown){return e instanceof N?e:e instanceof Error?e.name=="AbortError"?new N(e.message,C.Canceled):new N(e.message,s,void 0,void 0,e):new N(String(e),s,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===N.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const s=e.kind==="message"?{getMessage:i=>i===e.typeName?e:void 0}:e,n=[];for(const i of this.details){if("desc"in i){s.getMessage(i.desc.typeName)&&n.push(ne(i.desc,i.value));continue}const a=s.getMessage(i.type);if(a)try{n.push(kt(a,i.value))}catch{}}return n}}var Ct=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,s=t[Symbol.asyncIterator];return s?s.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(i){e[i]=t[i]&&function(a){return new Promise(function(r,l){(function(u,g,f,h){Promise.resolve(h).then(function(m){u({value:m,done:f})},g)})(r,l,(a=t[i](a)).done,a.value)})}}},B=function(t){return this instanceof B?(this.v=t,this):new B(t)},Et=function(t,e,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,i=s.apply(t,e||[]),a=[];return n=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",function(h){return function(m){return Promise.resolve(m).then(h,g)}}),n[Symbol.asyncIterator]=function(){return this},n;function r(h,m){i[h]&&(n[h]=function(p){return new Promise(function(b,I){a.push([h,p,b,I])>1||l(h,p)})},m&&(n[h]=m(n[h])))}function l(h,m){try{(p=i[h](m)).value instanceof B?Promise.resolve(p.value.v).then(u,g):f(a[0][2],p)}catch(b){f(a[0][3],b)}var p}function u(h){l("next",h)}function g(h){l("throw",h)}function f(h,m){h(m),a.shift(),a.length&&l(a[0][0],a[0][1])}},Rt=function(t){var e,s;return e={},n("next"),n("throw",function(i){throw i}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(i,a){e[i]=t[i]?function(r){return(s=!s)?{value:B(t[i](r)),done:!1}:a?a(r):r}:a}},We=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,s=t[Symbol.asyncIterator];return s?s.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(i){e[i]=t[i]&&function(a){return new Promise(function(r,l){(function(u,g,f,h){Promise.resolve(h).then(function(m){u({value:m,done:f})},g)})(r,l,(a=t[i](a)).done,a.value)})}}},F=function(t){return this instanceof F?(this.v=t,this):new F(t)},At=function(t){var e,s;return e={},n("next"),n("throw",function(i){throw i}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(i,a){e[i]=t[i]?function(r){return(s=!s)?{value:F(t[i](r)),done:!1}:a?a(r):r}:a}},Ft=function(t,e,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,i=s.apply(t,e||[]),a=[];return n=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",function(h){return function(m){return Promise.resolve(m).then(h,g)}}),n[Symbol.asyncIterator]=function(){return this},n;function r(h,m){i[h]&&(n[h]=function(p){return new Promise(function(b,I){a.push([h,p,b,I])>1||l(h,p)})},m&&(n[h]=m(n[h])))}function l(h,m){try{(p=i[h](m)).value instanceof F?Promise.resolve(p.value.v).then(u,g):f(a[0][2],p)}catch(b){f(a[0][3],b)}var p}function u(h){l("next",h)}function g(h){l("throw",h)}function f(h,m){h(m),a.shift(),a.length&&l(a[0][0],a[0][1])}};function Dt(t,e){return function(s,n){const i={};for(const a of s.methods){const r=n(a);r!=null&&(i[a.localName]=r)}return i}(t,s=>{switch(s.methodKind){case"unary":return function(n,i){return async function(a,r){var l,u;const g=await n.unary(i,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,a,r==null?void 0:r.contextValues);return(l=r==null?void 0:r.onHeader)===null||l===void 0||l.call(r,g.header),(u=r==null?void 0:r.onTrailer)===null||u===void 0||u.call(r,g.trailer),g.message}}(e,s);case"server_streaming":return function(n,i){return function(a,r){return Ce(n.stream(i,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,function(l){return Et(this,arguments,function*(){yield B(yield*Rt(Ct(l)))})}([a]),r==null?void 0:r.contextValues),r)}}(e,s);case"client_streaming":return function(n,i){return async function(a,r){var l,u,g,f,h,m;const p=await n.stream(i,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,a,r==null?void 0:r.contextValues);let b;(h=r==null?void 0:r.onHeader)===null||h===void 0||h.call(r,p.header);let I=0;try{for(var y,D=!0,H=We(p.message);!(l=(y=await H.next()).done);D=!0)f=y.value,D=!1,b=f,I++}catch(ze){u={error:ze}}finally{try{D||l||!(g=H.return)||await g.call(H)}finally{if(u)throw u.error}}if(!b)throw new N("protocol error: missing response message",C.Unimplemented);if(I>1)throw new N("protocol error: received extra messages for client streaming method",C.Unimplemented);return(m=r==null?void 0:r.onTrailer)===null||m===void 0||m.call(r,p.trailer),b}}(e,s);case"bidi_streaming":return function(n,i){return function(a,r){return Ce(n.stream(i,r==null?void 0:r.signal,r==null?void 0:r.timeoutMs,r==null?void 0:r.headers,a,r==null?void 0:r.contextValues),r)}}(e,s);default:return null}})}function Ce(t,e){const s=function(){return Ft(this,arguments,function*(){var n,i;const a=yield F(t);(n=e==null?void 0:e.onHeader)===null||n===void 0||n.call(e,a.header),yield F(yield*At(We(a.message))),(i=e==null?void 0:e.onTrailer)===null||i===void 0||i.call(e,a.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>s.next()})}}const ts="augment-welcome";var _=(t=>(t.draft="draft",t.sent="sent",t.failed="failed",t.success="success",t.cancelled="cancelled",t))(_||{}),M=(t=>(t.seen="seen",t.unseen="unseen",t))(M||{}),Pt=(t=>(t.signInWelcome="sign-in-welcome",t.generateCommitMessage="generate-commit-message",t.summaryResponse="summary-response",t.summaryTitle="summary-title",t.educateFeatures="educate-features",t.autofixMessage="autofix-message",t.autofixSteeringMessage="autofix-steering-message",t.autofixStage="autofix-stage",t.agentOnboarding="agent-onboarding",t.agenticTurnDelimiter="agentic-turn-delimiter",t.agenticRevertDelimiter="agentic-revert-delimiter",t.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",t.exchange="exchange",t))(Pt||{});function xt(t){return!!t&&(t.chatItemType===void 0||t.chatItemType==="agent-onboarding")}function ss(t){return xt(t)&&t.status==="success"}function ns(t){return t.chatItemType==="agentic-checkpoint-delimiter"}async function*Ut(t,e=1e3){for(;t>0;)yield t,await new Promise(s=>setTimeout(s,Math.min(e,t))),t-=e}class Ot{constructor(e,s,n,i=5,a=4e3,r){o(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=s,this.startStreamFn=n,this.maxRetries=i,this.baseDelay=a,this.flags=r}cancel(){this._isCancelled=!0}async*getStream(){let e=0,s=!1;try{for(;!this._isCancelled;){const n=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let i,a=!1,r="";for await(const u of n){if(u.status===_.failed){if(u.isRetriable!==!0||s)return yield u;a=!0,r=u.display_error_message||"Service is currently unavailable",i=u.request_id;break}s=!0,yield u}if(!a)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return void(yield{request_id:i??this.requestId,seen_state:M.unseen,status:_.failed,display_error_message:r,isRetriable:!1});const l=this.baseDelay*2**(e-1);for await(const u of Ut(l))yield{request_id:this.requestId,status:_.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(u/1e3)} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0};yield{request_id:this.requestId,status:_.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(n){yield{request_id:this.requestId,seen_state:M.unseen,status:_.failed,display_error_message:n instanceof Error?n.message:String(n)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:M.unseen,status:_.cancelled}}}function w(t,e){return e in t&&t[e]!==void 0}function Bt(t){return w(t,"file")}function qt(t){return w(t,"recentFile")}function Lt(t){return w(t,"folder")}function $t(t){return w(t,"sourceFolder")}function as(t){return w(t,"selection")}function jt(t){return w(t,"externalSource")}function is(t){return w(t,"allDefaultContext")}function rs(t){return w(t,"clearContext")}function os(t){return w(t,"userGuidelines")}function Kt(t){return w(t,"personality")}function Vt(t){return w(t,"rule")}const ls={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},cs={clearContext:!0,label:"Clear Context",id:"clearContext"},us={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},ds={agentMemories:{},label:"Agent Memories",id:"agentMemories"},Ee=[{personality:{type:q.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:q.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:q.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:q.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function hs(t){return w(t,"group")}function gs(t){const e=new Map;return t.forEach(s=>{Bt(s)?e.set("file",[...e.get("file")??[],s]):qt(s)?e.set("recentFile",[...e.get("recentFile")??[],s]):Lt(s)?e.set("folder",[...e.get("folder")??[],s]):jt(s)?e.set("externalSource",[...e.get("externalSource")??[],s]):$t(s)?e.set("sourceFolder",[...e.get("sourceFolder")??[],s]):Kt(s)?e.set("personality",[...e.get("personality")??[],s]):Vt(s)&&e.set("rule",[...e.get("rule")??[],s])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(s=>s.group.items.length>0)}function Ht(t){const e={label:lt(t.pathName).split("/").filter(s=>s.trim()!=="").pop()||"",name:t.pathName,id:Je({rootPath:t.repoRoot,relPath:t.pathName})};if(t.fullRange){const s=`:L${t.fullRange.startLineNumber}-${t.fullRange.endLineNumber}`;e.label+=s,e.name+=s,e.id+=s}else if(t.range){const s=`:L${t.range.start}-${t.range.stop}`;e.label+=s,e.name+=s,e.id+=s}return e}function Gt(t){const e=t.path.split("/"),s=e[e.length-1],n=s.endsWith(".md")?s.slice(0,-3):s,i=`${Qe}/${et}/${t.path}`;return{label:n,name:i,id:i}}class Xt{constructor(e){o(this,"getHydratedTask",async e=>{const s={type:P.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.task});o(this,"createTask",async(e,s,n)=>{const i={type:P.createTaskRequest,data:{name:e,description:s,parentTaskUuid:n}};return(await this._asyncMsgSender.sendToSidecar(i,3e4)).data.uuid});o(this,"updateTask",async(e,s,n)=>{const i={type:P.updateTaskRequest,data:{uuid:e,updates:s,updatedBy:n}};await this._asyncMsgSender.sendToSidecar(i,3e4)});o(this,"setCurrentRootTaskUuid",e=>{const s={type:P.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(s)});o(this,"updateHydratedTask",async(e,s)=>{const n={type:P.updateHydratedTaskRequest,data:{task:e,updatedBy:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});this._asyncMsgSender=e}}class fs{constructor(e,s,n){o(this,"_taskClient");o(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:d.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const s=await async function(n){return(await Dt(it,new at({sendMessage:a=>{n.postMessage(a)},onReceiveMessage:a=>{const r=l=>{a(l.data)};return window.addEventListener("message",r),()=>{window.removeEventListener("message",r)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",s)}catch(s){console.error("Hello world error:",s)}return e.data});o(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:d.reportWebviewClientMetric,data:{webviewName:tt.chat,client_metric:e,value:1}})});o(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:v.reportAgentSessionEvent,data:e})});o(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:v.reportAgentRequestEvent,data:e})});o(this,"getSuggestions",async(e,s=!1)=>{const n={rootPath:"",relPath:e},i=this.findFiles(n,6),a=this.findRecentlyOpenedFiles(n,6),r=this.findFolders(n,3),l=this.findExternalSources(e,s),u=this.findRules(e,6),[g,f,h,m,p]=await Promise.all([U(i,[]),U(a,[]),U(r,[]),U(l,[]),U(u,[])]),b=(y,D)=>({...Ht(y),[D]:y}),I=[...g.map(y=>b(y,"file")),...h.map(y=>b(y,"folder")),...f.map(y=>b(y,"recentFile")),...m.map(y=>({label:y.name,name:y.name,id:y.id,externalSource:y})),...p.map(y=>({...Gt(y),rule:y}))];if(this._flags.enablePersonalities){const y=this.getPersonalities(e);y.length>0&&I.push(...y)}return I});o(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return Ee;const s=e.toLowerCase();return Ee.filter(n=>{const i=n.personality.description.toLowerCase(),a=n.label.toLowerCase();return i.includes(s)||a.includes(s)})});o(this,"sendAction",e=>{this._host.postMessage({type:d.mainPanelPerformAction,data:e})});o(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:d.showAugmentPanel})});o(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:d.openConfirmationModal,data:e},1e9)).data.ok);o(this,"clearMetadataFor",e=>{this._host.postMessage({type:d.chatClearMetadata,data:e})});o(this,"resolvePath",async(e,s=void 0)=>{const n=await this._asyncMsgSender.send({type:d.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:s}},5e3);if(n.data)return n.data});o(this,"resolveSymbols",async(e,s)=>(await this._asyncMsgSender.send({type:d.findSymbolRequest,data:{query:e,searchScope:s}},3e4)).data);o(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:d.getDiagnosticsRequest},1e3)).data);o(this,"findFiles",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findFileRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findFolders",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findFolderRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findRecentlyOpenedFiles",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findRecentlyOpenedFilesRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findExternalSources",async(e,s=!1)=>this._flags.enableExternalSourcesInChat?s?[]:(await this._asyncMsgSender.send({type:d.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);o(this,"findRules",async(e,s=12)=>this._flags.enableRules?(await this._asyncMsgSender.send({type:d.getRulesListRequest,data:{query:e,maxResults:s}},5e3)).data:[]);o(this,"openFile",e=>{this._host.postMessage({type:d.openFile,data:e})});o(this,"saveFile",e=>this._host.postMessage({type:d.saveFile,data:e}));o(this,"loadFile",e=>this._host.postMessage({type:d.loadFile,data:e}));o(this,"openMemoriesFile",()=>{this._host.postMessage({type:d.openMemoriesFile})});o(this,"createFile",(e,s)=>{this._host.postMessage({type:d.chatCreateFile,data:{code:e,relPath:s}})});o(this,"openScratchFile",async(e,s="shellscript")=>{await this._asyncMsgSender.send({type:d.openScratchFileRequest,data:{content:e,language:s}},1e4)});o(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:d.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});o(this,"smartPaste",e=>{this._host.postMessage({type:d.chatSmartPaste,data:e})});o(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));o(this,"updateHydratedTask",async(e,s)=>this._taskClient.updateHydratedTask(e,s));o(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});o(this,"createTask",async(e,s,n)=>this._taskClient.createTask(e,s,n));o(this,"updateTask",async(e,s,n)=>this._taskClient.updateTask(e,s,n));o(this,"saveChat",async(e,s,n)=>this._asyncMsgSender.send({type:d.saveChat,data:{conversationId:e,chatHistory:s,title:n}}));o(this,"launchAutofixPanel",async(e,s,n)=>this._asyncMsgSender.send({type:d.chatLaunchAutofixPanel,data:{conversationId:e,iterationId:s,stage:n}}));o(this,"updateUserGuidelines",e=>{this._host.postMessage({type:d.updateUserGuidelines,data:e})});o(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:d.updateWorkspaceGuidelines,data:e})});o(this,"updateRuleFile",(e,s)=>{this._host.postMessage({type:d.updateRuleFile,data:{rulePath:e,content:s}})});o(this,"openSettingsPage",e=>{this._host.postMessage({type:d.openSettingsPage,data:e})});o(this,"_activeRetryStreams",new Map);o(this,"cancelChatStream",async e=>{var s;(s=this._activeRetryStreams.get(e))==null||s.cancel(),await this._asyncMsgSender.send({type:d.chatUserCancel,data:{requestId:e}},1e4)});o(this,"sendUserRating",async(e,s,n,i="")=>{const a={requestId:e,rating:n,note:i,mode:s},r={type:d.chatRating,data:a};return(await this._asyncMsgSender.send(r,3e4)).data});o(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:d.usedChat})});o(this,"createProject",e=>{this._host.postMessage({type:d.mainPanelCreateProject,data:{name:e}})});o(this,"openProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"open-folder"})});o(this,"closeProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"close-folder"})});o(this,"cloneRepository",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"clone-repository"})});o(this,"grantSyncPermission",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"grant-sync-permission"})});o(this,"callTool",async(e,s,n,i,a,r)=>{const l={type:d.callTool,data:{chatRequestId:e,toolUseId:s,name:n,input:i,chatHistory:a,conversationId:r}};return(await this._asyncMsgSender.send(l,0)).data});o(this,"cancelToolRun",async(e,s)=>{const n={type:d.cancelToolRun,data:{requestId:e,toolUseId:s}};await this._asyncMsgSender.send(n,0)});o(this,"checkSafe",async(e,s)=>{const n={type:d.toolCheckSafe,data:{name:e,input:s}};return(await this._asyncMsgSender.send(n,0)).data.isSafe});o(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:he.closeAllToolProcesses},0)});o(this,"getToolIdentifier",async e=>{const s={type:he.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(s,0)).data});o(this,"executeCommand",async(e,s,n)=>{try{const i=await this._asyncMsgSender.send({type:d.chatAutofixExecuteCommandRequest,data:{iterationId:e,command:s,args:n}},6e5);return{output:i.data.output,returnCode:i.data.returnCode}}catch(i){throw console.error("[ExtensionClient] Execute command failed:",i),i}});o(this,"sendAutofixStateUpdate",async e=>{await this._asyncMsgSender.send({type:d.chatAutofixStateUpdate,data:e})});o(this,"autofixPlan",async(e,s)=>(await this._asyncMsgSender.send({type:d.chatAutofixPlanRequest,data:{command:e,steeringHistory:s}},6e4)).data.plan);o(this,"setChatMode",e=>{this._asyncMsgSender.send({type:d.chatModeChanged,data:{mode:e}})});o(this,"getAgentEditList",async(e,s)=>{const n={type:v.getEditListRequest,data:{fromTimestamp:e,toTimestamp:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});o(this,"hasChangesSince",async e=>{const s={type:v.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.edits.filter(n=>{var i,a;return((i=n.changesSummary)==null?void 0:i.totalAddedLines)||((a=n.changesSummary)==null?void 0:a.totalRemovedLines)}).length>0});o(this,"getToolCallCheckpoint",async e=>{const s={type:d.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(s,3e4)).data.checkpointNumber});o(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:v.setCurrentConversation,data:{conversationId:e}})});o(this,"migrateConversationId",async(e,s)=>{await this._asyncMsgSender.sendToSidecar({type:v.migrateConversationId,data:{oldConversationId:e,newConversationId:s}},3e4)});o(this,"showAgentReview",(e,s,n,i=!0)=>{this._asyncMsgSender.sendToSidecar({type:v.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:s,toTimestamp:n,retainFocus:i}})});o(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:v.chatAgentEditAcceptAll}),!0));o(this,"revertToTimestamp",async(e,s)=>(await this._asyncMsgSender.sendToSidecar({type:v.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:s}}),!0));o(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:d.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);o(this,"getAgentEditChangesByRequestId",async e=>{const s={type:v.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"getAgentEditContentsByRequestId",async e=>{const s={type:v.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:d.triggerInitialOrientation})});o(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:d.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});o(this,"getRemoteAgentStatus",async()=>{try{return(await this._asyncMsgSender.send({type:d.getRemoteAgentStatus},5e3)).data}catch(e){return console.error("Error getting remote agent status:",e),{isRemoteAgentWindow:!1}}});o(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:d.toggleCollapseUnchangedRegions})});o(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:d.checkAgentAutoModeApproval},5e3)).data);o(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:d.setAgentAutoModeApproved,data:e},5e3)});o(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:v.checkHasEverUsedAgent},5e3)).data);o(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:v.setHasEverUsedAgent,data:e},5e3)});o(this,"getChatRequestIdeState",async()=>{const e={type:d.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});o(this,"reportError",e=>{this._host.postMessage({type:d.reportError,data:e})});this._host=e,this._asyncMsgSender=s,this._flags=n,this._taskClient=new Xt(s)}async*generateCommitMessage(){const e={type:d.generateCommitMessage},s=this._asyncMsgSender.stream(e,3e4,6e4);yield*Z(s)}async*sendInstructionMessage(e,s){const n={instruction:e.request_message??"",selectedCodeDetails:s,requestId:e.request_id},i={type:d.chatInstructionMessage,data:n},a=this._asyncMsgSender.stream(i,3e4,6e4);yield*async function*(r){let l;try{for await(const u of r)l=u.data.requestId,yield{request_id:l,response_text:u.data.text,seen_state:M.unseen,status:_.sent};yield{request_id:l,seen_state:M.unseen,status:_.success}}catch{yield{request_id:l,seen_state:M.unseen,status:_.failed}}}(a)}async openGuidelines(e){this._host.postMessage({type:d.openGuidelines,data:e})}async*getExistingChatStream(e,s){if(!e.request_id)return;const n=s==null?void 0:s.flags.enablePreferenceCollection,i=n?1e9:6e4,a=n?1e9:3e5,r={type:d.chatGetStreamRequest,data:{requestId:e.request_id}},l=this._asyncMsgSender.stream(r,i,a);yield*Z(l,this.reportError)}async*startChatStream(e,s){const n=s==null?void 0:s.flags.enablePreferenceCollection,i=n?1e9:6e4,a=n?1e9:3e5,r={type:d.chatUserMessage,data:e},l=this._asyncMsgSender.stream(r,i,a);yield*Z(l,this.reportError)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:d.checkToolExists,toolName:e},0)).exists}async saveImage(e,s){const n=st(await ge(e)),i=s??`${await nt(await fe(n))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:d.chatSaveImageRequest,data:{filename:i,data:n}},1e4)).data}async loadImage(e){const s=await this._asyncMsgSender.send({type:d.chatLoadImageRequest,data:e},1e4),n=s.data?await fe(s.data):void 0;if(!n)return;let i="application/octet-stream";const a=e.split(".").at(-1);a==="png"?i="image/png":a!=="jpg"&&a!=="jpeg"||(i="image/jpeg");const r=new File([n],e,{type:i});return await ge(r)}async deleteImage(e){await this._asyncMsgSender.send({type:d.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,s,n){const i=new Ot(e,s,(a,r)=>this.startChatStream(a,r),(n==null?void 0:n.maxRetries)??5,4e3,n==null?void 0:n.flags);this._activeRetryStreams.set(e,i);try{yield*i.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:d.getSubscriptionInfo},5e3)}}async function*Z(t,e=()=>{}){let s;try{for await(const n of t){if(s=n.data.requestId,n.data.error)return yield{request_id:s,seen_state:M.unseen,status:_.failed,display_error_message:n.data.error.displayErrorMessage,isRetriable:n.data.error.isRetriable};yield{request_id:s,response_text:n.data.text,workspace_file_chunks:n.data.workspaceFileChunks,structured_output_nodes:Wt(n.data.nodes),seen_state:M.unseen,status:_.sent}}yield{request_id:s,seen_state:M.unseen,status:_.success}}catch(n){e({originalRequestId:s||"",sanitizedMessage:n instanceof Error?n.message:String(n),stackTrace:n instanceof Error&&n.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:s,seen_state:M.unseen,status:_.failed}}}async function U(t,e){try{return await t}catch(s){return console.warn(`Error while resolving promise: ${s}`),e}}function Wt(t){if(!t)return t;let e=!1;return t.filter(s=>s.type!==ot.TOOL_USE||!e&&(e=!0,!0))}const ms=15,ys=1e3,zt=25e4;class ps{constructor(e){o(this,"_enableEditableHistory",!1);o(this,"_enablePreferenceCollection",!1);o(this,"_enableRetrievalDataCollection",!1);o(this,"_enableDebugFeatures",!1);o(this,"_enableRichTextHistory",!1);o(this,"_modelDisplayNameToId",{});o(this,"_fullFeatured",!0);o(this,"_enableExternalSourcesInChat",!1);o(this,"_smallSyncThreshold",15);o(this,"_bigSyncThreshold",1e3);o(this,"_enableSmartPaste",!1);o(this,"_enableDirectApply",!1);o(this,"_summaryTitles",!1);o(this,"_suggestedEditsAvailable",!1);o(this,"_enableShareService",!1);o(this,"_maxTrackableFileCount",zt);o(this,"_enableDesignSystemRichTextEditor",!1);o(this,"_enableSources",!1);o(this,"_enableChatMermaidDiagrams",!1);o(this,"_smartPastePrecomputeMode",rt.visibleHover);o(this,"_useNewThreadsMenu",!1);o(this,"_enableChatMermaidDiagramsMinVersion",!1);o(this,"_enablePromptEnhancer",!1);o(this,"_idleNewSessionNotificationTimeoutMs");o(this,"_idleNewSessionMessageTimeoutMs");o(this,"_enableChatMultimodal",!1);o(this,"_enableAgentMode",!1);o(this,"_enableRichCheckpointInfo",!1);o(this,"_agentMemoriesFilePathName");o(this,"_userTier","unknown");o(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});o(this,"_truncateChatHistory",!1);o(this,"_enableBackgroundAgents",!1);o(this,"_enableVirtualizedMessageList",!1);o(this,"_customPersonalityPrompts",{});o(this,"_enablePersonalities",!1);o(this,"_enableRules",!1);o(this,"_memoryClassificationOnFirstToken",!1);o(this,"_isRemoteAgentWindow",!1);o(this,"_remoteAgentId");o(this,"_enableGenerateCommitMessage",!1);o(this,"_doUseNewDraftFunctionality",!1);o(this,"_modelRegistry",{});o(this,"_enableModelRegistry",!1);o(this,"_enableTaskList",!1);o(this,"_clientAnnouncement","");o(this,"_subscribers",new Set);o(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));o(this,"update",e=>{this._enableEditableHistory=e.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=e.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=e.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=e.enableDebugFeatures??this._enableDebugFeatures,this._enableRichTextHistory=e.enableRichTextHistory??this._enableRichTextHistory,this._modelDisplayNameToId={...e.modelDisplayNameToId},this._fullFeatured=e.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=e.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=e.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=e.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=e.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=e.enableDirectApply??this._enableDirectApply,this._summaryTitles=e.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=e.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=e.enableShareService??this._enableShareService,this._maxTrackableFileCount=e.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=e.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=e.enableSources??this._enableSources,this._enableChatMermaidDiagrams=e.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=e.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=e.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=e.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=e.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=e.idleNewSessionMessageTimeoutMs??(e.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=e.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=e.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=e.enableAgentMode??this._enableAgentMode,this._enableRichCheckpointInfo=e.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=e.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._userTier=e.userTier??this._userTier,this._eloModelConfiguration=e.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=e.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=e.enableBackgroundAgents??this._enableBackgroundAgents,this._enableVirtualizedMessageList=e.enableVirtualizedMessageList??this._enableVirtualizedMessageList,this._customPersonalityPrompts=e.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=e.enablePersonalities??this._enablePersonalities,this._enableRules=e.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=e.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._isRemoteAgentWindow=e.isRemoteAgentWindow??this._isRemoteAgentWindow,this._remoteAgentId=e.remoteAgentId??this._remoteAgentId,this._doUseNewDraftFunctionality=e.doUseNewDraftFunctionality??this._doUseNewDraftFunctionality,this._enableGenerateCommitMessage=e.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=e.modelRegistry??this._modelRegistry,this._enableModelRegistry=e.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=e.enableTaskList??this._enableTaskList,this._clientAnnouncement=e.clientAnnouncement??this._clientAnnouncement,this._subscribers.forEach(s=>s(this))});o(this,"isModelIdValid",e=>e!==void 0&&(Object.values(this._modelDisplayNameToId).includes(e)||Object.values(this._modelRegistry).includes(e??"")));o(this,"getModelDisplayName",e=>{if(e!==void 0)return Object.keys(this._modelDisplayNameToId).find(s=>this._modelDisplayNameToId[s]===e)});e&&this.update(e)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((e,s)=>{const n=e.toLowerCase(),i=s.toLowerCase();return n==="default"&&i!=="default"?-1:i==="default"&&n!=="default"?1:e.localeCompare(s)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get doUseNewDraftFunctionality(){return this._doUseNewDraftFunctionality}get enableVirtualizedMessageList(){return this._enableVirtualizedMessageList||this._enableDebugFeatures}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get isRemoteAgentWindow(){return this._isRemoteAgentWindow}get remoteAgentId(){return this._remoteAgentId}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}}export{ds as A,ps as C,ms as D,fs as E,M as S,ls as U,ss as a,_ as b,ns as c,Pt as d,Kt as e,ys as f,zt as g,Bt as h,xt as i,qt as j,as as k,Lt as l,$t as m,jt as n,os as o,Vt as p,Ht as q,Gt as r,ts as s,gs as t,hs as u,is as v,cs as w,us as x,rs as y};
