import{S as O,i as Y,s as Z,P,Q as M,D as X,c as z,aa as T,a2 as g,e as F,f as y,a4 as S,a5 as ee,V as Q,W as R,X as j,u as x,q as te,t as B,r as ae,ab as se,h as G,a6 as de,a0 as oe,y as H,z as J,ac as ne,ad as q,B as K,w as I}from"./SpinnerAugment-BJ4-L7QR.js";import{I as le}from"./IconButtonAugment-Certjadv.js";import{f as U}from"./index-CGbmuyBX.js";import{r as re}from"./resize-observer-DdAtcrRr.js";import{E as ie}from"./ellipsis-BWy9xWah.js";const ce=a=>({}),V=a=>({}),ue=a=>({}),A=a=>({});function C(a){let t,e,d,u;return e=new le({props:{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$slots:{default:[me]},$$scope:{ctx:a}}}),e.$on("click",a[12]),{c(){t=M("div"),H(e.$$.fragment),z(t,"class","c-drawer__hidden-indicator svelte-18f0m3o")},m(l,$){F(l,t,$),J(e,t,null),u=!0},p(l,$){const p={};16777216&$&&(p.$$scope={dirty:$,ctx:l}),e.$set(p)},i(l){u||(x(e.$$.fragment,l),l&&ne(()=>{u&&(d||(d=q(t,U,{y:0,x:0,duration:200},!0)),d.run(1))}),u=!0)},o(l){B(e.$$.fragment,l),l&&(d||(d=q(t,U,{y:0,x:0,duration:200},!1)),d.run(0)),u=!1},d(l){l&&G(t),K(e),l&&d&&d.end()}}}function me(a){let t,e;return t=new ie({}),{c(){H(t.$$.fragment)},m(d,u){J(t,d,u),e=!0},i(d){e||(x(t.$$.fragment,d),e=!0)},o(d){B(t.$$.fragment,d),e=!1},d(d){K(t,d)}}}function he(a){let t,e,d,u,l,$,p,k,v,f,i,m,L;const _=a[20].left,h=P(_,a,a[24],A),W=a[20].right,r=P(W,a,a[24],V);let n=a[0]&&a[3]&&C(a);return{c(){t=M("div"),e=M("div"),d=M("div"),h&&h.c(),u=X(),l=M("div"),$=X(),p=M("div"),r&&r.c(),k=X(),n&&n.c(),z(d,"class","c-drawer__left-content svelte-18f0m3o"),d.inert=a[7],T(d,"width","var(--augment-drawer-width)"),T(d,"min-width","var(--augment-drawer-width)"),T(d,"max-width","var(--augment-drawer-width)"),z(e,"class","c-drawer__left svelte-18f0m3o"),T(e,"--augment-drawer-width",a[8]+"px"),z(l,"aria-hidden","true"),z(l,"class","c-drawer__handle svelte-18f0m3o"),g(l,"is-locked",a[4]),z(p,"class","c-drawer__right svelte-18f0m3o"),z(t,"class",v="c-drawer "+a[2]+" svelte-18f0m3o"),g(t,"is-dragging",a[7]),g(t,"is-hidden",!a[8]),g(t,"is-column",a[4])},m(s,c){F(s,t,c),y(t,e),y(e,d),h&&h.m(d,null),a[21](e),y(t,u),y(t,l),y(t,$),y(t,p),r&&r.m(p,null),y(t,k),n&&n.m(t,null),a[22](t),i=!0,m||(L=[S(window,"mousemove",a[10]),S(window,"mouseup",a[11]),S(l,"mousedown",a[9]),S(l,"dblclick",a[12]),ee(f=re.call(null,t,{onResize:a[23]}))],m=!0)},p(s,[c]){h&&h.p&&(!i||16777216&c)&&Q(h,_,s,s[24],i?j(_,s[24],c,ue):R(s[24]),A),(!i||128&c)&&(d.inert=s[7]),(!i||256&c)&&T(e,"--augment-drawer-width",s[8]+"px"),(!i||16&c)&&g(l,"is-locked",s[4]),r&&r.p&&(!i||16777216&c)&&Q(r,W,s,s[24],i?j(W,s[24],c,ce):R(s[24]),V),s[0]&&s[3]?n?(n.p(s,c),9&c&&x(n,1)):(n=C(s),n.c(),x(n,1),n.m(t,null)):n&&(te(),B(n,1,1,()=>{n=null}),ae()),(!i||4&c&&v!==(v="c-drawer "+s[2]+" svelte-18f0m3o"))&&z(t,"class",v),f&&se(f.update)&&2&c&&f.update.call(null,{onResize:s[23]}),(!i||132&c)&&g(t,"is-dragging",s[7]),(!i||260&c)&&g(t,"is-hidden",!s[8]),(!i||20&c)&&g(t,"is-column",s[4])},i(s){i||(x(h,s),x(r,s),x(n),i=!0)},o(s){B(h,s),B(r,s),B(n),i=!1},d(s){s&&G(t),h&&h.d(s),a[21](null),r&&r.d(s),n&&n.d(),a[22](null),m=!1,de(L)}}}function fe(a,t,e){let d,u,l,$,{$$slots:p={},$$scope:k}=t,{initialWidth:v=300}=t,{expandedMinWidth:f=50}=t,{minimizedWidth:i=0}=t,{minimized:m=!1}=t,{class:L=""}=t,{showButton:_=!0}=t,{deadzone:h=0}=t,{columnLayoutThreshold:W=600}=t,{layoutMode:r}=t,n=!1,s=v,c=v,w=!1;function b(){if(u){if(r!==void 0)return e(4,w=r==="column"),void(w&&e(7,n=!1));e(4,w=u.clientWidth<W),w&&e(7,n=!1)}}return oe(b),a.$$set=o=>{"initialWidth"in o&&e(14,v=o.initialWidth),"expandedMinWidth"in o&&e(15,f=o.expandedMinWidth),"minimizedWidth"in o&&e(16,i=o.minimizedWidth),"minimized"in o&&e(0,m=o.minimized),"class"in o&&e(2,L=o.class),"showButton"in o&&e(3,_=o.showButton),"deadzone"in o&&e(17,h=o.deadzone),"columnLayoutThreshold"in o&&e(18,W=o.columnLayoutThreshold),"layoutMode"in o&&e(1,r=o.layoutMode),"$$scope"in o&&e(24,k=o.$$scope)},a.$$.update=()=>{3&a.$$.dirty&&(m?(e(1,r="row"),e(4,w=!1)):r!=="row"||m||(e(1,r=void 0),b())),18&a.$$.dirty&&r!==void 0&&(e(4,w=r==="column"),w&&e(7,n=!1)),589825&a.$$.dirty&&e(8,c=m?i:s)},[m,r,L,_,w,d,u,n,c,function(o){w||(e(7,n=!0),l=o.clientX,$=d.offsetWidth,o.preventDefault())},function(o){if(!n||!d||w)return;const N=o.clientX-l,E=u.clientWidth-200,D=$+N;D<f?D<f-h?e(0,m=!0):(e(19,s=f),e(0,m=!1)):D>E?(e(19,s=E),e(0,m=!1)):(e(19,s=D),e(0,m=!1))},function(){e(7,n=!1),e(19,s=Math.max(s,f))},function(){e(0,m=!m)},b,v,f,i,h,W,s,p,function(o){I[o?"unshift":"push"](()=>{d=o,e(5,d)})},function(o){I[o?"unshift":"push"](()=>{u=o,e(6,u)})},()=>r===void 0&&b(),k]}class ze extends O{constructor(t){super(),Y(this,t,fe,he,Z,{initialWidth:14,expandedMinWidth:15,minimizedWidth:16,minimized:0,class:2,showButton:3,deadzone:17,columnLayoutThreshold:18,layoutMode:1})}}export{ze as D};
