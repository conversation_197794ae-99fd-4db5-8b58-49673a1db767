!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).vega={})}(this,(function(t){"use strict";function e(t,e,n){return t.fields=e||[],t.fname=n,t}function n(t){return null==t?null:t.fname}function r(t){return null==t?null:t.fields}function i(t){return 1===t.length?o(t[0]):a(t)}const o=t=>function(e){return e[t]},a=t=>{const e=t.length;return function(n){for(let r=0;r<e;++r)n=n[t[r]];return n}};function u(t){throw Error(t)}function s(t){const e=[],n=t.length;let r,i,o,a=null,s=0,l="";function c(){e.push(l+t.substring(r,i)),l="",r=i+1}for(t+="",r=i=0;i<n;++i)if(o=t[i],"\\"===o)l+=t.substring(r,i++),r=i;else if(o===a)c(),a=null,s=-1;else{if(a)continue;r===s&&'"'===o||r===s&&"'"===o?(r=i+1,a=o):"."!==o||s?"["===o?(i>r&&c(),s=r=i+1):"]"===o&&(s||u("Access path missing open bracket: "+t),s>0&&c(),s=0,r=i+1):i>r?c():r=i+1}return s&&u("Access path missing closing bracket: "+t),a&&u("Access path missing closing quote: "+t),i>r&&(i++,c()),e}function l(t,n,r){const o=s(t);return t=1===o.length?o[0]:t,e((r&&r.get||i)(o),[t],n||t)}const c=l("id"),f=e((t=>t),[],"identity"),h=e((()=>0),[],"zero"),d=e((()=>1),[],"one"),p=e((()=>!0),[],"true"),g=e((()=>!1),[],"false");function m(t,e,n){const r=[e].concat([].slice.call(n));console[t].apply(console,r)}const y=0,v=1,_=2,x=3,b=4;function w(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:m,r=t||y;return{level(t){return arguments.length?(r=+t,this):r},error(){return r>=v&&n(e||"error","ERROR",arguments),this},warn(){return r>=_&&n(e||"warn","WARN",arguments),this},info(){return r>=x&&n(e||"log","INFO",arguments),this},debug(){return r>=b&&n(e||"log","DEBUG",arguments),this}}}var k=Array.isArray;function A(t){return t===Object(t)}const M=t=>"__proto__"!==t;function E(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.reduce(((t,e)=>{for(const n in e)if("signals"===n)t.signals=C(t.signals,e.signals);else{const r="legend"===n?{layout:1}:"style"===n||null;D(t,n,e[n],r)}return t}),{})}function D(t,e,n,r){if(!M(e))return;let i,o;if(A(n)&&!k(n))for(i in o=A(t[e])?t[e]:t[e]={},n)r&&(!0===r||r[i])?D(o,i,n[i]):M(i)&&(o[i]=n[i]);else t[e]=n}function C(t,e){if(null==t)return e;const n={},r=[];function i(t){n[t.name]||(n[t.name]=1,r.push(t))}return e.forEach(i),t.forEach(i),r}function F(t){return t[t.length-1]}function S(t){return null==t||""===t?null:+t}const $=t=>e=>t*Math.exp(e),T=t=>e=>Math.log(t*e),B=t=>e=>Math.sign(e)*Math.log1p(Math.abs(e/t)),N=t=>e=>Math.sign(e)*Math.expm1(Math.abs(e))*t,z=t=>e=>e<0?-Math.pow(-e,t):Math.pow(e,t);function O(t,e,n,r){const i=n(t[0]),o=n(F(t)),a=(o-i)*e;return[r(i-a),r(o-a)]}function R(t,e){return O(t,e,S,f)}function U(t,e){var n=Math.sign(t[0]);return O(t,e,T(n),$(n))}function L(t,e,n){return O(t,e,z(n),z(1/n))}function q(t,e,n){return O(t,e,B(n),N(n))}function P(t,e,n,r,i){const o=r(t[0]),a=r(F(t)),u=null!=e?r(e):(o+a)/2;return[i(u+(o-u)*n),i(u+(a-u)*n)]}function j(t,e,n){return P(t,e,n,S,f)}function I(t,e,n){const r=Math.sign(t[0]);return P(t,e,n,T(r),$(r))}function W(t,e,n,r){return P(t,e,n,z(r),z(1/r))}function H(t,e,n,r){return P(t,e,n,B(r),N(r))}function Y(t){return 1+~~(new Date(t).getMonth()/3)}function G(t){return 1+~~(new Date(t).getUTCMonth()/3)}function V(t){return null!=t?k(t)?t:[t]:[]}function X(t,e,n){let r,i=t[0],o=t[1];return o<i&&(r=o,o=i,i=r),r=o-i,r>=n-e?[e,n]:[i=Math.min(Math.max(i,e),n-r),i+r]}function J(t){return"function"==typeof t}const Z="descending";function Q(t,n,i){i=i||{},n=V(n)||[];const o=[],a=[],u={},s=i.comparator||tt;return V(t).forEach(((t,e)=>{null!=t&&(o.push(n[e]===Z?-1:1),a.push(t=J(t)?t:l(t,null,i)),(r(t)||[]).forEach((t=>u[t]=1)))})),0===a.length?null:e(s(a,o),Object.keys(u))}const K=(t,e)=>(t<e||null==t)&&null!=e?-1:(t>e||null==e)&&null!=t?1:(e=e instanceof Date?+e:e,(t=t instanceof Date?+t:t)!==t&&e==e?-1:e!=e&&t==t?1:0),tt=(t,e)=>1===t.length?et(t[0],e[0]):nt(t,e,t.length),et=(t,e)=>function(n,r){return K(t(n),t(r))*e},nt=(t,e,n)=>(e.push(0),function(r,i){let o,a=0,u=-1;for(;0===a&&++u<n;)o=t[u],a=K(o(r),o(i));return a*e[u]});function rt(t){return J(t)?t:()=>t}function it(t,e){let n;return r=>{n&&clearTimeout(n),n=setTimeout((()=>(e(r),n=null)),t)}}function ot(t){for(let e,n,r=1,i=arguments.length;r<i;++r)for(n in e=arguments[r],e)t[n]=e[n];return t}function at(t,e){let n,r,i,o,a=0;if(t&&(n=t.length))if(null==e){for(r=t[a];a<n&&(null==r||r!=r);r=t[++a]);for(i=o=r;a<n;++a)r=t[a],null!=r&&(r<i&&(i=r),r>o&&(o=r))}else{for(r=e(t[a]);a<n&&(null==r||r!=r);r=e(t[++a]));for(i=o=r;a<n;++a)r=e(t[a]),null!=r&&(r<i&&(i=r),r>o&&(o=r))}return[i,o]}function ut(t,e){const n=t.length;let r,i,o,a,u,s=-1;if(null==e){for(;++s<n;)if(i=t[s],null!=i&&i>=i){r=o=i;break}if(s===n)return[-1,-1];for(a=u=s;++s<n;)i=t[s],null!=i&&(r>i&&(r=i,a=s),o<i&&(o=i,u=s))}else{for(;++s<n;)if(i=e(t[s],s,t),null!=i&&i>=i){r=o=i;break}if(s===n)return[-1,-1];for(a=u=s;++s<n;)i=e(t[s],s,t),null!=i&&(r>i&&(r=i,a=s),o<i&&(o=i,u=s))}return[a,u]}const st=Object.prototype.hasOwnProperty;function lt(t,e){return st.call(t,e)}const ct={};function ft(t){let e,n={};function r(t){return lt(n,t)&&n[t]!==ct}const i={size:0,empty:0,object:n,has:r,get:t=>r(t)?n[t]:void 0,set(t,e){return r(t)||(++i.size,n[t]===ct&&--i.empty),n[t]=e,this},delete(t){return r(t)&&(--i.size,++i.empty,n[t]=ct),this},clear(){i.size=i.empty=0,i.object=n={}},test(t){return arguments.length?(e=t,i):e},clean(){const t={};let r=0;for(const i in n){const o=n[i];o===ct||e&&e(o)||(t[i]=o,++r)}i.size=r,i.empty=0,i.object=n=t}};return t&&Object.keys(t).forEach((e=>{i.set(e,t[e])})),i}function ht(t,e,n,r,i,o){if(!n&&0!==n)return o;const a=+n;let u,s=t[0],l=F(t);l<s&&(u=s,s=l,l=u),u=Math.abs(e-s);const c=Math.abs(l-e);return u<c&&u<=a?r:c<=a?i:o}function dt(t,e,n){const r=t.prototype=Object.create(e.prototype);return Object.defineProperty(r,"constructor",{value:t,writable:!0,enumerable:!0,configurable:!0}),ot(r,n)}function pt(t,e,n,r){let i,o=e[0],a=e[e.length-1];return o>a&&(i=o,o=a,a=i),r=void 0===r||r,((n=void 0===n||n)?o<=t:o<t)&&(r?t<=a:t<a)}function gt(t){return"boolean"==typeof t}function mt(t){return"[object Date]"===Object.prototype.toString.call(t)}function yt(t){return t&&J(t[Symbol.iterator])}function vt(t){return"number"==typeof t}function _t(t){return"[object RegExp]"===Object.prototype.toString.call(t)}function xt(t){return"string"==typeof t}function bt(t,n,r){t&&(t=n?V(t).map((t=>t.replace(/\\(.)/g,"$1"))):V(t));const o=t&&t.length,a=r&&r.get||i,u=t=>a(n?[t]:s(t));let l;if(o)if(1===o){const e=u(t[0]);l=function(t){return""+e(t)}}else{const e=t.map(u);l=function(t){let n=""+e[0](t),r=0;for(;++r<o;)n+="|"+e[r](t);return n}}else l=function(){return""};return e(l,t,"key")}function wt(t,e){const n=t[0],r=F(t),i=+e;return i?1===i?r:n+i*(r-n):n}const kt=1e4;function At(t){let e,n,r;t=+t||kt;const i=()=>{e={},n={},r=0},o=(i,o)=>(++r>t&&(n=e,e={},r=1),e[i]=o);return i(),{clear:i,has:t=>lt(e,t)||lt(n,t),get:t=>lt(e,t)?e[t]:lt(n,t)?o(t,n[t]):void 0,set:(t,n)=>lt(e,t)?e[t]=n:o(t,n)}}function Mt(t,e,n,r){const i=e.length,o=n.length;if(!o)return e;if(!i)return n;const a=r||new e.constructor(i+o);let u=0,s=0,l=0;for(;u<i&&s<o;++l)a[l]=t(e[u],n[s])>0?n[s++]:e[u++];for(;u<i;++u,++l)a[l]=e[u];for(;s<o;++s,++l)a[l]=n[s];return a}function Et(t,e){let n="";for(;--e>=0;)n+=t;return n}function Dt(t,e,n,r){const i=n||" ",o=t+"",a=e-o.length;return a<=0?o:"left"===r?Et(i,a)+o:"center"===r?Et(i,~~(a/2))+o+Et(i,Math.ceil(a/2)):o+Et(i,a)}function Ct(t){return t&&F(t)-t[0]||0}function Ft(t){return k(t)?"["+t.map(Ft)+"]":A(t)||xt(t)?JSON.stringify(t).replace("\u2028","\\u2028").replace("\u2029","\\u2029"):t}function St(t){return null==t||""===t?null:!(!t||"false"===t||"0"===t)&&!!t}const $t=t=>vt(t)||mt(t)?t:Date.parse(t);function Tt(t,e){return e=e||$t,null==t||""===t?null:e(t)}function Bt(t){return null==t||""===t?null:t+""}function Nt(t){const e={},n=t.length;for(let r=0;r<n;++r)e[t[r]]=!0;return e}function zt(t,e,n,r){const i=null!=r?r:"…",o=t+"",a=o.length,u=Math.max(0,e-i.length);return a<=e?o:"left"===n?i+o.slice(a-u):"center"===n?o.slice(0,Math.ceil(u/2))+i+o.slice(a-~~(u/2)):o.slice(0,u)+i}function Ot(t,e,n){if(t)if(e){const r=t.length;for(let i=0;i<r;++i){const r=e(t[i]);r&&n(r,i,t)}}else t.forEach(n)}var Rt={},Ut={},Lt=34,qt=10,Pt=13;function jt(t){return new Function("d","return {"+t.map((function(t,e){return JSON.stringify(t)+": d["+e+'] || ""'})).join(",")+"}")}function It(t){var e=Object.create(null),n=[];return t.forEach((function(t){for(var r in t)r in e||n.push(e[r]=r)})),n}function Wt(t,e){var n=t+"",r=n.length;return r<e?new Array(e-r+1).join(0)+n:n}function Ht(t){var e,n=t.getUTCHours(),r=t.getUTCMinutes(),i=t.getUTCSeconds(),o=t.getUTCMilliseconds();return isNaN(t)?"Invalid Date":((e=t.getUTCFullYear())<0?"-"+Wt(-e,6):e>9999?"+"+Wt(e,6):Wt(e,4))+"-"+Wt(t.getUTCMonth()+1,2)+"-"+Wt(t.getUTCDate(),2)+(o?"T"+Wt(n,2)+":"+Wt(r,2)+":"+Wt(i,2)+"."+Wt(o,3)+"Z":i?"T"+Wt(n,2)+":"+Wt(r,2)+":"+Wt(i,2)+"Z":r||n?"T"+Wt(n,2)+":"+Wt(r,2)+"Z":"")}function Yt(t){var e=new RegExp('["'+t+"\n\r]"),n=t.charCodeAt(0);function r(t,e){var r,i=[],o=t.length,a=0,u=0,s=o<=0,l=!1;function c(){if(s)return Ut;if(l)return l=!1,Rt;var e,r,i=a;if(t.charCodeAt(i)===Lt){for(;a++<o&&t.charCodeAt(a)!==Lt||t.charCodeAt(++a)===Lt;);return(e=a)>=o?s=!0:(r=t.charCodeAt(a++))===qt?l=!0:r===Pt&&(l=!0,t.charCodeAt(a)===qt&&++a),t.slice(i+1,e-1).replace(/""/g,'"')}for(;a<o;){if((r=t.charCodeAt(e=a++))===qt)l=!0;else if(r===Pt)l=!0,t.charCodeAt(a)===qt&&++a;else if(r!==n)continue;return t.slice(i,e)}return s=!0,t.slice(i,o)}for(t.charCodeAt(o-1)===qt&&--o,t.charCodeAt(o-1)===Pt&&--o;(r=c())!==Ut;){for(var f=[];r!==Rt&&r!==Ut;)f.push(r),r=c();e&&null==(f=e(f,u++))||i.push(f)}return i}function i(e,n){return e.map((function(e){return n.map((function(t){return a(e[t])})).join(t)}))}function o(e){return e.map(a).join(t)}function a(t){return null==t?"":t instanceof Date?Ht(t):e.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:function(t,e){var n,i,o=r(t,(function(t,r){if(n)return n(t,r-1);i=t,n=e?function(t,e){var n=jt(t);return function(r,i){return e(n(r),i,t)}}(t,e):jt(t)}));return o.columns=i||[],o},parseRows:r,format:function(e,n){return null==n&&(n=It(e)),[n.map(a).join(t)].concat(i(e,n)).join("\n")},formatBody:function(t,e){return null==e&&(e=It(t)),i(t,e).join("\n")},formatRows:function(t){return t.map(o).join("\n")},formatRow:o,formatValue:a}}function Gt(t){return t}function Vt(t,e){return"string"==typeof e&&(e=t.objects[e]),"GeometryCollection"===e.type?{type:"FeatureCollection",features:e.geometries.map((function(e){return Xt(t,e)}))}:Xt(t,e)}function Xt(t,e){var n=e.id,r=e.bbox,i=null==e.properties?{}:e.properties,o=Jt(t,e);return null==n&&null==r?{type:"Feature",properties:i,geometry:o}:null==r?{type:"Feature",id:n,properties:i,geometry:o}:{type:"Feature",id:n,bbox:r,properties:i,geometry:o}}function Jt(t,e){var n=function(t){if(null==t)return Gt;var e,n,r=t.scale[0],i=t.scale[1],o=t.translate[0],a=t.translate[1];return function(t,u){u||(e=n=0);var s=2,l=t.length,c=new Array(l);for(c[0]=(e+=t[0])*r+o,c[1]=(n+=t[1])*i+a;s<l;)c[s]=t[s],++s;return c}}(t.transform),r=t.arcs;function i(t,e){e.length&&e.pop();for(var i=r[t<0?~t:t],o=0,a=i.length;o<a;++o)e.push(n(i[o],o));t<0&&function(t,e){for(var n,r=t.length,i=r-e;i<--r;)n=t[i],t[i++]=t[r],t[r]=n}(e,a)}function o(t){return n(t)}function a(t){for(var e=[],n=0,r=t.length;n<r;++n)i(t[n],e);return e.length<2&&e.push(e[0]),e}function u(t){for(var e=a(t);e.length<4;)e.push(e[0]);return e}function s(t){return t.map(u)}return function t(e){var n,r=e.type;switch(r){case"GeometryCollection":return{type:r,geometries:e.geometries.map(t)};case"Point":n=o(e.coordinates);break;case"MultiPoint":n=e.coordinates.map(o);break;case"LineString":n=a(e.arcs);break;case"MultiLineString":n=e.arcs.map(a);break;case"Polygon":n=s(e.arcs);break;case"MultiPolygon":n=e.arcs.map(s);break;default:return null}return{type:r,coordinates:n}}(e)}function Zt(t,e){var n={},r={},i={},o=[],a=-1;function u(t,e){for(var r in t){var i=t[r];delete e[i.start],delete i.start,delete i.end,i.forEach((function(t){n[t<0?~t:t]=1})),o.push(i)}}return e.forEach((function(n,r){var i,o=t.arcs[n<0?~n:n];o.length<3&&!o[1][0]&&!o[1][1]&&(i=e[++a],e[a]=n,e[r]=i)})),e.forEach((function(e){var n,o,a=function(e){var n,r=t.arcs[e<0?~e:e],i=r[0];t.transform?(n=[0,0],r.forEach((function(t){n[0]+=t[0],n[1]+=t[1]}))):n=r[r.length-1];return e<0?[n,i]:[i,n]}(e),u=a[0],s=a[1];if(n=i[u])if(delete i[n.end],n.push(e),n.end=s,o=r[s]){delete r[o.start];var l=o===n?n:n.concat(o);r[l.start=n.start]=i[l.end=o.end]=l}else r[n.start]=i[n.end]=n;else if(n=r[s])if(delete r[n.start],n.unshift(e),n.start=u,o=i[u]){delete i[o.end];var c=o===n?n:o.concat(n);r[c.start=o.start]=i[c.end=n.end]=c}else r[n.start]=i[n.end]=n;else r[(n=[e]).start=u]=i[n.end=s]=n})),u(i,r),u(r,i),e.forEach((function(t){n[t<0?~t:t]||o.push([t])})),o}function Qt(t){return Jt(t,Kt.apply(this,arguments))}function Kt(t,e,n){var r,i,o;if(arguments.length>1)r=function(t,e,n){var r,i=[],o=[];function a(t){var e=t<0?~t:t;(o[e]||(o[e]=[])).push({i:t,g:r})}function u(t){t.forEach(a)}function s(t){t.forEach(u)}function l(t){t.forEach(s)}function c(t){switch(r=t,t.type){case"GeometryCollection":t.geometries.forEach(c);break;case"LineString":u(t.arcs);break;case"MultiLineString":case"Polygon":s(t.arcs);break;case"MultiPolygon":l(t.arcs)}}return c(e),o.forEach(null==n?function(t){i.push(t[0].i)}:function(t){n(t[0].g,t[t.length-1].g)&&i.push(t[0].i)}),i}(0,e,n);else for(i=0,r=new Array(o=t.arcs.length);i<o;++i)r[i]=i;return{type:"MultiLineString",arcs:Zt(t,r)}}function te(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ee(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function ne(t){let e,n,r;function i(t,r){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(i<o){if(0!==e(r,r))return o;do{const e=i+o>>>1;n(t[e],r)<0?i=e+1:o=e}while(i<o)}return i}return 2!==t.length?(e=te,n=(e,n)=>te(t(e),n),r=(e,n)=>t(e)-n):(e=t===te||t===ee?t:re,n=t,r=t),{left:i,center:function(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const o=i(t,e,n,(arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length)-1);return o>n&&r(t[o-1],e)>-r(t[o],e)?o-1:o},right:function(t,r){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.length;if(i<o){if(0!==e(r,r))return o;do{const e=i+o>>>1;n(t[e],r)<=0?i=e+1:o=e}while(i<o)}return i}}}function re(){return 0}function ie(t){return null===t?NaN:+t}const oe=ne(te),ae=oe.right,ue=oe.left;ne(ie).center;var se=ae;class le{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const e=this._partials;let n=0;for(let r=0;r<this._n&&r<32;r++){const i=e[r],o=t+i,a=Math.abs(t)<Math.abs(i)?t-(o-i):i-(o-t);a&&(e[n++]=a),t=o}return e[n]=t,this._n=n+1,this}valueOf(){const t=this._partials;let e,n,r,i=this._n,o=0;if(i>0){for(o=t[--i];i>0&&(e=o,n=t[--i],o=e+n,r=n-(o-e),!r););i>0&&(r<0&&t[i-1]<0||r>0&&t[i-1]>0)&&(n=2*r,e=o+n,n==e-o&&(o=e))}return o}}class ce extends Map{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ge;if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[e,n]of t)this.set(e,n)}get(t){return super.get(he(this,t))}has(t){return super.has(he(this,t))}set(t,e){return super.set(de(this,t),e)}delete(t){return super.delete(pe(this,t))}}class fe extends Set{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ge;if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const e of t)this.add(e)}has(t){return super.has(he(this,t))}add(t){return super.add(de(this,t))}delete(t){return super.delete(pe(this,t))}}function he(t,e){let{_intern:n,_key:r}=t;const i=r(e);return n.has(i)?n.get(i):e}function de(t,e){let{_intern:n,_key:r}=t;const i=r(e);return n.has(i)?n.get(i):(n.set(i,e),e)}function pe(t,e){let{_intern:n,_key:r}=t;const i=r(e);return n.has(i)&&(e=n.get(i),n.delete(i)),e}function ge(t){return null!==t&&"object"==typeof t?t.valueOf():t}function me(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}const ye=Math.sqrt(50),ve=Math.sqrt(10),_e=Math.sqrt(2);function xe(t,e,n){const r=(e-t)/Math.max(0,n),i=Math.floor(Math.log10(r)),o=r/Math.pow(10,i),a=o>=ye?10:o>=ve?5:o>=_e?2:1;let u,s,l;return i<0?(l=Math.pow(10,-i)/a,u=Math.round(t*l),s=Math.round(e*l),u/l<t&&++u,s/l>e&&--s,l=-l):(l=Math.pow(10,i)*a,u=Math.round(t/l),s=Math.round(e/l),u*l<t&&++u,s*l>e&&--s),s<u&&.5<=n&&n<2?xe(t,e,2*n):[u,s,l]}function be(t,e,n){if(!((n=+n)>0))return[];if((t=+t)===(e=+e))return[t];const r=e<t,[i,o,a]=r?xe(e,t,n):xe(t,e,n);if(!(o>=i))return[];const u=o-i+1,s=new Array(u);if(r)if(a<0)for(let t=0;t<u;++t)s[t]=(o-t)/-a;else for(let t=0;t<u;++t)s[t]=(o-t)*a;else if(a<0)for(let t=0;t<u;++t)s[t]=(i+t)/-a;else for(let t=0;t<u;++t)s[t]=(i+t)*a;return s}function we(t,e,n){return xe(t=+t,e=+e,n=+n)[2]}function ke(t,e,n){n=+n;const r=(e=+e)<(t=+t),i=r?we(e,t,n):we(t,e,n);return(r?-1:1)*(i<0?1/-i:i)}function Ae(t,e){let n;if(void 0===e)for(const e of t)null!=e&&(n<e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let i of t)null!=(i=e(i,++r,t))&&(n<i||void 0===n&&i>=i)&&(n=i)}return n}function Me(t,e){let n;if(void 0===e)for(const e of t)null!=e&&(n>e||void 0===n&&e>=e)&&(n=e);else{let r=-1;for(let i of t)null!=(i=e(i,++r,t))&&(n>i||void 0===n&&i>=i)&&(n=i)}return n}function Ee(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1/0,i=arguments.length>4?arguments[4]:void 0;if(e=Math.floor(e),n=Math.floor(Math.max(0,n)),r=Math.floor(Math.min(t.length-1,r)),!(n<=e&&e<=r))return t;for(i=void 0===i?me:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:te;if(t===te)return me;if("function"!=typeof t)throw new TypeError("compare is not a function");return(e,n)=>{const r=t(e,n);return r||0===r?r:(0===t(n,n))-(0===t(e,e))}}(i);r>n;){if(r-n>600){const o=r-n+1,a=e-n+1,u=Math.log(o),s=.5*Math.exp(2*u/3),l=.5*Math.sqrt(u*s*(o-s)/o)*(a-o/2<0?-1:1);Ee(t,e,Math.max(n,Math.floor(e-a*s/o+l)),Math.min(r,Math.floor(e+(o-a)*s/o+l)),i)}const o=t[e];let a=n,u=r;for(De(t,n,e),i(t[r],o)>0&&De(t,n,r);a<u;){for(De(t,a,u),++a,--u;i(t[a],o)<0;)++a;for(;i(t[u],o)>0;)--u}0===i(t[n],o)?De(t,n,u):(++u,De(t,u,r)),u<=e&&(n=u+1),e<=u&&(r=u-1)}return t}function De(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function Ce(t,e,n){if(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let n=-1;for(let r of t)null!=(r=e(r,++n,t))&&(r=+r)>=r&&(yield r)}}(t,n)),(r=t.length)&&!isNaN(e=+e)){if(e<=0||r<2)return Me(t);if(e>=1)return Ae(t);var r,i=(r-1)*e,o=Math.floor(i),a=Ae(Ee(t,o).subarray(0,o+1));return a+(Me(t.subarray(o+1))-a)*(i-o)}}function Fe(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ie;if((r=t.length)&&!isNaN(e=+e)){if(e<=0||r<2)return+n(t[0],0,t);if(e>=1)return+n(t[r-1],r-1,t);var r,i=(r-1)*e,o=Math.floor(i),a=+n(t[o],o,t);return a+(+n(t[o+1],o+1,t)-a)*(i-o)}}function Se(t,e){return Ce(t,.5,e)}function $e(t){return Array.from(function*(t){for(const e of t)yield*e}(t))}function Te(t,e,n){t=+t,e=+e,n=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+n;for(var r=-1,i=0|Math.max(0,Math.ceil((e-t)/n)),o=new Array(i);++r<i;)o[r]=t+r*n;return o}function Be(t,e){let n=0;if(void 0===e)for(let e of t)(e=+e)&&(n+=e);else{let r=-1;for(let i of t)(i=+e(i,++r,t))&&(n+=i)}return n}function Ne(t){return t instanceof fe?t:new fe(t)}function ze(t,e){if((n=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var n,r=t.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+t.slice(n+1)]}function Oe(t){return(t=ze(Math.abs(t)))?t[1]:NaN}var Re,Ue=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Le(t){if(!(e=Ue.exec(t)))throw new Error("invalid format: "+t);var e;return new qe({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function qe(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function Pe(t,e){var n=ze(t,e);if(!n)return t+"";var r=n[0],i=n[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}Le.prototype=qe.prototype,qe.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var je={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>Pe(100*t,e),r:Pe,s:function(t,e){var n=ze(t,e);if(!n)return t+"";var r=n[0],i=n[1],o=i-(Re=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=r.length;return o===a?r:o>a?r+new Array(o-a+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+ze(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function Ie(t){return t}var We,He,Ye,Ge=Array.prototype.map,Ve=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Xe(t){var e,n,r=void 0===t.grouping||void 0===t.thousands?Ie:(e=Ge.call(t.grouping,Number),n=t.thousands+"",function(t,r){for(var i=t.length,o=[],a=0,u=e[0],s=0;i>0&&u>0&&(s+u+1>r&&(u=Math.max(1,r-s)),o.push(t.substring(i-=u,i+u)),!((s+=u+1)>r));)u=e[a=(a+1)%e.length];return o.reverse().join(n)}),i=void 0===t.currency?"":t.currency[0]+"",o=void 0===t.currency?"":t.currency[1]+"",a=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?Ie:function(t){return function(e){return e.replace(/[0-9]/g,(function(e){return t[+e]}))}}(Ge.call(t.numerals,String)),s=void 0===t.percent?"%":t.percent+"",l=void 0===t.minus?"−":t.minus+"",c=void 0===t.nan?"NaN":t.nan+"";function f(t){var e=(t=Le(t)).fill,n=t.align,f=t.sign,h=t.symbol,d=t.zero,p=t.width,g=t.comma,m=t.precision,y=t.trim,v=t.type;"n"===v?(g=!0,v="g"):je[v]||(void 0===m&&(m=12),y=!0,v="g"),(d||"0"===e&&"="===n)&&(d=!0,e="0",n="=");var _="$"===h?i:"#"===h&&/[boxX]/.test(v)?"0"+v.toLowerCase():"",x="$"===h?o:/[%p]/.test(v)?s:"",b=je[v],w=/[defgprs%]/.test(v);function k(t){var i,o,s,h=_,k=x;if("c"===v)k=b(t)+k,t="";else{var A=(t=+t)<0||1/t<0;if(t=isNaN(t)?c:b(Math.abs(t),m),y&&(t=function(t){t:for(var e,n=t.length,r=1,i=-1;r<n;++r)switch(t[r]){case".":i=e=r;break;case"0":0===i&&(i=r),e=r;break;default:if(!+t[r])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),A&&0==+t&&"+"!==f&&(A=!1),h=(A?"("===f?f:l:"-"===f||"("===f?"":f)+h,k=("s"===v?Ve[8+Re/3]:"")+k+(A&&"("===f?")":""),w)for(i=-1,o=t.length;++i<o;)if(48>(s=t.charCodeAt(i))||s>57){k=(46===s?a+t.slice(i+1):t.slice(i))+k,t=t.slice(0,i);break}}g&&!d&&(t=r(t,1/0));var M=h.length+t.length+k.length,E=M<p?new Array(p-M+1).join(e):"";switch(g&&d&&(t=r(E+t,E.length?p-k.length:1/0),E=""),n){case"<":t=h+t+k+E;break;case"=":t=h+E+t+k;break;case"^":t=E.slice(0,M=E.length>>1)+h+t+k+E.slice(M);break;default:t=E+h+t+k}return u(t)}return m=void 0===m?6:/[gprs]/.test(v)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),k.toString=function(){return t+""},k}return{format:f,formatPrefix:function(t,e){var n=f(((t=Le(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(Oe(e)/3))),i=Math.pow(10,-r),o=Ve[8+r/3];return function(t){return n(i*t)+o}}}}function Je(t){return Math.max(0,-Oe(Math.abs(t)))}function Ze(t,e){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Oe(e)/3)))-Oe(Math.abs(t)))}function Qe(t,e){return t=Math.abs(t),e=Math.abs(e)-t,Math.max(0,Oe(e)-Oe(t))+1}!function(t){We=Xe(t),He=We.format,Ye=We.formatPrefix}({thousands:",",grouping:[3],currency:["$",""]});const Ke=new Date,tn=new Date;function en(t,e,n,r){function i(e){return t(e=0===arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=n=>(t(n=new Date(n-1)),e(n,1),t(n),n),i.round=t=>{const e=i(t),n=i.ceil(t);return t-e<n-t?e:n},i.offset=(t,n)=>(e(t=new Date(+t),null==n?1:Math.floor(n)),t),i.range=(n,r,o)=>{const a=[];if(n=i.ceil(n),o=null==o?1:Math.floor(o),!(n<r&&o>0))return a;let u;do{a.push(u=new Date(+n)),e(n,o),t(n)}while(u<n&&n<r);return a},i.filter=n=>en((e=>{if(e>=e)for(;t(e),!n(e);)e.setTime(e-1)}),((t,r)=>{if(t>=t)if(r<0)for(;++r<=0;)for(;e(t,-1),!n(t););else for(;--r>=0;)for(;e(t,1),!n(t););})),n&&(i.count=(e,r)=>(Ke.setTime(+e),tn.setTime(+r),t(Ke),t(tn),Math.floor(n(Ke,tn))),i.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?i.filter(r?e=>r(e)%t==0:e=>i.count(0,e)%t==0):i:null)),i}const nn=en((()=>{}),((t,e)=>{t.setTime(+t+e)}),((t,e)=>e-t));nn.every=t=>(t=Math.floor(t),isFinite(t)&&t>0?t>1?en((e=>{e.setTime(Math.floor(e/t)*t)}),((e,n)=>{e.setTime(+e+n*t)}),((e,n)=>(n-e)/t)):nn:null),nn.range;const rn=1e3,on=60*rn,an=60*on,un=24*an,sn=7*un,ln=30*un,cn=365*un,fn=en((t=>{t.setTime(t-t.getMilliseconds())}),((t,e)=>{t.setTime(+t+e*rn)}),((t,e)=>(e-t)/rn),(t=>t.getUTCSeconds()));fn.range;const hn=en((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*rn)}),((t,e)=>{t.setTime(+t+e*on)}),((t,e)=>(e-t)/on),(t=>t.getMinutes()));hn.range;const dn=en((t=>{t.setUTCSeconds(0,0)}),((t,e)=>{t.setTime(+t+e*on)}),((t,e)=>(e-t)/on),(t=>t.getUTCMinutes()));dn.range;const pn=en((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*rn-t.getMinutes()*on)}),((t,e)=>{t.setTime(+t+e*an)}),((t,e)=>(e-t)/an),(t=>t.getHours()));pn.range;const gn=en((t=>{t.setUTCMinutes(0,0,0)}),((t,e)=>{t.setTime(+t+e*an)}),((t,e)=>(e-t)/an),(t=>t.getUTCHours()));gn.range;const mn=en((t=>t.setHours(0,0,0,0)),((t,e)=>t.setDate(t.getDate()+e)),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*on)/un),(t=>t.getDate()-1));mn.range;const yn=en((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/un),(t=>t.getUTCDate()-1));yn.range;const vn=en((t=>{t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+e)}),((t,e)=>(e-t)/un),(t=>Math.floor(t/un)));function _n(t){return en((e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)}),((t,e)=>{t.setDate(t.getDate()+7*e)}),((t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*on)/sn))}vn.range;const xn=_n(0),bn=_n(1),wn=_n(2),kn=_n(3),An=_n(4),Mn=_n(5),En=_n(6);function Dn(t){return en((e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)}),((t,e)=>(e-t)/sn))}xn.range,bn.range,wn.range,kn.range,An.range,Mn.range,En.range;const Cn=Dn(0),Fn=Dn(1),Sn=Dn(2),$n=Dn(3),Tn=Dn(4),Bn=Dn(5),Nn=Dn(6);Cn.range,Fn.range,Sn.range,$n.range,Tn.range,Bn.range,Nn.range;const zn=en((t=>{t.setDate(1),t.setHours(0,0,0,0)}),((t,e)=>{t.setMonth(t.getMonth()+e)}),((t,e)=>e.getMonth()-t.getMonth()+12*(e.getFullYear()-t.getFullYear())),(t=>t.getMonth()));zn.range;const On=en((t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)}),((t,e)=>e.getUTCMonth()-t.getUTCMonth()+12*(e.getUTCFullYear()-t.getUTCFullYear())),(t=>t.getUTCMonth()));On.range;const Rn=en((t=>{t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,e)=>{t.setFullYear(t.getFullYear()+e)}),((t,e)=>e.getFullYear()-t.getFullYear()),(t=>t.getFullYear()));Rn.every=t=>isFinite(t=Math.floor(t))&&t>0?en((e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,n)=>{e.setFullYear(e.getFullYear()+n*t)})):null,Rn.range;const Un=en((t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)}),((t,e)=>e.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));function Ln(t,e,n,r,i,o){const a=[[fn,1,rn],[fn,5,5*rn],[fn,15,15*rn],[fn,30,30*rn],[o,1,on],[o,5,5*on],[o,15,15*on],[o,30,30*on],[i,1,an],[i,3,3*an],[i,6,6*an],[i,12,12*an],[r,1,un],[r,2,2*un],[n,1,sn],[e,1,ln],[e,3,3*ln],[t,1,cn]];function u(e,n,r){const i=Math.abs(n-e)/r,o=ne((t=>{let[,,e]=t;return e})).right(a,i);if(o===a.length)return t.every(ke(e/cn,n/cn,r));if(0===o)return nn.every(Math.max(ke(e,n,r),1));const[u,s]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return u.every(s)}return[function(t,e,n){const r=e<t;r&&([t,e]=[e,t]);const i=n&&"function"==typeof n.range?n:u(t,e,n),o=i?i.range(t,+e+1):[];return r?o.reverse():o},u]}Un.every=t=>isFinite(t=Math.floor(t))&&t>0?en((e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,n)=>{e.setUTCFullYear(e.getUTCFullYear()+n*t)})):null,Un.range;const[qn,Pn]=Ln(Un,On,Cn,vn,gn,dn),[jn,In]=Ln(Rn,zn,xn,mn,pn,hn),Wn="year",Hn="quarter",Yn="month",Gn="week",Vn="date",Xn="day",Jn="dayofyear",Zn="hours",Qn="minutes",Kn="seconds",tr="milliseconds",er=[Wn,Hn,Yn,Gn,Vn,Xn,Jn,Zn,Qn,Kn,tr],nr=er.reduce(((t,e,n)=>(t[e]=1+n,t)),{});function rr(t){const e=V(t).slice(),n={};e.length||u("Missing time unit."),e.forEach((t=>{lt(nr,t)?n[t]=1:u(`Invalid time unit: ${t}.`)}));return(n[Gn]||n[Xn]?1:0)+(n[Hn]||n[Yn]||n[Vn]?1:0)+(n[Jn]?1:0)>1&&u(`Incompatible time units: ${t}`),e.sort(((t,e)=>nr[t]-nr[e])),e}const ir={[Wn]:"%Y ",[Hn]:"Q%q ",[Yn]:"%b ",[Vn]:"%d ",[Gn]:"W%U ",[Xn]:"%a ",[Jn]:"%j ",[Zn]:"%H:00",[Qn]:"00:%M",[Kn]:":%S",[tr]:".%L",[`${Wn}-${Yn}`]:"%Y-%m ",[`${Wn}-${Yn}-${Vn}`]:"%Y-%m-%d ",[`${Zn}-${Qn}`]:"%H:%M"};function or(t,e){const n=ot({},ir,e),r=rr(t),i=r.length;let o,a,u="",s=0;for(s=0;s<i;)for(o=r.length;o>s;--o)if(a=r.slice(s,o).join("-"),null!=n[a]){u+=n[a],s=o;break}return u.trim()}const ar=new Date;function ur(t){return ar.setFullYear(t),ar.setMonth(0),ar.setDate(1),ar.setHours(0,0,0,0),ar}function sr(t){return cr(new Date(t))}function lr(t){return fr(new Date(t))}function cr(t){return mn.count(ur(t.getFullYear())-1,t)}function fr(t){return xn.count(ur(t.getFullYear())-1,t)}function hr(t){return ur(t).getDay()}function dr(t,e,n,r,i,o,a){if(0<=t&&t<100){const u=new Date(-1,e,n,r,i,o,a);return u.setFullYear(t),u}return new Date(t,e,n,r,i,o,a)}function pr(t){return mr(new Date(t))}function gr(t){return yr(new Date(t))}function mr(t){const e=Date.UTC(t.getUTCFullYear(),0,1);return yn.count(e-1,t)}function yr(t){const e=Date.UTC(t.getUTCFullYear(),0,1);return Cn.count(e-1,t)}function vr(t){return ar.setTime(Date.UTC(t,0,1)),ar.getUTCDay()}function _r(t,e,n,r,i,o,a){if(0<=t&&t<100){const t=new Date(Date.UTC(-1,e,n,r,i,o,a));return t.setUTCFullYear(n.y),t}return new Date(Date.UTC(t,e,n,r,i,o,a))}function xr(t,e,n,r,i){const o=e||1,a=F(t),u=(t,e,i)=>function(t,e,n,r){const i=n<=1?t:r?(e,i)=>r+n*Math.floor((t(e,i)-r)/n):(e,r)=>n*Math.floor(t(e,r)/n);return e?(t,n)=>e(i(t,n),n):i}(n[i=i||t],r[i],t===a&&o,e),s=new Date,l=Nt(t),c=l[Wn]?u(Wn):rt(2012),f=l[Yn]?u(Yn):l[Hn]?u(Hn):h,p=l[Gn]&&l[Xn]?u(Xn,1,Gn+Xn):l[Gn]?u(Gn,1):l[Xn]?u(Xn,1):l[Vn]?u(Vn,1):l[Jn]?u(Jn,1):d,g=l[Zn]?u(Zn):h,m=l[Qn]?u(Qn):h,y=l[Kn]?u(Kn):h,v=l[tr]?u(tr):h;return function(t){s.setTime(+t);const e=c(s);return i(e,f(s),p(s,e),g(s),m(s),y(s),v(s))}}function br(t,e,n){return e+7*t-(n+6)%7}const wr={[Wn]:t=>t.getFullYear(),[Hn]:t=>Math.floor(t.getMonth()/3),[Yn]:t=>t.getMonth(),[Vn]:t=>t.getDate(),[Zn]:t=>t.getHours(),[Qn]:t=>t.getMinutes(),[Kn]:t=>t.getSeconds(),[tr]:t=>t.getMilliseconds(),[Jn]:t=>cr(t),[Gn]:t=>fr(t),[Gn+Xn]:(t,e)=>br(fr(t),t.getDay(),hr(e)),[Xn]:(t,e)=>br(1,t.getDay(),hr(e))},kr={[Hn]:t=>3*t,[Gn]:(t,e)=>br(t,0,hr(e))};function Ar(t,e){return xr(t,e||1,wr,kr,dr)}const Mr={[Wn]:t=>t.getUTCFullYear(),[Hn]:t=>Math.floor(t.getUTCMonth()/3),[Yn]:t=>t.getUTCMonth(),[Vn]:t=>t.getUTCDate(),[Zn]:t=>t.getUTCHours(),[Qn]:t=>t.getUTCMinutes(),[Kn]:t=>t.getUTCSeconds(),[tr]:t=>t.getUTCMilliseconds(),[Jn]:t=>mr(t),[Gn]:t=>yr(t),[Xn]:(t,e)=>br(1,t.getUTCDay(),vr(e)),[Gn+Xn]:(t,e)=>br(yr(t),t.getUTCDay(),vr(e))},Er={[Hn]:t=>3*t,[Gn]:(t,e)=>br(t,0,vr(e))};function Dr(t,e){return xr(t,e||1,Mr,Er,_r)}const Cr={[Wn]:Rn,[Hn]:zn.every(3),[Yn]:zn,[Gn]:xn,[Vn]:mn,[Xn]:mn,[Jn]:mn,[Zn]:pn,[Qn]:hn,[Kn]:fn,[tr]:nn},Fr={[Wn]:Un,[Hn]:On.every(3),[Yn]:On,[Gn]:Cn,[Vn]:yn,[Xn]:yn,[Jn]:yn,[Zn]:gn,[Qn]:dn,[Kn]:fn,[tr]:nn};function Sr(t){return Cr[t]}function $r(t){return Fr[t]}function Tr(t,e,n){return t?t.offset(e,n):void 0}function Br(t,e,n){return Tr(Sr(t),e,n)}function Nr(t,e,n){return Tr($r(t),e,n)}function zr(t,e,n,r){return t?t.range(e,n,r):void 0}function Or(t,e,n,r){return zr(Sr(t),e,n,r)}function Rr(t,e,n,r){return zr($r(t),e,n,r)}const Ur=1e3,Lr=6e4,qr=36e5,Pr=864e5,jr=2592e6,Ir=31536e6,Wr=[Wn,Yn,Vn,Zn,Qn,Kn,tr],Hr=Wr.slice(0,-1),Yr=Hr.slice(0,-1),Gr=Yr.slice(0,-1),Vr=Gr.slice(0,-1),Xr=[Wn,Yn],Jr=[Wn],Zr=[[Hr,1,Ur],[Hr,5,5e3],[Hr,15,15e3],[Hr,30,3e4],[Yr,1,Lr],[Yr,5,3e5],[Yr,15,9e5],[Yr,30,18e5],[Gr,1,qr],[Gr,3,108e5],[Gr,6,216e5],[Gr,12,432e5],[Vr,1,Pr],[[Wn,Gn],1,6048e5],[Xr,1,jr],[Xr,3,7776e6],[Jr,1,Ir]];function Qr(t){const e=t.extent,n=t.maxbins||40,r=Math.abs(Ct(e))/n;let i,o,a=ne((t=>t[2])).right(Zr,r);return a===Zr.length?(i=Jr,o=ke(e[0]/Ir,e[1]/Ir,n)):a?(a=Zr[r/Zr[a-1][2]<Zr[a][2]/r?a-1:a],i=a[0],o=a[1]):(i=Wr,o=Math.max(ke(e[0],e[1],n),1)),{units:i,step:o}}function Kr(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ti(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ei(t,e,n){return{y:t,m:e,d:n,H:0,M:0,S:0,L:0}}function ni(t){var e=t.dateTime,n=t.date,r=t.time,i=t.periods,o=t.days,a=t.shortDays,u=t.months,s=t.shortMonths,l=pi(i),c=gi(i),f=pi(o),h=gi(o),d=pi(a),p=gi(a),g=pi(u),m=gi(u),y=pi(s),v=gi(s),_={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return s[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:Oi,e:Oi,f:Pi,g:Qi,G:to,H:Ri,I:Ui,j:Li,L:qi,m:ji,M:Ii,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:Ao,s:Mo,S:Wi,u:Hi,U:Yi,V:Vi,w:Xi,W:Ji,x:null,X:null,y:Zi,Y:Ki,Z:eo,"%":ko},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return s[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:no,e:no,f:uo,g:_o,G:bo,H:ro,I:io,j:oo,L:ao,m:so,M:lo,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:Ao,s:Mo,S:co,u:fo,U:ho,V:go,w:mo,W:yo,x:null,X:null,y:vo,Y:xo,Z:wo,"%":ko},b={a:function(t,e,n){var r=d.exec(e.slice(n));return r?(t.w=p.get(r[0].toLowerCase()),n+r[0].length):-1},A:function(t,e,n){var r=f.exec(e.slice(n));return r?(t.w=h.get(r[0].toLowerCase()),n+r[0].length):-1},b:function(t,e,n){var r=y.exec(e.slice(n));return r?(t.m=v.get(r[0].toLowerCase()),n+r[0].length):-1},B:function(t,e,n){var r=g.exec(e.slice(n));return r?(t.m=m.get(r[0].toLowerCase()),n+r[0].length):-1},c:function(t,n,r){return A(t,e,n,r)},d:Ei,e:Ei,f:Ti,g:wi,G:bi,H:Ci,I:Ci,j:Di,L:$i,m:Mi,M:Fi,p:function(t,e,n){var r=l.exec(e.slice(n));return r?(t.p=c.get(r[0].toLowerCase()),n+r[0].length):-1},q:Ai,Q:Ni,s:zi,S:Si,u:yi,U:vi,V:_i,w:mi,W:xi,x:function(t,e,r){return A(t,n,e,r)},X:function(t,e,n){return A(t,r,e,n)},y:wi,Y:bi,Z:ki,"%":Bi};function w(t,e){return function(n){var r,i,o,a=[],u=-1,s=0,l=t.length;for(n instanceof Date||(n=new Date(+n));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(s,u)),null!=(i=si[r=t.charAt(++u)])?r=t.charAt(++u):i="e"===r?" ":"0",(o=e[r])&&(r=o(n,i)),a.push(r),s=u+1);return a.push(t.slice(s,u)),a.join("")}}function k(t,e){return function(n){var r,i,o=ei(1900,void 0,1);if(A(o,t,n+="",0)!=n.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(e&&!("Z"in o)&&(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(i=(r=ti(ei(o.y,0,1))).getUTCDay(),r=i>4||0===i?Fn.ceil(r):Fn(r),r=yn.offset(r,7*(o.V-1)),o.y=r.getUTCFullYear(),o.m=r.getUTCMonth(),o.d=r.getUTCDate()+(o.w+6)%7):(i=(r=Kr(ei(o.y,0,1))).getDay(),r=i>4||0===i?bn.ceil(r):bn(r),r=mn.offset(r,7*(o.V-1)),o.y=r.getFullYear(),o.m=r.getMonth(),o.d=r.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),i="Z"in o?ti(ei(o.y,0,1)).getUTCDay():Kr(ei(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,ti(o)):Kr(o)}}function A(t,e,n,r){for(var i,o,a=0,u=e.length,s=n.length;a<u;){if(r>=s)return-1;if(37===(i=e.charCodeAt(a++))){if(i=e.charAt(a++),!(o=b[i in si?e.charAt(a++):i])||(r=o(t,n,r))<0)return-1}else if(i!=n.charCodeAt(r++))return-1}return r}return _.x=w(n,_),_.X=w(r,_),_.c=w(e,_),x.x=w(n,x),x.X=w(r,x),x.c=w(e,x),{format:function(t){var e=w(t+="",_);return e.toString=function(){return t},e},parse:function(t){var e=k(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=k(t+="",!0);return e.toString=function(){return t},e}}}var ri,ii,oi,ai,ui,si={"-":"",_:" ",0:"0"},li=/^\s*\d+/,ci=/^%/,fi=/[\\^$*+?|[\]().{}]/g;function hi(t,e,n){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<n?new Array(n-o+1).join(e)+i:i)}function di(t){return t.replace(fi,"\\$&")}function pi(t){return new RegExp("^(?:"+t.map(di).join("|")+")","i")}function gi(t){return new Map(t.map(((t,e)=>[t.toLowerCase(),e])))}function mi(t,e,n){var r=li.exec(e.slice(n,n+1));return r?(t.w=+r[0],n+r[0].length):-1}function yi(t,e,n){var r=li.exec(e.slice(n,n+1));return r?(t.u=+r[0],n+r[0].length):-1}function vi(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.U=+r[0],n+r[0].length):-1}function _i(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.V=+r[0],n+r[0].length):-1}function xi(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.W=+r[0],n+r[0].length):-1}function bi(t,e,n){var r=li.exec(e.slice(n,n+4));return r?(t.y=+r[0],n+r[0].length):-1}function wi(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function ki(t,e,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(n,n+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function Ai(t,e,n){var r=li.exec(e.slice(n,n+1));return r?(t.q=3*r[0]-3,n+r[0].length):-1}function Mi(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.m=r[0]-1,n+r[0].length):-1}function Ei(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.d=+r[0],n+r[0].length):-1}function Di(t,e,n){var r=li.exec(e.slice(n,n+3));return r?(t.m=0,t.d=+r[0],n+r[0].length):-1}function Ci(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.H=+r[0],n+r[0].length):-1}function Fi(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.M=+r[0],n+r[0].length):-1}function Si(t,e,n){var r=li.exec(e.slice(n,n+2));return r?(t.S=+r[0],n+r[0].length):-1}function $i(t,e,n){var r=li.exec(e.slice(n,n+3));return r?(t.L=+r[0],n+r[0].length):-1}function Ti(t,e,n){var r=li.exec(e.slice(n,n+6));return r?(t.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Bi(t,e,n){var r=ci.exec(e.slice(n,n+1));return r?n+r[0].length:-1}function Ni(t,e,n){var r=li.exec(e.slice(n));return r?(t.Q=+r[0],n+r[0].length):-1}function zi(t,e,n){var r=li.exec(e.slice(n));return r?(t.s=+r[0],n+r[0].length):-1}function Oi(t,e){return hi(t.getDate(),e,2)}function Ri(t,e){return hi(t.getHours(),e,2)}function Ui(t,e){return hi(t.getHours()%12||12,e,2)}function Li(t,e){return hi(1+mn.count(Rn(t),t),e,3)}function qi(t,e){return hi(t.getMilliseconds(),e,3)}function Pi(t,e){return qi(t,e)+"000"}function ji(t,e){return hi(t.getMonth()+1,e,2)}function Ii(t,e){return hi(t.getMinutes(),e,2)}function Wi(t,e){return hi(t.getSeconds(),e,2)}function Hi(t){var e=t.getDay();return 0===e?7:e}function Yi(t,e){return hi(xn.count(Rn(t)-1,t),e,2)}function Gi(t){var e=t.getDay();return e>=4||0===e?An(t):An.ceil(t)}function Vi(t,e){return t=Gi(t),hi(An.count(Rn(t),t)+(4===Rn(t).getDay()),e,2)}function Xi(t){return t.getDay()}function Ji(t,e){return hi(bn.count(Rn(t)-1,t),e,2)}function Zi(t,e){return hi(t.getFullYear()%100,e,2)}function Qi(t,e){return hi((t=Gi(t)).getFullYear()%100,e,2)}function Ki(t,e){return hi(t.getFullYear()%1e4,e,4)}function to(t,e){var n=t.getDay();return hi((t=n>=4||0===n?An(t):An.ceil(t)).getFullYear()%1e4,e,4)}function eo(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+hi(e/60|0,"0",2)+hi(e%60,"0",2)}function no(t,e){return hi(t.getUTCDate(),e,2)}function ro(t,e){return hi(t.getUTCHours(),e,2)}function io(t,e){return hi(t.getUTCHours()%12||12,e,2)}function oo(t,e){return hi(1+yn.count(Un(t),t),e,3)}function ao(t,e){return hi(t.getUTCMilliseconds(),e,3)}function uo(t,e){return ao(t,e)+"000"}function so(t,e){return hi(t.getUTCMonth()+1,e,2)}function lo(t,e){return hi(t.getUTCMinutes(),e,2)}function co(t,e){return hi(t.getUTCSeconds(),e,2)}function fo(t){var e=t.getUTCDay();return 0===e?7:e}function ho(t,e){return hi(Cn.count(Un(t)-1,t),e,2)}function po(t){var e=t.getUTCDay();return e>=4||0===e?Tn(t):Tn.ceil(t)}function go(t,e){return t=po(t),hi(Tn.count(Un(t),t)+(4===Un(t).getUTCDay()),e,2)}function mo(t){return t.getUTCDay()}function yo(t,e){return hi(Fn.count(Un(t)-1,t),e,2)}function vo(t,e){return hi(t.getUTCFullYear()%100,e,2)}function _o(t,e){return hi((t=po(t)).getUTCFullYear()%100,e,2)}function xo(t,e){return hi(t.getUTCFullYear()%1e4,e,4)}function bo(t,e){var n=t.getUTCDay();return hi((t=n>=4||0===n?Tn(t):Tn.ceil(t)).getUTCFullYear()%1e4,e,4)}function wo(){return"+0000"}function ko(){return"%"}function Ao(t){return+t}function Mo(t){return Math.floor(+t/1e3)}function Eo(t){const e={};return n=>e[n]||(e[n]=t(n))}function Do(t){const e=Eo(t.format),n=t.formatPrefix;return{format:e,formatPrefix:n,formatFloat(t){const n=Le(t||",");if(null==n.precision){switch(n.precision=12,n.type){case"%":n.precision-=2;break;case"e":n.precision-=1}return r=e(n),i=e(".1f")(1)[1],t=>{const e=r(t),n=e.indexOf(i);if(n<0)return e;let o=function(t,e){let n,r=t.lastIndexOf("e");if(r>0)return r;for(r=t.length;--r>e;)if(n=t.charCodeAt(r),n>=48&&n<=57)return r+1}(e,n);const a=o<e.length?e.slice(o):"";for(;--o>n;)if("0"!==e[o]){++o;break}return e.slice(0,o)+a}}return e(n);var r,i},formatSpan(t,r,i,o){o=Le(null==o?",f":o);const a=ke(t,r,i),u=Math.max(Math.abs(t),Math.abs(r));let s;if(null==o.precision)switch(o.type){case"s":return isNaN(s=Ze(a,u))||(o.precision=s),n(o,u);case"":case"e":case"g":case"p":case"r":isNaN(s=Qe(a,u))||(o.precision=s-("e"===o.type));break;case"f":case"%":isNaN(s=Je(a))||(o.precision=s-2*("%"===o.type))}return e(o)}}}let Co,Fo;function So(){return Co=Do({format:He,formatPrefix:Ye})}function $o(t){return Do(Xe(t))}function To(t){return arguments.length?Co=$o(t):Co}function Bo(t,e,n){A(n=n||{})||u(`Invalid time multi-format specifier: ${n}`);const r=e(Kn),i=e(Qn),o=e(Zn),a=e(Vn),s=e(Gn),l=e(Yn),c=e(Hn),f=e(Wn),h=t(n[tr]||".%L"),d=t(n[Kn]||":%S"),p=t(n[Qn]||"%I:%M"),g=t(n[Zn]||"%I %p"),m=t(n[Vn]||n[Xn]||"%a %d"),y=t(n[Gn]||"%b %d"),v=t(n[Yn]||"%B"),_=t(n[Hn]||"%B"),x=t(n[Wn]||"%Y");return t=>(r(t)<t?h:i(t)<t?d:o(t)<t?p:a(t)<t?g:l(t)<t?s(t)<t?m:y:f(t)<t?c(t)<t?v:_:x)(t)}function No(t){const e=Eo(t.format),n=Eo(t.utcFormat);return{timeFormat:t=>xt(t)?e(t):Bo(e,Sr,t),utcFormat:t=>xt(t)?n(t):Bo(n,$r,t),timeParse:Eo(t.parse),utcParse:Eo(t.utcParse)}}function zo(){return Fo=No({format:ii,parse:oi,utcFormat:ai,utcParse:ui})}function Oo(t){return No(ni(t))}function Ro(t){return arguments.length?Fo=Oo(t):Fo}!function(t){ri=ni(t),ii=ri.format,oi=ri.parse,ai=ri.utcFormat,ui=ri.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}),So(),zo();const Uo=(t,e)=>ot({},t,e);function Lo(t,e){const n=t?$o(t):To(),r=e?Oo(e):Ro();return Uo(n,r)}function qo(t,e){const n=arguments.length;return n&&2!==n&&u("defaultLocale expects either zero or two arguments."),n?Uo(To(t),Ro(e)):Uo(To(),Ro())}const Po=/^(data:|([A-Za-z]+:)?\/\/)/,jo=/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|file|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,Io=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205f\u3000]/g,Wo="file://";async function Ho(t,e){const n=await this.sanitize(t,e),r=n.href;return n.localFile?this.file(r):this.http(r,e)}async function Yo(t,e){e=ot({},this.options,e);const n=this.fileAccess,r={href:null};let i,o,a;const s=jo.test(t.replace(Io,""));null!=t&&"string"==typeof t&&s||u("Sanitize failure, invalid URI: "+Ft(t));const l=Po.test(t);return(a=e.baseURL)&&!l&&(t.startsWith("/")||a.endsWith("/")||(t="/"+t),t=a+t),o=(i=t.startsWith(Wo))||"file"===e.mode||"http"!==e.mode&&!l&&n,i?t=t.slice(Wo.length):t.startsWith("//")&&("file"===e.defaultProtocol?(t=t.slice(2),o=!0):t=(e.defaultProtocol||"http")+":"+t),Object.defineProperty(r,"localFile",{value:!!o}),r.href=t,e.target&&(r.target=e.target+""),e.rel&&(r.rel=e.rel+""),"image"===e.context&&e.crossOrigin&&(r.crossOrigin=e.crossOrigin+""),r}function Go(t){return t?e=>new Promise(((n,r)=>{t.readFile(e,((t,e)=>{t?r(t):n(e)}))})):Vo}async function Vo(){u("No file system access.")}function Xo(t){return t?async function(e,n){const r=ot({},this.options.http,n),i=n&&n.response,o=await t(e,r);return o.ok?J(o[i])?o[i]():o.text():u(o.status+""+o.statusText)}:Jo}async function Jo(){u("No HTTP fetch method available.")}const Zo=t=>null!=t&&t==t,Qo=t=>!(Number.isNaN(+t)||t instanceof Date),Ko={boolean:St,integer:S,number:S,date:Tt,string:Bt,unknown:f},ta=[t=>"true"===t||"false"===t||!0===t||!1===t,t=>Qo(t)&&Number.isInteger(+t),Qo,t=>!Number.isNaN(Date.parse(t))],ea=["boolean","integer","number","date"];function na(t,e){if(!t||!t.length)return"unknown";const n=t.length,r=ta.length,i=ta.map(((t,e)=>e+1));for(let o,a,u=0,s=0;u<n;++u)for(a=e?t[u][e]:t[u],o=0;o<r;++o)if(i[o]&&Zo(a)&&!ta[o](a)&&(i[o]=0,++s,s===ta.length))return"string";return ea[i.reduce(((t,e)=>0===t?e:t),0)-1]}function ra(t,e){return e.reduce(((e,n)=>(e[n]=na(t,n),e)),{})}function ia(t){const e=function(e,n){const r={delimiter:t};return oa(e,n?ot(n,r):r)};return e.responseType="text",e}function oa(t,e){return e.header&&(t=e.header.map(Ft).join(e.delimiter)+"\n"+t),Yt(e.delimiter).parse(t+"")}function aa(t,e){const n=e&&e.property?l(e.property):f;return!A(t)||(r=t,"function"==typeof Buffer&&J(Buffer.isBuffer)&&Buffer.isBuffer(r))?n(JSON.parse(t)):function(t,e){!k(t)&&yt(t)&&(t=[...t]);return e&&e.copy?JSON.parse(JSON.stringify(t)):t}(n(t),e);var r}oa.responseType="text",aa.responseType="json";const ua={interior:(t,e)=>t!==e,exterior:(t,e)=>t===e};function sa(t,e){let n,r,i,o;return t=aa(t,e),e&&e.feature?(n=Vt,i=e.feature):e&&e.mesh?(n=Qt,i=e.mesh,o=ua[e.filter]):u("Missing TopoJSON feature or mesh parameter."),r=(r=t.objects[i])?n(t,r,o):u("Invalid TopoJSON object: "+i),r&&r.features||[r]}sa.responseType="json";const la={dsv:oa,csv:ia(","),tsv:ia("\t"),json:aa,topojson:sa};function ca(t,e){return arguments.length>1?(la[t]=e,this):lt(la,t)?la[t]:null}function fa(t){const e=ca(t);return e&&e.responseType||"text"}function ha(t,e,n,r){const i=ca((e=e||{}).type||"json");return i||u("Unknown data format type: "+e.type),t=i(t,e),e.parse&&function(t,e,n,r){if(!t.length)return;const i=Ro();n=n||i.timeParse,r=r||i.utcParse;let o,a,u,s,l,c,f=t.columns||Object.keys(t[0]);"auto"===e&&(e=ra(t,f));f=Object.keys(e);const h=f.map((t=>{const i=e[t];let o,a;if(i&&(i.startsWith("date:")||i.startsWith("utc:"))){o=i.split(/:(.+)?/,2),a=o[1],("'"===a[0]&&"'"===a[a.length-1]||'"'===a[0]&&'"'===a[a.length-1])&&(a=a.slice(1,-1));return("utc"===o[0]?r:n)(a)}if(!Ko[i])throw Error("Illegal format pattern: "+t+":"+i);return Ko[i]}));for(u=0,l=t.length,c=f.length;u<l;++u)for(o=t[u],s=0;s<c;++s)a=f[s],o[a]=h[s](o[a])}(t,e.parse,n,r),lt(t,"columns")&&delete t.columns,t}const da=function(t,e){return n=>({options:n||{},sanitize:Yo,load:Ho,fileAccess:!!e,file:Go(e),http:Xo(t)})}("undefined"!=typeof fetch&&fetch,null);function pa(t){const e=t||f,n=[],r={};return n.add=t=>{const i=e(t);return r[i]||(r[i]=1,n.push(t)),n},n.remove=t=>{const i=e(t);if(r[i]){r[i]=0;const e=n.indexOf(t);e>=0&&n.splice(e,1)}return n},n}async function ga(t,e){try{await e(t)}catch(e){t.error(e)}}const ma=Symbol("vega_id");let ya=1;function va(t){return!(!t||!_a(t))}function _a(t){return t[ma]}function xa(t,e){return t[ma]=e,t}function ba(t){const e=t===Object(t)?t:{data:t};return _a(e)?e:xa(e,ya++)}function wa(t){return ka(t,ba({}))}function ka(t,e){for(const n in t)e[n]=t[n];return e}function Aa(t,e){return xa(e,_a(t))}function Ma(t,e){return t?e?(n,r)=>t(n,r)||_a(e(n))-_a(e(r)):(e,n)=>t(e,n)||_a(e)-_a(n):null}function Ea(t){return t&&t.constructor===Da}function Da(){const t=[],e=[],n=[],r=[],i=[];let o=null,a=!1;return{constructor:Da,insert(e){const n=V(e),r=n.length;for(let e=0;e<r;++e)t.push(n[e]);return this},remove(t){const n=J(t)?r:e,i=V(t),o=i.length;for(let t=0;t<o;++t)n.push(i[t]);return this},modify(t,e,r){const o={field:e,value:rt(r)};return J(t)?(o.filter=t,i.push(o)):(o.tuple=t,n.push(o)),this},encode(t,e){return J(t)?i.push({filter:t,field:e}):n.push({tuple:t,field:e}),this},clean(t){return o=t,this},reflow(){return a=!0,this},pulse(u,s){const l={},c={};let f,h,d,p,g,m;for(f=0,h=s.length;f<h;++f)l[_a(s[f])]=1;for(f=0,h=e.length;f<h;++f)g=e[f],l[_a(g)]=-1;for(f=0,h=r.length;f<h;++f)p=r[f],s.forEach((t=>{p(t)&&(l[_a(t)]=-1)}));for(f=0,h=t.length;f<h;++f)g=t[f],m=_a(g),l[m]?l[m]=1:u.add.push(ba(t[f]));for(f=0,h=s.length;f<h;++f)g=s[f],l[_a(g)]<0&&u.rem.push(g);function y(t,e,n){n?t[e]=n(t):u.encode=e,a||(c[_a(t)]=t)}for(f=0,h=n.length;f<h;++f)d=n[f],g=d.tuple,p=d.field,m=l[_a(g)],m>0&&(y(g,p,d.value),u.modifies(p));for(f=0,h=i.length;f<h;++f)d=i[f],p=d.filter,s.forEach((t=>{p(t)&&l[_a(t)]>0&&y(t,d.field,d.value)})),u.modifies(d.field);if(a)u.mod=e.length||r.length?s.filter((t=>l[_a(t)]>0)):s.slice();else for(m in c)u.mod.push(c[m]);return(o||null==o&&(e.length||r.length))&&u.clean(!0),u}}}const Ca="_:mod:_";function Fa(){Object.defineProperty(this,Ca,{writable:!0,value:{}})}Fa.prototype={set(t,e,n,r){const i=this,o=i[t],a=i[Ca];return null!=e&&e>=0?(o[e]!==n||r)&&(o[e]=n,a[e+":"+t]=-1,a[t]=-1):(o!==n||r)&&(i[t]=n,a[t]=k(n)?1+n.length:-1),i},modified(t,e){const n=this[Ca];if(!arguments.length){for(const t in n)if(n[t])return!0;return!1}if(k(t)){for(let e=0;e<t.length;++e)if(n[t[e]])return!0;return!1}return null!=e&&e>=0?e+1<n[t]||!!n[e+":"+t]:!!n[t]},clear(){return this[Ca]={},this}};let Sa=0;const $a=new Fa;function Ta(t,e,n,r){this.id=++Sa,this.value=t,this.stamp=-1,this.rank=-1,this.qrank=-1,this.flags=0,e&&(this._update=e),n&&this.parameters(n,r)}function Ba(t){return function(e){const n=this.flags;return 0===arguments.length?!!(n&t):(this.flags=e?n|t:n&~t,this)}}Ta.prototype={targets(){return this._targets||(this._targets=pa(c))},set(t){return this.value!==t?(this.value=t,1):0},skip:Ba(1),modified:Ba(2),parameters(t,e,n){e=!1!==e;const r=this._argval=this._argval||new Fa,i=this._argops=this._argops||[],o=[];let a,s,l,c;const f=(t,n,a)=>{a instanceof Ta?(a!==this&&(e&&a.targets().add(this),o.push(a)),i.push({op:a,name:t,index:n})):r.set(t,n,a)};for(a in t)if(s=t[a],"pulse"===a)V(s).forEach((t=>{t instanceof Ta?t!==this&&(t.targets().add(this),o.push(t)):u("Pulse parameters must be operator instances.")})),this.source=s;else if(k(s))for(r.set(a,-1,Array(l=s.length)),c=0;c<l;++c)f(a,c,s[c]);else f(a,-1,s);return this.marshall().clear(),n&&(i.initonly=!0),o},marshall(t){const e=this._argval||$a,n=this._argops;let r,i,o,a;if(n){const u=n.length;for(i=0;i<u;++i)r=n[i],o=r.op,a=o.modified()&&o.stamp===t,e.set(r.name,r.index,o.value,a);if(n.initonly){for(i=0;i<u;++i)r=n[i],r.op.targets().remove(this);this._argops=null,this._update=null}}return e},detach(){const t=this._argops;let e,n,r,i;if(t)for(e=0,n=t.length;e<n;++e)r=t[e],i=r.op,i._targets&&i._targets.remove(this);this.pulse=null,this.source=null},evaluate(t){const e=this._update;if(e){const n=this.marshall(t.stamp),r=e.call(this,n,t);if(n.clear(),r!==this.value)this.value=r;else if(!this.modified())return t.StopPropagation}},run(t){if(t.stamp<this.stamp)return t.StopPropagation;let e;return this.skip()?(this.skip(!1),e=0):e=this.evaluate(t),this.pulse=e||t}};let Na=0;function za(t,e,n){this.id=++Na,this.value=null,n&&(this.receive=n),t&&(this._filter=t),e&&(this._apply=e)}function Oa(t,e,n){return new za(t,e,n)}za.prototype={_filter:p,_apply:f,targets(){return this._targets||(this._targets=pa(c))},consume(t){return arguments.length?(this._consume=!!t,this):!!this._consume},receive(t){if(this._filter(t)){const e=this.value=this._apply(t),n=this._targets,r=n?n.length:0;for(let t=0;t<r;++t)n[t].receive(e);this._consume&&(t.preventDefault(),t.stopPropagation())}},filter(t){const e=Oa(t);return this.targets().add(e),e},apply(t){const e=Oa(null,t);return this.targets().add(e),e},merge(){const t=Oa();this.targets().add(t);for(let e=0,n=arguments.length;e<n;++e)arguments[e].targets().add(t);return t},throttle(t){let e=-1;return this.filter((()=>{const n=Date.now();return n-e>t?(e=n,1):0}))},debounce(t){const e=Oa();return this.targets().add(Oa(null,null,it(t,(t=>{const n=t.dataflow;e.receive(t),n&&n.run&&n.run()})))),e},between(t,e){let n=!1;return t.targets().add(Oa(null,null,(()=>n=!0))),e.targets().add(Oa(null,null,(()=>n=!1))),this.filter((()=>n))},detach(){this._filter=p,this._targets=null}};const Ra={skip:!0};function Ua(t,e,n,r,i,o){const a=ot({},o,Ra);let u,s;J(n)||(n=rt(n)),void 0===r?u=e=>t.touch(n(e)):J(r)?(s=new Ta(null,r,i,!1),u=e=>{s.evaluate(e);const r=n(e),i=s.value;Ea(i)?t.pulse(r,i,o):t.update(r,i,a)}):u=e=>t.update(n(e),r,a),e.apply(u)}function La(t,e,n,r,i,o){if(void 0===r)e.targets().add(n);else{const a=o||{},u=new Ta(null,function(t,e){return e=J(e)?e:rt(e),t?function(n,r){const i=e(n,r);return t.skip()||(t.skip(i!==this.value).value=i),i}:e}(n,r),i,!1);u.modified(a.force),u.rank=e.rank,e.targets().add(u),n&&(u.skip(!0),u.value=n.value,u.targets().add(n),t.connect(n,[u]))}}const qa={};function Pa(t,e,n){this.dataflow=t,this.stamp=null==e?-1:e,this.add=[],this.rem=[],this.mod=[],this.fields=null,this.encode=n||null}function ja(t,e){const n=[];return Ot(t,e,(t=>n.push(t))),n}function Ia(t,e){const n={};return t.visit(e,(t=>{n[_a(t)]=1})),t=>n[_a(t)]?null:t}function Wa(t,e){return t?(n,r)=>t(n,r)&&e(n,r):e}function Ha(t,e,n,r){const i=this;let o=0;this.dataflow=t,this.stamp=e,this.fields=null,this.encode=r||null,this.pulses=n;for(const t of n)if(t.stamp===e){if(t.fields){const e=i.fields||(i.fields={});for(const n in t.fields)e[n]=1}t.changed(i.ADD)&&(o|=i.ADD),t.changed(i.REM)&&(o|=i.REM),t.changed(i.MOD)&&(o|=i.MOD)}this.changes=o}function Ya(t){return t.error("Dataflow already running. Use runAsync() to chain invocations."),t}Pa.prototype={StopPropagation:qa,ADD:1,REM:2,MOD:4,ADD_REM:3,ADD_MOD:5,ALL:7,REFLOW:8,SOURCE:16,NO_SOURCE:32,NO_FIELDS:64,fork(t){return new Pa(this.dataflow).init(this,t)},clone(){const t=this.fork(7);return t.add=t.add.slice(),t.rem=t.rem.slice(),t.mod=t.mod.slice(),t.source&&(t.source=t.source.slice()),t.materialize(23)},addAll(){let t=this;return!t.source||t.add===t.rem||!t.rem.length&&t.source.length===t.add.length||(t=new Pa(this.dataflow).init(this),t.add=t.source,t.rem=[]),t},init(t,e){const n=this;return n.stamp=t.stamp,n.encode=t.encode,!t.fields||64&e||(n.fields=t.fields),1&e?(n.addF=t.addF,n.add=t.add):(n.addF=null,n.add=[]),2&e?(n.remF=t.remF,n.rem=t.rem):(n.remF=null,n.rem=[]),4&e?(n.modF=t.modF,n.mod=t.mod):(n.modF=null,n.mod=[]),32&e?(n.srcF=null,n.source=null):(n.srcF=t.srcF,n.source=t.source,t.cleans&&(n.cleans=t.cleans)),n},runAfter(t){this.dataflow.runAfter(t)},changed(t){const e=t||7;return 1&e&&this.add.length||2&e&&this.rem.length||4&e&&this.mod.length},reflow(t){if(t)return this.fork(7).reflow();const e=this.add.length,n=this.source&&this.source.length;return n&&n!==e&&(this.mod=this.source,e&&this.filter(4,Ia(this,1))),this},clean(t){return arguments.length?(this.cleans=!!t,this):this.cleans},modifies(t){const e=this.fields||(this.fields={});return k(t)?t.forEach((t=>e[t]=!0)):e[t]=!0,this},modified(t,e){const n=this.fields;return!(!e&&!this.mod.length||!n)&&(arguments.length?k(t)?t.some((t=>n[t])):n[t]:!!n)},filter(t,e){const n=this;return 1&t&&(n.addF=Wa(n.addF,e)),2&t&&(n.remF=Wa(n.remF,e)),4&t&&(n.modF=Wa(n.modF,e)),16&t&&(n.srcF=Wa(n.srcF,e)),n},materialize(t){const e=this;return 1&(t=t||7)&&e.addF&&(e.add=ja(e.add,e.addF),e.addF=null),2&t&&e.remF&&(e.rem=ja(e.rem,e.remF),e.remF=null),4&t&&e.modF&&(e.mod=ja(e.mod,e.modF),e.modF=null),16&t&&e.srcF&&(e.source=e.source.filter(e.srcF),e.srcF=null),e},visit(t,e){const n=this,r=e;if(16&t)return Ot(n.source,n.srcF,r),n;1&t&&Ot(n.add,n.addF,r),2&t&&Ot(n.rem,n.remF,r),4&t&&Ot(n.mod,n.modF,r);const i=n.source;if(8&t&&i){const t=n.add.length+n.mod.length;t===i.length||Ot(i,t?Ia(n,5):n.srcF,r)}return n}},dt(Ha,Pa,{fork(t){const e=new Pa(this.dataflow).init(this,t&this.NO_FIELDS);return void 0!==t&&(t&e.ADD&&this.visit(e.ADD,(t=>e.add.push(t))),t&e.REM&&this.visit(e.REM,(t=>e.rem.push(t))),t&e.MOD&&this.visit(e.MOD,(t=>e.mod.push(t)))),e},changed(t){return this.changes&t},modified(t){const e=this,n=e.fields;return n&&e.changes&e.MOD?k(t)?t.some((t=>n[t])):n[t]:0},filter(){u("MultiPulse does not support filtering.")},materialize(){u("MultiPulse does not support materialization.")},visit(t,e){const n=this,r=n.pulses,i=r.length;let o=0;if(t&n.SOURCE)for(;o<i;++o)r[o].visit(t,e);else for(;o<i;++o)r[o].stamp===n.stamp&&r[o].visit(t,e);return n}});const Ga={skip:!1,force:!1};function Va(t){let e=[];return{clear:()=>e=[],size:()=>e.length,peek:()=>e[0],push:n=>(e.push(n),Xa(e,0,e.length-1,t)),pop:()=>{const n=e.pop();let r;return e.length?(r=e[0],e[0]=n,function(t,e,n){const r=e,i=t.length,o=t[e];let a,u=1+(e<<1);for(;u<i;)a=u+1,a<i&&n(t[u],t[a])>=0&&(u=a),t[e]=t[u],u=1+((e=u)<<1);t[e]=o,Xa(t,r,e,n)}(e,0,t)):r=n,r}}}function Xa(t,e,n,r){let i,o;const a=t[n];for(;n>e&&(o=n-1>>1,i=t[o],r(a,i)<0);)t[n]=i,n=o;return t[n]=a}function Ja(){this.logger(w()),this.logLevel(v),this._clock=0,this._rank=0,this._locale=qo();try{this._loader=da()}catch(t){}this._touched=pa(c),this._input={},this._pulse=null,this._heap=Va(((t,e)=>t.qrank-e.qrank)),this._postrun=[]}function Za(t){return function(){return this._log[t].apply(this,arguments)}}function Qa(t,e){Ta.call(this,t,null,e)}Ja.prototype={stamp(){return this._clock},loader(t){return arguments.length?(this._loader=t,this):this._loader},locale(t){return arguments.length?(this._locale=t,this):this._locale},logger(t){return arguments.length?(this._log=t,this):this._log},error:Za("error"),warn:Za("warn"),info:Za("info"),debug:Za("debug"),logLevel:Za("level"),cleanThreshold:1e4,add:function(t,e,n,r){let i,o=1;return t instanceof Ta?i=t:t&&t.prototype instanceof Ta?i=new t:J(t)?i=new Ta(null,t):(o=0,i=new Ta(t,e)),this.rank(i),o&&(r=n,n=e),n&&this.connect(i,i.parameters(n,r)),this.touch(i),i},connect:function(t,e){const n=t.rank,r=e.length;for(let i=0;i<r;++i)if(n<e[i].rank)return void this.rerank(t)},rank:function(t){t.rank=++this._rank},rerank:function(t){const e=[t];let n,r,i;for(;e.length;)if(this.rank(n=e.pop()),r=n._targets)for(i=r.length;--i>=0;)e.push(n=r[i]),n===t&&u("Cycle detected in dataflow graph.")},pulse:function(t,e,n){this.touch(t,n||Ga);const r=new Pa(this,this._clock+(this._pulse?0:1)),i=t.pulse&&t.pulse.source||[];return r.target=t,this._input[t.id]=e.pulse(r,i),this},touch:function(t,e){const n=e||Ga;return this._pulse?this._enqueue(t):this._touched.add(t),n.skip&&t.skip(!0),this},update:function(t,e,n){const r=n||Ga;return(t.set(e)||r.force)&&this.touch(t,r),this},changeset:Da,ingest:function(t,e,n){return e=this.parse(e,n),this.pulse(t,this.changeset().insert(e))},parse:function(t,e){const n=this.locale();return ha(t,e,n.timeParse,n.utcParse)},preload:async function(t,e,n){const r=this,i=r._pending||function(t){let e;const n=new Promise((t=>e=t));return n.requests=0,n.done=()=>{0==--n.requests&&(t._pending=null,e(t))},t._pending=n}(r);i.requests+=1;const o=await r.request(e,n);return r.pulse(t,r.changeset().remove(p).insert(o.data||[])),i.done(),o},request:async function(t,e){const n=this;let r,i=0;try{r=await n.loader().load(t,{context:"dataflow",response:fa(e&&e.type)});try{r=n.parse(r,e)}catch(e){i=-2,n.warn("Data ingestion failed",t,e)}}catch(e){i=-1,n.warn("Loading failed",t,e)}return{data:r,status:i}},events:function(t,e,n,r){const i=this,o=Oa(n,r),a=function(t){t.dataflow=i;try{o.receive(t)}catch(t){i.error(t)}finally{i.run()}};let u;u="string"==typeof t&&"undefined"!=typeof document?document.querySelectorAll(t):V(t);const s=u.length;for(let t=0;t<s;++t)u[t].addEventListener(e,a);return o},on:function(t,e,n,r,i){return(t instanceof Ta?La:Ua)(this,t,e,n,r,i),this},evaluate:async function(t,e,n){const r=this,i=[];if(r._pulse)return Ya(r);if(r._pending&&await r._pending,e&&await ga(r,e),!r._touched.length)return r.debug("Dataflow invoked, but nothing to do."),r;const o=++r._clock;r._pulse=new Pa(r,o,t),r._touched.forEach((t=>r._enqueue(t,!0))),r._touched=pa(c);let a,u,s,l=0;try{for(;r._heap.size()>0;)a=r._heap.pop(),a.rank===a.qrank?(u=a.run(r._getPulse(a,t)),u.then?u=await u:u.async&&(i.push(u.async),u=qa),u!==qa&&a._targets&&a._targets.forEach((t=>r._enqueue(t))),++l):r._enqueue(a,!0)}catch(t){r._heap.clear(),s=t}if(r._input={},r._pulse=null,r.debug(`Pulse ${o}: ${l} operators`),s&&(r._postrun=[],r.error(s)),r._postrun.length){const t=r._postrun.sort(((t,e)=>e.priority-t.priority));r._postrun=[];for(let e=0;e<t.length;++e)await ga(r,t[e].callback)}return n&&await ga(r,n),i.length&&Promise.all(i).then((t=>r.runAsync(null,(()=>{t.forEach((t=>{try{t(r)}catch(t){r.error(t)}}))})))),r},run:function(t,e,n){return this._pulse?Ya(this):(this.evaluate(t,e,n),this)},runAsync:async function(t,e,n){for(;this._running;)await this._running;const r=()=>this._running=null;return(this._running=this.evaluate(t,e,n)).then(r,r),this._running},runAfter:function(t,e,n){if(this._pulse||e)this._postrun.push({priority:n||0,callback:t});else try{t(this)}catch(t){this.error(t)}},_enqueue:function(t,e){const n=t.stamp<this._clock;n&&(t.stamp=this._clock),(n||e)&&(t.qrank=t.rank,this._heap.push(t))},_getPulse:function(t,e){const n=t.source,r=this._clock;return n&&k(n)?new Ha(this,r,n.map((t=>t.pulse)),e):this._input[t.id]||function(t,e){if(e&&e.stamp===t.stamp)return e;t=t.fork(),e&&e!==qa&&(t.source=e.source);return t}(this._pulse,n&&n.pulse)}},dt(Qa,Ta,{run(t){if(t.stamp<this.stamp)return t.StopPropagation;let e;return this.skip()?this.skip(!1):e=this.evaluate(t),e=e||t,e.then?e=e.then((t=>this.pulse=t)):e!==t.StopPropagation&&(this.pulse=e),e},evaluate(t){const e=this.marshall(t.stamp),n=this.transform(e,t);return e.clear(),n},transform(){}});const Ka={};function tu(t){const e=eu(t);return e&&e.Definition||null}function eu(t){return t=t&&t.toLowerCase(),lt(Ka,t)?Ka[t]:null}function*nu(t,e){if(null==e)for(let e of t)null!=e&&""!==e&&(e=+e)>=e&&(yield e);else{let n=-1;for(let r of t)r=e(r,++n,t),null!=r&&""!==r&&(r=+r)>=r&&(yield r)}}function ru(t,e,n){const r=Float64Array.from(nu(t,n));return r.sort(te),e.map((t=>Fe(r,t)))}function iu(t,e){return ru(t,[.25,.5,.75],e)}function ou(t,e){const n=t.length,r=function(t,e){const n=function(t,e){let n,r=0,i=0,o=0;if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(n=e-i,i+=n/++r,o+=n*(e-i));else{let a=-1;for(let u of t)null!=(u=e(u,++a,t))&&(u=+u)>=u&&(n=u-i,i+=n/++r,o+=n*(u-i))}if(r>1)return o/(r-1)}(t,e);return n?Math.sqrt(n):n}(t,e),i=iu(t,e),o=(i[2]-i[0])/1.34;return 1.06*(Math.min(r,o)||r||Math.abs(i[0])||1)*Math.pow(n,-.2)}function au(t){const e=t.maxbins||20,n=t.base||10,r=Math.log(n),i=t.divide||[5,2];let o,a,u,s,l,c,f=t.extent[0],h=t.extent[1];const d=t.span||h-f||Math.abs(f)||1;if(t.step)o=t.step;else if(t.steps){for(s=d/e,l=0,c=t.steps.length;l<c&&t.steps[l]<s;++l);o=t.steps[Math.max(0,l-1)]}else{for(a=Math.ceil(Math.log(e)/r),u=t.minstep||0,o=Math.max(u,Math.pow(n,Math.round(Math.log(d)/r)-a));Math.ceil(d/o)>e;)o*=n;for(l=0,c=i.length;l<c;++l)s=o/i[l],s>=u&&d/s<=e&&(o=s)}s=Math.log(o);const p=s>=0?0:1+~~(-s/r),g=Math.pow(n,-p-1);return(t.nice||void 0===t.nice)&&(s=Math.floor(f/o+g)*o,f=f<s?s-o:s,h=Math.ceil(h/o)*o),{start:f,stop:h===f?f+o:h,step:o}}function uu(e,n,r,i){if(!e.length)return[void 0,void 0];const o=Float64Array.from(nu(e,i)),a=o.length,u=n;let s,l,c,f;for(c=0,f=Array(u);c<u;++c){for(s=0,l=0;l<a;++l)s+=o[~~(t.random()*a)];f[c]=s/a}return f.sort(te),[Ce(f,r/2),Ce(f,1-r/2)]}function su(t,e,n,r){r=r||(t=>t);const i=t.length,o=new Float64Array(i);let a,u=0,s=1,l=r(t[0]),c=l,f=l+e;for(;s<i;++s){if(a=r(t[s]),a>=f){for(c=(l+c)/2;u<s;++u)o[u]=c;f=a+e,l=a}c=a}for(c=(l+c)/2;u<s;++u)o[u]=c;return n?function(t,e){const n=t.length;let r,i,o=0,a=1;for(;t[o]===t[a];)++a;for(;a<n;){for(r=a+1;t[a]===t[r];)++r;if(t[a]-t[a-1]<e){for(i=a+(o+r-a-a>>1);i<a;)t[i++]=t[a];for(;i>a;)t[i--]=t[o]}o=a,a=r}return t}(o,e+e/4):o}t.random=Math.random;const lu=Math.sqrt(2*Math.PI),cu=Math.SQRT2;let fu=NaN;function hu(e,n){e=e||0,n=null==n?1:n;let r,i,o=0,a=0;if(fu==fu)o=fu,fu=NaN;else{do{o=2*t.random()-1,a=2*t.random()-1,r=o*o+a*a}while(0===r||r>1);i=Math.sqrt(-2*Math.log(r)/r),o*=i,fu=a*i}return e+o*n}function du(t,e,n){const r=(t-(e||0))/(n=null==n?1:n);return Math.exp(-.5*r*r)/(n*lu)}function pu(t,e,n){const r=(t-(e=e||0))/(n=null==n?1:n),i=Math.abs(r);let o;if(i>37)o=0;else{const t=Math.exp(-i*i/2);let e;i<7.07106781186547?(e=.0352624965998911*i+.700383064443688,e=e*i+6.37396220353165,e=e*i+33.912866078383,e=e*i+112.079291497871,e=e*i+221.213596169931,e=e*i+220.206867912376,o=t*e,e=.0883883476483184*i+1.75566716318264,e=e*i+16.064177579207,e=e*i+86.7807322029461,e=e*i+296.564248779674,e=e*i+637.333633378831,e=e*i+793.826512519948,e=e*i+440.413735824752,o/=e):(e=i+.65,e=i+4/e,e=i+3/e,e=i+2/e,e=i+1/e,o=t/e/2.506628274631)}return r>0?1-o:o}function gu(t,e,n){return t<0||t>1?NaN:(e||0)+(null==n?1:n)*cu*function(t){let e,n=-Math.log((1-t)*(1+t));n<6.25?(n-=3.125,e=-364441206401782e-35,e=e*n-16850591381820166e-35,e=128584807152564e-32+e*n,e=11157877678025181e-33+e*n,e=e*n-1333171662854621e-31,e=20972767875968562e-33+e*n,e=6637638134358324e-30+e*n,e=e*n-4054566272975207e-29,e=e*n-8151934197605472e-29,e=26335093153082323e-28+e*n,e=e*n-12975133253453532e-27,e=e*n-5415412054294628e-26,e=1.0512122733215323e-9+e*n,e=e*n-4.112633980346984e-9,e=e*n-2.9070369957882005e-8,e=4.2347877827932404e-7+e*n,e=e*n-13654692000834679e-22,e=e*n-13882523362786469e-21,e=.00018673420803405714+e*n,e=e*n-.000740702534166267,e=e*n-.006033670871430149,e=.24015818242558962+e*n,e=1.6536545626831027+e*n):n<16?(n=Math.sqrt(n)-3.25,e=2.2137376921775787e-9,e=9.075656193888539e-8+e*n,e=e*n-2.7517406297064545e-7,e=1.8239629214389228e-8+e*n,e=15027403968909828e-22+e*n,e=e*n-4013867526981546e-21,e=29234449089955446e-22+e*n,e=12475304481671779e-21+e*n,e=e*n-47318229009055734e-21,e=6828485145957318e-20+e*n,e=24031110387097894e-21+e*n,e=e*n-.0003550375203628475,e=.0009532893797373805+e*n,e=e*n-.0016882755560235047,e=.002491442096107851+e*n,e=e*n-.003751208507569241,e=.005370914553590064+e*n,e=1.0052589676941592+e*n,e=3.0838856104922208+e*n):Number.isFinite(n)?(n=Math.sqrt(n)-5,e=-27109920616438573e-27,e=e*n-2.555641816996525e-10,e=1.5076572693500548e-9+e*n,e=e*n-3.789465440126737e-9,e=7.61570120807834e-9+e*n,e=e*n-1.496002662714924e-8,e=2.914795345090108e-8+e*n,e=e*n-6.771199775845234e-8,e=2.2900482228026655e-7+e*n,e=e*n-9.9298272942317e-7,e=4526062597223154e-21+e*n,e=e*n-1968177810553167e-20,e=7599527703001776e-20+e*n,e=e*n-.00021503011930044477,e=e*n-.00013871931833623122,e=1.0103004648645344+e*n,e=4.849906401408584+e*n):e=1/0;return e*t}(2*t-1)}function mu(t,e){let n,r;const i={mean(t){return arguments.length?(n=t||0,i):n},stdev(t){return arguments.length?(r=null==t?1:t,i):r},sample:()=>hu(n,r),pdf:t=>du(t,n,r),cdf:t=>pu(t,n,r),icdf:t=>gu(t,n,r)};return i.mean(t).stdev(e)}function yu(e,n){const r=mu();let i=0;const o={data(t){return arguments.length?(e=t,i=t?t.length:0,o.bandwidth(n)):e},bandwidth(t){return arguments.length?(!(n=t)&&e&&(n=ou(e)),o):n},sample:()=>e[~~(t.random()*i)]+n*r.sample(),pdf(t){let o=0,a=0;for(;a<i;++a)o+=r.pdf((t-e[a])/n);return o/n/i},cdf(t){let o=0,a=0;for(;a<i;++a)o+=r.cdf((t-e[a])/n);return o/i},icdf(){throw Error("KDE icdf not supported.")}};return o.data(e)}function vu(t,e){return t=t||0,e=null==e?1:e,Math.exp(t+hu()*e)}function _u(t,e,n){if(t<=0)return 0;e=e||0,n=null==n?1:n;const r=(Math.log(t)-e)/n;return Math.exp(-.5*r*r)/(n*lu*t)}function xu(t,e,n){return pu(Math.log(t),e,n)}function bu(t,e,n){return Math.exp(gu(t,e,n))}function wu(t,e){let n,r;const i={mean(t){return arguments.length?(n=t||0,i):n},stdev(t){return arguments.length?(r=null==t?1:t,i):r},sample:()=>vu(n,r),pdf:t=>_u(t,n,r),cdf:t=>xu(t,n,r),icdf:t=>bu(t,n,r)};return i.mean(t).stdev(e)}function ku(e,n){let r,i=0;const o={weights(t){return arguments.length?(r=function(t){const e=[];let n,r=0;for(n=0;n<i;++n)r+=e[n]=null==t[n]?1:+t[n];for(n=0;n<i;++n)e[n]/=r;return e}(n=t||[]),o):n},distributions(t){return arguments.length?(t?(i=t.length,e=t):(i=0,e=[]),o.weights(n)):e},sample(){const n=t.random();let o=e[i-1],a=r[0],u=0;for(;u<i-1;a+=r[++u])if(n<a){o=e[u];break}return o.sample()},pdf(t){let n=0,o=0;for(;o<i;++o)n+=r[o]*e[o].pdf(t);return n},cdf(t){let n=0,o=0;for(;o<i;++o)n+=r[o]*e[o].cdf(t);return n},icdf(){throw Error("Mixture icdf not supported.")}};return o.distributions(e).weights(n)}function Au(e,n){return null==n&&(n=null==e?1:e,e=0),e+(n-e)*t.random()}function Mu(t,e,n){return null==n&&(n=null==e?1:e,e=0),t>=e&&t<=n?1/(n-e):0}function Eu(t,e,n){return null==n&&(n=null==e?1:e,e=0),t<e?0:t>n?1:(t-e)/(n-e)}function Du(t,e,n){return null==n&&(n=null==e?1:e,e=0),t>=0&&t<=1?e+t*(n-e):NaN}function Cu(t,e){let n,r;const i={min(t){return arguments.length?(n=t||0,i):n},max(t){return arguments.length?(r=null==t?1:t,i):r},sample:()=>Au(n,r),pdf:t=>Mu(t,n,r),cdf:t=>Eu(t,n,r),icdf:t=>Du(t,n,r)};return null==e&&(e=null==t?1:t,t=0),i.min(t).max(e)}function Fu(t,e,n){let r=0,i=0;for(const o of t){const t=n(o);null==e(o)||null==t||isNaN(t)||(r+=(t-r)/++i)}return{coef:[r],predict:()=>r,rSquared:0}}function Su(t,e,n,r){const i=r-t*t,o=Math.abs(i)<1e-24?0:(n-t*e)/i;return[e-o*t,o]}function $u(t,e,n,r){t=t.filter((t=>{let r=e(t),i=n(t);return null!=r&&(r=+r)>=r&&null!=i&&(i=+i)>=i})),r&&t.sort(((t,n)=>e(t)-e(n)));const i=t.length,o=new Float64Array(i),a=new Float64Array(i);let u,s,l,c=0,f=0,h=0;for(l of t)o[c]=u=+e(l),a[c]=s=+n(l),++c,f+=(u-f)/c,h+=(s-h)/c;for(c=0;c<i;++c)o[c]-=f,a[c]-=h;return[o,a,f,h]}function Tu(t,e,n,r){let i,o,a=-1;for(const u of t)i=e(u),o=n(u),null!=i&&(i=+i)>=i&&null!=o&&(o=+o)>=o&&r(i,o,++a)}function Bu(t,e,n,r,i){let o=0,a=0;return Tu(t,e,n,((t,e)=>{const n=e-i(t),u=e-r;o+=n*n,a+=u*u})),1-o/a}function Nu(t,e,n){let r=0,i=0,o=0,a=0,u=0;Tu(t,e,n,((t,e)=>{++u,r+=(t-r)/u,i+=(e-i)/u,o+=(t*e-o)/u,a+=(t*t-a)/u}));const s=Su(r,i,o,a),l=t=>s[0]+s[1]*t;return{coef:s,predict:l,rSquared:Bu(t,e,n,i,l)}}function zu(t,e,n){let r=0,i=0,o=0,a=0,u=0;Tu(t,e,n,((t,e)=>{++u,t=Math.log(t),r+=(t-r)/u,i+=(e-i)/u,o+=(t*e-o)/u,a+=(t*t-a)/u}));const s=Su(r,i,o,a),l=t=>s[0]+s[1]*Math.log(t);return{coef:s,predict:l,rSquared:Bu(t,e,n,i,l)}}function Ou(t,e,n){const[r,i,o,a]=$u(t,e,n);let u,s,l,c=0,f=0,h=0,d=0,p=0;Tu(t,e,n,((t,e)=>{u=r[p++],s=Math.log(e),l=u*e,c+=(e*s-c)/p,f+=(l-f)/p,h+=(l*s-h)/p,d+=(u*l-d)/p}));const[g,m]=Su(f/a,c/a,h/a,d/a),y=t=>Math.exp(g+m*(t-o));return{coef:[Math.exp(g-m*o),m],predict:y,rSquared:Bu(t,e,n,a,y)}}function Ru(t,e,n){let r=0,i=0,o=0,a=0,u=0,s=0;Tu(t,e,n,((t,e)=>{const n=Math.log(t),l=Math.log(e);++s,r+=(n-r)/s,i+=(l-i)/s,o+=(n*l-o)/s,a+=(n*n-a)/s,u+=(e-u)/s}));const l=Su(r,i,o,a),c=t=>l[0]*Math.pow(t,l[1]);return l[0]=Math.exp(l[0]),{coef:l,predict:c,rSquared:Bu(t,e,n,u,c)}}function Uu(t,e,n){const[r,i,o,a]=$u(t,e,n),u=r.length;let s,l,c,f,h=0,d=0,p=0,g=0,m=0;for(s=0;s<u;)l=r[s],c=i[s++],f=l*l,h+=(f-h)/s,d+=(f*l-d)/s,p+=(f*f-p)/s,g+=(l*c-g)/s,m+=(f*c-m)/s;const y=p-h*h,v=h*y-d*d,_=(m*h-g*d)/v,x=(g*y-m*d)/v,b=-_*h,w=t=>_*(t-=o)*t+x*t+b+a;return{coef:[b-x*o+_*o*o+a,x-2*_*o,_],predict:w,rSquared:Bu(t,e,n,a,w)}}function Lu(t,e,n,r){if(0===r)return Fu(t,e,n);if(1===r)return Nu(t,e,n);if(2===r)return Uu(t,e,n);const[i,o,a,u]=$u(t,e,n),s=i.length,l=[],c=[],f=r+1;let h,d,p,g,m;for(h=0;h<f;++h){for(p=0,g=0;p<s;++p)g+=Math.pow(i[p],h)*o[p];for(l.push(g),m=new Float64Array(f),d=0;d<f;++d){for(p=0,g=0;p<s;++p)g+=Math.pow(i[p],h+d);m[d]=g}c.push(m)}c.push(l);const y=function(t){const e=t.length-1,n=[];let r,i,o,a,u;for(r=0;r<e;++r){for(a=r,i=r+1;i<e;++i)Math.abs(t[r][i])>Math.abs(t[r][a])&&(a=i);for(o=r;o<e+1;++o)u=t[o][r],t[o][r]=t[o][a],t[o][a]=u;for(i=r+1;i<e;++i)for(o=e;o>=r;o--)t[o][i]-=t[o][r]*t[r][i]/t[r][r]}for(i=e-1;i>=0;--i){for(u=0,o=i+1;o<e;++o)u+=t[o][i]*n[o];n[i]=(t[e][i]-u)/t[i][i]}return n}(c),v=t=>{t-=a;let e=u+y[0]+y[1]*t+y[2]*t*t;for(h=3;h<f;++h)e+=y[h]*Math.pow(t,h);return e};return{coef:qu(f,y,-a,u),predict:v,rSquared:Bu(t,e,n,u,v)}}function qu(t,e,n,r){const i=Array(t);let o,a,u,s;for(o=0;o<t;++o)i[o]=0;for(o=t-1;o>=0;--o)for(u=e[o],s=1,i[o]+=u,a=1;a<=o;++a)s*=(o+1-a)/a,i[o-a]+=u*Math.pow(n,a)*s;return i[0]+=r,i}const Pu=2,ju=1e-12;function Iu(t,e,n,r){const[i,o,a,u]=$u(t,e,n,!0),s=i.length,l=Math.max(2,~~(r*s)),c=new Float64Array(s),f=new Float64Array(s),h=new Float64Array(s).fill(1);for(let t=-1;++t<=Pu;){const e=[0,l-1];for(let t=0;t<s;++t){const n=i[t],r=e[0],a=e[1],u=n-i[r]>i[a]-n?r:a;let s=0,l=0,d=0,p=0,g=0;const m=1/Math.abs(i[u]-n||1);for(let t=r;t<=a;++t){const e=i[t],r=o[t],a=Wu(Math.abs(n-e)*m)*h[t],u=e*a;s+=a,l+=u,d+=r*a,p+=r*u,g+=e*u}const[y,v]=Su(l/s,d/s,p/s,g/s);c[t]=y+v*n,f[t]=Math.abs(o[t]-c[t]),Hu(i,t+1,e)}if(t===Pu)break;const n=Se(f);if(Math.abs(n)<ju)break;for(let t,e,r=0;r<s;++r)t=f[r]/(6*n),h[r]=t>=1?ju:(e=1-t*t)*e}return function(t,e,n,r){const i=t.length,o=[];let a,u=0,s=0,l=[];for(;u<i;++u)a=t[u]+n,l[0]===a?l[1]+=(e[u]-l[1])/++s:(s=0,l[1]+=r,l=[a,e[u]],o.push(l));return l[1]+=r,o}(i,c,a,u)}function Wu(t){return(t=1-t*t*t)*t*t}function Hu(t,e,n){const r=t[e];let i=n[0],o=n[1]+1;if(!(o>=t.length))for(;e>i&&t[o]-r<=r-t[i];)n[0]=++i,n[1]=o,++o}const Yu=.5*Math.PI/180;function Gu(t,e,n,r){n=n||25,r=Math.max(n,r||200);const i=e=>[e,t(e)],o=e[0],a=e[1],u=a-o,s=u/r,l=[i(o)],c=[];if(n===r){for(let t=1;t<r;++t)l.push(i(o+t/n*u));return l.push(i(a)),l}c.push(i(a));for(let t=n;--t>0;)c.push(i(o+t/n*u));let f=l[0],h=c[c.length-1];const d=1/u,p=function(t,e){let n=t,r=t;const i=e.length;for(let t=0;t<i;++t){const i=e[t][1];i<n&&(n=i),i>r&&(r=i)}return 1/(r-n)}(f[1],c);for(;h;){const t=i((f[0]+h[0])/2);t[0]-f[0]>=s&&Vu(f,t,h,d,p)>Yu?c.push(t):(f=h,l.push(h),c.pop()),h=c[c.length-1]}return l}function Vu(t,e,n,r,i){const o=Math.atan2(i*(n[1]-t[1]),r*(n[0]-t[0])),a=Math.atan2(i*(e[1]-t[1]),r*(e[0]-t[0]));return Math.abs(o-a)}function Xu(t){return t&&t.length?1===t.length?t[0]:(e=t,t=>{const n=e.length;let r=1,i=String(e[0](t));for(;r<n;++r)i+="|"+e[r](t);return i}):function(){return""};var e}function Ju(t,e,n){return n||t+(e?"_"+e:"")}const Zu=()=>{},Qu={init:Zu,add:Zu,rem:Zu,idx:0},Ku={values:{init:t=>t.cell.store=!0,value:t=>t.cell.data.values(),idx:-1},count:{value:t=>t.cell.num},__count__:{value:t=>t.missing+t.valid},missing:{value:t=>t.missing},valid:{value:t=>t.valid},sum:{init:t=>t.sum=0,value:t=>t.sum,add:(t,e)=>t.sum+=+e,rem:(t,e)=>t.sum-=e},product:{init:t=>t.product=1,value:t=>t.valid?t.product:void 0,add:(t,e)=>t.product*=e,rem:(t,e)=>t.product/=e},mean:{init:t=>t.mean=0,value:t=>t.valid?t.mean:void 0,add:(t,e)=>(t.mean_d=e-t.mean,t.mean+=t.mean_d/t.valid),rem:(t,e)=>(t.mean_d=e-t.mean,t.mean-=t.valid?t.mean_d/t.valid:t.mean)},average:{value:t=>t.valid?t.mean:void 0,req:["mean"],idx:1},variance:{init:t=>t.dev=0,value:t=>t.valid>1?t.dev/(t.valid-1):void 0,add:(t,e)=>t.dev+=t.mean_d*(e-t.mean),rem:(t,e)=>t.dev-=t.mean_d*(e-t.mean),req:["mean"],idx:1},variancep:{value:t=>t.valid>1?t.dev/t.valid:void 0,req:["variance"],idx:2},stdev:{value:t=>t.valid>1?Math.sqrt(t.dev/(t.valid-1)):void 0,req:["variance"],idx:2},stdevp:{value:t=>t.valid>1?Math.sqrt(t.dev/t.valid):void 0,req:["variance"],idx:2},stderr:{value:t=>t.valid>1?Math.sqrt(t.dev/(t.valid*(t.valid-1))):void 0,req:["variance"],idx:2},distinct:{value:t=>t.cell.data.distinct(t.get),req:["values"],idx:3},ci0:{value:t=>t.cell.data.ci0(t.get),req:["values"],idx:3},ci1:{value:t=>t.cell.data.ci1(t.get),req:["values"],idx:3},median:{value:t=>t.cell.data.q2(t.get),req:["values"],idx:3},q1:{value:t=>t.cell.data.q1(t.get),req:["values"],idx:3},q3:{value:t=>t.cell.data.q3(t.get),req:["values"],idx:3},min:{init:t=>t.min=void 0,value:t=>t.min=Number.isNaN(t.min)?t.cell.data.min(t.get):t.min,add:(t,e)=>{(e<t.min||void 0===t.min)&&(t.min=e)},rem:(t,e)=>{e<=t.min&&(t.min=NaN)},req:["values"],idx:4},max:{init:t=>t.max=void 0,value:t=>t.max=Number.isNaN(t.max)?t.cell.data.max(t.get):t.max,add:(t,e)=>{(e>t.max||void 0===t.max)&&(t.max=e)},rem:(t,e)=>{e>=t.max&&(t.max=NaN)},req:["values"],idx:4},argmin:{init:t=>t.argmin=void 0,value:t=>t.argmin||t.cell.data.argmin(t.get),add:(t,e,n)=>{e<t.min&&(t.argmin=n)},rem:(t,e)=>{e<=t.min&&(t.argmin=void 0)},req:["min","values"],idx:3},argmax:{init:t=>t.argmax=void 0,value:t=>t.argmax||t.cell.data.argmax(t.get),add:(t,e,n)=>{e>t.max&&(t.argmax=n)},rem:(t,e)=>{e>=t.max&&(t.argmax=void 0)},req:["max","values"],idx:3}},ts=Object.keys(Ku).filter((t=>"__count__"!==t));function es(t,e){return Ku[t](e)}function ns(t,e){return t.idx-e.idx}function rs(){this.valid=0,this.missing=0,this._ops.forEach((t=>t.init(this)))}function is(t,e){null!=t&&""!==t?t==t&&(++this.valid,this._ops.forEach((n=>n.add(this,t,e)))):++this.missing}function os(t,e){null!=t&&""!==t?t==t&&(--this.valid,this._ops.forEach((n=>n.rem(this,t,e)))):--this.missing}function as(t){return this._out.forEach((e=>t[e.out]=e.value(this))),t}function us(t,e){const n=e||f,r=function(t){const e={};t.forEach((t=>e[t.name]=t));const n=t=>{t.req&&t.req.forEach((t=>{e[t]||n(e[t]=Ku[t]())}))};return t.forEach(n),Object.values(e).sort(ns)}(t),i=t.slice().sort(ns);function o(t){this._ops=r,this._out=i,this.cell=t,this.init()}return o.prototype.init=rs,o.prototype.add=is,o.prototype.rem=os,o.prototype.set=as,o.prototype.get=n,o.fields=t.map((t=>t.out)),o}function ss(t){this._key=t?l(t):_a,this.reset()}[...ts,"__count__"].forEach((t=>{Ku[t]=function(t,e){return n=>ot({name:t,out:n||t},Qu,e)}(t,Ku[t])}));const ls=ss.prototype;function cs(t){Qa.call(this,null,t),this._adds=[],this._mods=[],this._alen=0,this._mlen=0,this._drop=!0,this._cross=!1,this._dims=[],this._dnames=[],this._measures=[],this._countOnly=!1,this._counts=null,this._prev=null,this._inputs=null,this._outputs=null}ls.reset=function(){this._add=[],this._rem=[],this._ext=null,this._get=null,this._q=null},ls.add=function(t){this._add.push(t)},ls.rem=function(t){this._rem.push(t)},ls.values=function(){if(this._get=null,0===this._rem.length)return this._add;const t=this._add,e=this._rem,n=this._key,r=t.length,i=e.length,o=Array(r-i),a={};let u,s,l;for(u=0;u<i;++u)a[n(e[u])]=1;for(u=0,s=0;u<r;++u)a[n(l=t[u])]?a[n(l)]=0:o[s++]=l;return this._rem=[],this._add=o},ls.distinct=function(t){const e=this.values(),n={};let r,i=e.length,o=0;for(;--i>=0;)r=t(e[i])+"",lt(n,r)||(n[r]=1,++o);return o},ls.extent=function(t){if(this._get!==t||!this._ext){const e=this.values(),n=ut(e,t);this._ext=[e[n[0]],e[n[1]]],this._get=t}return this._ext},ls.argmin=function(t){return this.extent(t)[0]||{}},ls.argmax=function(t){return this.extent(t)[1]||{}},ls.min=function(t){const e=this.extent(t)[0];return null!=e?t(e):void 0},ls.max=function(t){const e=this.extent(t)[1];return null!=e?t(e):void 0},ls.quartile=function(t){return this._get===t&&this._q||(this._q=iu(this.values(),t),this._get=t),this._q},ls.q1=function(t){return this.quartile(t)[0]},ls.q2=function(t){return this.quartile(t)[1]},ls.q3=function(t){return this.quartile(t)[2]},ls.ci=function(t){return this._get===t&&this._ci||(this._ci=uu(this.values(),1e3,.05,t),this._get=t),this._ci},ls.ci0=function(t){return this.ci(t)[0]},ls.ci1=function(t){return this.ci(t)[1]},cs.Definition={type:"Aggregate",metadata:{generates:!0,changes:!0},params:[{name:"groupby",type:"field",array:!0},{name:"ops",type:"enum",array:!0,values:ts},{name:"fields",type:"field",null:!0,array:!0},{name:"as",type:"string",null:!0,array:!0},{name:"drop",type:"boolean",default:!0},{name:"cross",type:"boolean",default:!1},{name:"key",type:"field"}]},dt(cs,Qa,{transform(t,e){const n=this,r=e.fork(e.NO_SOURCE|e.NO_FIELDS),i=t.modified();return n.stamp=r.stamp,n.value&&(i||e.modified(n._inputs,!0))?(n._prev=n.value,n.value=i?n.init(t):Object.create(null),e.visit(e.SOURCE,(t=>n.add(t)))):(n.value=n.value||n.init(t),e.visit(e.REM,(t=>n.rem(t))),e.visit(e.ADD,(t=>n.add(t)))),r.modifies(n._outputs),n._drop=!1!==t.drop,t.cross&&n._dims.length>1&&(n._drop=!1,n.cross()),e.clean()&&n._drop&&r.clean(!0).runAfter((()=>this.clean())),n.changes(r)},cross(){const t=this,e=t.value,n=t._dnames,r=n.map((()=>({}))),i=n.length;function o(t){let e,o,a,u;for(e in t)for(a=t[e].tuple,o=0;o<i;++o)r[o][u=a[n[o]]]=u}o(t._prev),o(e),function o(a,u,s){const l=n[s],c=r[s++];for(const n in c){const r=a?a+"|"+n:n;u[l]=c[n],s<i?o(r,u,s):e[r]||t.cell(r,u)}}("",{},0)},init(t){const e=this._inputs=[],i=this._outputs=[],o={};function a(t){const n=V(r(t)),i=n.length;let a,u=0;for(;u<i;++u)o[a=n[u]]||(o[a]=1,e.push(a))}this._dims=V(t.groupby),this._dnames=this._dims.map((t=>{const e=n(t);return a(t),i.push(e),e})),this.cellkey=t.key?t.key:Xu(this._dims),this._countOnly=!0,this._counts=[],this._measures=[];const s=t.fields||[null],l=t.ops||["count"],c=t.as||[],f=s.length,h={};let d,p,g,m,y,v;for(f!==l.length&&u("Unmatched number of fields and aggregate ops."),v=0;v<f;++v)d=s[v],p=l[v],null==d&&"count"!==p&&u("Null aggregate field specified."),m=n(d),y=Ju(p,m,c[v]),i.push(y),"count"!==p?(g=h[m],g||(a(d),g=h[m]=[],g.field=d,this._measures.push(g)),"count"!==p&&(this._countOnly=!1),g.push(es(p,y))):this._counts.push(y);return this._measures=this._measures.map((t=>us(t,t.field))),Object.create(null)},cellkey:Xu(),cell(t,e){let n=this.value[t];return n?0===n.num&&this._drop&&n.stamp<this.stamp?(n.stamp=this.stamp,this._adds[this._alen++]=n):n.stamp<this.stamp&&(n.stamp=this.stamp,this._mods[this._mlen++]=n):(n=this.value[t]=this.newcell(t,e),this._adds[this._alen++]=n),n},newcell(t,e){const n={key:t,num:0,agg:null,tuple:this.newtuple(e,this._prev&&this._prev[t]),stamp:this.stamp,store:!1};if(!this._countOnly){const t=this._measures,e=t.length;n.agg=Array(e);for(let r=0;r<e;++r)n.agg[r]=new t[r](n)}return n.store&&(n.data=new ss),n},newtuple(t,e){const n=this._dnames,r=this._dims,i=r.length,o={};for(let e=0;e<i;++e)o[n[e]]=r[e](t);return e?Aa(e.tuple,o):ba(o)},clean(){const t=this.value;for(const e in t)0===t[e].num&&delete t[e]},add(t){const e=this.cellkey(t),n=this.cell(e,t);if(n.num+=1,this._countOnly)return;n.store&&n.data.add(t);const r=n.agg;for(let e=0,n=r.length;e<n;++e)r[e].add(r[e].get(t),t)},rem(t){const e=this.cellkey(t),n=this.cell(e,t);if(n.num-=1,this._countOnly)return;n.store&&n.data.rem(t);const r=n.agg;for(let e=0,n=r.length;e<n;++e)r[e].rem(r[e].get(t),t)},celltuple(t){const e=t.tuple,n=this._counts;t.store&&t.data.values();for(let r=0,i=n.length;r<i;++r)e[n[r]]=t.num;if(!this._countOnly){const n=t.agg;for(let t=0,r=n.length;t<r;++t)n[t].set(e)}return e},changes(t){const e=this._adds,n=this._mods,r=this._prev,i=this._drop,o=t.add,a=t.rem,u=t.mod;let s,l,c,f;if(r)for(l in r)s=r[l],i&&!s.num||a.push(s.tuple);for(c=0,f=this._alen;c<f;++c)o.push(this.celltuple(e[c])),e[c]=null;for(c=0,f=this._mlen;c<f;++c)s=n[c],(0===s.num&&i?a:u).push(this.celltuple(s)),n[c]=null;return this._alen=this._mlen=0,this._prev=null,t}});function fs(t){Qa.call(this,null,t)}function hs(t,e,n){const r=t;let i=e||[],o=n||[],a={},u=0;return{add:t=>o.push(t),remove:t=>a[r(t)]=++u,size:()=>i.length,data:(t,e)=>(u&&(i=i.filter((t=>!a[r(t)])),a={},u=0),e&&t&&i.sort(t),o.length&&(i=t?Mt(t,i,o.sort(t)):i.concat(o),o=[]),i)}}function ds(t){Qa.call(this,[],t)}function ps(t){Ta.call(this,null,gs,t)}function gs(t){return this.value&&!t.modified()?this.value:Q(t.fields,t.orders)}function ms(t){Qa.call(this,null,t)}function ys(t){Qa.call(this,null,t)}fs.Definition={type:"Bin",metadata:{modifies:!0},params:[{name:"field",type:"field",required:!0},{name:"interval",type:"boolean",default:!0},{name:"anchor",type:"number"},{name:"maxbins",type:"number",default:20},{name:"base",type:"number",default:10},{name:"divide",type:"number",array:!0,default:[5,2]},{name:"extent",type:"number",array:!0,length:2,required:!0},{name:"span",type:"number"},{name:"step",type:"number"},{name:"steps",type:"number",array:!0},{name:"minstep",type:"number",default:0},{name:"nice",type:"boolean",default:!0},{name:"name",type:"string"},{name:"as",type:"string",array:!0,length:2,default:["bin0","bin1"]}]},dt(fs,Qa,{transform(t,e){const n=!1!==t.interval,i=this._bins(t),o=i.start,a=i.step,u=t.as||["bin0","bin1"],s=u[0],l=u[1];let c;return c=t.modified()?(e=e.reflow(!0)).SOURCE:e.modified(r(t.field))?e.ADD_MOD:e.ADD,e.visit(c,n?t=>{const e=i(t);t[s]=e,t[l]=null==e?null:o+a*(1+(e-o)/a)}:t=>t[s]=i(t)),e.modifies(n?u:s)},_bins(t){if(this.value&&!t.modified())return this.value;const i=t.field,o=au(t),a=o.step;let u,s,l=o.start,c=l+Math.ceil((o.stop-l)/a)*a;null!=(u=t.anchor)&&(s=u-(l+a*Math.floor((u-l)/a)),l+=s,c+=s);const f=function(t){let e=S(i(t));return null==e?null:e<l?-1/0:e>c?1/0:(e=Math.max(l,Math.min(e,c-a)),l+a*Math.floor(1e-14+(e-l)/a))};return f.start=l,f.stop=o.stop,f.step=a,this.value=e(f,r(i),t.name||"bin_"+n(i))}}),ds.Definition={type:"Collect",metadata:{source:!0},params:[{name:"sort",type:"compare"}]},dt(ds,Qa,{transform(t,e){const n=e.fork(e.ALL),r=hs(_a,this.value,n.materialize(n.ADD).add),i=t.sort,o=e.changed()||i&&(t.modified("sort")||e.modified(i.fields));return n.visit(n.REM,r.remove),this.modified(o),this.value=n.source=r.data(Ma(i),o),e.source&&e.source.root&&(this.value.root=e.source.root),n}}),dt(ps,Ta),ms.Definition={type:"CountPattern",metadata:{generates:!0,changes:!0},params:[{name:"field",type:"field",required:!0},{name:"case",type:"enum",values:["upper","lower","mixed"],default:"mixed"},{name:"pattern",type:"string",default:'[\\w"]+'},{name:"stopwords",type:"string",default:""},{name:"as",type:"string",array:!0,length:2,default:["text","count"]}]},dt(ms,Qa,{transform(t,e){const n=e=>n=>{for(var r,i=function(t,e,n){switch(e){case"upper":t=t.toUpperCase();break;case"lower":t=t.toLowerCase()}return t.match(n)}(u(n),t.case,o)||[],s=0,l=i.length;s<l;++s)a.test(r=i[s])||e(r)},r=this._parameterCheck(t,e),i=this._counts,o=this._match,a=this._stop,u=t.field,s=t.as||["text","count"],l=n((t=>i[t]=1+(i[t]||0))),c=n((t=>i[t]-=1));return r?e.visit(e.SOURCE,l):(e.visit(e.ADD,l),e.visit(e.REM,c)),this._finish(e,s)},_parameterCheck(t,e){let n=!1;return!t.modified("stopwords")&&this._stop||(this._stop=new RegExp("^"+(t.stopwords||"")+"$","i"),n=!0),!t.modified("pattern")&&this._match||(this._match=new RegExp(t.pattern||"[\\w']+","g"),n=!0),(t.modified("field")||e.modified(t.field.fields))&&(n=!0),n&&(this._counts={}),n},_finish(t,e){const n=this._counts,r=this._tuples||(this._tuples={}),i=e[0],o=e[1],a=t.fork(t.NO_SOURCE|t.NO_FIELDS);let u,s,l;for(u in n)s=r[u],l=n[u]||0,!s&&l?(r[u]=s=ba({}),s[i]=u,s[o]=l,a.add.push(s)):0===l?(s&&a.rem.push(s),n[u]=null,r[u]=null):s[o]!==l&&(s[o]=l,a.mod.push(s));return a.modifies(e)}}),ys.Definition={type:"Cross",metadata:{generates:!0},params:[{name:"filter",type:"expr"},{name:"as",type:"string",array:!0,length:2,default:["a","b"]}]},dt(ys,Qa,{transform(t,e){const n=e.fork(e.NO_SOURCE),r=t.as||["a","b"],i=r[0],o=r[1],a=!this.value||e.changed(e.ADD_REM)||t.modified("as")||t.modified("filter");let u=this.value;return a?(u&&(n.rem=u),u=e.materialize(e.SOURCE).source,n.add=this.value=function(t,e,n,r){for(var i,o,a=[],u={},s=t.length,l=0;l<s;++l)for(u[e]=o=t[l],i=0;i<s;++i)u[n]=t[i],r(u)&&(a.push(ba(u)),(u={})[e]=o);return a}(u,i,o,t.filter||p)):n.mod=u,n.source=this.value,n.modifies(r)}});const vs={kde:yu,mixture:ku,normal:mu,lognormal:wu,uniform:Cu},_s="distributions",xs="function",bs="field";function ws(t,e){const n=t[xs];lt(vs,n)||u("Unknown distribution function: "+n);const r=vs[n]();for(const n in t)n===bs?r.data((t.from||e()).map(t[n])):n===_s?r[n](t[n].map((t=>ws(t,e)))):typeof r[n]===xs&&r[n](t[n]);return r}function ks(t){Qa.call(this,null,t)}const As=[{key:{function:"normal"},params:[{name:"mean",type:"number",default:0},{name:"stdev",type:"number",default:1}]},{key:{function:"lognormal"},params:[{name:"mean",type:"number",default:0},{name:"stdev",type:"number",default:1}]},{key:{function:"uniform"},params:[{name:"min",type:"number",default:0},{name:"max",type:"number",default:1}]},{key:{function:"kde"},params:[{name:"field",type:"field",required:!0},{name:"from",type:"data"},{name:"bandwidth",type:"number",default:0}]}],Ms={key:{function:"mixture"},params:[{name:"distributions",type:"param",array:!0,params:As},{name:"weights",type:"number",array:!0}]};function Es(t,e){return t?t.map(((t,r)=>e[r]||n(t))):null}function Ds(t,e,n){const r=[],i=t=>t(s);let o,a,u,s,l,c;if(null==e)r.push(t.map(n));else for(o={},a=0,u=t.length;a<u;++a)s=t[a],l=e.map(i),c=o[l],c||(o[l]=c=[],c.dims=l,r.push(c)),c.push(n(s));return r}ks.Definition={type:"Density",metadata:{generates:!0},params:[{name:"extent",type:"number",array:!0,length:2},{name:"steps",type:"number"},{name:"minsteps",type:"number",default:25},{name:"maxsteps",type:"number",default:200},{name:"method",type:"string",default:"pdf",values:["pdf","cdf"]},{name:"distribution",type:"param",params:As.concat(Ms)},{name:"as",type:"string",array:!0,default:["value","density"]}]},dt(ks,Qa,{transform(t,e){const n=e.fork(e.NO_SOURCE|e.NO_FIELDS);if(!this.value||e.changed()||t.modified()){const r=ws(t.distribution,function(t){return()=>t.materialize(t.SOURCE).source}(e)),i=t.steps||t.minsteps||25,o=t.steps||t.maxsteps||200;let a=t.method||"pdf";"pdf"!==a&&"cdf"!==a&&u("Invalid density method: "+a),t.extent||r.data||u("Missing density extent parameter."),a=r[a];const s=t.as||["value","density"],l=Gu(a,t.extent||at(r.data()),i,o).map((t=>{const e={};return e[s[0]]=t[0],e[s[1]]=t[1],ba(e)}));this.value&&(n.rem=this.value),this.value=n.add=n.source=l}return n}});function Cs(t){Qa.call(this,null,t)}Cs.Definition={type:"DotBin",metadata:{modifies:!0},params:[{name:"field",type:"field",required:!0},{name:"groupby",type:"field",array:!0},{name:"step",type:"number"},{name:"smooth",type:"boolean",default:!1},{name:"as",type:"string",default:"bin"}]};function Fs(t){Ta.call(this,null,Ss,t),this.modified(!0)}function Ss(t){const i=t.expr;return this.value&&!t.modified("expr")?this.value:e((e=>i(e,t)),r(i),n(i))}function $s(t){Qa.call(this,[void 0,void 0],t)}function Ts(t,e){Ta.call(this,t),this.parent=e,this.count=0}function Bs(t){Qa.call(this,{},t),this._keys=ft();const e=this._targets=[];e.active=0,e.forEach=t=>{for(let n=0,r=e.active;n<r;++n)t(e[n],n,e)}}function Ns(t){Ta.call(this,null,zs,t)}function zs(t){return this.value&&!t.modified()?this.value:k(t.name)?V(t.name).map((t=>l(t))):l(t.name,t.as)}function Os(t){Qa.call(this,ft(),t)}function Rs(t){Qa.call(this,[],t)}function Us(t){Qa.call(this,[],t)}function Ls(t){Qa.call(this,null,t)}function qs(t){Qa.call(this,[],t)}dt(Cs,Qa,{transform(t,e){if(this.value&&!t.modified()&&!e.changed())return e;const n=e.materialize(e.SOURCE).source,r=Ds(e.source,t.groupby,f),i=t.smooth||!1,o=t.field,a=t.step||((t,e)=>Ct(at(t,e))/30)(n,o),u=Ma(((t,e)=>o(t)-o(e))),s=t.as||"bin",l=r.length;let c,h=1/0,d=-1/0,p=0;for(;p<l;++p){const t=r[p].sort(u);c=-1;for(const e of su(t,a,i,o))e<h&&(h=e),e>d&&(d=e),t[++c][s]=e}return this.value={start:h,stop:d,step:a},e.reflow(!0).modifies(s)}}),dt(Fs,Ta),$s.Definition={type:"Extent",metadata:{},params:[{name:"field",type:"field",required:!0}]},dt($s,Qa,{transform(t,e){const r=this.value,i=t.field,o=e.changed()||e.modified(i.fields)||t.modified("field");let a=r[0],u=r[1];if((o||null==a)&&(a=1/0,u=-1/0),e.visit(o?e.SOURCE:e.ADD,(t=>{const e=S(i(t));null!=e&&(e<a&&(a=e),e>u&&(u=e))})),!Number.isFinite(a)||!Number.isFinite(u)){let t=n(i);t&&(t=` for field "${t}"`),e.dataflow.warn(`Infinite extent${t}: [${a}, ${u}]`),a=u=void 0}this.value=[a,u]}}),dt(Ts,Ta,{connect(t){return this.detachSubflow=t.detachSubflow,this.targets().add(t),t.source=this},add(t){this.count+=1,this.value.add.push(t)},rem(t){this.count-=1,this.value.rem.push(t)},mod(t){this.value.mod.push(t)},init(t){this.value.init(t,t.NO_SOURCE)},evaluate(){return this.value}}),dt(Bs,Qa,{activate(t){this._targets[this._targets.active++]=t},subflow(t,e,n,r){const i=this.value;let o,a,u=lt(i,t)&&i[t];return u?u.value.stamp<n.stamp&&(u.init(n),this.activate(u)):(a=r||(a=this._group[t])&&a.tuple,o=n.dataflow,u=new Ts(n.fork(n.NO_SOURCE),this),o.add(u).connect(e(o,t,a)),i[t]=u,this.activate(u)),u},clean(){const t=this.value;let e=0;for(const n in t)if(0===t[n].count){const r=t[n].detachSubflow;r&&r(),delete t[n],++e}if(e){const t=this._targets.filter((t=>t&&t.count>0));this.initTargets(t)}},initTargets(t){const e=this._targets,n=e.length,r=t?t.length:0;let i=0;for(;i<r;++i)e[i]=t[i];for(;i<n&&null!=e[i];++i)e[i]=null;e.active=r},transform(t,e){const n=e.dataflow,r=t.key,i=t.subflow,o=this._keys,a=t.modified("key"),u=t=>this.subflow(t,i,e);return this._group=t.group||{},this.initTargets(),e.visit(e.REM,(t=>{const e=_a(t),n=o.get(e);void 0!==n&&(o.delete(e),u(n).rem(t))})),e.visit(e.ADD,(t=>{const e=r(t);o.set(_a(t),e),u(e).add(t)})),a||e.modified(r.fields)?e.visit(e.MOD,(t=>{const e=_a(t),n=o.get(e),i=r(t);n===i?u(i).mod(t):(o.set(e,i),u(n).rem(t),u(i).add(t))})):e.changed(e.MOD)&&e.visit(e.MOD,(t=>{u(o.get(_a(t))).mod(t)})),a&&e.visit(e.REFLOW,(t=>{const e=_a(t),n=o.get(e),i=r(t);n!==i&&(o.set(e,i),u(n).rem(t),u(i).add(t))})),e.clean()?n.runAfter((()=>{this.clean(),o.clean()})):o.empty>n.cleanThreshold&&n.runAfter(o.clean),e}}),dt(Ns,Ta),Os.Definition={type:"Filter",metadata:{changes:!0},params:[{name:"expr",type:"expr",required:!0}]},dt(Os,Qa,{transform(t,e){const n=e.dataflow,r=this.value,i=e.fork(),o=i.add,a=i.rem,u=i.mod,s=t.expr;let l=!0;function c(e){const n=_a(e),i=s(e,t),c=r.get(n);i&&c?(r.delete(n),o.push(e)):i||c?l&&i&&!c&&u.push(e):(r.set(n,1),a.push(e))}return e.visit(e.REM,(t=>{const e=_a(t);r.has(e)?r.delete(e):a.push(t)})),e.visit(e.ADD,(e=>{s(e,t)?o.push(e):r.set(_a(e),1)})),e.visit(e.MOD,c),t.modified()&&(l=!1,e.visit(e.REFLOW,c)),r.empty>n.cleanThreshold&&n.runAfter(r.clean),i}}),Rs.Definition={type:"Flatten",metadata:{generates:!0},params:[{name:"fields",type:"field",array:!0,required:!0},{name:"index",type:"string"},{name:"as",type:"string",array:!0}]},dt(Rs,Qa,{transform(t,e){const n=e.fork(e.NO_SOURCE),r=t.fields,i=Es(r,t.as||[]),o=t.index||null,a=i.length;return n.rem=this.value,e.visit(e.SOURCE,(t=>{const e=r.map((e=>e(t))),u=e.reduce(((t,e)=>Math.max(t,e.length)),0);let s,l,c,f=0;for(;f<u;++f){for(l=wa(t),s=0;s<a;++s)l[i[s]]=null==(c=e[s][f])?null:c;o&&(l[o]=f),n.add.push(l)}})),this.value=n.source=n.add,o&&n.modifies(o),n.modifies(i)}}),Us.Definition={type:"Fold",metadata:{generates:!0},params:[{name:"fields",type:"field",array:!0,required:!0},{name:"as",type:"string",array:!0,length:2,default:["key","value"]}]},dt(Us,Qa,{transform(t,e){const r=e.fork(e.NO_SOURCE),i=t.fields,o=i.map(n),a=t.as||["key","value"],u=a[0],s=a[1],l=i.length;return r.rem=this.value,e.visit(e.SOURCE,(t=>{for(let e,n=0;n<l;++n)e=wa(t),e[u]=o[n],e[s]=i[n](t),r.add.push(e)})),this.value=r.source=r.add,r.modifies(a)}}),Ls.Definition={type:"Formula",metadata:{modifies:!0},params:[{name:"expr",type:"expr",required:!0},{name:"as",type:"string",required:!0},{name:"initonly",type:"boolean"}]},dt(Ls,Qa,{transform(t,e){const n=t.expr,r=t.as,i=t.modified(),o=t.initonly?e.ADD:i?e.SOURCE:e.modified(n.fields)||e.modified(r)?e.ADD_MOD:e.ADD;return i&&(e=e.materialize().reflow(!0)),t.initonly||e.modifies(r),e.visit(o,(e=>e[r]=n(e,t)))}}),dt(qs,Qa,{transform(t,e){const n=e.fork(e.ALL),r=t.generator;let i,o,a,u=this.value,s=t.size-u.length;if(s>0){for(i=[];--s>=0;)i.push(a=ba(r(t))),u.push(a);n.add=n.add.length?n.materialize(n.ADD).add.concat(i):i}else o=u.slice(0,-s),n.rem=n.rem.length?n.materialize(n.REM).rem.concat(o):o,u=u.slice(-s);return n.source=this.value=u,n}});const Ps={value:"value",median:Se,mean:function(t,e){let n=0,r=0;if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(++n,r+=e);else{let i=-1;for(let o of t)null!=(o=e(o,++i,t))&&(o=+o)>=o&&(++n,r+=o)}if(n)return r/n},min:Me,max:Ae},js=[];function Is(t){Qa.call(this,[],t)}function Ws(t){cs.call(this,t)}function Hs(t){Qa.call(this,null,t)}function Ys(t){Ta.call(this,null,Gs,t)}function Gs(t){return this.value&&!t.modified()?this.value:bt(t.fields,t.flat)}function Vs(t){Qa.call(this,[],t),this._pending=null}function Xs(t,e,n){n.forEach(ba);const r=e.fork(e.NO_FIELDS&e.NO_SOURCE);return r.rem=t.value,t.value=r.source=r.add=n,t._pending=null,r.rem.length&&r.clean(!0),r}function Js(t){Qa.call(this,{},t)}function Zs(t){Ta.call(this,null,Qs,t)}function Qs(t){if(this.value&&!t.modified())return this.value;const e=t.extents,n=e.length;let r,i,o=1/0,a=-1/0;for(r=0;r<n;++r)i=e[r],i[0]<o&&(o=i[0]),i[1]>a&&(a=i[1]);return[o,a]}function Ks(t){Ta.call(this,null,tl,t)}function tl(t){return this.value&&!t.modified()?this.value:t.values.reduce(((t,e)=>t.concat(e)),[])}function el(t){Qa.call(this,null,t)}function nl(t){cs.call(this,t)}function rl(t){Bs.call(this,t)}function il(t){Qa.call(this,null,t)}function ol(t){Qa.call(this,null,t)}function al(t){Qa.call(this,null,t)}Is.Definition={type:"Impute",metadata:{changes:!0},params:[{name:"field",type:"field",required:!0},{name:"key",type:"field",required:!0},{name:"keyvals",array:!0},{name:"groupby",type:"field",array:!0},{name:"method",type:"enum",default:"value",values:["value","mean","median","max","min"]},{name:"value",default:0}]},dt(Is,Qa,{transform(t,e){var r,i,o,a,s,l,c,f,h,d,p=e.fork(e.ALL),g=function(t){var e,n=t.method||Ps.value;if(null!=Ps[n])return n===Ps.value?(e=void 0!==t.value?t.value:0,()=>e):Ps[n];u("Unrecognized imputation method: "+n)}(t),m=function(t){const e=t.field;return t=>t?e(t):NaN}(t),y=n(t.field),v=n(t.key),_=(t.groupby||[]).map(n),x=function(t,e,n,r){var i,o,a,u,s,l,c,f,h=t=>t(f),d=[],p=r?r.slice():[],g={},m={};for(p.forEach(((t,e)=>g[t]=e+1)),u=0,c=t.length;u<c;++u)l=n(f=t[u]),s=g[l]||(g[l]=p.push(l)),(a=m[o=(i=e?e.map(h):js)+""])||(a=m[o]=[],d.push(a),a.values=i),a[s-1]=f;return d.domain=p,d}(e.source,t.groupby,t.key,t.keyvals),b=[],w=this.value,k=x.domain.length;for(s=0,f=x.length;s<f;++s)for(o=(r=x[s]).values,i=NaN,c=0;c<k;++c)if(null==r[c]){for(a=x.domain[c],d={_impute:!0},l=0,h=o.length;l<h;++l)d[_[l]]=o[l];d[v]=a,d[y]=Number.isNaN(i)?i=g(r,m):i,b.push(ba(d))}return b.length&&(p.add=p.materialize(p.ADD).add.concat(b)),w.length&&(p.rem=p.materialize(p.REM).rem.concat(w)),this.value=b,p}}),Ws.Definition={type:"JoinAggregate",metadata:{modifies:!0},params:[{name:"groupby",type:"field",array:!0},{name:"fields",type:"field",null:!0,array:!0},{name:"ops",type:"enum",array:!0,values:ts},{name:"as",type:"string",null:!0,array:!0},{name:"key",type:"field"}]},dt(Ws,cs,{transform(t,e){const n=this,r=t.modified();let i;return n.value&&(r||e.modified(n._inputs,!0))?(i=n.value=r?n.init(t):{},e.visit(e.SOURCE,(t=>n.add(t)))):(i=n.value=n.value||this.init(t),e.visit(e.REM,(t=>n.rem(t))),e.visit(e.ADD,(t=>n.add(t)))),n.changes(),e.visit(e.SOURCE,(t=>{ot(t,i[n.cellkey(t)].tuple)})),e.reflow(r).modifies(this._outputs)},changes(){const t=this._adds,e=this._mods;let n,r;for(n=0,r=this._alen;n<r;++n)this.celltuple(t[n]),t[n]=null;for(n=0,r=this._mlen;n<r;++n)this.celltuple(e[n]),e[n]=null;this._alen=this._mlen=0}}),Hs.Definition={type:"KDE",metadata:{generates:!0},params:[{name:"groupby",type:"field",array:!0},{name:"field",type:"field",required:!0},{name:"cumulative",type:"boolean",default:!1},{name:"counts",type:"boolean",default:!1},{name:"bandwidth",type:"number",default:0},{name:"extent",type:"number",array:!0,length:2},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"steps",type:"number"},{name:"minsteps",type:"number",default:25},{name:"maxsteps",type:"number",default:200},{name:"as",type:"string",array:!0,default:["value","density"]}]},dt(Hs,Qa,{transform(t,e){const r=e.fork(e.NO_SOURCE|e.NO_FIELDS);if(!this.value||e.changed()||t.modified()){const i=e.materialize(e.SOURCE).source,o=Ds(i,t.groupby,t.field),a=(t.groupby||[]).map(n),s=t.bandwidth,l=t.cumulative?"cdf":"pdf",c=t.as||["value","density"],f=[];let h=t.extent,d=t.steps||t.minsteps||25,p=t.steps||t.maxsteps||200;"pdf"!==l&&"cdf"!==l&&u("Invalid density method: "+l),"shared"===t.resolve&&(h||(h=at(i,t.field)),d=p=t.steps||p),o.forEach((e=>{const n=yu(e,s)[l],r=t.counts?e.length:1;Gu(n,h||at(e),d,p).forEach((t=>{const n={};for(let t=0;t<a.length;++t)n[a[t]]=e.dims[t];n[c[0]]=t[0],n[c[1]]=t[1]*r,f.push(ba(n))}))})),this.value&&(r.rem=this.value),this.value=r.add=r.source=f}return r}}),dt(Ys,Ta),dt(Vs,Qa,{transform(t,e){const n=e.dataflow;if(this._pending)return Xs(this,e,this._pending);if(function(t){return t.modified("async")&&!(t.modified("values")||t.modified("url")||t.modified("format"))}(t))return e.StopPropagation;if(t.values)return Xs(this,e,n.parse(t.values,t.format));if(t.async){const e=n.request(t.url,t.format).then((t=>(this._pending=V(t.data),t=>t.touch(this))));return{async:e}}return n.request(t.url,t.format).then((t=>Xs(this,e,V(t.data))))}}),Js.Definition={type:"Lookup",metadata:{modifies:!0},params:[{name:"index",type:"index",params:[{name:"from",type:"data",required:!0},{name:"key",type:"field",required:!0}]},{name:"values",type:"field",array:!0},{name:"fields",type:"field",array:!0,required:!0},{name:"as",type:"string",array:!0},{name:"default",default:null}]},dt(Js,Qa,{transform(t,e){const r=t.fields,i=t.index,o=t.values,a=null==t.default?null:t.default,s=t.modified(),l=r.length;let c,f,h,d=s?e.SOURCE:e.ADD,p=e,g=t.as;return o?(f=o.length,l>1&&!g&&u('Multi-field lookup requires explicit "as" parameter.'),g&&g.length!==l*f&&u('The "as" parameter has too few output field names.'),g=g||o.map(n),c=function(t){for(var e,n,u=0,s=0;u<l;++u)if(null==(n=i.get(r[u](t))))for(e=0;e<f;++e,++s)t[g[s]]=a;else for(e=0;e<f;++e,++s)t[g[s]]=o[e](n)}):(g||u("Missing output field names."),c=function(t){for(var e,n=0;n<l;++n)e=i.get(r[n](t)),t[g[n]]=null==e?a:e}),s?p=e.reflow(!0):(h=r.some((t=>e.modified(t.fields))),d|=h?e.MOD:0),e.visit(d,c),p.modifies(g)}}),dt(Zs,Ta),dt(Ks,Ta),dt(el,Qa,{transform(t,e){return this.modified(t.modified()),this.value=t,e.fork(e.NO_SOURCE|e.NO_FIELDS)}}),nl.Definition={type:"Pivot",metadata:{generates:!0,changes:!0},params:[{name:"groupby",type:"field",array:!0},{name:"field",type:"field",required:!0},{name:"value",type:"field",required:!0},{name:"op",type:"enum",values:ts,default:"sum"},{name:"limit",type:"number",default:0},{name:"key",type:"field"}]},dt(nl,cs,{_transform:cs.prototype.transform,transform(t,n){return this._transform(function(t,n){const i=t.field,o=t.value,a=("count"===t.op?"__count__":t.op)||"sum",u=r(i).concat(r(o)),s=function(t,e,n){const r={},i=[];return n.visit(n.SOURCE,(e=>{const n=t(e);r[n]||(r[n]=1,i.push(n))})),i.sort(K),e?i.slice(0,e):i}(i,t.limit||0,n);n.changed()&&t.set("__pivot__",null,null,!0);return{key:t.key,groupby:t.groupby,ops:s.map((()=>a)),fields:s.map((t=>function(t,n,r,i){return e((e=>n(e)===t?r(e):NaN),i,t+"")}(t,i,o,u))),as:s.map((t=>t+"")),modified:t.modified.bind(t)}}(t,n),n)}}),dt(rl,Bs,{transform(t,e){const n=t.subflow,i=t.field,o=t=>this.subflow(_a(t),n,e,t);return(t.modified("field")||i&&e.modified(r(i)))&&u("PreFacet does not support field modification."),this.initTargets(),i?(e.visit(e.MOD,(t=>{const e=o(t);i(t).forEach((t=>e.mod(t)))})),e.visit(e.ADD,(t=>{const e=o(t);i(t).forEach((t=>e.add(ba(t))))})),e.visit(e.REM,(t=>{const e=o(t);i(t).forEach((t=>e.rem(t)))}))):(e.visit(e.MOD,(t=>o(t).mod(t))),e.visit(e.ADD,(t=>o(t).add(t))),e.visit(e.REM,(t=>o(t).rem(t)))),e.clean()&&e.runAfter((()=>this.clean())),e}}),il.Definition={type:"Project",metadata:{generates:!0,changes:!0},params:[{name:"fields",type:"field",array:!0},{name:"as",type:"string",null:!0,array:!0}]},dt(il,Qa,{transform(t,e){const n=e.fork(e.NO_SOURCE),r=t.fields,i=Es(t.fields,t.as||[]),o=r?(t,e)=>function(t,e,n,r){for(let i=0,o=n.length;i<o;++i)e[r[i]]=n[i](t);return e}(t,e,r,i):ka;let a;return this.value?a=this.value:(e=e.addAll(),a=this.value={}),e.visit(e.REM,(t=>{const e=_a(t);n.rem.push(a[e]),a[e]=null})),e.visit(e.ADD,(t=>{const e=o(t,ba({}));a[_a(t)]=e,n.add.push(e)})),e.visit(e.MOD,(t=>{n.mod.push(o(t,a[_a(t)]))})),n}}),dt(ol,Qa,{transform(t,e){return this.value=t.value,t.modified("value")?e.fork(e.NO_SOURCE|e.NO_FIELDS):e.StopPropagation}}),al.Definition={type:"Quantile",metadata:{generates:!0,changes:!0},params:[{name:"groupby",type:"field",array:!0},{name:"field",type:"field",required:!0},{name:"probs",type:"number",array:!0},{name:"step",type:"number",default:.01},{name:"as",type:"string",array:!0,default:["prob","value"]}]};function ul(t){Qa.call(this,null,t)}function sl(t){Qa.call(this,[],t),this.count=0}function ll(t){Qa.call(this,null,t)}function cl(t){Qa.call(this,null,t),this.modified(!0)}function fl(t){Qa.call(this,null,t)}dt(al,Qa,{transform(t,e){const r=e.fork(e.NO_SOURCE|e.NO_FIELDS),i=t.as||["prob","value"];if(this.value&&!t.modified()&&!e.changed())return r.source=this.value,r;const o=Ds(e.materialize(e.SOURCE).source,t.groupby,t.field),a=(t.groupby||[]).map(n),u=[],s=t.step||.01,l=t.probs||Te(s/2,1-1e-14,s),c=l.length;return o.forEach((t=>{const e=ru(t,l);for(let n=0;n<c;++n){const r={};for(let e=0;e<a.length;++e)r[a[e]]=t.dims[e];r[i[0]]=l[n],r[i[1]]=e[n],u.push(ba(r))}})),this.value&&(r.rem=this.value),this.value=r.add=r.source=u,r}}),dt(ul,Qa,{transform(t,e){let n,r;return this.value?r=this.value:(n=e=e.addAll(),r=this.value={}),t.derive&&(n=e.fork(e.NO_SOURCE),e.visit(e.REM,(t=>{const e=_a(t);n.rem.push(r[e]),r[e]=null})),e.visit(e.ADD,(t=>{const e=wa(t);r[_a(t)]=e,n.add.push(e)})),e.visit(e.MOD,(t=>{const e=r[_a(t)];for(const r in t)e[r]=t[r],n.modifies(r);n.mod.push(e)}))),n}}),sl.Definition={type:"Sample",metadata:{},params:[{name:"size",type:"number",default:1e3}]},dt(sl,Qa,{transform(e,n){const r=n.fork(n.NO_SOURCE),i=e.modified("size"),o=e.size,a=this.value.reduce(((t,e)=>(t[_a(e)]=1,t)),{});let u=this.value,s=this.count,l=0;function c(e){let n,i;u.length<o?u.push(e):(i=~~((s+1)*t.random()),i<u.length&&i>=l&&(n=u[i],a[_a(n)]&&r.rem.push(n),u[i]=e)),++s}if(n.rem.length&&(n.visit(n.REM,(t=>{const e=_a(t);a[e]&&(a[e]=-1,r.rem.push(t)),--s})),u=u.filter((t=>-1!==a[_a(t)]))),(n.rem.length||i)&&u.length<o&&n.source&&(l=s=u.length,n.visit(n.SOURCE,(t=>{a[_a(t)]||c(t)})),l=-1),i&&u.length>o){const t=u.length-o;for(let e=0;e<t;++e)a[_a(u[e])]=-1,r.rem.push(u[e]);u=u.slice(t)}return n.mod.length&&n.visit(n.MOD,(t=>{a[_a(t)]&&r.mod.push(t)})),n.add.length&&n.visit(n.ADD,c),(n.add.length||l<0)&&(r.add=u.filter((t=>!a[_a(t)]))),this.count=s,this.value=r.source=u,r}}),ll.Definition={type:"Sequence",metadata:{generates:!0,changes:!0},params:[{name:"start",type:"number",required:!0},{name:"stop",type:"number",required:!0},{name:"step",type:"number",default:1},{name:"as",type:"string",default:"data"}]},dt(ll,Qa,{transform(t,e){if(this.value&&!t.modified())return;const n=e.materialize().fork(e.MOD),r=t.as||"data";return n.rem=this.value?e.rem.concat(this.value):e.rem,this.value=Te(t.start,t.stop,t.step||1).map((t=>{const e={};return e[r]=t,ba(e)})),n.add=e.add.concat(this.value),n}}),dt(cl,Qa,{transform(t,e){return this.value=e.source,e.changed()?e.fork(e.NO_SOURCE|e.NO_FIELDS):e.StopPropagation}});const hl=["unit0","unit1"];function dl(t){Qa.call(this,ft(),t)}function pl(t){Qa.call(this,null,t)}fl.Definition={type:"TimeUnit",metadata:{modifies:!0},params:[{name:"field",type:"field",required:!0},{name:"interval",type:"boolean",default:!0},{name:"units",type:"enum",values:er,array:!0},{name:"step",type:"number",default:1},{name:"maxbins",type:"number",default:40},{name:"extent",type:"date",array:!0},{name:"timezone",type:"enum",default:"local",values:["local","utc"]},{name:"as",type:"string",array:!0,length:2,default:hl}]},dt(fl,Qa,{transform(t,e){const n=t.field,i=!1!==t.interval,o="utc"===t.timezone,a=this._floor(t,e),u=(o?$r:Sr)(a.unit).offset,s=t.as||hl,l=s[0],c=s[1],f=a.step;let h=a.start||1/0,d=a.stop||-1/0,p=e.ADD;return(t.modified()||e.changed(e.REM)||e.modified(r(n)))&&(p=(e=e.reflow(!0)).SOURCE,h=1/0,d=-1/0),e.visit(p,(t=>{const e=n(t);let r,o;null==e?(t[l]=null,i&&(t[c]=null)):(t[l]=r=o=a(e),i&&(t[c]=o=u(r,f)),r<h&&(h=r),o>d&&(d=o))})),a.start=h,a.stop=d,e.modifies(i?s:l)},_floor(t,e){const n="utc"===t.timezone,{units:r,step:i}=t.units?{units:t.units,step:t.step||1}:Qr({extent:t.extent||at(e.materialize(e.SOURCE).source,t.field),maxbins:t.maxbins}),o=rr(r),a=this.value||{},u=(n?Dr:Ar)(o,i);return u.unit=F(o),u.units=o,u.step=i,u.start=a.start,u.stop=a.stop,this.value=u}}),dt(dl,Qa,{transform(t,e){const n=e.dataflow,r=t.field,i=this.value,o=t=>i.set(r(t),t);let a=!0;return t.modified("field")||e.modified(r.fields)?(i.clear(),e.visit(e.SOURCE,o)):e.changed()?(e.visit(e.REM,(t=>i.delete(r(t)))),e.visit(e.ADD,o)):a=!1,this.modified(a),i.empty>n.cleanThreshold&&n.runAfter(i.clean),e.fork()}}),dt(pl,Qa,{transform(t,e){(!this.value||t.modified("field")||t.modified("sort")||e.changed()||t.sort&&e.modified(t.sort.fields))&&(this.value=(t.sort?e.source.slice().sort(Ma(t.sort)):e.source).map(t.field))}});const gl={row_number:function(){return{next:t=>t.index+1}},rank:function(){let t;return{init:()=>t=1,next:e=>{const n=e.index,r=e.data;return n&&e.compare(r[n-1],r[n])?t=n+1:t}}},dense_rank:function(){let t;return{init:()=>t=1,next:e=>{const n=e.index,r=e.data;return n&&e.compare(r[n-1],r[n])?++t:t}}},percent_rank:function(){const t=gl.rank(),e=t.next;return{init:t.init,next:t=>(e(t)-1)/(t.data.length-1)}},cume_dist:function(){let t;return{init:()=>t=0,next:e=>{const n=e.data,r=e.compare;let i=e.index;if(t<i){for(;i+1<n.length&&!r(n[i],n[i+1]);)++i;t=i}return(1+t)/n.length}}},ntile:function(t,e){(e=+e)>0||u("ntile num must be greater than zero.");const n=gl.cume_dist(),r=n.next;return{init:n.init,next:t=>Math.ceil(e*r(t))}},lag:function(t,e){return e=+e||1,{next:n=>{const r=n.index-e;return r>=0?t(n.data[r]):null}}},lead:function(t,e){return e=+e||1,{next:n=>{const r=n.index+e,i=n.data;return r<i.length?t(i[r]):null}}},first_value:function(t){return{next:e=>t(e.data[e.i0])}},last_value:function(t){return{next:e=>t(e.data[e.i1-1])}},nth_value:function(t,e){return(e=+e)>0||u("nth_value nth must be greater than zero."),{next:n=>{const r=n.i0+(e-1);return r<n.i1?t(n.data[r]):null}}},prev_value:function(t){let e;return{init:()=>e=null,next:n=>{const r=t(n.data[n.index]);return null!=r?e=r:e}}},next_value:function(t){let e,n;return{init:()=>(e=null,n=-1),next:r=>{const i=r.data;return r.index<=n?e:(n=function(t,e,n){for(let r=e.length;n<r;++n){if(null!=t(e[n]))return n}return-1}(t,i,r.index))<0?(n=i.length,e=null):e=t(i[n])}}}};const ml=Object.keys(gl);function yl(t){const e=V(t.ops),i=V(t.fields),o=V(t.params),a=V(t.as),s=this.outputs=[],l=this.windows=[],c={},f={},d=[],p=[];let g=!0;function m(t){V(r(t)).forEach((t=>c[t]=1))}m(t.sort),e.forEach(((t,e)=>{const r=i[e],c=n(r),y=Ju(t,c,a[e]);if(m(r),s.push(y),lt(gl,t))l.push(function(t,e,n,r){const i=gl[t](e,n);return{init:i.init||h,update:function(t,e){e[r]=i.next(t)}}}(t,i[e],o[e],y));else{if(null==r&&"count"!==t&&u("Null aggregate field specified."),"count"===t)return void d.push(y);g=!1;let e=f[c];e||(e=f[c]=[],e.field=r,p.push(e)),e.push(es(t,y))}})),(d.length||p.length)&&(this.cell=function(t,e,n){t=t.map((t=>us(t,t.field)));const r={num:0,agg:null,store:!1,count:e};if(!n)for(var i=t.length,o=r.agg=Array(i),a=0;a<i;++a)o[a]=new t[a](r);if(r.store)var u=r.data=new ss;return r.add=function(t){if(r.num+=1,!n){u&&u.add(t);for(let e=0;e<i;++e)o[e].add(o[e].get(t),t)}},r.rem=function(t){if(r.num-=1,!n){u&&u.rem(t);for(let e=0;e<i;++e)o[e].rem(o[e].get(t),t)}},r.set=function(t){let i,a;for(u&&u.values(),i=0,a=e.length;i<a;++i)t[e[i]]=r.num;if(!n)for(i=0,a=o.length;i<a;++i)o[i].set(t)},r.init=function(){r.num=0,u&&u.reset();for(let t=0;t<i;++t)o[t].init()},r}(p,d,g)),this.inputs=Object.keys(c)}const vl=yl.prototype;function _l(t){Qa.call(this,{},t),this._mlen=0,this._mods=[]}function xl(t,e,n,r){const i=r.sort,o=i&&!r.ignorePeers,a=r.frame||[null,0],u=t.data(n),s=u.length,l=o?ne(i):null,c={i0:0,i1:0,p0:0,p1:0,index:0,data:u,compare:i||rt(-1)};e.init();for(let t=0;t<s;++t)bl(c,a,t,s),o&&wl(c,l),e.update(c,u[t])}function bl(t,e,n,r){t.p0=t.i0,t.p1=t.i1,t.i0=null==e[0]?0:Math.max(0,n-Math.abs(e[0])),t.i1=null==e[1]?r:Math.min(r,n+Math.abs(e[1])+1),t.index=n}function wl(t,e){const n=t.i0,r=t.i1-1,i=t.compare,o=t.data,a=o.length-1;n>0&&!i(o[n],o[n-1])&&(t.i0=e.left(o,o[n])),r<a&&!i(o[r],o[r+1])&&(t.i1=e.right(o,o[r]))}vl.init=function(){this.windows.forEach((t=>t.init())),this.cell&&this.cell.init()},vl.update=function(t,e){const n=this.cell,r=this.windows,i=t.data,o=r&&r.length;let a;if(n){for(a=t.p0;a<t.i0;++a)n.rem(i[a]);for(a=t.p1;a<t.i1;++a)n.add(i[a]);n.set(e)}for(a=0;a<o;++a)r[a].update(t,e)},_l.Definition={type:"Window",metadata:{modifies:!0},params:[{name:"sort",type:"compare"},{name:"groupby",type:"field",array:!0},{name:"ops",type:"enum",array:!0,values:ml.concat(ts)},{name:"params",type:"number",null:!0,array:!0},{name:"fields",type:"field",null:!0,array:!0},{name:"as",type:"string",null:!0,array:!0},{name:"frame",type:"number",null:!0,array:!0,length:2,default:[null,0]},{name:"ignorePeers",type:"boolean",default:!1}]},dt(_l,Qa,{transform(t,e){this.stamp=e.stamp;const n=t.modified(),r=Ma(t.sort),i=Xu(t.groupby),o=t=>this.group(i(t));let a=this.state;a&&!n||(a=this.state=new yl(t)),n||e.modified(a.inputs)?(this.value={},e.visit(e.SOURCE,(t=>o(t).add(t)))):(e.visit(e.REM,(t=>o(t).remove(t))),e.visit(e.ADD,(t=>o(t).add(t))));for(let e=0,n=this._mlen;e<n;++e)xl(this._mods[e],a,r,t);return this._mlen=0,this._mods=[],e.reflow(n).modifies(a.outputs)},group(t){let e=this.value[t];return e||(e=this.value[t]=hs(_a),e.stamp=-1),e.stamp<this.stamp&&(e.stamp=this.stamp,this._mods[this._mlen++]=e),e}});var kl=Object.freeze({__proto__:null,aggregate:cs,bin:fs,collect:ds,compare:ps,countpattern:ms,cross:ys,density:ks,dotbin:Cs,expression:Fs,extent:$s,facet:Bs,field:Ns,filter:Os,flatten:Rs,fold:Us,formula:Ls,generate:qs,impute:Is,joinaggregate:Ws,kde:Hs,key:Ys,load:Vs,lookup:Js,multiextent:Zs,multivalues:Ks,params:el,pivot:nl,prefacet:rl,project:il,proxy:ol,quantile:al,relay:ul,sample:sl,sequence:ll,sieve:cl,subflow:Ts,timeunit:fl,tupleindex:dl,values:pl,window:_l});function Al(t){return function(){return t}}const Ml=Math.abs,El=Math.atan2,Dl=Math.cos,Cl=Math.max,Fl=Math.min,Sl=Math.sin,$l=Math.sqrt,Tl=1e-12,Bl=Math.PI,Nl=Bl/2,zl=2*Bl;function Ol(t){return t>=1?Nl:t<=-1?-Nl:Math.asin(t)}const Rl=Math.PI,Ul=2*Rl,Ll=1e-6,ql=Ul-Ll;function Pl(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=arguments[e]+t[e]}let jl=class{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?Pl:function(t){let e=Math.floor(t);if(!(e>=0))throw new Error(`invalid digits: ${t}`);if(e>15)return Pl;const n=10**e;return function(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=Math.round(arguments[e]*n)/n+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,n,r){this._append`Q${+t},${+e},${this._x1=+n},${this._y1=+r}`}bezierCurveTo(t,e,n,r,i,o){this._append`C${+t},${+e},${+n},${+r},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,n,r,i){if(t=+t,e=+e,n=+n,r=+r,(i=+i)<0)throw new Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,u=n-t,s=r-e,l=o-t,c=a-e,f=l*l+c*c;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>Ll)if(Math.abs(c*u-s*l)>Ll&&i){let h=n-o,d=r-a,p=u*u+s*s,g=h*h+d*d,m=Math.sqrt(p),y=Math.sqrt(f),v=i*Math.tan((Rl-Math.acos((p+f-g)/(2*m*y)))/2),_=v/y,x=v/m;Math.abs(_-1)>Ll&&this._append`L${t+_*l},${e+_*c}`,this._append`A${i},${i},0,0,${+(c*h>l*d)},${this._x1=t+x*u},${this._y1=e+x*s}`}else this._append`L${this._x1=t},${this._y1=e}`;else;}arc(t,e,n,r,i,o){if(t=+t,e=+e,o=!!o,(n=+n)<0)throw new Error(`negative radius: ${n}`);let a=n*Math.cos(r),u=n*Math.sin(r),s=t+a,l=e+u,c=1^o,f=o?r-i:i-r;null===this._x1?this._append`M${s},${l}`:(Math.abs(this._x1-s)>Ll||Math.abs(this._y1-l)>Ll)&&this._append`L${s},${l}`,n&&(f<0&&(f=f%Ul+Ul),f>ql?this._append`A${n},${n},0,1,${c},${t-a},${e-u}A${n},${n},0,1,${c},${this._x1=s},${this._y1=l}`:f>Ll&&this._append`A${n},${n},0,${+(f>=Rl)},${c},${this._x1=t+n*Math.cos(i)},${this._y1=e+n*Math.sin(i)}`)}rect(t,e,n,r){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${n=+n}v${+r}h${-n}Z`}toString(){return this._}};function Il(){return new jl}function Wl(t){let e=3;return t.digits=function(n){if(!arguments.length)return e;if(null==n)e=null;else{const t=Math.floor(n);if(!(t>=0))throw new RangeError(`invalid digits: ${n}`);e=t}return t},()=>new jl(e)}function Hl(t){return t.innerRadius}function Yl(t){return t.outerRadius}function Gl(t){return t.startAngle}function Vl(t){return t.endAngle}function Xl(t){return t&&t.padAngle}function Jl(t,e,n,r,i,o,a){var u=t-n,s=e-r,l=(a?o:-o)/$l(u*u+s*s),c=l*s,f=-l*u,h=t+c,d=e+f,p=n+c,g=r+f,m=(h+p)/2,y=(d+g)/2,v=p-h,_=g-d,x=v*v+_*_,b=i-o,w=h*g-p*d,k=(_<0?-1:1)*$l(Cl(0,b*b*x-w*w)),A=(w*_-v*k)/x,M=(-w*v-_*k)/x,E=(w*_+v*k)/x,D=(-w*v+_*k)/x,C=A-m,F=M-y,S=E-m,$=D-y;return C*C+F*F>S*S+$*$&&(A=E,M=D),{cx:A,cy:M,x01:-c,y01:-f,x11:A*(i/b-1),y11:M*(i/b-1)}}function Zl(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function Ql(t){this._context=t}function Kl(t){return new Ql(t)}function tc(t){return t[0]}function ec(t){return t[1]}function nc(t,e){var n=Al(!0),r=null,i=Kl,o=null,a=Wl(u);function u(u){var s,l,c,f=(u=Zl(u)).length,h=!1;for(null==r&&(o=i(c=a())),s=0;s<=f;++s)!(s<f&&n(l=u[s],s,u))===h&&((h=!h)?o.lineStart():o.lineEnd()),h&&o.point(+t(l,s,u),+e(l,s,u));if(c)return o=null,c+""||null}return t="function"==typeof t?t:void 0===t?tc:Al(t),e="function"==typeof e?e:void 0===e?ec:Al(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:Al(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:Al(+t),u):e},u.defined=function(t){return arguments.length?(n="function"==typeof t?t:Al(!!t),u):n},u.curve=function(t){return arguments.length?(i=t,null!=r&&(o=i(r)),u):i},u.context=function(t){return arguments.length?(null==t?r=o=null:o=i(r=t),u):r},u}function rc(t,e,n){var r=null,i=Al(!0),o=null,a=Kl,u=null,s=Wl(l);function l(l){var c,f,h,d,p,g=(l=Zl(l)).length,m=!1,y=new Array(g),v=new Array(g);for(null==o&&(u=a(p=s())),c=0;c<=g;++c){if(!(c<g&&i(d=l[c],c,l))===m)if(m=!m)f=c,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),h=c-1;h>=f;--h)u.point(y[h],v[h]);u.lineEnd(),u.areaEnd()}m&&(y[c]=+t(d,c,l),v[c]=+e(d,c,l),u.point(r?+r(d,c,l):y[c],n?+n(d,c,l):v[c]))}if(p)return u=null,p+""||null}function c(){return nc().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?tc:Al(+t),e="function"==typeof e?e:Al(void 0===e?0:+e),n="function"==typeof n?n:void 0===n?ec:Al(+n),l.x=function(e){return arguments.length?(t="function"==typeof e?e:Al(+e),r=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:Al(+e),l):t},l.x1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Al(+t),l):r},l.y=function(t){return arguments.length?(e="function"==typeof t?t:Al(+t),n=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:Al(+t),l):e},l.y1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:Al(+t),l):n},l.lineX0=l.lineY0=function(){return c().x(t).y(e)},l.lineY1=function(){return c().x(t).y(n)},l.lineX1=function(){return c().x(r).y(e)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:Al(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(u=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=u=null:u=a(o=t),l):o},l}Il.prototype=jl.prototype,Ql.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}};var ic={draw(t,e){const n=$l(e/Bl);t.moveTo(n,0),t.arc(0,0,n,0,zl)}};function oc(){}function ac(t,e,n){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+n)/6)}function uc(t){this._context=t}function sc(t){this._context=t}function lc(t){this._context=t}function cc(t,e){this._basis=new uc(t),this._beta=e}uc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:ac(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:ac(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},sc.prototype={areaStart:oc,areaEnd:oc,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:ac(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},lc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var n=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(n,r):this._context.moveTo(n,r);break;case 3:this._point=4;default:ac(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},cc.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,e=this._y,n=t.length-1;if(n>0)for(var r,i=t[0],o=e[0],a=t[n]-i,u=e[n]-o,s=-1;++s<=n;)r=s/n,this._basis.point(this._beta*t[s]+(1-this._beta)*(i+r*a),this._beta*e[s]+(1-this._beta)*(o+r*u));this._x=this._y=null,this._basis.lineEnd()},point:function(t,e){this._x.push(+t),this._y.push(+e)}};var fc=function t(e){function n(t){return 1===e?new uc(t):new cc(t,e)}return n.beta=function(e){return t(+e)},n}(.85);function hc(t,e,n){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-e),t._y2+t._k*(t._y1-n),t._x2,t._y2)}function dc(t,e){this._context=t,this._k=(1-e)/6}dc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:hc(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2,this._x1=t,this._y1=e;break;case 2:this._point=3;default:hc(this,t,e)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var pc=function t(e){function n(t){return new dc(t,e)}return n.tension=function(e){return t(+e)},n}(0);function gc(t,e){this._context=t,this._k=(1-e)/6}gc.prototype={areaStart:oc,areaEnd:oc,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x3=t,this._y3=e;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=e);break;case 2:this._point=3,this._x5=t,this._y5=e;break;default:hc(this,t,e)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var mc=function t(e){function n(t){return new gc(t,e)}return n.tension=function(e){return t(+e)},n}(0);function yc(t,e){this._context=t,this._k=(1-e)/6}yc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:hc(this,t,e)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var vc=function t(e){function n(t){return new yc(t,e)}return n.tension=function(e){return t(+e)},n}(0);function _c(t,e,n){var r=t._x1,i=t._y1,o=t._x2,a=t._y2;if(t._l01_a>Tl){var u=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,s=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*u-t._x0*t._l12_2a+t._x2*t._l01_2a)/s,i=(i*u-t._y0*t._l12_2a+t._y2*t._l01_2a)/s}if(t._l23_a>Tl){var l=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,c=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*l+t._x1*t._l23_2a-e*t._l12_2a)/c,a=(a*l+t._y1*t._l23_2a-n*t._l12_2a)/c}t._context.bezierCurveTo(r,i,o,a,t._x2,t._y2)}function xc(t,e){this._context=t,this._alpha=e}xc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){if(t=+t,e=+e,this._point){var n=this._x2-t,r=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3;default:_c(this,t,e)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var bc=function t(e){function n(t){return e?new xc(t,e):new dc(t,0)}return n.alpha=function(e){return t(+e)},n}(.5);function wc(t,e){this._context=t,this._alpha=e}wc.prototype={areaStart:oc,areaEnd:oc,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,e){if(t=+t,e=+e,this._point){var n=this._x2-t,r=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=e;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=e);break;case 2:this._point=3,this._x5=t,this._y5=e;break;default:_c(this,t,e)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var kc=function t(e){function n(t){return e?new wc(t,e):new gc(t,0)}return n.alpha=function(e){return t(+e)},n}(.5);function Ac(t,e){this._context=t,this._alpha=e}Ac.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){if(t=+t,e=+e,this._point){var n=this._x2-t,r=this._y2-e;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:_c(this,t,e)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=e}};var Mc=function t(e){function n(t){return e?new Ac(t,e):new yc(t,0)}return n.alpha=function(e){return t(+e)},n}(.5);function Ec(t){this._context=t}function Dc(t){return t<0?-1:1}function Cc(t,e,n){var r=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(r||i<0&&-0),a=(n-t._y1)/(i||r<0&&-0),u=(o*i+a*r)/(r+i);return(Dc(o)+Dc(a))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs(u))||0}function Fc(t,e){var n=t._x1-t._x0;return n?(3*(t._y1-t._y0)/n-e)/2:e}function Sc(t,e,n){var r=t._x0,i=t._y0,o=t._x1,a=t._y1,u=(o-r)/3;t._context.bezierCurveTo(r+u,i+u*e,o-u,a-u*n,o,a)}function $c(t){this._context=t}function Tc(t){this._context=new Bc(t)}function Bc(t){this._context=t}function Nc(t){this._context=t}function zc(t){var e,n,r=t.length-1,i=new Array(r),o=new Array(r),a=new Array(r);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<r-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[r-1]=2,o[r-1]=7,a[r-1]=8*t[r-1]+t[r],e=1;e<r;++e)n=i[e]/o[e-1],o[e]-=n,a[e]-=n*a[e-1];for(i[r-1]=a[r-1]/o[r-1],e=r-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(o[r-1]=(t[r]+i[r-1])/2,e=0;e<r-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function Oc(t,e){this._context=t,this._t=e}function Rc(t,e){if("undefined"!=typeof document&&document.createElement){const n=document.createElement("canvas");if(n&&n.getContext)return n.width=t,n.height=e,n}return null}Ec.prototype={areaStart:oc,areaEnd:oc,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},$c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Sc(this,this._t0,Fc(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var n=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,Sc(this,Fc(this,n=Cc(this,t,e)),n);break;default:Sc(this,this._t0,n=Cc(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=n}}},(Tc.prototype=Object.create($c.prototype)).point=function(t,e){$c.prototype.point.call(this,e,t)},Bc.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,n,r,i,o){this._context.bezierCurveTo(e,t,r,n,o,i)}},Nc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,n=t.length;if(n)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===n)this._context.lineTo(t[1],e[1]);else for(var r=zc(t),i=zc(e),o=0,a=1;a<n;++o,++a)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===n)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},Oc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var n=this._x*(1-this._t)+t*this._t;this._context.lineTo(n,this._y),this._context.lineTo(n,e)}}this._x=t,this._y=e}};const Uc=()=>"undefined"!=typeof Image?Image:null;function Lc(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function qc(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}const Pc=Symbol("implicit");function jc(){var t=new ce,e=[],n=[],r=Pc;function i(i){let o=t.get(i);if(void 0===o){if(r!==Pc)return r;t.set(i,o=e.push(i)-1)}return n[o%n.length]}return i.domain=function(n){if(!arguments.length)return e.slice();e=[],t=new ce;for(const r of n)t.has(r)||t.set(r,e.push(r)-1);return i},i.range=function(t){return arguments.length?(n=Array.from(t),i):n.slice()},i.unknown=function(t){return arguments.length?(r=t,i):r},i.copy=function(){return jc(e,n).unknown(r)},Lc.apply(i,arguments),i}function Ic(t,e,n){t.prototype=e.prototype=n,n.constructor=t}function Wc(t,e){var n=Object.create(t.prototype);for(var r in e)n[r]=e[r];return n}function Hc(){}var Yc=.7,Gc=1/Yc,Vc="\\s*([+-]?\\d+)\\s*",Xc="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Jc="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Zc=/^#([0-9a-f]{3,8})$/,Qc=new RegExp(`^rgb\\(${Vc},${Vc},${Vc}\\)$`),Kc=new RegExp(`^rgb\\(${Jc},${Jc},${Jc}\\)$`),tf=new RegExp(`^rgba\\(${Vc},${Vc},${Vc},${Xc}\\)$`),ef=new RegExp(`^rgba\\(${Jc},${Jc},${Jc},${Xc}\\)$`),nf=new RegExp(`^hsl\\(${Xc},${Jc},${Jc}\\)$`),rf=new RegExp(`^hsla\\(${Xc},${Jc},${Jc},${Xc}\\)$`),of={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function af(){return this.rgb().formatHex()}function uf(){return this.rgb().formatRgb()}function sf(t){var e,n;return t=(t+"").trim().toLowerCase(),(e=Zc.exec(t))?(n=e[1].length,e=parseInt(e[1],16),6===n?lf(e):3===n?new df(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===n?cf(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===n?cf(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=Qc.exec(t))?new df(e[1],e[2],e[3],1):(e=Kc.exec(t))?new df(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=tf.exec(t))?cf(e[1],e[2],e[3],e[4]):(e=ef.exec(t))?cf(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=nf.exec(t))?_f(e[1],e[2]/100,e[3]/100,1):(e=rf.exec(t))?_f(e[1],e[2]/100,e[3]/100,e[4]):of.hasOwnProperty(t)?lf(of[t]):"transparent"===t?new df(NaN,NaN,NaN,0):null}function lf(t){return new df(t>>16&255,t>>8&255,255&t,1)}function cf(t,e,n,r){return r<=0&&(t=e=n=NaN),new df(t,e,n,r)}function ff(t){return t instanceof Hc||(t=sf(t)),t?new df((t=t.rgb()).r,t.g,t.b,t.opacity):new df}function hf(t,e,n,r){return 1===arguments.length?ff(t):new df(t,e,n,null==r?1:r)}function df(t,e,n,r){this.r=+t,this.g=+e,this.b=+n,this.opacity=+r}function pf(){return`#${vf(this.r)}${vf(this.g)}${vf(this.b)}`}function gf(){const t=mf(this.opacity);return`${1===t?"rgb(":"rgba("}${yf(this.r)}, ${yf(this.g)}, ${yf(this.b)}${1===t?")":`, ${t})`}`}function mf(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function yf(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function vf(t){return((t=yf(t))<16?"0":"")+t.toString(16)}function _f(t,e,n,r){return r<=0?t=e=n=NaN:n<=0||n>=1?t=e=NaN:e<=0&&(t=NaN),new wf(t,e,n,r)}function xf(t){if(t instanceof wf)return new wf(t.h,t.s,t.l,t.opacity);if(t instanceof Hc||(t=sf(t)),!t)return new wf;if(t instanceof wf)return t;var e=(t=t.rgb()).r/255,n=t.g/255,r=t.b/255,i=Math.min(e,n,r),o=Math.max(e,n,r),a=NaN,u=o-i,s=(o+i)/2;return u?(a=e===o?(n-r)/u+6*(n<r):n===o?(r-e)/u+2:(e-n)/u+4,u/=s<.5?o+i:2-o-i,a*=60):u=s>0&&s<1?0:a,new wf(a,u,s,t.opacity)}function bf(t,e,n,r){return 1===arguments.length?xf(t):new wf(t,e,n,null==r?1:r)}function wf(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function kf(t){return(t=(t||0)%360)<0?t+360:t}function Af(t){return Math.max(0,Math.min(1,t||0))}function Mf(t,e,n){return 255*(t<60?e+(n-e)*t/60:t<180?n:t<240?e+(n-e)*(240-t)/60:e)}Ic(Hc,sf,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:af,formatHex:af,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return xf(this).formatHsl()},formatRgb:uf,toString:uf}),Ic(df,hf,Wc(Hc,{brighter(t){return t=null==t?Gc:Math.pow(Gc,t),new df(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?Yc:Math.pow(Yc,t),new df(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new df(yf(this.r),yf(this.g),yf(this.b),mf(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:pf,formatHex:pf,formatHex8:function(){return`#${vf(this.r)}${vf(this.g)}${vf(this.b)}${vf(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:gf,toString:gf})),Ic(wf,bf,Wc(Hc,{brighter(t){return t=null==t?Gc:Math.pow(Gc,t),new wf(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Yc:Math.pow(Yc,t),new wf(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+360*(this.h<0),e=isNaN(t)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*e,i=2*n-r;return new df(Mf(t>=240?t-240:t+120,i,r),Mf(t,i,r),Mf(t<120?t+240:t-120,i,r),this.opacity)},clamp(){return new wf(kf(this.h),Af(this.s),Af(this.l),mf(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const t=mf(this.opacity);return`${1===t?"hsl(":"hsla("}${kf(this.h)}, ${100*Af(this.s)}%, ${100*Af(this.l)}%${1===t?")":`, ${t})`}`}}));const Ef=Math.PI/180,Df=180/Math.PI,Cf=.96422,Ff=1,Sf=.82521,$f=4/29,Tf=6/29,Bf=3*Tf*Tf,Nf=Tf*Tf*Tf;function zf(t){if(t instanceof Rf)return new Rf(t.l,t.a,t.b,t.opacity);if(t instanceof If)return Wf(t);t instanceof df||(t=ff(t));var e,n,r=Pf(t.r),i=Pf(t.g),o=Pf(t.b),a=Uf((.2225045*r+.7168786*i+.0606169*o)/Ff);return r===i&&i===o?e=n=a:(e=Uf((.4360747*r+.3850649*i+.1430804*o)/Cf),n=Uf((.0139322*r+.0971045*i+.7141733*o)/Sf)),new Rf(116*a-16,500*(e-a),200*(a-n),t.opacity)}function Of(t,e,n,r){return 1===arguments.length?zf(t):new Rf(t,e,n,null==r?1:r)}function Rf(t,e,n,r){this.l=+t,this.a=+e,this.b=+n,this.opacity=+r}function Uf(t){return t>Nf?Math.pow(t,1/3):t/Bf+$f}function Lf(t){return t>Tf?t*t*t:Bf*(t-$f)}function qf(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function Pf(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function jf(t,e,n,r){return 1===arguments.length?function(t){if(t instanceof If)return new If(t.h,t.c,t.l,t.opacity);if(t instanceof Rf||(t=zf(t)),0===t.a&&0===t.b)return new If(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var e=Math.atan2(t.b,t.a)*Df;return new If(e<0?e+360:e,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}(t):new If(t,e,n,null==r?1:r)}function If(t,e,n,r){this.h=+t,this.c=+e,this.l=+n,this.opacity=+r}function Wf(t){if(isNaN(t.h))return new Rf(t.l,0,0,t.opacity);var e=t.h*Ef;return new Rf(t.l,Math.cos(e)*t.c,Math.sin(e)*t.c,t.opacity)}Ic(Rf,Of,Wc(Hc,{brighter(t){return new Rf(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new Rf(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,e=isNaN(this.a)?t:t+this.a/500,n=isNaN(this.b)?t:t-this.b/200;return new df(qf(3.1338561*(e=Cf*Lf(e))-1.6168667*(t=Ff*Lf(t))-.4906146*(n=Sf*Lf(n))),qf(-.9787684*e+1.9161415*t+.033454*n),qf(.0719453*e-.2289914*t+1.4052427*n),this.opacity)}})),Ic(If,jf,Wc(Hc,{brighter(t){return new If(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new If(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return Wf(this).rgb()}}));var Hf=-.14861,Yf=1.78277,Gf=-.29227,Vf=-.90649,Xf=1.97294,Jf=Xf*Vf,Zf=Xf*Yf,Qf=Yf*Gf-Vf*Hf;function Kf(t,e,n,r){return 1===arguments.length?function(t){if(t instanceof th)return new th(t.h,t.s,t.l,t.opacity);t instanceof df||(t=ff(t));var e=t.r/255,n=t.g/255,r=t.b/255,i=(Qf*r+Jf*e-Zf*n)/(Qf+Jf-Zf),o=r-i,a=(Xf*(n-i)-Gf*o)/Vf,u=Math.sqrt(a*a+o*o)/(Xf*i*(1-i)),s=u?Math.atan2(a,o)*Df-120:NaN;return new th(s<0?s+360:s,u,i,t.opacity)}(t):new th(t,e,n,null==r?1:r)}function th(t,e,n,r){this.h=+t,this.s=+e,this.l=+n,this.opacity=+r}function eh(t,e,n,r,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*n+(1+3*t+3*o-3*a)*r+a*i)/6}function nh(t){var e=t.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,e-1):Math.floor(n*e),i=t[r],o=t[r+1],a=r>0?t[r-1]:2*i-o,u=r<e-1?t[r+2]:2*o-i;return eh((n-r/e)*e,a,i,o,u)}}function rh(t){var e=t.length;return function(n){var r=Math.floor(((n%=1)<0?++n:n)*e),i=t[(r+e-1)%e],o=t[r%e],a=t[(r+1)%e],u=t[(r+2)%e];return eh((n-r/e)*e,i,o,a,u)}}Ic(th,Kf,Wc(Hc,{brighter(t){return t=null==t?Gc:Math.pow(Gc,t),new th(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?Yc:Math.pow(Yc,t),new th(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*Ef,e=+this.l,n=isNaN(this.s)?0:this.s*e*(1-e),r=Math.cos(t),i=Math.sin(t);return new df(255*(e+n*(Hf*r+Yf*i)),255*(e+n*(Gf*r+Vf*i)),255*(e+n*(Xf*r)),this.opacity)}}));var ih=t=>()=>t;function oh(t,e){return function(n){return t+n*e}}function ah(t,e){var n=e-t;return n?oh(t,n>180||n<-180?n-360*Math.round(n/360):n):ih(isNaN(t)?e:t)}function uh(t){return 1==(t=+t)?sh:function(e,n){return n-e?function(t,e,n){return t=Math.pow(t,n),e=Math.pow(e,n)-t,n=1/n,function(r){return Math.pow(t+r*e,n)}}(e,n,t):ih(isNaN(e)?n:e)}}function sh(t,e){var n=e-t;return n?oh(t,n):ih(isNaN(t)?e:t)}var lh=function t(e){var n=uh(e);function r(t,e){var r=n((t=hf(t)).r,(e=hf(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=sh(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return r.gamma=t,r}(1);function ch(t){return function(e){var n,r,i=e.length,o=new Array(i),a=new Array(i),u=new Array(i);for(n=0;n<i;++n)r=hf(e[n]),o[n]=r.r||0,a[n]=r.g||0,u[n]=r.b||0;return o=t(o),a=t(a),u=t(u),r.opacity=1,function(t){return r.r=o(t),r.g=a(t),r.b=u(t),r+""}}}var fh=ch(nh),hh=ch(rh);function dh(t,e){e||(e=[]);var n,r=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(n=0;n<r;++n)i[n]=t[n]*(1-o)+e[n]*o;return i}}function ph(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}function gh(t,e){var n,r=e?e.length:0,i=t?Math.min(r,t.length):0,o=new Array(i),a=new Array(r);for(n=0;n<i;++n)o[n]=wh(t[n],e[n]);for(;n<r;++n)a[n]=e[n];return function(t){for(n=0;n<i;++n)a[n]=o[n](t);return a}}function mh(t,e){var n=new Date;return t=+t,e=+e,function(r){return n.setTime(t*(1-r)+e*r),n}}function yh(t,e){return t=+t,e=+e,function(n){return t*(1-n)+e*n}}function vh(t,e){var n,r={},i={};for(n in null!==t&&"object"==typeof t||(t={}),null!==e&&"object"==typeof e||(e={}),e)n in t?r[n]=wh(t[n],e[n]):i[n]=e[n];return function(t){for(n in r)i[n]=r[n](t);return i}}var _h=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,xh=new RegExp(_h.source,"g");function bh(t,e){var n,r,i,o=_h.lastIndex=xh.lastIndex=0,a=-1,u=[],s=[];for(t+="",e+="";(n=_h.exec(t))&&(r=xh.exec(e));)(i=r.index)>o&&(i=e.slice(o,i),u[a]?u[a]+=i:u[++a]=i),(n=n[0])===(r=r[0])?u[a]?u[a]+=r:u[++a]=r:(u[++a]=null,s.push({i:a,x:yh(n,r)})),o=xh.lastIndex;return o<e.length&&(i=e.slice(o),u[a]?u[a]+=i:u[++a]=i),u.length<2?s[0]?function(t){return function(e){return t(e)+""}}(s[0].x):function(t){return function(){return t}}(e):(e=s.length,function(t){for(var n,r=0;r<e;++r)u[(n=s[r]).i]=n.x(t);return u.join("")})}function wh(t,e){var n,r=typeof e;return null==e||"boolean"===r?ih(e):("number"===r?yh:"string"===r?(n=sf(e))?(e=n,lh):bh:e instanceof sf?lh:e instanceof Date?mh:ph(e)?dh:Array.isArray(e)?gh:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?vh:yh)(t,e)}function kh(t,e){return t=+t,e=+e,function(n){return Math.round(t*(1-n)+e*n)}}var Ah,Mh=180/Math.PI,Eh={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Dh(t,e,n,r,i,o){var a,u,s;return(a=Math.sqrt(t*t+e*e))&&(t/=a,e/=a),(s=t*n+e*r)&&(n-=t*s,r-=e*s),(u=Math.sqrt(n*n+r*r))&&(n/=u,r/=u,s/=u),t*r<e*n&&(t=-t,e=-e,s=-s,a=-a),{translateX:i,translateY:o,rotate:Math.atan2(e,t)*Mh,skewX:Math.atan(s)*Mh,scaleX:a,scaleY:u}}function Ch(t,e,n,r){function i(t){return t.length?t.pop()+" ":""}return function(o,a){var u=[],s=[];return o=t(o),a=t(a),function(t,r,i,o,a,u){if(t!==i||r!==o){var s=a.push("translate(",null,e,null,n);u.push({i:s-4,x:yh(t,i)},{i:s-2,x:yh(r,o)})}else(i||o)&&a.push("translate("+i+e+o+n)}(o.translateX,o.translateY,a.translateX,a.translateY,u,s),function(t,e,n,o){t!==e?(t-e>180?e+=360:e-t>180&&(t+=360),o.push({i:n.push(i(n)+"rotate(",null,r)-2,x:yh(t,e)})):e&&n.push(i(n)+"rotate("+e+r)}(o.rotate,a.rotate,u,s),function(t,e,n,o){t!==e?o.push({i:n.push(i(n)+"skewX(",null,r)-2,x:yh(t,e)}):e&&n.push(i(n)+"skewX("+e+r)}(o.skewX,a.skewX,u,s),function(t,e,n,r,o,a){if(t!==n||e!==r){var u=o.push(i(o)+"scale(",null,",",null,")");a.push({i:u-4,x:yh(t,n)},{i:u-2,x:yh(e,r)})}else 1===n&&1===r||o.push(i(o)+"scale("+n+","+r+")")}(o.scaleX,o.scaleY,a.scaleX,a.scaleY,u,s),o=a=null,function(t){for(var e,n=-1,r=s.length;++n<r;)u[(e=s[n]).i]=e.x(t);return u.join("")}}}var Fh=Ch((function(t){const e=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return e.isIdentity?Eh:Dh(e.a,e.b,e.c,e.d,e.e,e.f)}),"px, ","px)","deg)"),Sh=Ch((function(t){return null==t?Eh:(Ah||(Ah=document.createElementNS("http://www.w3.org/2000/svg","g")),Ah.setAttribute("transform",t),(t=Ah.transform.baseVal.consolidate())?Dh((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):Eh)}),", ",")",")"),$h=1e-12;function Th(t){return((t=Math.exp(t))+1/t)/2}var Bh=function t(e,n,r){function i(t,i){var o,a,u=t[0],s=t[1],l=t[2],c=i[0],f=i[1],h=i[2],d=c-u,p=f-s,g=d*d+p*p;if(g<$h)a=Math.log(h/l)/e,o=function(t){return[u+t*d,s+t*p,l*Math.exp(e*t*a)]};else{var m=Math.sqrt(g),y=(h*h-l*l+r*g)/(2*l*n*m),v=(h*h-l*l-r*g)/(2*h*n*m),_=Math.log(Math.sqrt(y*y+1)-y),x=Math.log(Math.sqrt(v*v+1)-v);a=(x-_)/e,o=function(t){var r=t*a,i=Th(_),o=l/(n*m)*(i*function(t){return((t=Math.exp(2*t))-1)/(t+1)}(e*r+_)-function(t){return((t=Math.exp(t))-1/t)/2}(_));return[u+o*d,s+o*p,l*i/Th(e*r+_)]}}return o.duration=1e3*a*e/Math.SQRT2,o}return i.rho=function(e){var n=Math.max(.001,+e),r=n*n;return t(n,r,r*r)},i}(Math.SQRT2,2,4);function Nh(t){return function(e,n){var r=t((e=bf(e)).h,(n=bf(n)).h),i=sh(e.s,n.s),o=sh(e.l,n.l),a=sh(e.opacity,n.opacity);return function(t){return e.h=r(t),e.s=i(t),e.l=o(t),e.opacity=a(t),e+""}}}var zh=Nh(ah),Oh=Nh(sh);function Rh(t){return function(e,n){var r=t((e=jf(e)).h,(n=jf(n)).h),i=sh(e.c,n.c),o=sh(e.l,n.l),a=sh(e.opacity,n.opacity);return function(t){return e.h=r(t),e.c=i(t),e.l=o(t),e.opacity=a(t),e+""}}}var Uh=Rh(ah),Lh=Rh(sh);function qh(t){return function e(n){function r(e,r){var i=t((e=Kf(e)).h,(r=Kf(r)).h),o=sh(e.s,r.s),a=sh(e.l,r.l),u=sh(e.opacity,r.opacity);return function(t){return e.h=i(t),e.s=o(t),e.l=a(Math.pow(t,n)),e.opacity=u(t),e+""}}return n=+n,r.gamma=e,r}(1)}var Ph=qh(ah),jh=qh(sh);function Ih(t,e){void 0===e&&(e=t,t=wh);for(var n=0,r=e.length-1,i=e[0],o=new Array(r<0?0:r);n<r;)o[n]=t(i,i=e[++n]);return function(t){var e=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return o[e](t-e)}}var Wh=Object.freeze({__proto__:null,interpolate:wh,interpolateArray:function(t,e){return(ph(e)?dh:gh)(t,e)},interpolateBasis:nh,interpolateBasisClosed:rh,interpolateCubehelix:Ph,interpolateCubehelixLong:jh,interpolateDate:mh,interpolateDiscrete:function(t){var e=t.length;return function(n){return t[Math.max(0,Math.min(e-1,Math.floor(n*e)))]}},interpolateHcl:Uh,interpolateHclLong:Lh,interpolateHsl:zh,interpolateHslLong:Oh,interpolateHue:function(t,e){var n=ah(+t,+e);return function(t){var e=n(t);return e-360*Math.floor(e/360)}},interpolateLab:function(t,e){var n=sh((t=Of(t)).l,(e=Of(e)).l),r=sh(t.a,e.a),i=sh(t.b,e.b),o=sh(t.opacity,e.opacity);return function(e){return t.l=n(e),t.a=r(e),t.b=i(e),t.opacity=o(e),t+""}},interpolateNumber:yh,interpolateNumberArray:dh,interpolateObject:vh,interpolateRgb:lh,interpolateRgbBasis:fh,interpolateRgbBasisClosed:hh,interpolateRound:kh,interpolateString:bh,interpolateTransformCss:Fh,interpolateTransformSvg:Sh,interpolateZoom:Bh,piecewise:Ih,quantize:function(t,e){for(var n=new Array(e),r=0;r<e;++r)n[r]=t(r/(e-1));return n}});function Hh(t){return+t}var Yh=[0,1];function Gh(t){return t}function Vh(t,e){return(e-=t=+t)?function(n){return(n-t)/e}:function(t){return function(){return t}}(isNaN(e)?NaN:.5)}function Xh(t,e,n){var r=t[0],i=t[1],o=e[0],a=e[1];return i<r?(r=Vh(i,r),o=n(a,o)):(r=Vh(r,i),o=n(o,a)),function(t){return o(r(t))}}function Jh(t,e,n){var r=Math.min(t.length,e.length)-1,i=new Array(r),o=new Array(r),a=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<r;)i[a]=Vh(t[a],t[a+1]),o[a]=n(e[a],e[a+1]);return function(e){var n=se(t,e,1,r)-1;return o[n](i[n](e))}}function Zh(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function Qh(){var t,e,n,r,i,o,a=Yh,u=Yh,s=wh,l=Gh;function c(){var t=Math.min(a.length,u.length);return l!==Gh&&(l=function(t,e){var n;return t>e&&(n=t,t=e,e=n),function(n){return Math.max(t,Math.min(e,n))}}(a[0],a[t-1])),r=t>2?Jh:Xh,i=o=null,f}function f(e){return null==e||isNaN(e=+e)?n:(i||(i=r(a.map(t),u,s)))(t(l(e)))}return f.invert=function(n){return l(e((o||(o=r(u,a.map(t),yh)))(n)))},f.domain=function(t){return arguments.length?(a=Array.from(t,Hh),c()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),c()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),s=kh,c()},f.clamp=function(t){return arguments.length?(l=!!t||Gh,c()):l!==Gh},f.interpolate=function(t){return arguments.length?(s=t,c()):s},f.unknown=function(t){return arguments.length?(n=t,f):n},function(n,r){return t=n,e=r,c()}}function Kh(){return Qh()(Gh,Gh)}function td(t,e,n,r){var i,o=ke(t,e,n);switch((r=Le(null==r?",f":r)).type){case"s":var a=Math.max(Math.abs(t),Math.abs(e));return null!=r.precision||isNaN(i=Ze(o,a))||(r.precision=i),Ye(r,a);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=Qe(o,Math.max(Math.abs(t),Math.abs(e))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=Je(o))||(r.precision=i-2*("%"===r.type))}return He(r)}function ed(t){var e=t.domain;return t.ticks=function(t){var n=e();return be(n[0],n[n.length-1],null==t?10:t)},t.tickFormat=function(t,n){var r=e();return td(r[0],r[r.length-1],null==t?10:t,n)},t.nice=function(n){null==n&&(n=10);var r,i,o=e(),a=0,u=o.length-1,s=o[a],l=o[u],c=10;for(l<s&&(i=s,s=l,l=i,i=a,a=u,u=i);c-- >0;){if((i=we(s,l,n))===r)return o[a]=s,o[u]=l,e(o);if(i>0)s=Math.floor(s/i)*i,l=Math.ceil(l/i)*i;else{if(!(i<0))break;s=Math.ceil(s*i)/i,l=Math.floor(l*i)/i}r=i}return t},t}function nd(t,e){var n,r=0,i=(t=t.slice()).length-1,o=t[r],a=t[i];return a<o&&(n=r,r=i,i=n,n=o,o=a,a=n),t[r]=e.floor(o),t[i]=e.ceil(a),t}function rd(t){return Math.log(t)}function id(t){return Math.exp(t)}function od(t){return-Math.log(-t)}function ad(t){return-Math.exp(-t)}function ud(t){return isFinite(t)?+("1e"+t):t<0?0:t}function sd(t){return(e,n)=>-t(-e,n)}function ld(t){const e=t(rd,id),n=e.domain;let r,i,o=10;function a(){return r=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),e=>Math.log(e)/t)}(o),i=function(t){return 10===t?ud:t===Math.E?Math.exp:e=>Math.pow(t,e)}(o),n()[0]<0?(r=sd(r),i=sd(i),t(od,ad)):t(rd,id),e}return e.base=function(t){return arguments.length?(o=+t,a()):o},e.domain=function(t){return arguments.length?(n(t),a()):n()},e.ticks=t=>{const e=n();let a=e[0],u=e[e.length-1];const s=u<a;s&&([a,u]=[u,a]);let l,c,f=r(a),h=r(u);const d=null==t?10:+t;let p=[];if(!(o%1)&&h-f<d){if(f=Math.floor(f),h=Math.ceil(h),a>0){for(;f<=h;++f)for(l=1;l<o;++l)if(c=f<0?l/i(-f):l*i(f),!(c<a)){if(c>u)break;p.push(c)}}else for(;f<=h;++f)for(l=o-1;l>=1;--l)if(c=f>0?l/i(-f):l*i(f),!(c<a)){if(c>u)break;p.push(c)}2*p.length<d&&(p=be(a,u,d))}else p=be(f,h,Math.min(h-f,d)).map(i);return s?p.reverse():p},e.tickFormat=(t,n)=>{if(null==t&&(t=10),null==n&&(n=10===o?"s":","),"function"!=typeof n&&(o%1||null!=(n=Le(n)).precision||(n.trim=!0),n=He(n)),t===1/0)return n;const a=Math.max(1,o*t/e.ticks().length);return t=>{let e=t/i(Math.round(r(t)));return e*o<o-.5&&(e*=o),e<=a?n(t):""}},e.nice=()=>n(nd(n(),{floor:t=>i(Math.floor(r(t))),ceil:t=>i(Math.ceil(r(t)))})),e}function cd(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function fd(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function hd(t){var e=1,n=t(cd(e),fd(e));return n.constant=function(n){return arguments.length?t(cd(e=+n),fd(e)):e},ed(n)}function dd(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function pd(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function gd(t){return t<0?-t*t:t*t}function md(t){var e=t(Gh,Gh),n=1;return e.exponent=function(e){return arguments.length?1===(n=+e)?t(Gh,Gh):.5===n?t(pd,gd):t(dd(n),dd(1/n)):n},ed(e)}function yd(){var t=md(Qh());return t.copy=function(){return Zh(t,yd()).exponent(t.exponent())},Lc.apply(t,arguments),t}function vd(t){return new Date(t)}function _d(t){return t instanceof Date?+t:+new Date(+t)}function xd(t,e,n,r,i,o,a,u,s,l){var c=Kh(),f=c.invert,h=c.domain,d=l(".%L"),p=l(":%S"),g=l("%I:%M"),m=l("%I %p"),y=l("%a %d"),v=l("%b %d"),_=l("%B"),x=l("%Y");function b(t){return(s(t)<t?d:u(t)<t?p:a(t)<t?g:o(t)<t?m:r(t)<t?i(t)<t?y:v:n(t)<t?_:x)(t)}return c.invert=function(t){return new Date(f(t))},c.domain=function(t){return arguments.length?h(Array.from(t,_d)):h().map(vd)},c.ticks=function(e){var n=h();return t(n[0],n[n.length-1],null==e?10:e)},c.tickFormat=function(t,e){return null==e?b:l(e)},c.nice=function(t){var n=h();return t&&"function"==typeof t.range||(t=e(n[0],n[n.length-1],null==t?10:t)),t?h(nd(n,t)):c},c.copy=function(){return Zh(c,xd(t,e,n,r,i,o,a,u,s,l))},c}function bd(){var t,e,n,r,i,o=0,a=1,u=Gh,s=!1;function l(e){return null==e||isNaN(e=+e)?i:u(0===n?.5:(e=(r(e)-t)*n,s?Math.max(0,Math.min(1,e)):e))}function c(t){return function(e){var n,r;return arguments.length?([n,r]=e,u=t(n,r),l):[u(0),u(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=r(o=+o),e=r(a=+a),n=t===e?0:1/(e-t),l):[o,a]},l.clamp=function(t){return arguments.length?(s=!!t,l):s},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=c(wh),l.rangeRound=c(kh),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return r=i,t=i(o),e=i(a),n=t===e?0:1/(e-t),l}}function wd(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function kd(){var t=ed(bd()(Gh));return t.copy=function(){return wd(t,kd())},qc.apply(t,arguments)}function Ad(){var t=md(bd());return t.copy=function(){return wd(t,Ad()).exponent(t.exponent())},qc.apply(t,arguments)}function Md(){var t,e,n,r,i,o,a,u=0,s=.5,l=1,c=1,f=Gh,h=!1;function d(t){return isNaN(t=+t)?a:(t=.5+((t=+o(t))-e)*(c*t<c*e?r:i),f(h?Math.max(0,Math.min(1,t)):t))}function p(t){return function(e){var n,r,i;return arguments.length?([n,r,i]=e,f=Ih(t,[n,r,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([u,s,l]=a,t=o(u=+u),e=o(s=+s),n=o(l=+l),r=t===e?0:.5/(e-t),i=e===n?0:.5/(n-e),c=e<t?-1:1,d):[u,s,l]},d.clamp=function(t){return arguments.length?(h=!!t,d):h},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=p(wh),d.rangeRound=p(kh),d.unknown=function(t){return arguments.length?(a=t,d):a},function(a){return o=a,t=a(u),e=a(s),n=a(l),r=t===e?0:.5/(e-t),i=e===n?0:.5/(n-e),c=e<t?-1:1,d}}function Ed(){var t=md(Md());return t.copy=function(){return wd(t,Ed()).exponent(t.exponent())},qc.apply(t,arguments)}function Dd(t,e,n){const r=t-e+2*n;return t?r>0?r:1:0}const Cd="linear",Fd="log",Sd="pow",$d="sqrt",Td="symlog",Bd="time",Nd="utc",zd="sequential",Od="diverging",Rd="quantile",Ud="quantize",Ld="threshold",qd="ordinal",Pd="point",jd="band",Id="bin-ordinal",Wd="continuous",Hd="discrete",Yd="discretizing",Gd="interpolating",Vd="temporal";function Xd(){const t=jc().unknown(void 0),e=t.domain,n=t.range;let r,i,o=[0,1],a=!1,u=0,s=0,l=.5;function c(){const t=e().length,c=o[1]<o[0],f=o[1-c],h=Dd(t,u,s);let d=o[c-0];r=(f-d)/(h||1),a&&(r=Math.floor(r)),d+=(f-d-r*(t-u))*l,i=r*(1-u),a&&(d=Math.round(d),i=Math.round(i));const p=Te(t).map((t=>d+r*t));return n(c?p.reverse():p)}return delete t.unknown,t.domain=function(t){return arguments.length?(e(t),c()):e()},t.range=function(t){return arguments.length?(o=[+t[0],+t[1]],c()):o.slice()},t.rangeRound=function(t){return o=[+t[0],+t[1]],a=!0,c()},t.bandwidth=function(){return i},t.step=function(){return r},t.round=function(t){return arguments.length?(a=!!t,c()):a},t.padding=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),u=s,c()):u},t.paddingInner=function(t){return arguments.length?(u=Math.max(0,Math.min(1,t)),c()):u},t.paddingOuter=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),c()):s},t.align=function(t){return arguments.length?(l=Math.max(0,Math.min(1,t)),c()):l},t.invertRange=function(t){if(null==t[0]||null==t[1])return;const r=o[1]<o[0],a=r?n().reverse():n(),u=a.length-1;let s,l,c,f=+t[0],h=+t[1];return f!=f||h!=h||(h<f&&(c=f,f=h,h=c),h<a[0]||f>o[1-r])?void 0:(s=Math.max(0,ae(a,f)-1),l=f===h?s:ae(a,h)-1,f-a[s]>i+1e-10&&++s,r&&(c=s,s=u-l,l=u-c),s>l?void 0:e().slice(s,l+1))},t.invert=function(e){const n=t.invertRange([e,e]);return n?n[0]:n},t.copy=function(){return Xd().domain(e()).range(o).round(a).paddingInner(u).paddingOuter(s).align(l)},c()}function Jd(t){const e=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,t.copy=function(){return Jd(e())},t}var Zd=Array.prototype.map;const Qd=Array.prototype.slice;const Kd=new Map,tp=Symbol("vega_scale");function ep(t){return t[tp]=!0,t}function np(t,e,n){return arguments.length>1?(Kd.set(t,function(t,e,n){const r=function(){const n=e();return n.invertRange||(n.invertRange=n.invert?function(t){return function(e){let n,r=e[0],i=e[1];return i<r&&(n=r,r=i,i=n),[t.invert(r),t.invert(i)]}}(n):n.invertExtent?function(t){return function(e){const n=t.range();let r,i,o,a,u=e[0],s=e[1],l=-1;for(s<u&&(i=u,u=s,s=i),o=0,a=n.length;o<a;++o)n[o]>=u&&n[o]<=s&&(l<0&&(l=o),r=o);if(!(l<0))return u=t.invertExtent(n[l]),s=t.invertExtent(n[r]),[void 0===u[0]?u[1]:u[0],void 0===s[1]?s[0]:s[1]]}}(n):void 0),n.type=t,ep(n)};return r.metadata=Nt(V(n)),r}(t,e,n)),this):rp(t)?Kd.get(t):void 0}function rp(t){return Kd.has(t)}function ip(t,e){const n=Kd.get(t);return n&&n.metadata[e]}function op(t){return ip(t,Wd)}function ap(t){return ip(t,Hd)}function up(t){return ip(t,Yd)}function sp(t){return ip(t,Fd)}function lp(t){return ip(t,Gd)}function cp(t){return ip(t,Rd)}np("identity",(function t(e){var n;function r(t){return null==t||isNaN(t=+t)?n:t}return r.invert=r,r.domain=r.range=function(t){return arguments.length?(e=Array.from(t,Hh),r):e.slice()},r.unknown=function(t){return arguments.length?(n=t,r):n},r.copy=function(){return t(e).unknown(n)},e=arguments.length?Array.from(e,Hh):[0,1],ed(r)})),np(Cd,(function t(){var e=Kh();return e.copy=function(){return Zh(e,t())},Lc.apply(e,arguments),ed(e)}),Wd),np(Fd,(function t(){const e=ld(Qh()).domain([1,10]);return e.copy=()=>Zh(e,t()).base(e.base()),Lc.apply(e,arguments),e}),[Wd,Fd]),np(Sd,yd,Wd),np($d,(function(){return yd.apply(null,arguments).exponent(.5)}),Wd),np(Td,(function t(){var e=hd(Qh());return e.copy=function(){return Zh(e,t()).constant(e.constant())},Lc.apply(e,arguments)}),Wd),np(Bd,(function(){return Lc.apply(xd(jn,In,Rn,zn,xn,mn,pn,hn,fn,ii).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}),[Wd,Vd]),np(Nd,(function(){return Lc.apply(xd(qn,Pn,Un,On,Cn,yn,gn,dn,fn,ai).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}),[Wd,Vd]),np(zd,kd,[Wd,Gd]),np(`${zd}-${Cd}`,kd,[Wd,Gd]),np(`${zd}-${Fd}`,(function t(){var e=ld(bd()).domain([1,10]);return e.copy=function(){return wd(e,t()).base(e.base())},qc.apply(e,arguments)}),[Wd,Gd,Fd]),np(`${zd}-${Sd}`,Ad,[Wd,Gd]),np(`${zd}-${$d}`,(function(){return Ad.apply(null,arguments).exponent(.5)}),[Wd,Gd]),np(`${zd}-${Td}`,(function t(){var e=hd(bd());return e.copy=function(){return wd(e,t()).constant(e.constant())},qc.apply(e,arguments)}),[Wd,Gd]),np(`${Od}-${Cd}`,(function t(){var e=ed(Md()(Gh));return e.copy=function(){return wd(e,t())},qc.apply(e,arguments)}),[Wd,Gd]),np(`${Od}-${Fd}`,(function t(){var e=ld(Md()).domain([.1,1,10]);return e.copy=function(){return wd(e,t()).base(e.base())},qc.apply(e,arguments)}),[Wd,Gd,Fd]),np(`${Od}-${Sd}`,Ed,[Wd,Gd]),np(`${Od}-${$d}`,(function(){return Ed.apply(null,arguments).exponent(.5)}),[Wd,Gd]),np(`${Od}-${Td}`,(function t(){var e=hd(Md());return e.copy=function(){return wd(e,t()).constant(e.constant())},qc.apply(e,arguments)}),[Wd,Gd]),np(Rd,(function t(){var e,n=[],r=[],i=[];function o(){var t=0,e=Math.max(1,r.length);for(i=new Array(e-1);++t<e;)i[t-1]=Fe(n,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:r[se(i,t)]}return a.invertExtent=function(t){var e=r.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:n[0],e<i.length?i[e]:n[n.length-1]]},a.domain=function(t){if(!arguments.length)return n.slice();n=[];for(let e of t)null==e||isNaN(e=+e)||n.push(e);return n.sort(te),o()},a.range=function(t){return arguments.length?(r=Array.from(t),o()):r.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(n).range(r).unknown(e)},Lc.apply(a,arguments)}),[Yd,Rd]),np(Ud,(function t(){var e,n=0,r=1,i=1,o=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[se(o,t,0,i)]:e}function s(){var t=-1;for(o=new Array(i);++t<i;)o[t]=((t+1)*r-(t-i)*n)/(i+1);return u}return u.domain=function(t){return arguments.length?([n,r]=t,n=+n,r=+r,s()):[n,r]},u.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,s()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[n,o[0]]:e>=i?[o[i-1],r]:[o[e-1],o[e]]},u.unknown=function(t){return arguments.length?(e=t,u):u},u.thresholds=function(){return o.slice()},u.copy=function(){return t().domain([n,r]).range(a).unknown(e)},Lc.apply(ed(u),arguments)}),Yd),np(Ld,(function t(){var e,n=[.5],r=[0,1],i=1;function o(t){return null!=t&&t<=t?r[se(n,t,0,i)]:e}return o.domain=function(t){return arguments.length?(n=Array.from(t),i=Math.min(n.length,r.length-1),o):n.slice()},o.range=function(t){return arguments.length?(r=Array.from(t),i=Math.min(n.length,r.length-1),o):r.slice()},o.invertExtent=function(t){var e=r.indexOf(t);return[n[e-1],n[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(n).range(r).unknown(e)},Lc.apply(o,arguments)}),Yd),np(Id,(function t(){let e=[],n=[];function r(t){return null==t||t!=t?void 0:n[(se(e,t)-1)%n.length]}return r.domain=function(t){return arguments.length?(e=function(t){return Zd.call(t,S)}(t),r):e.slice()},r.range=function(t){return arguments.length?(n=Qd.call(t),r):n.slice()},r.tickFormat=function(t,n){return td(e[0],F(e),null==t?10:t,n)},r.copy=function(){return t().domain(r.domain()).range(r.range())},r}),[Hd,Yd]),np(qd,jc,Hd),np(jd,Xd,Hd),np(Pd,(function(){return Jd(Xd().paddingInner(1))}),Hd);const fp=["clamp","base","constant","exponent"];function hp(t,e){const n=e[0],r=F(e)-n;return function(e){return t(n+e*r)}}function dp(t,e,n){return Ih(mp(e||"rgb",n),t)}function pp(t,e){const n=new Array(e),r=e+1;for(let i=0;i<e;)n[i]=t(++i/r);return n}function gp(t,e,n){const r=n-e;let i,o,a;return r&&Number.isFinite(r)?(i=(o=t.type).indexOf("-"),o=i<0?o:o.slice(i+1),a=np(o)().domain([e,n]).range([0,1]),fp.forEach((e=>t[e]?a[e](t[e]()):0)),a):rt(.5)}function mp(t,e){const n=Wh[function(t){return"interpolate"+t.toLowerCase().split("-").map((t=>t[0].toUpperCase()+t.slice(1))).join("")}(t)];return null!=e&&n&&n.gamma?n.gamma(e):n}function yp(t){const e=t.length/6|0,n=new Array(e);for(let r=0;r<e;)n[r]="#"+t.slice(6*r,6*++r);return n}function vp(t,e){for(const n in t)xp(n,e(t[n]))}const _p={};function xp(t,e){return t=t&&t.toLowerCase(),arguments.length>1?(_p[t]=e,this):_p[t]}vp({category10:"1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf",category20:"1f77b4aec7e8ff7f0effbb782ca02c98df8ad62728ff98969467bdc5b0d58c564bc49c94e377c2f7b6d27f7f7fc7c7c7bcbd22dbdb8d17becf9edae5",category20b:"393b795254a36b6ecf9c9ede6379398ca252b5cf6bcedb9c8c6d31bd9e39e7ba52e7cb94843c39ad494ad6616be7969c7b4173a55194ce6dbdde9ed6",category20c:"3182bd6baed69ecae1c6dbefe6550dfd8d3cfdae6bfdd0a231a35474c476a1d99bc7e9c0756bb19e9ac8bcbddcdadaeb636363969696bdbdbdd9d9d9",tableau10:"4c78a8f58518e4575672b7b254a24beeca3bb279a2ff9da69d755dbab0ac",tableau20:"4c78a89ecae9f58518ffbf7954a24b88d27ab79a20f2cf5b43989483bcb6e45756ff9d9879706ebab0acd67195fcbfd2b279a2d6a5c99e765fd8b5a5",accent:"7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666",dark2:"1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666",paired:"a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928",pastel1:"fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2",pastel2:"b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc",set1:"e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999",set2:"66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3",set3:"8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"},yp),vp({blues:"cfe1f2bed8eca8cee58fc1de74b2d75ba3cf4592c63181bd206fb2125ca40a4a90",greens:"d3eecdc0e6baabdda594d3917bc77d60ba6c46ab5e329a512089430e7735036429",greys:"e2e2e2d4d4d4c4c4c4b1b1b19d9d9d8888887575756262624d4d4d3535351e1e1e",oranges:"fdd8b3fdc998fdb87bfda55efc9244f87f2cf06b18e4580bd14904b93d029f3303",purples:"e2e1efd4d4e8c4c5e0b4b3d6a3a0cc928ec3827cb97566ae684ea25c3696501f8c",reds:"fdc9b4fcb49afc9e80fc8767fa7051f6573fec3f2fdc2a25c81b1db21218970b13",blueGreen:"d5efedc1e8e0a7ddd18bd2be70c6a958ba9144ad77319c5d2089460e7736036429",bluePurple:"ccddecbad0e4a8c2dd9ab0d4919cc98d85be8b6db28a55a6873c99822287730f71",greenBlue:"d3eecec5e8c3b1e1bb9bd8bb82cec269c2ca51b2cd3c9fc7288abd1675b10b60a1",orangeRed:"fddcaffdcf9bfdc18afdad77fb9562f67d53ee6545e24932d32d1ebf130da70403",purpleBlue:"dbdaebc8cee4b1c3de97b7d87bacd15b9fc93a90c01e7fb70b70ab056199045281",purpleBlueGreen:"dbd8eac8cee4b0c3de93b7d872acd1549fc83892bb1c88a3097f8702736b016353",purpleRed:"dcc9e2d3b3d7ce9eccd186c0da6bb2e14da0e23189d91e6fc61159ab07498f023a",redPurple:"fccfccfcbec0faa9b8f98faff571a5ec539ddb3695c41b8aa908808d0179700174",yellowGreen:"e4f4acd1eca0b9e2949ed68880c97c62bb6e47aa5e3297502083440e723b036034",yellowOrangeBrown:"feeaa1fedd84fecc63feb746fca031f68921eb7215db5e0bc54c05ab3d038f3204",yellowOrangeRed:"fee087fed16ffebd59fea849fd903efc7335f9522bee3423de1b20ca0b22af0225",blueOrange:"134b852f78b35da2cb9dcae1d2e5eff2f0ebfce0bafbbf74e8932fc5690d994a07",brownBlueGreen:"704108a0651ac79548e3c78af3e6c6eef1eac9e9e48ed1c74da79e187a72025147",purpleGreen:"5b1667834792a67fb6c9aed3e6d6e8eff0efd9efd5aedda971bb75368e490e5e29",purpleOrange:"4114696647968f83b7b9b4d6dadbebf3eeeafce0bafbbf74e8932fc5690d994a07",redBlue:"8c0d25bf363adf745ef4ae91fbdbc9f2efeed2e5ef9dcae15da2cb2f78b3134b85",redGrey:"8c0d25bf363adf745ef4ae91fcdccbfaf4f1e2e2e2c0c0c0969696646464343434",yellowGreenBlue:"eff9bddbf1b4bde5b594d5b969c5be45b4c22c9ec02182b82163aa23479c1c3185",redYellowBlue:"a50026d4322cf16e43fcac64fedd90faf8c1dcf1ecabd6e875abd04a74b4313695",redYellowGreen:"a50026d4322cf16e43fcac63fedd8df9f7aed7ee8ea4d86e64bc6122964f006837",pinkYellowGreen:"8e0152c0267edd72adf0b3d6faddedf5f3efe1f2cab6de8780bb474f9125276419",spectral:"9e0142d13c4bf0704afcac63fedd8dfbf8b0e0f3a1a9dda269bda94288b55e4fa2",viridis:"440154470e61481a6c482575472f7d443a834144873d4e8a39568c35608d31688e2d708e2a788e27818e23888e21918d1f988b1fa08822a8842ab07f35b77943bf7154c56866cc5d7ad1518fd744a5db36bcdf27d2e21be9e51afde725",magma:"0000040404130b0924150e3720114b2c11603b0f704a107957157e651a80721f817f24828c29819a2e80a8327db6377ac43c75d1426fde4968e95462f1605df76f5cfa7f5efc8f65fe9f6dfeaf78febf84fece91fddea0fcedaffcfdbf",inferno:"0000040403130c0826170c3b240c4f330a5f420a68500d6c5d126e6b176e781c6d86216b932667a12b62ae305cbb3755c73e4cd24644dd513ae65c30ed6925f3771af8850ffb9506fca50afcb519fac62df6d645f2e661f3f484fcffa4",plasma:"0d088723069033059742039d5002a25d01a66a00a87801a88405a7900da49c179ea72198b12a90ba3488c33d80cb4779d35171da5a69e16462e76e5bed7953f2834cf68f44fa9a3dfca636fdb32ffec029fcce25f9dc24f5ea27f0f921",cividis:"00205100235800265d002961012b65042e670831690d346b11366c16396d1c3c6e213f6e26426e2c456e31476e374a6e3c4d6e42506e47536d4c566d51586e555b6e5a5e6e5e616e62646f66676f6a6a706e6d717270717573727976737c79747f7c75827f758682768985778c8877908b78938e789691789a94789e9778a19b78a59e77a9a177aea575b2a874b6ab73bbaf71c0b26fc5b66dc9b96acebd68d3c065d8c462ddc85fe2cb5ce7cf58ebd355f0d652f3da4ff7de4cfae249fce647",rainbow:"6e40aa883eb1a43db3bf3cafd83fa4ee4395fe4b83ff576eff6659ff7847ff8c38f3a130e2b72fcfcc36bee044aff05b8ff4576ff65b52f6673af27828ea8d1ddfa319d0b81cbecb23abd82f96e03d82e14c6edb5a5dd0664dbf6e40aa",sinebow:"ff4040fc582af47218e78d0bd5a703bfbf00a7d5038de70b72f41858fc2a40ff402afc5818f4720be78d03d5a700bfbf03a7d50b8de71872f42a58fc4040ff582afc7218f48d0be7a703d5bf00bfd503a7e70b8df41872fc2a58ff4040",turbo:"23171b32204a3e2a71453493493eae4b49c54a53d7485ee44569ee4074f53c7ff8378af93295f72e9ff42ba9ef28b3e926bce125c5d925cdcf27d5c629dcbc2de3b232e9a738ee9d3ff39347f68950f9805afc7765fd6e70fe667cfd5e88fc5795fb51a1f84badf545b9f140c5ec3cd0e637dae034e4d931ecd12ef4c92bfac029ffb626ffad24ffa223ff9821ff8d1fff821dff771cfd6c1af76118f05616e84b14df4111d5380fcb2f0dc0260ab61f07ac1805a313029b0f00950c00910b00",browns:"eedbbdecca96e9b97ae4a865dc9856d18954c7784cc0673fb85536ad44339f3632",tealBlues:"bce4d89dd3d181c3cb65b3c245a2b9368fae347da0306a932c5985",teals:"bbdfdfa2d4d58ac9c975bcbb61b0af4da5a43799982b8b8c1e7f7f127273006667",warmGreys:"dcd4d0cec5c1c0b8b4b3aaa7a59c9998908c8b827f7e7673726866665c5a59504e",goldGreen:"f4d166d5ca60b6c35c98bb597cb25760a6564b9c533f8f4f33834a257740146c36",goldOrange:"f4d166f8be5cf8aa4cf5983bf3852aef701be2621fd65322c54923b142239e3a26",goldRed:"f4d166f6be59f9aa51fc964ef6834bee734ae56249db5247cf4244c43141b71d3e",lightGreyRed:"efe9e6e1dad7d5cbc8c8bdb9bbaea9cd967ddc7b43e15f19df4011dc000b",lightGreyTeal:"e4eaead6dcddc8ced2b7c2c7a6b4bc64b0bf22a6c32295c11f85be1876bc",lightMulti:"e0f1f2c4e9d0b0de9fd0e181f6e072f6c053f3993ef77440ef4a3c",lightOrange:"f2e7daf7d5baf9c499fab184fa9c73f68967ef7860e8645bde515bd43d5b",lightTealBlue:"e3e9e0c0dccf9aceca7abfc859afc0389fb9328dad2f7ca0276b95255988",darkBlue:"3232322d46681a5c930074af008cbf05a7ce25c0dd38daed50f3faffffff",darkGold:"3c3c3c584b37725e348c7631ae8b2bcfa424ecc31ef9de30fff184ffffff",darkGreen:"3a3a3a215748006f4d048942489e4276b340a6c63dd2d836ffeb2cffffaa",darkMulti:"3737371f5287197d8c29a86995ce3fffe800ffffff",darkRed:"3434347036339e3c38cc4037e75d1eec8620eeab29f0ce32ffeb2c"},(t=>dp(yp(t))));const bp="symbol",wp="discrete",kp=t=>k(t)?t.map((t=>String(t))):String(t),Ap=(t,e)=>t[1]-e[1],Mp=(t,e)=>e[1]-t[1];function Ep(t,e,n){let r;return vt(e)&&(t.bins&&(e=Math.max(e,t.bins.length)),null!=n&&(e=Math.min(e,Math.floor(Ct(t.domain())/n||1)))),A(e)&&(r=e.step,e=e.interval),xt(e)&&(e=t.type===Bd?Sr(e):t.type==Nd?$r(e):u("Only time and utc scales accept interval strings."),r&&(e=e.every(r))),e}function Dp(t,e,n){let r=t.range(),i=r[0],o=F(r),a=Ap;if(i>o&&(r=o,o=i,i=r,a=Mp),i=Math.floor(i),o=Math.ceil(o),e=e.map((e=>[e,t(e)])).filter((t=>i<=t[1]&&t[1]<=o)).sort(a).map((t=>t[0])),n>0&&e.length>1){const t=[e[0],F(e)];for(;e.length>n&&e.length>=3;)e=e.filter(((t,e)=>!(e%2)));e.length<3&&(e=t)}return e}function Cp(t,e){return t.bins?Dp(t,t.bins):t.ticks?t.ticks(e):t.domain()}function Fp(t,e,n,r,i,o){const a=e.type;let u=kp;if(a===Bd||i===Bd)u=t.timeFormat(r);else if(a===Nd||i===Nd)u=t.utcFormat(r);else if(sp(a)){const i=t.formatFloat(r);if(o||e.bins)u=i;else{const t=Sp(e,n,!1);u=e=>t(e)?i(e):""}}else if(e.tickFormat){const i=e.domain();u=t.formatSpan(i[0],i[i.length-1],n,r)}else r&&(u=t.format(r));return u}function Sp(t,e,n){const r=Cp(t,e),i=t.base(),o=Math.log(i),a=Math.max(1,i*e/r.length),u=t=>{let e=t/Math.pow(i,Math.round(Math.log(t)/o));return e*i<i-.5&&(e*=i),e<=a};return n?r.filter(u):u}const $p={[Rd]:"quantiles",[Ud]:"thresholds",[Ld]:"domain"},Tp={[Rd]:"quantiles",[Ud]:"domain"};function Bp(t,e){return t.bins?function(t){const e=t.slice(0,-1);return e.max=F(t),e}(t.bins):t.type===Fd?Sp(t,e,!0):$p[t.type]?function(t){const e=[-1/0].concat(t);return e.max=1/0,e}(t[$p[t.type]]()):Cp(t,e)}const Np=t=>$p[t.type]||t.bins;function zp(t,e,n,r,i,o,a){const u=Tp[e.type]&&o!==Bd&&o!==Nd?function(t,e,n){const r=e[Tp[e.type]](),i=r.length;let o,a=i>1?r[1]-r[0]:r[0];for(o=1;o<i;++o)a=Math.min(a,r[o]-r[o-1]);return t.formatSpan(0,a,30,n)}(t,e,i):Fp(t,e,n,i,o,a);return r===bp&&Np(e)?Op(u):r===wp?Up(u):Lp(u)}const Op=t=>(e,n,r)=>{const i=Rp(r[n+1],Rp(r.max,1/0)),o=qp(e,t),a=qp(i,t);return o&&a?o+" – "+a:a?"< "+a:"≥ "+o},Rp=(t,e)=>null!=t?t:e,Up=t=>(e,n)=>n?t(e):null,Lp=t=>e=>t(e),qp=(t,e)=>Number.isFinite(t)?e(t):null;function Pp(t,e,n,r){const i=r||e.type;return xt(n)&&function(t){return ip(t,Vd)}(i)&&(n=n.replace(/%a/g,"%A").replace(/%b/g,"%B")),n||i!==Bd?n||i!==Nd?zp(t,e,5,null,n,r,!0):t.utcFormat("%A, %d %B %Y, %X UTC"):t.timeFormat("%A, %d %B %Y, %X")}function jp(t,e,n){n=n||{};const r=Math.max(3,n.maxlen||7),i=Pp(t,e,n.format,n.formatType);if(up(e.type)){const t=Bp(e).slice(1).map(i),n=t.length;return`${n} boundar${1===n?"y":"ies"}: ${t.join(", ")}`}if(ap(e.type)){const t=e.domain(),n=t.length;return`${n} value${1===n?"":"s"}: ${n>r?t.slice(0,r-2).map(i).join(", ")+", ending with "+t.slice(-1).map(i):t.map(i).join(", ")}`}{const t=e.domain();return`values from ${i(t[0])} to ${i(F(t))}`}}let Ip=0;const Wp="p_";function Hp(t){return t&&t.gradient}function Yp(t,e,n){const r=t.gradient;let i=t.id,o="radial"===r?Wp:"";return i||(i=t.id="gradient_"+Ip++,"radial"===r?(t.x1=Gp(t.x1,.5),t.y1=Gp(t.y1,.5),t.r1=Gp(t.r1,0),t.x2=Gp(t.x2,.5),t.y2=Gp(t.y2,.5),t.r2=Gp(t.r2,.5),o=Wp):(t.x1=Gp(t.x1,0),t.y1=Gp(t.y1,0),t.x2=Gp(t.x2,1),t.y2=Gp(t.y2,0))),e[i]=t,"url("+(n||"")+"#"+o+i+")"}function Gp(t,e){return null!=t?t:e}function Vp(t,e){var n,r=[];return n={gradient:"linear",x1:t?t[0]:0,y1:t?t[1]:0,x2:e?e[0]:1,y2:e?e[1]:0,stops:r,stop:function(t,e){return r.push({offset:t,color:e}),n}}}const Xp={basis:{curve:function(t){return new uc(t)}},"basis-closed":{curve:function(t){return new sc(t)}},"basis-open":{curve:function(t){return new lc(t)}},bundle:{curve:fc,tension:"beta",value:.85},cardinal:{curve:pc,tension:"tension",value:0},"cardinal-open":{curve:vc,tension:"tension",value:0},"cardinal-closed":{curve:mc,tension:"tension",value:0},"catmull-rom":{curve:bc,tension:"alpha",value:.5},"catmull-rom-closed":{curve:kc,tension:"alpha",value:.5},"catmull-rom-open":{curve:Mc,tension:"alpha",value:.5},linear:{curve:Kl},"linear-closed":{curve:function(t){return new Ec(t)}},monotone:{horizontal:function(t){return new Tc(t)},vertical:function(t){return new $c(t)}},natural:{curve:function(t){return new Nc(t)}},step:{curve:function(t){return new Oc(t,.5)}},"step-after":{curve:function(t){return new Oc(t,1)}},"step-before":{curve:function(t){return new Oc(t,0)}}};function Jp(t,e,n){var r=lt(Xp,t)&&Xp[t],i=null;return r&&(i=r.curve||r[e||"vertical"],r.tension&&null!=n&&(i=i[r.tension](n))),i}const Zp={m:2,l:2,h:1,v:1,z:0,c:6,s:4,q:4,t:2,a:7},Qp=/[mlhvzcsqta]([^mlhvzcsqta]+|$)/gi,Kp=/^[+-]?(([0-9]*\.[0-9]+)|([0-9]+\.)|([0-9]+))([eE][+-]?[0-9]+)?/,tg=/^((\s+,?\s*)|(,\s*))/,eg=/^[01]/;function ng(t){const e=[];return(t.match(Qp)||[]).forEach((t=>{let n=t[0];const r=n.toLowerCase(),i=Zp[r],o=function(t,e,n){const r=[];for(let i=0;e&&i<n.length;)for(let o=0;o<e;++o){const e="a"!==t||3!==o&&4!==o?Kp:eg,a=n.slice(i).match(e);if(null===a)throw Error("Invalid SVG path, incorrect parameter type");i+=a[0].length,r.push(+a[0]);const u=n.slice(i).match(tg);null!==u&&(i+=u[0].length)}return r}(r,i,t.slice(1).trim()),a=o.length;if(a<i||a&&a%i!=0)throw Error("Invalid SVG path, incorrect parameter count");if(e.push([n,...o.slice(0,i)]),a!==i){"m"===r&&(n="M"===n?"L":"l");for(let t=i;t<a;t+=i)e.push([n,...o.slice(t,t+i)])}})),e}const rg=Math.PI/180,ig=1e-14,og=Math.PI/2,ag=2*Math.PI,ug=Math.sqrt(3)/2;var sg={},lg={},cg=[].join;function fg(t){const e=cg.call(t);if(lg[e])return lg[e];var n=t[0],r=t[1],i=t[2],o=t[3],a=t[4],u=t[5],s=t[6],l=t[7];const c=l*a,f=-s*u,h=s*a,d=l*u,p=Math.cos(i),g=Math.sin(i),m=Math.cos(o),y=Math.sin(o),v=.5*(o-i),_=Math.sin(.5*v),x=8/3*_*_/Math.sin(v),b=n+p-x*g,w=r+g+x*p,k=n+m,A=r+y,M=k+x*y,E=A-x*m;return lg[e]=[c*b+f*w,h*b+d*w,c*M+f*E,h*M+d*E,c*k+f*A,h*k+d*A]}const hg=["l",0,0,0,0,0,0,0];function dg(t,e,n){const r=hg[0]=t[0];if("a"===r||"A"===r)hg[1]=e*t[1],hg[2]=n*t[2],hg[3]=t[3],hg[4]=t[4],hg[5]=t[5],hg[6]=e*t[6],hg[7]=n*t[7];else if("h"===r||"H"===r)hg[1]=e*t[1];else if("v"===r||"V"===r)hg[1]=n*t[1];else for(var i=1,o=t.length;i<o;++i)hg[i]=(i%2==1?e:n)*t[i];return hg}function pg(t,e,n,r,i,o){var a,u,s,l,c,f=null,h=0,d=0,p=0,g=0,m=0,y=0;null==n&&(n=0),null==r&&(r=0),null==i&&(i=1),null==o&&(o=i),t.beginPath&&t.beginPath();for(var v=0,_=e.length;v<_;++v){switch(a=e[v],1===i&&1===o||(a=dg(a,i,o)),a[0]){case"l":h+=a[1],d+=a[2],t.lineTo(h+n,d+r);break;case"L":h=a[1],d=a[2],t.lineTo(h+n,d+r);break;case"h":h+=a[1],t.lineTo(h+n,d+r);break;case"H":h=a[1],t.lineTo(h+n,d+r);break;case"v":d+=a[1],t.lineTo(h+n,d+r);break;case"V":d=a[1],t.lineTo(h+n,d+r);break;case"m":m=h+=a[1],y=d+=a[2],t.moveTo(h+n,d+r);break;case"M":m=h=a[1],y=d=a[2],t.moveTo(h+n,d+r);break;case"c":u=h+a[5],s=d+a[6],p=h+a[3],g=d+a[4],t.bezierCurveTo(h+a[1]+n,d+a[2]+r,p+n,g+r,u+n,s+r),h=u,d=s;break;case"C":h=a[5],d=a[6],p=a[3],g=a[4],t.bezierCurveTo(a[1]+n,a[2]+r,p+n,g+r,h+n,d+r);break;case"s":u=h+a[3],s=d+a[4],p=2*h-p,g=2*d-g,t.bezierCurveTo(p+n,g+r,h+a[1]+n,d+a[2]+r,u+n,s+r),p=h+a[1],g=d+a[2],h=u,d=s;break;case"S":u=a[3],s=a[4],p=2*h-p,g=2*d-g,t.bezierCurveTo(p+n,g+r,a[1]+n,a[2]+r,u+n,s+r),h=u,d=s,p=a[1],g=a[2];break;case"q":u=h+a[3],s=d+a[4],p=h+a[1],g=d+a[2],t.quadraticCurveTo(p+n,g+r,u+n,s+r),h=u,d=s;break;case"Q":u=a[3],s=a[4],t.quadraticCurveTo(a[1]+n,a[2]+r,u+n,s+r),h=u,d=s,p=a[1],g=a[2];break;case"t":u=h+a[1],s=d+a[2],null===f[0].match(/[QqTt]/)?(p=h,g=d):"t"===f[0]?(p=2*h-l,g=2*d-c):"q"===f[0]&&(p=2*h-p,g=2*d-g),l=p,c=g,t.quadraticCurveTo(p+n,g+r,u+n,s+r),d=s,p=(h=u)+a[1],g=d+a[2];break;case"T":u=a[1],s=a[2],p=2*h-p,g=2*d-g,t.quadraticCurveTo(p+n,g+r,u+n,s+r),h=u,d=s;break;case"a":gg(t,h+n,d+r,[a[1],a[2],a[3],a[4],a[5],a[6]+h+n,a[7]+d+r]),h+=a[6],d+=a[7];break;case"A":gg(t,h+n,d+r,[a[1],a[2],a[3],a[4],a[5],a[6]+n,a[7]+r]),h=a[6],d=a[7];break;case"z":case"Z":h=m,d=y,t.closePath()}f=a}}function gg(t,e,n,r){const i=function(t,e,n,r,i,o,a,u,s){const l=cg.call(arguments);if(sg[l])return sg[l];const c=a*rg,f=Math.sin(c),h=Math.cos(c),d=h*(u-t)*.5+f*(s-e)*.5,p=h*(s-e)*.5-f*(u-t)*.5;let g=d*d/((n=Math.abs(n))*n)+p*p/((r=Math.abs(r))*r);g>1&&(g=Math.sqrt(g),n*=g,r*=g);const m=h/n,y=f/n,v=-f/r,_=h/r,x=m*u+y*s,b=v*u+_*s,w=m*t+y*e,k=v*t+_*e;let A=1/((w-x)*(w-x)+(k-b)*(k-b))-.25;A<0&&(A=0);let M=Math.sqrt(A);o==i&&(M=-M);const E=.5*(x+w)-M*(k-b),D=.5*(b+k)+M*(w-x),C=Math.atan2(b-D,x-E);let F=Math.atan2(k-D,w-E)-C;F<0&&1===o?F+=ag:F>0&&0===o&&(F-=ag);const S=Math.ceil(Math.abs(F/(og+.001))),$=[];for(let t=0;t<S;++t){const e=C+t*F/S,i=C+(t+1)*F/S;$[t]=[E,D,e,i,n,r,f,h]}return sg[l]=$}(r[5],r[6],r[0],r[1],r[3],r[4],r[2],e,n);for(let e=0;e<i.length;++e){const n=fg(i[e]);t.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5])}}const mg=.5773502691896257,yg={circle:{draw:function(t,e){const n=Math.sqrt(e)/2;t.moveTo(n,0),t.arc(0,0,n,0,ag)}},cross:{draw:function(t,e){var n=Math.sqrt(e)/2,r=n/2.5;t.moveTo(-n,-r),t.lineTo(-n,r),t.lineTo(-r,r),t.lineTo(-r,n),t.lineTo(r,n),t.lineTo(r,r),t.lineTo(n,r),t.lineTo(n,-r),t.lineTo(r,-r),t.lineTo(r,-n),t.lineTo(-r,-n),t.lineTo(-r,-r),t.closePath()}},diamond:{draw:function(t,e){const n=Math.sqrt(e)/2;t.moveTo(-n,0),t.lineTo(0,-n),t.lineTo(n,0),t.lineTo(0,n),t.closePath()}},square:{draw:function(t,e){var n=Math.sqrt(e),r=-n/2;t.rect(r,r,n,n)}},arrow:{draw:function(t,e){var n=Math.sqrt(e)/2,r=n/7,i=n/2.5,o=n/8;t.moveTo(-r,n),t.lineTo(r,n),t.lineTo(r,-o),t.lineTo(i,-o),t.lineTo(0,-n),t.lineTo(-i,-o),t.lineTo(-r,-o),t.closePath()}},wedge:{draw:function(t,e){var n=Math.sqrt(e)/2,r=ug*n,i=r-n*mg,o=n/4;t.moveTo(0,-r-i),t.lineTo(-o,r-i),t.lineTo(o,r-i),t.closePath()}},triangle:{draw:function(t,e){var n=Math.sqrt(e)/2,r=ug*n,i=r-n*mg;t.moveTo(0,-r-i),t.lineTo(-n,r-i),t.lineTo(n,r-i),t.closePath()}},"triangle-up":{draw:function(t,e){var n=Math.sqrt(e)/2,r=ug*n;t.moveTo(0,-r),t.lineTo(-n,r),t.lineTo(n,r),t.closePath()}},"triangle-down":{draw:function(t,e){var n=Math.sqrt(e)/2,r=ug*n;t.moveTo(0,r),t.lineTo(-n,-r),t.lineTo(n,-r),t.closePath()}},"triangle-right":{draw:function(t,e){var n=Math.sqrt(e)/2,r=ug*n;t.moveTo(r,0),t.lineTo(-r,-n),t.lineTo(-r,n),t.closePath()}},"triangle-left":{draw:function(t,e){var n=Math.sqrt(e)/2,r=ug*n;t.moveTo(-r,0),t.lineTo(r,-n),t.lineTo(r,n),t.closePath()}},stroke:{draw:function(t,e){const n=Math.sqrt(e)/2;t.moveTo(-n,0),t.lineTo(n,0)}}};function vg(t){return lt(yg,t)?yg[t]:function(t){if(!lt(_g,t)){const e=ng(t);_g[t]={draw:function(t,n){pg(t,e,0,0,Math.sqrt(n)/2)}}}return _g[t]}(t)}var _g={};const xg=.448084975506;function bg(t){return t.x}function wg(t){return t.y}function kg(t){return t.width}function Ag(t){return t.height}function Mg(t){return"function"==typeof t?t:()=>+t}function Eg(t,e,n){return Math.max(e,Math.min(t,n))}function Dg(){var t=bg,e=wg,n=kg,r=Ag,i=Mg(0),o=i,a=i,u=i,s=null;function l(l,c,f){var h,d=null!=c?c:+t.call(this,l),p=null!=f?f:+e.call(this,l),g=+n.call(this,l),m=+r.call(this,l),y=Math.min(g,m)/2,v=Eg(+i.call(this,l),0,y),_=Eg(+o.call(this,l),0,y),x=Eg(+a.call(this,l),0,y),b=Eg(+u.call(this,l),0,y);if(s||(s=h=Il()),v<=0&&_<=0&&x<=0&&b<=0)s.rect(d,p,g,m);else{var w=d+g,k=p+m;s.moveTo(d+v,p),s.lineTo(w-_,p),s.bezierCurveTo(w-xg*_,p,w,p+xg*_,w,p+_),s.lineTo(w,k-b),s.bezierCurveTo(w,k-xg*b,w-xg*b,k,w-b,k),s.lineTo(d+x,k),s.bezierCurveTo(d+xg*x,k,d,k-xg*x,d,k-x),s.lineTo(d,p+v),s.bezierCurveTo(d,p+xg*v,d+xg*v,p,d+v,p),s.closePath()}if(h)return s=null,h+""||null}return l.x=function(e){return arguments.length?(t=Mg(e),l):t},l.y=function(t){return arguments.length?(e=Mg(t),l):e},l.width=function(t){return arguments.length?(n=Mg(t),l):n},l.height=function(t){return arguments.length?(r=Mg(t),l):r},l.cornerRadius=function(t,e,n,r){return arguments.length?(i=Mg(t),o=null!=e?Mg(e):i,u=null!=n?Mg(n):i,a=null!=r?Mg(r):o,l):i},l.context=function(t){return arguments.length?(s=null==t?null:t,l):s},l}function Cg(){var t,e,n,r,i,o,a,u,s=null;function l(t,e,n){const r=n/2;if(i){var l=a-e,c=t-o;if(l||c){var f=Math.hypot(l,c),h=(l/=f)*u,d=(c/=f)*u,p=Math.atan2(c,l);s.moveTo(o-h,a-d),s.lineTo(t-l*r,e-c*r),s.arc(t,e,r,p-Math.PI,p),s.lineTo(o+h,a+d),s.arc(o,a,u,p,p+Math.PI)}else s.arc(t,e,r,0,ag);s.closePath()}else i=1;o=t,a=e,u=r}function c(o){var a,u,c,f=o.length,h=!1;for(null==s&&(s=c=Il()),a=0;a<=f;++a)!(a<f&&r(u=o[a],a,o))===h&&(h=!h)&&(i=0),h&&l(+t(u,a,o),+e(u,a,o),+n(u,a,o));if(c)return s=null,c+""||null}return c.x=function(e){return arguments.length?(t=e,c):t},c.y=function(t){return arguments.length?(e=t,c):e},c.size=function(t){return arguments.length?(n=t,c):n},c.defined=function(t){return arguments.length?(r=t,c):r},c.context=function(t){return arguments.length?(s=null==t?null:t,c):s},c}function Fg(t,e){return null!=t?t:e}const Sg=t=>t.x||0,$g=t=>t.y||0,Tg=t=>!(!1===t.defined),Bg=function(){var t=Hl,e=Yl,n=Al(0),r=null,i=Gl,o=Vl,a=Xl,u=null,s=Wl(l);function l(){var l,c,f=+t.apply(this,arguments),h=+e.apply(this,arguments),d=i.apply(this,arguments)-Nl,p=o.apply(this,arguments)-Nl,g=Ml(p-d),m=p>d;if(u||(u=l=s()),h<f&&(c=h,h=f,f=c),h>Tl)if(g>zl-Tl)u.moveTo(h*Dl(d),h*Sl(d)),u.arc(0,0,h,d,p,!m),f>Tl&&(u.moveTo(f*Dl(p),f*Sl(p)),u.arc(0,0,f,p,d,m));else{var y,v,_=d,x=p,b=d,w=p,k=g,A=g,M=a.apply(this,arguments)/2,E=M>Tl&&(r?+r.apply(this,arguments):$l(f*f+h*h)),D=Fl(Ml(h-f)/2,+n.apply(this,arguments)),C=D,F=D;if(E>Tl){var S=Ol(E/f*Sl(M)),$=Ol(E/h*Sl(M));(k-=2*S)>Tl?(b+=S*=m?1:-1,w-=S):(k=0,b=w=(d+p)/2),(A-=2*$)>Tl?(_+=$*=m?1:-1,x-=$):(A=0,_=x=(d+p)/2)}var T=h*Dl(_),B=h*Sl(_),N=f*Dl(w),z=f*Sl(w);if(D>Tl){var O,R=h*Dl(x),U=h*Sl(x),L=f*Dl(b),q=f*Sl(b);if(g<Bl)if(O=function(t,e,n,r,i,o,a,u){var s=n-t,l=r-e,c=a-i,f=u-o,h=f*s-c*l;if(!(h*h<Tl))return[t+(h=(c*(e-o)-f*(t-i))/h)*s,e+h*l]}(T,B,L,q,R,U,N,z)){var P=T-O[0],j=B-O[1],I=R-O[0],W=U-O[1],H=1/Sl(function(t){return t>1?0:t<-1?Bl:Math.acos(t)}((P*I+j*W)/($l(P*P+j*j)*$l(I*I+W*W)))/2),Y=$l(O[0]*O[0]+O[1]*O[1]);C=Fl(D,(f-Y)/(H-1)),F=Fl(D,(h-Y)/(H+1))}else C=F=0}A>Tl?F>Tl?(y=Jl(L,q,T,B,h,F,m),v=Jl(R,U,N,z,h,F,m),u.moveTo(y.cx+y.x01,y.cy+y.y01),F<D?u.arc(y.cx,y.cy,F,El(y.y01,y.x01),El(v.y01,v.x01),!m):(u.arc(y.cx,y.cy,F,El(y.y01,y.x01),El(y.y11,y.x11),!m),u.arc(0,0,h,El(y.cy+y.y11,y.cx+y.x11),El(v.cy+v.y11,v.cx+v.x11),!m),u.arc(v.cx,v.cy,F,El(v.y11,v.x11),El(v.y01,v.x01),!m))):(u.moveTo(T,B),u.arc(0,0,h,_,x,!m)):u.moveTo(T,B),f>Tl&&k>Tl?C>Tl?(y=Jl(N,z,R,U,f,-C,m),v=Jl(T,B,L,q,f,-C,m),u.lineTo(y.cx+y.x01,y.cy+y.y01),C<D?u.arc(y.cx,y.cy,C,El(y.y01,y.x01),El(v.y01,v.x01),!m):(u.arc(y.cx,y.cy,C,El(y.y01,y.x01),El(y.y11,y.x11),!m),u.arc(0,0,f,El(y.cy+y.y11,y.cx+y.x11),El(v.cy+v.y11,v.cx+v.x11),m),u.arc(v.cx,v.cy,C,El(v.y11,v.x11),El(v.y01,v.x01),!m))):u.arc(0,0,f,w,b,m):u.lineTo(N,z)}else u.moveTo(0,0);if(u.closePath(),l)return u=null,l+""||null}return l.centroid=function(){var n=(+t.apply(this,arguments)+ +e.apply(this,arguments))/2,r=(+i.apply(this,arguments)+ +o.apply(this,arguments))/2-Bl/2;return[Dl(r)*n,Sl(r)*n]},l.innerRadius=function(e){return arguments.length?(t="function"==typeof e?e:Al(+e),l):t},l.outerRadius=function(t){return arguments.length?(e="function"==typeof t?t:Al(+t),l):e},l.cornerRadius=function(t){return arguments.length?(n="function"==typeof t?t:Al(+t),l):n},l.padRadius=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Al(+t),l):r},l.startAngle=function(t){return arguments.length?(i="function"==typeof t?t:Al(+t),l):i},l.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:Al(+t),l):o},l.padAngle=function(t){return arguments.length?(a="function"==typeof t?t:Al(+t),l):a},l.context=function(t){return arguments.length?(u=null==t?null:t,l):u},l}().startAngle((t=>t.startAngle||0)).endAngle((t=>t.endAngle||0)).padAngle((t=>t.padAngle||0)).innerRadius((t=>t.innerRadius||0)).outerRadius((t=>t.outerRadius||0)).cornerRadius((t=>t.cornerRadius||0)),Ng=rc().x(Sg).y1($g).y0((t=>(t.y||0)+(t.height||0))).defined(Tg),zg=rc().y($g).x1(Sg).x0((t=>(t.x||0)+(t.width||0))).defined(Tg),Og=nc().x(Sg).y($g).defined(Tg),Rg=Dg().x(Sg).y($g).width((t=>t.width||0)).height((t=>t.height||0)).cornerRadius((t=>Fg(t.cornerRadiusTopLeft,t.cornerRadius)||0),(t=>Fg(t.cornerRadiusTopRight,t.cornerRadius)||0),(t=>Fg(t.cornerRadiusBottomRight,t.cornerRadius)||0),(t=>Fg(t.cornerRadiusBottomLeft,t.cornerRadius)||0)),Ug=function(t,e){let n=null,r=Wl(i);function i(){let i;if(n||(n=i=r()),t.apply(this,arguments).draw(n,+e.apply(this,arguments)),i)return n=null,i+""||null}return t="function"==typeof t?t:Al(t||ic),e="function"==typeof e?e:Al(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:Al(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:Al(+t),i):e},i.context=function(t){return arguments.length?(n=null==t?null:t,i):n},i}().type((t=>vg(t.shape||"circle"))).size((t=>Fg(t.size,64))),Lg=Cg().x(Sg).y($g).defined(Tg).size((t=>t.size||1));function qg(t){return t.cornerRadius||t.cornerRadiusTopLeft||t.cornerRadiusTopRight||t.cornerRadiusBottomRight||t.cornerRadiusBottomLeft}function Pg(t,e,n,r){return Rg.context(t)(e,n,r)}var jg=1;function Ig(){jg=1}function Wg(t,e,n){var r=e.clip,i=t._defs,o=e.clip_id||(e.clip_id="clip"+jg++),a=i.clipping[o]||(i.clipping[o]={id:o});return J(r)?a.path=r(null):qg(n)?a.path=Pg(null,n,0,0):(a.width=n.width||0,a.height=n.height||0),"url(#"+o+")"}function Hg(t){this.clear(),t&&this.union(t)}function Yg(t){this.mark=t,this.bounds=this.bounds||new Hg}function Gg(t){Yg.call(this,t),this.items=this.items||[]}function Vg(t){this._pending=0,this._loader=t||da()}function Xg(t){t._pending+=1}function Jg(t){t._pending-=1}function Zg(t,e,n){if(e.stroke&&0!==e.opacity&&0!==e.strokeOpacity){const r=null!=e.strokeWidth?+e.strokeWidth:1;t.expand(r+(n?function(t,e){return t.strokeJoin&&"miter"!==t.strokeJoin?0:e}(e,r):0))}return t}Hg.prototype={clone(){return new Hg(this)},clear(){return this.x1=+Number.MAX_VALUE,this.y1=+Number.MAX_VALUE,this.x2=-Number.MAX_VALUE,this.y2=-Number.MAX_VALUE,this},empty(){return this.x1===+Number.MAX_VALUE&&this.y1===+Number.MAX_VALUE&&this.x2===-Number.MAX_VALUE&&this.y2===-Number.MAX_VALUE},equals(t){return this.x1===t.x1&&this.y1===t.y1&&this.x2===t.x2&&this.y2===t.y2},set(t,e,n,r){return n<t?(this.x2=t,this.x1=n):(this.x1=t,this.x2=n),r<e?(this.y2=e,this.y1=r):(this.y1=e,this.y2=r),this},add(t,e){return t<this.x1&&(this.x1=t),e<this.y1&&(this.y1=e),t>this.x2&&(this.x2=t),e>this.y2&&(this.y2=e),this},expand(t){return this.x1-=t,this.y1-=t,this.x2+=t,this.y2+=t,this},round(){return this.x1=Math.floor(this.x1),this.y1=Math.floor(this.y1),this.x2=Math.ceil(this.x2),this.y2=Math.ceil(this.y2),this},scale(t){return this.x1*=t,this.y1*=t,this.x2*=t,this.y2*=t,this},translate(t,e){return this.x1+=t,this.x2+=t,this.y1+=e,this.y2+=e,this},rotate(t,e,n){const r=this.rotatedPoints(t,e,n);return this.clear().add(r[0],r[1]).add(r[2],r[3]).add(r[4],r[5]).add(r[6],r[7])},rotatedPoints(t,e,n){var{x1:r,y1:i,x2:o,y2:a}=this,u=Math.cos(t),s=Math.sin(t),l=e-e*u+n*s,c=n-e*s-n*u;return[u*r-s*i+l,s*r+u*i+c,u*r-s*a+l,s*r+u*a+c,u*o-s*i+l,s*o+u*i+c,u*o-s*a+l,s*o+u*a+c]},union(t){return t.x1<this.x1&&(this.x1=t.x1),t.y1<this.y1&&(this.y1=t.y1),t.x2>this.x2&&(this.x2=t.x2),t.y2>this.y2&&(this.y2=t.y2),this},intersect(t){return t.x1>this.x1&&(this.x1=t.x1),t.y1>this.y1&&(this.y1=t.y1),t.x2<this.x2&&(this.x2=t.x2),t.y2<this.y2&&(this.y2=t.y2),this},encloses(t){return t&&this.x1<=t.x1&&this.x2>=t.x2&&this.y1<=t.y1&&this.y2>=t.y2},alignsWith(t){return t&&(this.x1==t.x1||this.x2==t.x2||this.y1==t.y1||this.y2==t.y2)},intersects(t){return t&&!(this.x2<t.x1||this.x1>t.x2||this.y2<t.y1||this.y1>t.y2)},contains(t,e){return!(t<this.x1||t>this.x2||e<this.y1||e>this.y2)},width(){return this.x2-this.x1},height(){return this.y2-this.y1}},dt(Gg,Yg),Vg.prototype={pending(){return this._pending},sanitizeURL(t){const e=this;return Xg(e),e._loader.sanitize(t,{context:"href"}).then((t=>(Jg(e),t))).catch((()=>(Jg(e),null)))},loadImage(t){const e=this,n=Uc();return Xg(e),e._loader.sanitize(t,{context:"image"}).then((t=>{const r=t.href;if(!r||!n)throw{url:r};const i=new n,o=lt(t,"crossOrigin")?t.crossOrigin:"anonymous";return null!=o&&(i.crossOrigin=o),i.onload=()=>Jg(e),i.onerror=()=>Jg(e),i.src=r,i})).catch((t=>(Jg(e),{complete:!1,width:0,height:0,src:t&&t.url||""})))},ready(){const t=this;return new Promise((e=>{!function n(r){t.pending()?setTimeout((()=>{n(!0)}),10):e(r)}(!1)}))}};const Qg=ag-1e-8;let Kg,tm,em,nm,rm,im,om,am;const um=(t,e)=>Kg.add(t,e),sm=(t,e)=>um(tm=t,em=e),lm=t=>um(t,Kg.y1),cm=t=>um(Kg.x1,t),fm=(t,e)=>rm*t+om*e,hm=(t,e)=>im*t+am*e,dm=(t,e)=>um(fm(t,e),hm(t,e)),pm=(t,e)=>sm(fm(t,e),hm(t,e));function gm(t,e){return Kg=t,e?(nm=e*rg,rm=am=Math.cos(nm),im=Math.sin(nm),om=-im):(rm=am=1,nm=im=om=0),mm}const mm={beginPath(){},closePath(){},moveTo:pm,lineTo:pm,rect(t,e,n,r){nm?(dm(t+n,e),dm(t+n,e+r),dm(t,e+r),pm(t,e)):(um(t+n,e+r),sm(t,e))},quadraticCurveTo(t,e,n,r){const i=fm(t,e),o=hm(t,e),a=fm(n,r),u=hm(n,r);ym(tm,i,a,lm),ym(em,o,u,cm),sm(a,u)},bezierCurveTo(t,e,n,r,i,o){const a=fm(t,e),u=hm(t,e),s=fm(n,r),l=hm(n,r),c=fm(i,o),f=hm(i,o);vm(tm,a,s,c,lm),vm(em,u,l,f,cm),sm(c,f)},arc(t,e,n,r,i,o){if(r+=nm,i+=nm,tm=n*Math.cos(i)+t,em=n*Math.sin(i)+e,Math.abs(i-r)>Qg)um(t-n,e-n),um(t+n,e+n);else{const a=r=>um(n*Math.cos(r)+t,n*Math.sin(r)+e);let u,s;if(a(r),a(i),i!==r)if((r%=ag)<0&&(r+=ag),(i%=ag)<0&&(i+=ag),i<r&&(o=!o,u=r,r=i,i=u),o)for(i-=ag,u=r-r%og,s=0;s<4&&u>i;++s,u-=og)a(u);else for(u=r-r%og+og,s=0;s<4&&u<i;++s,u+=og)a(u)}}};function ym(t,e,n,r){const i=(t-e)/(t+n-2*e);0<i&&i<1&&r(t+(e-t)*i)}function vm(t,e,n,r,i){const o=r-t+3*e-3*n,a=t+n-2*e,u=t-e;let s,l=0,c=0;Math.abs(o)>ig?(s=a*a+u*o,s>=0&&(s=Math.sqrt(s),l=(-a+s)/o,c=(-a-s)/o)):l=.5*u/a,0<l&&l<1&&i(_m(l,t,e,n,r)),0<c&&c<1&&i(_m(c,t,e,n,r))}function _m(t,e,n,r,i){const o=1-t,a=o*o,u=t*t;return a*o*e+3*a*t*n+3*o*u*r+u*t*i}var xm=(xm=Rc(1,1))?xm.getContext("2d"):null;const bm=new Hg;function wm(t){return function(e,n){if(!xm)return!0;t(xm,e),bm.clear().union(e.bounds).intersect(n).round();const{x1:r,y1:i,x2:o,y2:a}=bm;for(let t=i;t<=a;++t)for(let e=r;e<=o;++e)if(xm.isPointInPath(e,t))return!0;return!1}}function km(t,e){return e.contains(t.x||0,t.y||0)}function Am(t,e){const n=t.x||0,r=t.y||0,i=t.width||0,o=t.height||0;return e.intersects(bm.set(n,r,n+i,r+o))}function Mm(t,e){const n=t.x||0,r=t.y||0;return Em(e,n,r,null!=t.x2?t.x2:n,null!=t.y2?t.y2:r)}function Em(t,e,n,r,i){const{x1:o,y1:a,x2:u,y2:s}=t,l=r-e,c=i-n;let f,h,d,p,g=0,m=1;for(p=0;p<4;++p){if(0===p&&(f=-l,h=-(o-e)),1===p&&(f=l,h=u-e),2===p&&(f=-c,h=-(a-n)),3===p&&(f=c,h=s-n),Math.abs(f)<1e-10&&h<0)return!1;if(d=h/f,f<0){if(d>m)return!1;d>g&&(g=d)}else if(f>0){if(d<g)return!1;d<m&&(m=d)}}return!0}function Dm(t,e){t.globalCompositeOperation=e.blend||"source-over"}function Cm(t,e){return null==t?e:t}function Fm(t,e){const n=e.length;for(let r=0;r<n;++r)t.addColorStop(e[r].offset,e[r].color);return t}function Sm(t,e,n){return Hp(n)?function(t,e,n){const r=n.width(),i=n.height();let o;if("radial"===e.gradient)o=t.createRadialGradient(n.x1+Cm(e.x1,.5)*r,n.y1+Cm(e.y1,.5)*i,Math.max(r,i)*Cm(e.r1,0),n.x1+Cm(e.x2,.5)*r,n.y1+Cm(e.y2,.5)*i,Math.max(r,i)*Cm(e.r2,.5));else{const a=Cm(e.x1,0),u=Cm(e.y1,0),s=Cm(e.x2,1),l=Cm(e.y2,0);if(a!==s&&u!==l&&r!==i){const n=Rc(Math.ceil(r),Math.ceil(i)),o=n.getContext("2d");return o.scale(r,i),o.fillStyle=Fm(o.createLinearGradient(a,u,s,l),e.stops),o.fillRect(0,0,r,i),t.createPattern(n,"no-repeat")}o=t.createLinearGradient(n.x1+a*r,n.y1+u*i,n.x1+s*r,n.y1+l*i)}return Fm(o,e.stops)}(t,n,e.bounds):n}function $m(t,e,n){return(n*=null==e.fillOpacity?1:e.fillOpacity)>0&&(t.globalAlpha=n,t.fillStyle=Sm(t,e,e.fill),!0)}var Tm=[];function Bm(t,e,n){var r=null!=(r=e.strokeWidth)?r:1;return!(r<=0)&&((n*=null==e.strokeOpacity?1:e.strokeOpacity)>0&&(t.globalAlpha=n,t.strokeStyle=Sm(t,e,e.stroke),t.lineWidth=r,t.lineCap=e.strokeCap||"butt",t.lineJoin=e.strokeJoin||"miter",t.miterLimit=e.strokeMiterLimit||10,t.setLineDash&&(t.setLineDash(e.strokeDash||Tm),t.lineDashOffset=e.strokeDashOffset||0),!0))}function Nm(t,e){return t.zindex-e.zindex||t.index-e.index}function zm(t){if(!t.zdirty)return t.zitems;var e,n,r,i=t.items,o=[];for(n=0,r=i.length;n<r;++n)(e=i[n]).index=n,e.zindex&&o.push(e);return t.zdirty=!1,t.zitems=o.sort(Nm)}function Om(t,e){var n,r,i=t.items;if(!i||!i.length)return;const o=zm(t);if(o&&o.length){for(n=0,r=i.length;n<r;++n)i[n].zindex||e(i[n]);i=o}for(n=0,r=i.length;n<r;++n)e(i[n])}function Rm(t,e){var n,r,i=t.items;if(!i||!i.length)return null;const o=zm(t);for(o&&o.length&&(i=o),r=i.length;--r>=0;)if(n=e(i[r]))return n;if(i===o)for(r=(i=t.items).length;--r>=0;)if(!i[r].zindex&&(n=e(i[r])))return n;return null}function Um(t){return function(e,n,r){Om(n,(n=>{r&&!r.intersects(n.bounds)||qm(t,e,n,n)}))}}function Lm(t){return function(e,n,r){!n.items.length||r&&!r.intersects(n.bounds)||qm(t,e,n.items[0],n.items)}}function qm(t,e,n,r){var i=null==n.opacity?1:n.opacity;0!==i&&(t(e,r)||(Dm(e,n),n.fill&&$m(e,n,i)&&e.fill(),n.stroke&&Bm(e,n,i)&&e.stroke()))}function Pm(t){return t=t||p,function(e,n,r,i,o,a){return r*=e.pixelRatio,i*=e.pixelRatio,Rm(n,(n=>{const u=n.bounds;if((!u||u.contains(o,a))&&u)return t(e,n,r,i,o,a)?n:void 0}))}}function jm(t,e){return function(n,r,i,o){var a,u,s=Array.isArray(r)?r[0]:r,l=null==e?s.fill:e,c=s.stroke&&n.isPointInStroke;return c&&(a=s.strokeWidth,u=s.strokeCap,n.lineWidth=null!=a?a:1,n.lineCap=null!=u?u:"butt"),!t(n,r)&&(l&&n.isPointInPath(i,o)||c&&n.isPointInStroke(i,o))}}function Im(t){return Pm(jm(t))}function Wm(t,e){return"translate("+t+","+e+")"}function Hm(t){return"rotate("+t+")"}function Ym(t){return Wm(t.x||0,t.y||0)}function Gm(t,e,n){function r(t,n){var r=n.x||0,i=n.y||0,o=n.angle||0;t.translate(r,i),o&&t.rotate(o*=rg),t.beginPath(),e(t,n),o&&t.rotate(-o),t.translate(-r,-i)}return{type:t,tag:"path",nested:!1,attr:function(t,n){t("transform",function(t){return Wm(t.x||0,t.y||0)+(t.angle?" "+Hm(t.angle):"")}(n)),t("d",e(null,n))},bound:function(t,n){return e(gm(t,n.angle),n),Zg(t,n).translate(n.x||0,n.y||0)},draw:Um(r),pick:Im(r),isect:n||wm(r)}}var Vm=Gm("arc",(function(t,e){return Bg.context(t)(e)}));function Xm(t,e,n){function r(t,n){t.beginPath(),e(t,n)}const i=jm(r);return{type:t,tag:"path",nested:!0,attr:function(t,n){var r=n.mark.items;r.length&&t("d",e(null,r))},bound:function(t,n){var r=n.items;return 0===r.length?t:(e(gm(t),r),Zg(t,r[0]))},draw:Lm(r),pick:function(t,e,n,r,o,a){var u=e.items,s=e.bounds;return!u||!u.length||s&&!s.contains(o,a)?null:(n*=t.pixelRatio,r*=t.pixelRatio,i(t,u,n,r)?u[0]:null)},isect:km,tip:n}}var Jm=Xm("area",(function(t,e){const n=e[0],r=n.interpolate||"linear";return("horizontal"===n.orient?zg:Ng).curve(Jp(r,n.orient,n.tension)).context(t)(e)}),(function(t,e){for(var n,r,i="horizontal"===t[0].orient?e[1]:e[0],o="horizontal"===t[0].orient?"y":"x",a=t.length,u=1/0;--a>=0;)!1!==t[a].defined&&(r=Math.abs(t[a][o]-i))<u&&(u=r,n=t[a]);return n}));function Zm(t,e){t.beginPath(),qg(e)?Pg(t,e,0,0):t.rect(0,0,e.width||0,e.height||0),t.clip()}function Qm(t){const e=Cm(t.strokeWidth,1);return null!=t.strokeOffset?t.strokeOffset:t.stroke&&e>.5&&e<1.5?.5-Math.abs(e-1):0}function Km(t,e){const n=Qm(e);t("d",Pg(null,e,n,n))}function ty(t,e,n,r){const i=Qm(e);t.beginPath(),Pg(t,e,(n||0)+i,(r||0)+i)}const ey=jm(ty),ny=jm(ty,!1),ry=jm(ty,!0);var iy={type:"group",tag:"g",nested:!1,attr:function(t,e){t("transform",Ym(e))},bound:function(t,e){if(!e.clip&&e.items){const n=e.items,r=n.length;for(let e=0;e<r;++e)t.union(n[e].bounds)}return(e.clip||e.width||e.height)&&!e.noBound&&t.add(0,0).add(e.width||0,e.height||0),Zg(t,e),t.translate(e.x||0,e.y||0)},draw:function(t,e,n){Om(e,(e=>{const r=e.x||0,i=e.y||0,o=e.strokeForeground,a=null==e.opacity?1:e.opacity;(e.stroke||e.fill)&&a&&(ty(t,e,r,i),Dm(t,e),e.fill&&$m(t,e,a)&&t.fill(),e.stroke&&!o&&Bm(t,e,a)&&t.stroke()),t.save(),t.translate(r,i),e.clip&&Zm(t,e),n&&n.translate(-r,-i),Om(e,(e=>{this.draw(t,e,n)})),n&&n.translate(r,i),t.restore(),o&&e.stroke&&a&&(ty(t,e,r,i),Dm(t,e),Bm(t,e,a)&&t.stroke())}))},pick:function(t,e,n,r,i,o){if(e.bounds&&!e.bounds.contains(i,o)||!e.items)return null;const a=n*t.pixelRatio,u=r*t.pixelRatio;return Rm(e,(s=>{let l,c,f;const h=s.bounds;if(h&&!h.contains(i,o))return;c=s.x||0,f=s.y||0;const d=c+(s.width||0),p=f+(s.height||0),g=s.clip;if(g&&(i<c||i>d||o<f||o>p))return;if(t.save(),t.translate(c,f),c=i-c,f=o-f,g&&qg(s)&&!ry(t,s,a,u))return t.restore(),null;const m=s.strokeForeground,y=!1!==e.interactive;return y&&m&&s.stroke&&ny(t,s,a,u)?(t.restore(),s):(l=Rm(s,(t=>function(t,e,n){return(!1!==t.interactive||"group"===t.marktype)&&t.bounds&&t.bounds.contains(e,n)}(t,c,f)?this.pick(t,n,r,c,f):null)),!l&&y&&(s.fill||!m&&s.stroke)&&ey(t,s,a,u)&&(l=s),t.restore(),l||null)}))},isect:Am,content:function(t,e,n){t("clip-path",e.clip?Wg(n,e,e):null)},background:function(t,e){t("class","background"),t("aria-hidden",!0),Km(t,e)},foreground:function(t,e){t("class","foreground"),t("aria-hidden",!0),e.strokeForeground?Km(t,e):t("d","")}},oy={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",version:"1.1"};function ay(t,e){var n=t.image;return(!n||t.url&&t.url!==n.url)&&(n={complete:!1,width:0,height:0},e.loadImage(t.url).then((e=>{t.image=e,t.image.url=t.url}))),n}function uy(t,e){return null!=t.width?t.width:e&&e.width?!1!==t.aspect&&t.height?t.height*e.width/e.height:e.width:0}function sy(t,e){return null!=t.height?t.height:e&&e.height?!1!==t.aspect&&t.width?t.width*e.height/e.width:e.height:0}function ly(t,e){return"center"===t?e/2:"right"===t?e:0}function cy(t,e){return"middle"===t?e/2:"bottom"===t?e:0}var fy={type:"image",tag:"image",nested:!1,attr:function(t,e,n){const r=ay(e,n),i=uy(e,r),o=sy(e,r),a=(e.x||0)-ly(e.align,i),u=(e.y||0)-cy(e.baseline,o);t("href",!r.src&&r.toDataURL?r.toDataURL():r.src||"",oy["xmlns:xlink"],"xlink:href"),t("transform",Wm(a,u)),t("width",i),t("height",o),t("preserveAspectRatio",!1===e.aspect?"none":"xMidYMid")},bound:function(t,e){const n=e.image,r=uy(e,n),i=sy(e,n),o=(e.x||0)-ly(e.align,r),a=(e.y||0)-cy(e.baseline,i);return t.set(o,a,o+r,a+i)},draw:function(t,e,n){Om(e,(e=>{if(n&&!n.intersects(e.bounds))return;const r=ay(e,this);let i=uy(e,r),o=sy(e,r);if(0===i||0===o)return;let a,u,s,l,c=(e.x||0)-ly(e.align,i),f=(e.y||0)-cy(e.baseline,o);!1!==e.aspect&&(u=r.width/r.height,s=e.width/e.height,u==u&&s==s&&u!==s&&(s<u?(l=i/u,f+=(o-l)/2,o=l):(l=o*u,c+=(i-l)/2,i=l))),(r.complete||r.toDataURL)&&(Dm(t,e),t.globalAlpha=null!=(a=e.opacity)?a:1,t.imageSmoothingEnabled=!1!==e.smooth,t.drawImage(r,c,f,i,o))}))},pick:Pm(),isect:p,get:ay,xOffset:ly,yOffset:cy},hy=Xm("line",(function(t,e){const n=e[0],r=n.interpolate||"linear";return Og.curve(Jp(r,n.orient,n.tension)).context(t)(e)}),(function(t,e){for(var n,r,i=Math.pow(t[0].strokeWidth||1,2),o=t.length;--o>=0;)if(!1!==t[o].defined&&(n=t[o].x-e[0])*n+(r=t[o].y-e[1])*r<i)return t[o];return null}));function dy(t,e){var n=e.path;if(null==n)return!0;var r=e.x||0,i=e.y||0,o=e.scaleX||1,a=e.scaleY||1,u=(e.angle||0)*rg,s=e.pathCache;s&&s.path===n||((e.pathCache=s=ng(n)).path=n),u&&t.rotate&&t.translate?(t.translate(r,i),t.rotate(u),pg(t,s,0,0,o,a),t.rotate(-u),t.translate(-r,-i)):pg(t,s,r,i,o,a)}var py={type:"path",tag:"path",nested:!1,attr:function(t,e){var n=e.scaleX||1,r=e.scaleY||1;1===n&&1===r||t("vector-effect","non-scaling-stroke"),t("transform",function(t){return Wm(t.x||0,t.y||0)+(t.angle?" "+Hm(t.angle):"")+(t.scaleX||t.scaleY?" "+function(t,e){return"scale("+t+","+e+")"}(t.scaleX||1,t.scaleY||1):"")}(e)),t("d",e.path)},bound:function(t,e){return dy(gm(t,e.angle),e)?t.set(0,0,0,0):Zg(t,e,!0)},draw:Um(dy),pick:Im(dy),isect:wm(dy)};function gy(t,e){t.beginPath(),Pg(t,e)}var my={type:"rect",tag:"path",nested:!1,attr:function(t,e){t("d",Pg(null,e))},bound:function(t,e){var n,r;return Zg(t.set(n=e.x||0,r=e.y||0,n+e.width||0,r+e.height||0),e)},draw:Um(gy),pick:Im(gy),isect:Am};function yy(t,e,n){var r,i,o,a;return!(!e.stroke||!Bm(t,e,n))&&(r=e.x||0,i=e.y||0,o=null!=e.x2?e.x2:r,a=null!=e.y2?e.y2:i,t.beginPath(),t.moveTo(r,i),t.lineTo(o,a),!0)}var vy={type:"rule",tag:"line",nested:!1,attr:function(t,e){t("transform",Ym(e)),t("x2",null!=e.x2?e.x2-(e.x||0):0),t("y2",null!=e.y2?e.y2-(e.y||0):0)},bound:function(t,e){var n,r;return Zg(t.set(n=e.x||0,r=e.y||0,null!=e.x2?e.x2:n,null!=e.y2?e.y2:r),e)},draw:function(t,e,n){Om(e,(e=>{if(!n||n.intersects(e.bounds)){var r=null==e.opacity?1:e.opacity;r&&yy(t,e,r)&&(Dm(t,e),t.stroke())}}))},pick:Pm((function(t,e,n,r){return!!t.isPointInStroke&&(yy(t,e,1)&&t.isPointInStroke(n,r))})),isect:Mm},_y=Gm("shape",(function(t,e){return(e.mark.shape||e.shape).context(t)(e)})),xy=Gm("symbol",(function(t,e){return Ug.context(t)(e)}),km);const by=At();var wy={height:Cy,measureWidth:Ey,estimateWidth:Ay,width:Ay,canvas:ky};function ky(t){wy.width=t&&xm?Ey:Ay}function Ay(t,e){return My(Ty(t,e),Cy(t))}function My(t,e){return~~(.8*t.length*e)}function Ey(t,e){return Cy(t)<=0||!(e=Ty(t,e))?0:Dy(e,Ny(t))}function Dy(t,e){const n=`(${e}) ${t}`;let r=by.get(n);return void 0===r&&(xm.font=e,r=xm.measureText(t).width,by.set(n,r)),r}function Cy(t){return null!=t.fontSize?+t.fontSize||0:11}function Fy(t){return null!=t.lineHeight?t.lineHeight:Cy(t)+2}function Sy(t){return e=t.lineBreak&&t.text&&!k(t.text)?t.text.split(t.lineBreak):t.text,k(e)?e.length>1?e:e[0]:e;var e}function $y(t){const e=Sy(t);return(k(e)?e.length-1:0)*Fy(t)}function Ty(t,e){const n=null==e?"":(e+"").trim();return t.limit>0&&n.length?function(t,e){var n=+t.limit,r=function(t){if(wy.width===Ey){const e=Ny(t);return t=>Dy(t,e)}{const e=Cy(t);return t=>My(t,e)}}(t);if(r(e)<n)return e;var i,o=t.ellipsis||"…",a="rtl"===t.dir,u=0,s=e.length;if(n-=r(o),a){for(;u<s;)i=u+s>>>1,r(e.slice(i))>n?u=i+1:s=i;return o+e.slice(u)}for(;u<s;)i=1+(u+s>>>1),r(e.slice(0,i))<n?u=i:s=i-1;return e.slice(0,u)+o}(t,n):n}function By(t,e){var n=t.font;return(e&&n?String(n).replace(/"/g,"'"):n)||"sans-serif"}function Ny(t,e){return(t.fontStyle?t.fontStyle+" ":"")+(t.fontVariant?t.fontVariant+" ":"")+(t.fontWeight?t.fontWeight+" ":"")+Cy(t)+"px "+By(t,e)}function zy(t){var e=t.baseline,n=Cy(t);return Math.round("top"===e?.79*n:"middle"===e?.3*n:"bottom"===e?-.21*n:"line-top"===e?.29*n+.5*Fy(t):"line-bottom"===e?.29*n-.5*Fy(t):0)}ky(!0);const Oy={left:"start",center:"middle",right:"end"},Ry=new Hg;function Uy(t){var e,n=t.x||0,r=t.y||0,i=t.radius||0;return i&&(e=(t.theta||0)-og,n+=i*Math.cos(e),r+=i*Math.sin(e)),Ry.x1=n,Ry.y1=r,Ry}function Ly(t,e,n){var r,i=wy.height(e),o=e.align,a=Uy(e),u=a.x1,s=a.y1,l=e.dx||0,c=(e.dy||0)+zy(e)-Math.round(.8*i),f=Sy(e);if(k(f)?(i+=Fy(e)*(f.length-1),r=f.reduce(((t,n)=>Math.max(t,wy.width(e,n))),0)):r=wy.width(e,f),"center"===o?l-=r/2:"right"===o&&(l-=r),t.set(l+=u,c+=s,l+r,c+i),e.angle&&!n)t.rotate(e.angle*rg,u,s);else if(2===n)return t.rotatedPoints(e.angle*rg,u,s);return t}var qy={type:"text",tag:"text",nested:!1,attr:function(t,e){var n,r=e.dx||0,i=(e.dy||0)+zy(e),o=Uy(e),a=o.x1,u=o.y1,s=e.angle||0;t("text-anchor",Oy[e.align]||"start"),s?(n=Wm(a,u)+" "+Hm(s),(r||i)&&(n+=" "+Wm(r,i))):n=Wm(a+r,u+i),t("transform",n)},bound:Ly,draw:function(t,e,n){Om(e,(e=>{var r,i,o,a,u,s,l,c=null==e.opacity?1:e.opacity;if(!(n&&!n.intersects(e.bounds)||0===c||e.fontSize<=0||null==e.text||0===e.text.length)){if(t.font=Ny(e),t.textAlign=e.align||"left",i=(r=Uy(e)).x1,o=r.y1,e.angle&&(t.save(),t.translate(i,o),t.rotate(e.angle*rg),i=o=0),i+=e.dx||0,o+=(e.dy||0)+zy(e),s=Sy(e),Dm(t,e),k(s))for(u=Fy(e),a=0;a<s.length;++a)l=Ty(e,s[a]),e.fill&&$m(t,e,c)&&t.fillText(l,i,o),e.stroke&&Bm(t,e,c)&&t.strokeText(l,i,o),o+=u;else l=Ty(e,s),e.fill&&$m(t,e,c)&&t.fillText(l,i,o),e.stroke&&Bm(t,e,c)&&t.strokeText(l,i,o);e.angle&&t.restore()}}))},pick:Pm((function(t,e,n,r,i,o){if(e.fontSize<=0)return!1;if(!e.angle)return!0;var a=Uy(e),u=a.x1,s=a.y1,l=Ly(Ry,e,1),c=-e.angle*rg,f=Math.cos(c),h=Math.sin(c),d=f*i-h*o+(u-f*u+h*s),p=h*i+f*o+(s-h*u-f*s);return l.contains(d,p)})),isect:function(t,e){const n=Ly(Ry,t,2);return Em(e,n[0],n[1],n[2],n[3])||Em(e,n[0],n[1],n[4],n[5])||Em(e,n[4],n[5],n[6],n[7])||Em(e,n[2],n[3],n[6],n[7])}},Py=Xm("trail",(function(t,e){return Lg.context(t)(e)}),(function(t,e){for(var n,r,i=t.length;--i>=0;)if(!1!==t[i].defined&&(n=t[i].x-e[0])*n+(r=t[i].y-e[1])*r<(n=t[i].size||1)*n)return t[i];return null})),jy={arc:Vm,area:Jm,group:iy,image:fy,line:hy,path:py,rect:my,rule:vy,shape:_y,symbol:xy,text:qy,trail:Py};function Iy(t,e,n){var r=jy[t.mark.marktype],i=e||r.bound;return r.nested&&(t=t.mark),i(t.bounds||(t.bounds=new Hg),t,n)}var Wy={mark:null};function Hy(t,e,n){var r,i,o,a,u=jy[t.marktype],s=u.bound,l=t.items,c=l&&l.length;if(u.nested)return c?o=l[0]:(Wy.mark=t,o=Wy),a=Iy(o,s,n),e=e&&e.union(a)||a;if(e=e||t.bounds&&t.bounds.clear()||new Hg,c)for(r=0,i=l.length;r<i;++r)e.union(Iy(l[r],s,n));return t.bounds=e}const Yy=["marktype","name","role","interactive","clip","items","zindex","x","y","width","height","align","baseline","fill","fillOpacity","opacity","blend","stroke","strokeOpacity","strokeWidth","strokeCap","strokeDash","strokeDashOffset","strokeForeground","strokeOffset","startAngle","endAngle","innerRadius","outerRadius","cornerRadius","padAngle","cornerRadiusTopLeft","cornerRadiusTopRight","cornerRadiusBottomLeft","cornerRadiusBottomRight","interpolate","tension","orient","defined","url","aspect","smooth","path","scaleX","scaleY","x2","y2","size","shape","text","angle","theta","radius","dir","dx","dy","ellipsis","limit","lineBreak","lineHeight","font","fontSize","fontWeight","fontStyle","fontVariant","description","aria","ariaRole","ariaRoleDescription"];function Gy(t,e){return JSON.stringify(t,Yy,e)}function Vy(t){return Xy("string"==typeof t?JSON.parse(t):t)}function Xy(t){var e,n,r,i=t.marktype,o=t.items;if(o)for(n=0,r=o.length;n<r;++n)e=i?"mark":"group",o[n][e]=t,o[n].zindex&&(o[n][e].zdirty=!0),"group"===(i||e)&&Xy(o[n]);return i&&Hy(t),t}function Jy(t){arguments.length?this.root=Vy(t):(this.root=Zy({marktype:"group",name:"root",role:"frame"}),this.root.items=[new Gg(this.root)])}function Zy(t,e){const n={bounds:new Hg,clip:!!t.clip,group:e,interactive:!1!==t.interactive,items:[],marktype:t.marktype,name:t.name||void 0,role:t.role||void 0,zindex:t.zindex||0};return null!=t.aria&&(n.aria=t.aria),t.description&&(n.description=t.description),n}function Qy(t,e,n){return!t&&"undefined"!=typeof document&&document.createElement&&(t=document),t?n?t.createElementNS(n,e):t.createElement(e):null}function Ky(t,e){e=e.toLowerCase();for(var n=t.childNodes,r=0,i=n.length;r<i;++r)if(n[r].tagName.toLowerCase()===e)return n[r]}function tv(t,e,n,r){var i,o=t.childNodes[e];return o&&o.tagName.toLowerCase()===n.toLowerCase()||(i=o||null,o=Qy(t.ownerDocument,n,r),t.insertBefore(o,i)),o}function ev(t,e){for(var n=t.childNodes,r=n.length;r>e;)t.removeChild(n[--r]);return t}function nv(t){return"mark-"+t.marktype+(t.role?" role-"+t.role:"")+(t.name?" "+t.name:"")}function rv(t,e){const n=e.getBoundingClientRect();return[t.clientX-n.left-(e.clientLeft||0),t.clientY-n.top-(e.clientTop||0)]}function iv(t,e){this._active=null,this._handlers={},this._loader=t||da(),this._tooltip=e||ov}function ov(t,e,n,r){t.element().setAttribute("title",r||"")}function av(t){this._el=null,this._bgcolor=null,this._loader=new Vg(t)}Jy.prototype={toJSON(t){return Gy(this.root,t||0)},mark(t,e,n){const r=Zy(t,e=e||this.root.items[0]);return e.items[n]=r,r.zindex&&(r.group.zdirty=!0),r}},iv.prototype={initialize(t,e,n){return this._el=t,this._obj=n||null,this.origin(e)},element(){return this._el},canvas(){return this._el&&this._el.firstChild},origin(t){return arguments.length?(this._origin=t||[0,0],this):this._origin.slice()},scene(t){return arguments.length?(this._scene=t,this):this._scene},on(){},off(){},_handlerIndex(t,e,n){for(let r=t?t.length:0;--r>=0;)if(t[r].type===e&&(!n||t[r].handler===n))return r;return-1},handlers(t){const e=this._handlers,n=[];if(t)n.push(...e[this.eventName(t)]);else for(const t in e)n.push(...e[t]);return n},eventName(t){const e=t.indexOf(".");return e<0?t:t.slice(0,e)},handleHref(t,e,n){this._loader.sanitize(n,{context:"href"}).then((e=>{const n=new MouseEvent(t.type,t),r=Qy(null,"a");for(const t in e)r.setAttribute(t,e[t]);r.dispatchEvent(n)})).catch((()=>{}))},handleTooltip(t,e,n){if(e&&null!=e.tooltip){e=function(t,e,n,r){var i,o,a=t&&t.mark;if(a&&(i=jy[a.marktype]).tip){for((o=rv(e,n))[0]-=r[0],o[1]-=r[1];t=t.mark.group;)o[0]-=t.x||0,o[1]-=t.y||0;t=i.tip(a.items,o)}return t}(e,t,this.canvas(),this._origin);const r=n&&e&&e.tooltip||null;this._tooltip.call(this._obj,this,t,e,r)}},getItemBoundingClientRect(t){const e=this.canvas();if(!e)return;const n=e.getBoundingClientRect(),r=this._origin,i=t.bounds,o=i.width(),a=i.height();let u=i.x1+r[0]+n.left,s=i.y1+r[1]+n.top;for(;t.mark&&(t=t.mark.group);)u+=t.x||0,s+=t.y||0;return{x:u,y:s,width:o,height:a,left:u,top:s,right:u+o,bottom:s+a}}},av.prototype={initialize(t,e,n,r,i){return this._el=t,this.resize(e,n,r,i)},element(){return this._el},canvas(){return this._el&&this._el.firstChild},background(t){return 0===arguments.length?this._bgcolor:(this._bgcolor=t,this)},resize(t,e,n,r){return this._width=t,this._height=e,this._origin=n||[0,0],this._scale=r||1,this},dirty(){},render(t){const e=this;return e._call=function(){e._render(t)},e._call(),e._call=null,e},_render(){},renderAsync(t){const e=this.render(t);return this._ready?this._ready.then((()=>e)):Promise.resolve(e)},_load(t,e){var n=this,r=n._loader[t](e);if(!n._ready){const t=n._call;n._ready=n._loader.ready().then((e=>{e&&t(),n._ready=null}))}return r},sanitizeURL(t){return this._load("sanitizeURL",t)},loadImage(t){return this._load("loadImage",t)}};const uv="dragenter",sv="dragleave",lv="dragover",cv="mousedown",fv="mousemove",hv="mouseout",dv="mouseover",pv="click",gv="mousewheel",mv="touchstart",yv="touchmove",vv="touchend",_v=fv,xv=hv,bv=pv;function wv(t,e){iv.call(this,t,e),this._down=null,this._touch=null,this._first=!0,this._events={}}const kv=t=>t===mv||t===yv||t===vv?[mv,yv,vv]:[t];function Av(t,e){kv(e).forEach((e=>function(t,e){const n=t.canvas();n&&!t._events[e]&&(t._events[e]=1,n.addEventListener(e,t[e]?n=>t[e](n):n=>t.fire(e,n)))}(t,e)))}function Mv(t,e,n){return function(r){const i=this._active,o=this.pickEvent(r);o===i||(i&&i.exit||this.fire(n,r),this._active=o,this.fire(e,r)),this.fire(t,r)}}function Ev(t){return function(e){this.fire(t,e),this._active=null}}dt(wv,iv,{initialize(t,e,n){return this._canvas=t&&Ky(t,"canvas"),[pv,cv,fv,hv,sv].forEach((t=>Av(this,t))),iv.prototype.initialize.call(this,t,e,n)},canvas(){return this._canvas},context(){return this._canvas.getContext("2d")},events:["keydown","keypress","keyup",uv,sv,lv,cv,"mouseup",fv,hv,dv,pv,"dblclick","wheel",gv,mv,yv,vv],DOMMouseScroll(t){this.fire(gv,t)},mousemove:Mv(fv,dv,hv),dragover:Mv(lv,uv,sv),mouseout:Ev(hv),dragleave:Ev(sv),mousedown(t){this._down=this._active,this.fire(cv,t)},click(t){this._down===this._active&&(this.fire(pv,t),this._down=null)},touchstart(t){this._touch=this.pickEvent(t.changedTouches[0]),this._first&&(this._active=this._touch,this._first=!1),this.fire(mv,t,!0)},touchmove(t){this.fire(yv,t,!0)},touchend(t){this.fire(vv,t,!0),this._touch=null},fire(t,e,n){const r=n?this._touch:this._active,i=this._handlers[t];if(e.vegaType=t,t===bv&&r&&r.href?this.handleHref(e,r,r.href):t!==_v&&t!==xv||this.handleTooltip(e,r,t!==xv),i)for(let t=0,n=i.length;t<n;++t)i[t].handler.call(this._obj,e,r)},on(t,e){const n=this.eventName(t),r=this._handlers;return this._handlerIndex(r[n],t,e)<0&&(Av(this,t),(r[n]||(r[n]=[])).push({type:t,handler:e})),this},off(t,e){const n=this.eventName(t),r=this._handlers[n],i=this._handlerIndex(r,t,e);return i>=0&&r.splice(i,1),this},pickEvent(t){const e=rv(t,this._canvas),n=this._origin;return this.pick(this._scene,e[0],e[1],e[0]-n[0],e[1]-n[1])},pick(t,e,n,r,i){const o=this.context();return jy[t.marktype].pick.call(this,o,t,e,n,r,i)}});var Dv="undefined"!=typeof window&&window.devicePixelRatio||1;function Cv(t){av.call(this,t),this._options={},this._redraw=!1,this._dirty=new Hg,this._tempb=new Hg}const Fv=av.prototype;function Sv(t,e){iv.call(this,t,e);const n=this;n._hrefHandler=$v(n,((t,e)=>{e&&e.href&&n.handleHref(t,e,e.href)})),n._tooltipHandler=$v(n,((t,e)=>{n.handleTooltip(t,e,t.type!==xv)}))}dt(Cv,av,{initialize(t,e,n,r,i,o){return this._options=o||{},this._canvas=this._options.externalContext?null:Rc(1,1,this._options.type),t&&this._canvas&&(ev(t,0).appendChild(this._canvas),this._canvas.setAttribute("class","marks")),Fv.initialize.call(this,t,e,n,r,i)},resize(t,e,n,r){if(Fv.resize.call(this,t,e,n,r),this._canvas)!function(t,e,n,r,i,o){const a="undefined"!=typeof HTMLElement&&t instanceof HTMLElement&&null!=t.parentNode,u=t.getContext("2d"),s=a?Dv:i;t.width=e*s,t.height=n*s;for(const t in o)u[t]=o[t];a&&1!==s&&(t.style.width=e+"px",t.style.height=n+"px"),u.pixelRatio=s,u.setTransform(s,0,0,s,s*r[0],s*r[1])}(this._canvas,this._width,this._height,this._origin,this._scale,this._options.context);else{const t=this._options.externalContext;t||u("CanvasRenderer is missing a valid canvas or context"),t.scale(this._scale,this._scale),t.translate(this._origin[0],this._origin[1])}return this._redraw=!0,this},canvas(){return this._canvas},context(){return this._options.externalContext||(this._canvas?this._canvas.getContext("2d"):null)},dirty(t){const e=this._tempb.clear().union(t.bounds);let n=t.mark.group;for(;n;)e.translate(n.x||0,n.y||0),n=n.mark.group;this._dirty.union(e)},_render(t){const e=this.context(),n=this._origin,r=this._width,i=this._height,o=this._dirty,a=((t,e,n)=>(new Hg).set(0,0,e,n).translate(-t[0],-t[1]))(n,r,i);e.save();const u=this._redraw||o.empty()?(this._redraw=!1,a.expand(1)):function(t,e,n){return e.expand(1).round(),t.pixelRatio%1&&e.scale(t.pixelRatio).round().scale(1/t.pixelRatio),e.translate(-n[0]%1,-n[1]%1),t.beginPath(),t.rect(e.x1,e.y1,e.width(),e.height()),t.clip(),e}(e,a.intersect(o),n);return this.clear(-n[0],-n[1],r,i),this.draw(e,t,u),e.restore(),o.clear(),this},draw(t,e,n){const r=jy[e.marktype];e.clip&&function(t,e){var n=e.clip;t.save(),J(n)?(t.beginPath(),n(t),t.clip()):Zm(t,e.group)}(t,e),r.draw.call(this,t,e,n),e.clip&&t.restore()},clear(t,e,n,r){const i=this._options,o=this.context();"pdf"===i.type||i.externalContext||o.clearRect(t,e,n,r),null!=this._bgcolor&&(o.fillStyle=this._bgcolor,o.fillRect(t,e,n,r))}});const $v=(t,e)=>n=>{let r=n.target.__data__;r=Array.isArray(r)?r[0]:r,n.vegaType=n.type,e.call(t._obj,n,r)};dt(Sv,iv,{initialize(t,e,n){let r=this._svg;return r&&(r.removeEventListener(bv,this._hrefHandler),r.removeEventListener(_v,this._tooltipHandler),r.removeEventListener(xv,this._tooltipHandler)),this._svg=r=t&&Ky(t,"svg"),r&&(r.addEventListener(bv,this._hrefHandler),r.addEventListener(_v,this._tooltipHandler),r.addEventListener(xv,this._tooltipHandler)),iv.prototype.initialize.call(this,t,e,n)},canvas(){return this._svg},on(t,e){const n=this.eventName(t),r=this._handlers;if(this._handlerIndex(r[n],t,e)<0){const i={type:t,handler:e,listener:$v(this,e)};(r[n]||(r[n]=[])).push(i),this._svg&&this._svg.addEventListener(n,i.listener)}return this},off(t,e){const n=this.eventName(t),r=this._handlers[n],i=this._handlerIndex(r,t,e);return i>=0&&(this._svg&&this._svg.removeEventListener(n,r[i].listener),r.splice(i,1)),this}});const Tv="aria-hidden",Bv="aria-label",Nv="role",zv="aria-roledescription",Ov="graphics-object",Rv="graphics-symbol",Uv=(t,e,n)=>({[Nv]:t,[zv]:e,[Bv]:n||void 0}),Lv=Nt(["axis-domain","axis-grid","axis-label","axis-tick","axis-title","legend-band","legend-entry","legend-gradient","legend-label","legend-title","legend-symbol","title"]),qv={axis:{desc:"axis",caption:function(t){const e=t.datum,n=t.orient,r=e.title?Hv(t):null,i=t.context,o=i.scales[e.scale].value,a=i.dataflow.locale(),u=o.type;return("left"===n||"right"===n?"Y":"X")+"-axis"+(r?` titled '${r}'`:"")+` for a ${ap(u)?"discrete":u} scale`+` with ${jp(a,o,t)}`}},legend:{desc:"legend",caption:function(t){const e=t.datum,n=e.title?Hv(t):null,r=`${e.type||""} legend`.trim(),i=e.scales,o=Object.keys(i),a=t.context,u=a.scales[i[o[0]]].value,s=a.dataflow.locale();return l=r,(l.length?l[0].toUpperCase()+l.slice(1):l)+(n?` titled '${n}'`:"")+` for ${function(t){return t=t.map((t=>t+("fill"===t||"stroke"===t?" color":""))),t.length<2?t[0]:t.slice(0,-1).join(", ")+" and "+F(t)}(o)}`+` with ${jp(s,u,t)}`;var l}},"title-text":{desc:"title",caption:t=>`Title text '${Wv(t)}'`},"title-subtitle":{desc:"subtitle",caption:t=>`Subtitle text '${Wv(t)}'`}},Pv={ariaRole:Nv,ariaRoleDescription:zv,description:Bv};function jv(t,e){const n=!1===e.aria;if(t(Tv,n||void 0),n||null==e.description)for(const e in Pv)t(Pv[e],void 0);else{const n=e.mark.marktype;t(Bv,e.description),t(Nv,e.ariaRole||("group"===n?Ov:Rv)),t(zv,e.ariaRoleDescription||`${n} mark`)}}function Iv(t){return!1===t.aria?{[Tv]:!0}:Lv[t.role]?null:qv[t.role]?function(t,e){try{const n=t.items[0],r=e.caption||(()=>"");return Uv(e.role||Rv,e.desc,n.description||r(n))}catch(t){return null}}(t,qv[t.role]):function(t){const e=t.marktype,n="group"===e||"text"===e||t.items.some((t=>null!=t.description&&!1!==t.aria));return Uv(n?Ov:Rv,`${e} mark container`,t.description)}(t)}function Wv(t){return V(t.text).join(" ")}function Hv(t){try{return V(F(t.items).items[0].text).join(" ")}catch(t){return null}}const Yv=t=>(t+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),Gv=t=>Yv(t).replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;");function Vv(){let t="",e="",n="";const r=[],i=()=>e=n="",o=(t,n)=>(null!=n&&(e+=` ${t}="${Gv(n)}"`),a),a={open(u){(o=>{e&&(t+=`${e}>${n}`,i()),r.push(o)})(u),e="<"+u;for(var s=arguments.length,l=new Array(s>1?s-1:0),c=1;c<s;c++)l[c-1]=arguments[c];for(const t of l)for(const e in t)o(e,t[e]);return a},close(){const o=r.pop();return t+=e?e+(n?`>${n}</${o}>`:"/>"):`</${o}>`,i(),a},attr:o,text:t=>(n+=Yv(t),a),toString:()=>t};return a}const Xv=t=>Jv(Vv(),t)+"";function Jv(t,e){if(t.open(e.tagName),e.hasAttributes()){const n=e.attributes,r=n.length;for(let e=0;e<r;++e)t.attr(n[e].name,n[e].value)}if(e.hasChildNodes()){const n=e.childNodes;for(const e of n)3===e.nodeType?t.text(e.nodeValue):Jv(t,e)}return t.close()}const Zv={fill:"fill",fillOpacity:"fill-opacity",stroke:"stroke",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",strokeCap:"stroke-linecap",strokeJoin:"stroke-linejoin",strokeDash:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeMiterLimit:"stroke-miterlimit",opacity:"opacity"},Qv={blend:"mix-blend-mode"},Kv={fill:"none","stroke-miterlimit":10},t_="http://www.w3.org/2000/xmlns/",e_=oy.xmlns;function n_(t){av.call(this,t),this._dirtyID=0,this._dirty=[],this._svg=null,this._root=null,this._defs=null}const r_=av.prototype;function i_(t,e){for(;t&&t.dirty!==e;t=t.mark.group){if(t.dirty=e,!t.mark||t.mark.dirty===e)return;t.mark.dirty=e}}function o_(t,e,n){let r,i,o;if("radial"===e.gradient){let r=tv(t,n++,"pattern",e_);d_(r,{id:Wp+e.id,viewBox:"0,0,1,1",width:"100%",height:"100%",preserveAspectRatio:"xMidYMid slice"}),r=tv(r,0,"rect",e_),d_(r,{width:1,height:1,fill:`url(${g_()}#${e.id})`}),d_(t=tv(t,n++,"radialGradient",e_),{id:e.id,fx:e.x1,fy:e.y1,fr:e.r1,cx:e.x2,cy:e.y2,r:e.r2})}else d_(t=tv(t,n++,"linearGradient",e_),{id:e.id,x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2});for(r=0,i=e.stops.length;r<i;++r)o=tv(t,r,"stop",e_),o.setAttribute("offset",e.stops[r].offset),o.setAttribute("stop-color",e.stops[r].color);return ev(t,r),n}function a_(t,e,n){let r;return(t=tv(t,n,"clipPath",e_)).setAttribute("id",e.id),e.path?(r=tv(t,0,"path",e_),r.setAttribute("d",e.path)):(r=tv(t,0,"rect",e_),d_(r,{x:0,y:0,width:e.width,height:e.height})),ev(t,1),n+1}function u_(t,e,n,r,i){let o,a=t._svg;if(!a&&(o=e.ownerDocument,a=Qy(o,r,e_),t._svg=a,t.mark&&(a.__data__=t,a.__values__={fill:"default"},"g"===r))){const e=Qy(o,"path",e_);a.appendChild(e),e.__data__=t;const n=Qy(o,"g",e_);a.appendChild(n),n.__data__=t;const r=Qy(o,"path",e_);a.appendChild(r),r.__data__=t,r.__values__={fill:"default"}}return(a.ownerSVGElement!==i||function(t,e){return t.parentNode&&t.parentNode.childNodes.length>1&&t.previousSibling!=e}(a,n))&&e.insertBefore(a,n?n.nextSibling:e.firstChild),a}dt(n_,av,{initialize(t,e,n,r,i){return this._defs={},this._clearDefs(),t&&(this._svg=tv(t,0,"svg",e_),this._svg.setAttributeNS(t_,"xmlns",e_),this._svg.setAttributeNS(t_,"xmlns:xlink",oy["xmlns:xlink"]),this._svg.setAttribute("version",oy.version),this._svg.setAttribute("class","marks"),ev(t,1),this._root=tv(this._svg,0,"g",e_),d_(this._root,Kv),ev(this._svg,1)),this.background(this._bgcolor),r_.initialize.call(this,t,e,n,r,i)},background(t){return arguments.length&&this._svg&&this._svg.style.setProperty("background-color",t),r_.background.apply(this,arguments)},resize(t,e,n,r){return r_.resize.call(this,t,e,n,r),this._svg&&(d_(this._svg,{width:this._width*this._scale,height:this._height*this._scale,viewBox:`0 0 ${this._width} ${this._height}`}),this._root.setAttribute("transform",`translate(${this._origin})`)),this._dirty=[],this},canvas(){return this._svg},svg(){const t=this._svg,e=this._bgcolor;if(!t)return null;let n;e&&(t.removeAttribute("style"),n=tv(t,0,"rect",e_),d_(n,{width:this._width,height:this._height,fill:e}));const r=Xv(t);return e&&(t.removeChild(n),this._svg.style.setProperty("background-color",e)),r},_render(t){return this._dirtyCheck()&&(this._dirtyAll&&this._clearDefs(),this.mark(this._root,t),ev(this._root,1)),this.defs(),this._dirty=[],++this._dirtyID,this},dirty(t){t.dirty!==this._dirtyID&&(t.dirty=this._dirtyID,this._dirty.push(t))},isDirty(t){return this._dirtyAll||!t._svg||!t._svg.ownerSVGElement||t.dirty===this._dirtyID},_dirtyCheck(){this._dirtyAll=!0;const t=this._dirty;if(!t.length||!this._dirtyID)return!0;const e=++this._dirtyID;let n,r,i,o,a,u,s;for(a=0,u=t.length;a<u;++a)n=t[a],r=n.mark,r.marktype!==i&&(i=r.marktype,o=jy[i]),r.zdirty&&r.dirty!==e&&(this._dirtyAll=!1,i_(n,e),r.items.forEach((t=>{t.dirty=e}))),r.zdirty||(n.exit?(o.nested&&r.items.length?(s=r.items[0],s._svg&&this._update(o,s._svg,s)):n._svg&&(s=n._svg.parentNode,s&&s.removeChild(n._svg)),n._svg=null):(n=o.nested?r.items[0]:n,n._update!==e&&(n._svg&&n._svg.ownerSVGElement?this._update(o,n._svg,n):(this._dirtyAll=!1,i_(n,e)),n._update=e)));return!this._dirtyAll},mark(t,e,n){if(!this.isDirty(e))return e._svg;const r=this._svg,i=jy[e.marktype],o=!1===e.interactive?"none":null,a="g"===i.tag,u=u_(e,t,n,"g",r);u.setAttribute("class",nv(e));const s=Iv(e);for(const t in s)p_(u,t,s[t]);a||p_(u,"pointer-events",o),p_(u,"clip-path",e.clip?Wg(this,e,e.group):null);let l=null,c=0;const f=t=>{const e=this.isDirty(t),n=u_(t,u,l,i.tag,r);e&&(this._update(i,n,t),a&&function(t,e,n){e=e.lastChild.previousSibling;let r,i=0;Om(n,(n=>{r=t.mark(e,n,r),++i})),ev(e,1+i)}(this,n,t)),l=n,++c};return i.nested?e.items.length&&f(e.items[0]):Om(e,f),ev(u,c),u},_update(t,e,n){s_=e,l_=e.__values__,jv(f_,n),t.attr(f_,n,this);const r=c_[t.type];r&&r.call(this,t,e,n),s_&&this.style(s_,n)},style(t,e){if(null!=e){for(const n in Zv){let r="font"===n?By(e):e[n];if(r===l_[n])continue;const i=Zv[n];null==r?t.removeAttribute(i):(Hp(r)&&(r=Yp(r,this._defs.gradient,g_())),t.setAttribute(i,r+"")),l_[n]=r}for(const n in Qv)h_(t,Qv[n],e[n])}},defs(){const t=this._svg,e=this._defs;let n=e.el,r=0;for(const i in e.gradient)n||(e.el=n=tv(t,1,"defs",e_)),r=o_(n,e.gradient[i],r);for(const i in e.clipping)n||(e.el=n=tv(t,1,"defs",e_)),r=a_(n,e.clipping[i],r);n&&(0===r?(t.removeChild(n),e.el=null):ev(n,r))},_clearDefs(){const t=this._defs;t.gradient={},t.clipping={}}});let s_=null,l_=null;const c_={group(t,e,n){const r=s_=e.childNodes[2];l_=r.__values__,t.foreground(f_,n,this),l_=e.__values__,s_=e.childNodes[1],t.content(f_,n,this);const i=s_=e.childNodes[0];t.background(f_,n,this);const o=!1===n.mark.interactive?"none":null;if(o!==l_.events&&(p_(r,"pointer-events",o),p_(i,"pointer-events",o),l_.events=o),n.strokeForeground&&n.stroke){const t=n.fill;p_(r,"display",null),this.style(i,n),p_(i,"stroke",null),t&&(n.fill=null),l_=r.__values__,this.style(r,n),t&&(n.fill=t),s_=null}else p_(r,"display","none")},image(t,e,n){!1===n.smooth?(h_(e,"image-rendering","optimizeSpeed"),h_(e,"image-rendering","pixelated")):h_(e,"image-rendering",null)},text(t,e,n){const r=Sy(n);let i,o,a,u;k(r)?(o=r.map((t=>Ty(n,t))),i=o.join("\n"),i!==l_.text&&(ev(e,0),a=e.ownerDocument,u=Fy(n),o.forEach(((t,r)=>{const i=Qy(a,"tspan",e_);i.__data__=n,i.textContent=t,r&&(i.setAttribute("x",0),i.setAttribute("dy",u)),e.appendChild(i)})),l_.text=i)):(o=Ty(n,r),o!==l_.text&&(e.textContent=o,l_.text=o)),p_(e,"font-family",By(n)),p_(e,"font-size",Cy(n)+"px"),p_(e,"font-style",n.fontStyle),p_(e,"font-variant",n.fontVariant),p_(e,"font-weight",n.fontWeight)}};function f_(t,e,n){e!==l_[t]&&(n?function(t,e,n,r){null!=n?t.setAttributeNS(r,e,n):t.removeAttributeNS(r,e)}(s_,t,e,n):p_(s_,t,e),l_[t]=e)}function h_(t,e,n){n!==l_[e]&&(null==n?t.style.removeProperty(e):t.style.setProperty(e,n+""),l_[e]=n)}function d_(t,e){for(const n in e)p_(t,n,e[n])}function p_(t,e,n){null!=n?t.setAttribute(e,n):t.removeAttribute(e)}function g_(){let t;return"undefined"==typeof window?"":(t=window.location).hash?t.href.slice(0,-t.hash.length):t.href}function m_(t){av.call(this,t),this._text=null,this._defs={gradient:{},clipping:{}}}dt(m_,av,{svg(){return this._text},_render(t){const e=Vv();e.open("svg",ot({},oy,{class:"marks",width:this._width*this._scale,height:this._height*this._scale,viewBox:`0 0 ${this._width} ${this._height}`}));const n=this._bgcolor;return n&&"transparent"!==n&&"none"!==n&&e.open("rect",{width:this._width,height:this._height,fill:n}).close(),e.open("g",Kv,{transform:"translate("+this._origin+")"}),this.mark(e,t),e.close(),this.defs(e),this._text=e.close()+"",this},mark(t,e){const n=jy[e.marktype],r=n.tag,i=[jv,n.attr];t.open("g",{class:nv(e),"clip-path":e.clip?Wg(this,e,e.group):null},Iv(e),{"pointer-events":"g"!==r&&!1===e.interactive?"none":null});const o=o=>{const a=this.href(o);if(a&&t.open("a",a),t.open(r,this.attr(e,o,i,"g"!==r?r:null)),"text"===r){const e=Sy(o);if(k(e)){const n={x:0,dy:Fy(o)};for(let r=0;r<e.length;++r)t.open("tspan",r?n:null).text(Ty(o,e[r])).close()}else t.text(Ty(o,e))}else if("g"===r){const r=o.strokeForeground,i=o.fill,a=o.stroke;r&&a&&(o.stroke=null),t.open("path",this.attr(e,o,n.background,"bgrect")).close(),t.open("g",this.attr(e,o,n.content)),Om(o,(e=>this.mark(t,e))),t.close(),r&&a?(i&&(o.fill=null),o.stroke=a,t.open("path",this.attr(e,o,n.foreground,"bgrect")).close(),i&&(o.fill=i)):t.open("path",this.attr(e,o,n.foreground,"bgfore")).close()}t.close(),a&&t.close()};return n.nested?e.items&&e.items.length&&o(e.items[0]):Om(e,o),t.close()},href(t){const e=t.href;let n;if(e){if(n=this._hrefs&&this._hrefs[e])return n;this.sanitizeURL(e).then((t=>{t["xlink:href"]=t.href,t.href=null,(this._hrefs||(this._hrefs={}))[e]=t}))}return null},attr(t,e,n,r){const i={},o=(t,e,n,r)=>{i[r||t]=e};return Array.isArray(n)?n.forEach((t=>t(o,e,this))):n(o,e,this),r&&function(t,e,n,r,i){let o;if(null==e)return t;"bgrect"===r&&!1===n.interactive&&(t["pointer-events"]="none");if("bgfore"===r&&(!1===n.interactive&&(t["pointer-events"]="none"),t.display="none",null!==e.fill))return t;"image"===r&&!1===e.smooth&&(o=["image-rendering: optimizeSpeed;","image-rendering: pixelated;"]);"text"===r&&(t["font-family"]=By(e),t["font-size"]=Cy(e)+"px",t["font-style"]=e.fontStyle,t["font-variant"]=e.fontVariant,t["font-weight"]=e.fontWeight);for(const n in Zv){let r=e[n];const o=Zv[n];("transparent"!==r||"fill"!==o&&"stroke"!==o)&&null!=r&&(Hp(r)&&(r=Yp(r,i.gradient,"")),t[o]=r)}for(const t in Qv){const n=e[t];null!=n&&(o=o||[],o.push(`${Qv[t]}: ${n};`))}o&&(t.style=o.join(" "))}(i,e,t,r,this._defs),i},defs(t){const e=this._defs.gradient,n=this._defs.clipping;if(0!==Object.keys(e).length+Object.keys(n).length){t.open("defs");for(const n in e){const r=e[n],i=r.stops;"radial"===r.gradient?(t.open("pattern",{id:Wp+n,viewBox:"0,0,1,1",width:"100%",height:"100%",preserveAspectRatio:"xMidYMid slice"}),t.open("rect",{width:"1",height:"1",fill:"url(#"+n+")"}).close(),t.close(),t.open("radialGradient",{id:n,fx:r.x1,fy:r.y1,fr:r.r1,cx:r.x2,cy:r.y2,r:r.r2})):t.open("linearGradient",{id:n,x1:r.x1,x2:r.x2,y1:r.y1,y2:r.y2});for(let e=0;e<i.length;++e)t.open("stop",{offset:i[e].offset,"stop-color":i[e].color}).close();t.close()}for(const e in n){const r=n[e];t.open("clipPath",{id:e}),r.path?t.open("path",{d:r.path}).close():t.open("rect",{x:0,y:0,width:r.width,height:r.height}).close(),t.close()}t.close()}}});const y_="canvas",v_="none",__={Canvas:y_,PNG:"png",SVG:"svg",None:v_},x_={};function b_(t,e){return t=String(t||"").toLowerCase(),arguments.length>1?(x_[t]=e,this):x_[t]}function w_(t,e,n){const r=[],i=(new Hg).union(e),o=t.marktype;return o?k_(t,i,n,r):"group"===o?A_(t,i,n,r):u("Intersect scene must be mark node or group item.")}function k_(t,e,n,r){if(function(t,e,n){return t.bounds&&e.intersects(t.bounds)&&("group"===t.marktype||!1!==t.interactive&&(!n||n(t)))}(t,e,n)){const i=t.items,o=t.marktype,a=i.length;let u=0;if("group"===o)for(;u<a;++u)A_(i[u],e,n,r);else for(const t=jy[o].isect;u<a;++u){const n=i[u];M_(n,e,t)&&r.push(n)}}return r}function A_(t,e,n,r){n&&n(t.mark)&&M_(t,e,jy.group.isect)&&r.push(t);const i=t.items,o=i&&i.length;if(o){const a=t.x||0,u=t.y||0;e.translate(-a,-u);for(let t=0;t<o;++t)k_(i[t],e,n,r);e.translate(a,u)}return r}function M_(t,e,n){const r=t.bounds;return e.encloses(r)||e.intersects(r)&&n(t,e)}x_[y_]=x_.png={renderer:Cv,headless:Cv,handler:wv},x_.svg={renderer:n_,headless:m_,handler:Sv},x_[v_]={};const E_=new Hg;function D_(t){const e=t.clip;if(J(e))e(gm(E_.clear()));else{if(!e)return;E_.set(0,0,t.group.width,t.group.height)}t.bounds.intersect(E_)}const C_=1e-9;function F_(t,e,n){return t===e||("path"===n?S_(t,e):t instanceof Date&&e instanceof Date?+t==+e:vt(t)&&vt(e)?Math.abs(t-e)<=C_:t&&e&&(A(t)||A(e))?function(t,e){var n,r,i=Object.keys(t),o=Object.keys(e);if(i.length!==o.length)return!1;for(i.sort(),o.sort(),r=i.length-1;r>=0;r--)if(i[r]!=o[r])return!1;for(r=i.length-1;r>=0;r--)if(!F_(t[n=i[r]],e[n],n))return!1;return typeof t==typeof e}(t,e):t==e)}function S_(t,e){return F_(ng(t),ng(e))}const $_="top",T_="left",B_="right",N_="bottom",z_="top-left",O_="top-right",R_="bottom-left",U_="bottom-right",L_="start",q_="middle",P_="end",j_="x",I_="y",W_="group",H_="axis",Y_="title",G_="frame",V_="scope",X_="legend",J_="row-header",Z_="row-footer",Q_="row-title",K_="column-header",tx="column-footer",ex="column-title",nx="padding",rx="symbol",ix="fit",ox="fit-x",ax="fit-y",ux="pad",sx="none",lx="all",cx="each",fx="flush",hx="column",dx="row";function px(t){Qa.call(this,null,t)}function gx(t,e,n){return e(t.bounds.clear(),t,n)}dt(px,Qa,{transform(t,e){const n=e.dataflow,r=t.mark,i=r.marktype,o=jy[i],a=o.bound;let u,s=r.bounds;if(o.nested)r.items.length&&n.dirty(r.items[0]),s=gx(r,a),r.items.forEach((t=>{t.bounds.clear().union(s)}));else if(i===W_||t.modified())switch(e.visit(e.MOD,(t=>n.dirty(t))),s.clear(),r.items.forEach((t=>s.union(gx(t,a)))),r.role){case H_:case X_:case Y_:e.reflow()}else u=e.changed(e.REM),e.visit(e.ADD,(t=>{s.union(gx(t,a))})),e.visit(e.MOD,(t=>{u=u||s.alignsWith(t.bounds),n.dirty(t),s.union(gx(t,a))})),u&&(s.clear(),r.items.forEach((t=>s.union(t.bounds))));return D_(r),e.modifies("bounds")}});const mx=":vega_identifier:";function yx(t){Qa.call(this,0,t)}function vx(t){Qa.call(this,null,t)}function _x(t){Qa.call(this,null,t)}yx.Definition={type:"Identifier",metadata:{modifies:!0},params:[{name:"as",type:"string",required:!0}]},dt(yx,Qa,{transform(t,e){const n=(i=e.dataflow)._signals[mx]||(i._signals[mx]=i.add(0)),r=t.as;var i;let o=n.value;return e.visit(e.ADD,(t=>t[r]=t[r]||++o)),n.set(this.value=o),e}}),dt(vx,Qa,{transform(t,e){let n=this.value;n||(n=e.dataflow.scenegraph().mark(t.markdef,function(t){const e=t.groups,n=t.parent;return e&&1===e.size?e.get(Object.keys(e.object)[0]):e&&n?e.lookup(n):null}(t),t.index),n.group.context=t.context,t.context.group||(t.context.group=n.group),n.source=this.source,n.clip=t.clip,n.interactive=t.interactive,this.value=n);const r=n.marktype===W_?Gg:Yg;return e.visit(e.ADD,(t=>r.call(t,n))),(t.modified("clip")||t.modified("interactive"))&&(n.clip=t.clip,n.interactive=!!t.interactive,n.zdirty=!0,e.reflow()),n.items=e.source,e}});const xx={parity:t=>t.filter(((t,e)=>e%2?t.opacity=0:1)),greedy:(t,e)=>{let n;return t.filter(((t,r)=>r&&bx(n.bounds,t.bounds,e)?t.opacity=0:(n=t,1)))}},bx=(t,e,n)=>n>Math.max(e.x1-t.x2,t.x1-e.x2,e.y1-t.y2,t.y1-e.y2),wx=(t,e)=>{for(var n,r=1,i=t.length,o=t[0].bounds;r<i;o=n,++r)if(bx(o,n=t[r].bounds,e))return!0},kx=t=>{const e=t.bounds;return e.width()>1&&e.height()>1},Ax=t=>(t.forEach((t=>t.opacity=1)),t),Mx=(t,e)=>t.reflow(e.modified()).modifies("opacity");function Ex(t){Qa.call(this,null,t)}dt(_x,Qa,{transform(t,e){const n=xx[t.method]||xx.parity,r=t.separation||0;let i,o,a=e.materialize(e.SOURCE).source;if(!a||!a.length)return;if(!t.method)return t.modified("method")&&(Ax(a),e=Mx(e,t)),e;if(a=a.filter(kx),!a.length)return;if(t.sort&&(a=a.slice().sort(t.sort)),i=Ax(a),e=Mx(e,t),i.length>=3&&wx(i,r)){do{i=n(i,r)}while(i.length>=3&&wx(i,r));i.length<3&&!F(a).opacity&&(i.length>1&&(F(i).opacity=0),F(a).opacity=1)}t.boundScale&&t.boundTolerance>=0&&(o=((t,e,n)=>{var r=t.range(),i=new Hg;return e===$_||e===N_?i.set(r[0],-1/0,r[1],1/0):i.set(-1/0,r[0],1/0,r[1]),i.expand(n||1),t=>i.encloses(t.bounds)})(t.boundScale,t.boundOrient,+t.boundTolerance),a.forEach((t=>{o(t)||(t.opacity=0)})));const u=i[0].mark.bounds.clear();return a.forEach((t=>{t.opacity&&u.union(t.bounds)})),e}}),dt(Ex,Qa,{transform(t,e){const n=e.dataflow;if(e.visit(e.ALL,(t=>n.dirty(t))),e.fields&&e.fields.zindex){const t=e.source&&e.source[0];t&&(t.mark.zdirty=!0)}}});const Dx=new Hg;function Cx(t,e,n){return t[e]===n?0:(t[e]=n,1)}function Fx(t){var e=t.items[0].orient;return e===T_||e===B_}function Sx(t,e,n,r){var i,o,a=e.items[0],u=a.datum,s=null!=a.translate?a.translate:.5,l=a.orient,c=function(t){let e=+t.grid;return[t.ticks?e++:-1,t.labels?e++:-1,e+ +t.domain]}(u),f=a.range,h=a.offset,d=a.position,p=a.minExtent,g=a.maxExtent,m=u.title&&a.items[c[2]].items[0],y=a.titlePadding,v=a.bounds,_=m&&$y(m),x=0,b=0;switch(Dx.clear().union(v),v.clear(),(i=c[0])>-1&&v.union(a.items[i].bounds),(i=c[1])>-1&&v.union(a.items[i].bounds),l){case $_:x=d||0,b=-h,o=Math.max(p,Math.min(g,-v.y1)),v.add(0,-o).add(f,0),m&&$x(t,m,o,y,_,0,-1,v);break;case T_:x=-h,b=d||0,o=Math.max(p,Math.min(g,-v.x1)),v.add(-o,0).add(0,f),m&&$x(t,m,o,y,_,1,-1,v);break;case B_:x=n+h,b=d||0,o=Math.max(p,Math.min(g,v.x2)),v.add(0,0).add(o,f),m&&$x(t,m,o,y,_,1,1,v);break;case N_:x=d||0,b=r+h,o=Math.max(p,Math.min(g,v.y2)),v.add(0,0).add(f,o),m&&$x(t,m,o,y,0,0,1,v);break;default:x=a.x,b=a.y}return Zg(v.translate(x,b),a),Cx(a,"x",x+s)|Cx(a,"y",b+s)&&(a.bounds=Dx,t.dirty(a),a.bounds=v,t.dirty(a)),a.mark.bounds.clear().union(v)}function $x(t,e,n,r,i,o,a,u){const s=e.bounds;if(e.auto){const u=a*(n+i+r);let l=0,c=0;t.dirty(e),o?l=(e.x||0)-(e.x=u):c=(e.y||0)-(e.y=u),e.mark.bounds.clear().union(s.translate(-l,-c)),t.dirty(e)}u.union(s)}const Tx=(t,e)=>Math.floor(Math.min(t,e)),Bx=(t,e)=>Math.ceil(Math.max(t,e));function Nx(t){return(new Hg).set(0,0,t.width||0,t.height||0)}function zx(t){const e=t.bounds.clone();return e.empty()?e.set(0,0,0,0):e.translate(-(t.x||0),-(t.y||0))}function Ox(t,e,n){const r=A(t)?t[e]:t;return null!=r?r:void 0!==n?n:0}function Rx(t){return t<0?Math.ceil(-t):0}function Ux(t,e,n){var r,i,o,a,u,s,l,c,f,h,d,p=!n.nodirty,g=n.bounds===fx?Nx:zx,m=Dx.set(0,0,0,0),y=Ox(n.align,hx),v=Ox(n.align,dx),_=Ox(n.padding,hx),x=Ox(n.padding,dx),b=n.columns||e.length,w=b<=0?1:Math.ceil(e.length/b),k=e.length,A=Array(k),M=Array(b),E=0,D=Array(k),C=Array(w),F=0,S=Array(k),$=Array(k),T=Array(k);for(i=0;i<b;++i)M[i]=0;for(i=0;i<w;++i)C[i]=0;for(i=0;i<k;++i)s=e[i],u=T[i]=g(s),s.x=s.x||0,S[i]=0,s.y=s.y||0,$[i]=0,o=i%b,a=~~(i/b),E=Math.max(E,l=Math.ceil(u.x2)),F=Math.max(F,c=Math.ceil(u.y2)),M[o]=Math.max(M[o],l),C[a]=Math.max(C[a],c),A[i]=_+Rx(u.x1),D[i]=x+Rx(u.y1),p&&t.dirty(e[i]);for(i=0;i<k;++i)i%b==0&&(A[i]=0),i<b&&(D[i]=0);if(y===cx)for(o=1;o<b;++o){for(d=0,i=o;i<k;i+=b)d<A[i]&&(d=A[i]);for(i=o;i<k;i+=b)A[i]=d+M[o-1]}else if(y===lx){for(d=0,i=0;i<k;++i)i%b&&d<A[i]&&(d=A[i]);for(i=0;i<k;++i)i%b&&(A[i]=d+E)}else for(y=!1,o=1;o<b;++o)for(i=o;i<k;i+=b)A[i]+=M[o-1];if(v===cx)for(a=1;a<w;++a){for(d=0,r=(i=a*b)+b;i<r;++i)d<D[i]&&(d=D[i]);for(i=a*b;i<r;++i)D[i]=d+C[a-1]}else if(v===lx){for(d=0,i=b;i<k;++i)d<D[i]&&(d=D[i]);for(i=b;i<k;++i)D[i]=d+F}else for(v=!1,a=1;a<w;++a)for(r=(i=a*b)+b;i<r;++i)D[i]+=C[a-1];for(f=0,i=0;i<k;++i)f=A[i]+(i%b?f:0),S[i]+=f-e[i].x;for(o=0;o<b;++o)for(h=0,i=o;i<k;i+=b)h+=D[i],$[i]+=h-e[i].y;if(y&&Ox(n.center,hx)&&w>1)for(i=0;i<k;++i)(f=(u=y===lx?E:M[i%b])-T[i].x2-e[i].x-S[i])>0&&(S[i]+=f/2);if(v&&Ox(n.center,dx)&&1!==b)for(i=0;i<k;++i)(h=(u=v===lx?F:C[~~(i/b)])-T[i].y2-e[i].y-$[i])>0&&($[i]+=h/2);for(i=0;i<k;++i)m.union(T[i].translate(S[i],$[i]));switch(f=Ox(n.anchor,j_),h=Ox(n.anchor,I_),Ox(n.anchor,hx)){case P_:f-=m.width();break;case q_:f-=m.width()/2}switch(Ox(n.anchor,dx)){case P_:h-=m.height();break;case q_:h-=m.height()/2}for(f=Math.round(f),h=Math.round(h),m.clear(),i=0;i<k;++i)e[i].mark.bounds.clear();for(i=0;i<k;++i)(s=e[i]).x+=S[i]+=f,s.y+=$[i]+=h,m.union(s.mark.bounds.union(s.bounds.translate(S[i],$[i]))),p&&t.dirty(s);return m}function Lx(t,e,n){var r,i,o,a,u,s,l,c=function(t){var e,n,r=t.items,i=r.length,o=0;const a={marks:[],rowheaders:[],rowfooters:[],colheaders:[],colfooters:[],rowtitle:null,coltitle:null};for(;o<i;++o)if(n=(e=r[o]).items,e.marktype===W_)switch(e.role){case H_:case X_:case Y_:break;case J_:a.rowheaders.push(...n);break;case Z_:a.rowfooters.push(...n);break;case K_:a.colheaders.push(...n);break;case tx:a.colfooters.push(...n);break;case Q_:a.rowtitle=n[0];break;case ex:a.coltitle=n[0];break;default:a.marks.push(...n)}return a}(e),f=c.marks,h=n.bounds===fx?qx:Px,d=n.offset,p=n.columns||f.length,g=p<=0?1:Math.ceil(f.length/p),m=g*p;const y=Ux(t,f,n);y.empty()&&y.set(0,0,0,0),c.rowheaders&&(s=Ox(n.headerBand,dx,null),r=jx(t,c.rowheaders,f,p,g,-Ox(d,"rowHeader"),Tx,0,h,"x1",0,p,1,s)),c.colheaders&&(s=Ox(n.headerBand,hx,null),i=jx(t,c.colheaders,f,p,p,-Ox(d,"columnHeader"),Tx,1,h,"y1",0,1,p,s)),c.rowfooters&&(s=Ox(n.footerBand,dx,null),o=jx(t,c.rowfooters,f,p,g,Ox(d,"rowFooter"),Bx,0,h,"x2",p-1,p,1,s)),c.colfooters&&(s=Ox(n.footerBand,hx,null),a=jx(t,c.colfooters,f,p,p,Ox(d,"columnFooter"),Bx,1,h,"y2",m-p,1,p,s)),c.rowtitle&&(u=Ox(n.titleAnchor,dx),l=Ox(d,"rowTitle"),l=u===P_?o+l:r-l,s=Ox(n.titleBand,dx,.5),Ix(t,c.rowtitle,l,0,y,s)),c.coltitle&&(u=Ox(n.titleAnchor,hx),l=Ox(d,"columnTitle"),l=u===P_?a+l:i-l,s=Ox(n.titleBand,hx,.5),Ix(t,c.coltitle,l,1,y,s))}function qx(t,e){return"x1"===e?t.x||0:"y1"===e?t.y||0:"x2"===e?(t.x||0)+(t.width||0):"y2"===e?(t.y||0)+(t.height||0):void 0}function Px(t,e){return t.bounds[e]}function jx(t,e,n,r,i,o,a,u,s,l,c,f,h,d){var p,g,m,y,v,_,x,b,w,k=n.length,A=0,M=0;if(!k)return A;for(p=c;p<k;p+=f)n[p]&&(A=a(A,s(n[p],l)));if(!e.length)return A;for(e.length>i&&(t.warn("Grid headers exceed limit: "+i),e=e.slice(0,i)),A+=o,g=0,y=e.length;g<y;++g)t.dirty(e[g]),e[g].mark.bounds.clear();for(p=c,g=0,y=e.length;g<y;++g,p+=f){for(v=(_=e[g]).mark.bounds,m=p;m>=0&&null==(x=n[m]);m-=h);u?(b=null==d?x.x:Math.round(x.bounds.x1+d*x.bounds.width()),w=A):(b=A,w=null==d?x.y:Math.round(x.bounds.y1+d*x.bounds.height())),v.union(_.bounds.translate(b-(_.x||0),w-(_.y||0))),_.x=b,_.y=w,t.dirty(_),M=a(M,v[l])}return M}function Ix(t,e,n,r,i,o){if(e){t.dirty(e);var a=n,u=n;r?a=Math.round(i.x1+o*i.width()):u=Math.round(i.y1+o*i.height()),e.bounds.translate(a-(e.x||0),u-(e.y||0)),e.mark.bounds.clear().union(e.bounds),e.x=a,e.y=u,t.dirty(e)}}function Wx(t,e,n,r,i,o,a){const u=function(t,e){const n=t[e]||{};return(e,r)=>null!=n[e]?n[e]:null!=t[e]?t[e]:r}(n,e),s=function(t,e){let n=-1/0;return t.forEach((t=>{null!=t.offset&&(n=Math.max(n,t.offset))})),n>-1/0?n:e}(t,u("offset",0)),l=u("anchor",L_),c=l===P_?1:l===q_?.5:0,f={align:cx,bounds:u("bounds",fx),columns:"vertical"===u("direction")?1:t.length,padding:u("margin",8),center:u("center"),nodirty:!0};switch(e){case T_:f.anchor={x:Math.floor(r.x1)-s,column:P_,y:c*(a||r.height()+2*r.y1),row:l};break;case B_:f.anchor={x:Math.ceil(r.x2)+s,y:c*(a||r.height()+2*r.y1),row:l};break;case $_:f.anchor={y:Math.floor(i.y1)-s,row:P_,x:c*(o||i.width()+2*i.x1),column:l};break;case N_:f.anchor={y:Math.ceil(i.y2)+s,x:c*(o||i.width()+2*i.x1),column:l};break;case z_:f.anchor={x:s,y:s};break;case O_:f.anchor={x:o-s,y:s,column:P_};break;case R_:f.anchor={x:s,y:a-s,row:P_};break;case U_:f.anchor={x:o-s,y:a-s,column:P_,row:P_}}return f}function Hx(t,e){var n,r,i=e.items[0],o=i.datum,a=i.orient,u=i.bounds,s=i.x,l=i.y;return i._bounds?i._bounds.clear().union(u):i._bounds=u.clone(),u.clear(),function(t,e,n){var r=e.padding,i=r-n.x,o=r-n.y;if(e.datum.title){var a=e.items[1].items[0],u=a.anchor,s=e.titlePadding||0,l=r-a.x,c=r-a.y;switch(a.orient){case T_:i+=Math.ceil(a.bounds.width())+s;break;case B_:case N_:break;default:o+=a.bounds.height()+s}switch((i||o)&&Gx(t,n,i,o),a.orient){case T_:c+=Yx(e,n,a,u,1,1);break;case B_:l+=Yx(e,n,a,P_,0,0)+s,c+=Yx(e,n,a,u,1,1);break;case N_:l+=Yx(e,n,a,u,0,0),c+=Yx(e,n,a,P_,-1,0,1)+s;break;default:l+=Yx(e,n,a,u,0,0)}(l||c)&&Gx(t,a,l,c),(l=Math.round(a.bounds.x1-r))<0&&(Gx(t,n,-l,0),Gx(t,a,-l,0))}else(i||o)&&Gx(t,n,i,o)}(t,i,i.items[0].items[0]),u=function(t,e){return t.items.forEach((t=>e.union(t.bounds))),e.x1=t.padding,e.y1=t.padding,e}(i,u),n=2*i.padding,r=2*i.padding,u.empty()||(n=Math.ceil(u.width()+n),r=Math.ceil(u.height()+r)),o.type===rx&&function(t){const e=t.reduce(((t,e)=>(t[e.column]=Math.max(e.bounds.x2-e.x,t[e.column]||0),t)),{});t.forEach((t=>{t.width=e[t.column],t.height=t.bounds.y2-t.y}))}(i.items[0].items[0].items[0].items),a!==sx&&(i.x=s=0,i.y=l=0),i.width=n,i.height=r,Zg(u.set(s,l,s+n,l+r),i),i.mark.bounds.clear().union(u),i}function Yx(t,e,n,r,i,o,a){const u="symbol"!==t.datum.type,s=n.datum.vgrad,l=(!u||!o&&s||a?e:e.items[0]).bounds[i?"y2":"x2"]-t.padding,c=s&&o?l:0,f=s&&o?0:l,h=i<=0?0:$y(n);return Math.round(r===L_?c:r===P_?f-h:.5*(l-h))}function Gx(t,e,n,r){e.x+=n,e.y+=r,e.bounds.translate(n,r),e.mark.bounds.translate(n,r),t.dirty(e)}function Vx(t){Qa.call(this,null,t)}dt(Vx,Qa,{transform(t,e){const n=e.dataflow;return t.mark.items.forEach((e=>{t.layout&&Lx(n,e,t.layout),function(t,e,n){var r,i,o,a,u,s=e.items,l=Math.max(0,e.width||0),c=Math.max(0,e.height||0),f=(new Hg).set(0,0,l,c),h=f.clone(),d=f.clone(),p=[];for(a=0,u=s.length;a<u;++a)switch((i=s[a]).role){case H_:(Fx(i)?h:d).union(Sx(t,i,l,c));break;case Y_:r=i;break;case X_:p.push(Hx(t,i));break;case G_:case V_:case J_:case Z_:case Q_:case K_:case tx:case ex:h.union(i.bounds),d.union(i.bounds);break;default:f.union(i.bounds)}if(p.length){const e={};p.forEach((t=>{(o=t.orient||B_)!==sx&&(e[o]||(e[o]=[])).push(t)}));for(const r in e){const i=e[r];Ux(t,i,Wx(i,r,n.legends,h,d,l,c))}p.forEach((e=>{const r=e.bounds;if(r.equals(e._bounds)||(e.bounds=e._bounds,t.dirty(e),e.bounds=r,t.dirty(e)),!n.autosize||n.autosize.type!==ix&&n.autosize.type!==ox&&n.autosize.type!==ax)f.union(r);else switch(e.orient){case T_:case B_:f.add(r.x1,0).add(r.x2,0);break;case $_:case N_:f.add(0,r.y1).add(0,r.y2)}}))}f.union(h).union(d),r&&f.union(function(t,e,n,r,i){var o,a=e.items[0],u=a.frame,s=a.orient,l=a.anchor,c=a.offset,f=a.padding,h=a.items[0].items[0],d=a.items[1]&&a.items[1].items[0],p=s===T_||s===B_?r:n,g=0,m=0,y=0,v=0,_=0;if(u!==W_?s===T_?(g=i.y2,p=i.y1):s===B_?(g=i.y1,p=i.y2):(g=i.x1,p=i.x2):s===T_&&(g=r,p=0),o=l===L_?g:l===P_?p:(g+p)/2,d&&d.text){switch(s){case $_:case N_:_=h.bounds.height()+f;break;case T_:v=h.bounds.width()+f;break;case B_:v=-h.bounds.width()-f}Dx.clear().union(d.bounds),Dx.translate(v-(d.x||0),_-(d.y||0)),Cx(d,"x",v)|Cx(d,"y",_)&&(t.dirty(d),d.bounds.clear().union(Dx),d.mark.bounds.clear().union(Dx),t.dirty(d)),Dx.clear().union(d.bounds)}else Dx.clear();switch(Dx.union(h.bounds),s){case $_:m=o,y=i.y1-Dx.height()-c;break;case T_:m=i.x1-Dx.width()-c,y=o;break;case B_:m=i.x2+Dx.width()+c,y=o;break;case N_:m=o,y=i.y2+c;break;default:m=a.x,y=a.y}return Cx(a,"x",m)|Cx(a,"y",y)&&(Dx.translate(m,y),t.dirty(a),a.bounds.clear().union(Dx),e.bounds.clear().union(Dx),t.dirty(a)),a.bounds}(t,r,l,c,f));e.clip&&f.set(0,0,e.width||0,e.height||0);!function(t,e,n,r){const i=r.autosize||{},o=i.type;if(t._autosize<1||!o)return;let a=t._width,u=t._height,s=Math.max(0,e.width||0),l=Math.max(0,Math.ceil(-n.x1)),c=Math.max(0,e.height||0),f=Math.max(0,Math.ceil(-n.y1));const h=Math.max(0,Math.ceil(n.x2-s)),d=Math.max(0,Math.ceil(n.y2-c));if(i.contains===nx){const e=t.padding();a-=e.left+e.right,u-=e.top+e.bottom}o===sx?(l=0,f=0,s=a,c=u):o===ix?(s=Math.max(0,a-l-h),c=Math.max(0,u-f-d)):o===ox?(s=Math.max(0,a-l-h),u=c+f+d):o===ax?(a=s+l+h,c=Math.max(0,u-f-d)):o===ux&&(a=s+l+h,u=c+f+d);t._resizeView(a,u,s,c,[l,f],i.resize)}(t,e,f,n)}(n,e,t)})),function(t){return t&&"legend-entry"!==t.mark.role}(t.mark.group)?e.reflow():e}});var Xx=Object.freeze({__proto__:null,bound:px,identifier:yx,mark:vx,overlap:_x,render:Ex,viewlayout:Vx});function Jx(t){Qa.call(this,null,t)}function Zx(t){Qa.call(this,null,t)}function Qx(){return ba({})}function Kx(t){Qa.call(this,null,t)}function tb(t){Qa.call(this,[],t)}dt(Jx,Qa,{transform(t,e){if(this.value&&!t.modified())return e.StopPropagation;var n=e.dataflow.locale(),r=e.fork(e.NO_SOURCE|e.NO_FIELDS),i=this.value,o=t.scale,a=Ep(o,null==t.count?t.values?t.values.length:10:t.count,t.minstep),u=t.format||Fp(n,o,a,t.formatSpecifier,t.formatType,!!t.values),s=t.values?Dp(o,t.values,a):Cp(o,a);return i&&(r.rem=i),i=s.map(((t,e)=>ba({index:e/(s.length-1||1),value:t,label:u(t)}))),t.extra&&i.length&&i.push(ba({index:-1,extra:{value:i[0].value},label:""})),r.source=i,r.add=i,this.value=i,r}}),dt(Zx,Qa,{transform(t,e){var n=e.dataflow,r=e.fork(e.NO_SOURCE|e.NO_FIELDS),i=t.item||Qx,o=t.key||_a,a=this.value;return k(r.encode)&&(r.encode=null),a&&(t.modified("key")||e.modified(o))&&u("DataJoin does not support modified key function or fields."),a||(e=e.addAll(),this.value=a=function(t){const e=ft().test((t=>t.exit));return e.lookup=n=>e.get(t(n)),e}(o)),e.visit(e.ADD,(t=>{const e=o(t);let n=a.get(e);n?n.exit?(a.empty--,r.add.push(n)):r.mod.push(n):(n=i(t),a.set(e,n),r.add.push(n)),n.datum=t,n.exit=!1})),e.visit(e.MOD,(t=>{const e=o(t),n=a.get(e);n&&(n.datum=t,r.mod.push(n))})),e.visit(e.REM,(t=>{const e=o(t),n=a.get(e);t!==n.datum||n.exit||(r.rem.push(n),n.exit=!0,++a.empty)})),e.changed(e.ADD_MOD)&&r.modifies("datum"),(e.clean()||t.clean&&a.empty>n.cleanThreshold)&&n.runAfter(a.clean),r}}),dt(Kx,Qa,{transform(t,e){var n=e.fork(e.ADD_REM),r=t.mod||!1,i=t.encoders,o=e.encode;if(k(o)){if(!n.changed()&&!o.every((t=>i[t])))return e.StopPropagation;o=o[0],n.encode=null}var a="enter"===o,u=i.update||g,s=i.enter||g,l=i.exit||g,c=(o&&!a?i[o]:u)||g;if(e.changed(e.ADD)&&(e.visit(e.ADD,(e=>{s(e,t),u(e,t)})),n.modifies(s.output),n.modifies(u.output),c!==g&&c!==u&&(e.visit(e.ADD,(e=>{c(e,t)})),n.modifies(c.output))),e.changed(e.REM)&&l!==g&&(e.visit(e.REM,(e=>{l(e,t)})),n.modifies(l.output)),a||c!==g){const i=e.MOD|(t.modified()?e.REFLOW:0);a?(e.visit(i,(e=>{const i=s(e,t)||r;(c(e,t)||i)&&n.mod.push(e)})),n.mod.length&&n.modifies(s.output)):e.visit(i,(e=>{(c(e,t)||r)&&n.mod.push(e)})),n.mod.length&&n.modifies(c.output)}return n.changed()?n:e.StopPropagation}}),dt(tb,Qa,{transform(t,e){if(null!=this.value&&!t.modified())return e.StopPropagation;var n,r,i,o,a,u=e.dataflow.locale(),s=e.fork(e.NO_SOURCE|e.NO_FIELDS),l=this.value,c=t.type||bp,f=t.scale,h=+t.limit,d=Ep(f,null==t.count?5:t.count,t.minstep),p=!!t.values||c===bp,g=t.format||zp(u,f,d,c,t.formatSpecifier,t.formatType,p),m=t.values||Bp(f,d);return l&&(s.rem=l),c===bp?(h&&m.length>h?(e.dataflow.warn("Symbol legend count exceeds limit, filtering items."),l=m.slice(0,h-1),a=!0):l=m,J(i=t.size)?(t.values||0!==f(l[0])||(l=l.slice(1)),o=l.reduce(((e,n)=>Math.max(e,i(n,t))),0)):i=rt(o=i||8),l=l.map(((e,n)=>ba({index:n,label:g(e,n,l),value:e,offset:o,size:i(e,t)}))),a&&(a=m[l.length],l.push(ba({index:l.length,label:`…${m.length-l.length} entries`,value:a,offset:o,size:i(a,t)})))):"gradient"===c?(n=f.domain(),r=gp(f,n[0],F(n)),m.length<3&&!t.values&&n[0]!==F(n)&&(m=[n[0],F(n)]),l=m.map(((t,e)=>ba({index:e,label:g(t,e,m),value:t,perc:r(t)})))):(i=m.length-1,r=function(t){const e=t.domain(),n=e.length-1;let r=+e[0],i=+F(e),o=i-r;if(t.type===Ld){const t=n?o/n:.1;r-=t,i+=t,o=i-r}return t=>(t-r)/o}(f),l=m.map(((t,e)=>ba({index:e,label:g(t,e,m),value:t,perc:e?r(t):0,perc2:e===i?1:r(m[e+1])})))),s.source=l,s.add=l,this.value=l,s}});const eb=t=>t.source.x,nb=t=>t.source.y,rb=t=>t.target.x,ib=t=>t.target.y;function ob(t){Qa.call(this,{},t)}ob.Definition={type:"LinkPath",metadata:{modifies:!0},params:[{name:"sourceX",type:"field",default:"source.x"},{name:"sourceY",type:"field",default:"source.y"},{name:"targetX",type:"field",default:"target.x"},{name:"targetY",type:"field",default:"target.y"},{name:"orient",type:"enum",default:"vertical",values:["horizontal","vertical","radial"]},{name:"shape",type:"enum",default:"line",values:["line","arc","curve","diagonal","orthogonal"]},{name:"require",type:"signal"},{name:"as",type:"string",default:"path"}]},dt(ob,Qa,{transform(t,e){var n=t.sourceX||eb,r=t.sourceY||nb,i=t.targetX||rb,o=t.targetY||ib,a=t.as||"path",s=t.orient||"vertical",l=t.shape||"line",c=lb.get(l+"-"+s)||lb.get(l);return c||u("LinkPath unsupported type: "+t.shape+(t.orient?"-"+t.orient:"")),e.visit(e.SOURCE,(t=>{t[a]=c(n(t),r(t),i(t),o(t))})),e.reflow(t.modified()).modifies(a)}});const ab=(t,e,n,r)=>"M"+t+","+e+"L"+n+","+r,ub=(t,e,n,r)=>{var i=n-t,o=r-e,a=Math.hypot(i,o)/2;return"M"+t+","+e+"A"+a+","+a+" "+180*Math.atan2(o,i)/Math.PI+" 0 1 "+n+","+r},sb=(t,e,n,r)=>{const i=n-t,o=r-e,a=.2*(i+o),u=.2*(o-i);return"M"+t+","+e+"C"+(t+a)+","+(e+u)+" "+(n+u)+","+(r-a)+" "+n+","+r},lb=ft({line:ab,"line-radial":(t,e,n,r)=>ab(e*Math.cos(t),e*Math.sin(t),r*Math.cos(n),r*Math.sin(n)),arc:ub,"arc-radial":(t,e,n,r)=>ub(e*Math.cos(t),e*Math.sin(t),r*Math.cos(n),r*Math.sin(n)),curve:sb,"curve-radial":(t,e,n,r)=>sb(e*Math.cos(t),e*Math.sin(t),r*Math.cos(n),r*Math.sin(n)),"orthogonal-horizontal":(t,e,n,r)=>"M"+t+","+e+"V"+r+"H"+n,"orthogonal-vertical":(t,e,n,r)=>"M"+t+","+e+"H"+n+"V"+r,"orthogonal-radial":(t,e,n,r)=>{const i=Math.cos(t),o=Math.sin(t),a=Math.cos(n),u=Math.sin(n);return"M"+e*i+","+e*o+"A"+e+","+e+" 0 0,"+((Math.abs(n-t)>Math.PI?n<=t:n>t)?1:0)+" "+e*a+","+e*u+"L"+r*a+","+r*u},"diagonal-horizontal":(t,e,n,r)=>{const i=(t+n)/2;return"M"+t+","+e+"C"+i+","+e+" "+i+","+r+" "+n+","+r},"diagonal-vertical":(t,e,n,r)=>{const i=(e+r)/2;return"M"+t+","+e+"C"+t+","+i+" "+n+","+i+" "+n+","+r},"diagonal-radial":(t,e,n,r)=>{const i=Math.cos(t),o=Math.sin(t),a=Math.cos(n),u=Math.sin(n),s=(e+r)/2;return"M"+e*i+","+e*o+"C"+s*i+","+s*o+" "+s*a+","+s*u+" "+r*a+","+r*u}});function cb(t){Qa.call(this,null,t)}cb.Definition={type:"Pie",metadata:{modifies:!0},params:[{name:"field",type:"field"},{name:"startAngle",type:"number",default:0},{name:"endAngle",type:"number",default:6.283185307179586},{name:"sort",type:"boolean",default:!1},{name:"as",type:"string",array:!0,length:2,default:["startAngle","endAngle"]}]},dt(cb,Qa,{transform(t,e){var n,r,i,o=t.as||["startAngle","endAngle"],a=o[0],u=o[1],s=t.field||d,l=t.startAngle||0,c=null!=t.endAngle?t.endAngle:2*Math.PI,f=e.source,h=f.map(s),p=h.length,g=l,m=(c-l)/Be(h),y=Te(p);for(t.sort&&y.sort(((t,e)=>h[t]-h[e])),n=0;n<p;++n)i=h[y[n]],(r=f[y[n]])[a]=g,r[u]=g+=i*m;return this.value=h,e.reflow(t.modified()).modifies(o)}});const fb=5;function hb(t){return op(t)&&t!==zd}const db=Nt(["set","modified","clear","type","scheme","schemeExtent","schemeCount","domain","domainMin","domainMid","domainMax","domainRaw","domainImplicit","nice","zero","bins","range","rangeStep","round","reverse","interpolate","interpolateGamma"]);function pb(t){Qa.call(this,null,t),this.modified(!0)}function gb(t,e,n){sp(t)&&(Math.abs(e.reduce(((t,e)=>t+(e<0?-1:e>0?1:0)),0))!==e.length&&n.warn("Log scale domain includes zero: "+Ft(e)));return e}function mb(t,e,n){return J(t)&&(e||n)?hp(t,yb(e||[0,1],n)):t}function yb(t,e){return e?t.slice().reverse():t}function vb(t){Qa.call(this,null,t)}dt(pb,Qa,{transform(t,e){var n=e.dataflow,r=this.value,i=function(t){var e,n=t.type,r="";if(n===zd)return zd+"-"+Cd;(function(t){const e=t.type;return op(e)&&e!==Bd&&e!==Nd&&(t.scheme||t.range&&t.range.length&&t.range.every(xt))})(t)&&(r=2===(e=t.rawDomain?t.rawDomain.length:t.domain?t.domain.length+ +(null!=t.domainMid):0)?zd+"-":3===e?Od+"-":"");return(r+n||Cd).toLowerCase()}(t);for(i in r&&i===r.type||(this.value=r=np(i)()),t)if(!db[i]){if("padding"===i&&hb(r.type))continue;J(r[i])?r[i](t[i]):n.warn("Unsupported scale property: "+i)}return function(t,e,n){var r=t.type,i=e.round||!1,o=e.range;if(null!=e.rangeStep)o=function(t,e,n){t!==jd&&t!==Pd&&u("Only band and point scales support rangeStep.");var r=(null!=e.paddingOuter?e.paddingOuter:e.padding)||0,i=t===Pd?1:(null!=e.paddingInner?e.paddingInner:e.padding)||0;return[0,e.rangeStep*Dd(n,i,r)]}(r,e,n);else if(e.scheme&&(o=function(t,e,n){var r,i=e.schemeExtent;k(e.scheme)?r=dp(e.scheme,e.interpolate,e.interpolateGamma):(r=xp(e.scheme.toLowerCase()))||u(`Unrecognized scheme name: ${e.scheme}`);return n=t===Ld?n+1:t===Id?n-1:t===Rd||t===Ud?+e.schemeCount||fb:n,lp(t)?mb(r,i,e.reverse):J(r)?pp(mb(r,i),n):t===qd?r:r.slice(0,n)}(r,e,n),J(o))){if(t.interpolator)return t.interpolator(o);u(`Scale type ${r} does not support interpolating color schemes.`)}if(o&&lp(r))return t.interpolator(dp(yb(o,e.reverse),e.interpolate,e.interpolateGamma));o&&e.interpolate&&t.interpolate?t.interpolate(mp(e.interpolate,e.interpolateGamma)):J(t.round)?t.round(i):J(t.rangeRound)&&t.interpolate(i?kh:wh);o&&t.range(yb(o,e.reverse))}(r,t,function(t,e,n){let r=e.bins;if(r&&!k(r)){const e=t.domain(),n=e[0],i=F(e),o=r.step;let a=null==r.start?n:r.start,s=null==r.stop?i:r.stop;o||u("Scale bins parameter missing step property."),a<n&&(a=o*Math.ceil(n/o)),s>i&&(s=o*Math.floor(i/o)),r=Te(a,s+o/2,o)}r?t.bins=r:t.bins&&delete t.bins;t.type===Id&&(r?e.domain||e.domainRaw||(t.domain(r),n=r.length):t.bins=t.domain());return n}(r,t,function(t,e,n){const r=function(t,e,n){return e?(t.domain(gb(t.type,e,n)),e.length):-1}(t,e.domainRaw,n);if(r>-1)return r;var i,o,a=e.domain,u=t.type,s=e.zero||void 0===e.zero&&function(t){const e=t.type;return!t.bins&&(e===Cd||e===Sd||e===$d)}(t);if(!a)return 0;hb(u)&&e.padding&&a[0]!==F(a)&&(a=function(t,e,n,r,i,o){var a=Math.abs(F(n)-n[0]),u=a/(a-2*r),s=t===Fd?I(e,null,u):t===$d?W(e,null,u,.5):t===Sd?W(e,null,u,i||1):t===Td?H(e,null,u,o||1):j(e,null,u);return e=e.slice(),e[0]=s[0],e[e.length-1]=s[1],e}(u,a,e.range,e.padding,e.exponent,e.constant));if((s||null!=e.domainMin||null!=e.domainMax||null!=e.domainMid)&&(i=(a=a.slice()).length-1||1,s&&(a[0]>0&&(a[0]=0),a[i]<0&&(a[i]=0)),null!=e.domainMin&&(a[0]=e.domainMin),null!=e.domainMax&&(a[i]=e.domainMax),null!=e.domainMid)){const t=(o=e.domainMid)>a[i]?i+1:o<a[0]?0:i;t!==i&&n.warn("Scale domainMid exceeds domain min or max.",o),a.splice(t,0,o)}t.domain(gb(u,a,n)),u===qd&&t.unknown(e.domainImplicit?Pc:void 0);e.nice&&t.nice&&t.nice(!0!==e.nice&&Ep(t,e.nice)||null);return a.length}(r,t,n))),e.fork(e.NO_SOURCE|e.NO_FIELDS)}}),dt(vb,Qa,{transform(t,e){const n=t.modified("sort")||e.changed(e.ADD)||e.modified(t.sort.fields)||e.modified("datum");return n&&e.source.sort(Ma(t.sort)),this.modified(n),e}});const _b="zero",xb="center",bb="normalize",wb=["y0","y1"];function kb(t){Qa.call(this,null,t)}function Ab(t,e,n,r,i){for(var o,a=(e-t.sum)/2,u=t.length,s=0;s<u;++s)(o=t[s])[r]=a,o[i]=a+=Math.abs(n(o))}function Mb(t,e,n,r,i){for(var o,a=1/t.sum,u=0,s=t.length,l=0,c=0;l<s;++l)(o=t[l])[r]=u,o[i]=u=a*(c+=Math.abs(n(o)))}function Eb(t,e,n,r,i){for(var o,a,u=0,s=0,l=t.length,c=0;c<l;++c)(o=+n(a=t[c]))<0?(a[r]=s,a[i]=s+=o):(a[r]=u,a[i]=u+=o)}kb.Definition={type:"Stack",metadata:{modifies:!0},params:[{name:"field",type:"field"},{name:"groupby",type:"field",array:!0},{name:"sort",type:"compare"},{name:"offset",type:"enum",default:_b,values:[_b,xb,bb]},{name:"as",type:"string",array:!0,length:2,default:wb}]},dt(kb,Qa,{transform(t,e){var n,r,i,o,a=t.as||wb,u=a[0],s=a[1],l=Ma(t.sort),c=t.field||d,f=t.offset===xb?Ab:t.offset===bb?Mb:Eb;for(n=function(t,e,n,r){var i,o,a,u,s,l,c,f,h,d=[],p=t=>t(s);if(null==e)d.push(t.slice());else for(i={},o=0,a=t.length;o<a;++o)s=t[o],(c=i[l=e.map(p)])||(i[l]=c=[],d.push(c)),c.push(s);for(l=0,h=0,u=d.length;l<u;++l){for(o=0,f=0,a=(c=d[l]).length;o<a;++o)f+=Math.abs(r(c[o]));c.sum=f,f>h&&(h=f),n&&c.sort(n)}return d.max=h,d}(e.source,t.groupby,l,c),r=0,i=n.length,o=n.max;r<i;++r)f(n[r],o,c,u,s);return e.reflow(t.modified()).modifies(a)}});var Db=Object.freeze({__proto__:null,axisticks:Jx,datajoin:Zx,encode:Kx,legendentries:tb,linkpath:ob,pie:cb,scale:pb,sortitems:vb,stack:kb}),Cb=1e-6,Fb=1e-12,Sb=Math.PI,$b=Sb/2,Tb=Sb/4,Bb=2*Sb,Nb=180/Sb,zb=Sb/180,Ob=Math.abs,Rb=Math.atan,Ub=Math.atan2,Lb=Math.cos,qb=Math.ceil,Pb=Math.exp,jb=Math.hypot,Ib=Math.log,Wb=Math.pow,Hb=Math.sin,Yb=Math.sign||function(t){return t>0?1:t<0?-1:0},Gb=Math.sqrt,Vb=Math.tan;function Xb(t){return t>1?0:t<-1?Sb:Math.acos(t)}function Jb(t){return t>1?$b:t<-1?-$b:Math.asin(t)}function Zb(){}function Qb(t,e){t&&tw.hasOwnProperty(t.type)&&tw[t.type](t,e)}var Kb={Feature:function(t,e){Qb(t.geometry,e)},FeatureCollection:function(t,e){for(var n=t.features,r=-1,i=n.length;++r<i;)Qb(n[r].geometry,e)}},tw={Sphere:function(t,e){e.sphere()},Point:function(t,e){t=t.coordinates,e.point(t[0],t[1],t[2])},MultiPoint:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)t=n[r],e.point(t[0],t[1],t[2])},LineString:function(t,e){ew(t.coordinates,e,0)},MultiLineString:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)ew(n[r],e,0)},Polygon:function(t,e){nw(t.coordinates,e)},MultiPolygon:function(t,e){for(var n=t.coordinates,r=-1,i=n.length;++r<i;)nw(n[r],e)},GeometryCollection:function(t,e){for(var n=t.geometries,r=-1,i=n.length;++r<i;)Qb(n[r],e)}};function ew(t,e,n){var r,i=-1,o=t.length-n;for(e.lineStart();++i<o;)r=t[i],e.point(r[0],r[1],r[2]);e.lineEnd()}function nw(t,e){var n=-1,r=t.length;for(e.polygonStart();++n<r;)ew(t[n],e,1);e.polygonEnd()}function rw(t,e){t&&Kb.hasOwnProperty(t.type)?Kb[t.type](t,e):Qb(t,e)}var iw,ow,aw,uw,sw,lw,cw,fw,hw,dw,pw,gw,mw,yw,vw,_w,xw=new le,bw=new le,ww={point:Zb,lineStart:Zb,lineEnd:Zb,polygonStart:function(){xw=new le,ww.lineStart=kw,ww.lineEnd=Aw},polygonEnd:function(){var t=+xw;bw.add(t<0?Bb+t:t),this.lineStart=this.lineEnd=this.point=Zb},sphere:function(){bw.add(Bb)}};function kw(){ww.point=Mw}function Aw(){Ew(iw,ow)}function Mw(t,e){ww.point=Ew,iw=t,ow=e,aw=t*=zb,uw=Lb(e=(e*=zb)/2+Tb),sw=Hb(e)}function Ew(t,e){var n=(t*=zb)-aw,r=n>=0?1:-1,i=r*n,o=Lb(e=(e*=zb)/2+Tb),a=Hb(e),u=sw*a,s=uw*o+u*Lb(i),l=u*r*Hb(i);xw.add(Ub(l,s)),aw=t,uw=o,sw=a}function Dw(t){return[Ub(t[1],t[0]),Jb(t[2])]}function Cw(t){var e=t[0],n=t[1],r=Lb(n);return[r*Lb(e),r*Hb(e),Hb(n)]}function Fw(t,e){return t[0]*e[0]+t[1]*e[1]+t[2]*e[2]}function Sw(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function $w(t,e){t[0]+=e[0],t[1]+=e[1],t[2]+=e[2]}function Tw(t,e){return[t[0]*e,t[1]*e,t[2]*e]}function Bw(t){var e=Gb(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=e,t[1]/=e,t[2]/=e}var Nw,zw,Ow,Rw,Uw,Lw,qw,Pw,jw,Iw,Ww,Hw,Yw,Gw,Vw,Xw,Jw={point:Zw,lineStart:Kw,lineEnd:tk,polygonStart:function(){Jw.point=ek,Jw.lineStart=nk,Jw.lineEnd=rk,yw=new le,ww.polygonStart()},polygonEnd:function(){ww.polygonEnd(),Jw.point=Zw,Jw.lineStart=Kw,Jw.lineEnd=tk,xw<0?(lw=-(fw=180),cw=-(hw=90)):yw>Cb?hw=90:yw<-Cb&&(cw=-90),_w[0]=lw,_w[1]=fw},sphere:function(){lw=-(fw=180),cw=-(hw=90)}};function Zw(t,e){vw.push(_w=[lw=t,fw=t]),e<cw&&(cw=e),e>hw&&(hw=e)}function Qw(t,e){var n=Cw([t*zb,e*zb]);if(mw){var r=Sw(mw,n),i=Sw([r[1],-r[0],0],r);Bw(i),i=Dw(i);var o,a=t-dw,u=a>0?1:-1,s=i[0]*Nb*u,l=Ob(a)>180;l^(u*dw<s&&s<u*t)?(o=i[1]*Nb)>hw&&(hw=o):l^(u*dw<(s=(s+360)%360-180)&&s<u*t)?(o=-i[1]*Nb)<cw&&(cw=o):(e<cw&&(cw=e),e>hw&&(hw=e)),l?t<dw?ik(lw,t)>ik(lw,fw)&&(fw=t):ik(t,fw)>ik(lw,fw)&&(lw=t):fw>=lw?(t<lw&&(lw=t),t>fw&&(fw=t)):t>dw?ik(lw,t)>ik(lw,fw)&&(fw=t):ik(t,fw)>ik(lw,fw)&&(lw=t)}else vw.push(_w=[lw=t,fw=t]);e<cw&&(cw=e),e>hw&&(hw=e),mw=n,dw=t}function Kw(){Jw.point=Qw}function tk(){_w[0]=lw,_w[1]=fw,Jw.point=Zw,mw=null}function ek(t,e){if(mw){var n=t-dw;yw.add(Ob(n)>180?n+(n>0?360:-360):n)}else pw=t,gw=e;ww.point(t,e),Qw(t,e)}function nk(){ww.lineStart()}function rk(){ek(pw,gw),ww.lineEnd(),Ob(yw)>Cb&&(lw=-(fw=180)),_w[0]=lw,_w[1]=fw,mw=null}function ik(t,e){return(e-=t)<0?e+360:e}function ok(t,e){return t[0]-e[0]}function ak(t,e){return t[0]<=t[1]?t[0]<=e&&e<=t[1]:e<t[0]||t[1]<e}var uk={sphere:Zb,point:sk,lineStart:ck,lineEnd:dk,polygonStart:function(){uk.lineStart=pk,uk.lineEnd=gk},polygonEnd:function(){uk.lineStart=ck,uk.lineEnd=dk}};function sk(t,e){t*=zb;var n=Lb(e*=zb);lk(n*Lb(t),n*Hb(t),Hb(e))}function lk(t,e,n){++Nw,Ow+=(t-Ow)/Nw,Rw+=(e-Rw)/Nw,Uw+=(n-Uw)/Nw}function ck(){uk.point=fk}function fk(t,e){t*=zb;var n=Lb(e*=zb);Gw=n*Lb(t),Vw=n*Hb(t),Xw=Hb(e),uk.point=hk,lk(Gw,Vw,Xw)}function hk(t,e){t*=zb;var n=Lb(e*=zb),r=n*Lb(t),i=n*Hb(t),o=Hb(e),a=Ub(Gb((a=Vw*o-Xw*i)*a+(a=Xw*r-Gw*o)*a+(a=Gw*i-Vw*r)*a),Gw*r+Vw*i+Xw*o);zw+=a,Lw+=a*(Gw+(Gw=r)),qw+=a*(Vw+(Vw=i)),Pw+=a*(Xw+(Xw=o)),lk(Gw,Vw,Xw)}function dk(){uk.point=sk}function pk(){uk.point=mk}function gk(){yk(Hw,Yw),uk.point=sk}function mk(t,e){Hw=t,Yw=e,t*=zb,e*=zb,uk.point=yk;var n=Lb(e);Gw=n*Lb(t),Vw=n*Hb(t),Xw=Hb(e),lk(Gw,Vw,Xw)}function yk(t,e){t*=zb;var n=Lb(e*=zb),r=n*Lb(t),i=n*Hb(t),o=Hb(e),a=Vw*o-Xw*i,u=Xw*r-Gw*o,s=Gw*i-Vw*r,l=jb(a,u,s),c=Jb(l),f=l&&-c/l;jw.add(f*a),Iw.add(f*u),Ww.add(f*s),zw+=c,Lw+=c*(Gw+(Gw=r)),qw+=c*(Vw+(Vw=i)),Pw+=c*(Xw+(Xw=o)),lk(Gw,Vw,Xw)}function vk(t,e){function n(n,r){return n=t(n,r),e(n[0],n[1])}return t.invert&&e.invert&&(n.invert=function(n,r){return(n=e.invert(n,r))&&t.invert(n[0],n[1])}),n}function _k(t,e){return Ob(t)>Sb&&(t-=Math.round(t/Bb)*Bb),[t,e]}function xk(t,e,n){return(t%=Bb)?e||n?vk(wk(t),kk(e,n)):wk(t):e||n?kk(e,n):_k}function bk(t){return function(e,n){return Ob(e+=t)>Sb&&(e-=Math.round(e/Bb)*Bb),[e,n]}}function wk(t){var e=bk(t);return e.invert=bk(-t),e}function kk(t,e){var n=Lb(t),r=Hb(t),i=Lb(e),o=Hb(e);function a(t,e){var a=Lb(e),u=Lb(t)*a,s=Hb(t)*a,l=Hb(e),c=l*n+u*r;return[Ub(s*i-c*o,u*n-l*r),Jb(c*i+s*o)]}return a.invert=function(t,e){var a=Lb(e),u=Lb(t)*a,s=Hb(t)*a,l=Hb(e),c=l*i-s*o;return[Ub(s*i+l*o,u*n+c*r),Jb(c*n-u*r)]},a}function Ak(t,e){(e=Cw(e))[0]-=t,Bw(e);var n=Xb(-e[1]);return((-e[2]<0?-n:n)+Bb-Cb)%Bb}function Mk(){var t,e=[];return{point:function(e,n,r){t.push([e,n,r])},lineStart:function(){e.push(t=[])},lineEnd:Zb,rejoin:function(){e.length>1&&e.push(e.pop().concat(e.shift()))},result:function(){var n=e;return e=[],t=null,n}}}function Ek(t,e){return Ob(t[0]-e[0])<Cb&&Ob(t[1]-e[1])<Cb}function Dk(t,e,n,r){this.x=t,this.z=e,this.o=n,this.e=r,this.v=!1,this.n=this.p=null}function Ck(t,e,n,r,i){var o,a,u=[],s=[];if(t.forEach((function(t){if(!((e=t.length-1)<=0)){var e,n,r=t[0],a=t[e];if(Ek(r,a)){if(!r[2]&&!a[2]){for(i.lineStart(),o=0;o<e;++o)i.point((r=t[o])[0],r[1]);return void i.lineEnd()}a[0]+=2*Cb}u.push(n=new Dk(r,t,null,!0)),s.push(n.o=new Dk(r,null,n,!1)),u.push(n=new Dk(a,t,null,!1)),s.push(n.o=new Dk(a,null,n,!0))}})),u.length){for(s.sort(e),Fk(u),Fk(s),o=0,a=s.length;o<a;++o)s[o].e=n=!n;for(var l,c,f=u[0];;){for(var h=f,d=!0;h.v;)if((h=h.n)===f)return;l=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(d)for(o=0,a=l.length;o<a;++o)i.point((c=l[o])[0],c[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(d)for(l=h.p.z,o=l.length-1;o>=0;--o)i.point((c=l[o])[0],c[1]);else r(h.x,h.p.x,-1,i);h=h.p}l=(h=h.o).z,d=!d}while(!h.v);i.lineEnd()}}}function Fk(t){if(e=t.length){for(var e,n,r=0,i=t[0];++r<e;)i.n=n=t[r],n.p=i,i=n;i.n=n=t[0],n.p=i}}function Sk(t){return Ob(t[0])<=Sb?t[0]:Yb(t[0])*((Ob(t[0])+Sb)%Bb-Sb)}function $k(t,e,n,r){return function(i){var o,a,u,s=e(i),l=Mk(),c=e(l),f=!1,h={point:d,lineStart:g,lineEnd:m,polygonStart:function(){h.point=y,h.lineStart=v,h.lineEnd=_,a=[],o=[]},polygonEnd:function(){h.point=d,h.lineStart=g,h.lineEnd=m,a=$e(a);var t=function(t,e){var n=Sk(e),r=e[1],i=Hb(r),o=[Hb(n),-Lb(n),0],a=0,u=0,s=new le;1===i?r=$b+Cb:-1===i&&(r=-$b-Cb);for(var l=0,c=t.length;l<c;++l)if(h=(f=t[l]).length)for(var f,h,d=f[h-1],p=Sk(d),g=d[1]/2+Tb,m=Hb(g),y=Lb(g),v=0;v<h;++v,p=x,m=w,y=k,d=_){var _=f[v],x=Sk(_),b=_[1]/2+Tb,w=Hb(b),k=Lb(b),A=x-p,M=A>=0?1:-1,E=M*A,D=E>Sb,C=m*w;if(s.add(Ub(C*M*Hb(E),y*k+C*Lb(E))),a+=D?A+M*Bb:A,D^p>=n^x>=n){var F=Sw(Cw(d),Cw(_));Bw(F);var S=Sw(o,F);Bw(S);var $=(D^A>=0?-1:1)*Jb(S[2]);(r>$||r===$&&(F[0]||F[1]))&&(u+=D^A>=0?1:-1)}}return(a<-Cb||a<Cb&&s<-Fb)^1&u}(o,r);a.length?(f||(i.polygonStart(),f=!0),Ck(a,Bk,t,n,i)):t&&(f||(i.polygonStart(),f=!0),i.lineStart(),n(null,null,1,i),i.lineEnd()),f&&(i.polygonEnd(),f=!1),a=o=null},sphere:function(){i.polygonStart(),i.lineStart(),n(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(e,n){t(e,n)&&i.point(e,n)}function p(t,e){s.point(t,e)}function g(){h.point=p,s.lineStart()}function m(){h.point=d,s.lineEnd()}function y(t,e){u.push([t,e]),c.point(t,e)}function v(){c.lineStart(),u=[]}function _(){y(u[0][0],u[0][1]),c.lineEnd();var t,e,n,r,s=c.clean(),h=l.result(),d=h.length;if(u.pop(),o.push(u),u=null,d)if(1&s){if((e=(n=h[0]).length-1)>0){for(f||(i.polygonStart(),f=!0),i.lineStart(),t=0;t<e;++t)i.point((r=n[t])[0],r[1]);i.lineEnd()}}else d>1&&2&s&&h.push(h.pop().concat(h.shift())),a.push(h.filter(Tk))}return h}}function Tk(t){return t.length>1}function Bk(t,e){return((t=t.x)[0]<0?t[1]-$b-Cb:$b-t[1])-((e=e.x)[0]<0?e[1]-$b-Cb:$b-e[1])}_k.invert=_k;var Nk=$k((function(){return!0}),(function(t){var e,n=NaN,r=NaN,i=NaN;return{lineStart:function(){t.lineStart(),e=1},point:function(o,a){var u=o>0?Sb:-Sb,s=Ob(o-n);Ob(s-Sb)<Cb?(t.point(n,r=(r+a)/2>0?$b:-$b),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(u,r),t.point(o,r),e=0):i!==u&&s>=Sb&&(Ob(n-i)<Cb&&(n-=i*Cb),Ob(o-u)<Cb&&(o-=u*Cb),r=function(t,e,n,r){var i,o,a=Hb(t-n);return Ob(a)>Cb?Rb((Hb(e)*(o=Lb(r))*Hb(n)-Hb(r)*(i=Lb(e))*Hb(t))/(i*o*a)):(e+r)/2}(n,r,o,a),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(u,r),e=0),t.point(n=o,r=a),i=u},lineEnd:function(){t.lineEnd(),n=r=NaN},clean:function(){return 2-e}}}),(function(t,e,n,r){var i;if(null==t)i=n*$b,r.point(-Sb,i),r.point(0,i),r.point(Sb,i),r.point(Sb,0),r.point(Sb,-i),r.point(0,-i),r.point(-Sb,-i),r.point(-Sb,0),r.point(-Sb,i);else if(Ob(t[0]-e[0])>Cb){var o=t[0]<e[0]?Sb:-Sb;i=n*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(e[0],e[1])}),[-Sb,-$b]);function zk(t){var e=Lb(t),n=6*zb,r=e>0,i=Ob(e)>Cb;function o(t,n){return Lb(t)*Lb(n)>e}function a(t,n,r){var i=[1,0,0],o=Sw(Cw(t),Cw(n)),a=Fw(o,o),u=o[0],s=a-u*u;if(!s)return!r&&t;var l=e*a/s,c=-e*u/s,f=Sw(i,o),h=Tw(i,l);$w(h,Tw(o,c));var d=f,p=Fw(h,d),g=Fw(d,d),m=p*p-g*(Fw(h,h)-1);if(!(m<0)){var y=Gb(m),v=Tw(d,(-p-y)/g);if($w(v,h),v=Dw(v),!r)return v;var _,x=t[0],b=n[0],w=t[1],k=n[1];b<x&&(_=x,x=b,b=_);var A=b-x,M=Ob(A-Sb)<Cb;if(!M&&k<w&&(_=w,w=k,k=_),M||A<Cb?M?w+k>0^v[1]<(Ob(v[0]-x)<Cb?w:k):w<=v[1]&&v[1]<=k:A>Sb^(x<=v[0]&&v[0]<=b)){var E=Tw(d,(-p+y)/g);return $w(E,h),[v,Dw(E)]}}}function u(e,n){var i=r?t:Sb-t,o=0;return e<-i?o|=1:e>i&&(o|=2),n<-i?o|=4:n>i&&(o|=8),o}return $k(o,(function(t){var e,n,s,l,c;return{lineStart:function(){l=s=!1,c=1},point:function(f,h){var d,p=[f,h],g=o(f,h),m=r?g?0:u(f,h):g?u(f+(f<0?Sb:-Sb),h):0;if(!e&&(l=s=g)&&t.lineStart(),g!==s&&(!(d=a(e,p))||Ek(e,d)||Ek(p,d))&&(p[2]=1),g!==s)c=0,g?(t.lineStart(),d=a(p,e),t.point(d[0],d[1])):(d=a(e,p),t.point(d[0],d[1],2),t.lineEnd()),e=d;else if(i&&e&&r^g){var y;m&n||!(y=a(p,e,!0))||(c=0,r?(t.lineStart(),t.point(y[0][0],y[0][1]),t.point(y[1][0],y[1][1]),t.lineEnd()):(t.point(y[1][0],y[1][1]),t.lineEnd(),t.lineStart(),t.point(y[0][0],y[0][1],3)))}!g||e&&Ek(e,p)||t.point(p[0],p[1]),e=p,s=g,n=m},lineEnd:function(){s&&t.lineEnd(),e=null},clean:function(){return c|(l&&s)<<1}}}),(function(e,r,i,o){!function(t,e,n,r,i,o){if(n){var a=Lb(e),u=Hb(e),s=r*n;null==i?(i=e+r*Bb,o=e-s/2):(i=Ak(a,i),o=Ak(a,o),(r>0?i<o:i>o)&&(i+=r*Bb));for(var l,c=i;r>0?c>o:c<o;c-=s)l=Dw([a,-u*Lb(c),-u*Hb(c)]),t.point(l[0],l[1])}}(o,t,n,i,e,r)}),r?[0,-t]:[-Sb,t-Sb])}var Ok=1e9,Rk=-Ok;function Uk(t,e,n,r){function i(i,o){return t<=i&&i<=n&&e<=o&&o<=r}function o(i,o,u,l){var c=0,f=0;if(null==i||(c=a(i,u))!==(f=a(o,u))||s(i,o)<0^u>0)do{l.point(0===c||3===c?t:n,c>1?r:e)}while((c=(c+u+4)%4)!==f);else l.point(o[0],o[1])}function a(r,i){return Ob(r[0]-t)<Cb?i>0?0:3:Ob(r[0]-n)<Cb?i>0?2:1:Ob(r[1]-e)<Cb?i>0?1:0:i>0?3:2}function u(t,e){return s(t.x,e.x)}function s(t,e){var n=a(t,1),r=a(e,1);return n!==r?n-r:0===n?e[1]-t[1]:1===n?t[0]-e[0]:2===n?t[1]-e[1]:e[0]-t[0]}return function(a){var s,l,c,f,h,d,p,g,m,y,v,_=a,x=Mk(),b={point:w,lineStart:function(){b.point=k,l&&l.push(c=[]);y=!0,m=!1,p=g=NaN},lineEnd:function(){s&&(k(f,h),d&&m&&x.rejoin(),s.push(x.result()));b.point=w,m&&_.lineEnd()},polygonStart:function(){_=x,s=[],l=[],v=!0},polygonEnd:function(){var e=function(){for(var e=0,n=0,i=l.length;n<i;++n)for(var o,a,u=l[n],s=1,c=u.length,f=u[0],h=f[0],d=f[1];s<c;++s)o=h,a=d,h=(f=u[s])[0],d=f[1],a<=r?d>r&&(h-o)*(r-a)>(d-a)*(t-o)&&++e:d<=r&&(h-o)*(r-a)<(d-a)*(t-o)&&--e;return e}(),n=v&&e,i=(s=$e(s)).length;(n||i)&&(a.polygonStart(),n&&(a.lineStart(),o(null,null,1,a),a.lineEnd()),i&&Ck(s,u,e,o,a),a.polygonEnd());_=a,s=l=c=null}};function w(t,e){i(t,e)&&_.point(t,e)}function k(o,a){var u=i(o,a);if(l&&c.push([o,a]),y)f=o,h=a,d=u,y=!1,u&&(_.lineStart(),_.point(o,a));else if(u&&m)_.point(o,a);else{var s=[p=Math.max(Rk,Math.min(Ok,p)),g=Math.max(Rk,Math.min(Ok,g))],x=[o=Math.max(Rk,Math.min(Ok,o)),a=Math.max(Rk,Math.min(Ok,a))];!function(t,e,n,r,i,o){var a,u=t[0],s=t[1],l=0,c=1,f=e[0]-u,h=e[1]-s;if(a=n-u,f||!(a>0)){if(a/=f,f<0){if(a<l)return;a<c&&(c=a)}else if(f>0){if(a>c)return;a>l&&(l=a)}if(a=i-u,f||!(a<0)){if(a/=f,f<0){if(a>c)return;a>l&&(l=a)}else if(f>0){if(a<l)return;a<c&&(c=a)}if(a=r-s,h||!(a>0)){if(a/=h,h<0){if(a<l)return;a<c&&(c=a)}else if(h>0){if(a>c)return;a>l&&(l=a)}if(a=o-s,h||!(a<0)){if(a/=h,h<0){if(a>c)return;a>l&&(l=a)}else if(h>0){if(a<l)return;a<c&&(c=a)}return l>0&&(t[0]=u+l*f,t[1]=s+l*h),c<1&&(e[0]=u+c*f,e[1]=s+c*h),!0}}}}}(s,x,t,e,n,r)?u&&(_.lineStart(),_.point(o,a),v=!1):(m||(_.lineStart(),_.point(s[0],s[1])),_.point(x[0],x[1]),u||_.lineEnd(),v=!1)}p=o,g=a,m=u}return b}}function Lk(t,e,n){var r=Te(t,e-Cb,n).concat(e);return function(t){return r.map((function(e){return[t,e]}))}}function qk(t,e,n){var r=Te(t,e-Cb,n).concat(e);return function(t){return r.map((function(e){return[e,t]}))}}var Pk,jk,Ik,Wk,Hk=t=>t,Yk=new le,Gk=new le,Vk={point:Zb,lineStart:Zb,lineEnd:Zb,polygonStart:function(){Vk.lineStart=Xk,Vk.lineEnd=Qk},polygonEnd:function(){Vk.lineStart=Vk.lineEnd=Vk.point=Zb,Yk.add(Ob(Gk)),Gk=new le},result:function(){var t=Yk/2;return Yk=new le,t}};function Xk(){Vk.point=Jk}function Jk(t,e){Vk.point=Zk,Pk=Ik=t,jk=Wk=e}function Zk(t,e){Gk.add(Wk*t-Ik*e),Ik=t,Wk=e}function Qk(){Zk(Pk,jk)}var Kk=Vk,tA=1/0,eA=tA,nA=-tA,rA=nA,iA={point:function(t,e){t<tA&&(tA=t);t>nA&&(nA=t);e<eA&&(eA=e);e>rA&&(rA=e)},lineStart:Zb,lineEnd:Zb,polygonStart:Zb,polygonEnd:Zb,result:function(){var t=[[tA,eA],[nA,rA]];return nA=rA=-(eA=tA=1/0),t}};var oA,aA,uA,sA,lA=iA,cA=0,fA=0,hA=0,dA=0,pA=0,gA=0,mA=0,yA=0,vA=0,_A={point:xA,lineStart:bA,lineEnd:AA,polygonStart:function(){_A.lineStart=MA,_A.lineEnd=EA},polygonEnd:function(){_A.point=xA,_A.lineStart=bA,_A.lineEnd=AA},result:function(){var t=vA?[mA/vA,yA/vA]:gA?[dA/gA,pA/gA]:hA?[cA/hA,fA/hA]:[NaN,NaN];return cA=fA=hA=dA=pA=gA=mA=yA=vA=0,t}};function xA(t,e){cA+=t,fA+=e,++hA}function bA(){_A.point=wA}function wA(t,e){_A.point=kA,xA(uA=t,sA=e)}function kA(t,e){var n=t-uA,r=e-sA,i=Gb(n*n+r*r);dA+=i*(uA+t)/2,pA+=i*(sA+e)/2,gA+=i,xA(uA=t,sA=e)}function AA(){_A.point=xA}function MA(){_A.point=DA}function EA(){CA(oA,aA)}function DA(t,e){_A.point=CA,xA(oA=uA=t,aA=sA=e)}function CA(t,e){var n=t-uA,r=e-sA,i=Gb(n*n+r*r);dA+=i*(uA+t)/2,pA+=i*(sA+e)/2,gA+=i,mA+=(i=sA*t-uA*e)*(uA+t),yA+=i*(sA+e),vA+=3*i,xA(uA=t,sA=e)}var FA=_A;function SA(t){this._context=t}SA.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,e){switch(this._point){case 0:this._context.moveTo(t,e),this._point=1;break;case 1:this._context.lineTo(t,e);break;default:this._context.moveTo(t+this._radius,e),this._context.arc(t,e,this._radius,0,Bb)}},result:Zb};var $A,TA,BA,NA,zA,OA=new le,RA={point:Zb,lineStart:function(){RA.point=UA},lineEnd:function(){$A&&LA(TA,BA),RA.point=Zb},polygonStart:function(){$A=!0},polygonEnd:function(){$A=null},result:function(){var t=+OA;return OA=new le,t}};function UA(t,e){RA.point=LA,TA=NA=t,BA=zA=e}function LA(t,e){NA-=t,zA-=e,OA.add(Gb(NA*NA+zA*zA)),NA=t,zA=e}var qA=RA;let PA,jA,IA,WA;class HA{constructor(t){this._append=null==t?YA:function(t){const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);if(e>15)return YA;if(e!==PA){const t=10**e;PA=e,jA=function(e){let n=1;this._+=e[0];for(const r=e.length;n<r;++n)this._+=Math.round(arguments[n]*t)/t+e[n]}}return jA}(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){0===this._line&&(this._+="Z"),this._point=NaN}point(t,e){switch(this._point){case 0:this._append`M${t},${e}`,this._point=1;break;case 1:this._append`L${t},${e}`;break;default:if(this._append`M${t},${e}`,this._radius!==IA||this._append!==jA){const t=this._radius,e=this._;this._="",this._append`m0,${t}a${t},${t} 0 1,1 0,${-2*t}a${t},${t} 0 1,1 0,${2*t}z`,IA=t,jA=this._append,WA=this._,this._=e}this._+=WA}}result(){const t=this._;return this._="",t.length?t:null}}function YA(t){let e=1;this._+=t[0];for(const n=t.length;e<n;++e)this._+=arguments[e]+t[e]}function GA(t,e){let n,r,i=3,o=4.5;function a(t){return t&&("function"==typeof o&&r.pointRadius(+o.apply(this,arguments)),rw(t,n(r))),r.result()}return a.area=function(t){return rw(t,n(Kk)),Kk.result()},a.measure=function(t){return rw(t,n(qA)),qA.result()},a.bounds=function(t){return rw(t,n(lA)),lA.result()},a.centroid=function(t){return rw(t,n(FA)),FA.result()},a.projection=function(e){return arguments.length?(n=null==e?(t=null,Hk):(t=e).stream,a):t},a.context=function(t){return arguments.length?(r=null==t?(e=null,new HA(i)):new SA(e=t),"function"!=typeof o&&r.pointRadius(o),a):e},a.pointRadius=function(t){return arguments.length?(o="function"==typeof t?t:(r.pointRadius(+t),+t),a):o},a.digits=function(t){if(!arguments.length)return i;if(null==t)i=null;else{const e=Math.floor(t);if(!(e>=0))throw new RangeError(`invalid digits: ${t}`);i=e}return null===e&&(r=new HA(i)),a},a.projection(t).digits(i).context(e)}function VA(t){return function(e){var n=new XA;for(var r in t)n[r]=t[r];return n.stream=e,n}}function XA(){}function JA(t,e,n){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),rw(n,t.stream(lA)),e(lA.result()),null!=r&&t.clipExtent(r),t}function ZA(t,e,n){return JA(t,(function(n){var r=e[1][0]-e[0][0],i=e[1][1]-e[0][1],o=Math.min(r/(n[1][0]-n[0][0]),i/(n[1][1]-n[0][1])),a=+e[0][0]+(r-o*(n[1][0]+n[0][0]))/2,u=+e[0][1]+(i-o*(n[1][1]+n[0][1]))/2;t.scale(150*o).translate([a,u])}),n)}function QA(t,e,n){return ZA(t,[[0,0],e],n)}function KA(t,e,n){return JA(t,(function(n){var r=+e,i=r/(n[1][0]-n[0][0]),o=(r-i*(n[1][0]+n[0][0]))/2,a=-i*n[0][1];t.scale(150*i).translate([o,a])}),n)}function tM(t,e,n){return JA(t,(function(n){var r=+e,i=r/(n[1][1]-n[0][1]),o=-i*n[0][0],a=(r-i*(n[1][1]+n[0][1]))/2;t.scale(150*i).translate([o,a])}),n)}XA.prototype={constructor:XA,point:function(t,e){this.stream.point(t,e)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var eM=16,nM=Lb(30*zb);function rM(t,e){return+e?function(t,e){function n(r,i,o,a,u,s,l,c,f,h,d,p,g,m){var y=l-r,v=c-i,_=y*y+v*v;if(_>4*e&&g--){var x=a+h,b=u+d,w=s+p,k=Gb(x*x+b*b+w*w),A=Jb(w/=k),M=Ob(Ob(w)-1)<Cb||Ob(o-f)<Cb?(o+f)/2:Ub(b,x),E=t(M,A),D=E[0],C=E[1],F=D-r,S=C-i,$=v*F-y*S;($*$/_>e||Ob((y*F+v*S)/_-.5)>.3||a*h+u*d+s*p<nM)&&(n(r,i,o,a,u,s,D,C,M,x/=k,b/=k,w,g,m),m.point(D,C),n(D,C,M,x,b,w,l,c,f,h,d,p,g,m))}}return function(e){var r,i,o,a,u,s,l,c,f,h,d,p,g={point:m,lineStart:y,lineEnd:_,polygonStart:function(){e.polygonStart(),g.lineStart=x},polygonEnd:function(){e.polygonEnd(),g.lineStart=y}};function m(n,r){n=t(n,r),e.point(n[0],n[1])}function y(){c=NaN,g.point=v,e.lineStart()}function v(r,i){var o=Cw([r,i]),a=t(r,i);n(c,f,l,h,d,p,c=a[0],f=a[1],l=r,h=o[0],d=o[1],p=o[2],eM,e),e.point(c,f)}function _(){g.point=m,e.lineEnd()}function x(){y(),g.point=b,g.lineEnd=w}function b(t,e){v(r=t,e),i=c,o=f,a=h,u=d,s=p,g.point=v}function w(){n(c,f,l,h,d,p,i,o,r,a,u,s,eM,e),g.lineEnd=_,_()}return g}}(t,e):function(t){return VA({point:function(e,n){e=t(e,n),this.stream.point(e[0],e[1])}})}(t)}var iM=VA({point:function(t,e){this.stream.point(t*zb,e*zb)}});function oM(t,e,n,r,i,o){if(!o)return function(t,e,n,r,i){function o(o,a){return[e+t*(o*=r),n-t*(a*=i)]}return o.invert=function(o,a){return[(o-e)/t*r,(n-a)/t*i]},o}(t,e,n,r,i);var a=Lb(o),u=Hb(o),s=a*t,l=u*t,c=a/t,f=u/t,h=(u*n-a*e)/t,d=(u*e+a*n)/t;function p(t,o){return[s*(t*=r)-l*(o*=i)+e,n-l*t-s*o]}return p.invert=function(t,e){return[r*(c*t-f*e+h),i*(d-f*t-c*e)]},p}function aM(t){return uM((function(){return t}))()}function uM(t){var e,n,r,i,o,a,u,s,l,c,f=150,h=480,d=250,p=0,g=0,m=0,y=0,v=0,_=0,x=1,b=1,w=null,k=Nk,A=null,M=Hk,E=.5;function D(t){return s(t[0]*zb,t[1]*zb)}function C(t){return(t=s.invert(t[0],t[1]))&&[t[0]*Nb,t[1]*Nb]}function F(){var t=oM(f,0,0,x,b,_).apply(null,e(p,g)),r=oM(f,h-t[0],d-t[1],x,b,_);return n=xk(m,y,v),u=vk(e,r),s=vk(n,u),a=rM(u,E),S()}function S(){return l=c=null,D}return D.stream=function(t){return l&&c===t?l:l=iM(function(t){return VA({point:function(e,n){var r=t(e,n);return this.stream.point(r[0],r[1])}})}(n)(k(a(M(c=t)))))},D.preclip=function(t){return arguments.length?(k=t,w=void 0,S()):k},D.postclip=function(t){return arguments.length?(M=t,A=r=i=o=null,S()):M},D.clipAngle=function(t){return arguments.length?(k=+t?zk(w=t*zb):(w=null,Nk),S()):w*Nb},D.clipExtent=function(t){return arguments.length?(M=null==t?(A=r=i=o=null,Hk):Uk(A=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),S()):null==A?null:[[A,r],[i,o]]},D.scale=function(t){return arguments.length?(f=+t,F()):f},D.translate=function(t){return arguments.length?(h=+t[0],d=+t[1],F()):[h,d]},D.center=function(t){return arguments.length?(p=t[0]%360*zb,g=t[1]%360*zb,F()):[p*Nb,g*Nb]},D.rotate=function(t){return arguments.length?(m=t[0]%360*zb,y=t[1]%360*zb,v=t.length>2?t[2]%360*zb:0,F()):[m*Nb,y*Nb,v*Nb]},D.angle=function(t){return arguments.length?(_=t%360*zb,F()):_*Nb},D.reflectX=function(t){return arguments.length?(x=t?-1:1,F()):x<0},D.reflectY=function(t){return arguments.length?(b=t?-1:1,F()):b<0},D.precision=function(t){return arguments.length?(a=rM(u,E=t*t),S()):Gb(E)},D.fitExtent=function(t,e){return ZA(D,t,e)},D.fitSize=function(t,e){return QA(D,t,e)},D.fitWidth=function(t,e){return KA(D,t,e)},D.fitHeight=function(t,e){return tM(D,t,e)},function(){return e=t.apply(this,arguments),D.invert=e.invert&&C,F()}}function sM(t){var e=0,n=Sb/3,r=uM(t),i=r(e,n);return i.parallels=function(t){return arguments.length?r(e=t[0]*zb,n=t[1]*zb):[e*Nb,n*Nb]},i}function lM(t,e){var n=Hb(t),r=(n+Hb(e))/2;if(Ob(r)<Cb)return function(t){var e=Lb(t);function n(t,n){return[t*e,Hb(n)/e]}return n.invert=function(t,n){return[t/e,Jb(n*e)]},n}(t);var i=1+n*(2*r-n),o=Gb(i)/r;function a(t,e){var n=Gb(i-2*r*Hb(e))/r;return[n*Hb(t*=r),o-n*Lb(t)]}return a.invert=function(t,e){var n=o-e,a=Ub(t,Ob(n))*Yb(n);return n*r<0&&(a-=Sb*Yb(t)*Yb(n)),[a/r,Jb((i-(t*t+n*n)*r*r)/(2*r))]},a}function cM(){return sM(lM).scale(155.424).center([0,33.6442])}function fM(){return cM().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function hM(t){return function(e,n){var r=Lb(e),i=Lb(n),o=t(r*i);return o===1/0?[2,0]:[o*i*Hb(e),o*Hb(n)]}}function dM(t){return function(e,n){var r=Gb(e*e+n*n),i=t(r),o=Hb(i),a=Lb(i);return[Ub(e*o,r*a),Jb(r&&n*o/r)]}}var pM=hM((function(t){return Gb(2/(1+t))}));pM.invert=dM((function(t){return 2*Jb(t/2)}));var gM=hM((function(t){return(t=Xb(t))&&t/Hb(t)}));function mM(t,e){return[t,Ib(Vb(($b+e)/2))]}function yM(t){var e,n,r,i=aM(t),o=i.center,a=i.scale,u=i.translate,s=i.clipExtent,l=null;function c(){var o=Sb*a(),u=i(function(t){function e(e){return(e=t(e[0]*zb,e[1]*zb))[0]*=Nb,e[1]*=Nb,e}return t=xk(t[0]*zb,t[1]*zb,t.length>2?t[2]*zb:0),e.invert=function(e){return(e=t.invert(e[0]*zb,e[1]*zb))[0]*=Nb,e[1]*=Nb,e},e}(i.rotate()).invert([0,0]));return s(null==l?[[u[0]-o,u[1]-o],[u[0]+o,u[1]+o]]:t===mM?[[Math.max(u[0]-o,l),e],[Math.min(u[0]+o,n),r]]:[[l,Math.max(u[1]-o,e)],[n,Math.min(u[1]+o,r)]])}return i.scale=function(t){return arguments.length?(a(t),c()):a()},i.translate=function(t){return arguments.length?(u(t),c()):u()},i.center=function(t){return arguments.length?(o(t),c()):o()},i.clipExtent=function(t){return arguments.length?(null==t?l=e=n=r=null:(l=+t[0][0],e=+t[0][1],n=+t[1][0],r=+t[1][1]),c()):null==l?null:[[l,e],[n,r]]},c()}function vM(t){return Vb(($b+t)/2)}function _M(t,e){var n=Lb(t),r=t===e?Hb(t):Ib(n/Lb(e))/Ib(vM(e)/vM(t)),i=n*Wb(vM(t),r)/r;if(!r)return mM;function o(t,e){i>0?e<-$b+Cb&&(e=-$b+Cb):e>$b-Cb&&(e=$b-Cb);var n=i/Wb(vM(e),r);return[n*Hb(r*t),i-n*Lb(r*t)]}return o.invert=function(t,e){var n=i-e,o=Yb(r)*Gb(t*t+n*n),a=Ub(t,Ob(n))*Yb(n);return n*r<0&&(a-=Sb*Yb(t)*Yb(n)),[a/r,2*Rb(Wb(i/o,1/r))-$b]},o}function xM(t,e){return[t,e]}function bM(t,e){var n=Lb(t),r=t===e?Hb(t):(n-Lb(e))/(e-t),i=n/r+t;if(Ob(r)<Cb)return xM;function o(t,e){var n=i-e,o=r*t;return[n*Hb(o),i-n*Lb(o)]}return o.invert=function(t,e){var n=i-e,o=Ub(t,Ob(n))*Yb(n);return n*r<0&&(o-=Sb*Yb(t)*Yb(n)),[o/r,i-Yb(r)*Gb(t*t+n*n)]},o}gM.invert=dM((function(t){return t})),mM.invert=function(t,e){return[t,2*Rb(Pb(e))-$b]},xM.invert=xM;var wM=1.340264,kM=-.081106,AM=893e-6,MM=.003796,EM=Gb(3)/2;function DM(t,e){var n=Jb(EM*Hb(e)),r=n*n,i=r*r*r;return[t*Lb(n)/(EM*(wM+3*kM*r+i*(7*AM+9*MM*r))),n*(wM+kM*r+i*(AM+MM*r))]}function CM(t,e){var n=Lb(e),r=Lb(t)*n;return[n*Hb(t)/r,Hb(e)/r]}function FM(t,e){var n=e*e,r=n*n;return[t*(.8707-.131979*n+r*(r*(.003971*n-.001529*r)-.013791)),e*(1.007226+n*(.015085+r*(.028874*n-.044475-.005916*r)))]}function SM(t,e){return[Lb(e)*Hb(t),Hb(e)]}function $M(t,e){var n=Lb(e),r=1+Lb(t)*n;return[n*Hb(t)/r,Hb(e)/r]}function TM(t,e){return[Ib(Vb(($b+e)/2)),-t]}DM.invert=function(t,e){for(var n,r=e,i=r*r,o=i*i*i,a=0;a<12&&(o=(i=(r-=n=(r*(wM+kM*i+o*(AM+MM*i))-e)/(wM+3*kM*i+o*(7*AM+9*MM*i)))*r)*i*i,!(Ob(n)<Fb));++a);return[EM*t*(wM+3*kM*i+o*(7*AM+9*MM*i))/Lb(r),Jb(Hb(r)/EM)]},CM.invert=dM(Rb),FM.invert=function(t,e){var n,r=e,i=25;do{var o=r*r,a=o*o;r-=n=(r*(1.007226+o*(.015085+a*(.028874*o-.044475-.005916*a)))-e)/(1.007226+o*(.045255+a*(.259866*o-.311325-.005916*11*a)))}while(Ob(n)>Cb&&--i>0);return[t/(.8707+(o=r*r)*(o*(o*o*o*(.003971-.001529*o)-.013791)-.131979)),r]},SM.invert=dM(Jb),$M.invert=dM((function(t){return 2*Rb(t)})),TM.invert=function(t,e){return[-e,2*Rb(Pb(t))-$b]};var BM=Math.abs,NM=Math.cos,zM=Math.sin,OM=1e-6,RM=Math.PI,UM=RM/2,LM=function(t){return t>0?Math.sqrt(t):0}(2);function qM(t){return t>1?UM:t<-1?-UM:Math.asin(t)}function PM(t,e){var n,r=t*zM(e),i=30;do{e-=n=(e+zM(e)-r)/(1+NM(e))}while(BM(n)>OM&&--i>0);return e/2}var jM=function(t,e,n){function r(r,i){return[t*r*NM(i=PM(n,i)),e*zM(i)]}return r.invert=function(r,i){return i=qM(i/e),[r/(t*NM(i)),qM((2*i+zM(2*i))/n)]},r}(LM/UM,LM,RM);const IM=GA(),WM=["clipAngle","clipExtent","scale","translate","center","rotate","parallels","precision","reflectX","reflectY","coefficient","distance","fraction","lobes","parallel","radius","ratio","spacing","tilt"];function HM(t,e){if(!t||"string"!=typeof t)throw new Error("Projection type must be a name string.");return t=t.toLowerCase(),arguments.length>1?(GM[t]=function(t,e){return function n(){const r=e();return r.type=t,r.path=GA().projection(r),r.copy=r.copy||function(){const t=n();return WM.forEach((e=>{r[e]&&t[e](r[e]())})),t.path.pointRadius(r.path.pointRadius()),t},ep(r)}}(t,e),this):GM[t]||null}function YM(t){return t&&t.path||IM}const GM={albers:fM,albersusa:function(){var t,e,n,r,i,o,a=fM(),u=cM().rotate([154,0]).center([-2,58.5]).parallels([55,65]),s=cM().rotate([157,0]).center([-3,19.9]).parallels([8,18]),l={point:function(t,e){o=[t,e]}};function c(t){var e=t[0],a=t[1];return o=null,n.point(e,a),o||(r.point(e,a),o)||(i.point(e,a),o)}function f(){return t=e=null,c}return c.invert=function(t){var e=a.scale(),n=a.translate(),r=(t[0]-n[0])/e,i=(t[1]-n[1])/e;return(i>=.12&&i<.234&&r>=-.425&&r<-.214?u:i>=.166&&i<.234&&r>=-.214&&r<-.115?s:a).invert(t)},c.stream=function(n){return t&&e===n?t:(r=[a.stream(e=n),u.stream(n),s.stream(n)],i=r.length,t={point:function(t,e){for(var n=-1;++n<i;)r[n].point(t,e)},sphere:function(){for(var t=-1;++t<i;)r[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)r[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)r[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)r[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)r[t].polygonEnd()}});var r,i},c.precision=function(t){return arguments.length?(a.precision(t),u.precision(t),s.precision(t),f()):a.precision()},c.scale=function(t){return arguments.length?(a.scale(t),u.scale(.35*t),s.scale(t),c.translate(a.translate())):a.scale()},c.translate=function(t){if(!arguments.length)return a.translate();var e=a.scale(),o=+t[0],c=+t[1];return n=a.translate(t).clipExtent([[o-.455*e,c-.238*e],[o+.455*e,c+.238*e]]).stream(l),r=u.translate([o-.307*e,c+.201*e]).clipExtent([[o-.425*e+Cb,c+.12*e+Cb],[o-.214*e-Cb,c+.234*e-Cb]]).stream(l),i=s.translate([o-.205*e,c+.212*e]).clipExtent([[o-.214*e+Cb,c+.166*e+Cb],[o-.115*e-Cb,c+.234*e-Cb]]).stream(l),f()},c.fitExtent=function(t,e){return ZA(c,t,e)},c.fitSize=function(t,e){return QA(c,t,e)},c.fitWidth=function(t,e){return KA(c,t,e)},c.fitHeight=function(t,e){return tM(c,t,e)},c.scale(1070)},azimuthalequalarea:function(){return aM(pM).scale(124.75).clipAngle(179.999)},azimuthalequidistant:function(){return aM(gM).scale(79.4188).clipAngle(179.999)},conicconformal:function(){return sM(_M).scale(109.5).parallels([30,30])},conicequalarea:cM,conicequidistant:function(){return sM(bM).scale(131.154).center([0,13.9389])},equalEarth:function(){return aM(DM).scale(177.158)},equirectangular:function(){return aM(xM).scale(152.63)},gnomonic:function(){return aM(CM).scale(144.049).clipAngle(60)},identity:function(){var t,e,n,r,i,o,a,u=1,s=0,l=0,c=1,f=1,h=0,d=null,p=1,g=1,m=VA({point:function(t,e){var n=_([t,e]);this.stream.point(n[0],n[1])}}),y=Hk;function v(){return p=u*c,g=u*f,o=a=null,_}function _(n){var r=n[0]*p,i=n[1]*g;if(h){var o=i*t-r*e;r=r*t+i*e,i=o}return[r+s,i+l]}return _.invert=function(n){var r=n[0]-s,i=n[1]-l;if(h){var o=i*t+r*e;r=r*t-i*e,i=o}return[r/p,i/g]},_.stream=function(t){return o&&a===t?o:o=m(y(a=t))},_.postclip=function(t){return arguments.length?(y=t,d=n=r=i=null,v()):y},_.clipExtent=function(t){return arguments.length?(y=null==t?(d=n=r=i=null,Hk):Uk(d=+t[0][0],n=+t[0][1],r=+t[1][0],i=+t[1][1]),v()):null==d?null:[[d,n],[r,i]]},_.scale=function(t){return arguments.length?(u=+t,v()):u},_.translate=function(t){return arguments.length?(s=+t[0],l=+t[1],v()):[s,l]},_.angle=function(n){return arguments.length?(e=Hb(h=n%360*zb),t=Lb(h),v()):h*Nb},_.reflectX=function(t){return arguments.length?(c=t?-1:1,v()):c<0},_.reflectY=function(t){return arguments.length?(f=t?-1:1,v()):f<0},_.fitExtent=function(t,e){return ZA(_,t,e)},_.fitSize=function(t,e){return QA(_,t,e)},_.fitWidth=function(t,e){return KA(_,t,e)},_.fitHeight=function(t,e){return tM(_,t,e)},_},mercator:function(){return yM(mM).scale(961/Bb)},mollweide:function(){return aM(jM).scale(169.529)},naturalEarth1:function(){return aM(FM).scale(175.295)},orthographic:function(){return aM(SM).scale(249.5).clipAngle(90+Cb)},stereographic:function(){return aM($M).scale(250).clipAngle(142)},transversemercator:function(){var t=yM(TM),e=t.center,n=t.rotate;return t.center=function(t){return arguments.length?e([-t[1],t[0]]):[(t=e())[1],-t[0]]},t.rotate=function(t){return arguments.length?n([t[0],t[1],t.length>2?t[2]+90:90]):[(t=n())[0],t[1],t[2]-90]},n([0,0,90]).scale(159.155)}};for(const t in GM)HM(t,GM[t]);function VM(){}const XM=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function JM(){var t=1,e=1,n=a;function r(t,e){return e.map((e=>i(t,e)))}function i(r,i){var a=[],u=[];return function(n,r,i){var a,u,s,l,c,f,h=new Array,d=new Array;a=u=-1,l=n[0]>=r,XM[l<<1].forEach(p);for(;++a<t-1;)s=l,l=n[a+1]>=r,XM[s|l<<1].forEach(p);XM[l<<0].forEach(p);for(;++u<e-1;){for(a=-1,l=n[u*t+t]>=r,c=n[u*t]>=r,XM[l<<1|c<<2].forEach(p);++a<t-1;)s=l,l=n[u*t+t+a+1]>=r,f=c,c=n[u*t+a+1]>=r,XM[s|l<<1|c<<2|f<<3].forEach(p);XM[l|c<<3].forEach(p)}a=-1,c=n[u*t]>=r,XM[c<<2].forEach(p);for(;++a<t-1;)f=c,c=n[u*t+a+1]>=r,XM[c<<2|f<<3].forEach(p);function p(t){var e,n,r=[t[0][0]+a,t[0][1]+u],s=[t[1][0]+a,t[1][1]+u],l=o(r),c=o(s);(e=d[l])?(n=h[c])?(delete d[e.end],delete h[n.start],e===n?(e.ring.push(s),i(e.ring)):h[e.start]=d[n.end]={start:e.start,end:n.end,ring:e.ring.concat(n.ring)}):(delete d[e.end],e.ring.push(s),d[e.end=c]=e):(e=h[c])?(n=d[l])?(delete h[e.start],delete d[n.end],e===n?(e.ring.push(s),i(e.ring)):h[n.start]=d[e.end]={start:n.start,end:e.end,ring:n.ring.concat(e.ring)}):(delete h[e.start],e.ring.unshift(r),h[e.start=l]=e):h[l]=d[c]={start:l,end:c,ring:[r,s]}}XM[c<<3].forEach(p)}(r,i,(t=>{n(t,r,i),function(t){var e=0,n=t.length,r=t[n-1][1]*t[0][0]-t[n-1][0]*t[0][1];for(;++e<n;)r+=t[e-1][1]*t[e][0]-t[e-1][0]*t[e][1];return r}(t)>0?a.push([t]):u.push(t)})),u.forEach((t=>{for(var e,n=0,r=a.length;n<r;++n)if(-1!==ZM((e=a[n])[0],t))return void e.push(t)})),{type:"MultiPolygon",value:i,coordinates:a}}function o(e){return 2*e[0]+e[1]*(t+1)*4}function a(n,r,i){n.forEach((n=>{var o,a=n[0],u=n[1],s=0|a,l=0|u,c=r[l*t+s];a>0&&a<t&&s===a&&(o=r[l*t+s-1],n[0]=a+(i-o)/(c-o)-.5),u>0&&u<e&&l===u&&(o=r[(l-1)*t+s],n[1]=u+(i-o)/(c-o)-.5)}))}return r.contour=i,r.size=function(n){if(!arguments.length)return[t,e];var i=Math.floor(n[0]),o=Math.floor(n[1]);return i>=0&&o>=0||u("invalid size"),t=i,e=o,r},r.smooth=function(t){return arguments.length?(n=t?a:VM,r):n===a},r}function ZM(t,e){for(var n,r=-1,i=e.length;++r<i;)if(n=QM(t,e[r]))return n;return 0}function QM(t,e){for(var n=e[0],r=e[1],i=-1,o=0,a=t.length,u=a-1;o<a;u=o++){var s=t[o],l=s[0],c=s[1],f=t[u],h=f[0],d=f[1];if(KM(s,f,e))return 0;c>r!=d>r&&n<(h-l)*(r-c)/(d-c)+l&&(i=-i)}return i}function KM(t,e,n){var r,i,o,a;return function(t,e,n){return(e[0]-t[0])*(n[1]-t[1])==(n[0]-t[0])*(e[1]-t[1])}(t,e,n)&&(i=t[r=+(t[0]===e[0])],o=n[r],a=e[r],i<=o&&o<=a||a<=o&&o<=i)}function tE(t,e,n){return function(r){var i=at(r),o=n?Math.min(i[0],0):i[0],a=i[1],u=a-o,s=e?ke(o,a,t):u/(t+1);return Te(o+s,a,s)}}function eE(t){Qa.call(this,null,t)}function nE(t,e,n,r,i){const o=t.x1||0,a=t.y1||0,u=e*n<0;function s(t){t.forEach(l)}function l(t){u&&t.reverse(),t.forEach(c)}function c(t){t[0]=(t[0]-o)*e+r,t[1]=(t[1]-a)*n+i}return function(t){return t.coordinates.forEach(s),t}}function rE(t,e,n){const r=t>=0?t:ou(e,n);return Math.round((Math.sqrt(4*r*r+1)-1)/2)}function iE(t){return J(t)?t:rt(+t)}function oE(){var t=t=>t[0],e=t=>t[1],n=d,r=[-1,-1],i=960,o=500,a=2;function s(u,s){const l=rE(r[0],u,t)>>a,c=rE(r[1],u,e)>>a,f=l?l+2:0,h=c?c+2:0,d=2*f+(i>>a),p=2*h+(o>>a),g=new Float32Array(d*p),m=new Float32Array(d*p);let y=g;u.forEach((r=>{const i=f+(+t(r)>>a),o=h+(+e(r)>>a);i>=0&&i<d&&o>=0&&o<p&&(g[i+o*d]+=+n(r))})),l>0&&c>0?(aE(d,p,g,m,l),uE(d,p,m,g,c),aE(d,p,g,m,l),uE(d,p,m,g,c),aE(d,p,g,m,l),uE(d,p,m,g,c)):l>0?(aE(d,p,g,m,l),aE(d,p,m,g,l),aE(d,p,g,m,l),y=m):c>0&&(uE(d,p,g,m,c),uE(d,p,m,g,c),uE(d,p,g,m,c),y=m);const v=s?Math.pow(2,-2*a):1/Be(y);for(let t=0,e=d*p;t<e;++t)y[t]*=v;return{values:y,scale:1<<a,width:d,height:p,x1:f,y1:h,x2:f+(i>>a),y2:h+(o>>a)}}return s.x=function(e){return arguments.length?(t=iE(e),s):t},s.y=function(t){return arguments.length?(e=iE(t),s):e},s.weight=function(t){return arguments.length?(n=iE(t),s):n},s.size=function(t){if(!arguments.length)return[i,o];var e=+t[0],n=+t[1];return e>=0&&n>=0||u("invalid size"),i=e,o=n,s},s.cellSize=function(t){return arguments.length?((t=+t)>=1||u("invalid cell size"),a=Math.floor(Math.log(t)/Math.LN2),s):1<<a},s.bandwidth=function(t){return arguments.length?(1===(t=V(t)).length&&(t=[+t[0],+t[0]]),2!==t.length&&u("invalid bandwidth"),r=t,s):r},s}function aE(t,e,n,r,i){const o=1+(i<<1);for(let a=0;a<e;++a)for(let e=0,u=0;e<t+i;++e)e<t&&(u+=n[e+a*t]),e>=i&&(e>=o&&(u-=n[e-o+a*t]),r[e-i+a*t]=u/Math.min(e+1,t-1+o-e,o))}function uE(t,e,n,r,i){const o=1+(i<<1);for(let a=0;a<t;++a)for(let u=0,s=0;u<e+i;++u)u<e&&(s+=n[a+u*t]),u>=i&&(u>=o&&(s-=n[a+(u-o)*t]),r[a+(u-i)*t]=s/Math.min(u+1,e-1+o-u,o))}function sE(t){Qa.call(this,null,t)}eE.Definition={type:"Isocontour",metadata:{generates:!0},params:[{name:"field",type:"field"},{name:"thresholds",type:"number",array:!0},{name:"levels",type:"number"},{name:"nice",type:"boolean",default:!1},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"zero",type:"boolean",default:!0},{name:"smooth",type:"boolean",default:!0},{name:"scale",type:"number",expr:!0},{name:"translate",type:"number",array:!0,expr:!0},{name:"as",type:"string",null:!0,default:"contour"}]},dt(eE,Qa,{transform(t,e){if(this.value&&!e.changed()&&!t.modified())return e.StopPropagation;var n=e.fork(e.NO_SOURCE|e.NO_FIELDS),r=e.materialize(e.SOURCE).source,i=t.field||f,o=JM().smooth(!1!==t.smooth),a=t.thresholds||function(t,e,n){const r=tE(n.levels||10,n.nice,!1!==n.zero);return"shared"!==n.resolve?r:r(t.map((t=>Ae(e(t).values))))}(r,i,t),u=null===t.as?null:t.as||"contour",s=[];return r.forEach((e=>{const n=i(e),r=o.size([n.width,n.height])(n.values,k(a)?a:a(n.values));!function(t,e,n,r){let i=r.scale||e.scale,o=r.translate||e.translate;J(i)&&(i=i(n,r));J(o)&&(o=o(n,r));if((1===i||null==i)&&!o)return;const a=(vt(i)?i:i[0])||1,u=(vt(i)?i:i[1])||1,s=o&&o[0]||0,l=o&&o[1]||0;t.forEach(nE(e,a,u,s,l))}(r,n,e,t),r.forEach((t=>{s.push(ka(e,ba(null!=u?{[u]:t}:t)))}))})),this.value&&(n.rem=this.value),this.value=n.source=n.add=s,n}}),sE.Definition={type:"KDE2D",metadata:{generates:!0},params:[{name:"size",type:"number",array:!0,length:2,required:!0},{name:"x",type:"field",required:!0},{name:"y",type:"field",required:!0},{name:"weight",type:"field"},{name:"groupby",type:"field",array:!0},{name:"cellSize",type:"number"},{name:"bandwidth",type:"number",array:!0,length:2},{name:"counts",type:"boolean",default:!1},{name:"as",type:"string",default:"grid"}]};const lE=["x","y","weight","size","cellSize","bandwidth"];function cE(t,e){return lE.forEach((n=>null!=e[n]?t[n](e[n]):0)),t}function fE(t){Qa.call(this,null,t)}dt(sE,Qa,{transform(t,e){if(this.value&&!e.changed()&&!t.modified())return e.StopPropagation;var r,i=e.fork(e.NO_SOURCE|e.NO_FIELDS),o=function(t,e){var n,r,i,o,a,u,s=[],l=t=>t(o);if(null==e)s.push(t);else for(n={},r=0,i=t.length;r<i;++r)o=t[r],(u=n[a=e.map(l)])||(n[a]=u=[],u.dims=a,s.push(u)),u.push(o);return s}(e.materialize(e.SOURCE).source,t.groupby),a=(t.groupby||[]).map(n),u=cE(oE(),t),s=t.as||"grid";return r=o.map((e=>ba(function(t,e){for(let n=0;n<a.length;++n)t[a[n]]=e[n];return t}({[s]:u(e,t.counts)},e.dims)))),this.value&&(i.rem=this.value),this.value=i.source=i.add=r,i}}),fE.Definition={type:"Contour",metadata:{generates:!0},params:[{name:"size",type:"number",array:!0,length:2,required:!0},{name:"values",type:"number",array:!0},{name:"x",type:"field"},{name:"y",type:"field"},{name:"weight",type:"field"},{name:"cellSize",type:"number"},{name:"bandwidth",type:"number"},{name:"count",type:"number"},{name:"nice",type:"boolean",default:!1},{name:"thresholds",type:"number",array:!0},{name:"smooth",type:"boolean",default:!0}]},dt(fE,Qa,{transform(t,e){if(this.value&&!e.changed()&&!t.modified())return e.StopPropagation;var n,r,i=e.fork(e.NO_SOURCE|e.NO_FIELDS),o=JM().smooth(!1!==t.smooth),a=t.values,u=t.thresholds||tE(t.count||10,t.nice,!!a),s=t.size;return a||(a=e.materialize(e.SOURCE).source,r=nE(n=cE(oE(),t)(a,!0),n.scale||1,n.scale||1,0,0),s=[n.width,n.height],a=n.values),u=k(u)?u:u(a),a=o.size(s)(a,u),r&&a.forEach(r),this.value&&(i.rem=this.value),this.value=i.source=i.add=(a||[]).map(ba),i}});const hE="Feature",dE="FeatureCollection";function pE(t){Qa.call(this,null,t)}function gE(t){Qa.call(this,null,t)}function mE(t){Qa.call(this,null,t)}function yE(t){Qa.call(this,null,t)}function vE(t){Qa.call(this,[],t),this.generator=function(){var t,e,n,r,i,o,a,u,s,l,c,f,h=10,d=h,p=90,g=360,m=2.5;function y(){return{type:"MultiLineString",coordinates:v()}}function v(){return Te(qb(r/p)*p,n,p).map(c).concat(Te(qb(u/g)*g,a,g).map(f)).concat(Te(qb(e/h)*h,t,h).filter((function(t){return Ob(t%p)>Cb})).map(s)).concat(Te(qb(o/d)*d,i,d).filter((function(t){return Ob(t%g)>Cb})).map(l))}return y.lines=function(){return v().map((function(t){return{type:"LineString",coordinates:t}}))},y.outline=function(){return{type:"Polygon",coordinates:[c(r).concat(f(a).slice(1),c(n).reverse().slice(1),f(u).reverse().slice(1))]}},y.extent=function(t){return arguments.length?y.extentMajor(t).extentMinor(t):y.extentMinor()},y.extentMajor=function(t){return arguments.length?(r=+t[0][0],n=+t[1][0],u=+t[0][1],a=+t[1][1],r>n&&(t=r,r=n,n=t),u>a&&(t=u,u=a,a=t),y.precision(m)):[[r,u],[n,a]]},y.extentMinor=function(n){return arguments.length?(e=+n[0][0],t=+n[1][0],o=+n[0][1],i=+n[1][1],e>t&&(n=e,e=t,t=n),o>i&&(n=o,o=i,i=n),y.precision(m)):[[e,o],[t,i]]},y.step=function(t){return arguments.length?y.stepMajor(t).stepMinor(t):y.stepMinor()},y.stepMajor=function(t){return arguments.length?(p=+t[0],g=+t[1],y):[p,g]},y.stepMinor=function(t){return arguments.length?(h=+t[0],d=+t[1],y):[h,d]},y.precision=function(h){return arguments.length?(m=+h,s=Lk(o,i,90),l=qk(e,t,m),c=Lk(u,a,90),f=qk(r,n,m),y):m},y.extentMajor([[-180,-90+Cb],[180,90-Cb]]).extentMinor([[-180,-80-Cb],[180,80+Cb]])}()}function _E(t){Qa.call(this,null,t)}function xE(t){if(!J(t))return!1;const e=Nt(r(t));return e.$x||e.$y||e.$value||e.$max}function bE(t){Qa.call(this,null,t),this.modified(!0)}function wE(t,e,n){J(t[e])&&t[e](n)}pE.Definition={type:"GeoJSON",metadata:{},params:[{name:"fields",type:"field",array:!0,length:2},{name:"geojson",type:"field"}]},dt(pE,Qa,{transform(t,e){var n,i=this._features,o=this._points,a=t.fields,u=a&&a[0],s=a&&a[1],l=t.geojson||!a&&f,c=e.ADD;n=t.modified()||e.changed(e.REM)||e.modified(r(l))||u&&e.modified(r(u))||s&&e.modified(r(s)),this.value&&!n||(c=e.SOURCE,this._features=i=[],this._points=o=[]),l&&e.visit(c,(t=>i.push(l(t)))),u&&s&&(e.visit(c,(t=>{var e=u(t),n=s(t);null!=e&&null!=n&&(e=+e)===e&&(n=+n)===n&&o.push([e,n])})),i=i.concat({type:hE,geometry:{type:"MultiPoint",coordinates:o}})),this.value={type:dE,features:i}}}),gE.Definition={type:"GeoPath",metadata:{modifies:!0},params:[{name:"projection",type:"projection"},{name:"field",type:"field"},{name:"pointRadius",type:"number",expr:!0},{name:"as",type:"string",default:"path"}]},dt(gE,Qa,{transform(t,e){var n=e.fork(e.ALL),r=this.value,i=t.field||f,o=t.as||"path",a=n.SOURCE;!r||t.modified()?(this.value=r=YM(t.projection),n.materialize().reflow()):a=i===f||e.modified(i.fields)?n.ADD_MOD:n.ADD;const u=function(t,e){const n=t.pointRadius();t.context(null),null!=e&&t.pointRadius(e);return n}(r,t.pointRadius);return n.visit(a,(t=>t[o]=r(i(t)))),r.pointRadius(u),n.modifies(o)}}),mE.Definition={type:"GeoPoint",metadata:{modifies:!0},params:[{name:"projection",type:"projection",required:!0},{name:"fields",type:"field",array:!0,required:!0,length:2},{name:"as",type:"string",array:!0,length:2,default:["x","y"]}]},dt(mE,Qa,{transform(t,e){var n,r=t.projection,i=t.fields[0],o=t.fields[1],a=t.as||["x","y"],u=a[0],s=a[1];function l(t){const e=r([i(t),o(t)]);e?(t[u]=e[0],t[s]=e[1]):(t[u]=void 0,t[s]=void 0)}return t.modified()?e=e.materialize().reflow(!0).visit(e.SOURCE,l):(n=e.modified(i.fields)||e.modified(o.fields),e.visit(n?e.ADD_MOD:e.ADD,l)),e.modifies(a)}}),yE.Definition={type:"GeoShape",metadata:{modifies:!0,nomod:!0},params:[{name:"projection",type:"projection"},{name:"field",type:"field",default:"datum"},{name:"pointRadius",type:"number",expr:!0},{name:"as",type:"string",default:"shape"}]},dt(yE,Qa,{transform(t,e){var n=e.fork(e.ALL),r=this.value,i=t.as||"shape",o=n.ADD;return r&&!t.modified()||(this.value=r=function(t,e,n){const r=null==n?n=>t(e(n)):r=>{var i=t.pointRadius(),o=t.pointRadius(n)(e(r));return t.pointRadius(i),o};return r.context=e=>(t.context(e),r),r}(YM(t.projection),t.field||l("datum"),t.pointRadius),n.materialize().reflow(),o=n.SOURCE),n.visit(o,(t=>t[i]=r)),n.modifies(i)}}),vE.Definition={type:"Graticule",metadata:{changes:!0,generates:!0},params:[{name:"extent",type:"array",array:!0,length:2,content:{type:"number",array:!0,length:2}},{name:"extentMajor",type:"array",array:!0,length:2,content:{type:"number",array:!0,length:2}},{name:"extentMinor",type:"array",array:!0,length:2,content:{type:"number",array:!0,length:2}},{name:"step",type:"number",array:!0,length:2},{name:"stepMajor",type:"number",array:!0,length:2,default:[90,360]},{name:"stepMinor",type:"number",array:!0,length:2,default:[10,10]},{name:"precision",type:"number",default:2.5}]},dt(vE,Qa,{transform(t,e){var n,r=this.value,i=this.generator;if(!r.length||t.modified())for(const e in t)J(i[e])&&i[e](t[e]);return n=i(),r.length?e.mod.push(Aa(r[0],n)):e.add.push(ba(n)),r[0]=n,e}}),_E.Definition={type:"heatmap",metadata:{modifies:!0},params:[{name:"field",type:"field"},{name:"color",type:"string",expr:!0},{name:"opacity",type:"number",expr:!0},{name:"resolve",type:"enum",values:["shared","independent"],default:"independent"},{name:"as",type:"string",default:"image"}]},dt(_E,Qa,{transform(t,e){if(!e.changed()&&!t.modified())return e.StopPropagation;var n=e.materialize(e.SOURCE).source,r="shared"===t.resolve,i=t.field||f,o=function(t,e){let n;J(t)?(n=n=>t(n,e),n.dep=xE(t)):t?n=rt(t):(n=t=>t.$value/t.$max||0,n.dep=!0);return n}(t.opacity,t),a=function(t,e){let n;J(t)?(n=n=>hf(t(n,e)),n.dep=xE(t)):n=rt(hf(t||"#888"));return n}(t.color,t),u=t.as||"image",s={$x:0,$y:0,$value:0,$max:r?Ae(n.map((t=>Ae(i(t).values)))):0};return n.forEach((t=>{const e=i(t),n=ot({},t,s);r||(n.$max=Ae(e.values||[])),t[u]=function(t,e,n,r){const i=t.width,o=t.height,a=t.x1||0,u=t.y1||0,s=t.x2||i,l=t.y2||o,c=t.values,f=c?t=>c[t]:h,d=Rc(s-a,l-u),p=d.getContext("2d"),g=p.getImageData(0,0,s-a,l-u),m=g.data;for(let t=u,o=0;t<l;++t){e.$y=t-u;for(let u=a,l=t*i;u<s;++u,o+=4){e.$x=u-a,e.$value=f(u+l);const t=n(e);m[o+0]=t.r,m[o+1]=t.g,m[o+2]=t.b,m[o+3]=~~(255*r(e))}}return p.putImageData(g,0,0),d}(e,n,a.dep?a:rt(a(n)),o.dep?o:rt(o(n)))})),e.reflow(!0).modifies(u)}}),dt(bE,Qa,{transform(t,e){let n=this.value;return!n||t.modified("type")?(this.value=n=function(t){const e=HM((t||"mercator").toLowerCase());e||u("Unrecognized projection type: "+t);return e()}(t.type),WM.forEach((e=>{null!=t[e]&&wE(n,e,t[e])}))):WM.forEach((e=>{t.modified(e)&&wE(n,e,t[e])})),null!=t.pointRadius&&n.path.pointRadius(t.pointRadius),t.fit&&function(t,e){const n=function(t){return t=V(t),1===t.length?t[0]:{type:dE,features:t.reduce(((t,e)=>t.concat(function(t){return t.type===dE?t.features:V(t).filter((t=>null!=t)).map((t=>t.type===hE?t:{type:hE,geometry:t}))}(e))),[])}}(e.fit);e.extent?t.fitExtent(e.extent,n):e.size&&t.fitSize(e.size,n)}(n,t),e.fork(e.NO_SOURCE|e.NO_FIELDS)}});var kE=Object.freeze({__proto__:null,contour:fE,geojson:pE,geopath:gE,geopoint:mE,geoshape:yE,graticule:vE,heatmap:_E,isocontour:eE,kde2d:sE,projection:bE});function AE(t,e,n,r){if(isNaN(e)||isNaN(n))return t;var i,o,a,u,s,l,c,f,h,d=t._root,p={data:r},g=t._x0,m=t._y0,y=t._x1,v=t._y1;if(!d)return t._root=p,t;for(;d.length;)if((l=e>=(o=(g+y)/2))?g=o:y=o,(c=n>=(a=(m+v)/2))?m=a:v=a,i=d,!(d=d[f=c<<1|l]))return i[f]=p,t;if(u=+t._x.call(null,d.data),s=+t._y.call(null,d.data),e===u&&n===s)return p.next=d,i?i[f]=p:t._root=p,t;do{i=i?i[f]=new Array(4):t._root=new Array(4),(l=e>=(o=(g+y)/2))?g=o:y=o,(c=n>=(a=(m+v)/2))?m=a:v=a}while((f=c<<1|l)==(h=(s>=a)<<1|u>=o));return i[h]=d,i[f]=p,t}function ME(t,e,n,r,i){this.node=t,this.x0=e,this.y0=n,this.x1=r,this.y1=i}function EE(t){return t[0]}function DE(t){return t[1]}function CE(t,e,n){var r=new FE(null==e?EE:e,null==n?DE:n,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function FE(t,e,n,r,i,o){this._x=t,this._y=e,this._x0=n,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function SE(t){for(var e={data:t.data},n=e;t=t.next;)n=n.next={data:t.data};return e}var $E=CE.prototype=FE.prototype;function TE(t){return function(){return t}}function BE(t){return 1e-6*(t()-.5)}function NE(t){return t.x+t.vx}function zE(t){return t.y+t.vy}function OE(t){return t.index}function RE(t,e){var n=t.get(e);if(!n)throw new Error("node not found: "+e);return n}$E.copy=function(){var t,e,n=new FE(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return n;if(!r.length)return n._root=SE(r),n;for(t=[{source:r,target:n._root=new Array(4)}];r=t.pop();)for(var i=0;i<4;++i)(e=r.source[i])&&(e.length?t.push({source:e,target:r.target[i]=new Array(4)}):r.target[i]=SE(e));return n},$E.add=function(t){const e=+this._x.call(null,t),n=+this._y.call(null,t);return AE(this.cover(e,n),e,n,t)},$E.addAll=function(t){var e,n,r,i,o=t.length,a=new Array(o),u=new Array(o),s=1/0,l=1/0,c=-1/0,f=-1/0;for(n=0;n<o;++n)isNaN(r=+this._x.call(null,e=t[n]))||isNaN(i=+this._y.call(null,e))||(a[n]=r,u[n]=i,r<s&&(s=r),r>c&&(c=r),i<l&&(l=i),i>f&&(f=i));if(s>c||l>f)return this;for(this.cover(s,l).cover(c,f),n=0;n<o;++n)AE(this,a[n],u[n],t[n]);return this},$E.cover=function(t,e){if(isNaN(t=+t)||isNaN(e=+e))return this;var n=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(n))i=(n=Math.floor(t))+1,o=(r=Math.floor(e))+1;else{for(var a,u,s=i-n||1,l=this._root;n>t||t>=i||r>e||e>=o;)switch(u=(e<r)<<1|t<n,(a=new Array(4))[u]=l,l=a,s*=2,u){case 0:i=n+s,o=r+s;break;case 1:n=i-s,o=r+s;break;case 2:i=n+s,r=o-s;break;case 3:n=i-s,r=o-s}this._root&&this._root.length&&(this._root=l)}return this._x0=n,this._y0=r,this._x1=i,this._y1=o,this},$E.data=function(){var t=[];return this.visit((function(e){if(!e.length)do{t.push(e.data)}while(e=e.next)})),t},$E.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},$E.find=function(t,e,n){var r,i,o,a,u,s,l,c=this._x0,f=this._y0,h=this._x1,d=this._y1,p=[],g=this._root;for(g&&p.push(new ME(g,c,f,h,d)),null==n?n=1/0:(c=t-n,f=e-n,h=t+n,d=e+n,n*=n);s=p.pop();)if(!(!(g=s.node)||(i=s.x0)>h||(o=s.y0)>d||(a=s.x1)<c||(u=s.y1)<f))if(g.length){var m=(i+a)/2,y=(o+u)/2;p.push(new ME(g[3],m,y,a,u),new ME(g[2],i,y,m,u),new ME(g[1],m,o,a,y),new ME(g[0],i,o,m,y)),(l=(e>=y)<<1|t>=m)&&(s=p[p.length-1],p[p.length-1]=p[p.length-1-l],p[p.length-1-l]=s)}else{var v=t-+this._x.call(null,g.data),_=e-+this._y.call(null,g.data),x=v*v+_*_;if(x<n){var b=Math.sqrt(n=x);c=t-b,f=e-b,h=t+b,d=e+b,r=g.data}}return r},$E.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(a=+this._y.call(null,t)))return this;var e,n,r,i,o,a,u,s,l,c,f,h,d=this._root,p=this._x0,g=this._y0,m=this._x1,y=this._y1;if(!d)return this;if(d.length)for(;;){if((l=o>=(u=(p+m)/2))?p=u:m=u,(c=a>=(s=(g+y)/2))?g=s:y=s,e=d,!(d=d[f=c<<1|l]))return this;if(!d.length)break;(e[f+1&3]||e[f+2&3]||e[f+3&3])&&(n=e,h=f)}for(;d.data!==t;)if(r=d,!(d=d.next))return this;return(i=d.next)&&delete d.next,r?(i?r.next=i:delete r.next,this):e?(i?e[f]=i:delete e[f],(d=e[0]||e[1]||e[2]||e[3])&&d===(e[3]||e[2]||e[1]||e[0])&&!d.length&&(n?n[h]=d:this._root=d),this):(this._root=i,this)},$E.removeAll=function(t){for(var e=0,n=t.length;e<n;++e)this.remove(t[e]);return this},$E.root=function(){return this._root},$E.size=function(){var t=0;return this.visit((function(e){if(!e.length)do{++t}while(e=e.next)})),t},$E.visit=function(t){var e,n,r,i,o,a,u=[],s=this._root;for(s&&u.push(new ME(s,this._x0,this._y0,this._x1,this._y1));e=u.pop();)if(!t(s=e.node,r=e.x0,i=e.y0,o=e.x1,a=e.y1)&&s.length){var l=(r+o)/2,c=(i+a)/2;(n=s[3])&&u.push(new ME(n,l,c,o,a)),(n=s[2])&&u.push(new ME(n,r,c,l,a)),(n=s[1])&&u.push(new ME(n,l,i,o,c)),(n=s[0])&&u.push(new ME(n,r,i,l,c))}return this},$E.visitAfter=function(t){var e,n=[],r=[];for(this._root&&n.push(new ME(this._root,this._x0,this._y0,this._x1,this._y1));e=n.pop();){var i=e.node;if(i.length){var o,a=e.x0,u=e.y0,s=e.x1,l=e.y1,c=(a+s)/2,f=(u+l)/2;(o=i[0])&&n.push(new ME(o,a,u,c,f)),(o=i[1])&&n.push(new ME(o,c,u,s,f)),(o=i[2])&&n.push(new ME(o,a,f,c,l)),(o=i[3])&&n.push(new ME(o,c,f,s,l))}r.push(e)}for(;e=r.pop();)t(e.node,e.x0,e.y0,e.x1,e.y1);return this},$E.x=function(t){return arguments.length?(this._x=t,this):this._x},$E.y=function(t){return arguments.length?(this._y=t,this):this._y};var UE={value:()=>{}};function LE(){for(var t,e=0,n=arguments.length,r={};e<n;++e){if(!(t=arguments[e]+"")||t in r||/[\s.]/.test(t))throw new Error("illegal type: "+t);r[t]=[]}return new qE(r)}function qE(t){this._=t}function PE(t,e){for(var n,r=0,i=t.length;r<i;++r)if((n=t[r]).name===e)return n.value}function jE(t,e,n){for(var r=0,i=t.length;r<i;++r)if(t[r].name===e){t[r]=UE,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=n&&t.push({name:e,value:n}),t}qE.prototype=LE.prototype={constructor:qE,on:function(t,e){var n,r,i=this._,o=(r=i,(t+"").trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))),a=-1,u=o.length;if(!(arguments.length<2)){if(null!=e&&"function"!=typeof e)throw new Error("invalid callback: "+e);for(;++a<u;)if(n=(t=o[a]).type)i[n]=jE(i[n],t.name,e);else if(null==e)for(n in i)i[n]=jE(i[n],t.name,null);return this}for(;++a<u;)if((n=(t=o[a]).type)&&(n=PE(i[n],t.name)))return n},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new qE(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,r,i=new Array(n),o=0;o<n;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,n=(r=this._[t]).length;o<n;++o)r[o].value.apply(e,i)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(e,n)}};var IE,WE,HE=0,YE=0,GE=0,VE=1e3,XE=0,JE=0,ZE=0,QE="object"==typeof performance&&performance.now?performance:Date,KE="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function tD(){return JE||(KE(eD),JE=QE.now()+ZE)}function eD(){JE=0}function nD(){this._call=this._time=this._next=null}function rD(t,e,n){var r=new nD;return r.restart(t,e,n),r}function iD(){JE=(XE=QE.now())+ZE,HE=YE=0;try{!function(){tD(),++HE;for(var t,e=IE;e;)(t=JE-e._time)>=0&&e._call.call(void 0,t),e=e._next;--HE}()}finally{HE=0,function(){var t,e,n=IE,r=1/0;for(;n;)n._call?(r>n._time&&(r=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:IE=e);WE=t,aD(r)}(),JE=0}}function oD(){var t=QE.now(),e=t-XE;e>VE&&(ZE-=e,XE=t)}function aD(t){HE||(YE&&(YE=clearTimeout(YE)),t-JE>24?(t<1/0&&(YE=setTimeout(iD,t-QE.now()-ZE)),GE&&(GE=clearInterval(GE))):(GE||(XE=QE.now(),GE=setInterval(oD,VE)),HE=1,KE(iD)))}nD.prototype=rD.prototype={constructor:nD,restart:function(t,e,n){if("function"!=typeof t)throw new TypeError("callback is not a function");n=(null==n?tD():+n)+(null==e?0:+e),this._next||WE===this||(WE?WE._next=this:IE=this,WE=this),this._call=t,this._time=n,aD()},stop:function(){this._call&&(this._call=null,this._time=1/0,aD())}};const uD=1664525,sD=1013904223,lD=4294967296;function cD(t){return t.x}function fD(t){return t.y}var hD=10,dD=Math.PI*(3-Math.sqrt(5));function pD(t){var e,n=1,r=.001,i=1-Math.pow(r,1/300),o=0,a=.6,u=new Map,s=rD(f),l=LE("tick","end"),c=function(){let t=1;return()=>(t=(uD*t+sD)%lD)/lD}();function f(){h(),l.call("tick",e),n<r&&(s.stop(),l.call("end",e))}function h(r){var s,l,c=t.length;void 0===r&&(r=1);for(var f=0;f<r;++f)for(n+=(o-n)*i,u.forEach((function(t){t(n)})),s=0;s<c;++s)null==(l=t[s]).fx?l.x+=l.vx*=a:(l.x=l.fx,l.vx=0),null==l.fy?l.y+=l.vy*=a:(l.y=l.fy,l.vy=0);return e}function d(){for(var e,n=0,r=t.length;n<r;++n){if((e=t[n]).index=n,null!=e.fx&&(e.x=e.fx),null!=e.fy&&(e.y=e.fy),isNaN(e.x)||isNaN(e.y)){var i=hD*Math.sqrt(.5+n),o=n*dD;e.x=i*Math.cos(o),e.y=i*Math.sin(o)}(isNaN(e.vx)||isNaN(e.vy))&&(e.vx=e.vy=0)}}function p(e){return e.initialize&&e.initialize(t,c),e}return null==t&&(t=[]),d(),e={tick:h,restart:function(){return s.restart(f),e},stop:function(){return s.stop(),e},nodes:function(n){return arguments.length?(t=n,d(),u.forEach(p),e):t},alpha:function(t){return arguments.length?(n=+t,e):n},alphaMin:function(t){return arguments.length?(r=+t,e):r},alphaDecay:function(t){return arguments.length?(i=+t,e):+i},alphaTarget:function(t){return arguments.length?(o=+t,e):o},velocityDecay:function(t){return arguments.length?(a=1-t,e):1-a},randomSource:function(t){return arguments.length?(c=t,u.forEach(p),e):c},force:function(t,n){return arguments.length>1?(null==n?u.delete(t):u.set(t,p(n)),e):u.get(t)},find:function(e,n,r){var i,o,a,u,s,l=0,c=t.length;for(null==r?r=1/0:r*=r,l=0;l<c;++l)(a=(i=e-(u=t[l]).x)*i+(o=n-u.y)*o)<r&&(s=u,r=a);return s},on:function(t,n){return arguments.length>1?(l.on(t,n),e):l.on(t)}}}const gD={center:function(t,e){var n,r=1;function i(){var i,o,a=n.length,u=0,s=0;for(i=0;i<a;++i)u+=(o=n[i]).x,s+=o.y;for(u=(u/a-t)*r,s=(s/a-e)*r,i=0;i<a;++i)(o=n[i]).x-=u,o.y-=s}return null==t&&(t=0),null==e&&(e=0),i.initialize=function(t){n=t},i.x=function(e){return arguments.length?(t=+e,i):t},i.y=function(t){return arguments.length?(e=+t,i):e},i.strength=function(t){return arguments.length?(r=+t,i):r},i},collide:function(t){var e,n,r,i=1,o=1;function a(){for(var t,a,s,l,c,f,h,d=e.length,p=0;p<o;++p)for(a=CE(e,NE,zE).visitAfter(u),t=0;t<d;++t)s=e[t],f=n[s.index],h=f*f,l=s.x+s.vx,c=s.y+s.vy,a.visit(g);function g(t,e,n,o,a){var u=t.data,d=t.r,p=f+d;if(!u)return e>l+p||o<l-p||n>c+p||a<c-p;if(u.index>s.index){var g=l-u.x-u.vx,m=c-u.y-u.vy,y=g*g+m*m;y<p*p&&(0===g&&(y+=(g=BE(r))*g),0===m&&(y+=(m=BE(r))*m),y=(p-(y=Math.sqrt(y)))/y*i,s.vx+=(g*=y)*(p=(d*=d)/(h+d)),s.vy+=(m*=y)*p,u.vx-=g*(p=1-p),u.vy-=m*p)}}}function u(t){if(t.data)return t.r=n[t.data.index];for(var e=t.r=0;e<4;++e)t[e]&&t[e].r>t.r&&(t.r=t[e].r)}function s(){if(e){var r,i,o=e.length;for(n=new Array(o),r=0;r<o;++r)i=e[r],n[i.index]=+t(i,r,e)}}return"function"!=typeof t&&(t=TE(null==t?1:+t)),a.initialize=function(t,n){e=t,r=n,s()},a.iterations=function(t){return arguments.length?(o=+t,a):o},a.strength=function(t){return arguments.length?(i=+t,a):i},a.radius=function(e){return arguments.length?(t="function"==typeof e?e:TE(+e),s(),a):t},a},nbody:function(){var t,e,n,r,i,o=TE(-30),a=1,u=1/0,s=.81;function l(n){var i,o=t.length,a=CE(t,cD,fD).visitAfter(f);for(r=n,i=0;i<o;++i)e=t[i],a.visit(h)}function c(){if(t){var e,n,r=t.length;for(i=new Array(r),e=0;e<r;++e)n=t[e],i[n.index]=+o(n,e,t)}}function f(t){var e,n,r,o,a,u=0,s=0;if(t.length){for(r=o=a=0;a<4;++a)(e=t[a])&&(n=Math.abs(e.value))&&(u+=e.value,s+=n,r+=n*e.x,o+=n*e.y);t.x=r/s,t.y=o/s}else{(e=t).x=e.data.x,e.y=e.data.y;do{u+=i[e.data.index]}while(e=e.next)}t.value=u}function h(t,o,l,c){if(!t.value)return!0;var f=t.x-e.x,h=t.y-e.y,d=c-o,p=f*f+h*h;if(d*d/s<p)return p<u&&(0===f&&(p+=(f=BE(n))*f),0===h&&(p+=(h=BE(n))*h),p<a&&(p=Math.sqrt(a*p)),e.vx+=f*t.value*r/p,e.vy+=h*t.value*r/p),!0;if(!(t.length||p>=u)){(t.data!==e||t.next)&&(0===f&&(p+=(f=BE(n))*f),0===h&&(p+=(h=BE(n))*h),p<a&&(p=Math.sqrt(a*p)));do{t.data!==e&&(d=i[t.data.index]*r/p,e.vx+=f*d,e.vy+=h*d)}while(t=t.next)}}return l.initialize=function(e,r){t=e,n=r,c()},l.strength=function(t){return arguments.length?(o="function"==typeof t?t:TE(+t),c(),l):o},l.distanceMin=function(t){return arguments.length?(a=t*t,l):Math.sqrt(a)},l.distanceMax=function(t){return arguments.length?(u=t*t,l):Math.sqrt(u)},l.theta=function(t){return arguments.length?(s=t*t,l):Math.sqrt(s)},l},link:function(t){var e,n,r,i,o,a,u=OE,s=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},l=TE(30),c=1;function f(r){for(var i=0,u=t.length;i<c;++i)for(var s,l,f,h,d,p,g,m=0;m<u;++m)l=(s=t[m]).source,h=(f=s.target).x+f.vx-l.x-l.vx||BE(a),d=f.y+f.vy-l.y-l.vy||BE(a),h*=p=((p=Math.sqrt(h*h+d*d))-n[m])/p*r*e[m],d*=p,f.vx-=h*(g=o[m]),f.vy-=d*g,l.vx+=h*(g=1-g),l.vy+=d*g}function h(){if(r){var a,s,l=r.length,c=t.length,f=new Map(r.map(((t,e)=>[u(t,e,r),t])));for(a=0,i=new Array(l);a<c;++a)(s=t[a]).index=a,"object"!=typeof s.source&&(s.source=RE(f,s.source)),"object"!=typeof s.target&&(s.target=RE(f,s.target)),i[s.source.index]=(i[s.source.index]||0)+1,i[s.target.index]=(i[s.target.index]||0)+1;for(a=0,o=new Array(c);a<c;++a)s=t[a],o[a]=i[s.source.index]/(i[s.source.index]+i[s.target.index]);e=new Array(c),d(),n=new Array(c),p()}}function d(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+s(t[n],n,t)}function p(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+l(t[e],e,t)}return null==t&&(t=[]),f.initialize=function(t,e){r=t,a=e,h()},f.links=function(e){return arguments.length?(t=e,h(),f):t},f.id=function(t){return arguments.length?(u=t,f):u},f.iterations=function(t){return arguments.length?(c=+t,f):c},f.strength=function(t){return arguments.length?(s="function"==typeof t?t:TE(+t),d(),f):s},f.distance=function(t){return arguments.length?(l="function"==typeof t?t:TE(+t),p(),f):l},f},x:function(t){var e,n,r,i=TE(.1);function o(t){for(var i,o=0,a=e.length;o<a;++o)(i=e[o]).vx+=(r[o]-i.x)*n[o]*t}function a(){if(e){var o,a=e.length;for(n=new Array(a),r=new Array(a),o=0;o<a;++o)n[o]=isNaN(r[o]=+t(e[o],o,e))?0:+i(e[o],o,e)}}return"function"!=typeof t&&(t=TE(null==t?0:+t)),o.initialize=function(t){e=t,a()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:TE(+t),a(),o):i},o.x=function(e){return arguments.length?(t="function"==typeof e?e:TE(+e),a(),o):t},o},y:function(t){var e,n,r,i=TE(.1);function o(t){for(var i,o=0,a=e.length;o<a;++o)(i=e[o]).vy+=(r[o]-i.y)*n[o]*t}function a(){if(e){var o,a=e.length;for(n=new Array(a),r=new Array(a),o=0;o<a;++o)n[o]=isNaN(r[o]=+t(e[o],o,e))?0:+i(e[o],o,e)}}return"function"!=typeof t&&(t=TE(null==t?0:+t)),o.initialize=function(t){e=t,a()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:TE(+t),a(),o):i},o.y=function(e){return arguments.length?(t="function"==typeof e?e:TE(+e),a(),o):t},o}},mD="forces",yD=["alpha","alphaMin","alphaTarget","velocityDecay","forces"],vD=["static","iterations"],_D=["x","y","vx","vy"];function xD(t){Qa.call(this,null,t)}function bD(t,e,n,r){var i,o,a,u,s=V(e.forces);for(i=0,o=yD.length;i<o;++i)(a=yD[i])!==mD&&e.modified(a)&&t[a](e[a]);for(i=0,o=s.length;i<o;++i)u=mD+i,(a=n||e.modified(mD,i)?kD(s[i]):r&&wD(s[i],r)?t.force(u):null)&&t.force(u,a);for(o=t.numForces||0;i<o;++i)t.force(mD+i,null);return t.numForces=s.length,t}function wD(t,e){var n,i;for(n in t)if(J(i=t[n])&&e.modified(r(i)))return 1;return 0}function kD(t){var e,n;for(n in lt(gD,t.force)||u("Unrecognized force: "+t.force),e=gD[t.force](),t)J(e[n])&&AD(e[n],t[n],t);return e}function AD(t,e,n){t(J(e)?t=>e(t,n):e)}xD.Definition={type:"Force",metadata:{modifies:!0},params:[{name:"static",type:"boolean",default:!1},{name:"restart",type:"boolean",default:!1},{name:"iterations",type:"number",default:300},{name:"alpha",type:"number",default:1},{name:"alphaMin",type:"number",default:.001},{name:"alphaTarget",type:"number",default:0},{name:"velocityDecay",type:"number",default:.4},{name:"forces",type:"param",array:!0,params:[{key:{force:"center"},params:[{name:"x",type:"number",default:0},{name:"y",type:"number",default:0}]},{key:{force:"collide"},params:[{name:"radius",type:"number",expr:!0},{name:"strength",type:"number",default:.7},{name:"iterations",type:"number",default:1}]},{key:{force:"nbody"},params:[{name:"strength",type:"number",default:-30,expr:!0},{name:"theta",type:"number",default:.9},{name:"distanceMin",type:"number",default:1},{name:"distanceMax",type:"number"}]},{key:{force:"link"},params:[{name:"links",type:"data"},{name:"id",type:"field"},{name:"distance",type:"number",default:30,expr:!0},{name:"strength",type:"number",expr:!0},{name:"iterations",type:"number",default:1}]},{key:{force:"x"},params:[{name:"strength",type:"number",default:.1},{name:"x",type:"field"}]},{key:{force:"y"},params:[{name:"strength",type:"number",default:.1},{name:"y",type:"field"}]}]},{name:"as",type:"string",array:!0,modify:!1,default:_D}]},dt(xD,Qa,{transform(t,e){var n,r,i=this.value,o=e.changed(e.ADD_REM),a=t.modified(yD),u=t.iterations||300;if(i?(o&&(e.modifies("index"),i.nodes(e.source)),(a||e.changed(e.MOD))&&bD(i,t,0,e)):(this.value=i=function(t,e){const n=pD(t),r=n.stop,i=n.restart;let o=!1;return n.stopped=()=>o,n.restart=()=>(o=!1,i()),n.stop=()=>(o=!0,r()),bD(n,e,!0).on("end",(()=>o=!0))}(e.source,t),i.on("tick",(n=e.dataflow,r=this,()=>n.touch(r).run())),t.static||(o=!0,i.tick()),e.modifies("index")),a||o||t.modified(vD)||e.changed()&&t.restart)if(i.alpha(Math.max(i.alpha(),t.alpha||1)).alphaDecay(1-Math.pow(i.alphaMin(),1/u)),t.static)for(i.stop();--u>=0;)i.tick();else if(i.stopped()&&i.restart(),!o)return e.StopPropagation;return this.finish(t,e)},finish(t,e){const n=e.dataflow;for(let t,e=this._argops,u=0,s=e.length;u<s;++u)if(t=e[u],t.name===mD&&"link"===t.op._argval.force)for(var r,i=t.op._argops,o=0,a=i.length;o<a;++o)if("links"===i[o].name&&(r=i[o].op.source)){n.pulse(r,n.changeset().reflow());break}return e.reflow(t.modified()).modifies(_D)}});var MD=Object.freeze({__proto__:null,force:xD});function ED(t,e){return t.parent===e.parent?1:2}function DD(t,e){return t+e.x}function CD(t,e){return Math.max(t,e.y)}function FD(t){var e=0,n=t.children,r=n&&n.length;if(r)for(;--r>=0;)e+=n[r].value;else e=1;t.value=e}function SD(t,e){t instanceof Map?(t=[void 0,t],void 0===e&&(e=TD)):void 0===e&&(e=$D);for(var n,r,i,o,a,u=new zD(t),s=[u];n=s.pop();)if((i=e(n.data))&&(a=(i=Array.from(i)).length))for(n.children=i,o=a-1;o>=0;--o)s.push(r=i[o]=new zD(i[o])),r.parent=n,r.depth=n.depth+1;return u.eachBefore(ND)}function $D(t){return t.children}function TD(t){return Array.isArray(t)?t[1]:null}function BD(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function ND(t){var e=0;do{t.height=e}while((t=t.parent)&&t.height<++e)}function zD(t){this.data=t,this.depth=this.height=0,this.parent=null}function OD(t){return null==t?null:RD(t)}function RD(t){if("function"!=typeof t)throw new Error;return t}function UD(){return 0}function LD(t){return function(){return t}}zD.prototype=SD.prototype={constructor:zD,count:function(){return this.eachAfter(FD)},each:function(t,e){let n=-1;for(const r of this)t.call(e,r,++n,this);return this},eachAfter:function(t,e){for(var n,r,i,o=this,a=[o],u=[],s=-1;o=a.pop();)if(u.push(o),n=o.children)for(r=0,i=n.length;r<i;++r)a.push(n[r]);for(;o=u.pop();)t.call(e,o,++s,this);return this},eachBefore:function(t,e){for(var n,r,i=this,o=[i],a=-1;i=o.pop();)if(t.call(e,i,++a,this),n=i.children)for(r=n.length-1;r>=0;--r)o.push(n[r]);return this},find:function(t,e){let n=-1;for(const r of this)if(t.call(e,r,++n,this))return r},sum:function(t){return this.eachAfter((function(e){for(var n=+t(e.data)||0,r=e.children,i=r&&r.length;--i>=0;)n+=r[i].value;e.value=n}))},sort:function(t){return this.eachBefore((function(e){e.children&&e.children.sort(t)}))},path:function(t){for(var e=this,n=function(t,e){if(t===e)return t;var n=t.ancestors(),r=e.ancestors(),i=null;t=n.pop(),e=r.pop();for(;t===e;)i=t,t=n.pop(),e=r.pop();return i}(e,t),r=[e];e!==n;)e=e.parent,r.push(e);for(var i=r.length;t!==n;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,e=[t];t=t.parent;)e.push(t);return e},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore((function(e){e.children||t.push(e)})),t},links:function(){var t=this,e=[];return t.each((function(n){n!==t&&e.push({source:n.parent,target:n})})),e},copy:function(){return SD(this).eachBefore(BD)},[Symbol.iterator]:function*(){var t,e,n,r,i=this,o=[i];do{for(t=o.reverse(),o=[];i=t.pop();)if(yield i,e=i.children)for(n=0,r=e.length;n<r;++n)o.push(e[n])}while(o.length)}};const qD=1664525,PD=1013904223,jD=4294967296;function ID(t,e){var n,r;if(YD(e,t))return[e];for(n=0;n<t.length;++n)if(WD(e,t[n])&&YD(VD(t[n],e),t))return[t[n],e];for(n=0;n<t.length-1;++n)for(r=n+1;r<t.length;++r)if(WD(VD(t[n],t[r]),e)&&WD(VD(t[n],e),t[r])&&WD(VD(t[r],e),t[n])&&YD(XD(t[n],t[r],e),t))return[t[n],t[r],e];throw new Error}function WD(t,e){var n=t.r-e.r,r=e.x-t.x,i=e.y-t.y;return n<0||n*n<r*r+i*i}function HD(t,e){var n=t.r-e.r+1e-9*Math.max(t.r,e.r,1),r=e.x-t.x,i=e.y-t.y;return n>0&&n*n>r*r+i*i}function YD(t,e){for(var n=0;n<e.length;++n)if(!HD(t,e[n]))return!1;return!0}function GD(t){switch(t.length){case 1:return function(t){return{x:t.x,y:t.y,r:t.r}}(t[0]);case 2:return VD(t[0],t[1]);case 3:return XD(t[0],t[1],t[2])}}function VD(t,e){var n=t.x,r=t.y,i=t.r,o=e.x,a=e.y,u=e.r,s=o-n,l=a-r,c=u-i,f=Math.sqrt(s*s+l*l);return{x:(n+o+s/f*c)/2,y:(r+a+l/f*c)/2,r:(f+i+u)/2}}function XD(t,e,n){var r=t.x,i=t.y,o=t.r,a=e.x,u=e.y,s=e.r,l=n.x,c=n.y,f=n.r,h=r-a,d=r-l,p=i-u,g=i-c,m=s-o,y=f-o,v=r*r+i*i-o*o,_=v-a*a-u*u+s*s,x=v-l*l-c*c+f*f,b=d*p-h*g,w=(p*x-g*_)/(2*b)-r,k=(g*m-p*y)/b,A=(d*_-h*x)/(2*b)-i,M=(h*y-d*m)/b,E=k*k+M*M-1,D=2*(o+w*k+A*M),C=w*w+A*A-o*o,F=-(Math.abs(E)>1e-6?(D+Math.sqrt(D*D-4*E*C))/(2*E):C/D);return{x:r+w+k*F,y:i+A+M*F,r:F}}function JD(t,e,n){var r,i,o,a,u=t.x-e.x,s=t.y-e.y,l=u*u+s*s;l?(i=e.r+n.r,i*=i,a=t.r+n.r,i>(a*=a)?(r=(l+a-i)/(2*l),o=Math.sqrt(Math.max(0,a/l-r*r)),n.x=t.x-r*u-o*s,n.y=t.y-r*s+o*u):(r=(l+i-a)/(2*l),o=Math.sqrt(Math.max(0,i/l-r*r)),n.x=e.x+r*u-o*s,n.y=e.y+r*s+o*u)):(n.x=e.x+n.r,n.y=e.y)}function ZD(t,e){var n=t.r+e.r-1e-6,r=e.x-t.x,i=e.y-t.y;return n>0&&n*n>r*r+i*i}function QD(t){var e=t._,n=t.next._,r=e.r+n.r,i=(e.x*n.r+n.x*e.r)/r,o=(e.y*n.r+n.y*e.r)/r;return i*i+o*o}function KD(t){this._=t,this.next=null,this.previous=null}function tC(t,e){if(!(o=(t=function(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}(t)).length))return 0;var n,r,i,o,a,u,s,l,c,f,h;if((n=t[0]).x=0,n.y=0,!(o>1))return n.r;if(r=t[1],n.x=-r.r,r.x=n.r,r.y=0,!(o>2))return n.r+r.r;JD(r,n,i=t[2]),n=new KD(n),r=new KD(r),i=new KD(i),n.next=i.previous=r,r.next=n.previous=i,i.next=r.previous=n;t:for(s=3;s<o;++s){JD(n._,r._,i=t[s]),i=new KD(i),l=r.next,c=n.previous,f=r._.r,h=n._.r;do{if(f<=h){if(ZD(l._,i._)){r=l,n.next=r,r.previous=n,--s;continue t}f+=l._.r,l=l.next}else{if(ZD(c._,i._)){(n=c).next=r,r.previous=n,--s;continue t}h+=c._.r,c=c.previous}}while(l!==c.next);for(i.previous=n,i.next=r,n.next=r.previous=r=i,a=QD(n);(i=i.next)!==r;)(u=QD(i))<a&&(n=i,a=u);r=n.next}for(n=[r._],i=r;(i=i.next)!==r;)n.push(i._);for(i=function(t,e){for(var n,r,i=0,o=(t=function(t,e){let n,r,i=t.length;for(;i;)r=e()*i--|0,n=t[i],t[i]=t[r],t[r]=n;return t}(Array.from(t),e)).length,a=[];i<o;)n=t[i],r&&HD(r,n)?++i:(r=GD(a=ID(a,n)),i=0);return r}(n,e),s=0;s<o;++s)(n=t[s]).x-=i.x,n.y-=i.y;return i.r}function eC(t){return Math.sqrt(t.value)}function nC(t){return function(e){e.children||(e.r=Math.max(0,+t(e)||0))}}function rC(t,e,n){return function(r){if(i=r.children){var i,o,a,u=i.length,s=t(r)*e||0;if(s)for(o=0;o<u;++o)i[o].r+=s;if(a=tC(i,n),s)for(o=0;o<u;++o)i[o].r-=s;r.r=a+s}}}function iC(t){return function(e){var n=e.parent;e.r*=t,n&&(e.x=n.x+t*e.x,e.y=n.y+t*e.y)}}function oC(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function aC(t,e,n,r,i){for(var o,a=t.children,u=-1,s=a.length,l=t.value&&(r-e)/t.value;++u<s;)(o=a[u]).y0=n,o.y1=i,o.x0=e,o.x1=e+=o.value*l}var uC={depth:-1},sC={},lC={};function cC(t){return t.id}function fC(t){return t.parentId}function hC(){var t,e=cC,n=fC;function r(r){var i,o,a,u,s,l,c,f,h=Array.from(r),d=e,p=n,g=new Map;if(null!=t){const e=h.map(((e,n)=>function(t){t=`${t}`;let e=t.length;pC(t,e-1)&&!pC(t,e-2)&&(t=t.slice(0,-1));return"/"===t[0]?t:`/${t}`}(t(e,n,r)))),n=e.map(dC),i=new Set(e).add("");for(const t of n)i.has(t)||(i.add(t),e.push(t),n.push(dC(t)),h.push(lC));d=(t,n)=>e[n],p=(t,e)=>n[e]}for(a=0,i=h.length;a<i;++a)o=h[a],l=h[a]=new zD(o),null!=(c=d(o,a,r))&&(c+="")&&(f=l.id=c,g.set(f,g.has(f)?sC:l)),null!=(c=p(o,a,r))&&(c+="")&&(l.parent=c);for(a=0;a<i;++a)if(c=(l=h[a]).parent){if(!(s=g.get(c)))throw new Error("missing: "+c);if(s===sC)throw new Error("ambiguous: "+c);s.children?s.children.push(l):s.children=[l],l.parent=s}else{if(u)throw new Error("multiple roots");u=l}if(!u)throw new Error("no root");if(null!=t){for(;u.data===lC&&1===u.children.length;)u=u.children[0],--i;for(let t=h.length-1;t>=0&&(l=h[t]).data===lC;--t)l.data=null}if(u.parent=uC,u.eachBefore((function(t){t.depth=t.parent.depth+1,--i})).eachBefore(ND),u.parent=null,i>0)throw new Error("cycle");return u}return r.id=function(t){return arguments.length?(e=OD(t),r):e},r.parentId=function(t){return arguments.length?(n=OD(t),r):n},r.path=function(e){return arguments.length?(t=OD(e),r):t},r}function dC(t){let e=t.length;if(e<2)return"";for(;--e>1&&!pC(t,e););return t.slice(0,e)}function pC(t,e){if("/"===t[e]){let n=0;for(;e>0&&"\\"===t[--e];)++n;if(0==(1&n))return!0}return!1}function gC(t,e){return t.parent===e.parent?1:2}function mC(t){var e=t.children;return e?e[0]:t.t}function yC(t){var e=t.children;return e?e[e.length-1]:t.t}function vC(t,e,n){var r=n/(e.i-t.i);e.c-=r,e.s+=n,t.c+=r,e.z+=n,e.m+=n}function _C(t,e,n){return t.a.parent===e.parent?t.a:n}function xC(t,e){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=e}function bC(t,e,n,r,i){for(var o,a=t.children,u=-1,s=a.length,l=t.value&&(i-n)/t.value;++u<s;)(o=a[u]).x0=e,o.x1=r,o.y0=n,o.y1=n+=o.value*l}xC.prototype=Object.create(zD.prototype);var wC=(1+Math.sqrt(5))/2;function kC(t,e,n,r,i,o){for(var a,u,s,l,c,f,h,d,p,g,m,y=[],v=e.children,_=0,x=0,b=v.length,w=e.value;_<b;){s=i-n,l=o-r;do{c=v[x++].value}while(!c&&x<b);for(f=h=c,m=c*c*(g=Math.max(l/s,s/l)/(w*t)),p=Math.max(h/m,m/f);x<b;++x){if(c+=u=v[x].value,u<f&&(f=u),u>h&&(h=u),m=c*c*g,(d=Math.max(h/m,m/f))>p){c-=u;break}p=d}y.push(a={value:c,dice:s<l,children:v.slice(_,x)}),a.dice?aC(a,n,r,i,w?r+=l*c/w:o):bC(a,n,r,w?n+=s*c/w:i,o),w-=c,_=x}return y}var AC=function t(e){function n(t,n,r,i,o){kC(e,t,n,r,i,o)}return n.ratio=function(e){return t((e=+e)>1?e:1)},n}(wC);var MC=function t(e){function n(t,n,r,i,o){if((a=t._squarify)&&a.ratio===e)for(var a,u,s,l,c,f=-1,h=a.length,d=t.value;++f<h;){for(s=(u=a[f]).children,l=u.value=0,c=s.length;l<c;++l)u.value+=s[l].value;u.dice?aC(u,n,r,i,d?r+=(o-r)*u.value/d:o):bC(u,n,r,d?n+=(i-n)*u.value/d:i,o),d-=u.value}else t._squarify=a=kC(e,t,n,r,i,o),a.ratio=e}return n.ratio=function(e){return t((e=+e)>1?e:1)},n}(wC);function EC(t,e,n){const r={};return t.each((t=>{const i=t.data;n(i)&&(r[e(i)]=t)})),t.lookup=r,t}function DC(t){Qa.call(this,null,t)}DC.Definition={type:"Nest",metadata:{treesource:!0,changes:!0},params:[{name:"keys",type:"field",array:!0},{name:"generate",type:"boolean"}]};const CC=t=>t.values;function FC(){const t=[],e={entries:t=>r(n(t,0),0),key:n=>(t.push(n),e)};function n(e,r){if(r>=t.length)return e;const i=e.length,o=t[r++],a={},u={};let s,l,c,f=-1;for(;++f<i;)s=o(l=e[f])+"",(c=a[s])?c.push(l):a[s]=[l];for(s in a)u[s]=n(a[s],r);return u}function r(e,n){if(++n>t.length)return e;const i=[];for(const t in e)i.push({key:t,values:r(e[t],n)});return i}return e}function SC(t){Qa.call(this,null,t)}dt(DC,Qa,{transform(t,e){e.source||u("Nest transform requires an upstream data source.");var n=t.generate,r=t.modified(),i=e.clone(),o=this.value;return(!o||r||e.changed())&&(o&&o.each((t=>{t.children&&va(t.data)&&i.rem.push(t.data)})),this.value=o=SD({values:V(t.keys).reduce(((t,e)=>(t.key(e),t)),FC()).entries(i.source)},CC),n&&o.each((t=>{t.children&&(t=ba(t.data),i.add.push(t),i.source.push(t))})),EC(o,_a,_a)),i.source.root=o,i}});const $C=(t,e)=>t.parent===e.parent?1:2;dt(SC,Qa,{transform(t,e){e.source&&e.source.root||u(this.constructor.name+" transform requires a backing tree data source.");const n=this.layout(t.method),r=this.fields,i=e.source.root,o=t.as||r;t.field?i.sum(t.field):i.count(),t.sort&&i.sort(Ma(t.sort,(t=>t.data))),function(t,e,n){for(let r,i=0,o=e.length;i<o;++i)r=e[i],r in n&&t[r](n[r])}(n,this.params,t),n.separation&&n.separation(!1!==t.separation?$C:d);try{this.value=n(i)}catch(t){u(t)}return i.each((t=>function(t,e,n){const r=t.data,i=e.length-1;for(let o=0;o<i;++o)r[n[o]]=t[e[o]];r[n[i]]=t.children?t.children.length:0}(t,r,o))),e.reflow(t.modified()).modifies(o).modifies("leaf")}});const TC=["x","y","r","depth","children"];function BC(t){SC.call(this,t)}BC.Definition={type:"Pack",metadata:{tree:!0,modifies:!0},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"padding",type:"number",default:0},{name:"radius",type:"field",default:null},{name:"size",type:"number",array:!0,length:2},{name:"as",type:"string",array:!0,length:TC.length,default:TC}]},dt(BC,SC,{layout:function(){var t=null,e=1,n=1,r=UD;function i(i){const o=function(){let t=1;return()=>(t=(qD*t+PD)%jD)/jD}();return i.x=e/2,i.y=n/2,t?i.eachBefore(nC(t)).eachAfter(rC(r,.5,o)).eachBefore(iC(1)):i.eachBefore(nC(eC)).eachAfter(rC(UD,1,o)).eachAfter(rC(r,i.r/Math.min(e,n),o)).eachBefore(iC(Math.min(e,n)/(2*i.r))),i}return i.radius=function(e){return arguments.length?(t=OD(e),i):t},i.size=function(t){return arguments.length?(e=+t[0],n=+t[1],i):[e,n]},i.padding=function(t){return arguments.length?(r="function"==typeof t?t:LD(+t),i):r},i},params:["radius","size","padding"],fields:TC});const NC=["x0","y0","x1","y1","depth","children"];function zC(t){SC.call(this,t)}function OC(t){Qa.call(this,null,t)}zC.Definition={type:"Partition",metadata:{tree:!0,modifies:!0},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"padding",type:"number",default:0},{name:"round",type:"boolean",default:!1},{name:"size",type:"number",array:!0,length:2},{name:"as",type:"string",array:!0,length:NC.length,default:NC}]},dt(zC,SC,{layout:function(){var t=1,e=1,n=0,r=!1;function i(i){var o=i.height+1;return i.x0=i.y0=n,i.x1=t,i.y1=e/o,i.eachBefore(function(t,e){return function(r){r.children&&aC(r,r.x0,t*(r.depth+1)/e,r.x1,t*(r.depth+2)/e);var i=r.x0,o=r.y0,a=r.x1-n,u=r.y1-n;a<i&&(i=a=(i+a)/2),u<o&&(o=u=(o+u)/2),r.x0=i,r.y0=o,r.x1=a,r.y1=u}}(e,o)),r&&i.eachBefore(oC),i}return i.round=function(t){return arguments.length?(r=!!t,i):r},i.size=function(n){return arguments.length?(t=+n[0],e=+n[1],i):[t,e]},i.padding=function(t){return arguments.length?(n=+t,i):n},i},params:["size","round","padding"],fields:NC}),OC.Definition={type:"Stratify",metadata:{treesource:!0},params:[{name:"key",type:"field",required:!0},{name:"parentKey",type:"field",required:!0}]},dt(OC,Qa,{transform(t,e){e.source||u("Stratify transform requires an upstream data source.");let n=this.value;const r=t.modified(),i=e.fork(e.ALL).materialize(e.SOURCE),o=!n||r||e.changed(e.ADD_REM)||e.modified(t.key.fields)||e.modified(t.parentKey.fields);return i.source=i.source.slice(),o&&(n=i.source.length?EC(hC().id(t.key).parentId(t.parentKey)(i.source),t.key,p):EC(hC()([{}]),t.key,t.key)),i.source.root=this.value=n,i}});const RC={tidy:function(){var t=gC,e=1,n=1,r=null;function i(i){var s=function(t){for(var e,n,r,i,o,a=new xC(t,0),u=[a];e=u.pop();)if(r=e._.children)for(e.children=new Array(o=r.length),i=o-1;i>=0;--i)u.push(n=e.children[i]=new xC(r[i],i)),n.parent=e;return(a.parent=new xC(null,0)).children=[a],a}(i);if(s.eachAfter(o),s.parent.m=-s.z,s.eachBefore(a),r)i.eachBefore(u);else{var l=i,c=i,f=i;i.eachBefore((function(t){t.x<l.x&&(l=t),t.x>c.x&&(c=t),t.depth>f.depth&&(f=t)}));var h=l===c?1:t(l,c)/2,d=h-l.x,p=e/(c.x+h+d),g=n/(f.depth||1);i.eachBefore((function(t){t.x=(t.x+d)*p,t.y=t.depth*g}))}return i}function o(e){var n=e.children,r=e.parent.children,i=e.i?r[e.i-1]:null;if(n){!function(t){for(var e,n=0,r=0,i=t.children,o=i.length;--o>=0;)(e=i[o]).z+=n,e.m+=n,n+=e.s+(r+=e.c)}(e);var o=(n[0].z+n[n.length-1].z)/2;i?(e.z=i.z+t(e._,i._),e.m=e.z-o):e.z=o}else i&&(e.z=i.z+t(e._,i._));e.parent.A=function(e,n,r){if(n){for(var i,o=e,a=e,u=n,s=o.parent.children[0],l=o.m,c=a.m,f=u.m,h=s.m;u=yC(u),o=mC(o),u&&o;)s=mC(s),(a=yC(a)).a=e,(i=u.z+f-o.z-l+t(u._,o._))>0&&(vC(_C(u,e,r),e,i),l+=i,c+=i),f+=u.m,l+=o.m,h+=s.m,c+=a.m;u&&!yC(a)&&(a.t=u,a.m+=f-c),o&&!mC(s)&&(s.t=o,s.m+=l-h,r=e)}return r}(e,i,e.parent.A||r[0])}function a(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function u(t){t.x*=e,t.y=t.depth*n}return i.separation=function(e){return arguments.length?(t=e,i):t},i.size=function(t){return arguments.length?(r=!1,e=+t[0],n=+t[1],i):r?null:[e,n]},i.nodeSize=function(t){return arguments.length?(r=!0,e=+t[0],n=+t[1],i):r?[e,n]:null},i},cluster:function(){var t=ED,e=1,n=1,r=!1;function i(i){var o,a=0;i.eachAfter((function(e){var n=e.children;n?(e.x=function(t){return t.reduce(DD,0)/t.length}(n),e.y=function(t){return 1+t.reduce(CD,0)}(n)):(e.x=o?a+=t(e,o):0,e.y=0,o=e)}));var u=function(t){for(var e;e=t.children;)t=e[0];return t}(i),s=function(t){for(var e;e=t.children;)t=e[e.length-1];return t}(i),l=u.x-t(u,s)/2,c=s.x+t(s,u)/2;return i.eachAfter(r?function(t){t.x=(t.x-i.x)*e,t.y=(i.y-t.y)*n}:function(t){t.x=(t.x-l)/(c-l)*e,t.y=(1-(i.y?t.y/i.y:1))*n})}return i.separation=function(e){return arguments.length?(t=e,i):t},i.size=function(t){return arguments.length?(r=!1,e=+t[0],n=+t[1],i):r?null:[e,n]},i.nodeSize=function(t){return arguments.length?(r=!0,e=+t[0],n=+t[1],i):r?[e,n]:null},i}},UC=["x","y","depth","children"];function LC(t){SC.call(this,t)}function qC(t){Qa.call(this,[],t)}LC.Definition={type:"Tree",metadata:{tree:!0,modifies:!0},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"method",type:"enum",default:"tidy",values:["tidy","cluster"]},{name:"size",type:"number",array:!0,length:2},{name:"nodeSize",type:"number",array:!0,length:2},{name:"separation",type:"boolean",default:!0},{name:"as",type:"string",array:!0,length:UC.length,default:UC}]},dt(LC,SC,{layout(t){const e=t||"tidy";if(lt(RC,e))return RC[e]();u("Unrecognized Tree layout method: "+e)},params:["size","nodeSize"],fields:UC}),qC.Definition={type:"TreeLinks",metadata:{tree:!0,generates:!0,changes:!0},params:[]},dt(qC,Qa,{transform(t,e){const n=this.value,r=e.source&&e.source.root,i=e.fork(e.NO_SOURCE),o={};return r||u("TreeLinks transform requires a tree data source."),e.changed(e.ADD_REM)?(i.rem=n,e.visit(e.SOURCE,(t=>o[_a(t)]=1)),r.each((t=>{const e=t.data,n=t.parent&&t.parent.data;n&&o[_a(e)]&&o[_a(n)]&&i.add.push(ba({source:n,target:e}))})),this.value=i.add):e.changed(e.MOD)&&(e.visit(e.MOD,(t=>o[_a(t)]=1)),n.forEach((t=>{(o[_a(t.source)]||o[_a(t.target)])&&i.mod.push(t)}))),i}});const PC={binary:function(t,e,n,r,i){var o,a,u=t.children,s=u.length,l=new Array(s+1);for(l[0]=a=o=0;o<s;++o)l[o+1]=a+=u[o].value;!function t(e,n,r,i,o,a,s){if(e>=n-1){var c=u[e];return c.x0=i,c.y0=o,c.x1=a,void(c.y1=s)}var f=l[e],h=r/2+f,d=e+1,p=n-1;for(;d<p;){var g=d+p>>>1;l[g]<h?d=g+1:p=g}h-l[d-1]<l[d]-h&&e+1<d&&--d;var m=l[d]-f,y=r-m;if(a-i>s-o){var v=r?(i*y+a*m)/r:a;t(e,d,m,i,o,v,s),t(d,n,y,v,o,a,s)}else{var _=r?(o*y+s*m)/r:s;t(e,d,m,i,o,a,_),t(d,n,y,i,_,a,s)}}(0,s,t.value,e,n,r,i)},dice:aC,slice:bC,slicedice:function(t,e,n,r,i){(1&t.depth?bC:aC)(t,e,n,r,i)},squarify:AC,resquarify:MC},jC=["x0","y0","x1","y1","depth","children"];function IC(t){SC.call(this,t)}IC.Definition={type:"Treemap",metadata:{tree:!0,modifies:!0},params:[{name:"field",type:"field"},{name:"sort",type:"compare"},{name:"method",type:"enum",default:"squarify",values:["squarify","resquarify","binary","dice","slice","slicedice"]},{name:"padding",type:"number",default:0},{name:"paddingInner",type:"number",default:0},{name:"paddingOuter",type:"number",default:0},{name:"paddingTop",type:"number",default:0},{name:"paddingRight",type:"number",default:0},{name:"paddingBottom",type:"number",default:0},{name:"paddingLeft",type:"number",default:0},{name:"ratio",type:"number",default:1.618033988749895},{name:"round",type:"boolean",default:!1},{name:"size",type:"number",array:!0,length:2},{name:"as",type:"string",array:!0,length:jC.length,default:jC}]},dt(IC,SC,{layout(){const t=function(){var t=AC,e=!1,n=1,r=1,i=[0],o=UD,a=UD,u=UD,s=UD,l=UD;function c(t){return t.x0=t.y0=0,t.x1=n,t.y1=r,t.eachBefore(f),i=[0],e&&t.eachBefore(oC),t}function f(e){var n=i[e.depth],r=e.x0+n,c=e.y0+n,f=e.x1-n,h=e.y1-n;f<r&&(r=f=(r+f)/2),h<c&&(c=h=(c+h)/2),e.x0=r,e.y0=c,e.x1=f,e.y1=h,e.children&&(n=i[e.depth+1]=o(e)/2,r+=l(e)-n,c+=a(e)-n,(f-=u(e)-n)<r&&(r=f=(r+f)/2),(h-=s(e)-n)<c&&(c=h=(c+h)/2),t(e,r,c,f,h))}return c.round=function(t){return arguments.length?(e=!!t,c):e},c.size=function(t){return arguments.length?(n=+t[0],r=+t[1],c):[n,r]},c.tile=function(e){return arguments.length?(t=RD(e),c):t},c.padding=function(t){return arguments.length?c.paddingInner(t).paddingOuter(t):c.paddingInner()},c.paddingInner=function(t){return arguments.length?(o="function"==typeof t?t:LD(+t),c):o},c.paddingOuter=function(t){return arguments.length?c.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):c.paddingTop()},c.paddingTop=function(t){return arguments.length?(a="function"==typeof t?t:LD(+t),c):a},c.paddingRight=function(t){return arguments.length?(u="function"==typeof t?t:LD(+t),c):u},c.paddingBottom=function(t){return arguments.length?(s="function"==typeof t?t:LD(+t),c):s},c.paddingLeft=function(t){return arguments.length?(l="function"==typeof t?t:LD(+t),c):l},c}();return t.ratio=e=>{const n=t.tile();n.ratio&&t.tile(n.ratio(e))},t.method=e=>{lt(PC,e)?t.tile(PC[e]):u("Unrecognized Treemap layout method: "+e)},t},params:["method","ratio","size","round","padding","paddingInner","paddingOuter","paddingTop","paddingRight","paddingBottom","paddingLeft"],fields:jC});var WC=Object.freeze({__proto__:null,nest:DC,pack:BC,partition:zC,stratify:OC,tree:LC,treelinks:qC,treemap:IC});const HC=4278190080;function YC(t,e,n){return new Uint32Array(t.getImageData(0,0,e,n).data.buffer)}function GC(t,e,n){if(!e.length)return;const r=e[0].mark.marktype;"group"===r?e.forEach((e=>{e.items.forEach((e=>GC(t,e.items,n)))})):jy[r].draw(t,{items:n?e.map(VC):e})}function VC(t){const e=ka(t,{});return e.stroke&&0!==e.strokeOpacity||e.fill&&0!==e.fillOpacity?{...e,strokeOpacity:1,stroke:"#000",fillOpacity:0}:e}const XC=5,JC=31,ZC=32,QC=new Uint32Array(ZC+1),KC=new Uint32Array(ZC+1);KC[0]=0,QC[0]=~KC[0];for(let t=1;t<=ZC;++t)KC[t]=KC[t-1]<<1|1,QC[t]=~KC[t];function tF(t,e,n){const r=Math.max(1,Math.sqrt(t*e/1e6)),i=~~((t+2*n+r)/r),o=~~((e+2*n+r)/r),a=t=>~~((t+n)/r);return a.invert=t=>t*r-n,a.bitmap=()=>function(t,e){const n=new Uint32Array(~~((t*e+ZC)/ZC));function r(t,e){n[t]|=e}function i(t,e){n[t]&=e}return{array:n,get:(e,r)=>{const i=r*t+e;return n[i>>>XC]&1<<(i&JC)},set:(e,n)=>{const i=n*t+e;r(i>>>XC,1<<(i&JC))},clear:(e,n)=>{const r=n*t+e;i(r>>>XC,~(1<<(r&JC)))},getRange:(e,r,i,o)=>{let a,u,s,l,c=o;for(;c>=r;--c)if(a=c*t+e,u=c*t+i,s=a>>>XC,l=u>>>XC,s===l){if(n[s]&QC[a&JC]&KC[1+(u&JC)])return!0}else{if(n[s]&QC[a&JC])return!0;if(n[l]&KC[1+(u&JC)])return!0;for(let t=s+1;t<l;++t)if(n[t])return!0}return!1},setRange:(e,n,i,o)=>{let a,u,s,l,c;for(;n<=o;++n)if(a=n*t+e,u=n*t+i,s=a>>>XC,l=u>>>XC,s===l)r(s,QC[a&JC]&KC[1+(u&JC)]);else for(r(s,QC[a&JC]),r(l,KC[1+(u&JC)]),c=s+1;c<l;++c)r(c,4294967295)},clearRange:(e,n,r,o)=>{let a,u,s,l,c;for(;n<=o;++n)if(a=n*t+e,u=n*t+r,s=a>>>XC,l=u>>>XC,s===l)i(s,KC[a&JC]|QC[1+(u&JC)]);else for(i(s,KC[a&JC]),i(l,QC[1+(u&JC)]),c=s+1;c<l;++c)i(c,0)},outOfBounds:(n,r,i,o)=>n<0||r<0||o>=e||i>=t}}(i,o),a.ratio=r,a.padding=n,a.width=t,a.height=e,a}function eF(t,e,n,r,i,o){let a=n/2;return t-a<0||t+a>i||e-(a=r/2)<0||e+a>o}function nF(t,e,n,r,i,o,a,u){const s=i*o/(2*r),l=t(e-s),c=t(e+s),f=t(n-(o/=2)),h=t(n+o);return a.outOfBounds(l,f,c,h)||a.getRange(l,f,c,h)||u&&u.getRange(l,f,c,h)}const rF=[-1,-1,1,1],iF=[-1,1,-1,1];const oF=["right","center","left"],aF=["bottom","middle","top"];function uF(t,e,n,r,i,o,a,u,s,l,c,f){return!(i.outOfBounds(t,n,e,r)||(f&&o||i).getRange(t,n,e,r))}const sF={"top-left":0,top:1,"top-right":2,left:4,middle:5,right:6,"bottom-left":8,bottom:9,"bottom-right":10},lF={naive:function(t,e,n,r){const i=t.width,o=t.height;return function(t){const e=t.datum.datum.items[r].items,n=e.length,a=t.datum.fontSize,u=wy.width(t.datum,t.datum.text);let s,l,c,f,h,d,p,g=0;for(let r=0;r<n;++r)s=e[r].x,c=e[r].y,l=void 0===e[r].x2?s:e[r].x2,f=void 0===e[r].y2?c:e[r].y2,h=(s+l)/2,d=(c+f)/2,p=Math.abs(l-s+f-c),p>=g&&(g=p,t.x=h,t.y=d);return h=u/2,d=a/2,s=t.x-h,l=t.x+h,c=t.y-d,f=t.y+d,t.align="center",s<0&&l<=i?t.align="left":0<=s&&i<l&&(t.align="right"),t.baseline="middle",c<0&&f<=o?t.baseline="top":0<=c&&o<f&&(t.baseline="bottom"),!0}},"reduced-search":function(t,e,n,r){const i=t.width,o=t.height,a=e[0],u=e[1];function s(e,n,r,s,l){const c=t.invert(e),f=t.invert(n);let h,d=r,p=o;if(!eF(c,f,s,l,i,o)&&!nF(t,c,f,l,s,d,a,u)&&!nF(t,c,f,l,s,l,a,null)){for(;p-d>=1;)h=(d+p)/2,nF(t,c,f,l,s,h,a,u)?p=h:d=h;if(d>r)return[c,f,d,!0]}}return function(e){const u=e.datum.datum.items[r].items,l=u.length,c=e.datum.fontSize,f=wy.width(e.datum,e.datum.text);let h,d,p,g,m,y,v,_,x,b,w,k,A,M,E,D,C,F=n?c:0,S=!1,$=!1,T=0;for(let r=0;r<l;++r){for(h=u[r].x,p=u[r].y,d=void 0===u[r].x2?h:u[r].x2,g=void 0===u[r].y2?p:u[r].y2,h>d&&(C=h,h=d,d=C),p>g&&(C=p,p=g,g=C),x=t(h),w=t(d),b=~~((x+w)/2),k=t(p),M=t(g),A=~~((k+M)/2),v=b;v>=x;--v)for(_=A;_>=k;--_)D=s(v,_,F,f,c),D&&([e.x,e.y,F,S]=D);for(v=b;v<=w;++v)for(_=A;_<=M;++_)D=s(v,_,F,f,c),D&&([e.x,e.y,F,S]=D);S||n||(E=Math.abs(d-h+g-p),m=(h+d)/2,y=(p+g)/2,E>=T&&!eF(m,y,f,c,i,o)&&!nF(t,m,y,c,f,c,a,null)&&(T=E,e.x=m,e.y=y,$=!0))}return!(!S&&!$)&&(m=f/2,y=c/2,a.setRange(t(e.x-m),t(e.y-y),t(e.x+m),t(e.y+y)),e.align="center",e.baseline="middle",!0)}},floodfill:function(t,e,n,r){const i=t.width,o=t.height,a=e[0],u=e[1],s=t.bitmap();return function(e){const l=e.datum.datum.items[r].items,c=l.length,f=e.datum.fontSize,h=wy.width(e.datum,e.datum.text),d=[];let p,g,m,y,v,_,x,b,w,k,A,M,E=n?f:0,D=!1,C=!1,F=0;for(let r=0;r<c;++r){for(p=l[r].x,m=l[r].y,g=void 0===l[r].x2?p:l[r].x2,y=void 0===l[r].y2?m:l[r].y2,d.push([t((p+g)/2),t((m+y)/2)]);d.length;)if([x,b]=d.pop(),!(a.get(x,b)||u.get(x,b)||s.get(x,b))){s.set(x,b);for(let t=0;t<4;++t)v=x+rF[t],_=b+iF[t],s.outOfBounds(v,_,v,_)||d.push([v,_]);if(v=t.invert(x),_=t.invert(b),w=E,k=o,!eF(v,_,h,f,i,o)&&!nF(t,v,_,f,h,w,a,u)&&!nF(t,v,_,f,h,f,a,null)){for(;k-w>=1;)A=(w+k)/2,nF(t,v,_,f,h,A,a,u)?k=A:w=A;w>E&&(e.x=v,e.y=_,E=w,D=!0)}}D||n||(M=Math.abs(g-p+y-m),v=(p+g)/2,_=(m+y)/2,M>=F&&!eF(v,_,h,f,i,o)&&!nF(t,v,_,f,h,f,a,null)&&(F=M,e.x=v,e.y=_,C=!0))}return!(!D&&!C)&&(v=h/2,_=f/2,a.setRange(t(e.x-v),t(e.y-_),t(e.x+v),t(e.y+_)),e.align="center",e.baseline="middle",!0)}}};function cF(t,e,n,r,i,o,a,u,s,l,c){if(!t.length)return t;const f=Math.max(r.length,i.length),h=function(t,e){const n=new Float64Array(e),r=t.length;for(let e=0;e<r;++e)n[e]=t[e]||0;for(let t=r;t<e;++t)n[t]=n[r-1];return n}(r,f),d=function(t,e){const n=new Int8Array(e),r=t.length;for(let e=0;e<r;++e)n[e]|=sF[t[e]];for(let t=r;t<e;++t)n[t]=n[r-1];return n}(i,f),p=(x=t[0].datum)&&x.mark&&x.mark.marktype,g="group"===p&&t[0].datum.items[s].marktype,m="area"===g,y=function(t,e,n,r){const i=t=>[t.x,t.x,t.x,t.y,t.y,t.y];return t?"line"===t||"area"===t?t=>i(t.datum):"line"===e?t=>{const e=t.datum.items[r].items;return i(e.length?e["start"===n?0:e.length-1]:{x:NaN,y:NaN})}:t=>{const e=t.datum.bounds;return[e.x1,(e.x1+e.x2)/2,e.x2,e.y1,(e.y1+e.y2)/2,e.y2]}:i}(p,g,u,s),v=null===l||l===1/0,_=m&&"naive"===c;var x;let b=-1,w=-1;const k=t.map((t=>{const e=v?wy.width(t,t.text):void 0;return b=Math.max(b,e),w=Math.max(w,t.fontSize),{datum:t,opacity:0,x:void 0,y:void 0,align:void 0,baseline:void 0,boundary:y(t),textWidth:e}}));l=null===l||l===1/0?Math.max(b,w)+Math.max(...r):l;const A=tF(e[0],e[1],l);let M;if(!_){n&&k.sort(((t,e)=>n(t.datum,e.datum)));let e=!1;for(let t=0;t<d.length&&!e;++t)e=5===d[t]||h[t]<0;const r=(p&&a||m)&&t.map((t=>t.datum));M=o.length||r?function(t,e,n,r,i){const o=t.width,a=t.height,u=r||i,s=Rc(o,a).getContext("2d"),l=Rc(o,a).getContext("2d"),c=u&&Rc(o,a).getContext("2d");n.forEach((t=>GC(s,t,!1))),GC(l,e,!1),u&&GC(c,e,!0);const f=YC(s,o,a),h=YC(l,o,a),d=u&&YC(c,o,a),p=t.bitmap(),g=u&&t.bitmap();let m,y,v,_,x,b,w,k;for(y=0;y<a;++y)for(m=0;m<o;++m)x=y*o+m,b=f[x]&HC,k=h[x]&HC,w=u&&d[x]&HC,(b||w||k)&&(v=t(m),_=t(y),i||!b&&!k||p.set(v,_),u&&(b||w)&&g.set(v,_));return[p,g]}(A,r||[],o,e,m):function(t,e){const n=t.bitmap();return(e||[]).forEach((e=>n.set(t(e.boundary[0]),t(e.boundary[3])))),[n,void 0]}(A,a&&k)}const E=m?lF[c](A,M,a,s):function(t,e,n,r){const i=t.width,o=t.height,a=e[0],u=e[1],s=r.length;return function(e){const l=e.boundary,c=e.datum.fontSize;if(l[2]<0||l[5]<0||l[0]>i||l[3]>o)return!1;let f,h,d,p,g,m,y,v,_,x,b,w,k,A,M,E=e.textWidth??0;for(let i=0;i<s;++i){if(f=(3&n[i])-1,h=(n[i]>>>2&3)-1,d=0===f&&0===h||r[i]<0,p=f&&h?Math.SQRT1_2:1,g=r[i]<0?-1:1,m=l[1+f]+r[i]*f*p,b=l[4+h]+g*c*h/2+r[i]*h*p,v=b-c/2,_=b+c/2,w=t(m),A=t(v),M=t(_),!E){if(!uF(w,w,A,M,a,u,0,0,0,0,0,d))continue;E=wy.width(e.datum,e.datum.text)}if(x=m+g*E*f/2,m=x-E/2,y=x+E/2,w=t(m),k=t(y),uF(w,k,A,M,a,u,0,0,0,0,0,d))return e.x=f?f*g<0?y:m:x,e.y=h?h*g<0?_:v:b,e.align=oF[f*g+1],e.baseline=aF[h*g+1],a.setRange(w,A,k,M),!0}return!1}}(A,M,d,h);return k.forEach((t=>t.opacity=+E(t))),k}const fF=["x","y","opacity","align","baseline"],hF=["top-left","left","bottom-left","top","bottom","top-right","right","bottom-right"];function dF(t){Qa.call(this,null,t)}dF.Definition={type:"Label",metadata:{modifies:!0},params:[{name:"size",type:"number",array:!0,length:2,required:!0},{name:"sort",type:"compare"},{name:"anchor",type:"string",array:!0,default:hF},{name:"offset",type:"number",array:!0,default:[1]},{name:"padding",type:"number",default:0,null:!0},{name:"lineAnchor",type:"string",values:["start","end"],default:"end"},{name:"markIndex",type:"number",default:0},{name:"avoidBaseMark",type:"boolean",default:!0},{name:"avoidMarks",type:"data",array:!0},{name:"method",type:"string",default:"naive"},{name:"as",type:"string",array:!0,length:fF.length,default:fF}]},dt(dF,Qa,{transform(t,e){const n=t.modified();if(!(n||e.changed(e.ADD_REM)||function(n){const r=t[n];return J(r)&&e.modified(r.fields)}("sort")))return;t.size&&2===t.size.length||u("Size parameter should be specified as a [width, height] array.");const r=t.as||fF;return cF(e.materialize(e.SOURCE).source||[],t.size,t.sort,V(null==t.offset?1:t.offset),V(t.anchor||hF),t.avoidMarks||[],!1!==t.avoidBaseMark,t.lineAnchor||"end",t.markIndex||0,void 0===t.padding?0:t.padding,t.method||"naive").forEach((t=>{const e=t.datum;e[r[0]]=t.x,e[r[1]]=t.y,e[r[2]]=t.opacity,e[r[3]]=t.align,e[r[4]]=t.baseline})),e.reflow(n).modifies(r)}});var pF=Object.freeze({__proto__:null,label:dF});function gF(t,e){var n,r,i,o,a,u,s=[],l=function(t){return t(o)};if(null==e)s.push(t);else for(n={},r=0,i=t.length;r<i;++r)o=t[r],(u=n[a=e.map(l)])||(n[a]=u=[],u.dims=a,s.push(u)),u.push(o);return s}function mF(t){Qa.call(this,null,t)}mF.Definition={type:"Loess",metadata:{generates:!0},params:[{name:"x",type:"field",required:!0},{name:"y",type:"field",required:!0},{name:"groupby",type:"field",array:!0},{name:"bandwidth",type:"number",default:.3},{name:"as",type:"string",array:!0}]},dt(mF,Qa,{transform(t,e){const r=e.fork(e.NO_SOURCE|e.NO_FIELDS);if(!this.value||e.changed()||t.modified()){const i=gF(e.materialize(e.SOURCE).source,t.groupby),o=(t.groupby||[]).map(n),a=o.length,u=t.as||[n(t.x),n(t.y)],s=[];i.forEach((e=>{Iu(e,t.x,t.y,t.bandwidth||.3).forEach((t=>{const n={};for(let t=0;t<a;++t)n[o[t]]=e.dims[t];n[u[0]]=t[0],n[u[1]]=t[1],s.push(ba(n))}))})),this.value&&(r.rem=this.value),this.value=r.add=r.source=s}return r}});const yF={constant:Fu,linear:Nu,log:zu,exp:Ou,pow:Ru,quad:Uu,poly:Lu};function vF(t){Qa.call(this,null,t)}vF.Definition={type:"Regression",metadata:{generates:!0},params:[{name:"x",type:"field",required:!0},{name:"y",type:"field",required:!0},{name:"groupby",type:"field",array:!0},{name:"method",type:"string",default:"linear",values:Object.keys(yF)},{name:"order",type:"number",default:3},{name:"extent",type:"number",array:!0,length:2},{name:"params",type:"boolean",default:!1},{name:"as",type:"string",array:!0}]},dt(vF,Qa,{transform(t,e){const r=e.fork(e.NO_SOURCE|e.NO_FIELDS);if(!this.value||e.changed()||t.modified()){const i=gF(e.materialize(e.SOURCE).source,t.groupby),o=(t.groupby||[]).map(n),a=t.method||"linear",s=null==t.order?3:t.order,l=((t,e)=>"poly"===t?e:"quad"===t?2:1)(a,s),c=t.as||[n(t.x),n(t.y)],f=yF[a],h=[];let d=t.extent;lt(yF,a)||u("Invalid regression method: "+a),null!=d&&"log"===a&&d[0]<=0&&(e.dataflow.warn("Ignoring extent with values <= 0 for log regression."),d=null),i.forEach((n=>{if(n.length<=l)return void e.dataflow.warn("Skipping regression with more parameters than data points.");const r=f(n,t.x,t.y,s);if(t.params)return void h.push(ba({keys:n.dims,coef:r.coef,rSquared:r.rSquared}));const i=d||at(n,t.x),u=t=>{const e={};for(let t=0;t<o.length;++t)e[o[t]]=n.dims[t];e[c[0]]=t[0],e[c[1]]=t[1],h.push(ba(e))};"linear"===a||"constant"===a?i.forEach((t=>u([t,r.predict(t)]))):Gu(r.predict,i,25,200).forEach(u)})),this.value&&(r.rem=this.value),this.value=r.add=r.source=h}return r}});var _F=Object.freeze({__proto__:null,loess:mF,regression:vF});const xF=134217729,bF=33306690738754706e-32;function wF(t,e,n,r,i){let o,a,u,s,l=e[0],c=r[0],f=0,h=0;c>l==c>-l?(o=l,l=e[++f]):(o=c,c=r[++h]);let d=0;if(f<t&&h<n)for(c>l==c>-l?(a=l+o,u=o-(a-l),l=e[++f]):(a=c+o,u=o-(a-c),c=r[++h]),o=a,0!==u&&(i[d++]=u);f<t&&h<n;)c>l==c>-l?(a=o+l,s=a-o,u=o-(a-s)+(l-s),l=e[++f]):(a=o+c,s=a-o,u=o-(a-s)+(c-s),c=r[++h]),o=a,0!==u&&(i[d++]=u);for(;f<t;)a=o+l,s=a-o,u=o-(a-s)+(l-s),l=e[++f],o=a,0!==u&&(i[d++]=u);for(;h<n;)a=o+c,s=a-o,u=o-(a-s)+(c-s),c=r[++h],o=a,0!==u&&(i[d++]=u);return 0===o&&0!==d||(i[d++]=o),d}function kF(t){return new Float64Array(t)}const AF=33306690738754716e-32,MF=22204460492503146e-32,EF=11093356479670487e-47,DF=kF(4),CF=kF(8),FF=kF(12),SF=kF(16),$F=kF(4);function TF(t,e,n,r,i,o){const a=(e-o)*(n-i),u=(t-i)*(r-o),s=a-u;if(0===a||0===u||a>0!=u>0)return s;const l=Math.abs(a+u);return Math.abs(s)>=AF*l?s:-function(t,e,n,r,i,o,a){let u,s,l,c,f,h,d,p,g,m,y,v,_,x,b,w,k,A;const M=t-i,E=n-i,D=e-o,C=r-o;x=M*C,h=xF*M,d=h-(h-M),p=M-d,h=xF*C,g=h-(h-C),m=C-g,b=p*m-(x-d*g-p*g-d*m),w=D*E,h=xF*D,d=h-(h-D),p=D-d,h=xF*E,g=h-(h-E),m=E-g,k=p*m-(w-d*g-p*g-d*m),y=b-k,f=b-y,DF[0]=b-(y+f)+(f-k),v=x+y,f=v-x,_=x-(v-f)+(y-f),y=_-w,f=_-y,DF[1]=_-(y+f)+(f-w),A=v+y,f=A-v,DF[2]=v-(A-f)+(y-f),DF[3]=A;let F=function(t,e){let n=e[0];for(let r=1;r<t;r++)n+=e[r];return n}(4,DF),S=MF*a;if(F>=S||-F>=S)return F;if(f=t-M,u=t-(M+f)+(f-i),f=n-E,l=n-(E+f)+(f-i),f=e-D,s=e-(D+f)+(f-o),f=r-C,c=r-(C+f)+(f-o),0===u&&0===s&&0===l&&0===c)return F;if(S=EF*a+bF*Math.abs(F),F+=M*c+C*u-(D*l+E*s),F>=S||-F>=S)return F;x=u*C,h=xF*u,d=h-(h-u),p=u-d,h=xF*C,g=h-(h-C),m=C-g,b=p*m-(x-d*g-p*g-d*m),w=s*E,h=xF*s,d=h-(h-s),p=s-d,h=xF*E,g=h-(h-E),m=E-g,k=p*m-(w-d*g-p*g-d*m),y=b-k,f=b-y,$F[0]=b-(y+f)+(f-k),v=x+y,f=v-x,_=x-(v-f)+(y-f),y=_-w,f=_-y,$F[1]=_-(y+f)+(f-w),A=v+y,f=A-v,$F[2]=v-(A-f)+(y-f),$F[3]=A;const $=wF(4,DF,4,$F,CF);x=M*c,h=xF*M,d=h-(h-M),p=M-d,h=xF*c,g=h-(h-c),m=c-g,b=p*m-(x-d*g-p*g-d*m),w=D*l,h=xF*D,d=h-(h-D),p=D-d,h=xF*l,g=h-(h-l),m=l-g,k=p*m-(w-d*g-p*g-d*m),y=b-k,f=b-y,$F[0]=b-(y+f)+(f-k),v=x+y,f=v-x,_=x-(v-f)+(y-f),y=_-w,f=_-y,$F[1]=_-(y+f)+(f-w),A=v+y,f=A-v,$F[2]=v-(A-f)+(y-f),$F[3]=A;const T=wF($,CF,4,$F,FF);x=u*c,h=xF*u,d=h-(h-u),p=u-d,h=xF*c,g=h-(h-c),m=c-g,b=p*m-(x-d*g-p*g-d*m),w=s*l,h=xF*s,d=h-(h-s),p=s-d,h=xF*l,g=h-(h-l),m=l-g,k=p*m-(w-d*g-p*g-d*m),y=b-k,f=b-y,$F[0]=b-(y+f)+(f-k),v=x+y,f=v-x,_=x-(v-f)+(y-f),y=_-w,f=_-y,$F[1]=_-(y+f)+(f-w),A=v+y,f=A-v,$F[2]=v-(A-f)+(y-f),$F[3]=A;const B=wF(T,FF,4,$F,SF);return SF[B-1]}(t,e,n,r,i,o,l)}const BF=Math.pow(2,-52),NF=new Uint32Array(512);class zF{static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:PF,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:jF;const r=t.length,i=new Float64Array(2*r);for(let o=0;o<r;o++){const r=t[o];i[2*o]=e(r),i[2*o+1]=n(r)}return new zF(i)}constructor(t){const e=t.length>>1;if(e>0&&"number"!=typeof t[0])throw new Error("Expected coords to contain numbers.");this.coords=t;const n=Math.max(2*e-5,0);this._triangles=new Uint32Array(3*n),this._halfedges=new Int32Array(3*n),this._hashSize=Math.ceil(Math.sqrt(e)),this._hullPrev=new Uint32Array(e),this._hullNext=new Uint32Array(e),this._hullTri=new Uint32Array(e),this._hullHash=new Int32Array(this._hashSize).fill(-1),this._ids=new Uint32Array(e),this._dists=new Float64Array(e),this.update()}update(){const{coords:t,_hullPrev:e,_hullNext:n,_hullTri:r,_hullHash:i}=this,o=t.length>>1;let a=1/0,u=1/0,s=-1/0,l=-1/0;for(let e=0;e<o;e++){const n=t[2*e],r=t[2*e+1];n<a&&(a=n),r<u&&(u=r),n>s&&(s=n),r>l&&(l=r),this._ids[e]=e}const c=(a+s)/2,f=(u+l)/2;let h,d,p,g=1/0;for(let e=0;e<o;e++){const n=OF(c,f,t[2*e],t[2*e+1]);n<g&&(h=e,g=n)}const m=t[2*h],y=t[2*h+1];g=1/0;for(let e=0;e<o;e++){if(e===h)continue;const n=OF(m,y,t[2*e],t[2*e+1]);n<g&&n>0&&(d=e,g=n)}let v=t[2*d],_=t[2*d+1],x=1/0;for(let e=0;e<o;e++){if(e===h||e===d)continue;const n=UF(m,y,v,_,t[2*e],t[2*e+1]);n<x&&(p=e,x=n)}let b=t[2*p],w=t[2*p+1];if(x===1/0){for(let e=0;e<o;e++)this._dists[e]=t[2*e]-t[0]||t[2*e+1]-t[1];LF(this._ids,this._dists,0,o-1);const e=new Uint32Array(o);let n=0;for(let t=0,r=-1/0;t<o;t++){const i=this._ids[t];this._dists[i]>r&&(e[n++]=i,r=this._dists[i])}return this.hull=e.subarray(0,n),this.triangles=new Uint32Array(0),void(this.halfedges=new Uint32Array(0))}if(TF(m,y,v,_,b,w)<0){const t=d,e=v,n=_;d=p,v=b,_=w,p=t,b=e,w=n}const k=function(t,e,n,r,i,o){const a=n-t,u=r-e,s=i-t,l=o-e,c=a*a+u*u,f=s*s+l*l,h=.5/(a*l-u*s),d=t+(l*c-u*f)*h,p=e+(a*f-s*c)*h;return{x:d,y:p}}(m,y,v,_,b,w);this._cx=k.x,this._cy=k.y;for(let e=0;e<o;e++)this._dists[e]=OF(t[2*e],t[2*e+1],k.x,k.y);LF(this._ids,this._dists,0,o-1),this._hullStart=h;let A=3;n[h]=e[p]=d,n[d]=e[h]=p,n[p]=e[d]=h,r[h]=0,r[d]=1,r[p]=2,i.fill(-1),i[this._hashKey(m,y)]=h,i[this._hashKey(v,_)]=d,i[this._hashKey(b,w)]=p,this.trianglesLen=0,this._addTriangle(h,d,p,-1,-1,-1);for(let o,a,u=0;u<this._ids.length;u++){const s=this._ids[u],l=t[2*s],c=t[2*s+1];if(u>0&&Math.abs(l-o)<=BF&&Math.abs(c-a)<=BF)continue;if(o=l,a=c,s===h||s===d||s===p)continue;let f=0;for(let t=0,e=this._hashKey(l,c);t<this._hashSize&&(f=i[(e+t)%this._hashSize],-1===f||f===n[f]);t++);f=e[f];let g,m=f;for(;g=n[m],TF(l,c,t[2*m],t[2*m+1],t[2*g],t[2*g+1])>=0;)if(m=g,m===f){m=-1;break}if(-1===m)continue;let y=this._addTriangle(m,s,n[m],-1,-1,r[m]);r[s]=this._legalize(y+2),r[m]=y,A++;let v=n[m];for(;g=n[v],TF(l,c,t[2*v],t[2*v+1],t[2*g],t[2*g+1])<0;)y=this._addTriangle(v,s,g,r[s],-1,r[v]),r[s]=this._legalize(y+2),n[v]=v,A--,v=g;if(m===f)for(;g=e[m],TF(l,c,t[2*g],t[2*g+1],t[2*m],t[2*m+1])<0;)y=this._addTriangle(g,s,m,-1,r[m],r[g]),this._legalize(y+2),r[g]=y,n[m]=m,A--,m=g;this._hullStart=e[s]=m,n[m]=e[v]=s,n[s]=v,i[this._hashKey(l,c)]=s,i[this._hashKey(t[2*m],t[2*m+1])]=m}this.hull=new Uint32Array(A);for(let t=0,e=this._hullStart;t<A;t++)this.hull[t]=e,e=n[e];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(t,e){return Math.floor(function(t,e){const n=t/(Math.abs(t)+Math.abs(e));return(e>0?3-n:1+n)/4}(t-this._cx,e-this._cy)*this._hashSize)%this._hashSize}_legalize(t){const{_triangles:e,_halfedges:n,coords:r}=this;let i=0,o=0;for(;;){const a=n[t],u=t-t%3;if(o=u+(t+2)%3,-1===a){if(0===i)break;t=NF[--i];continue}const s=a-a%3,l=u+(t+1)%3,c=s+(a+2)%3,f=e[o],h=e[t],d=e[l],p=e[c];if(RF(r[2*f],r[2*f+1],r[2*h],r[2*h+1],r[2*d],r[2*d+1],r[2*p],r[2*p+1])){e[t]=p,e[a]=f;const r=n[c];if(-1===r){let e=this._hullStart;do{if(this._hullTri[e]===c){this._hullTri[e]=t;break}e=this._hullPrev[e]}while(e!==this._hullStart)}this._link(t,r),this._link(a,n[o]),this._link(o,c);const u=s+(a+1)%3;i<NF.length&&(NF[i++]=u)}else{if(0===i)break;t=NF[--i]}}return o}_link(t,e){this._halfedges[t]=e,-1!==e&&(this._halfedges[e]=t)}_addTriangle(t,e,n,r,i,o){const a=this.trianglesLen;return this._triangles[a]=t,this._triangles[a+1]=e,this._triangles[a+2]=n,this._link(a,r),this._link(a+1,i),this._link(a+2,o),this.trianglesLen+=3,a}}function OF(t,e,n,r){const i=t-n,o=e-r;return i*i+o*o}function RF(t,e,n,r,i,o,a,u){const s=t-a,l=e-u,c=n-a,f=r-u,h=i-a,d=o-u,p=c*c+f*f,g=h*h+d*d;return s*(f*g-p*d)-l*(c*g-p*h)+(s*s+l*l)*(c*d-f*h)<0}function UF(t,e,n,r,i,o){const a=n-t,u=r-e,s=i-t,l=o-e,c=a*a+u*u,f=s*s+l*l,h=.5/(a*l-u*s),d=(l*c-u*f)*h,p=(a*f-s*c)*h;return d*d+p*p}function LF(t,e,n,r){if(r-n<=20)for(let i=n+1;i<=r;i++){const r=t[i],o=e[r];let a=i-1;for(;a>=n&&e[t[a]]>o;)t[a+1]=t[a--];t[a+1]=r}else{let i=n+1,o=r;qF(t,n+r>>1,i),e[t[n]]>e[t[r]]&&qF(t,n,r),e[t[i]]>e[t[r]]&&qF(t,i,r),e[t[n]]>e[t[i]]&&qF(t,n,i);const a=t[i],u=e[a];for(;;){do{i++}while(e[t[i]]<u);do{o--}while(e[t[o]]>u);if(o<i)break;qF(t,i,o)}t[n+1]=t[o],t[o]=a,r-i+1>=o-n?(LF(t,e,i,r),LF(t,e,n,o-1)):(LF(t,e,n,o-1),LF(t,e,i,r))}}function qF(t,e,n){const r=t[e];t[e]=t[n],t[n]=r}function PF(t){return t[0]}function jF(t){return t[1]}const IF=1e-6;class WF{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(t,e){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(t,e){this._+=`L${this._x1=+t},${this._y1=+e}`}arc(t,e,n){const r=(t=+t)+(n=+n),i=e=+e;if(n<0)throw new Error("negative radius");null===this._x1?this._+=`M${r},${i}`:(Math.abs(this._x1-r)>IF||Math.abs(this._y1-i)>IF)&&(this._+="L"+r+","+i),n&&(this._+=`A${n},${n},0,1,1,${t-n},${e}A${n},${n},0,1,1,${this._x1=r},${this._y1=i}`)}rect(t,e,n,r){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${+n}v${+r}h${-n}Z`}value(){return this._||null}}class HF{constructor(){this._=[]}moveTo(t,e){this._.push([t,e])}closePath(){this._.push(this._[0].slice())}lineTo(t,e){this._.push([t,e])}value(){return this._.length?this._:null}}const YF=2*Math.PI,GF=Math.pow;function VF(t){return t[0]}function XF(t){return t[1]}function JF(t,e,n){return[t+Math.sin(t+e)*n,e+Math.cos(t-e)*n]}class ZF{static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:VF,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:XF,r=arguments.length>3?arguments[3]:void 0;return new ZF("length"in t?function(t,e,n,r){const i=t.length,o=new Float64Array(2*i);for(let a=0;a<i;++a){const i=t[a];o[2*a]=e.call(r,i,a,t),o[2*a+1]=n.call(r,i,a,t)}return o}(t,e,n,r):Float64Array.from(function*(t,e,n,r){let i=0;for(const o of t)yield e.call(r,o,i,t),yield n.call(r,o,i,t),++i}(t,e,n,r)))}constructor(t){this._delaunator=new zF(t),this.inedges=new Int32Array(t.length/2),this._hullIndex=new Int32Array(t.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){const t=this._delaunator,e=this.points;if(t.hull&&t.hull.length>2&&function(t){const{triangles:e,coords:n}=t;for(let t=0;t<e.length;t+=3){const r=2*e[t],i=2*e[t+1],o=2*e[t+2];if((n[o]-n[r])*(n[i+1]-n[r+1])-(n[i]-n[r])*(n[o+1]-n[r+1])>1e-10)return!1}return!0}(t)){this.collinear=Int32Array.from({length:e.length/2},((t,e)=>e)).sort(((t,n)=>e[2*t]-e[2*n]||e[2*t+1]-e[2*n+1]));const t=this.collinear[0],n=this.collinear[this.collinear.length-1],r=[e[2*t],e[2*t+1],e[2*n],e[2*n+1]],i=1e-8*Math.hypot(r[3]-r[1],r[2]-r[0]);for(let t=0,n=e.length/2;t<n;++t){const n=JF(e[2*t],e[2*t+1],i);e[2*t]=n[0],e[2*t+1]=n[1]}this._delaunator=new zF(e)}else delete this.collinear;const n=this.halfedges=this._delaunator.halfedges,r=this.hull=this._delaunator.hull,i=this.triangles=this._delaunator.triangles,o=this.inedges.fill(-1),a=this._hullIndex.fill(-1);for(let t=0,e=n.length;t<e;++t){const e=i[t%3==2?t-2:t+1];-1!==n[t]&&-1!==o[e]||(o[e]=t)}for(let t=0,e=r.length;t<e;++t)a[r[t]]=t;r.length<=2&&r.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=r[0],o[r[0]]=1,2===r.length&&(o[r[1]]=0,this.triangles[1]=r[1],this.triangles[2]=r[1]))}voronoi(t){return new class{constructor(t){let[e,n,r,i]=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[0,0,960,500];if(!((r=+r)>=(e=+e)&&(i=+i)>=(n=+n)))throw new Error("invalid bounds");this.delaunay=t,this._circumcenters=new Float64Array(2*t.points.length),this.vectors=new Float64Array(2*t.points.length),this.xmax=r,this.xmin=e,this.ymax=i,this.ymin=n,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){const{delaunay:{points:t,hull:e,triangles:n},vectors:r}=this;let i,o;const a=this.circumcenters=this._circumcenters.subarray(0,n.length/3*2);for(let r,u,s=0,l=0,c=n.length;s<c;s+=3,l+=2){const c=2*n[s],f=2*n[s+1],h=2*n[s+2],d=t[c],p=t[c+1],g=t[f],m=t[f+1],y=t[h],v=t[h+1],_=g-d,x=m-p,b=y-d,w=v-p,k=2*(_*w-x*b);if(Math.abs(k)<1e-9){if(void 0===i){i=o=0;for(const n of e)i+=t[2*n],o+=t[2*n+1];i/=e.length,o/=e.length}const n=1e9*Math.sign((i-d)*w-(o-p)*b);r=(d+y)/2-n*w,u=(p+v)/2+n*b}else{const t=1/k,e=_*_+x*x,n=b*b+w*w;r=d+(w*e-x*n)*t,u=p+(_*n-b*e)*t}a[l]=r,a[l+1]=u}let u,s,l,c=e[e.length-1],f=4*c,h=t[2*c],d=t[2*c+1];r.fill(0);for(let n=0;n<e.length;++n)c=e[n],u=f,s=h,l=d,f=4*c,h=t[2*c],d=t[2*c+1],r[u+2]=r[f]=l-d,r[u+3]=r[f+1]=h-s}render(t){const e=null==t?t=new WF:void 0,{delaunay:{halfedges:n,inedges:r,hull:i},circumcenters:o,vectors:a}=this;if(i.length<=1)return null;for(let e=0,r=n.length;e<r;++e){const r=n[e];if(r<e)continue;const i=2*Math.floor(e/3),a=2*Math.floor(r/3),u=o[i],s=o[i+1],l=o[a],c=o[a+1];this._renderSegment(u,s,l,c,t)}let u,s=i[i.length-1];for(let e=0;e<i.length;++e){u=s,s=i[e];const n=2*Math.floor(r[s]/3),l=o[n],c=o[n+1],f=4*u,h=this._project(l,c,a[f+2],a[f+3]);h&&this._renderSegment(l,c,h[0],h[1],t)}return e&&e.value()}renderBounds(t){const e=null==t?t=new WF:void 0;return t.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),e&&e.value()}renderCell(t,e){const n=null==e?e=new WF:void 0,r=this._clip(t);if(null===r||!r.length)return;e.moveTo(r[0],r[1]);let i=r.length;for(;r[0]===r[i-2]&&r[1]===r[i-1]&&i>1;)i-=2;for(let t=2;t<i;t+=2)r[t]===r[t-2]&&r[t+1]===r[t-1]||e.lineTo(r[t],r[t+1]);return e.closePath(),n&&n.value()}*cellPolygons(){const{delaunay:{points:t}}=this;for(let e=0,n=t.length/2;e<n;++e){const t=this.cellPolygon(e);t&&(t.index=e,yield t)}}cellPolygon(t){const e=new HF;return this.renderCell(t,e),e.value()}_renderSegment(t,e,n,r,i){let o;const a=this._regioncode(t,e),u=this._regioncode(n,r);0===a&&0===u?(i.moveTo(t,e),i.lineTo(n,r)):(o=this._clipSegment(t,e,n,r,a,u))&&(i.moveTo(o[0],o[1]),i.lineTo(o[2],o[3]))}contains(t,e,n){return(e=+e)==e&&(n=+n)==n&&this.delaunay._step(t,e,n)===t}*neighbors(t){const e=this._clip(t);if(e)for(const n of this.delaunay.neighbors(t)){const t=this._clip(n);if(t)t:for(let r=0,i=e.length;r<i;r+=2)for(let o=0,a=t.length;o<a;o+=2)if(e[r]===t[o]&&e[r+1]===t[o+1]&&e[(r+2)%i]===t[(o+a-2)%a]&&e[(r+3)%i]===t[(o+a-1)%a]){yield n;break t}}}_cell(t){const{circumcenters:e,delaunay:{inedges:n,halfedges:r,triangles:i}}=this,o=n[t];if(-1===o)return null;const a=[];let u=o;do{const n=Math.floor(u/3);if(a.push(e[2*n],e[2*n+1]),u=u%3==2?u-2:u+1,i[u]!==t)break;u=r[u]}while(u!==o&&-1!==u);return a}_clip(t){if(0===t&&1===this.delaunay.hull.length)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];const e=this._cell(t);if(null===e)return null;const{vectors:n}=this,r=4*t;return this._simplify(n[r]||n[r+1]?this._clipInfinite(t,e,n[r],n[r+1],n[r+2],n[r+3]):this._clipFinite(t,e))}_clipFinite(t,e){const n=e.length;let r,i,o,a,u=null,s=e[n-2],l=e[n-1],c=this._regioncode(s,l),f=0;for(let h=0;h<n;h+=2)if(r=s,i=l,s=e[h],l=e[h+1],o=c,c=this._regioncode(s,l),0===o&&0===c)a=f,f=0,u?u.push(s,l):u=[s,l];else{let e,n,h,d,p;if(0===o){if(null===(e=this._clipSegment(r,i,s,l,o,c)))continue;[n,h,d,p]=e}else{if(null===(e=this._clipSegment(s,l,r,i,c,o)))continue;[d,p,n,h]=e,a=f,f=this._edgecode(n,h),a&&f&&this._edge(t,a,f,u,u.length),u?u.push(n,h):u=[n,h]}a=f,f=this._edgecode(d,p),a&&f&&this._edge(t,a,f,u,u.length),u?u.push(d,p):u=[d,p]}if(u)a=f,f=this._edgecode(u[0],u[1]),a&&f&&this._edge(t,a,f,u,u.length);else if(this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return u}_clipSegment(t,e,n,r,i,o){const a=i<o;for(a&&([t,e,n,r,i,o]=[n,r,t,e,o,i]);;){if(0===i&&0===o)return a?[n,r,t,e]:[t,e,n,r];if(i&o)return null;let u,s,l=i||o;8&l?(u=t+(n-t)*(this.ymax-e)/(r-e),s=this.ymax):4&l?(u=t+(n-t)*(this.ymin-e)/(r-e),s=this.ymin):2&l?(s=e+(r-e)*(this.xmax-t)/(n-t),u=this.xmax):(s=e+(r-e)*(this.xmin-t)/(n-t),u=this.xmin),i?(t=u,e=s,i=this._regioncode(t,e)):(n=u,r=s,o=this._regioncode(n,r))}}_clipInfinite(t,e,n,r,i,o){let a,u=Array.from(e);if((a=this._project(u[0],u[1],n,r))&&u.unshift(a[0],a[1]),(a=this._project(u[u.length-2],u[u.length-1],i,o))&&u.push(a[0],a[1]),u=this._clipFinite(t,u))for(let e,n=0,r=u.length,i=this._edgecode(u[r-2],u[r-1]);n<r;n+=2)e=i,i=this._edgecode(u[n],u[n+1]),e&&i&&(n=this._edge(t,e,i,u,n),r=u.length);else this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(u=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return u}_edge(t,e,n,r,i){for(;e!==n;){let n,o;switch(e){case 5:e=4;continue;case 4:e=6,n=this.xmax,o=this.ymin;break;case 6:e=2;continue;case 2:e=10,n=this.xmax,o=this.ymax;break;case 10:e=8;continue;case 8:e=9,n=this.xmin,o=this.ymax;break;case 9:e=1;continue;case 1:e=5,n=this.xmin,o=this.ymin}r[i]===n&&r[i+1]===o||!this.contains(t,n,o)||(r.splice(i,0,n,o),i+=2)}return i}_project(t,e,n,r){let i,o,a,u=1/0;if(r<0){if(e<=this.ymin)return null;(i=(this.ymin-e)/r)<u&&(a=this.ymin,o=t+(u=i)*n)}else if(r>0){if(e>=this.ymax)return null;(i=(this.ymax-e)/r)<u&&(a=this.ymax,o=t+(u=i)*n)}if(n>0){if(t>=this.xmax)return null;(i=(this.xmax-t)/n)<u&&(o=this.xmax,a=e+(u=i)*r)}else if(n<0){if(t<=this.xmin)return null;(i=(this.xmin-t)/n)<u&&(o=this.xmin,a=e+(u=i)*r)}return[o,a]}_edgecode(t,e){return(t===this.xmin?1:t===this.xmax?2:0)|(e===this.ymin?4:e===this.ymax?8:0)}_regioncode(t,e){return(t<this.xmin?1:t>this.xmax?2:0)|(e<this.ymin?4:e>this.ymax?8:0)}_simplify(t){if(t&&t.length>4){for(let e=0;e<t.length;e+=2){const n=(e+2)%t.length,r=(e+4)%t.length;(t[e]===t[n]&&t[n]===t[r]||t[e+1]===t[n+1]&&t[n+1]===t[r+1])&&(t.splice(n,2),e-=2)}t.length||(t=null)}return t}}(this,t)}*neighbors(t){const{inedges:e,hull:n,_hullIndex:r,halfedges:i,triangles:o,collinear:a}=this;if(a){const e=a.indexOf(t);return e>0&&(yield a[e-1]),void(e<a.length-1&&(yield a[e+1]))}const u=e[t];if(-1===u)return;let s=u,l=-1;do{if(yield l=o[s],s=s%3==2?s-2:s+1,o[s]!==t)return;if(s=i[s],-1===s){const e=n[(r[t]+1)%n.length];return void(e!==l&&(yield e))}}while(s!==u)}find(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if((t=+t)!=t||(e=+e)!=e)return-1;const r=n;let i;for(;(i=this._step(n,t,e))>=0&&i!==n&&i!==r;)n=i;return i}_step(t,e,n){const{inedges:r,hull:i,_hullIndex:o,halfedges:a,triangles:u,points:s}=this;if(-1===r[t]||!s.length)return(t+1)%(s.length>>1);let l=t,c=GF(e-s[2*t],2)+GF(n-s[2*t+1],2);const f=r[t];let h=f;do{let r=u[h];const f=GF(e-s[2*r],2)+GF(n-s[2*r+1],2);if(f<c&&(c=f,l=r),h=h%3==2?h-2:h+1,u[h]!==t)break;if(h=a[h],-1===h){if(h=i[(o[t]+1)%i.length],h!==r&&GF(e-s[2*h],2)+GF(n-s[2*h+1],2)<c)return h;break}}while(h!==f);return l}render(t){const e=null==t?t=new WF:void 0,{points:n,halfedges:r,triangles:i}=this;for(let e=0,o=r.length;e<o;++e){const o=r[e];if(o<e)continue;const a=2*i[e],u=2*i[o];t.moveTo(n[a],n[a+1]),t.lineTo(n[u],n[u+1])}return this.renderHull(t),e&&e.value()}renderPoints(t,e){void 0!==e||t&&"function"==typeof t.moveTo||(e=t,t=null),e=null==e?2:+e;const n=null==t?t=new WF:void 0,{points:r}=this;for(let n=0,i=r.length;n<i;n+=2){const i=r[n],o=r[n+1];t.moveTo(i+e,o),t.arc(i,o,e,0,YF)}return n&&n.value()}renderHull(t){const e=null==t?t=new WF:void 0,{hull:n,points:r}=this,i=2*n[0],o=n.length;t.moveTo(r[i],r[i+1]);for(let e=1;e<o;++e){const i=2*n[e];t.lineTo(r[i],r[i+1])}return t.closePath(),e&&e.value()}hullPolygon(){const t=new HF;return this.renderHull(t),t.value()}renderTriangle(t,e){const n=null==e?e=new WF:void 0,{points:r,triangles:i}=this,o=2*i[t*=3],a=2*i[t+1],u=2*i[t+2];return e.moveTo(r[o],r[o+1]),e.lineTo(r[a],r[a+1]),e.lineTo(r[u],r[u+1]),e.closePath(),n&&n.value()}*trianglePolygons(){const{triangles:t}=this;for(let e=0,n=t.length/3;e<n;++e)yield this.trianglePolygon(e)}trianglePolygon(t){const e=new HF;return this.renderTriangle(t,e),e.value()}}function QF(t){Qa.call(this,null,t)}QF.Definition={type:"Voronoi",metadata:{modifies:!0},params:[{name:"x",type:"field",required:!0},{name:"y",type:"field",required:!0},{name:"size",type:"number",array:!0,length:2},{name:"extent",type:"array",array:!0,length:2,default:[[-1e5,-1e5],[1e5,1e5]],content:{type:"number",array:!0,length:2}},{name:"as",type:"string",default:"path"}]};const KF=[-1e5,-1e5,1e5,1e5];function tS(t){const e=t[0][0],n=t[0][1];let r=t.length-1;for(;t[r][0]===e&&t[r][1]===n;--r);return"M"+t.slice(0,r+1).join("L")+"Z"}dt(QF,Qa,{transform(t,e){const n=t.as||"path",r=e.source;if(!r||!r.length)return e;let i=t.size;i=i?[0,0,i[0],i[1]]:(i=t.extent)?[i[0][0],i[0][1],i[1][0],i[1][1]]:KF;const o=this.value=ZF.from(r,t.x,t.y).voronoi(i);for(let t=0,e=r.length;t<e;++t){const e=o.cellPolygon(t);r[t][n]=e?tS(e):null}return e.reflow(t.modified()).modifies(n)}});var eS=Object.freeze({__proto__:null,voronoi:QF}),nS=Math.PI/180,rS=64,iS=2048;function oS(){var t,e,n,r,i,o,a,u=[256,256],s=cS,l=[],c=Math.random,f={};function h(t,e,n){for(var r,i,o,a=e.x,l=e.y,f=Math.hypot(u[0],u[1]),h=s(u),d=c()<.5?1:-1,p=-d;(r=h(p+=d))&&(i=~~r[0],o=~~r[1],!(Math.min(Math.abs(i),Math.abs(o))>=f));)if(e.x=a+i,e.y=l+o,!(e.x+e.x0<0||e.y+e.y0<0||e.x+e.x1>u[0]||e.y+e.y1>u[1])&&(!n||!uS(e,t,u[0]))&&(!n||lS(e,n))){for(var g,m=e.sprite,y=e.width>>5,v=u[0]>>5,_=e.x-(y<<4),x=127&_,b=32-x,w=e.y1-e.y0,k=(e.y+e.y0)*v+(_>>5),A=0;A<w;A++){g=0;for(var M=0;M<=y;M++)t[k+M]|=g<<b|(M<y?(g=m[A*y+M])>>>x:0);k+=v}return e.sprite=null,!0}return!1}return f.layout=function(){for(var s=function(t){t.width=t.height=1;var e=Math.sqrt(t.getContext("2d").getImageData(0,0,1,1).data.length>>2);t.width=(rS<<5)/e,t.height=iS/e;var n=t.getContext("2d");return n.fillStyle=n.strokeStyle="red",n.textAlign="center",{context:n,ratio:e}}(Rc()),f=function(t){var e=[],n=-1;for(;++n<t;)e[n]=0;return e}((u[0]>>5)*u[1]),d=null,p=l.length,g=-1,m=[],y=l.map((u=>({text:t(u),font:e(u),style:r(u),weight:i(u),rotate:o(u),size:~~(n(u)+1e-14),padding:a(u),xoff:0,yoff:0,x1:0,y1:0,x0:0,y0:0,hasText:!1,sprite:null,datum:u}))).sort(((t,e)=>e.size-t.size));++g<p;){var v=y[g];v.x=u[0]*(c()+.5)>>1,v.y=u[1]*(c()+.5)>>1,aS(s,v,y,g),v.hasText&&h(f,v,d)&&(m.push(v),d?sS(d,v):d=[{x:v.x+v.x0,y:v.y+v.y0},{x:v.x+v.x1,y:v.y+v.y1}],v.x-=u[0]>>1,v.y-=u[1]>>1)}return m},f.words=function(t){return arguments.length?(l=t,f):l},f.size=function(t){return arguments.length?(u=[+t[0],+t[1]],f):u},f.font=function(t){return arguments.length?(e=fS(t),f):e},f.fontStyle=function(t){return arguments.length?(r=fS(t),f):r},f.fontWeight=function(t){return arguments.length?(i=fS(t),f):i},f.rotate=function(t){return arguments.length?(o=fS(t),f):o},f.text=function(e){return arguments.length?(t=fS(e),f):t},f.spiral=function(t){return arguments.length?(s=hS[t]||t,f):s},f.fontSize=function(t){return arguments.length?(n=fS(t),f):n},f.padding=function(t){return arguments.length?(a=fS(t),f):a},f.random=function(t){return arguments.length?(c=t,f):c},f}function aS(t,e,n,r){if(!e.sprite){var i=t.context,o=t.ratio;i.clearRect(0,0,(rS<<5)/o,iS/o);var a,u,s,l,c,f=0,h=0,d=0,p=n.length;for(--r;++r<p;){if(e=n[r],i.save(),i.font=e.style+" "+e.weight+" "+~~((e.size+1)/o)+"px "+e.font,a=i.measureText(e.text+"m").width*o,s=e.size<<1,e.rotate){var g=Math.sin(e.rotate*nS),m=Math.cos(e.rotate*nS),y=a*m,v=a*g,_=s*m,x=s*g;a=Math.max(Math.abs(y+x),Math.abs(y-x))+31>>5<<5,s=~~Math.max(Math.abs(v+_),Math.abs(v-_))}else a=a+31>>5<<5;if(s>d&&(d=s),f+a>=rS<<5&&(f=0,h+=d,d=0),h+s>=iS)break;i.translate((f+(a>>1))/o,(h+(s>>1))/o),e.rotate&&i.rotate(e.rotate*nS),i.fillText(e.text,0,0),e.padding&&(i.lineWidth=2*e.padding,i.strokeText(e.text,0,0)),i.restore(),e.width=a,e.height=s,e.xoff=f,e.yoff=h,e.x1=a>>1,e.y1=s>>1,e.x0=-e.x1,e.y0=-e.y1,e.hasText=!0,f+=a}for(var b=i.getImageData(0,0,(rS<<5)/o,iS/o).data,w=[];--r>=0;)if((e=n[r]).hasText){for(u=(a=e.width)>>5,s=e.y1-e.y0,l=0;l<s*u;l++)w[l]=0;if(null==(f=e.xoff))return;h=e.yoff;var k=0,A=-1;for(c=0;c<s;c++){for(l=0;l<a;l++){var M=u*c+(l>>5),E=b[(h+c)*(rS<<5)+(f+l)<<2]?1<<31-l%32:0;w[M]|=E,k|=E}k?A=c:(e.y0++,s--,c--,h++)}e.y1=e.y0+A,e.sprite=w.slice(0,(e.y1-e.y0)*u)}}}function uS(t,e,n){n>>=5;for(var r,i=t.sprite,o=t.width>>5,a=t.x-(o<<4),u=127&a,s=32-u,l=t.y1-t.y0,c=(t.y+t.y0)*n+(a>>5),f=0;f<l;f++){r=0;for(var h=0;h<=o;h++)if((r<<s|(h<o?(r=i[f*o+h])>>>u:0))&e[c+h])return!0;c+=n}return!1}function sS(t,e){var n=t[0],r=t[1];e.x+e.x0<n.x&&(n.x=e.x+e.x0),e.y+e.y0<n.y&&(n.y=e.y+e.y0),e.x+e.x1>r.x&&(r.x=e.x+e.x1),e.y+e.y1>r.y&&(r.y=e.y+e.y1)}function lS(t,e){return t.x+t.x1>e[0].x&&t.x+t.x0<e[1].x&&t.y+t.y1>e[0].y&&t.y+t.y0<e[1].y}function cS(t){var e=t[0]/t[1];return function(t){return[e*(t*=.1)*Math.cos(t),t*Math.sin(t)]}}function fS(t){return"function"==typeof t?t:function(){return t}}var hS={archimedean:cS,rectangular:function(t){var e=4*t[0]/t[1],n=0,r=0;return function(t){var i=t<0?-1:1;switch(Math.sqrt(1+4*i*t)-i&3){case 0:n+=e;break;case 1:r+=4;break;case 2:n-=e;break;default:r-=4}return[n,r]}}};const dS=["x","y","font","fontSize","fontStyle","fontWeight","angle"],pS=["text","font","rotate","fontSize","fontStyle","fontWeight"];function gS(t){Qa.call(this,oS(),t)}gS.Definition={type:"Wordcloud",metadata:{modifies:!0},params:[{name:"size",type:"number",array:!0,length:2},{name:"font",type:"string",expr:!0,default:"sans-serif"},{name:"fontStyle",type:"string",expr:!0,default:"normal"},{name:"fontWeight",type:"string",expr:!0,default:"normal"},{name:"fontSize",type:"number",expr:!0,default:14},{name:"fontSizeRange",type:"number",array:"nullable",default:[10,50]},{name:"rotate",type:"number",expr:!0,default:0},{name:"text",type:"field"},{name:"spiral",type:"string",values:["archimedean","rectangular"]},{name:"padding",type:"number",expr:!0},{name:"as",type:"string",array:!0,length:7,default:dS}]},dt(gS,Qa,{transform(e,n){!e.size||e.size[0]&&e.size[1]||u("Wordcloud size dimensions must be non-zero.");const r=e.modified();if(!(r||n.changed(n.ADD_REM)||pS.some((function(t){const r=e[t];return J(r)&&n.modified(r.fields)}))))return;const i=n.materialize(n.SOURCE).source,o=this.value,a=e.as||dS;let s,l=e.fontSize||14;if(J(l)?s=e.fontSizeRange:l=rt(l),s){const t=l,e=np("sqrt")().domain(at(i,t)).range(s);l=n=>e(t(n))}i.forEach((t=>{t[a[0]]=NaN,t[a[1]]=NaN,t[a[3]]=0}));const c=o.words(i).text(e.text).size(e.size||[500,500]).padding(e.padding||1).spiral(e.spiral||"archimedean").rotate(e.rotate||0).font(e.font||"sans-serif").fontStyle(e.fontStyle||"normal").fontWeight(e.fontWeight||"normal").fontSize(l).random(t.random).layout(),f=o.size(),h=f[0]>>1,d=f[1]>>1,p=c.length;for(let t,e,n=0;n<p;++n)t=c[n],e=t.datum,e[a[0]]=t.x+h,e[a[1]]=t.y+d,e[a[2]]=t.font,e[a[3]]=t.size,e[a[4]]=t.style,e[a[5]]=t.weight,e[a[6]]=t.rotate;return n.reflow(r).modifies(a)}});var mS=Object.freeze({__proto__:null,wordcloud:gS});const yS=t=>new Uint8Array(t),vS=t=>new Uint16Array(t),_S=t=>new Uint32Array(t);function xS(t,e,n){const r=(e<257?yS:e<65537?vS:_S)(t);return n&&r.set(n),r}function bS(t,e,n){const r=1<<e;return{one:r,zero:~r,range:n.slice(),bisect:t.bisect,index:t.index,size:t.size,onAdd(t,e){const n=this,i=n.bisect(n.range,t.value),o=t.index,a=i[0],u=i[1],s=o.length;let l;for(l=0;l<a;++l)e[o[l]]|=r;for(l=u;l<s;++l)e[o[l]]|=r;return n}}}function wS(){let t=_S(0),e=[],n=0;return{insert:function(r,i,o){if(!i.length)return[];const a=n,u=i.length,s=_S(u);let l,c,f,h=Array(u);for(f=0;f<u;++f)h[f]=r(i[f]),s[f]=f;if(h=function(t,e){return t.sort.call(e,((e,n)=>{const r=t[e],i=t[n];return r<i?-1:r>i?1:0})),function(t,e){return Array.from(e,(e=>t[e]))}(t,e)}(h,s),a)l=e,c=t,e=Array(a+u),t=_S(a+u),function(t,e,n,r,i,o,a,u,s){let l,c=0,f=0;for(l=0;c<r&&f<a;++l)e[c]<i[f]?(u[l]=e[c],s[l]=n[c++]):(u[l]=i[f],s[l]=o[f++]+t);for(;c<r;++c,++l)u[l]=e[c],s[l]=n[c];for(;f<a;++f,++l)u[l]=i[f],s[l]=o[f]+t}(o,l,c,a,h,s,u,e,t);else{if(o>0)for(f=0;f<u;++f)s[f]+=o;e=h,t=s}return n=a+u,{index:s,value:h}},remove:function(r,i){const o=n;let a,u,s;for(u=0;!i[t[u]]&&u<o;++u);for(s=u;u<o;++u)i[a=t[u]]||(t[s]=a,e[s]=e[u],++s);n=o-r},bisect:function(t,r){let i;return r?i=r.length:(r=e,i=n),[ue(r,t[0],0,i),ae(r,t[1],0,i)]},reindex:function(e){for(let r=0,i=n;r<i;++r)t[r]=e[t[r]]},index:()=>t,size:()=>n}}function kS(t){Qa.call(this,function(){let t=8,e=[],n=_S(0),r=xS(0,t),i=xS(0,t);return{data:()=>e,seen:()=>n=function(t,e,n){return t.length>=e?t:((n=n||new t.constructor(e)).set(t),n)}(n,e.length),add(t){for(let n,r=0,i=e.length,o=t.length;r<o;++r)n=t[r],n._index=i++,e.push(n)},remove(t,n){const o=e.length,a=Array(o-t),u=e;let s,l,c;for(l=0;!n[l]&&l<o;++l)a[l]=e[l],u[l]=l;for(c=l;l<o;++l)s=e[l],n[l]?u[l]=-1:(u[l]=c,r[c]=r[l],i[c]=i[l],a[c]=s,s._index=c++),r[l]=0;return e=a,u},size:()=>e.length,curr:()=>r,prev:()=>i,reset:t=>i[t]=r[t],all:()=>t<257?255:t<65537?65535:4294967295,set(t,e){r[t]|=e},clear(t,e){r[t]&=~e},resize(e,n){(e>r.length||n>t)&&(t=Math.max(n,t),r=xS(e,t,r),i=xS(e,t))}}}(),t),this._indices=null,this._dims=null}function AS(t){Qa.call(this,null,t)}kS.Definition={type:"CrossFilter",metadata:{},params:[{name:"fields",type:"field",array:!0,required:!0},{name:"query",type:"array",array:!0,required:!0,content:{type:"number",array:!0,length:2}}]},dt(kS,Qa,{transform(t,e){return this._dims?t.modified("fields")||t.fields.some((t=>e.modified(t.fields)))?this.reinit(t,e):this.eval(t,e):this.init(t,e)},init(t,e){const n=t.fields,r=t.query,i=this._indices={},o=this._dims=[],a=r.length;let u,s,l=0;for(;l<a;++l)u=n[l].fname,s=i[u]||(i[u]=wS()),o.push(bS(s,l,r[l]));return this.eval(t,e)},reinit(t,e){const n=e.materialize().fork(),r=t.fields,i=t.query,o=this._indices,a=this._dims,u=this.value,s=u.curr(),l=u.prev(),c=u.all(),f=n.rem=n.add,h=n.mod,d=i.length,p={};let g,m,y,v,_,x,b,w,k;if(l.set(s),e.rem.length&&(_=this.remove(t,e,n)),e.add.length&&u.add(e.add),e.mod.length)for(x={},v=e.mod,b=0,w=v.length;b<w;++b)x[v[b]._index]=1;for(b=0;b<d;++b)k=r[b],(!a[b]||t.modified("fields",b)||e.modified(k.fields))&&(y=k.fname,(g=p[y])||(o[y]=m=wS(),p[y]=g=m.insert(k,e.source,0)),a[b]=bS(m,b,i[b]).onAdd(g,s));for(b=0,w=u.data().length;b<w;++b)_[b]||(l[b]!==s[b]?f.push(b):x[b]&&s[b]!==c&&h.push(b));return u.mask=(1<<d)-1,n},eval(t,e){const n=e.materialize().fork(),r=this._dims.length;let i=0;return e.rem.length&&(this.remove(t,e,n),i|=(1<<r)-1),t.modified("query")&&!t.modified("fields")&&(i|=this.update(t,e,n)),e.add.length&&(this.insert(t,e,n),i|=(1<<r)-1),e.mod.length&&(this.modify(e,n),i|=(1<<r)-1),this.value.mask=i,n},insert(t,e,n){const r=e.add,i=this.value,o=this._dims,a=this._indices,u=t.fields,s={},l=n.add,c=i.size()+r.length,f=o.length;let h,d,p,g=i.size();i.resize(c,f),i.add(r);const m=i.curr(),y=i.prev(),v=i.all();for(h=0;h<f;++h)d=u[h].fname,p=s[d]||(s[d]=a[d].insert(u[h],r,g)),o[h].onAdd(p,m);for(;g<c;++g)y[g]=v,m[g]!==v&&l.push(g)},modify(t,e){const n=e.mod,r=this.value,i=r.curr(),o=r.all(),a=t.mod;let u,s,l;for(u=0,s=a.length;u<s;++u)l=a[u]._index,i[l]!==o&&n.push(l)},remove(t,e,n){const r=this._indices,i=this.value,o=i.curr(),a=i.prev(),u=i.all(),s={},l=n.rem,c=e.rem;let f,h,d,p;for(f=0,h=c.length;f<h;++f)d=c[f]._index,s[d]=1,a[d]=p=o[d],o[d]=u,p!==u&&l.push(d);for(d in r)r[d].remove(h,s);return this.reindex(e,h,s),s},reindex(t,e,n){const r=this._indices,i=this.value;t.runAfter((()=>{const t=i.remove(e,n);for(const e in r)r[e].reindex(t)}))},update(t,e,n){const r=this._dims,i=t.query,o=e.stamp,a=r.length;let u,s,l=0;for(n.filters=0,s=0;s<a;++s)t.modified("query",s)&&(u=s,++l);if(1===l)l=r[u].one,this.incrementOne(r[u],i[u],n.add,n.rem);else for(s=0,l=0;s<a;++s)t.modified("query",s)&&(l|=r[s].one,this.incrementAll(r[s],i[s],o,n.add),n.rem=n.add);return l},incrementAll(t,e,n,r){const i=this.value,o=i.seen(),a=i.curr(),u=i.prev(),s=t.index(),l=t.bisect(t.range),c=t.bisect(e),f=c[0],h=c[1],d=l[0],p=l[1],g=t.one;let m,y,v;if(f<d)for(m=f,y=Math.min(d,h);m<y;++m)v=s[m],o[v]!==n&&(u[v]=a[v],o[v]=n,r.push(v)),a[v]^=g;else if(f>d)for(m=d,y=Math.min(f,p);m<y;++m)v=s[m],o[v]!==n&&(u[v]=a[v],o[v]=n,r.push(v)),a[v]^=g;if(h>p)for(m=Math.max(f,p),y=h;m<y;++m)v=s[m],o[v]!==n&&(u[v]=a[v],o[v]=n,r.push(v)),a[v]^=g;else if(h<p)for(m=Math.max(d,h),y=p;m<y;++m)v=s[m],o[v]!==n&&(u[v]=a[v],o[v]=n,r.push(v)),a[v]^=g;t.range=e.slice()},incrementOne(t,e,n,r){const i=this.value.curr(),o=t.index(),a=t.bisect(t.range),u=t.bisect(e),s=u[0],l=u[1],c=a[0],f=a[1],h=t.one;let d,p,g;if(s<c)for(d=s,p=Math.min(c,l);d<p;++d)g=o[d],i[g]^=h,n.push(g);else if(s>c)for(d=c,p=Math.min(s,f);d<p;++d)g=o[d],i[g]^=h,r.push(g);if(l>f)for(d=Math.max(s,f),p=l;d<p;++d)g=o[d],i[g]^=h,n.push(g);else if(l<f)for(d=Math.max(c,l),p=f;d<p;++d)g=o[d],i[g]^=h,r.push(g);t.range=e.slice()}}),AS.Definition={type:"ResolveFilter",metadata:{},params:[{name:"ignore",type:"number",required:!0,description:"A bit mask indicating which filters to ignore."},{name:"filter",type:"object",required:!0,description:"Per-tuple filter bitmaps from a CrossFilter transform."}]},dt(AS,Qa,{transform(t,e){const n=~(t.ignore||0),r=t.filter,i=r.mask;if(0==(i&n))return e.StopPropagation;const o=e.fork(e.ALL),a=r.data(),u=r.curr(),s=r.prev(),l=t=>u[t]&n?null:a[t];return o.filter(o.MOD,l),i&i-1?(o.filter(o.ADD,(t=>{const e=u[t]&n;return!e&&e^s[t]&n?a[t]:null})),o.filter(o.REM,(t=>{const e=u[t]&n;return e&&!(e^e^s[t]&n)?a[t]:null}))):(o.filter(o.ADD,l),o.filter(o.REM,(t=>(u[t]&n)===i?a[t]:null))),o.filter(o.SOURCE,(t=>l(t._index)))}});var MS=Object.freeze({__proto__:null,crossfilter:kS,resolvefilter:AS});const ES="Literal",DS="Property",CS="ArrayExpression",FS="BinaryExpression",SS="CallExpression",$S="ConditionalExpression",TS="LogicalExpression",BS="MemberExpression",NS="ObjectExpression",zS="UnaryExpression";function OS(t){this.type=t}var RS,US,LS,qS,PS;OS.prototype.visit=function(t){let e,n,r;if(t(this))return 1;for(e=function(t){switch(t.type){case CS:return t.elements;case FS:case TS:return[t.left,t.right];case SS:return[t.callee].concat(t.arguments);case $S:return[t.test,t.consequent,t.alternate];case BS:return[t.object,t.property];case NS:return t.properties;case DS:return[t.key,t.value];case zS:return[t.argument];default:return[]}}(this),n=0,r=e.length;n<r;++n)if(e[n].visit(t))return 1};var jS=1,IS=2,WS=3,HS=4,YS=5,GS=6,VS=7,XS=8;(RS={})[jS]="Boolean",RS[IS]="<end>",RS[WS]="Identifier",RS[HS]="Keyword",RS[YS]="Null",RS[GS]="Numeric",RS[VS]="Punctuator",RS[XS]="String",RS[9]="RegularExpression";var JS="ArrayExpression",ZS="BinaryExpression",QS="CallExpression",KS="ConditionalExpression",t$="Identifier",e$="Literal",n$="LogicalExpression",r$="MemberExpression",i$="ObjectExpression",o$="Property",a$="UnaryExpression",u$="Unexpected token %0",s$="Unexpected number",l$="Unexpected string",c$="Unexpected identifier",f$="Unexpected reserved word",h$="Unexpected end of input",d$="Invalid regular expression",p$="Invalid regular expression: missing /",g$="Octal literals are not allowed in strict mode.",m$="Duplicate data property in object literal not allowed in strict mode",y$="ILLEGAL",v$="Disabled.",_$=new RegExp("[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u08A0-\\u08B2\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58\\u0C59\\u0C60\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D60\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E33\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB0\\u0EB2\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19C1-\\u19C7\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1CE9-\\u1CEC\\u1CEE-\\u1CF1\\u1CF5\\u1CF6\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]"),x$=new RegExp("[\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037A-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u0800-\\u082D\\u0840-\\u085B\\u08A0-\\u08B2\\u08E4-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B56\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C03\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58\\u0C59\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C81-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1\\u0CF2\\u0D01-\\u0D03\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D57\\u0D60-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D82\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E87\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA\\u0EAB\\u0EAD-\\u0EB9\\u0EBB-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECD\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u170C\\u170E-\\u1714\\u1720-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u1810-\\u1819\\u1820-\\u1877\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19D9\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1B00-\\u1B4B\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1CD0-\\u1CD2\\u1CD4-\\u1CF6\\u1CF8\\u1CF9\\u1D00-\\u1DF5\\u1DFC-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u200C\\u200D\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u2E2F\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31BA\\u31F0-\\u31FF\\u3400-\\u4DB5\\u4E00-\\u9FCC\\uA000-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA69D\\uA69F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78E\\uA790-\\uA7AD\\uA7B0\\uA7B1\\uA7F7-\\uA827\\uA840-\\uA873\\uA880-\\uA8C4\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA900-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB5F\\uAB64\\uAB65\\uABC0-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE00-\\uFE0F\\uFE20-\\uFE2D\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]");function b$(t,e){if(!t)throw new Error("ASSERT: "+e)}function w$(t){return t>=48&&t<=57}function k$(t){return"0123456789abcdefABCDEF".indexOf(t)>=0}function A$(t){return"01234567".indexOf(t)>=0}function M$(t){return 32===t||9===t||11===t||12===t||160===t||t>=5760&&[5760,6158,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(t)>=0}function E$(t){return 10===t||13===t||8232===t||8233===t}function D$(t){return 36===t||95===t||t>=65&&t<=90||t>=97&&t<=122||92===t||t>=128&&_$.test(String.fromCharCode(t))}function C$(t){return 36===t||95===t||t>=65&&t<=90||t>=97&&t<=122||t>=48&&t<=57||92===t||t>=128&&x$.test(String.fromCharCode(t))}const F$={if:1,in:1,do:1,var:1,for:1,new:1,try:1,let:1,this:1,else:1,case:1,void:1,with:1,enum:1,while:1,break:1,catch:1,throw:1,const:1,yield:1,class:1,super:1,return:1,typeof:1,delete:1,switch:1,export:1,import:1,public:1,static:1,default:1,finally:1,extends:1,package:1,private:1,function:1,continue:1,debugger:1,interface:1,protected:1,instanceof:1,implements:1};function S$(){for(;LS<qS;){const t=US.charCodeAt(LS);if(!M$(t)&&!E$(t))break;++LS}}function $$(t){var e,n,r,i=0;for(n="u"===t?4:2,e=0;e<n;++e)LS<qS&&k$(US[LS])?(r=US[LS++],i=16*i+"0123456789abcdef".indexOf(r.toLowerCase())):G$({},u$,y$);return String.fromCharCode(i)}function T$(){var t,e,n,r;for(e=0,"}"===(t=US[LS])&&G$({},u$,y$);LS<qS&&k$(t=US[LS++]);)e=16*e+"0123456789abcdef".indexOf(t.toLowerCase());return(e>1114111||"}"!==t)&&G$({},u$,y$),e<=65535?String.fromCharCode(e):(n=55296+(e-65536>>10),r=56320+(e-65536&1023),String.fromCharCode(n,r))}function B$(){var t,e;for(t=US.charCodeAt(LS++),e=String.fromCharCode(t),92===t&&(117!==US.charCodeAt(LS)&&G$({},u$,y$),++LS,(t=$$("u"))&&"\\"!==t&&D$(t.charCodeAt(0))||G$({},u$,y$),e=t);LS<qS&&C$(t=US.charCodeAt(LS));)++LS,e+=String.fromCharCode(t),92===t&&(e=e.substr(0,e.length-1),117!==US.charCodeAt(LS)&&G$({},u$,y$),++LS,(t=$$("u"))&&"\\"!==t&&C$(t.charCodeAt(0))||G$({},u$,y$),e+=t);return e}function N$(){var t,e;return t=LS,e=92===US.charCodeAt(LS)?B$():function(){var t,e;for(t=LS++;LS<qS;){if(92===(e=US.charCodeAt(LS)))return LS=t,B$();if(!C$(e))break;++LS}return US.slice(t,LS)}(),{type:1===e.length?WS:F$.hasOwnProperty(e)?HS:"null"===e?YS:"true"===e||"false"===e?jS:WS,value:e,start:t,end:LS}}function z$(){var t,e,n,r,i=LS,o=US.charCodeAt(LS),a=US[LS];switch(o){case 46:case 40:case 41:case 59:case 44:case 123:case 125:case 91:case 93:case 58:case 63:case 126:return++LS,{type:VS,value:String.fromCharCode(o),start:i,end:LS};default:if(61===(t=US.charCodeAt(LS+1)))switch(o){case 43:case 45:case 47:case 60:case 62:case 94:case 124:case 37:case 38:case 42:return LS+=2,{type:VS,value:String.fromCharCode(o)+String.fromCharCode(t),start:i,end:LS};case 33:case 61:return LS+=2,61===US.charCodeAt(LS)&&++LS,{type:VS,value:US.slice(i,LS),start:i,end:LS}}}return">>>="===(r=US.substr(LS,4))?{type:VS,value:r,start:i,end:LS+=4}:">>>"===(n=r.substr(0,3))||"<<="===n||">>="===n?{type:VS,value:n,start:i,end:LS+=3}:a===(e=n.substr(0,2))[1]&&"+-<>&|".indexOf(a)>=0||"=>"===e?{type:VS,value:e,start:i,end:LS+=2}:("//"===e&&G$({},u$,y$),"<>=!+-*%&|^/".indexOf(a)>=0?(++LS,{type:VS,value:a,start:i,end:LS}):void G$({},u$,y$))}function O$(){var t,e,n;if(b$(w$((n=US[LS]).charCodeAt(0))||"."===n,"Numeric literal must start with a decimal digit or a decimal point"),e=LS,t="","."!==n){if(t=US[LS++],n=US[LS],"0"===t){if("x"===n||"X"===n)return++LS,function(t){let e="";for(;LS<qS&&k$(US[LS]);)e+=US[LS++];return 0===e.length&&G$({},u$,y$),D$(US.charCodeAt(LS))&&G$({},u$,y$),{type:GS,value:parseInt("0x"+e,16),start:t,end:LS}}(e);if(A$(n))return function(t){let e="0"+US[LS++];for(;LS<qS&&A$(US[LS]);)e+=US[LS++];return(D$(US.charCodeAt(LS))||w$(US.charCodeAt(LS)))&&G$({},u$,y$),{type:GS,value:parseInt(e,8),octal:!0,start:t,end:LS}}(e);n&&w$(n.charCodeAt(0))&&G$({},u$,y$)}for(;w$(US.charCodeAt(LS));)t+=US[LS++];n=US[LS]}if("."===n){for(t+=US[LS++];w$(US.charCodeAt(LS));)t+=US[LS++];n=US[LS]}if("e"===n||"E"===n)if(t+=US[LS++],"+"!==(n=US[LS])&&"-"!==n||(t+=US[LS++]),w$(US.charCodeAt(LS)))for(;w$(US.charCodeAt(LS));)t+=US[LS++];else G$({},u$,y$);return D$(US.charCodeAt(LS))&&G$({},u$,y$),{type:GS,value:parseFloat(t),start:e,end:LS}}function R$(){var t,e,n,r;return PS=null,S$(),t=LS,e=function(){var t,e,n,r;for(b$("/"===(t=US[LS]),"Regular expression literal must start with a slash"),e=US[LS++],n=!1,r=!1;LS<qS;)if(e+=t=US[LS++],"\\"===t)E$((t=US[LS++]).charCodeAt(0))&&G$({},p$),e+=t;else if(E$(t.charCodeAt(0)))G$({},p$);else if(n)"]"===t&&(n=!1);else{if("/"===t){r=!0;break}"["===t&&(n=!0)}return r||G$({},p$),{value:e.substr(1,e.length-2),literal:e}}(),n=function(){var t,e,n;for(e="",n="";LS<qS&&C$((t=US[LS]).charCodeAt(0));)++LS,"\\"===t&&LS<qS?G$({},u$,y$):(n+=t,e+=t);return n.search(/[^gimuy]/g)>=0&&G$({},d$,n),{value:n,literal:e}}(),r=function(t,e){let n=t;e.indexOf("u")>=0&&(n=n.replace(/\\u\{([0-9a-fA-F]+)\}/g,((t,e)=>{if(parseInt(e,16)<=1114111)return"x";G$({},d$)})).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"x"));try{new RegExp(n)}catch(t){G$({},d$)}try{return new RegExp(t,e)}catch(t){return null}}(e.value,n.value),{literal:e.literal+n.literal,value:r,regex:{pattern:e.value,flags:n.value},start:t,end:LS}}function U$(){if(S$(),LS>=qS)return{type:IS,start:LS,end:LS};const t=US.charCodeAt(LS);return D$(t)?N$():40===t||41===t||59===t?z$():39===t||34===t?function(){var t,e,n,r,i="",o=!1;for(b$("'"===(t=US[LS])||'"'===t,"String literal must starts with a quote"),e=LS,++LS;LS<qS;){if((n=US[LS++])===t){t="";break}if("\\"===n)if((n=US[LS++])&&E$(n.charCodeAt(0)))"\r"===n&&"\n"===US[LS]&&++LS;else switch(n){case"u":case"x":"{"===US[LS]?(++LS,i+=T$()):i+=$$(n);break;case"n":i+="\n";break;case"r":i+="\r";break;case"t":i+="\t";break;case"b":i+="\b";break;case"f":i+="\f";break;case"v":i+="\v";break;default:A$(n)?(0!==(r="01234567".indexOf(n))&&(o=!0),LS<qS&&A$(US[LS])&&(o=!0,r=8*r+"01234567".indexOf(US[LS++]),"0123".indexOf(n)>=0&&LS<qS&&A$(US[LS])&&(r=8*r+"01234567".indexOf(US[LS++]))),i+=String.fromCharCode(r)):i+=n}else{if(E$(n.charCodeAt(0)))break;i+=n}}return""!==t&&G$({},u$,y$),{type:XS,value:i,octal:o,start:e,end:LS}}():46===t?w$(US.charCodeAt(LS+1))?O$():z$():w$(t)?O$():z$()}function L$(){const t=PS;return LS=t.end,PS=U$(),LS=t.end,t}function q$(){const t=LS;PS=U$(),LS=t}function P$(t,e,n){const r=new OS("||"===t||"&&"===t?n$:ZS);return r.operator=t,r.left=e,r.right=n,r}function j$(t,e){const n=new OS(QS);return n.callee=t,n.arguments=e,n}function I$(t){const e=new OS(t$);return e.name=t,e}function W$(t){const e=new OS(e$);return e.value=t.value,e.raw=US.slice(t.start,t.end),t.regex&&("//"===e.raw&&(e.raw="/(?:)/"),e.regex=t.regex),e}function H$(t,e,n){const r=new OS(r$);return r.computed="["===t,r.object=e,r.property=n,r.computed||(n.member=!0),r}function Y$(t,e,n){const r=new OS(o$);return r.key=e,r.value=n,r.kind=t,r}function G$(t,e){var n,r=Array.prototype.slice.call(arguments,2),i=e.replace(/%(\d)/g,((t,e)=>(b$(e<r.length,"Message reference must be in range"),r[e])));throw(n=new Error(i)).index=LS,n.description=i,n}function V$(t){t.type===IS&&G$(t,h$),t.type===GS&&G$(t,s$),t.type===XS&&G$(t,l$),t.type===WS&&G$(t,c$),t.type===HS&&G$(t,f$),G$(t,u$,t.value)}function X$(t){const e=L$();e.type===VS&&e.value===t||V$(e)}function J$(t){return PS.type===VS&&PS.value===t}function Z$(t){return PS.type===HS&&PS.value===t}function Q$(){const t=[];for(LS=PS.start,X$("[");!J$("]");)J$(",")?(L$(),t.push(null)):(t.push(cT()),J$("]")||X$(","));return L$(),function(t){const e=new OS(JS);return e.elements=t,e}(t)}function K$(){LS=PS.start;const t=L$();return t.type===XS||t.type===GS?(t.octal&&G$(t,g$),W$(t)):I$(t.value)}function tT(){var t,e,n;return LS=PS.start,(t=PS).type===WS?(n=K$(),X$(":"),Y$("init",n,cT())):t.type!==IS&&t.type!==VS?(e=K$(),X$(":"),Y$("init",e,cT())):void V$(t)}function eT(){var t,e,n=[],r={},i=String;for(LS=PS.start,X$("{");!J$("}");)e="$"+((t=tT()).key.type===t$?t.key.name:i(t.key.value)),Object.prototype.hasOwnProperty.call(r,e)?G$({},m$):r[e]=!0,n.push(t),J$("}")||X$(",");return X$("}"),function(t){const e=new OS(i$);return e.properties=t,e}(n)}const nT={if:1};function rT(){var t,e,n;if(J$("("))return function(){X$("(");const t=fT();return X$(")"),t}();if(J$("["))return Q$();if(J$("{"))return eT();if(t=PS.type,LS=PS.start,t===WS||nT[PS.value])n=I$(L$().value);else if(t===XS||t===GS)PS.octal&&G$(PS,g$),n=W$(L$());else{if(t===HS)throw new Error(v$);t===jS?((e=L$()).value="true"===e.value,n=W$(e)):t===YS?((e=L$()).value=null,n=W$(e)):J$("/")||J$("/=")?(n=W$(R$()),q$()):V$(L$())}return n}function iT(){const t=[];if(X$("("),!J$(")"))for(;LS<qS&&(t.push(cT()),!J$(")"));)X$(",");return X$(")"),t}function oT(){LS=PS.start;const t=L$();return function(t){return t.type===WS||t.type===HS||t.type===jS||t.type===YS}(t)||V$(t),I$(t.value)}function aT(){X$("[");const t=fT();return X$("]"),t}function uT(){const t=function(){var t;for(t=rT();;)if(J$("."))X$("."),t=H$(".",t,oT());else if(J$("("))t=j$(t,iT());else{if(!J$("["))break;t=H$("[",t,aT())}return t}();if(PS.type===VS&&(J$("++")||J$("--")))throw new Error(v$);return t}function sT(){var t,e;if(PS.type!==VS&&PS.type!==HS)e=uT();else{if(J$("++")||J$("--"))throw new Error(v$);if(J$("+")||J$("-")||J$("~")||J$("!"))t=L$(),e=sT(),e=function(t,e){const n=new OS(a$);return n.operator=t,n.argument=e,n.prefix=!0,n}(t.value,e);else{if(Z$("delete")||Z$("void")||Z$("typeof"))throw new Error(v$);e=uT()}}return e}function lT(t){let e=0;if(t.type!==VS&&t.type!==HS)return 0;switch(t.value){case"||":e=1;break;case"&&":e=2;break;case"|":e=3;break;case"^":e=4;break;case"&":e=5;break;case"==":case"!=":case"===":case"!==":e=6;break;case"<":case">":case"<=":case">=":case"instanceof":case"in":e=7;break;case"<<":case">>":case">>>":e=8;break;case"+":case"-":e=9;break;case"*":case"/":case"%":e=11}return e}function cT(){var t,e;return t=function(){var t,e,n,r,i,o,a,u,s,l;if(t=PS,s=sT(),0===(i=lT(r=PS)))return s;for(r.prec=i,L$(),e=[t,PS],o=[s,r,a=sT()];(i=lT(PS))>0;){for(;o.length>2&&i<=o[o.length-2].prec;)a=o.pop(),u=o.pop().value,s=o.pop(),e.pop(),n=P$(u,s,a),o.push(n);(r=L$()).prec=i,o.push(r),e.push(PS),n=sT(),o.push(n)}for(n=o[l=o.length-1],e.pop();l>1;)e.pop(),n=P$(o[l-1].value,o[l-2],n),l-=2;return n}(),J$("?")&&(L$(),e=cT(),X$(":"),t=function(t,e,n){const r=new OS(KS);return r.test=t,r.consequent=e,r.alternate=n,r}(t,e,cT())),t}function fT(){const t=cT();if(J$(","))throw new Error(v$);return t}function hT(t){LS=0,qS=(US=t).length,PS=null,q$();const e=fT();if(PS.type!==IS)throw new Error("Unexpect token after expression.");return e}var dT={NaN:"NaN",E:"Math.E",LN2:"Math.LN2",LN10:"Math.LN10",LOG2E:"Math.LOG2E",LOG10E:"Math.LOG10E",PI:"Math.PI",SQRT1_2:"Math.SQRT1_2",SQRT2:"Math.SQRT2",MIN_VALUE:"Number.MIN_VALUE",MAX_VALUE:"Number.MAX_VALUE"};function pT(t){function e(e,n,r){return i=>function(e,n,r,i){let o=t(n[0]);return r&&(o=r+"("+o+")",0===r.lastIndexOf("new ",0)&&(o="("+o+")")),o+"."+e+(i<0?"":0===i?"()":"("+n.slice(1).map(t).join(",")+")")}(e,i,n,r)}const n="new Date",r="String",i="RegExp";return{isNaN:"Number.isNaN",isFinite:"Number.isFinite",abs:"Math.abs",acos:"Math.acos",asin:"Math.asin",atan:"Math.atan",atan2:"Math.atan2",ceil:"Math.ceil",cos:"Math.cos",exp:"Math.exp",floor:"Math.floor",hypot:"Math.hypot",log:"Math.log",max:"Math.max",min:"Math.min",pow:"Math.pow",random:"Math.random",round:"Math.round",sin:"Math.sin",sqrt:"Math.sqrt",tan:"Math.tan",clamp:function(e){e.length<3&&u("Missing arguments to clamp function."),e.length>3&&u("Too many arguments to clamp function.");const n=e.map(t);return"Math.max("+n[1]+", Math.min("+n[2]+","+n[0]+"))"},now:"Date.now",utc:"Date.UTC",datetime:n,date:e("getDate",n,0),day:e("getDay",n,0),year:e("getFullYear",n,0),month:e("getMonth",n,0),hours:e("getHours",n,0),minutes:e("getMinutes",n,0),seconds:e("getSeconds",n,0),milliseconds:e("getMilliseconds",n,0),time:e("getTime",n,0),timezoneoffset:e("getTimezoneOffset",n,0),utcdate:e("getUTCDate",n,0),utcday:e("getUTCDay",n,0),utcyear:e("getUTCFullYear",n,0),utcmonth:e("getUTCMonth",n,0),utchours:e("getUTCHours",n,0),utcminutes:e("getUTCMinutes",n,0),utcseconds:e("getUTCSeconds",n,0),utcmilliseconds:e("getUTCMilliseconds",n,0),length:e("length",null,-1),parseFloat:"parseFloat",parseInt:"parseInt",upper:e("toUpperCase",r,0),lower:e("toLowerCase",r,0),substring:e("substring",r),split:e("split",r),trim:e("trim",r,0),regexp:i,test:e("test",i),if:function(e){e.length<3&&u("Missing arguments to if function."),e.length>3&&u("Too many arguments to if function.");const n=e.map(t);return"("+n[0]+"?"+n[1]+":"+n[2]+")"}}}function gT(t){const e=(t=t||{}).allowed?Nt(t.allowed):{},n=t.forbidden?Nt(t.forbidden):{},r=t.constants||dT,i=(t.functions||pT)(h),o=t.globalvar,a=t.fieldvar,s=J(o)?o:t=>`${o}["${t}"]`;let l={},c={},f=0;function h(t){if(xt(t))return t;const e=d[t.type];return null==e&&u("Unsupported type: "+t.type),e(t)}const d={Literal:t=>t.raw,Identifier:t=>{const i=t.name;return f>0?i:lt(n,i)?u("Illegal identifier: "+i):lt(r,i)?r[i]:lt(e,i)?i:(l[i]=1,s(i))},MemberExpression:t=>{const e=!t.computed,n=h(t.object);e&&(f+=1);const r=h(t.property);return n===a&&(c[function(t){const e=t&&t.length-1;return e&&('"'===t[0]&&'"'===t[e]||"'"===t[0]&&"'"===t[e])?t.slice(1,-1):t}(r)]=1),e&&(f-=1),n+(e?"."+r:"["+r+"]")},CallExpression:t=>{"Identifier"!==t.callee.type&&u("Illegal callee type: "+t.callee.type);const e=t.callee.name,n=t.arguments,r=lt(i,e)&&i[e];return r||u("Unrecognized function: "+e),J(r)?r(n):r+"("+n.map(h).join(",")+")"},ArrayExpression:t=>"["+t.elements.map(h).join(",")+"]",BinaryExpression:t=>"("+h(t.left)+" "+t.operator+" "+h(t.right)+")",UnaryExpression:t=>"("+t.operator+h(t.argument)+")",ConditionalExpression:t=>"("+h(t.test)+"?"+h(t.consequent)+":"+h(t.alternate)+")",LogicalExpression:t=>"("+h(t.left)+t.operator+h(t.right)+")",ObjectExpression:t=>"{"+t.properties.map(h).join(",")+"}",Property:t=>{f+=1;const e=h(t.key);return f-=1,e+":"+h(t.value)}};function p(t){const e={code:h(t),globals:Object.keys(l),fields:Object.keys(c)};return l={},c={},e}return p.functions=i,p.constants=r,p}const mT="intersect",yT="union",vT="vlMulti",_T="vlPoint",xT="or",bT="and",wT="_vgsid_",kT=l(wT),AT="E",MT="R",ET="R-E",DT="R-LE",CT="R-RE",FT="index:unit";function ST(t,e){for(var n,r,i=e.fields,o=e.values,a=i.length,u=0;u<a;++u)if((r=i[u]).getter=l.getter||l(r.field),mt(n=r.getter(t))&&(n=S(n)),mt(o[u])&&(o[u]=S(o[u])),mt(o[u][0])&&(o[u]=o[u].map(S)),r.type===AT){if(k(o[u])?o[u].indexOf(n)<0:n!==o[u])return!1}else if(r.type===MT){if(!pt(n,o[u]))return!1}else if(r.type===CT){if(!pt(n,o[u],!0,!1))return!1}else if(r.type===ET){if(!pt(n,o[u],!1,!1))return!1}else if(r.type===DT&&!pt(n,o[u],!1,!0))return!1;return!0}const $T=ne(kT),TT=$T.left,BT=$T.right;var NT={[`${wT}_union`]:function(){const t=new fe;for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];for(const e of n)for(const n of e)t.add(n);return t},[`${wT}_intersect`]:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];t=new fe(t),n=n.map(Ne);t:for(const e of t)for(const r of n)if(!r.has(e)){t.delete(e);continue t}return t},E_union:function(t,e){if(!t.length)return e;for(var n=0,r=e.length;n<r;++n)t.indexOf(e[n])<0&&t.push(e[n]);return t},E_intersect:function(t,e){return t.length?t.filter((t=>e.indexOf(t)>=0)):e},R_union:function(t,e){var n=S(e[0]),r=S(e[1]);return n>r&&(n=e[1],r=e[0]),t.length?(t[0]>n&&(t[0]=n),t[1]<r&&(t[1]=r),t):[n,r]},R_intersect:function(t,e){var n=S(e[0]),r=S(e[1]);return n>r&&(n=e[1],r=e[0]),t.length?r<t[0]||t[1]<n?[]:(t[0]<n&&(t[0]=n),t[1]>r&&(t[1]=r),t):[n,r]}};const zT=":",OT="@";function RT(t,e,n,r){e[0].type!==ES&&u("First argument to selection functions must be a string literal.");const i=e[0].value,o=e.length>=2&&F(e).value,a="unit",s=OT+a,l=zT+i;o!==mT||lt(r,s)||(r[s]=n.getData(i).indataRef(n,a)),lt(r,l)||(r[l]=n.getData(i).tuplesRef())}function UT(t){const e=this.context.data[t];return e?e.values.value:[]}const LT=t=>function(e,n){return this.context.dataflow.locale()[t](n)(e)},qT=LT("format"),PT=LT("timeFormat"),jT=LT("utcFormat"),IT=LT("timeParse"),WT=LT("utcParse"),HT=new Date(2e3,0,1);function YT(t,e,n){return Number.isInteger(t)&&Number.isInteger(e)?(HT.setYear(2e3),HT.setMonth(t),HT.setDate(e),PT.call(this,HT,n)):""}const GT=":",VT="@",XT="%",JT="$";function ZT(t,e,n,r){e[0].type!==ES&&u("First argument to data functions must be a string literal.");const i=e[0].value,o=GT+i;if(!lt(o,r))try{r[o]=n.getData(i).tuplesRef()}catch(t){}}function QT(t,e,n,r){if(e[0].type===ES)KT(n,r,e[0].value);else for(t in n.scales)KT(n,r,t)}function KT(t,e,n){const r=XT+n;if(!lt(e,r))try{e[r]=t.scaleRef(n)}catch(t){}}function tB(t,e){if(J(t))return t;if(xt(t)){const n=e.scales[t];return n&&function(t){return t&&!0===t[tp]}(n.value)?n.value:void 0}}function eB(t,e,n){e.__bandwidth=t=>t&&t.bandwidth?t.bandwidth():0,n._bandwidth=QT,n._range=QT,n._scale=QT;const r=e=>"_["+(e.type===ES?Ft(XT+e.value):Ft(XT)+"+"+t(e))+"]";return{_bandwidth:t=>`this.__bandwidth(${r(t[0])})`,_range:t=>`${r(t[0])}.range()`,_scale:e=>`${r(e[0])}(${t(e[1])})`}}function nB(t,e){return function(n,r,i){if(n){const e=tB(n,(i||this).context);return e&&e.path[t](r)}return e(r)}}const rB=nB("area",(function(t){return bw=new le,rw(t,ww),2*bw})),iB=nB("bounds",(function(t){var e,n,r,i,o,a,u;if(hw=fw=-(lw=cw=1/0),vw=[],rw(t,Jw),n=vw.length){for(vw.sort(ok),e=1,o=[r=vw[0]];e<n;++e)ak(r,(i=vw[e])[0])||ak(r,i[1])?(ik(r[0],i[1])>ik(r[0],r[1])&&(r[1]=i[1]),ik(i[0],r[1])>ik(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(a=-1/0,e=0,r=o[n=o.length-1];e<=n;r=i,++e)i=o[e],(u=ik(r[1],i[0]))>a&&(a=u,lw=i[0],fw=r[1])}return vw=_w=null,lw===1/0||cw===1/0?[[NaN,NaN],[NaN,NaN]]:[[lw,cw],[fw,hw]]})),oB=nB("centroid",(function(t){Nw=zw=Ow=Rw=Uw=Lw=qw=Pw=0,jw=new le,Iw=new le,Ww=new le,rw(t,uk);var e=+jw,n=+Iw,r=+Ww,i=jb(e,n,r);return i<Fb&&(e=Lw,n=qw,r=Pw,zw<Cb&&(e=Ow,n=Rw,r=Uw),(i=jb(e,n,r))<Fb)?[NaN,NaN]:[Ub(n,e)*Nb,Jb(r/i)*Nb]}));function aB(t,e,n){try{t[e].apply(t,["EXPRESSION"].concat([].slice.call(n)))}catch(e){t.warn(e)}return n[n.length-1]}function uB(t){const e=t/255;return e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)}function sB(t){const e=hf(t);return.2126*uB(e.r)+.7152*uB(e.g)+.0722*uB(e.b)}function lB(t,e){return t===e||t!=t&&e!=e||(k(t)?!(!k(e)||t.length!==e.length)&&function(t,e){for(let n=0,r=t.length;n<r;++n)if(!lB(t[n],e[n]))return!1;return!0}(t,e):!(!A(t)||!A(e))&&cB(t,e))}function cB(t,e){for(const n in t)if(!lB(t[n],e[n]))return!1;return!0}function fB(t){return e=>cB(t,e)}const hB={};function dB(t){return k(t)||ArrayBuffer.isView(t)?t:null}function pB(t){return dB(t)||(xt(t)?t:null)}const gB=t=>t.data;function mB(t,e){const n=UT.call(e,t);return n.root&&n.root.lookup||{}}const yB=()=>"undefined"!=typeof window&&window||null;function vB(t,e,n){if(!t)return[];const[r,i]=t,o=(new Hg).set(r[0],r[1],i[0],i[1]);return w_(n||this.context.dataflow.scenegraph().root,o,function(t){let e=null;if(t){const n=V(t.marktype),r=V(t.markname);e=t=>(!n.length||n.some((e=>t.marktype===e)))&&(!r.length||r.some((e=>t.name===e)))}return e}(e))}const _B={random:()=>t.random(),cumulativeNormal:pu,cumulativeLogNormal:xu,cumulativeUniform:Eu,densityNormal:du,densityLogNormal:_u,densityUniform:Mu,quantileNormal:gu,quantileLogNormal:bu,quantileUniform:Du,sampleNormal:hu,sampleLogNormal:vu,sampleUniform:Au,isArray:k,isBoolean:gt,isDate:mt,isDefined:t=>void 0!==t,isNumber:vt,isObject:A,isRegExp:_t,isString:xt,isTuple:va,isValid:t=>null!=t&&t==t,toBoolean:St,toDate:t=>Tt(t),toNumber:S,toString:Bt,indexof:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return pB(t).indexOf(...n)},join:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return dB(t).join(...n)},lastindexof:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return pB(t).lastIndexOf(...n)},replace:function(t,e,n){return J(n)&&u("Function argument passed to replace."),String(t).replace(e,n)},reverse:function(t){return dB(t).slice().reverse()},slice:function(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return pB(t).slice(...n)},flush:ht,lerp:wt,merge:function(){const t=[].slice.call(arguments);return t.unshift({}),ot(...t)},pad:Dt,peek:F,pluck:function(t,e){const n=hB[e]||(hB[e]=l(e));return k(t)?t.map(n):n(t)},span:Ct,inrange:pt,truncate:zt,rgb:hf,lab:Of,hcl:jf,hsl:bf,luminance:sB,contrast:function(t,e){const n=sB(t),r=sB(e);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)},sequence:Te,format:qT,utcFormat:jT,utcParse:WT,utcOffset:Nr,utcSequence:Rr,timeFormat:PT,timeParse:IT,timeOffset:Br,timeSequence:Or,timeUnitSpecifier:or,monthFormat:function(t){return YT.call(this,t,1,"%B")},monthAbbrevFormat:function(t){return YT.call(this,t,1,"%b")},dayFormat:function(t){return YT.call(this,0,2+t,"%A")},dayAbbrevFormat:function(t){return YT.call(this,0,2+t,"%a")},quarter:Y,utcquarter:G,week:lr,utcweek:gr,dayofyear:sr,utcdayofyear:pr,warn:function(){return aB(this.context.dataflow,"warn",arguments)},info:function(){return aB(this.context.dataflow,"info",arguments)},debug:function(){return aB(this.context.dataflow,"debug",arguments)},extent:t=>at(t),inScope:function(t){const e=this.context.group;let n=!1;if(e)for(;t;){if(t===e){n=!0;break}t=t.mark.group}return n},intersect:vB,clampRange:X,pinchDistance:function(t){const e=t.touches,n=e[0].clientX-e[1].clientX,r=e[0].clientY-e[1].clientY;return Math.hypot(n,r)},pinchAngle:function(t){const e=t.touches;return Math.atan2(e[0].clientY-e[1].clientY,e[0].clientX-e[1].clientX)},screen:function(){const t=yB();return t?t.screen:{}},containerSize:function(){const t=this.context.dataflow,e=t.container&&t.container();return e?[e.clientWidth,e.clientHeight]:[void 0,void 0]},windowSize:function(){const t=yB();return t?[t.innerWidth,t.innerHeight]:[void 0,void 0]},bandspace:function(t,e,n){return Dd(t||0,e||0,n||0)},setdata:function(t,e){const n=this.context.dataflow,r=this.context.data[t].input;return n.pulse(r,n.changeset().remove(p).insert(e)),1},pathShape:function(t){let e=null;return function(n){return n?pg(n,e=e||ng(t)):t}},panLinear:R,panLog:U,panPow:L,panSymlog:q,zoomLinear:j,zoomLog:I,zoomPow:W,zoomSymlog:H,encode:function(t,e,n){if(t){const n=this.context.dataflow,r=t.mark.source;n.pulse(r,n.changeset().encode(t,e))}return void 0!==n?n:t},modify:function(t,e,n,r,i,o){const a=this.context.dataflow,u=this.context.data[t],s=u.input,l=a.stamp();let c,f,h=u.changes;if(!1===a._trigger||!(s.value.length||e||r))return 0;if((!h||h.stamp<l)&&(u.changes=h=a.changeset(),h.stamp=l,a.runAfter((()=>{u.modified=!0,a.pulse(s,h).run()}),!0,1)),n&&(c=!0===n?p:k(n)||va(n)?n:fB(n),h.remove(c)),e&&h.insert(e),r&&(c=fB(r),s.value.some(c)?h.remove(c):h.insert(r)),i)for(f in o)h.modify(i,f,o[f]);return 1},lassoAppend:function(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:5;const i=(t=V(t))[t.length-1];return void 0===i||Math.hypot(i[0]-e,i[1]-n)>r?[...t,[e,n]]:t},lassoPath:function(t){return V(t).reduce(((e,n,r)=>{let[i,o]=n;return e+(0==r?`M ${i},${o} `:r===t.length-1?" Z":`L ${i},${o} `)}),"")},intersectLasso:function(t,e,n){const{x:r,y:i,mark:o}=n,a=(new Hg).set(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MIN_SAFE_INTEGER,Number.MIN_SAFE_INTEGER);for(const[t,n]of e)t<a.x1&&(a.x1=t),t>a.x2&&(a.x2=t),n<a.y1&&(a.y1=n),n>a.y2&&(a.y2=n);return a.translate(r,i),vB([[a.x1,a.y1],[a.x2,a.y2]],t,o).filter((t=>function(t,e,n){let r=0;for(let i=0,o=n.length-1;i<n.length;o=i++){const[a,u]=n[o],[s,l]=n[i];l>e!=u>e&&t<(a-s)*(e-l)/(u-l)+s&&r++}return 1&r}(t.x,t.y,e)))}},xB=["view","item","group","xy","x","y"],bB="event.vega.",wB="this.",kB={},AB={forbidden:["_"],allowed:["datum","event","item"],fieldvar:"datum",globalvar:t=>`_[${Ft(JT+t)}]`,functions:function(t){const e=pT(t);xB.forEach((t=>e[t]=bB+t));for(const t in _B)e[t]=wB+t;return ot(e,eB(t,_B,kB)),e},constants:dT,visitors:kB},MB=gT(AB);function EB(t,e,n){return 1===arguments.length?_B[t]:(_B[t]=e,n&&(kB[t]=n),MB&&(MB.functions[t]=wB+t),this)}function DB(t,e){const n={};let r;try{r=hT(t=xt(t)?t:Ft(t)+"")}catch(e){u("Expression parse error: "+t)}r.visit((t=>{if(t.type!==SS)return;const r=t.callee.name,i=AB.visitors[r];i&&i(r,t.arguments,e,n)}));const i=MB(r);return i.globals.forEach((t=>{const r=JT+t;!lt(n,r)&&e.getSignal(t)&&(n[r]=e.signalRef(t))})),{$expr:ot({code:i.code},e.options.ast?{ast:r}:null),$fields:i.fields,$params:n}}EB("bandwidth",(function(t,e){const n=tB(t,(e||this).context);return n&&n.bandwidth?n.bandwidth():0}),QT),EB("copy",(function(t,e){const n=tB(t,(e||this).context);return n?n.copy():void 0}),QT),EB("domain",(function(t,e){const n=tB(t,(e||this).context);return n?n.domain():[]}),QT),EB("range",(function(t,e){const n=tB(t,(e||this).context);return n&&n.range?n.range():[]}),QT),EB("invert",(function(t,e,n){const r=tB(t,(n||this).context);return r?k(e)?(r.invertRange||r.invert)(e):(r.invert||r.invertExtent)(e):void 0}),QT),EB("scale",(function(t,e,n){const r=tB(t,(n||this).context);return r?r(e):void 0}),QT),EB("gradient",(function(t,e,n,r,i){t=tB(t,(i||this).context);const o=Vp(e,n);let a=t.domain(),u=a[0],s=F(a),l=f;return s-u?l=gp(t,u,s):t=(t.interpolator?np("sequential")().interpolator(t.interpolator()):np("linear")().interpolate(t.interpolate()).range(t.range())).domain([u=0,s=1]),t.ticks&&(a=t.ticks(+r||15),u!==a[0]&&a.unshift(u),s!==F(a)&&a.push(s)),a.forEach((e=>o.stop(l(e),t(e)))),o}),QT),EB("geoArea",rB,QT),EB("geoBounds",iB,QT),EB("geoCentroid",oB,QT),EB("geoShape",(function(t,e,n){const r=tB(t,(n||this).context);return function(t){return r?r.path.context(t)(e):""}}),QT),EB("indata",(function(t,e,n){const r=this.context.data[t]["index:"+e],i=r?r.value.get(n):void 0;return i?i.count:i}),(function(t,e,n,r){e[0].type!==ES&&u("First argument to indata must be a string literal."),e[1].type!==ES&&u("Second argument to indata must be a string literal.");const i=e[0].value,o=e[1].value,a=VT+o;lt(a,r)||(r[a]=n.getData(i).indataRef(n,o))})),EB("data",UT,ZT),EB("treePath",(function(t,e,n){const r=mB(t,this),i=r[e],o=r[n];return i&&o?i.path(o).map(gB):void 0}),ZT),EB("treeAncestors",(function(t,e){const n=mB(t,this)[e];return n?n.ancestors().map(gB):void 0}),ZT),EB("vlSelectionTest",(function(t,e,n){for(var r,i,o,a,u,s=this.context.data[t],l=s?s.values.value:[],c=s?s[FT]&&s[FT].value:void 0,f=n===mT,h=l.length,d=0;d<h;++d)if(r=l[d],c&&f){if(-1===(o=(i=i||{})[a=r.unit]||0))continue;if(u=ST(e,r),i[a]=u?-1:++o,u&&1===c.size)return!0;if(!u&&o===c.get(a).count)return!1}else if(f^(u=ST(e,r)))return u;return h&&f}),RT),EB("vlSelectionIdTest",(function(t,e,n){const r=this.context.data[t],i=r?r.values.value:[],o=r?r[FT]&&r[FT].value:void 0,a=n===mT,u=kT(e),s=TT(i,u);if(s===i.length)return!1;if(kT(i[s])!==u)return!1;if(o&&a){if(1===o.size)return!0;if(BT(i,u)-s<o.size)return!1}return!0}),RT),EB("vlSelectionResolve",(function(t,e,n,r){for(var i,o,a,u,s,l,c,f,h,d,p,g,m=this.context.data[t],y=m?m.values.value:[],v={},_={},x={},b=y.length,w=0;w<b;++w)if(u=(i=y[w]).unit,o=i.fields,a=i.values,o&&a){for(p=0,g=o.length;p<g;++p)s=o[p],f=(c=v[s.field]||(v[s.field]={}))[u]||(c[u]=[]),x[s.field]=h=s.type.charAt(0),d=NT[`${h}_union`],c[u]=d(f,V(a[p]));n&&(f=_[u]||(_[u]=[])).push(V(a).reduce(((t,e,n)=>(t[o[n].field]=e,t)),{}))}else s=wT,l=kT(i),(f=(c=v[s]||(v[s]={}))[u]||(c[u]=[])).push(l),n&&(f=_[u]||(_[u]=[])).push({[wT]:l});if(e=e||yT,v[wT]?v[wT]=NT[`${wT}_${e}`](...Object.values(v[wT])):Object.keys(v).forEach((t=>{v[t]=Object.keys(v[t]).map((e=>v[t][e])).reduce(((n,r)=>void 0===n?r:NT[`${x[t]}_${e}`](n,r)))})),y=Object.keys(_),n&&y.length){v[r?_T:vT]=e===yT?{[xT]:y.reduce(((t,e)=>(t.push(..._[e]),t)),[])}:{[bT]:y.map((t=>({[xT]:_[t]})))}}return v}),RT),EB("vlSelectionTuples",(function(t,e){return t.map((t=>ot(e.fields?{values:e.fields.map((e=>(e.getter||(e.getter=l(e.field)))(t.datum)))}:{[wT]:kT(t.datum)},e)))}));const CB=Nt(["rule"]),FB=Nt(["group","image","rect"]);function SB(t){return(t+"").toLowerCase()}function $B(t,e,n){n.endsWith(";")||(n="return("+n+");");const r=Function(...e.concat(n));return t&&t.functions?r.bind(t.functions):r}var TB={operator:(t,e)=>$B(t,["_"],e.code),parameter:(t,e)=>$B(t,["datum","_"],e.code),event:(t,e)=>$B(t,["event"],e.code),handler:(t,e)=>$B(t,["_","event"],`var datum=event.item&&event.item.datum;return ${e.code};`),encode:(t,e)=>{const{marktype:n,channels:r}=e;let i="var o=item,datum=o.datum,m=0,$;";for(const t in r){const e="o["+Ft(t)+"]";i+=`$=${r[t].code};if(${e}!==$)${e}=$,m=1;`}return i+=function(t,e){let n="";return CB[e]||(t.x2&&(t.x?(FB[e]&&(n+="if(o.x>o.x2)$=o.x,o.x=o.x2,o.x2=$;"),n+="o.width=o.x2-o.x;"):n+="o.x=o.x2-(o.width||0);"),t.xc&&(n+="o.x=o.xc-(o.width||0)/2;"),t.y2&&(t.y?(FB[e]&&(n+="if(o.y>o.y2)$=o.y,o.y=o.y2,o.y2=$;"),n+="o.height=o.y2-o.y;"):n+="o.y=o.y2-(o.height||0);"),t.yc&&(n+="o.y=o.yc-(o.height||0)/2;")),n}(r,n),i+="return m;",$B(t,["item","_"],i)},codegen:{get(t){const e=`[${t.map(Ft).join("][")}]`,n=Function("_",`return _${e};`);return n.path=e,n},comparator(t,e){let n;const r=Function("a","b","var u, v; return "+t.map(((t,r)=>{const i=e[r];let o,a;return t.path?(o=`a${t.path}`,a=`b${t.path}`):((n=n||{})["f"+r]=t,o=`this.f${r}(a)`,a=`this.f${r}(b)`),function(t,e,n,r){return`((u = ${t}) < (v = ${e}) || u == null) && v != null ? ${n}\n  : (u > v || v == null) && u != null ? ${r}\n  : ((v = v instanceof Date ? +v : v), (u = u instanceof Date ? +u : u)) !== u && v === v ? ${n}\n  : v !== v && u === u ? ${r} : `}(o,a,-i,i)})).join("")+"0;");return n?r.bind(n):r}}};function BB(t,e,n){if(!t||!A(t))return t;for(let r,i=0,o=NB.length;i<o;++i)if(r=NB[i],lt(t,r.key))return r.parse(t,e,n);return t}var NB=[{key:"$ref",parse:function(t,e){return e.get(t.$ref)||u("Operator not defined: "+t.$ref)}},{key:"$key",parse:function(t,e){const n="k:"+t.$key+"_"+!!t.$flat;return e.fn[n]||(e.fn[n]=bt(t.$key,t.$flat,e.expr.codegen))}},{key:"$expr",parse:function(t,n,r){t.$params&&n.parseParameters(t.$params,r);const i="e:"+t.$expr.code;return n.fn[i]||(n.fn[i]=e(n.parameterExpression(t.$expr),t.$fields))}},{key:"$field",parse:function(t,e){if(!t.$field)return null;const n="f:"+t.$field+"_"+t.$name;return e.fn[n]||(e.fn[n]=l(t.$field,t.$name,e.expr.codegen))}},{key:"$encode",parse:function(t,n){const r=t.$encode,i={};for(const t in r){const o=r[t];i[t]=e(n.encodeExpression(o.$expr),o.$fields),i[t].output=o.$output}return i}},{key:"$compare",parse:function(t,e){const n="c:"+t.$compare+"_"+t.$order,r=V(t.$compare).map((t=>t&&t.$tupleid?_a:t));return e.fn[n]||(e.fn[n]=Q(r,t.$order,e.expr.codegen))}},{key:"$context",parse:function(t,e){return e}},{key:"$subflow",parse:function(t,e){const n=t.$subflow;return function(t,r,i){const o=e.fork().parse(n),a=o.get(n.operators[0].id),u=o.signals.parent;return u&&u.set(i),a.detachSubflow=()=>e.detach(o),a}}},{key:"$tupleid",parse:function(){return _a}}];const zB={skip:!0};function OB(t,e,n,r){return new RB(t,e,n,r)}function RB(t,e,n,r){this.dataflow=t,this.transforms=e,this.events=t.events.bind(t),this.expr=r||TB,this.signals={},this.scales={},this.nodes={},this.data={},this.fn={},n&&(this.functions=Object.create(n),this.functions.context=this)}function UB(t){this.dataflow=t.dataflow,this.transforms=t.transforms,this.events=t.events,this.expr=t.expr,this.signals=Object.create(t.signals),this.scales=Object.create(t.scales),this.nodes=Object.create(t.nodes),this.data=Object.create(t.data),this.fn=Object.create(t.fn),t.functions&&(this.functions=Object.create(t.functions),this.functions.context=this)}function LB(t,e){t&&(null==e?t.removeAttribute("aria-label"):t.setAttribute("aria-label",e))}RB.prototype=UB.prototype={fork(){const t=new UB(this);return(this.subcontext||(this.subcontext=[])).push(t),t},detach(t){this.subcontext=this.subcontext.filter((e=>e!==t));const e=Object.keys(t.nodes);for(const n of e)t.nodes[n]._targets=null;for(const n of e)t.nodes[n].detach();t.nodes=null},get(t){return this.nodes[t]},set(t,e){return this.nodes[t]=e},add(t,e){const n=this,r=n.dataflow,i=t.value;if(n.set(t.id,e),function(t){return"collect"===SB(t)}(t.type)&&i&&(i.$ingest?r.ingest(e,i.$ingest,i.$format):i.$request?r.preload(e,i.$request,i.$format):r.pulse(e,r.changeset().insert(i))),t.root&&(n.root=e),t.parent){let i=n.get(t.parent.$ref);i?(r.connect(i,[e]),e.targets().add(i)):(n.unresolved=n.unresolved||[]).push((()=>{i=n.get(t.parent.$ref),r.connect(i,[e]),e.targets().add(i)}))}if(t.signal&&(n.signals[t.signal]=e),t.scale&&(n.scales[t.scale]=e),t.data)for(const r in t.data){const i=n.data[r]||(n.data[r]={});t.data[r].forEach((t=>i[t]=e))}},resolve(){return(this.unresolved||[]).forEach((t=>t())),delete this.unresolved,this},operator(t,e){this.add(t,this.dataflow.add(t.value,e))},transform(t,e){this.add(t,this.dataflow.add(this.transforms[SB(e)]))},stream(t,e){this.set(t.id,e)},update(t,e,n,r,i){this.dataflow.on(e,n,r,i,t.options)},operatorExpression(t){return this.expr.operator(this,t)},parameterExpression(t){return this.expr.parameter(this,t)},eventExpression(t){return this.expr.event(this,t)},handlerExpression(t){return this.expr.handler(this,t)},encodeExpression(t){return this.expr.encode(this,t)},parse:function(t){const e=this,n=t.operators||[];return t.background&&(e.background=t.background),t.eventConfig&&(e.eventConfig=t.eventConfig),t.locale&&(e.locale=t.locale),n.forEach((t=>e.parseOperator(t))),n.forEach((t=>e.parseOperatorParameters(t))),(t.streams||[]).forEach((t=>e.parseStream(t))),(t.updates||[]).forEach((t=>e.parseUpdate(t))),e.resolve()},parseOperator:function(t){const e=this;!function(t){return"operator"===SB(t)}(t.type)&&t.type?e.transform(t,t.type):e.operator(t,t.update?e.operatorExpression(t.update):null)},parseOperatorParameters:function(t){const e=this;if(t.params){const n=e.get(t.id);n||u("Invalid operator id: "+t.id),e.dataflow.connect(n,n.parameters(e.parseParameters(t.params),t.react,t.initonly))}},parseParameters:function(t,e){e=e||{};const n=this;for(const r in t){const i=t[r];e[r]=k(i)?i.map((t=>BB(t,n,e))):BB(i,n,e)}return e},parseStream:function(t){var e,n=this,r=null!=t.filter?n.eventExpression(t.filter):void 0,i=null!=t.stream?n.get(t.stream):void 0;t.source?i=n.events(t.source,t.type,r):t.merge&&(i=(e=t.merge.map((t=>n.get(t))))[0].merge.apply(e[0],e.slice(1))),t.between&&(e=t.between.map((t=>n.get(t))),i=i.between(e[0],e[1])),t.filter&&(i=i.filter(r)),null!=t.throttle&&(i=i.throttle(+t.throttle)),null!=t.debounce&&(i=i.debounce(+t.debounce)),null==i&&u("Invalid stream definition: "+JSON.stringify(t)),t.consume&&i.consume(!0),n.stream(t,i)},parseUpdate:function(t){var e,n=this,r=A(r=t.source)?r.$ref:r,i=n.get(r),o=t.update,a=void 0;i||u("Source not defined: "+t.source),e=t.target&&t.target.$expr?n.eventExpression(t.target.$expr):n.get(t.target),o&&o.$expr&&(o.$params&&(a=n.parseParameters(o.$params)),o=n.handlerExpression(o.$expr)),n.update(t,i,e,o,a)},getState:function(t){var e=this,n={};if(t.signals){var r=n.signals={};Object.keys(e.signals).forEach((n=>{const i=e.signals[n];t.signals(n,i)&&(r[n]=i.value)}))}if(t.data){var i=n.data={};Object.keys(e.data).forEach((n=>{const r=e.data[n];t.data(n,r)&&(i[n]=r.input.value)}))}return e.subcontext&&!1!==t.recurse&&(n.subcontext=e.subcontext.map((e=>e.getState(t)))),n},setState:function(t){var e=this,n=e.dataflow,r=t.data,i=t.signals;Object.keys(i||{}).forEach((t=>{n.update(e.signals[t],i[t],zB)})),Object.keys(r||{}).forEach((t=>{n.pulse(e.data[t].input,n.changeset().remove(p).insert(r[t]))})),(t.subcontext||[]).forEach(((t,n)=>{const r=e.subcontext[n];r&&r.setState(t)}))}};const qB="default";function PB(t,e){const n=t.globalCursor()?"undefined"!=typeof document&&document.body:t.container();if(n)return null==e?n.style.removeProperty("cursor"):n.style.cursor=e}function jB(t,e){var n=t._runtime.data;return lt(n,e)||u("Unrecognized data set: "+e),n[e]}function IB(t,e){Ea(e)||u("Second argument to changes must be a changeset.");const n=jB(this,t);return n.modified=!0,this.pulse(n.input,e)}function WB(t){var e=t.padding();return Math.max(0,t._viewWidth+e.left+e.right)}function HB(t){var e=t.padding();return Math.max(0,t._viewHeight+e.top+e.bottom)}function YB(t){var e=t.padding(),n=t._origin;return[e.left+n[0],e.top+n[1]]}function GB(t,e,n){var r,i,o=t._renderer,a=o&&o.canvas();return a&&(i=YB(t),(r=rv(e.changedTouches?e.changedTouches[0]:e,a))[0]-=i[0],r[1]-=i[1]),e.dataflow=t,e.item=n,e.vega=function(t,e,n){const r=e?"group"===e.mark.marktype?e:e.mark.group:null;function i(t){var n,i=r;if(t)for(n=e;n;n=n.mark.group)if(n.mark.name===t){i=n;break}return i&&i.mark&&i.mark.interactive?i:{}}function o(t){if(!t)return n;xt(t)&&(t=i(t));const e=n.slice();for(;t;)e[0]-=t.x||0,e[1]-=t.y||0,t=t.mark&&t.mark.group;return e}return{view:rt(t),item:rt(e||{}),group:i,xy:o,x:t=>o(t)[0],y:t=>o(t)[1]}}(t,n,r),e}const VB="view",XB="timer",JB="window",ZB={trap:!1};function QB(t,e,n,r){t._eventListeners.push({type:n,sources:V(e),handler:r})}function KB(t,e,n){const r=t._eventConfig&&t._eventConfig[e];return!(!1===r||A(r)&&!r[n])||(t.warn(`Blocked ${e} ${n} event listener.`),!1)}function tN(t){return t.item}function eN(t){return t.item.mark.source}function nN(t){return function(e,n){return n.vega.view().changeset().encode(n.item,t)}}function rN(t,e,n){const r=document.createElement(t);for(const t in e)r.setAttribute(t,e[t]);return null!=n&&(r.textContent=n),r}const iN="vega-bind",oN="vega-bind-name",aN="vega-bind-radio";function uN(t,e,n,r){const i=n.event||"input",o=()=>t.update(e.value);r.signal(n.signal,e.value),e.addEventListener(i,o),QB(r,e,i,o),t.set=t=>{e.value=t,e.dispatchEvent(function(t){return"undefined"!=typeof Event?new Event(t):{type:t}}(i))}}function sN(t,e,n,r){const i=r.signal(n.signal),o=rN("div",{class:iN}),a="radio"===n.input?o:o.appendChild(rN("label"));a.appendChild(rN("span",{class:oN},n.name||n.signal)),e.appendChild(o);let u=lN;switch(n.input){case"checkbox":u=cN;break;case"select":u=fN;break;case"radio":u=hN;break;case"range":u=dN}u(t,a,n,i)}function lN(t,e,n,r){const i=rN("input");for(const t in n)"signal"!==t&&"element"!==t&&i.setAttribute("input"===t?"type":t,n[t]);i.setAttribute("name",n.signal),i.value=r,e.appendChild(i),i.addEventListener("input",(()=>t.update(i.value))),t.elements=[i],t.set=t=>i.value=t}function cN(t,e,n,r){const i={type:"checkbox",name:n.signal};r&&(i.checked=!0);const o=rN("input",i);e.appendChild(o),o.addEventListener("change",(()=>t.update(o.checked))),t.elements=[o],t.set=t=>o.checked=!!t||null}function fN(t,e,n,r){const i=rN("select",{name:n.signal}),o=n.labels||[];n.options.forEach(((t,e)=>{const n={value:t};pN(t,r)&&(n.selected=!0),i.appendChild(rN("option",n,(o[e]||t)+""))})),e.appendChild(i),i.addEventListener("change",(()=>{t.update(n.options[i.selectedIndex])})),t.elements=[i],t.set=t=>{for(let e=0,r=n.options.length;e<r;++e)if(pN(n.options[e],t))return void(i.selectedIndex=e)}}function hN(t,e,n,r){const i=rN("span",{class:aN}),o=n.labels||[];e.appendChild(i),t.elements=n.options.map(((e,a)=>{const u={type:"radio",name:n.signal,value:e};pN(e,r)&&(u.checked=!0);const s=rN("input",u);s.addEventListener("change",(()=>t.update(e)));const l=rN("label",{},(o[a]||e)+"");return l.prepend(s),i.appendChild(l),s})),t.set=e=>{const n=t.elements,r=n.length;for(let t=0;t<r;++t)pN(n[t].value,e)&&(n[t].checked=!0)}}function dN(t,e,n,r){r=void 0!==r?r:(+n.max+ +n.min)/2;const i=null!=n.max?n.max:Math.max(100,+r)||100,o=n.min||Math.min(0,i,+r)||0,a=n.step||ke(o,i,100),u=rN("input",{type:"range",name:n.signal,min:o,max:i,step:a});u.value=r;const s=rN("span",{},+r);e.appendChild(u),e.appendChild(s);const l=()=>{s.textContent=u.value,t.update(+u.value)};u.addEventListener("input",l),u.addEventListener("change",l),t.elements=[u],t.set=t=>{u.value=t,s.textContent=t}}function pN(t,e){return t===e||t+""==e+""}function gN(t,e,n,r,i,o){return(e=e||new r(t.loader())).initialize(n,WB(t),HB(t),YB(t),i,o).background(t.background())}function mN(t,e){return e?function(){try{e.apply(this,arguments)}catch(e){t.error(e)}}:null}function yN(t,e,n){if("string"==typeof e){if("undefined"==typeof document)return t.error("DOM document instance not found."),null;if(!(e=document.querySelector(e)))return t.error("Signal bind element not found: "+e),null}if(e&&n)try{e.textContent=""}catch(n){e=null,t.error(n)}return e}const vN=t=>+t||0,_N=t=>({top:t,bottom:t,left:t,right:t});function xN(t){return A(t)?{top:vN(t.top),bottom:vN(t.bottom),left:vN(t.left),right:vN(t.right)}:_N(vN(t))}async function bN(t,e,n,r){const i=b_(e),o=i&&i.headless;return o||u("Unrecognized renderer type: "+e),await t.runAsync(),gN(t,null,null,o,n,r).renderAsync(t._scenegraph.root)}var wN="width",kN="height",AN="padding",MN={skip:!0};function EN(t,e){var n=t.autosize(),r=t.padding();return e-(n&&n.contains===AN?r.left+r.right:0)}function DN(t,e){var n=t.autosize(),r=t.padding();return e-(n&&n.contains===AN?r.top+r.bottom:0)}function CN(t,e){return e.modified&&k(e.input.value)&&t.indexOf("_:vega:_")}function FN(t,e){return!("parent"===t||e instanceof Ka.proxy)}function SN(t,e,n,r){const i=t.element();i&&i.setAttribute("title",function(t){return null==t?"":k(t)?$N(t):A(t)&&!mt(t)?(e=t,Object.keys(e).map((t=>{const n=e[t];return t+": "+(k(n)?$N(n):TN(n))})).join("\n")):t+"";var e}(r))}function $N(t){return"["+t.map(TN).join(", ")+"]"}function TN(t){return k(t)?"[…]":A(t)&&!mt(t)?"{…}":t}function BN(t,e){const n=this;if(e=e||{},Ja.call(n),e.loader&&n.loader(e.loader),e.logger&&n.logger(e.logger),null!=e.logLevel&&n.logLevel(e.logLevel),e.locale||t.locale){const r=ot({},t.locale,e.locale);n.locale(Lo(r.number,r.time))}n._el=null,n._elBind=null,n._renderType=e.renderer||__.Canvas,n._scenegraph=new Jy;const r=n._scenegraph.root;n._renderer=null,n._tooltip=e.tooltip||SN,n._redraw=!0,n._handler=(new wv).scene(r),n._globalCursor=!1,n._preventDefault=!1,n._timers=[],n._eventListeners=[],n._resizeListeners=[],n._eventConfig=function(t){const e=ot({defaults:{}},t),n=(t,e)=>{e.forEach((e=>{k(t[e])&&(t[e]=Nt(t[e]))}))};return n(e.defaults,["prevent","allow"]),n(e,["view","window","selector"]),e}(t.eventConfig),n.globalCursor(n._eventConfig.globalCursor);const i=function(t,e,n){return OB(t,Ka,_B,n).parse(e)}(n,t,e.expr);n._runtime=i,n._signals=i.signals,n._bind=(t.bindings||[]).map((t=>({state:null,param:ot({},t)}))),i.root&&i.root.set(r),r.source=i.data.root.input,n.pulse(i.data.root.input,n.changeset().insert(r.items)),n._width=n.width(),n._height=n.height(),n._viewWidth=EN(n,n._width),n._viewHeight=DN(n,n._height),n._origin=[0,0],n._resize=0,n._autosize=1,function(t){var e=t._signals,n=e[wN],r=e[kN],i=e[AN];function o(){t._autosize=t._resize=1}t._resizeWidth=t.add(null,(e=>{t._width=e.size,t._viewWidth=EN(t,e.size),o()}),{size:n}),t._resizeHeight=t.add(null,(e=>{t._height=e.size,t._viewHeight=DN(t,e.size),o()}),{size:r});const a=t.add(null,o,{pad:i});t._resizeWidth.rank=n.rank+1,t._resizeHeight.rank=r.rank+1,a.rank=i.rank+1}(n),function(t){t.add(null,(e=>(t._background=e.bg,t._resize=1,e.bg)),{bg:t._signals.background})}(n),function(t){const e=t._signals.cursor||(t._signals.cursor=t.add({user:qB,item:null}));t.on(t.events("view","mousemove"),e,((t,n)=>{const r=e.value,i=r?xt(r)?r:r.user:qB,o=n.item&&n.item.cursor||null;return r&&i===r.user&&o==r.item?r:{user:i,item:o}})),t.add(null,(function(e){let n=e.cursor,r=this.value;return xt(n)||(r=n.item,n=n.user),PB(t,n&&n!==qB?n:r||n),r}),{cursor:e})}(n),n.description(t.description),e.hover&&n.hover(),e.container&&n.initialize(e.container,e.bind)}function NN(t,e){return lt(t._signals,e)?t._signals[e]:u("Unrecognized signal name: "+Ft(e))}function zN(t,e){const n=(t._targets||[]).filter((t=>t._update&&t._update.handler===e));return n.length?n[0]:null}function ON(t,e,n,r){let i=zN(n,r);return i||(i=mN(t,(()=>r(e,n.value))),i.handler=r,t.on(n,null,i)),t}function RN(t,e,n){const r=zN(e,n);return r&&e._targets.remove(r),t}dt(BN,Ja,{async evaluate(t,e,n){if(await Ja.prototype.evaluate.call(this,t,e),this._redraw||this._resize)try{this._renderer&&(this._resize&&(this._resize=0,function(t){var e=YB(t),n=WB(t),r=HB(t);t._renderer.background(t.background()),t._renderer.resize(n,r,e),t._handler.origin(e),t._resizeListeners.forEach((e=>{try{e(n,r)}catch(e){t.error(e)}}))}(this)),await this._renderer.renderAsync(this._scenegraph.root)),this._redraw=!1}catch(t){this.error(t)}return n&&ga(this,n),this},dirty(t){this._redraw=!0,this._renderer&&this._renderer.dirty(t)},description(t){if(arguments.length){const e=null!=t?t+"":null;return e!==this._desc&&LB(this._el,this._desc=e),this}return this._desc},container(){return this._el},scenegraph(){return this._scenegraph},origin(){return this._origin.slice()},signal(t,e,n){const r=NN(this,t);return 1===arguments.length?r.value:this.update(r,e,n)},width(t){return arguments.length?this.signal("width",t):this.signal("width")},height(t){return arguments.length?this.signal("height",t):this.signal("height")},padding(t){return arguments.length?this.signal("padding",xN(t)):xN(this.signal("padding"))},autosize(t){return arguments.length?this.signal("autosize",t):this.signal("autosize")},background(t){return arguments.length?this.signal("background",t):this.signal("background")},renderer(t){return arguments.length?(b_(t)||u("Unrecognized renderer type: "+t),t!==this._renderType&&(this._renderType=t,this._resetRenderer()),this):this._renderType},tooltip(t){return arguments.length?(t!==this._tooltip&&(this._tooltip=t,this._resetRenderer()),this):this._tooltip},loader(t){return arguments.length?(t!==this._loader&&(Ja.prototype.loader.call(this,t),this._resetRenderer()),this):this._loader},resize(){return this._autosize=1,this.touch(NN(this,"autosize"))},_resetRenderer(){this._renderer&&(this._renderer=null,this.initialize(this._el,this._elBind))},_resizeView:function(t,e,n,r,i,o){this.runAfter((a=>{let u=0;a._autosize=0,a.width()!==n&&(u=1,a.signal(wN,n,MN),a._resizeWidth.skip(!0)),a.height()!==r&&(u=1,a.signal(kN,r,MN),a._resizeHeight.skip(!0)),a._viewWidth!==t&&(a._resize=1,a._viewWidth=t),a._viewHeight!==e&&(a._resize=1,a._viewHeight=e),a._origin[0]===i[0]&&a._origin[1]===i[1]||(a._resize=1,a._origin=i),u&&a.run("enter"),o&&a.runAfter((t=>t.resize()))}),!1,1)},addEventListener(t,e,n){let r=e;return n&&!1===n.trap||(r=mN(this,e),r.raw=e),this._handler.on(t,r),this},removeEventListener(t,e){for(var n,r,i=this._handler.handlers(t),o=i.length;--o>=0;)if(r=i[o].type,n=i[o].handler,t===r&&(e===n||e===n.raw)){this._handler.off(r,n);break}return this},addResizeListener(t){const e=this._resizeListeners;return e.indexOf(t)<0&&e.push(t),this},removeResizeListener(t){var e=this._resizeListeners,n=e.indexOf(t);return n>=0&&e.splice(n,1),this},addSignalListener(t,e){return ON(this,t,NN(this,t),e)},removeSignalListener(t,e){return RN(this,NN(this,t),e)},addDataListener(t,e){return ON(this,t,jB(this,t).values,e)},removeDataListener(t,e){return RN(this,jB(this,t).values,e)},globalCursor(t){if(arguments.length){if(this._globalCursor!==!!t){const e=PB(this,null);this._globalCursor=!!t,e&&PB(this,e)}return this}return this._globalCursor},preventDefault(t){return arguments.length?(this._preventDefault=t,this):this._preventDefault},timer:function(t,e){this._timers.push(function(t,e,n){var r=new nD,i=e;return null==e?(r.restart(t,e,n),r):(r._restart=r.restart,r.restart=function(t,e,n){e=+e,n=null==n?tD():+n,r._restart((function o(a){a+=i,r._restart(o,i+=e,n),t(a)}),e,n)},r.restart(t,e,n),r)}((function(e){t({timestamp:Date.now(),elapsed:e})}),e))},events:function(t,e,n){var r,i=this,o=new za(n),a=function(n,r){i.runAsync(null,(()=>{t===VB&&function(t,e){var n=t._eventConfig.defaults,r=n.prevent,i=n.allow;return!1!==r&&!0!==i&&(!0===r||!1===i||(r?r[e]:i?!i[e]:t.preventDefault()))}(i,e)&&n.preventDefault(),o.receive(GB(i,n,r))}))};if(t===XB)KB(i,"timer",e)&&i.timer(a,e);else if(t===VB)KB(i,"view",e)&&i.addEventListener(e,a,ZB);else if(t===JB?KB(i,"window",e)&&"undefined"!=typeof window&&(r=[window]):"undefined"!=typeof document&&KB(i,"selector",e)&&(r=Array.from(document.querySelectorAll(t))),r){for(var u=0,s=r.length;u<s;++u)r[u].addEventListener(e,a);QB(i,r,e,a)}else i.warn("Can not resolve event source: "+t);return o},finalize:function(){var t,e,n,r=this._tooltip,i=this._timers,o=this._eventListeners;for(t=i.length;--t>=0;)i[t].stop();for(t=o.length;--t>=0;)for(e=(n=o[t]).sources.length;--e>=0;)n.sources[e].removeEventListener(n.type,n.handler);return r&&r.call(this,this._handler,null,null,null),this},hover:function(t,e){return e=[e||"update",(t=[t||"hover"])[0]],this.on(this.events("view","mouseover",tN),eN,nN(t)),this.on(this.events("view","mouseout",tN),eN,nN(e)),this},data:function(t,e){return arguments.length<2?jB(this,t).values.value:IB.call(this,t,Da().remove(p).insert(e))},change:IB,insert:function(t,e){return IB.call(this,t,Da().insert(e))},remove:function(t,e){return IB.call(this,t,Da().remove(e))},scale:function(t){var e=this._runtime.scales;return lt(e,t)||u("Unrecognized scale or projection: "+t),e[t].value},initialize:function(t,e){const n=this,r=n._renderType,i=n._eventConfig.bind,o=b_(r);t=n._el=t?yN(n,t,!0):null,function(t){const e=t.container();e&&(e.setAttribute("role","graphics-document"),e.setAttribute("aria-roleDescription","visualization"),LB(e,t.description()))}(n),o||n.error("Unrecognized renderer type: "+r);const a=o.handler||wv,u=t?o.renderer:o.headless;return n._renderer=u?gN(n,n._renderer,t,u):null,n._handler=function(t,e,n,r){const i=new r(t.loader(),mN(t,t.tooltip())).scene(t.scenegraph().root).initialize(n,YB(t),t);return e&&e.handlers().forEach((t=>{i.on(t.type,t.handler)})),i}(n,n._handler,t,a),n._redraw=!0,t&&"none"!==i&&(e=e?n._elBind=yN(n,e,!0):t.appendChild(rN("form",{class:"vega-bindings"})),n._bind.forEach((t=>{t.param.element&&"container"!==i&&(t.element=yN(n,t.param.element,!!t.param.input))})),n._bind.forEach((t=>{!function(t,e,n){if(!e)return;const r=n.param;let i=n.state;i||(i=n.state={elements:null,active:!1,set:null,update:e=>{e!=t.signal(r.signal)&&t.runAsync(null,(()=>{i.source=!0,t.signal(r.signal,e)}))}},r.debounce&&(i.update=it(r.debounce,i.update))),(null==r.input&&r.element?uN:sN)(i,e,r,t),i.active||(t.on(t._signals[r.signal],null,(()=>{i.source?i.source=!1:i.set(t.signal(r.signal))})),i.active=!0)}(n,t.element||e,t)}))),n},toImageURL:async function(t,e){t!==__.Canvas&&t!==__.SVG&&t!==__.PNG&&u("Unrecognized image type: "+t);const n=await bN(this,t,e);return t===__.SVG?function(t,e){const n=new Blob([t],{type:e});return window.URL.createObjectURL(n)}(n.svg(),"image/svg+xml"):n.canvas().toDataURL("image/png")},toCanvas:async function(t,e){return(await bN(this,__.Canvas,t,e)).canvas()},toSVG:async function(t){return(await bN(this,__.SVG,t)).svg()},getState:function(t){return this._runtime.getState(t||{data:CN,signals:FN,recurse:!0})},setState:function(t){return this.runAsync(null,(e=>{e._trigger=!1,e._runtime.setState(t)}),(t=>{t._trigger=!0})),this}});const UN="view",LN="[",qN="]",PN="{",jN="}",IN=":",WN=",",HN="@",YN=">",GN=/[[\]{}]/,VN={"*":1,arc:1,area:1,group:1,image:1,line:1,path:1,rect:1,rule:1,shape:1,symbol:1,text:1,trail:1};let XN,JN;function ZN(t,e,n){return XN=e||UN,JN=n||VN,KN(t.trim()).map(tz)}function QN(t,e,n,r,i){const o=t.length;let a,u=0;for(;e<o;++e){if(a=t[e],!u&&a===n)return e;i&&i.indexOf(a)>=0?--u:r&&r.indexOf(a)>=0&&++u}return e}function KN(t){const e=[],n=t.length;let r=0,i=0;for(;i<n;)i=QN(t,i,WN,LN+PN,qN+jN),e.push(t.substring(r,i).trim()),r=++i;if(0===e.length)throw"Empty event selector: "+t;return e}function tz(t){return"["===t[0]?function(t){const e=t.length;let n,r=1;if(r=QN(t,r,qN,LN,qN),r===e)throw"Empty between selector: "+t;if(n=KN(t.substring(1,r)),2!==n.length)throw"Between selector must have two elements: "+t;if(t=t.slice(r+1).trim(),t[0]!==YN)throw"Expected '>' after between selector: "+t;n=n.map(tz);const i=tz(t.slice(1).trim());if(i.between)return{between:n,stream:i};i.between=n;return i}(t):function(t){const e={source:XN},n=[];let r,i,o=[0,0],a=0,u=0,s=t.length,l=0;if(t[s-1]===jN){if(l=t.lastIndexOf(PN),!(l>=0))throw"Unmatched right brace: "+t;try{o=function(t){const e=t.split(WN);if(!t.length||e.length>2)throw t;return e.map((e=>{const n=+e;if(n!=n)throw t;return n}))}(t.substring(l+1,s-1))}catch(e){throw"Invalid throttle specification: "+t}s=(t=t.slice(0,l).trim()).length,l=0}if(!s)throw t;t[0]===HN&&(a=++l);r=QN(t,l,IN),r<s&&(n.push(t.substring(u,r).trim()),u=l=++r);if(l=QN(t,l,LN),l===s)n.push(t.substring(u,s).trim());else if(n.push(t.substring(u,l).trim()),i=[],u=++l,u===s)throw"Unmatched left bracket: "+t;for(;l<s;){if(l=QN(t,l,qN),l===s)throw"Unmatched left bracket: "+t;if(i.push(t.substring(u,l).trim()),l<s-1&&t[++l]!==LN)throw"Expected left bracket: "+t;u=++l}if(!(s=n.length)||GN.test(n[s-1]))throw"Invalid event selector: "+t;s>1?(e.type=n[1],a?e.markname=n[0].slice(1):!function(t){return JN[t]}(n[0])?e.source=n[0]:e.marktype=n[0]):e.type=n[0];"!"===e.type.slice(-1)&&(e.consume=!0,e.type=e.type.slice(0,-1));null!=i&&(e.filter=i);o[0]&&(e.throttle=o[0]);o[1]&&(e.debounce=o[1]);return e}(t)}function ez(t){return A(t)?t:{type:t||"pad"}}const nz=t=>+t||0,rz=t=>({top:t,bottom:t,left:t,right:t});function iz(t){return A(t)?t.signal?t:{top:nz(t.top),bottom:nz(t.bottom),left:nz(t.left),right:nz(t.right)}:rz(nz(t))}const oz=t=>A(t)&&!k(t)?ot({},t):{value:t};function az(t,e,n,r){if(null!=n){return A(n)&&!k(n)||k(n)&&n.length&&A(n[0])?t.update[e]=n:t[r||"enter"][e]={value:n},1}return 0}function uz(t,e,n){for(const n in e)az(t,n,e[n]);for(const e in n)az(t,e,n[e],"update")}function sz(t,e,n){for(const r in e)n&&lt(n,r)||(t[r]=ot(t[r]||{},e[r]));return t}function lz(t,e){return e&&(e.enter&&e.enter[t]||e.update&&e.update[t])}const cz="mark",fz="frame",hz="scope",dz="axis",pz="axis-domain",gz="axis-grid",mz="axis-label",yz="axis-tick",vz="axis-title",_z="legend",xz="legend-band",bz="legend-entry",wz="legend-gradient",kz="legend-label",Az="legend-symbol",Mz="legend-title",Ez="title",Dz="title-text",Cz="title-subtitle";function Fz(t,e,n){t[e]=n&&n.signal?{signal:n.signal}:{value:n}}const Sz=t=>xt(t)?Ft(t):t.signal?`(${t.signal})`:Nz(t);function $z(t){if(null!=t.gradient)return function(t){const e=[t.start,t.stop,t.count].map((t=>null==t?null:Ft(t)));for(;e.length&&null==F(e);)e.pop();return e.unshift(Sz(t.gradient)),`gradient(${e.join(",")})`}(t);let e=t.signal?`(${t.signal})`:t.color?function(t){return t.c?Tz("hcl",t.h,t.c,t.l):t.h||t.s?Tz("hsl",t.h,t.s,t.l):t.l||t.a?Tz("lab",t.l,t.a,t.b):t.r||t.g||t.b?Tz("rgb",t.r,t.g,t.b):null}(t.color):null!=t.field?Nz(t.field):void 0!==t.value?Ft(t.value):void 0;return null!=t.scale&&(e=function(t,e){const n=Sz(t.scale);null!=t.range?e=`lerp(_range(${n}), ${+t.range})`:(void 0!==e&&(e=`_scale(${n}, ${e})`),t.band&&(e=(e?e+"+":"")+`_bandwidth(${n})`+(1==+t.band?"":"*"+Bz(t.band)),t.extra&&(e=`(datum.extra ? _scale(${n}, datum.extra.value) : ${e})`)),null==e&&(e="0"));return e}(t,e)),void 0===e&&(e=null),null!=t.exponent&&(e=`pow(${e},${Bz(t.exponent)})`),null!=t.mult&&(e+=`*${Bz(t.mult)}`),null!=t.offset&&(e+=`+${Bz(t.offset)}`),t.round&&(e=`round(${e})`),e}const Tz=(t,e,n,r)=>`(${t}(${[e,n,r].map($z).join(",")})+'')`;function Bz(t){return A(t)?"("+$z(t)+")":t}function Nz(t){return zz(A(t)?t:{datum:t})}function zz(t){let e,n,r;if(t.signal)e="datum",r=t.signal;else if(t.group||t.parent){for(n=Math.max(1,t.level||1),e="item";n-- >0;)e+=".mark.group";t.parent?(r=t.parent,e+=".datum"):r=t.group}else t.datum?(e="datum",r=t.datum):u("Invalid field reference: "+Ft(t));return t.signal||(r=xt(r)?s(r).map(Ft).join("]["):zz(r)),e+"["+r+"]"}function Oz(t,e,n,r,i,o){const a={};(o=o||{}).encoders={$encode:a},t=function(t,e,n,r,i){const o={},a={};let u,s,l,c;for(s in s="lineBreak","text"!==e||null==i[s]||lz(s,t)||Fz(o,s,i[s]),("legend"==n||String(n).startsWith("axis"))&&(n=null),c=n===fz?i.group:n===cz?ot({},i.mark,i[e]):null,c)l=lz(s,t)||("fill"===s||"stroke"===s)&&(lz("fill",t)||lz("stroke",t)),l||Fz(o,s,c[s]);for(s in V(r).forEach((e=>{const n=i.style&&i.style[e];for(const e in n)lz(e,t)||Fz(o,e,n[e])})),t=ot({},t),o)c=o[s],c.signal?(u=u||{})[s]=c:a[s]=c;return t.enter=ot(a,t.enter),u&&(t.update=ot(u,t.update)),t}(t,e,n,r,i.config);for(const n in t)a[n]=Rz(t[n],e,o,i);return o}function Rz(t,e,n,r){const i={},o={};for(const e in t)null!=t[e]&&(i[e]=Uz((a=t[e],k(a)?function(t){let e="";return t.forEach((t=>{const n=$z(t);e+=t.test?`(${t.test})?${n}:`:n})),":"===F(e)&&(e+="null"),e}(a):$z(a)),r,n,o));var a;return{$expr:{marktype:e,channels:i},$fields:Object.keys(o),$output:Object.keys(t)}}function Uz(t,e,n,r){const i=DB(t,e);return i.$fields.forEach((t=>r[t]=1)),ot(n,i.$params),i.$expr}const Lz="outer",qz=["value","update","init","react","bind"];function Pz(t,e){u(t+' for "outer" push: '+Ft(e))}function jz(t,e){const n=t.name;if(t.push===Lz)e.signals[n]||Pz("No prior signal definition",n),qz.forEach((e=>{void 0!==t[e]&&Pz("Invalid property ",e)}));else{const r=e.addSignal(n,t.value);!1===t.react&&(r.react=!1),t.bind&&e.addBinding(n,t.bind)}}function Iz(t,e,n,r){this.id=-1,this.type=t,this.value=e,this.params=n,r&&(this.parent=r)}function Wz(t,e,n,r){return new Iz(t,e,n,r)}function Hz(t,e){return Wz("operator",t,e)}function Yz(t){const e={$ref:t.id};return t.id<0&&(t.refs=t.refs||[]).push(e),e}function Gz(t,e){return e?{$field:t,$name:e}:{$field:t}}const Vz=Gz("key");function Xz(t,e){return{$compare:t,$order:e}}const Jz="descending";function Zz(t,e){return(t&&t.signal?"$"+t.signal:t||"")+(t&&e?"_":"")+(e&&e.signal?"$"+e.signal:e||"")}const Qz="scope",Kz="view";function tO(t){return t&&t.signal}function eO(t){if(tO(t))return!0;if(A(t))for(const e in t)if(eO(t[e]))return!0;return!1}function nO(t,e){return null!=t?t:e}function rO(t){return t&&t.signal||t}const iO="timer";function oO(t,e){return(t.merge?aO:t.stream?uO:t.type?sO:u("Invalid stream specification: "+Ft(t)))(t,e)}function aO(t,e){const n=lO({merge:t.merge.map((t=>oO(t,e)))},t,e);return e.addStream(n).id}function uO(t,e){const n=lO({stream:oO(t.stream,e)},t,e);return e.addStream(n).id}function sO(t,e){let n;t.type===iO?(n=e.event(iO,t.throttle),t={between:t.between,filter:t.filter}):n=e.event(function(t){return t===Qz?Kz:t||Kz}(t.source),t.type);const r=lO({stream:n},t,e);return 1===Object.keys(r).length?n:e.addStream(r).id}function lO(t,e,n){let r=e.between;return r&&(2!==r.length&&u('Stream "between" parameter must have 2 entries: '+Ft(e)),t.between=[oO(r[0],n),oO(r[1],n)]),r=e.filter?[].concat(e.filter):[],(e.marktype||e.markname||e.markrole)&&r.push(function(t,e,n){const r="event.item";return r+(t&&"*"!==t?"&&"+r+".mark.marktype==='"+t+"'":"")+(n?"&&"+r+".mark.role==='"+n+"'":"")+(e?"&&"+r+".mark.name==='"+e+"'":"")}(e.marktype,e.markname,e.markrole)),e.source===Qz&&r.push("inScope(event.item)"),r.length&&(t.filter=DB("("+r.join(")&&(")+")",n).$expr),null!=(r=e.throttle)&&(t.throttle=+r),null!=(r=e.debounce)&&(t.debounce=+r),e.consume&&(t.consume=!0),t}const cO={code:"_.$value",ast:{type:"Identifier",value:"value"}};function fO(t,e,n){const r=t.encode,i={target:n};let o=t.events,a=t.update,s=[];o||u("Signal update missing events specification."),xt(o)&&(o=ZN(o,e.isSubscope()?Qz:Kz)),o=V(o).filter((t=>t.signal||t.scale?(s.push(t),0):1)),s.length>1&&(s=[hO(s)]),o.length&&s.push(o.length>1?{merge:o}:o[0]),null!=r&&(a&&u("Signal encode and update are mutually exclusive."),a="encode(item(),"+Ft(r)+")"),i.update=xt(a)?DB(a,e):null!=a.expr?DB(a.expr,e):null!=a.value?a.value:null!=a.signal?{$expr:cO,$params:{$value:e.signalRef(a.signal)}}:u("Invalid signal update specification."),t.force&&(i.options={force:!0}),s.forEach((t=>e.addUpdate(ot(function(t,e){return{source:t.signal?e.signalRef(t.signal):t.scale?e.scaleRef(t.scale):oO(t,e)}}(t,e),i))))}function hO(t){return{signal:"["+t.map((t=>t.scale?'scale("'+t.scale+'")':t.signal))+"]"}}const dO=t=>(e,n,r)=>Wz(t,n,e||void 0,r),pO=dO("aggregate"),gO=dO("axisticks"),mO=dO("bound"),yO=dO("collect"),vO=dO("compare"),_O=dO("datajoin"),xO=dO("encode"),bO=dO("expression"),wO=dO("facet"),kO=dO("field"),AO=dO("key"),MO=dO("legendentries"),EO=dO("load"),DO=dO("mark"),CO=dO("multiextent"),FO=dO("multivalues"),SO=dO("overlap"),$O=dO("params"),TO=dO("prefacet"),BO=dO("projection"),NO=dO("proxy"),zO=dO("relay"),OO=dO("render"),RO=dO("scale"),UO=dO("sieve"),LO=dO("sortitems"),qO=dO("viewlayout"),PO=dO("values");let jO=0;const IO={min:"min",max:"max",count:"sum"};function WO(t,e){const n=e.getScale(t.name).params;let r;for(r in n.domain=VO(t.domain,t,e),null!=t.range&&(n.range=nR(t,e,n)),null!=t.interpolate&&function(t,e){e.interpolate=HO(t.type||t),null!=t.gamma&&(e.interpolateGamma=HO(t.gamma))}(t.interpolate,n),null!=t.nice&&(n.nice=function(t){return A(t)?{interval:HO(t.interval),step:HO(t.step)}:HO(t)}(t.nice)),null!=t.bins&&(n.bins=function(t,e){return t.signal||k(t)?YO(t,e):e.objectProperty(t)}(t.bins,e)),t)lt(n,r)||"name"===r||(n[r]=HO(t[r],e))}function HO(t,e){return A(t)?t.signal?e.signalRef(t.signal):u("Unsupported object: "+Ft(t)):t}function YO(t,e){return t.signal?e.signalRef(t.signal):t.map((t=>HO(t,e)))}function GO(t){u("Can not find data set: "+Ft(t))}function VO(t,e,n){if(t)return t.signal?n.signalRef(t.signal):(k(t)?XO:t.fields?ZO:JO)(t,e,n);null==e.domainMin&&null==e.domainMax||u("No scale domain defined for domainMin/domainMax to override.")}function XO(t,e,n){return t.map((t=>HO(t,n)))}function JO(t,e,n){const r=n.getData(t.data);return r||GO(t.data),ap(e.type)?r.valuesRef(n,t.field,KO(t.sort,!1)):cp(e.type)?r.domainRef(n,t.field):r.extentRef(n,t.field)}function ZO(t,e,n){const r=t.data,i=t.fields.reduce(((t,e)=>(e=xt(e)?{data:r,field:e}:k(e)||e.signal?function(t,e){const n="_:vega:_"+jO++,r=yO({});if(k(t))r.value={$ingest:t};else if(t.signal){const i="setdata("+Ft(n)+","+t.signal+")";r.params.input=e.signalRef(i)}return e.addDataPipeline(n,[r,UO({})]),{data:n,field:"data"}}(e,n):e,t.push(e),t)),[]);return(ap(e.type)?QO:cp(e.type)?tR:eR)(t,n,i)}function QO(t,e,n){const r=KO(t.sort,!0);let i,o;const a=n.map((t=>{const n=e.getData(t.data);return n||GO(t.data),n.countsRef(e,t.field,r)})),u={groupby:Vz,pulse:a};r&&(i=r.op||"count",o=r.field?Zz(i,r.field):"count",u.ops=[IO[i]],u.fields=[e.fieldRef(o)],u.as=[o]),i=e.add(pO(u));const s=e.add(yO({pulse:Yz(i)}));return o=e.add(PO({field:Vz,sort:e.sortRef(r),pulse:Yz(s)})),Yz(o)}function KO(t,e){return t&&(t.field||t.op?t.field||"count"===t.op?e&&t.field&&t.op&&!IO[t.op]&&u("Multiple domain scales can not be sorted using "+t.op):u("No field provided for sort aggregate op: "+t.op):A(t)?t.field="key":t={field:"key"}),t}function tR(t,e,n){const r=n.map((t=>{const n=e.getData(t.data);return n||GO(t.data),n.domainRef(e,t.field)}));return Yz(e.add(FO({values:r})))}function eR(t,e,n){const r=n.map((t=>{const n=e.getData(t.data);return n||GO(t.data),n.extentRef(e,t.field)}));return Yz(e.add(CO({extents:r})))}function nR(t,e,n){const r=e.config.range;let i=t.range;if(i.signal)return e.signalRef(i.signal);if(xt(i)){if(r&&lt(r,i))return nR(t=ot({},t,{range:r[i]}),e,n);"width"===i?i=[0,{signal:"width"}]:"height"===i?i=ap(t.type)?[0,{signal:"height"}]:[{signal:"height"},0]:u("Unrecognized scale range value: "+Ft(i))}else{if(i.scheme)return n.scheme=k(i.scheme)?YO(i.scheme,e):HO(i.scheme,e),i.extent&&(n.schemeExtent=YO(i.extent,e)),void(i.count&&(n.schemeCount=HO(i.count,e)));if(i.step)return void(n.rangeStep=HO(i.step,e));if(ap(t.type)&&!k(i))return VO(i,t,e);k(i)||u("Unsupported range type: "+Ft(i))}return i.map((t=>(k(t)?YO:HO)(t,e)))}function rR(t,e,n){return k(t)?t.map((t=>rR(t,e,n))):A(t)?t.signal?n.signalRef(t.signal):"fit"===e?t:u("Unsupported parameter object: "+Ft(t)):t}const iR="top",oR="left",aR="right",uR="bottom",sR="center",lR="vertical",cR="start",fR="end",hR="index",dR="label",pR="offset",gR="perc",mR="perc2",yR="value",vR="guide-label",_R="guide-title",xR="group-title",bR="group-subtitle",wR="symbol",kR="gradient",AR="discrete",MR="size",ER=[MR,"shape","fill","stroke","strokeWidth","strokeDash","opacity"],DR={name:1,style:1,interactive:1},CR={value:0},FR={value:1},SR="group",$R="rect",TR="rule",BR="symbol",NR="text";function zR(t){return t.type=SR,t.interactive=t.interactive||!1,t}function OR(t,e){const n=(n,r)=>nO(t[n],nO(e[n],r));return n.isVertical=n=>lR===nO(t.direction,e.direction||(n?e.symbolDirection:e.gradientDirection)),n.gradientLength=()=>nO(t.gradientLength,e.gradientLength||e.gradientWidth),n.gradientThickness=()=>nO(t.gradientThickness,e.gradientThickness||e.gradientHeight),n.entryColumns=()=>nO(t.columns,nO(e.columns,+n.isVertical(!0))),n}function RR(t,e){const n=e&&(e.update&&e.update[t]||e.enter&&e.enter[t]);return n&&n.signal?n:n?n.value:null}function UR(t,e,n){return`item.anchor === '${cR}' ? ${t} : item.anchor === '${fR}' ? ${e} : ${n}`}const LR=UR(Ft(oR),Ft(aR),Ft(sR));function qR(t,e){return e?t?A(t)?Object.assign({},t,{offset:qR(t.offset,e)}):{value:t,offset:e}:e:t}function PR(t,e){return e?(t.name=e.name,t.style=e.style||t.style,t.interactive=!!e.interactive,t.encode=sz(t.encode,e,DR)):t.interactive=!1,t}function jR(t,e,n,r){const i=OR(t,n),o=i.isVertical(),a=i.gradientThickness(),u=i.gradientLength();let s,l,c,f,h;o?(l=[0,1],c=[0,0],f=a,h=u):(l=[0,0],c=[1,0],f=u,h=a);const d={enter:s={opacity:CR,x:CR,y:CR,width:oz(f),height:oz(h)},update:ot({},s,{opacity:FR,fill:{gradient:e,start:l,stop:c}}),exit:{opacity:CR}};return uz(d,{stroke:i("gradientStrokeColor"),strokeWidth:i("gradientStrokeWidth")},{opacity:i("gradientOpacity")}),PR({type:$R,role:wz,encode:d},r)}function IR(t,e,n,r,i){const o=OR(t,n),a=o.isVertical(),u=o.gradientThickness(),s=o.gradientLength();let l,c,f,h,d="";a?(l="y",f="y2",c="x",h="width",d="1-"):(l="x",f="x2",c="y",h="height");const p={opacity:CR,fill:{scale:e,field:yR}};p[l]={signal:d+"datum."+gR,mult:s},p[c]=CR,p[f]={signal:d+"datum."+mR,mult:s},p[h]=oz(u);const g={enter:p,update:ot({},p,{opacity:FR}),exit:{opacity:CR}};return uz(g,{stroke:o("gradientStrokeColor"),strokeWidth:o("gradientStrokeWidth")},{opacity:o("gradientOpacity")}),PR({type:$R,role:xz,key:yR,from:i,encode:g},r)}const WR=`datum.${gR}<=0?"${oR}":datum.${gR}>=1?"${aR}":"${sR}"`,HR=`datum.${gR}<=0?"${uR}":datum.${gR}>=1?"${iR}":"middle"`;function YR(t,e,n,r){const i=OR(t,e),o=i.isVertical(),a=oz(i.gradientThickness()),u=i.gradientLength();let s,l,c,f,h=i("labelOverlap"),d="";const p={enter:s={opacity:CR},update:l={opacity:FR,text:{field:dR}},exit:{opacity:CR}};return uz(p,{fill:i("labelColor"),fillOpacity:i("labelOpacity"),font:i("labelFont"),fontSize:i("labelFontSize"),fontStyle:i("labelFontStyle"),fontWeight:i("labelFontWeight"),limit:nO(t.labelLimit,e.gradientLabelLimit)}),o?(s.align={value:"left"},s.baseline=l.baseline={signal:HR},c="y",f="x",d="1-"):(s.align=l.align={signal:WR},s.baseline={value:"top"},c="x",f="y"),s[c]=l[c]={signal:d+"datum."+gR,mult:u},s[f]=l[f]=a,a.offset=nO(t.labelOffset,e.gradientLabelOffset)||0,h=h?{separation:i("labelSeparation"),method:h,order:"datum."+hR}:void 0,PR({type:NR,role:kz,style:vR,key:yR,from:r,encode:p,overlap:h},n)}function GR(t,e,n,r,i){const o=OR(t,e),a=n.entries,u=!(!a||!a.interactive),s=a?a.name:void 0,l=o("clipHeight"),c=o("symbolOffset"),f={data:"value"},h=`(${i}) ? datum.${pR} : datum.${MR}`,d=l?oz(l):{field:MR},p=`datum.${hR}`,g=`max(1, ${i})`;let m,y,v,_,x;d.mult=.5,m={enter:y={opacity:CR,x:{signal:h,mult:.5,offset:c},y:d},update:v={opacity:FR,x:y.x,y:y.y},exit:{opacity:CR}};let b=null,w=null;t.fill||(b=e.symbolBaseFillColor,w=e.symbolBaseStrokeColor),uz(m,{fill:o("symbolFillColor",b),shape:o("symbolType"),size:o("symbolSize"),stroke:o("symbolStrokeColor",w),strokeDash:o("symbolDash"),strokeDashOffset:o("symbolDashOffset"),strokeWidth:o("symbolStrokeWidth")},{opacity:o("symbolOpacity")}),ER.forEach((e=>{t[e]&&(v[e]=y[e]={scale:t[e],field:yR})}));const k=PR({type:BR,role:Az,key:yR,from:f,clip:!!l||void 0,encode:m},n.symbols),A=oz(c);A.offset=o("labelOffset"),m={enter:y={opacity:CR,x:{signal:h,offset:A},y:d},update:v={opacity:FR,text:{field:dR},x:y.x,y:y.y},exit:{opacity:CR}},uz(m,{align:o("labelAlign"),baseline:o("labelBaseline"),fill:o("labelColor"),fillOpacity:o("labelOpacity"),font:o("labelFont"),fontSize:o("labelFontSize"),fontStyle:o("labelFontStyle"),fontWeight:o("labelFontWeight"),limit:o("labelLimit")});const M=PR({type:NR,role:kz,style:vR,key:yR,from:f,encode:m},n.labels);return m={enter:{noBound:{value:!l},width:CR,height:l?oz(l):CR,opacity:CR},exit:{opacity:CR},update:v={opacity:FR,row:{signal:null},column:{signal:null}}},o.isVertical(!0)?(_=`ceil(item.mark.items.length / ${g})`,v.row.signal=`${p}%${_}`,v.column.signal=`floor(${p} / ${_})`,x={field:["row",p]}):(v.row.signal=`floor(${p} / ${g})`,v.column.signal=`${p} % ${g}`,x={field:p}),v.column.signal=`(${i})?${v.column.signal}:${p}`,zR({role:hz,from:r={facet:{data:r,name:"value",groupby:hR}},encode:sz(m,a,DR),marks:[k,M],name:s,interactive:u,sort:x})}const VR='item.orient === "left"',XR='item.orient === "right"',JR=`(${VR} || ${XR})`,ZR=`datum.vgrad && ${JR}`,QR=UR('"top"','"bottom"','"middle"'),KR=`datum.vgrad && ${XR} ? (${UR('"right"','"left"','"center"')}) : (${JR} && !(datum.vgrad && ${VR})) ? "left" : ${LR}`,tU=`item._anchor || (${JR} ? "middle" : "start")`,eU=`${ZR} ? (${VR} ? -90 : 90) : 0`,nU=`${JR} ? (datum.vgrad ? (${XR} ? "bottom" : "top") : ${QR}) : "top"`;function rU(t,e){let n;return A(t)&&(t.signal?n=t.signal:t.path?n="pathShape("+iU(t.path)+")":t.sphere&&(n="geoShape("+iU(t.sphere)+', {type: "Sphere"})')),n?e.signalRef(n):!!t}function iU(t){return A(t)&&t.signal?t.signal:Ft(t)}function oU(t){const e=t.role||"";return e.indexOf("axis")&&e.indexOf("legend")&&e.indexOf("title")?t.type===SR?hz:e||cz:e}function aU(t){return{marktype:t.type,name:t.name||void 0,role:t.role||oU(t),zindex:+t.zindex||void 0,aria:t.aria,description:t.description}}function uU(t,e){return t&&t.signal?e.signalRef(t.signal):!1!==t}function sU(t,e){const n=tu(t.type);n||u("Unrecognized transform type: "+Ft(t.type));const r=Wz(n.type.toLowerCase(),null,lU(n,t,e));return t.signal&&e.addSignal(t.signal,e.proxy(r)),r.metadata=n.metadata||{},r}function lU(t,e,n){const r={},i=t.params.length;for(let o=0;o<i;++o){const i=t.params[o];r[i.name]=cU(i,e,n)}return r}function cU(t,e,n){const r=t.type,i=e[t.name];return"index"===r?function(t,e,n){xt(e.from)||u('Lookup "from" parameter must be a string literal.');return n.getData(e.from).lookupRef(n,e.key)}(0,e,n):void 0!==i?"param"===r?function(t,e,n){const r=e[t.name];return t.array?(k(r)||u("Expected an array of sub-parameters. Instead: "+Ft(r)),r.map((e=>hU(t,e,n)))):hU(t,r,n)}(t,e,n):"projection"===r?n.projectionRef(e[t.name]):t.array&&!tO(i)?i.map((e=>fU(t,e,n))):fU(t,i,n):void(t.required&&u("Missing required "+Ft(e.type)+" parameter: "+Ft(t.name)))}function fU(t,e,n){const r=t.type;if(tO(e))return mU(r)?u("Expression references can not be signals."):yU(r)?n.fieldRef(e):vU(r)?n.compareRef(e):n.signalRef(e.signal);{const i=t.expr||yU(r);return i&&dU(e)?n.exprRef(e.expr,e.as):i&&pU(e)?Gz(e.field,e.as):mU(r)?DB(e,n):gU(r)?Yz(n.getData(e).values):yU(r)?Gz(e):vU(r)?n.compareRef(e):e}}function hU(t,e,n){const r=t.params.length;let i;for(let n=0;n<r;++n){i=t.params[n];for(const t in i.key)if(i.key[t]!==e[t]){i=null;break}if(i)break}i||u("Unsupported parameter: "+Ft(e));const o=ot(lU(i,e,n),i.key);return Yz(n.add($O(o)))}const dU=t=>t&&t.expr,pU=t=>t&&t.field,gU=t=>"data"===t,mU=t=>"expr"===t,yU=t=>"field"===t,vU=t=>"compare"===t;function _U(t,e){return t.$ref?t:t.data&&t.data.$ref?t.data:Yz(e.getData(t.data).output)}function xU(t,e,n,r,i){this.scope=t,this.input=e,this.output=n,this.values=r,this.aggregate=i,this.index={}}function bU(t){return xt(t)?t:null}function wU(t,e,n){const r=Zz(n.op,n.field);let i;if(e.ops){for(let t=0,n=e.as.length;t<n;++t)if(e.as[t]===r)return}else e.ops=["count"],e.fields=[null],e.as=["count"];n.op&&(e.ops.push((i=n.op.signal)?t.signalRef(i):n.op),e.fields.push(t.fieldRef(n.field)),e.as.push(r))}function kU(t,e,n,r,i,o,a){const u=e[n]||(e[n]={}),s=function(t){return A(t)?(t.order===Jz?"-":"+")+Zz(t.op,t.field):""}(o);let l,c,f=bU(i);if(null!=f&&(t=e.scope,f+=s?"|"+s:"",l=u[f]),!l){const n=o?{field:Vz,pulse:e.countsRef(t,i,o)}:{field:t.fieldRef(i),pulse:Yz(e.output)};s&&(n.sort=t.sortRef(o)),c=t.add(Wz(r,void 0,n)),a&&(e.index[i]=c),l=Yz(c),null!=f&&(u[f]=l)}return l}function AU(t,e,n){const r=t.remove,i=t.insert,o=t.toggle,a=t.modify,u=t.values,s=e.add(Hz()),l=DB("if("+t.trigger+',modify("'+n+'",'+[i,r,o,a,u].map((t=>null==t?"null":t)).join(",")+"),0)",e);s.update=l.$expr,s.params=l.$params}function MU(t,e){const n=oU(t),r=t.type===SR,i=t.from&&t.from.facet,o=t.overlap;let a,s,l,c,f,h,d,p=t.layout||n===hz||n===fz;const g=n===cz||p||i,m=function(t,e,n){let r,i,o,a,s;return t?(r=t.facet)&&(e||u("Only group marks can be faceted."),null!=r.field?a=s=_U(r,n):(t.data?s=Yz(n.getData(t.data).aggregate):(o=sU(ot({type:"aggregate",groupby:V(r.groupby)},r.aggregate),n),o.params.key=n.keyRef(r.groupby),o.params.pulse=_U(r,n),a=s=Yz(n.add(o))),i=n.keyRef(r.groupby,!0))):a=Yz(n.add(yO(null,[{}]))),a||(a=_U(t,n)),{key:i,pulse:a,parent:s}}(t.from,r,e);s=e.add(_O({key:m.key||(t.key?Gz(t.key):void 0),pulse:m.pulse,clean:!r}));const y=Yz(s);s=l=e.add(yO({pulse:y})),s=e.add(DO({markdef:aU(t),interactive:uU(t.interactive,e),clip:rU(t.clip,e),context:{$context:!0},groups:e.lookup(),parent:e.signals.parent?e.signalRef("parent"):null,index:e.markpath(),pulse:Yz(s)}));const v=Yz(s);s=c=e.add(xO(Oz(t.encode,t.type,n,t.style,e,{mod:!1,pulse:v}))),s.params.parent=e.encode(),t.transform&&t.transform.forEach((t=>{const n=sU(t,e),r=n.metadata;(r.generates||r.changes)&&u("Mark transforms should not generate new data."),r.nomod||(c.params.mod=!0),n.params.pulse=Yz(s),e.add(s=n)})),t.sort&&(s=e.add(LO({sort:e.compareRef(t.sort),pulse:Yz(s)})));const _=Yz(s);(i||p)&&(p=e.add(qO({layout:e.objectProperty(t.layout),legends:e.legends,mark:v,pulse:_})),h=Yz(p));const x=e.add(mO({mark:v,pulse:h||_}));d=Yz(x),r&&(g&&(a=e.operators,a.pop(),p&&a.pop()),e.pushState(_,h||d,y),i?function(t,e,n){const r=t.from.facet,i=r.name,o=_U(r,e);let a;r.name||u("Facet must have a name: "+Ft(r)),r.data||u("Facet must reference a data set: "+Ft(r)),r.field?a=e.add(TO({field:e.fieldRef(r.field),pulse:o})):r.groupby?a=e.add(wO({key:e.keyRef(r.groupby),group:Yz(e.proxy(n.parent)),pulse:o})):u("Facet must specify groupby or field: "+Ft(r));const s=e.fork(),l=s.add(yO()),c=s.add(UO({pulse:Yz(l)}));s.addData(i,new xU(s,l,l,c)),s.addSignal("parent",null),a.params.subflow={$subflow:s.parse(t).toRuntime()}}(t,e,m):g?function(t,e,n){const r=e.add(TO({pulse:n.pulse})),i=e.fork();i.add(UO()),i.addSignal("parent",null),r.params.subflow={$subflow:i.parse(t).toRuntime()}}(t,e,m):e.parse(t),e.popState(),g&&(p&&a.push(p),a.push(x))),o&&(d=function(t,e,n){const r=t.method,i=t.bound,o=t.separation,a={separation:tO(o)?n.signalRef(o.signal):o,method:tO(r)?n.signalRef(r.signal):r,pulse:e};t.order&&(a.sort=n.compareRef({field:t.order}));if(i){const t=i.tolerance;a.boundTolerance=tO(t)?n.signalRef(t.signal):+t,a.boundScale=n.scaleRef(i.scale),a.boundOrient=i.orient}return Yz(n.add(SO(a)))}(o,d,e));const b=e.add(OO({pulse:d})),w=e.add(UO({pulse:Yz(b)},void 0,e.parent()));null!=t.name&&(f=t.name,e.addData(f,new xU(e,l,b,w)),t.on&&t.on.forEach((t=>{(t.insert||t.remove||t.toggle)&&u("Marks only support modify triggers."),AU(t,e,f)})))}function EU(t,e){const n=e.config.legend,r=t.encode||{},i=OR(t,n),o=r.legend||{},a=o.name||void 0,s=o.interactive,l=o.style,c={};let f,h,d,p=0;ER.forEach((e=>t[e]?(c[e]=t[e],p=p||t[e]):0)),p||u("Missing valid scale for legend.");const g=function(t,e){let n=t.type||wR;t.type||1!==function(t){return ER.reduce(((e,n)=>e+(t[n]?1:0)),0)}(t)||!t.fill&&!t.stroke||(n=op(e)?kR:up(e)?AR:wR);return n!==kR?n:up(e)?AR:kR}(t,e.scaleType(p)),m={title:null!=t.title,scales:c,type:g,vgrad:"symbol"!==g&&i.isVertical()},y=Yz(e.add(yO(null,[m]))),v=Yz(e.add(MO(h={type:g,scale:e.scaleRef(p),count:e.objectProperty(i("tickCount")),limit:e.property(i("symbolLimit")),values:e.objectProperty(t.values),minstep:e.property(t.tickMinStep),formatType:e.property(t.formatType),formatSpecifier:e.property(t.format)})));return g===kR?(d=[jR(t,p,n,r.gradient),YR(t,n,r.labels,v)],h.count=h.count||e.signalRef(`max(2,2*floor((${rO(i.gradientLength())})/100))`)):g===AR?d=[IR(t,p,n,r.gradient,v),YR(t,n,r.labels,v)]:(f=function(t,e){const n=OR(t,e);return{align:n("gridAlign"),columns:n.entryColumns(),center:{row:!0,column:!1},padding:{row:n("rowPadding"),column:n("columnPadding")}}}(t,n),d=[GR(t,n,r,v,rO(f.columns))],h.size=function(t,e,n){const r=rO(CU("size",t,n)),i=rO(CU("strokeWidth",t,n)),o=rO(function(t,e,n){return RR("fontSize",t)||function(t,e,n){const r=e.config.style[n];return r&&r[t]}("fontSize",e,n)}(n[1].encode,e,vR));return DB(`max(ceil(sqrt(${r})+${i}),${o})`,e)}(t,e,d[0].marks)),d=[zR({role:bz,from:y,encode:{enter:{x:{value:0},y:{value:0}}},marks:d,layout:f,interactive:s})],m.title&&d.push(function(t,e,n,r){const i=OR(t,e),o={enter:{opacity:CR},update:{opacity:FR,x:{field:{group:"padding"}},y:{field:{group:"padding"}}},exit:{opacity:CR}};return uz(o,{orient:i("titleOrient"),_anchor:i("titleAnchor"),anchor:{signal:tU},angle:{signal:eU},align:{signal:KR},baseline:{signal:nU},text:t.title,fill:i("titleColor"),fillOpacity:i("titleOpacity"),font:i("titleFont"),fontSize:i("titleFontSize"),fontStyle:i("titleFontStyle"),fontWeight:i("titleFontWeight"),limit:i("titleLimit"),lineHeight:i("titleLineHeight")},{align:i("titleAlign"),baseline:i("titleBaseline")}),PR({type:NR,role:Mz,style:_R,from:r,encode:o},n)}(t,n,r.title,y)),MU(zR({role:_z,from:y,encode:sz(DU(i,t,n),o,DR),marks:d,aria:i("aria"),description:i("description"),zindex:i("zindex"),name:a,interactive:s,style:l}),e)}function DU(t,e,n){const r={enter:{},update:{}};return uz(r,{orient:t("orient"),offset:t("offset"),padding:t("padding"),titlePadding:t("titlePadding"),cornerRadius:t("cornerRadius"),fill:t("fillColor"),stroke:t("strokeColor"),strokeWidth:n.strokeWidth,strokeDash:n.strokeDash,x:t("legendX"),y:t("legendY"),format:e.format,formatType:e.formatType}),r}function CU(t,e,n){return e[t]?`scale("${e[t]}",datum)`:RR(t,n[0].encode)}xU.fromEntries=function(t,e){const n=e.length,r=e[n-1],i=e[n-2];let o=e[0],a=null,u=1;for(o&&"load"===o.type&&(o=e[1]),t.add(e[0]);u<n;++u)e[u].params.pulse=Yz(e[u-1]),t.add(e[u]),"aggregate"===e[u].type&&(a=e[u]);return new xU(t,o,i,r,a)},xU.prototype={countsRef(t,e,n){const r=this,i=r.counts||(r.counts={}),o=bU(e);let a,u,s;return null!=o&&(t=r.scope,a=i[o]),a?n&&n.field&&wU(t,a.agg.params,n):(s={groupby:t.fieldRef(e,"key"),pulse:Yz(r.output)},n&&n.field&&wU(t,s,n),u=t.add(pO(s)),a=t.add(yO({pulse:Yz(u)})),a={agg:u,ref:Yz(a)},null!=o&&(i[o]=a)),a.ref},tuplesRef(){return Yz(this.values)},extentRef(t,e){return kU(t,this,"extent","extent",e,!1)},domainRef(t,e){return kU(t,this,"domain","values",e,!1)},valuesRef(t,e,n){return kU(t,this,"vals","values",e,n||!0)},lookupRef(t,e){return kU(t,this,"lookup","tupleindex",e,!1)},indataRef(t,e){return kU(t,this,"indata","tupleindex",e,!0,!0)}};const FU=`item.orient==="${oR}"?-90:item.orient==="${aR}"?90:0`;function SU(t,e){const n=OR(t=xt(t)?{text:t}:t,e.config.title),r=t.encode||{},i=r.group||{},o=i.name||void 0,a=i.interactive,u=i.style,s=[],l=Yz(e.add(yO(null,[{}])));return s.push(function(t,e,n,r){const i={value:0},o=t.text,a={enter:{opacity:i},update:{opacity:{value:1}},exit:{opacity:i}};return uz(a,{text:o,align:{signal:"item.mark.group.align"},angle:{signal:"item.mark.group.angle"},limit:{signal:"item.mark.group.limit"},baseline:"top",dx:e("dx"),dy:e("dy"),fill:e("color"),font:e("font"),fontSize:e("fontSize"),fontStyle:e("fontStyle"),fontWeight:e("fontWeight"),lineHeight:e("lineHeight")},{align:e("align"),angle:e("angle"),baseline:e("baseline")}),PR({type:NR,role:Dz,style:xR,from:r,encode:a},n)}(t,n,function(t){const e=t.encode;return e&&e.title||ot({name:t.name,interactive:t.interactive,style:t.style},e)}(t),l)),t.subtitle&&s.push(function(t,e,n,r){const i={value:0},o=t.subtitle,a={enter:{opacity:i},update:{opacity:{value:1}},exit:{opacity:i}};return uz(a,{text:o,align:{signal:"item.mark.group.align"},angle:{signal:"item.mark.group.angle"},limit:{signal:"item.mark.group.limit"},baseline:"top",dx:e("dx"),dy:e("dy"),fill:e("subtitleColor"),font:e("subtitleFont"),fontSize:e("subtitleFontSize"),fontStyle:e("subtitleFontStyle"),fontWeight:e("subtitleFontWeight"),lineHeight:e("subtitleLineHeight")},{align:e("align"),angle:e("angle"),baseline:e("baseline")}),PR({type:NR,role:Cz,style:bR,from:r,encode:a},n)}(t,n,r.subtitle,l)),MU(zR({role:Ez,from:l,encode:$U(n,i),marks:s,aria:n("aria"),description:n("description"),zindex:n("zindex"),name:o,interactive:a,style:u}),e)}function $U(t,e){const n={enter:{},update:{}};return uz(n,{orient:t("orient"),anchor:t("anchor"),align:{signal:LR},angle:{signal:FU},limit:t("limit"),frame:t("frame"),offset:t("offset")||0,padding:t("subtitlePadding")}),sz(n,e,DR)}function TU(t,e){const n=[];t.transform&&t.transform.forEach((t=>{n.push(sU(t,e))})),t.on&&t.on.forEach((n=>{AU(n,e,t.name)})),e.addDataPipeline(t.name,function(t,e,n){const r=[];let i,o,a,u,s,l=null,c=!1,f=!1;t.values?tO(t.values)||eO(t.format)?(r.push(NU(e,t)),r.push(l=BU())):r.push(l=BU({$ingest:t.values,$format:t.format})):t.url?eO(t.url)||eO(t.format)?(r.push(NU(e,t)),r.push(l=BU())):r.push(l=BU({$request:t.url,$format:t.format})):t.source&&(l=i=V(t.source).map((t=>Yz(e.getData(t).output))),r.push(null));for(o=0,a=n.length;o<a;++o)u=n[o],s=u.metadata,l||s.source||r.push(l=BU()),r.push(u),s.generates&&(f=!0),s.modifies&&!f&&(c=!0),s.source?l=u:s.changes&&(l=null);i&&(a=i.length-1,r[0]=zO({derive:c,pulse:a?i:i[0]}),(c||a)&&r.splice(1,0,BU()));l||r.push(BU());return r.push(UO({})),r}(t,e,n))}function BU(t){const e=yO({},t);return e.metadata={source:!0},e}function NU(t,e){return EO({url:e.url?t.property(e.url):void 0,async:e.async?t.property(e.async):void 0,values:e.values?t.property(e.values):void 0,format:t.objectProperty(e.format)})}const zU=t=>t===uR||t===iR,OU=(t,e,n)=>tO(t)?IU(t.signal,e,n):t===oR||t===iR?e:n,RU=(t,e,n)=>tO(t)?PU(t.signal,e,n):zU(t)?e:n,UU=(t,e,n)=>tO(t)?jU(t.signal,e,n):zU(t)?n:e,LU=(t,e,n)=>tO(t)?WU(t.signal,e,n):t===iR?{value:e}:{value:n},qU=(t,e,n)=>tO(t)?HU(t.signal,e,n):t===aR?{value:e}:{value:n},PU=(t,e,n)=>YU(`${t} === '${iR}' || ${t} === '${uR}'`,e,n),jU=(t,e,n)=>YU(`${t} !== '${iR}' && ${t} !== '${uR}'`,e,n),IU=(t,e,n)=>VU(`${t} === '${oR}' || ${t} === '${iR}'`,e,n),WU=(t,e,n)=>VU(`${t} === '${iR}'`,e,n),HU=(t,e,n)=>VU(`${t} === '${aR}'`,e,n),YU=(t,e,n)=>(e=null!=e?oz(e):e,n=null!=n?oz(n):n,GU(e)&&GU(n)?{signal:`${t} ? (${e=e?e.signal||Ft(e.value):null}) : (${n=n?n.signal||Ft(n.value):null})`}:[ot({test:t},e)].concat(n||[])),GU=t=>null==t||1===Object.keys(t).length,VU=(t,e,n)=>({signal:`${t} ? (${JU(e)}) : (${JU(n)})`}),XU=(t,e,n,r,i)=>({signal:(null!=r?`${t} === '${oR}' ? (${JU(r)}) : `:"")+(null!=n?`${t} === '${uR}' ? (${JU(n)}) : `:"")+(null!=i?`${t} === '${aR}' ? (${JU(i)}) : `:"")+(null!=e?`${t} === '${iR}' ? (${JU(e)}) : `:"")+"(null)"}),JU=t=>tO(t)?t.signal:null==t?null:Ft(t),ZU=(t,e)=>0===e?0:tO(t)?{signal:`(${t.signal}) * ${e}`}:{value:t*e},QU=(t,e)=>{const n=t.signal;return n&&n.endsWith("(null)")?{signal:n.slice(0,-6)+e.signal}:t};function KU(t,e,n,r){let i;if(e&&lt(e,t))return e[t];if(lt(n,t))return n[t];if(t.startsWith("title")){switch(t){case"titleColor":i="fill";break;case"titleFont":case"titleFontSize":case"titleFontWeight":i=t[5].toLowerCase()+t.slice(6)}return r[_R][i]}if(t.startsWith("label")){switch(t){case"labelColor":i="fill";break;case"labelFont":case"labelFontSize":i=t[5].toLowerCase()+t.slice(6)}return r[vR][i]}return null}function tL(t){const e={};for(const n of t)if(n)for(const t in n)e[t]=1;return Object.keys(e)}function eL(t,e){return{scale:t.scale,range:e}}function nL(t,e,n,r,i){const o=OR(t,e),a=t.orient,u=t.gridScale,s=OU(a,1,-1),l=function(t,e){if(1===e);else if(A(t)){let n=t=ot({},t);for(;null!=n.mult;){if(!A(n.mult))return n.mult=tO(e)?{signal:`(${n.mult}) * (${e.signal})`}:n.mult*e,t;n=n.mult=ot({},n.mult)}n.mult=e}else t=tO(e)?{signal:`(${e.signal}) * (${t||0})`}:e*(t||0);return t}(t.offset,s);let c,f,h;const d={enter:c={opacity:CR},update:h={opacity:FR},exit:f={opacity:CR}};uz(d,{stroke:o("gridColor"),strokeCap:o("gridCap"),strokeDash:o("gridDash"),strokeDashOffset:o("gridDashOffset"),strokeOpacity:o("gridOpacity"),strokeWidth:o("gridWidth")});const p={scale:t.scale,field:yR,band:i.band,extra:i.extra,offset:i.offset,round:o("tickRound")},g=RU(a,{signal:"height"},{signal:"width"}),m=u?{scale:u,range:0,mult:s,offset:l}:{value:0,offset:l},y=u?{scale:u,range:1,mult:s,offset:l}:ot(g,{mult:s,offset:l});return c.x=h.x=RU(a,p,m),c.y=h.y=UU(a,p,m),c.x2=h.x2=UU(a,y),c.y2=h.y2=RU(a,y),f.x=RU(a,p),f.y=UU(a,p),PR({type:TR,role:gz,key:yR,from:r,encode:d},n)}function rL(t,e,n,r,i){return{signal:'flush(range("'+t+'"), scale("'+t+'", datum.value), '+e+","+n+","+r+","+i+")"}}function iL(t,e,n,r){const i=OR(t,e),o=t.orient,a=OU(o,-1,1);let u,s;const l={enter:u={opacity:CR,anchor:oz(i("titleAnchor",null)),align:{signal:LR}},update:s=ot({},u,{opacity:FR,text:oz(t.title)}),exit:{opacity:CR}},c={signal:`lerp(range("${t.scale}"), ${UR(0,1,.5)})`};return s.x=RU(o,c),s.y=UU(o,c),u.angle=RU(o,CR,ZU(a,90)),u.baseline=RU(o,LU(o,uR,iR),{value:uR}),s.angle=u.angle,s.baseline=u.baseline,uz(l,{fill:i("titleColor"),fillOpacity:i("titleOpacity"),font:i("titleFont"),fontSize:i("titleFontSize"),fontStyle:i("titleFontStyle"),fontWeight:i("titleFontWeight"),limit:i("titleLimit"),lineHeight:i("titleLineHeight")},{align:i("titleAlign"),angle:i("titleAngle"),baseline:i("titleBaseline")}),function(t,e,n,r){const i=(t,e)=>null!=t?(n.update[e]=QU(oz(t),n.update[e]),!1):!lz(e,r),o=i(t("titleX"),"x"),a=i(t("titleY"),"y");n.enter.auto=a===o?oz(a):RU(e,oz(a),oz(o))}(i,o,l,n),l.update.align=QU(l.update.align,u.align),l.update.angle=QU(l.update.angle,u.angle),l.update.baseline=QU(l.update.baseline,u.baseline),PR({type:NR,role:vz,style:_R,from:r,encode:l},n)}function oL(t,e){const n=function(t,e){var n,r,i,o=e.config,a=o.style,u=o.axis,s="band"===e.scaleType(t.scale)&&o.axisBand,l=t.orient;if(tO(l)){const t=tL([o.axisX,o.axisY]),e=tL([o.axisTop,o.axisBottom,o.axisLeft,o.axisRight]);for(i of(n={},t))n[i]=RU(l,KU(i,o.axisX,u,a),KU(i,o.axisY,u,a));for(i of(r={},e))r[i]=XU(l.signal,KU(i,o.axisTop,u,a),KU(i,o.axisBottom,u,a),KU(i,o.axisLeft,u,a),KU(i,o.axisRight,u,a))}else n=l===iR||l===uR?o.axisX:o.axisY,r=o["axis"+l[0].toUpperCase()+l.slice(1)];return n||r||s?ot({},u,n,r,s):u}(t,e),r=t.encode||{},i=r.axis||{},o=i.name||void 0,a=i.interactive,u=i.style,s=OR(t,n),l=function(t){const e=t("tickBand");let n,r,i=t("tickOffset");return e?e.signal?(n={signal:`(${e.signal}) === 'extent' ? 1 : 0.5`},r={signal:`(${e.signal}) === 'extent'`},A(i)||(i={signal:`(${e.signal}) === 'extent' ? 0 : ${i}`})):"extent"===e?(n=1,r=!0,i=0):(n=.5,r=!1):(n=t("bandPosition"),r=t("tickExtra")),{extra:r,band:n,offset:i}}(s),c={scale:t.scale,ticks:!!s("ticks"),labels:!!s("labels"),grid:!!s("grid"),domain:!!s("domain"),title:null!=t.title},f=Yz(e.add(yO({},[c]))),h=Yz(e.add(gO({scale:e.scaleRef(t.scale),extra:e.property(l.extra),count:e.objectProperty(t.tickCount),values:e.objectProperty(t.values),minstep:e.property(t.tickMinStep),formatType:e.property(t.formatType),formatSpecifier:e.property(t.format)}))),d=[];let p;return c.grid&&d.push(nL(t,n,r.grid,h,l)),c.ticks&&(p=s("tickSize"),d.push(function(t,e,n,r,i,o){const a=OR(t,e),u=t.orient,s=OU(u,-1,1);let l,c,f;const h={enter:l={opacity:CR},update:f={opacity:FR},exit:c={opacity:CR}};uz(h,{stroke:a("tickColor"),strokeCap:a("tickCap"),strokeDash:a("tickDash"),strokeDashOffset:a("tickDashOffset"),strokeOpacity:a("tickOpacity"),strokeWidth:a("tickWidth")});const d=oz(i);d.mult=s;const p={scale:t.scale,field:yR,band:o.band,extra:o.extra,offset:o.offset,round:a("tickRound")};return f.y=l.y=RU(u,CR,p),f.y2=l.y2=RU(u,d),c.x=RU(u,p),f.x=l.x=UU(u,CR,p),f.x2=l.x2=UU(u,d),c.y=UU(u,p),PR({type:TR,role:yz,key:yR,from:r,encode:h},n)}(t,n,r.ticks,h,p,l))),c.labels&&(p=c.ticks?p:0,d.push(function(t,e,n,r,i,o){const a=OR(t,e),u=t.orient,s=t.scale,l=OU(u,-1,1),c=rO(a("labelFlush")),f=rO(a("labelFlushOffset")),h=a("labelAlign"),d=a("labelBaseline");let p,g=0===c||!!c;const m=oz(i);m.mult=l,m.offset=oz(a("labelPadding")||0),m.offset.mult=l;const y={scale:s,field:yR,band:.5,offset:qR(o.offset,a("labelOffset"))},v=RU(u,g?rL(s,c,'"left"','"right"','"center"'):{value:"center"},qU(u,"left","right")),_=RU(u,LU(u,"bottom","top"),g?rL(s,c,'"top"','"bottom"','"middle"'):{value:"middle"}),x=rL(s,c,`-(${f})`,f,0);g=g&&f;const b={opacity:CR,x:RU(u,y,m),y:UU(u,y,m)},w={enter:b,update:p={opacity:FR,text:{field:dR},x:b.x,y:b.y,align:v,baseline:_},exit:{opacity:CR,x:b.x,y:b.y}};uz(w,{dx:!h&&g?RU(u,x):null,dy:!d&&g?UU(u,x):null}),uz(w,{angle:a("labelAngle"),fill:a("labelColor"),fillOpacity:a("labelOpacity"),font:a("labelFont"),fontSize:a("labelFontSize"),fontWeight:a("labelFontWeight"),fontStyle:a("labelFontStyle"),limit:a("labelLimit"),lineHeight:a("labelLineHeight")},{align:h,baseline:d});const k=a("labelBound");let A=a("labelOverlap");return A=A||k?{separation:a("labelSeparation"),method:A,order:"datum.index",bound:k?{scale:s,orient:u,tolerance:k}:null}:void 0,p.align!==v&&(p.align=QU(p.align,v)),p.baseline!==_&&(p.baseline=QU(p.baseline,_)),PR({type:NR,role:mz,style:vR,key:yR,from:r,encode:w,overlap:A},n)}(t,n,r.labels,h,p,l))),c.domain&&d.push(function(t,e,n,r){const i=OR(t,e),o=t.orient;let a,u;const s={enter:a={opacity:CR},update:u={opacity:FR},exit:{opacity:CR}};uz(s,{stroke:i("domainColor"),strokeCap:i("domainCap"),strokeDash:i("domainDash"),strokeDashOffset:i("domainDashOffset"),strokeWidth:i("domainWidth"),strokeOpacity:i("domainOpacity")});const l=eL(t,0),c=eL(t,1);return a.x=u.x=RU(o,l,CR),a.x2=u.x2=RU(o,c),a.y=u.y=UU(o,l,CR),a.y2=u.y2=UU(o,c),PR({type:TR,role:pz,from:r,encode:s},n)}(t,n,r.domain,f)),c.title&&d.push(iL(t,n,r.title,f)),MU(zR({role:dz,from:f,encode:sz(aL(s,t),i,DR),marks:d,aria:s("aria"),description:s("description"),zindex:s("zindex"),name:o,interactive:a,style:u}),e)}function aL(t,e){const n={enter:{},update:{}};return uz(n,{orient:t("orient"),offset:t("offset")||0,position:nO(e.position,0),titlePadding:t("titlePadding"),minExtent:t("minExtent"),maxExtent:t("maxExtent"),range:{signal:`abs(span(range("${e.scale}")))`},translate:t("translate"),format:e.format,formatType:e.formatType}),n}function uL(t,e,n){const r=V(t.signals),i=V(t.scales);return n||r.forEach((t=>jz(t,e))),V(t.projections).forEach((t=>function(t,e){const n=e.config.projection||{},r={};for(const n in t)"name"!==n&&(r[n]=rR(t[n],n,e));for(const t in n)null==r[t]&&(r[t]=rR(n[t],t,e));e.addProjection(t.name,r)}(t,e))),i.forEach((t=>function(t,e){const n=t.type||"linear";rp(n)||u("Unrecognized scale type: "+Ft(n)),e.addScale(t.name,{type:n,domain:void 0})}(t,e))),V(t.data).forEach((t=>TU(t,e))),i.forEach((t=>WO(t,e))),(n||r).forEach((t=>function(t,e){const n=e.getSignal(t.name);let r=t.update;t.init&&(r?u("Signals can not include both init and update expressions."):(r=t.init,n.initonly=!0)),r&&(r=DB(r,e),n.update=r.$expr,n.params=r.$params),t.on&&t.on.forEach((t=>fO(t,e,n.id)))}(t,e))),V(t.axes).forEach((t=>oL(t,e))),V(t.marks).forEach((t=>MU(t,e))),V(t.legends).forEach((t=>EU(t,e))),t.title&&SU(t.title,e),e.parseLambdas(),e}const sL=t=>sz({enter:{x:{value:0},y:{value:0}},update:{width:{signal:"width"},height:{signal:"height"}}},t);function lL(t,e){const n=e.config,r=Yz(e.root=e.add(Hz())),i=function(t,e){const n=n=>nO(t[n],e[n]),r=[cL("background",n("background")),cL("autosize",ez(n("autosize"))),cL("padding",iz(n("padding"))),cL("width",n("width")||0),cL("height",n("height")||0)],i=r.reduce(((t,e)=>(t[e.name]=e,t)),{}),o={};return V(t.signals).forEach((t=>{lt(i,t.name)?t=ot(i[t.name],t):r.push(t),o[t.name]=t})),V(e.signals).forEach((t=>{lt(o,t.name)||lt(i,t.name)||r.push(t)})),r}(t,n);i.forEach((t=>jz(t,e))),e.description=t.description||n.description,e.eventConfig=n.events,e.legends=e.objectProperty(n.legend&&n.legend.layout),e.locale=n.locale;const o=e.add(yO()),a=e.add(xO(Oz(sL(t.encode),SR,fz,t.style,e,{pulse:Yz(o)}))),u=e.add(qO({layout:e.objectProperty(t.layout),legends:e.legends,autosize:e.signalRef("autosize"),mark:r,pulse:Yz(a)}));e.operators.pop(),e.pushState(Yz(a),Yz(u),null),uL(t,e,i),e.operators.push(u);let s=e.add(mO({mark:r,pulse:Yz(u)}));return s=e.add(OO({pulse:Yz(s)})),s=e.add(UO({pulse:Yz(s)})),e.addData("root",new xU(e,o,o,s)),e}function cL(t,e){return e&&e.signal?{name:t,update:e.signal}:{name:t,value:e}}function fL(t,e){this.config=t||{},this.options=e||{},this.bindings=[],this.field={},this.signals={},this.lambdas={},this.scales={},this.events={},this.data={},this.streams=[],this.updates=[],this.operators=[],this.eventConfig=null,this.locale=null,this._id=0,this._subid=0,this._nextsub=[0],this._parent=[],this._encode=[],this._lookup=[],this._markpath=[]}function hL(t){this.config=t.config,this.options=t.options,this.legends=t.legends,this.field=Object.create(t.field),this.signals=Object.create(t.signals),this.lambdas=Object.create(t.lambdas),this.scales=Object.create(t.scales),this.events=Object.create(t.events),this.data=Object.create(t.data),this.streams=[],this.updates=[],this.operators=[],this._id=0,this._subid=++t._nextsub[0],this._nextsub=t._nextsub,this._parent=t._parent.slice(),this._encode=t._encode.slice(),this._lookup=t._lookup.slice(),this._markpath=t._markpath}function dL(t){return(k(t)?pL:gL)(t)}function pL(t){const e=t.length;let n="[";for(let r=0;r<e;++r){const e=t[r];n+=(r>0?",":"")+(A(e)?e.signal||dL(e):Ft(e))}return n+"]"}function gL(t){let e,n,r="{",i=0;for(e in t)n=t[e],r+=(++i>1?",":"")+Ft(e)+":"+(A(n)?n.signal||dL(n):Ft(n));return r+"}"}fL.prototype=hL.prototype={parse(t){return uL(t,this)},fork(){return new hL(this)},isSubscope(){return this._subid>0},toRuntime(){return this.finish(),{description:this.description,operators:this.operators,streams:this.streams,updates:this.updates,bindings:this.bindings,eventConfig:this.eventConfig,locale:this.locale}},id(){return(this._subid?this._subid+":":0)+this._id++},add(t){return this.operators.push(t),t.id=this.id(),t.refs&&(t.refs.forEach((e=>{e.$ref=t.id})),t.refs=null),t},proxy(t){const e=t instanceof Iz?Yz(t):t;return this.add(NO({value:e}))},addStream(t){return this.streams.push(t),t.id=this.id(),t},addUpdate(t){return this.updates.push(t),t},finish(){let t,e;for(t in this.root&&(this.root.root=!0),this.signals)this.signals[t].signal=t;for(t in this.scales)this.scales[t].scale=t;function n(t,e,n){let r,i;t&&(r=t.data||(t.data={}),i=r[e]||(r[e]=[]),i.push(n))}for(t in this.data){e=this.data[t],n(e.input,t,"input"),n(e.output,t,"output"),n(e.values,t,"values");for(const r in e.index)n(e.index[r],t,"index:"+r)}return this},pushState(t,e,n){this._encode.push(Yz(this.add(UO({pulse:t})))),this._parent.push(e),this._lookup.push(n?Yz(this.proxy(n)):null),this._markpath.push(-1)},popState(){this._encode.pop(),this._parent.pop(),this._lookup.pop(),this._markpath.pop()},parent(){return F(this._parent)},encode(){return F(this._encode)},lookup(){return F(this._lookup)},markpath(){const t=this._markpath;return++t[t.length-1]},fieldRef(t,e){if(xt(t))return Gz(t,e);t.signal||u("Unsupported field reference: "+Ft(t));const n=t.signal;let r=this.field[n];if(!r){const t={name:this.signalRef(n)};e&&(t.as=e),this.field[n]=r=Yz(this.add(kO(t)))}return r},compareRef(t){let e=!1;const n=t=>tO(t)?(e=!0,this.signalRef(t.signal)):function(t){return t&&t.expr}(t)?(e=!0,this.exprRef(t.expr)):t,r=V(t.field).map(n),i=V(t.order).map(n);return e?Yz(this.add(vO({fields:r,orders:i}))):Xz(r,i)},keyRef(t,e){let n=!1;const r=this.signals;return t=V(t).map((t=>tO(t)?(n=!0,Yz(r[t.signal])):t)),n?Yz(this.add(AO({fields:t,flat:e}))):function(t,e){const n={$key:t};return e&&(n.$flat=!0),n}(t,e)},sortRef(t){if(!t)return t;const e=Zz(t.op,t.field),n=t.order||"ascending";return n.signal?Yz(this.add(vO({fields:e,orders:this.signalRef(n.signal)}))):Xz(e,n)},event(t,e){const n=t+":"+e;if(!this.events[n]){const r=this.id();this.streams.push({id:r,source:t,type:e}),this.events[n]=r}return this.events[n]},hasOwnSignal(t){return lt(this.signals,t)},addSignal(t,e){this.hasOwnSignal(t)&&u("Duplicate signal name: "+Ft(t));const n=e instanceof Iz?e:this.add(Hz(e));return this.signals[t]=n},getSignal(t){return this.signals[t]||u("Unrecognized signal name: "+Ft(t)),this.signals[t]},signalRef(t){return this.signals[t]?Yz(this.signals[t]):(lt(this.lambdas,t)||(this.lambdas[t]=this.add(Hz(null))),Yz(this.lambdas[t]))},parseLambdas(){const t=Object.keys(this.lambdas);for(let e=0,n=t.length;e<n;++e){const n=t[e],r=DB(n,this),i=this.lambdas[n];i.params=r.$params,i.update=r.$expr}},property(t){return t&&t.signal?this.signalRef(t.signal):t},objectProperty(t){return t&&A(t)?this.signalRef(t.signal||dL(t)):t},exprRef(t,e){const n={expr:DB(t,this)};return e&&(n.expr.$name=e),Yz(this.add(bO(n)))},addBinding(t,e){this.bindings||u("Nested signals do not support binding: "+Ft(t)),this.bindings.push(ot({signal:t},e))},addScaleProj(t,e){lt(this.scales,t)&&u("Duplicate scale or projection name: "+Ft(t)),this.scales[t]=this.add(e)},addScale(t,e){this.addScaleProj(t,RO(e))},addProjection(t,e){this.addScaleProj(t,BO(e))},getScale(t){return this.scales[t]||u("Unrecognized scale name: "+Ft(t)),this.scales[t]},scaleRef(t){return Yz(this.getScale(t))},scaleType(t){return this.getScale(t).params.type},projectionRef(t){return this.scaleRef(t)},projectionType(t){return this.scaleType(t)},addData(t,e){return lt(this.data,t)&&u("Duplicate data set name: "+Ft(t)),this.data[t]=e},getData(t){return this.data[t]||u("Undefined data set name: "+Ft(t)),this.data[t]},addDataPipeline(t,e){return lt(this.data,t)&&u("Duplicate data set name: "+Ft(t)),this.addData(t,xU.fromEntries(this,e))}},ot(Ka,kl,Xx,Db,kE,MD,pF,WC,_F,eS,mS,MS),t.Bounds=Hg,t.CanvasHandler=wv,t.CanvasRenderer=Cv,t.DATE=Vn,t.DAY=Xn,t.DAYOFYEAR=Jn,t.Dataflow=Ja,t.Debug=b,t.Error=v,t.EventStream=za,t.Gradient=Vp,t.GroupItem=Gg,t.HOURS=Zn,t.Handler=iv,t.Info=x,t.Item=Yg,t.MILLISECONDS=tr,t.MINUTES=Qn,t.MONTH=Yn,t.Marks=jy,t.MultiPulse=Ha,t.None=y,t.Operator=Ta,t.Parameters=Fa,t.Pulse=Pa,t.QUARTER=Hn,t.RenderType=__,t.Renderer=av,t.ResourceLoader=Vg,t.SECONDS=Kn,t.SVGHandler=Sv,t.SVGRenderer=n_,t.SVGStringRenderer=m_,t.Scenegraph=Jy,t.TIME_UNITS=er,t.Transform=Qa,t.View=BN,t.WEEK=Gn,t.Warn=_,t.YEAR=Wn,t.accessor=e,t.accessorFields=r,t.accessorName=n,t.array=V,t.ascending=K,t.bandwidthNRD=ou,t.bin=au,t.bootstrapCI=uu,t.boundClip=D_,t.boundContext=gm,t.boundItem=Iy,t.boundMark=Hy,t.boundStroke=Zg,t.changeset=Da,t.clampRange=X,t.codegenExpression=gT,t.compare=Q,t.constant=rt,t.cumulativeLogNormal=xu,t.cumulativeNormal=pu,t.cumulativeUniform=Eu,t.dayofyear=sr,t.debounce=it,t.defaultLocale=qo,t.definition=tu,t.densityLogNormal=_u,t.densityNormal=du,t.densityUniform=Mu,t.domChild=tv,t.domClear=ev,t.domCreate=Qy,t.domFind=Ky,t.dotbin=su,t.error=u,t.expressionFunction=EB,t.extend=ot,t.extent=at,t.extentIndex=ut,t.falsy=g,t.fastmap=ft,t.field=l,t.flush=ht,t.font=Ny,t.fontFamily=By,t.fontSize=Cy,t.format=la,t.formatLocale=To,t.formats=ca,t.hasOwnProperty=lt,t.id=c,t.identity=f,t.inferType=na,t.inferTypes=ra,t.ingest=ba,t.inherits=dt,t.inrange=pt,t.interpolate=mp,t.interpolateColors=dp,t.interpolateRange=hp,t.intersect=w_,t.intersectBoxLine=Em,t.intersectPath=wm,t.intersectPoint=km,t.intersectRule=Mm,t.isArray=k,t.isBoolean=gt,t.isDate=mt,t.isFunction=J,t.isIterable=yt,t.isNumber=vt,t.isObject=A,t.isRegExp=_t,t.isString=xt,t.isTuple=va,t.key=bt,t.lerp=wt,t.lineHeight=Fy,t.loader=da,t.locale=Lo,t.logger=w,t.lruCache=At,t.markup=Vv,t.merge=Mt,t.mergeConfig=E,t.multiLineOffset=$y,t.one=d,t.pad=Dt,t.panLinear=R,t.panLog=U,t.panPow=L,t.panSymlog=q,t.parse=function(t,e,n){return A(t)||u("Input Vega specification must be an object."),lL(t,new fL(e=E(function(){const t="sans-serif",e="#4c78a8",n="#000",r="#888",i="#ddd";return{description:"Vega visualization",padding:0,autosize:"pad",background:null,events:{defaults:{allow:["wheel"]}},group:null,mark:null,arc:{fill:e},area:{fill:e},image:null,line:{stroke:e,strokeWidth:2},path:{stroke:e},rect:{fill:e},rule:{stroke:n},shape:{stroke:e},symbol:{fill:e,size:64},text:{fill:n,font:t,fontSize:11},trail:{fill:e,size:2},style:{"guide-label":{fill:n,font:t,fontSize:10},"guide-title":{fill:n,font:t,fontSize:11,fontWeight:"bold"},"group-title":{fill:n,font:t,fontSize:13,fontWeight:"bold"},"group-subtitle":{fill:n,font:t,fontSize:12},point:{size:30,strokeWidth:2,shape:"circle"},circle:{size:30,strokeWidth:2},square:{size:30,strokeWidth:2,shape:"square"},cell:{fill:"transparent",stroke:i},view:{fill:"transparent"}},title:{orient:"top",anchor:"middle",offset:4,subtitlePadding:3},axis:{minExtent:0,maxExtent:200,bandPosition:.5,domain:!0,domainWidth:1,domainColor:r,grid:!1,gridWidth:1,gridColor:i,labels:!0,labelAngle:0,labelLimit:180,labelOffset:0,labelPadding:2,ticks:!0,tickColor:r,tickOffset:0,tickRound:!0,tickSize:5,tickWidth:1,titlePadding:4},axisBand:{tickOffset:-.5},projection:{type:"mercator"},legend:{orient:"right",padding:0,gridAlign:"each",columnPadding:10,rowPadding:2,symbolDirection:"vertical",gradientDirection:"vertical",gradientLength:200,gradientThickness:16,gradientStrokeColor:i,gradientStrokeWidth:0,gradientLabelOffset:2,labelAlign:"left",labelBaseline:"middle",labelLimit:160,labelOffset:4,labelOverlap:!0,symbolLimit:30,symbolType:"circle",symbolSize:100,symbolOffset:0,symbolStrokeWidth:1.5,symbolBaseFillColor:"transparent",symbolBaseStrokeColor:r,titleLimit:180,titleOrient:"top",titlePadding:5,layout:{offset:18,direction:"horizontal",left:{direction:"vertical"},right:{direction:"vertical"}}},range:{category:{scheme:"tableau10"},ordinal:{scheme:"blues"},heatmap:{scheme:"yellowgreenblue"},ramp:{scheme:"blues"},diverging:{scheme:"blueorange",extent:[1,0]},symbol:["circle","square","triangle-up","cross","diamond","triangle-right","triangle-down","triangle-left"]}}}(),e,t.config),n)).toRuntime()},t.parseExpression=hT,t.parseSelector=ZN,t.path=Il,t.pathCurves=Jp,t.pathEqual=S_,t.pathParse=ng,t.pathRectangle=Dg,t.pathRender=pg,t.pathSymbols=vg,t.pathTrail=Cg,t.peek=F,t.point=rv,t.projection=HM,t.quantileLogNormal=bu,t.quantileNormal=gu,t.quantileUniform=Du,t.quantiles=ru,t.quantizeInterpolator=pp,t.quarter=Y,t.quartiles=iu,t.randomInteger=function(e,n){let r,i,o;null==n&&(n=e,e=0);const a={min(t){return arguments.length?(r=t||0,o=i-r,a):r},max(t){return arguments.length?(i=t||0,o=i-r,a):i},sample:()=>r+Math.floor(o*t.random()),pdf:t=>t===Math.floor(t)&&t>=r&&t<i?1/o:0,cdf(t){const e=Math.floor(t);return e<r?0:e>=i?1:(e-r+1)/o},icdf:t=>t>=0&&t<=1?r-1+Math.floor(t*o):NaN};return a.min(e).max(n)},t.randomKDE=yu,t.randomLCG=function(t){return function(){return(t=(1103515245*t+12345)%2147483647)/2147483647}},t.randomLogNormal=wu,t.randomMixture=ku,t.randomNormal=mu,t.randomUniform=Cu,t.read=ha,t.regressionConstant=Fu,t.regressionExp=Ou,t.regressionLinear=Nu,t.regressionLoess=Iu,t.regressionLog=zu,t.regressionPoly=Lu,t.regressionPow=Ru,t.regressionQuad=Uu,t.renderModule=b_,t.repeat=Et,t.resetDefaultLocale=function(){return So(),zo(),qo()},t.resetSVGClipId=Ig,t.resetSVGDefIds=function(){Ig(),Ip=0},t.responseType=fa,t.runtimeContext=OB,t.sampleCurve=Gu,t.sampleLogNormal=vu,t.sampleNormal=hu,t.sampleUniform=Au,t.scale=np,t.sceneEqual=F_,t.sceneFromJSON=Vy,t.scenePickVisit=Rm,t.sceneToJSON=Gy,t.sceneVisit=Om,t.sceneZOrder=zm,t.scheme=xp,t.serializeXML=Xv,t.setRandom=function(e){t.random=e},t.span=Ct,t.splitAccessPath=s,t.stringValue=Ft,t.textMetrics=wy,t.timeBin=Qr,t.timeFloor=Ar,t.timeFormatLocale=Ro,t.timeInterval=Sr,t.timeOffset=Br,t.timeSequence=Or,t.timeUnitSpecifier=or,t.timeUnits=rr,t.toBoolean=St,t.toDate=Tt,t.toNumber=S,t.toSet=Nt,t.toString=Bt,t.transform=eu,t.transforms=Ka,t.truncate=zt,t.truthy=p,t.tupleid=_a,t.typeParsers=Ko,t.utcFloor=Dr,t.utcInterval=$r,t.utcOffset=Nr,t.utcSequence=Rr,t.utcdayofyear=pr,t.utcquarter=G,t.utcweek=gr,t.version="5.25.0",t.visitArray=Ot,t.week=lr,t.writeConfig=D,t.zero=h,t.zoomLinear=j,t.zoomLog=I,t.zoomPow=W,t.zoomSymlog=H}));
//# sourceMappingURL=vega.min.js.map
