{"name": "markdown-preview-enhanced", "displayName": "%displayName%", "version": "0.8.18", "description": "%description%", "categories": ["Other"], "keywords": ["markdown"], "bugs": {"url": "https://github.com/shd101wyy/vscode-markdown-preview-enhanced/issues"}, "repository": "https://github.com/shd101wyy/vscode-markdown-preview-enhanced", "license": "NCSA", "contributors": ["shd101wyy", "<PERSON><PERSON><PERSON><PERSON>", "gabyx", "mavaddat"], "publisher": "shd101wyy", "main": "./out/native/extension.js", "browser": "./out/web/extension.js", "scripts": {"build": "gulp copy-files && gulp clean-out && node build.js", "check:all": "yarn check:eslint && yarn check:prettier", "check:eslint": "eslint \"**/*\"", "check:prettier": "prettier --check \"**/*.*\"", "fix:all": "yarn fix:eslint && yarn fix:eslint && yarn fix:prettier", "fix:eslint": "eslint --fix \"**/*\"", "fix:prettier": "prettier --write \"**/*.*\"", "prepare": "husky install", "run-in-browser": "concurrently \"vscode-test-web --browserType=chromium --extensionDevelopmentPath=. $SERVE_DIR\" \"npx http-server ./crossnote -p 6789 --cors\"", "run-in-vscode-dev": "npx serve --cors -l 5000 --ssl-cert $HOME/certs/localhost.pem --ssl-key $HOME/certs/localhost-key.pem", "test": "yarn build && node ./node_modules/vscode/bin/test", "vscode:prepublish": "yarn install && yarn build", "watch": "gulp copy-files && gulp clean-out && node build.js --watch"}, "contributes": {"commands": [{"command": "markdown-preview-enhanced.openPreviewToTheSide", "title": "%markdown-preview-enhanced.openPreviewToTheSide.title%", "category": "<PERSON><PERSON>", "icon": {"light": "./media/preview-right-light.svg", "dark": "./media/preview-right-dark.svg"}}, {"command": "markdown-preview-enhanced.openPreview", "title": "%markdown-preview-enhanced.openPreview.title%", "category": "<PERSON><PERSON>", "icon": {"light": "./media/preview-right-light.svg", "dark": "./media/preview-right-dark.svg"}}, {"command": "markdown-preview-enhanced.toggleScrollSync", "title": "%markdown-preview-enhanced.toggleScrollSync.title%"}, {"command": "markdown-preview-enhanced.toggleLiveUpdate", "title": "%markdown-preview-enhanced.toggleLiveUpdate.title%"}, {"command": "markdown-preview-enhanced.toggleBreakOnSingleNewLine", "title": "%markdown-preview-enhanced.toggleBreakOnSingleNewLine.title%"}, {"command": "markdown-preview-enhanced.openImageHelper", "title": "%markdown-preview-enhanced.openImageHelper.title%"}, {"command": "markdown-preview-enhanced.runAllCodeChunks", "title": "%markdown-preview-enhanced.runAllCodeChunks.title%"}, {"command": "markdown-preview-enhanced.runCodeChunk", "title": "%markdown-preview-enhanced.runCodeChunk.title%"}, {"command": "markdown-preview-enhanced.syncPreview", "title": "%markdown-preview-enhanced.syncPreview.title%"}, {"command": "markdown-preview-enhanced.customizeCss", "title": "%markdown-preview-enhanced.customizeCss.title%", "enablement": "!isWeb"}, {"command": "markdown-preview-enhanced.customizeCssInWorkspace", "title": "%markdown-preview-enhanced.customizeCssInWorkspace.title%"}, {"command": "markdown-preview-enhanced.insertNewSlide", "title": "%markdown-preview-enhanced.insertNewSlide.title%"}, {"command": "markdown-preview-enhanced.insertTable", "title": "%markdown-preview-enhanced.insertTable.title%"}, {"command": "markdown-preview-enhanced.insertPagebreak", "title": "%markdown-preview-enhanced.insertPagebreak.title%"}, {"command": "markdown-preview-enhanced.createTOC", "title": "%markdown-preview-enhanced.createTOC.title%", "enablement": "!isWeb"}, {"command": "markdown-preview-enhanced.openConfigScript", "title": "%markdown-preview-enhanced.openConfigScript.title%", "enablement": "!isWeb"}, {"command": "markdown-preview-enhanced.extendParser", "title": "%markdown-preview-enhanced.extendParser.title%", "enablement": "!isWeb"}, {"command": "markdown-preview-enhanced.customizePreviewHtmlHead", "title": "%markdown-preview-enhanced.customizePreviewHtmlHead.title%", "enablement": "!isWeb"}, {"command": "markdown-preview-enhanced.openConfigScriptInWorkspace", "title": "%markdown-preview-enhanced.openConfigScriptInWorkspace.title%"}, {"command": "markdown-preview-enhanced.extendParserInWorkspace", "title": "%markdown-preview-enhanced.extendParserInWorkspace.title%"}, {"command": "markdown-preview-enhanced.customizePreviewHtmlHeadInWorkspace", "title": "%markdown-preview-enhanced.customizePreviewHtmlHeadInWorkspace.title%"}, {"command": "markdown-preview-enhanced.showUploadedImages", "title": "%markdown-preview-enhanced.showUploadedImages.title%", "enablement": "!isWeb"}], "configuration": {"type": "object", "title": "Markdown Preview Enhanced", "properties": {"markdown-preview-enhanced.configPath": {"markdownDescription": "Restart is required after changes. The global configuration directory path. Leave it empty to use `$HOME/.crossnote` for Windows or `$XDG_CONFIG_HOME/.crossnote` or `$HOME/.local/state/crossnote` as the config path.", "default": "", "type": "string"}, "markdown-preview-enhanced.usePandocParser": {"description": "Enable this option will render markdown by pandoc instead of markdown-it.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.breakOnSingleNewLine": {"description": "In Markdown, a single newline character doesn't cause a line break in the generated HTML. In GitHub Flavored Markdown, that is not true. Enable this config option to insert line breaks in rendered HTML for single newlines in Markdown source.", "default": true, "type": "boolean"}, "markdown-preview-enhanced.scrollSync": {"description": "Automatic scroll sync. This is now partially supported.", "default": true, "type": "boolean"}, "markdown-preview-enhanced.liveUpdate": {"description": "Re-render the preview as the contents of the source changes, without requiring the source buffer to be saved. If disabled, the preview is re-rendered only when the buffer is saved to disk.", "default": true, "type": "boolean"}, "markdown-preview-enhanced.previewMode": {"markdownDescription": "- **Single Preview**: Only one preview will be shown for all editors.\n- **Multiple Previews**: Multiple previews will be shown. Each editor has its own preview.\n- **Previews Only**: No editor will be shown. Only previews will be shown. You can use the in-preview editor to edit the markdown.\n\nRestart is required after changes.", "type": "string", "enum": ["Single Preview", "Multiple Previews", "Previews Only"], "default": "Single Preview"}, "markdown-preview-enhanced.automaticallyShowPreviewOfMarkdownBeingEdited": {"description": "Automatically show preview of markdown being edited.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.previewColorScheme": {"type": "string", "enum": ["selectedPreviewTheme", "systemColorScheme", "editorColorScheme"], "default": "selectedPreviewTheme", "markdownEnumDescriptions": ["Use the `markdown-preview-enhanced.previewTheme` setting.", "Follow system color scheme. If set to true, then the theme of markdown preview will automatically switch between light and dark when your system switch between light and dark. For example, if you set the current preview theme to `github-light.css`, then when your system is dark, the preview theme will be switched to `github-dark.css` automatically. If set to false, then the theme of markdown preview will not be changed automatically.", "Use the same theme as the editor (light or dark). If set to true, then the theme of markdown preview will automatically switch between light and dark when you switch between vscode light and dark theme. For example, if you set the current preview theme to `github-light.css`, then when you switch to vscode dark theme, the preview theme will be switched to `github-dark.css` automatically. If set to false, then the theme of markdown preview will not be changed automatically."]}, "markdown-preview-enhanced.enableTypographer": {"description": "Enable smartypants and other sweet transforms.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.mathRenderingOption": {"description": "Choose the Math expression rendering method here. You can also disable math rendering if you want by choosing 'None'.", "default": "KaTeX", "type": "string", "enum": ["KaTeX", "MathJax", "None"]}, "markdown-preview-enhanced.mathInlineDelimiters": {"description": "Use customized Math expression inline delimiters.", "default": [["$", "$"], ["\\(", "\\)"]], "type": "array"}, "markdown-preview-enhanced.mathBlockDelimiters": {"description": "Use customized Math expression block delimiters.", "default": [["$$", "$$"], ["\\[", "\\]"]], "type": "array"}, "markdown-preview-enhanced.mathRenderingOnlineService": {"description": "Choose the Math expression rendering method option for GFM markdown export (Save as Markdown).", "default": "https://latex.codecogs.com/gif.latex", "type": "string", "enum": ["https://latex.codecogs.com/gif.latex", "https://latex.codecogs.com/svg.latex", "https://latex.codecogs.com/png.latex"]}, "markdown-preview-enhanced.mathjaxV3ScriptSrc": {"description": "MathJax v3 script source. Leave it empty to use the default CDN.", "default": "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "type": "string"}, "markdown-preview-enhanced.enableWikiLinkSyntax": {"description": "Enable Wiki Link syntax support. More information can be found at https://help.github.com/articles/adding-links-to-wikis/", "default": true, "type": "boolean"}, "markdown-preview-enhanced.enableLinkify": {"description": "Enable or disable conversion of URL-like text to links in the markdown preview.", "default": true, "type": "boolean"}, "markdown-preview-enhanced.useGitHubStylePipedLink": {"description": "If checked, we use GitHub style piped wiki links, i.e. [[linkText|wikiLink]]. Otherwise, we use [[wikiLink|linkText]] as the original Wikipedia style.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.enableEmojiSyntax": {"description": "Enable emoji & font-awesome plugin. This only works for markdown-it parser, but not pandoc parser.", "default": true, "type": "boolean"}, "markdown-preview-enhanced.enableExtendedTableSyntax": {"description": "Enable extended table syntax to support merging table cells.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.enableCriticMarkupSyntax": {"description": "Enable CriticMarkup syntax. Only works with markdown-it parser. Please check http://criticmarkup.com/users-guide.php for more information.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.wikiLinkTargetFileExtension": {"markdownDescription": "The file extension for the link in wikilink if the link does not have an extension.", "default": ".md", "type": "string"}, "markdown-preview-enhanced.wikiLinkTargetFileNameChangeCase": {"markdownDescription": "The case for the file name in wikilink. If the value is `none`, then the file name will not be changed. Otherwise, the file name will be transformed to the specified case. You can read https://www.npmjs.com/package/case-anything for more details.", "default": "none", "type": "string", "enum": ["none", "camelCase", "pascalCase", "kebabCase", "snakeCase", "constantCase", "trainCase", "adaCase", "cobolCase", "dotNotation", "pathCase", "spaceCase", "capitalCase", "lowerCase", "upperCase"]}, "markdown-preview-enhanced.frontMatterRenderingOption": {"description": "Front matter rendering option", "type": "string", "enum": ["none", "table", "code block"], "default": "none"}, "markdown-preview-enhanced.mermaidTheme": {"description": "Mermaid theme, you can choose one from [\"mermaid.css\", \"mermaid.dark.css\", \"mermaid.forest.css\"]", "default": "default", "type": "string", "enum": ["default", "dark", "forest"]}, "markdown-preview-enhanced.codeBlockTheme": {"description": "Code block theme. If `auto.css` is chosen, then the code block theme that best matches the current preview theme will be picked.", "default": "auto.css", "type": "string", "enum": ["auto.css", "default.css", "atom-dark.css", "atom-light.css", "atom-material.css", "coy.css", "darcula.css", "dark.css", "funky.css", "github.css", "github-dark.css", "hopscotch.css", "monokai.css", "okaidia.css", "one-dark.css", "one-light.css", "pen-paper-coffee.css", "pojoaque.css", "solarized-dark.css", "solarized-light.css", "twilight.css", "vs.css", "vue.css", "xonokai.css"]}, "markdown-preview-enhanced.previewTheme": {"description": "Preview Theme", "default": "github-light.css", "type": "string", "enum": ["atom-dark.css", "atom-light.css", "atom-material.css", "github-dark.css", "github-light.css", "gothic.css", "medium.css", "monokai.css", "newsprint.css", "night.css", "none.css", "one-dark.css", "one-light.css", "solarized-dark.css", "solarized-light.css", "vue.css"], "markdownEnumDescriptions": ["Atom Dark", "Atom Light", "Atom Material", "GitHub Dark", "GitHub Light", "Gothic", "Medium", "Monokai", "Newsprint", "Night", "None", "One Dark", "One Light", "Solarized Dark", "Solarized Light", "<PERSON><PERSON>"]}, "markdown-preview-enhanced.revealjsTheme": {"description": "RevealJS Presentation Theme", "default": "white.css", "type": "string", "enum": ["beige.css", "black.css", "blood.css", "league.css", "moon.css", "night.css", "serif.css", "simple.css", "sky.css", "solarized.css", "white.css", "none.css"]}, "markdown-preview-enhanced.protocolsWhiteList": {"description": "Accepted protocols for links.", "default": "http://, https://, atom://, file://, mailto:, tel:", "type": "string"}, "markdown-preview-enhanced.imageFolderPath": {"description": "When using Image Helper to copy images, by default images will be copied to root image folder path '/assets'", "default": "/assets", "type": "string"}, "markdown-preview-enhanced.imageUploader": {"description": "You can choose different image uploader to upload image", "default": "imgur", "type": "string", "enum": ["imgur", "sm.ms", "qiniu"]}, "markdown-preview-enhanced.qiniuAccessKey": {"type": "string", "default": "", "description": "<PERSON><PERSON>"}, "markdown-preview-enhanced.qiniuSecretKey": {"type": "string", "default": "", "description": "<PERSON><PERSON>"}, "markdown-preview-enhanced.qiniuBucket": {"type": "string", "default": "", "description": "<PERSON><PERSON>"}, "markdown-preview-enhanced.qiniuDomain": {"type": "string", "default": "http://", "description": "Qiniu Domain"}, "markdown-preview-enhanced.printBackground": {"description": "Whether to print background for file export or not. If set to `false`, then `github-light` preview theme will be used. You can also set `print_background` in front-matter for individual files.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.chromePath": {"description": "Chrome executable path, which is used for Puppeteer export. Leaving it empty means the path will be found automatically.", "default": "", "type": "string", "scope": "machine"}, "markdown-preview-enhanced.imageMagickPath": {"description": "ImageMagick command line path. Should be either `magick` or `convert`. Leaving it empty means the path will be found automatically.", "default": "", "type": "string", "scope": "machine"}, "markdown-preview-enhanced.pandocPath": {"description": "Pandoc executable path", "default": "pandoc", "type": "string", "scope": "machine"}, "markdown-preview-enhanced.pandocMarkdownFlavor": {"description": "The pandoc markdown flavor you want", "default": "markdown-raw_tex+tex_math_single_backslash", "type": "string"}, "markdown-preview-enhanced.pandocArguments": {"description": "Args passed to pandoc command e.g. [\"--smart\", \"--filter=/bin/exe\"] Please use long argument names.", "default": [], "type": "array"}, "markdown-preview-enhanced.latexEngine": {"description": "Default latex engine for Pandoc export and latex code chunk.", "default": "pdflatex", "type": "string"}, "markdown-preview-enhanced.enableScriptExecution": {"description": "Enables executing code chunks and importing javascript files.\n⚠️ Please use this feature with caution because it may put your security at risk! Your machine can get hacked if someone makes you open a markdown with malicious code while script execution is enabled.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.enableHTML5Embed": {"description": " Enables transform audio video link to to html5 embed audio video tags.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.HTML5EmbedUseImageSyntax": {"description": " Enables video/audio embed with ![]() syntax (default).", "default": true, "type": "boolean"}, "markdown-preview-enhanced.HTML5EmbedUseLinkSyntax": {"description": "Enables video/audio embed with []() syntax.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.HTML5EmbedIsAllowedHttp": {"description": "When true embed media with http:// schema in URLs. When false ignore and don't embed them.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.HTML5EmbedAudioAttributes": {"description": "HTML attributes to pass to audio tags.", "default": "controls preload=\"metadata\" width=\"320\"", "type": "string"}, "markdown-preview-enhanced.HTML5EmbedVideoAttributes": {"description": "HTML attributes to pass to video tags.", "default": "controls preload=\"metadata\" width=\"320\" height=\"240\"", "type": "string"}, "markdown-preview-enhanced.puppeteerWaitForTimeout": {"description": "Puppeteer waits for a certain timeout in milliseconds before the document export.", "default": 0, "type": "number"}, "markdown-preview-enhanced.puppeteerArgs": {"description": "Args passed to puppeteer.launch({args: $puppeteerArgs})", "default": [], "type": "array"}, "markdown-preview-enhanced.plantumlServer": {"description": "Render using PlantUML server instead of binary. Leave it empty to use the builtin plantuml.jar binary (`java` is required in system path). Eg: \"http://localhost:8080/svg/\"", "default": "", "type": "string"}, "markdown-preview-enhanced.hideDefaultVSCodeMarkdownPreviewButtons": {"description": "Hide the default VSCode markdown preview extension buttons. Restarting the editor is required to make this config take effect.", "default": true, "type": "boolean"}, "markdown-preview-enhanced.jsdelivrCdnHost": {"markdownDescription": "jsDelivr CDN host. Example values: `cdn.jsdelivr.net`, `fastly.jsdelivr.net`, `gcore.jsdelivr.net`, `testingcf.jsdelivr.net`", "default": "cdn.jsdelivr.net", "type": "string"}, "markdown-preview-enhanced.plantumlJarPath": {"description": "Absolute path to the plantuml.jar file (`java` is required in system path).", "default": "", "type": "string"}, "markdown-preview-enhanced.krokiServer": {"description": "The URL of the Kroki server to use.  ", "default": "https://kroki.io", "type": "string"}, "markdown-preview-enhanced.markdownFileExtensions": {"description": "Markdown file extensions. This is used to determine whether to show the preview button in the markdown file context menu.", "default": [".md", ".markdown", ".mdown", ".mkdn", ".mkd", ".rmd", ".qmd", ".mdx"], "type": "array"}, "markdown-preview-enhanced.alwaysShowBacklinksInPreview": {"description": "Always show backlinks in preview.", "default": false, "type": "boolean"}, "markdown-preview-enhanced.enablePreviewZenMode": {"description": "Enable this option will hide unnecessary UI elements in preview unless your mouse is over it.", "default": true, "type": "boolean"}}}, "customEditors": [{"viewType": "markdown-preview-enhanced", "displayName": "%customEditorPreviewDisplayName%", "selector": [{"filenamePattern": "*.{md,markdown,mdown,mkdn,mkd,rmd,qmd}"}], "priority": "option"}], "keybindings": [{"command": "markdown-preview-enhanced.openPreviewToTheSide", "key": "ctrl+k v", "mac": "cmd+k v", "when": "editorLangId == markdown"}, {"command": "markdown-preview-enhanced.openPreview", "key": "ctrl+shift+v", "mac": "cmd+shift+v", "when": "editorLangId == markdown"}, {"command": "markdown-preview-enhanced.runAllCodeChunks", "key": "ctrl+shift+enter", "when": "editorLangId == markdown"}, {"command": "markdown-preview-enhanced.runCodeChunk", "key": "shift+enter", "when": "editorLangId == markdown"}, {"command": "markdown-preview-enhanced.syncPreview", "key": "ctrl+shift+s", "when": "editorLangId == markdown"}], "menus": {"editor/context": [{"command": "markdown-preview-enhanced.openPreviewToTheSide", "when": "editorLangId == markdown", "group": "markdown-preview-enhanced"}], "editor/title": [{"command": "markdown-preview-enhanced.openPreviewToTheSide", "when": "editorLangId == markdown", "group": "navigation"}]}}, "activationEvents": ["onLanguage:markdown", "onLanguage:quarto"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"**/*.*": ["eslint", "prettier --write"]}, "dependencies": {"@types/crypto-js": "^4.1.2", "@types/vfile": "^3.0.2", "async-mutex": "^0.4.0", "crossnote": "^0.9.14", "crypto-js": "^4.2.0"}, "devDependencies": {"@types/cheerio": "0.22.11", "@types/mocha": "^5.2.5", "@types/node": "16", "@types/vscode": "1.70.0", "@typescript-eslint/eslint-plugin": "^6.5.0", "@typescript-eslint/parser": "^6.5.0", "@vscode/test-web": "^0.0.67", "concurrently": "^8.2.1", "esbuild": "^0.25.0", "esbuild-plugin-polyfill-node": "^0.3.0", "eslint": "^8.48.0", "gulp": "^4.0.2", "http-server": "^14.1.1", "husky": "^8.0.3", "lint-staged": "^9.4.2", "mocha": "^6.1.4", "prettier": "^1.18.2", "prettier-plugin-packagejson": "^2.0.1", "ts-loader": "^9.4.4", "typescript": "^5.2.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "engines": {"vscode": "^1.70.0"}, "icon": "media/mpe.png", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "__metadata": {"installedTimestamp": 1748881056373, "targetPlatform": "undefined", "size": 32402249}}