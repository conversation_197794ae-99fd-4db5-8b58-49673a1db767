{"displayName": "Markdown Preview Enhanced", "description": "Markdown Preview Enhanced ported to vscode", "customEditorPreviewDisplayName": "Markdown Preview Enhanced", "markdown-preview-enhanced.openPreviewToTheSide.title": "Markdown Preview Enhanced: Open Preview to the Side", "markdown-preview-enhanced.openPreview.title": "Markdown Preview Enhanced: Open Preview", "markdown-preview-enhanced.toggleScrollSync.title": "Markdown Preview Enhanced: Toggle Scroll Sync", "markdown-preview-enhanced.toggleLiveUpdate.title": "Markdown Preview Enhanced: Toggle Live Update", "markdown-preview-enhanced.toggleBreakOnSingleNewLine.title": "Markdown Preview Enhanced: Toggle Break On Single New Line", "markdown-preview-enhanced.openImageHelper.title": "Markdown Preview Enhanced: Image Helper", "markdown-preview-enhanced.runAllCodeChunks.title": "Markdown Preview Enhanced: Run All Code Chunks", "markdown-preview-enhanced.runCodeChunk.title": "Markdown Preview Enhanced: Run Code Chunk", "markdown-preview-enhanced.syncPreview.title": "Markdown Preview Enhanced: Sync Preview", "markdown-preview-enhanced.customizeCss.title": "Markdown Preview Enhanced: Customize CSS (Global)", "markdown-preview-enhanced.customizeCssInWorkspace.title": "Markdown Preview Enhanced: Customize CSS (Workspace)", "markdown-preview-enhanced.insertNewSlide.title": "Markdown Preview Enhanced: Insert New Slide", "markdown-preview-enhanced.insertTable.title": "Markdown Preview Enhanced: Insert Table", "markdown-preview-enhanced.insertPagebreak.title": "Markdown Preview Enhanced: Insert Page Break", "markdown-preview-enhanced.createTOC.title": "Markdown Preview Enhanced: Create TOC", "markdown-preview-enhanced.openConfigScript.title": "Markdown Preview Enhanced: Open Config Script (Global)", "markdown-preview-enhanced.openConfigScriptInWorkspace.title": "Markdown Preview Enhanced: Open Config Script (Workspace)", "markdown-preview-enhanced.extendParser.title": "Markdown Preview Enhanced: <PERSON><PERSON> (Global)", "markdown-preview-enhanced.extendParserInWorkspace.title": "Markdown Preview Enhanced: Extend Parser (Workspace)", "markdown-preview-enhanced.customizePreviewHtmlHead.title": "Markdown Preview Enhanced: Customize Preview Html Head (Global)", "markdown-preview-enhanced.customizePreviewHtmlHeadInWorkspace.title": "Markdown Preview Enhanced: Customize Preview Html Head (Workspace)", "markdown-preview-enhanced.showUploadedImages.title": "Markdown Preview Enhanced: Show Uploaded Images"}