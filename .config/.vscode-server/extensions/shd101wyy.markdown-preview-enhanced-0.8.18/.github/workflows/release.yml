name: "Publish the extension"
on:
  push:
    branches:
      - master
      - develop
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: 'Install nodejs 20'
        uses: actions/setup-node@v2
        with:
          node-version: '20'
      - name: Install dependencies
        run: |
          corepack enable
          yarn install
      - name: Build
        run: yarn run build
      - name: Get current package version
        id: package_version
        uses: martinbeentjes/npm-get-version-action@v1.1.0
      - name: Get Changelog Entry
        id: changelog_reader
        uses: mindsers/changelog-reader-action@v2.0.0
        with:
          validation_level: warn
          version: ${{ steps.package_version.outputs.current-version }}
          path: 'CHANGELOG.md'
      - name:  Publish extension to Visual Studio Marketplace
        id: create_vsix
        uses: HaaLeo/publish-vscode-extension@v1
        with:
          dryRun: ${{ github.ref != 'refs/heads/master'  }}
          pat: ${{ secrets.VS_MARKETPLACE_TOKEN }}
          registryUrl: https://marketplace.visualstudio.com
          yarn: true
      - name: Create a Release
        if: github.ref == 'refs/heads/master'
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name : ${{ steps.package_version.outputs.current-version}}
          release_name: ${{ steps.package_version.outputs.current-version}}
          body: ${{ steps.changelog_reader.outputs.changes }}
      - name: Attach vsix to release
        if: github.ref == 'refs/heads/master'
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ${{ steps.create_vsix.outputs.vsixPath}}
          asset_name: ${{ steps.create_vsix.outputs.vsixPath}}
          asset_content_type: application/vsix