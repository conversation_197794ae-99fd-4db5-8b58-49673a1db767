<h1 align="center"> Markdown Preview Enhanced </h1>

![intro](https://user-images.githubusercontent.com/1908863/28495106-30b3b15e-6f09-11e7-8eb6-ca4ca001ab15.png)

<p align="center">
<a href="https://shd101wyy.github.io/markdown-preview-enhanced/#/"> English &nbsp;&nbsp; </a>  
<a href="https://shd101wyy.github.io/markdown-preview-enhanced/#/zh-cn/"> 简体中文 &nbsp;&nbsp; </a>  
<a href="https://shd101wyy.github.io/markdown-preview-enhanced/#/zh-tw/"> 繁體中文 &nbsp;&nbsp; </a>
<a href="https://shd101wyy.github.io/markdown-preview-enhanced/#/ja-jp/"> 日本語 &nbsp;&nbsp; </a> <br>
</p>

<p align="center">
<a href="https://atom.io/packages/markdown-preview-enhanced">Atom</a>
&
<a href="https://marketplace.visualstudio.com/items?itemName=shd101wyy.markdown-preview-enhanced">VSCode</a> 
&
<a href="https://vscode.dev">VSCode for the Web</a>
<br>
<br>
  <a href="https://a.paddle.com/v2/click/16413/111548?link=1227"><img src="https://img.shields.io/badge/LEARN-VSCODE%20POWER%20USER%20COURSE%20%E2%86%92-gray.svg?colorB=4D2AFF" alt="Become VSCode Power User"></a>
</p>

## Supporting this project

Markdown Preview Enhanced is an open source project released under the [University of Illinois/NCSA Open Source License](https://github.com/shd101wyy/vscode-markdown-preview-enhanced/blob/HEAD/LICENSE.md). Its ongoing development is made possible thanks to the support by these awesome [backers](https://shd101wyy.github.io/markdown-preview-enhanced/#/backers). You can help make this project better by [supporting us on GitHub Sponsors](https://github.com/sponsors/shd101wyy), [PayPal](https://shd101wyy.github.io/markdown-preview-enhanced/#/paypal), or [微信支付 Wechat Pay](https://shd101wyy.github.io/markdown-preview-enhanced/#/wechat). Thank you!

## Sponsors

<p><a title="Try CodeStream" href="https://sponsorlink.codestream.com/?utm_source=vscmarket&amp;utm_campaign=shd101wyy-markdown&amp;utm_medium=banner"><img src="https://alt-images.codestream.com/codestream_logo_shd101wyy-markdown.png"></a></br>
Manage pull requests and conduct code reviews in your IDE with full source-tree context. Comment on any line, not just the diffs. Use jump-to-definition, your favorite keybindings, and code intelligence with more of your workflow.<br> <a title="Try CodeStream" href="https://sponsorlink.codestream.com/?utm_source=vscmarket&amp;utm_campaign=shd101wyy-markdown&amp;utm_medium=banner">Learn More</a></p>

<br>

<a href="https://github.com/sponsors/shd101wyy">
  <img src="https://github.blog/wp-content/uploads/2019/05/mona-heart-featured.png?" width="200"></a><br>

These [GitHub Sponsors](https://github.com/sponsors/shd101wyy#sponsors) and [Backers](https://shd101wyy.github.io/markdown-preview-enhanced/#/backers) help push this project forward 🎉.

## Introduction

Markdown Preview Enhanced is an extension that provides you with many useful functionalities such as automatic scroll sync, [math typesetting](https://shd101wyy.github.io/markdown-preview-enhanced/#/math), [mermaid](https://shd101wyy.github.io/markdown-preview-enhanced/#/diagrams?id=mermaid), [PlantUML](https://shd101wyy.github.io/markdown-preview-enhanced/#/diagrams?id=plantuml), [pandoc](https://shd101wyy.github.io/markdown-preview-enhanced/#/pandoc), PDF export, [code chunk](https://shd101wyy.github.io/markdown-preview-enhanced/#/code-chunk), [presentation writer](https://rawgit.com/shd101wyy/markdown-preview-enhanced/master/docs/presentation-intro.html), etc. A lot of its ideas are inspired by [Markdown Preview Plus](https://github.com/atom-community/markdown-preview-plus) and [RStudio Markdown](http://rmarkdown.rstudio.com/).

Feel free to ask questions, post issues, submit pull request, and request new features.

For more information about this project and how to use this extension, please check out our documentation ⬇︎

## Documentation

To check out the documentation, visit

- [English](https://shd101wyy.github.io/markdown-preview-enhanced/#/)
- [简体中文](https://shd101wyy.github.io/markdown-preview-enhanced/#/zh-cn/)
- [繁體中文](https://shd101wyy.github.io/markdown-preview-enhanced/#/zh-tw/)
- [日本語](https://shd101wyy.github.io/markdown-preview-enhanced/#/ja-jp/)

Contact me if you are willing to help translate the documentation :)

## Keybindings

> The <kbd>cmd</kbd> key for _Windows_ is <kbd>ctrl</kbd>.

| Shortcuts                                         | Functionality              |
| ------------------------------------------------- | -------------------------- |
| <kbd>cmd-k v</kbd> or <kbd>ctrl-k v</kbd>         | Open preview to the Side   |
| <kbd>cmd-shift-v</kbd> or <kbd>ctrl-shift-v</kbd> | Open preview               |
| <kbd>ctrl-shift-s</kbd>                           | Sync preview / Sync source |
| <kbd>shift-enter</kbd>                            | Run Code Chunk             |
| <kbd>ctrl-shift-enter</kbd>                       | Run all Code Chunks        |
| <kbd>cmd-=</kbd> or <kbd>cmd-shift-=</kbd>        | Preview zoom in            |
| <kbd>cmd--</kbd> or <kbd>cmd-shift-\_</kbd>       | Preview zoom out           |
| <kbd>cmd-0</kbd>                                  | Preview reset zoom         |
| <kbd>esc</kbd>                                    | Toggle sidebar TOC         |

## Changelog

Please check the [Releases](https://github.com/shd101wyy/vscode-markdown-preview-enhanced/releases) page of this project.

## License

[University of Illinois/NCSA Open Source License](https://github.com/shd101wyy/vscode-markdown-preview-enhanced/blob/HEAD/LICENSE.md)
