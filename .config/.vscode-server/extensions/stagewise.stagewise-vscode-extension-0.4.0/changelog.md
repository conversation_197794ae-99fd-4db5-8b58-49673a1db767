# stagewise-vscode-extension

## 0.4.0

### Minor Changes

- bde4944: Add images, files, mode and model properties to the srpc contract and agent call dispatches
- f4b085d: Add session management and connection state

### Patch Changes

- 0092794: Update license and copyright notices
- 3b637e8: Update README.md to include multiple-window-caveat
- 1575df4: Renaming variables to improve clarity.
- 79e11fa: Align versions to match 0.3
- 92407bd: Update license field in readme.
- Updated dependencies [bde4944]
- Updated dependencies [79e11fa]
- Updated dependencies [0092794]
- Updated dependencies [f4b085d]
- Updated dependencies [1575df4]
- Updated dependencies [058d70b]
- Updated dependencies [79e11fa]
  - @stagewise/extension-toolbar-srpc-contract@0.1.0

## 0.4.0-alpha.2

### Minor Changes

- f4b085d: Add session management and connection state

### Patch Changes

- Updated dependencies [f4b085d]
  - @stagewise/extension-toolbar-srpc-contract@0.1.0-alpha.1

## 0.3.1-alpha.1

### Patch Changes

- 92407bd: Update license field in readme.

## 0.3.1-alpha.0

### Patch Changes

- 3b637e8: Update README.md to include multiple-window-caveat

## 0.3.0-alpha.0

### Minor Changes

- bde4944: Add images, files, mode and model properties to the srpc contract and agent call dispatches

### Patch Changes

- 1575df4: Renaming variables to improve clarity.
- 79e11fa: Align versions to match 0.3
- Updated dependencies [bde4944]
- Updated dependencies [79e11fa]
- Updated dependencies [1575df4]
- Updated dependencies [058d70b]
- Updated dependencies [79e11fa]
  - @stagewise/extension-toolbar-srpc-contract@0.1.0-alpha.0

## 0.2.2

### Patch Changes

- 4337bd6: Remove mcp.json update, since mcp tools are not supported yet

## 0.2.1

### Patch Changes

- b74c54f: Improving the toolbar auto-setup prompt

## 0.2.0

### Minor Changes

- 278ae2a: Implement support for the windsurf IDE.
