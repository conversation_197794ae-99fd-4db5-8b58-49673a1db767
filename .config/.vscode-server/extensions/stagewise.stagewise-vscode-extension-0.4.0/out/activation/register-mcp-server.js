"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCursorMcpConfig = updateCursorMcpConfig;
const vscode = __importStar(require("vscode"));
const fs = __importStar(require("node:fs"));
const path = __importStar(require("node:path"));
const os = __importStar(require("node:os"));
function updateCursorMcpConfig(port) {
    const serverName = 'stagewise';
    const serverConfig = {
        url: `http://localhost:${port}/sse`,
        env: {}, // No environment variables needed for now
    };
    // Try to get the workspace root path first
    const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
    // Determine the config directory path
    let configDir;
    if (workspaceRoot) {
        // Use project's .cursor directory
        configDir = path.join(workspaceRoot, '.cursor');
    }
    else {
        // Fall back to home directory's .cursor
        configDir = path.join(os.homedir(), '.cursor');
    }
    // Create .cursor directory if it doesn't exist
    if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
    }
    const configPath = path.join(configDir, 'mcp.json');
    try {
        // Read existing config if it exists
        let config = {
            mcpServers: {},
        };
        if (fs.existsSync(configPath)) {
            const content = fs.readFileSync(configPath, 'utf8');
            config = JSON.parse(content);
            // Ensure mcpServers exists
            if (!config.mcpServers) {
                config.mcpServers = {};
            }
        }
        // Update or add the server configuration
        config.mcpServers[serverName] = serverConfig;
        // Write back to file
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    }
    catch (error) {
        console.error('Failed to register MCP server:', error);
        throw error;
    }
}
//# sourceMappingURL=register-mcp-server.js.map