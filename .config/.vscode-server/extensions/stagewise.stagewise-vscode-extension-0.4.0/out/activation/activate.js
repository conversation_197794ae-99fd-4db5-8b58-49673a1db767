"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const server_1 = require("../http-server/server");
const register_mcp_server_1 = require("./register-mcp-server");
const find_available_port_1 = require("../utils/find-available-port");
const call_cursor_agent_1 = require("../utils/call-cursor-agent");
const extension_toolbar_srpc_contract_1 = require("@stagewise/extension-toolbar-srpc-contract");
const setup_toolbar_1 = require("./setup-toolbar");
// Diagnostic collection specifically for our fake prompt
const fakeDiagCollection = vscode.languages.createDiagnosticCollection('stagewise');
// Dummy handler for the setupToolbar command
async function setupToolbarHandler() {
    await (0, setup_toolbar_1.setupToolbar)();
}
async function activate(context) {
    const isCursorIDE = vscode.env.appName.toLowerCase().includes('cursor');
    if (!isCursorIDE) {
        vscode.window.showInformationMessage('For now, this extension is designed to work only in Cursor IDE. Please use Cursor to run this extension.');
        return;
    }
    context.subscriptions.push(fakeDiagCollection); // Dispose on deactivation
    try {
        // Find an available port
        const port = await (0, find_available_port_1.findAvailablePort)(extension_toolbar_srpc_contract_1.DEFAULT_PORT);
        // Register MCP server with the actual port
        (0, register_mcp_server_1.updateCursorMcpConfig)(port);
        // Start the HTTP server with the same port
        const server = await (0, server_1.startServer)(port);
        const bridge = (0, extension_toolbar_srpc_contract_1.getExtensionBridge)(server);
        bridge.register({
            triggerAgentPrompt: async (request, sendUpdate) => {
                await (0, call_cursor_agent_1.callCursorAgent)(request.prompt);
                sendUpdate.sendUpdate({ updateText: 'Called the agent' });
                return { result: { success: true } };
            },
        });
    }
    catch (error) {
        vscode.window.showErrorMessage(`Failed to start server: ${error}`);
        throw error;
    }
    // Register the setupToolbar command
    const setupToolbarCommand = vscode.commands.registerCommand('stagewise.setupToolbar', setupToolbarHandler);
    context.subscriptions.push(setupToolbarCommand);
}
async function deactivate() {
    await (0, server_1.stopServer)();
}
//# sourceMappingURL=activate.js.map