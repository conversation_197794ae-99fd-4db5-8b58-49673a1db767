"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mcpServer = void 0;
exports.createMcpServer = createMcpServer;
const mcp_1 = require("@modelcontextprotocol/sdk/server/mcp");
const tools_1 = require("./tools");
const constants_1 = require("../constants");
function createMcpServer() {
    const mcpServer = new mcp_1.McpServer({
        name: constants_1.MCP_SERVER_NAME,
        version: constants_1.MCP_SERVER_VERSION,
        description: "A Model Context Protocol server that enables AI agents to interact with and monitor the user's browser environment in real-time.",
        schema: {
            type: 'object',
            properties: {
                logs: { type: 'array', items: { type: 'string' } },
            },
        },
    });
    (0, tools_1.registerConsoleLogsTool)(mcpServer);
    return mcpServer;
}
// Export a default instance for convenience
exports.mcpServer = createMcpServer();
//# sourceMappingURL=server.js.map