"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.registerConsoleLogsTool = registerConsoleLogsTool;
const zod_1 = __importDefault(require("zod"));
// TODO: This is mocked, will be replaced with dynamic tool registration via sRPC from the toolbar
// Types and functions will be defined in @stagewise/extension-toolbar-srpc-contract/src/contract.ts
async function registerConsoleLogsTool(server) {
    return server.tool('get-console-logs', 'Get the console logs', {
        request: zod_1.default.object({
            amount: zod_1.default.number().optional(),
        }),
    }, async ({ request }) => {
        const logs = [];
        return {
            content: [{ type: 'text', text: JSON.stringify(logs, null, 2) }],
        };
    });
}
//# sourceMappingURL=tools.js.map