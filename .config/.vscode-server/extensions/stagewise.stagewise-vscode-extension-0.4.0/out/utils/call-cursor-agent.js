"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.callCursorAgent = callCursorAgent;
const vscode = __importStar(require("vscode"));
const constants_1 = require("../constants");
const CURSOR_PROMPT_PREFIX = `\`\`\`
    Ah, sorry! I was wrong, it wasn't an error. The user has a specific change request. Here is the request:
    \`\`\``;
async function callCursorAgent(prompt) {
    const promptWithPrefix = `${CURSOR_PROMPT_PREFIX}\n${prompt}`;
    let editor = vscode.window.activeTextEditor;
    if (!editor) {
        try {
            // Get all workspace files
            const files = await vscode.workspace.findFiles('**/*', '**/node_modules/**');
            if (files.length === 0) {
                vscode.window.showErrorMessage('No files found in workspace to open.');
                return;
            }
            // Open the first file found
            const document = await vscode.workspace.openTextDocument(files[0]);
            editor = await vscode.window.showTextDocument(document);
        }
        catch (error) {
            vscode.window.showErrorMessage('Failed to open existing file for prompt injection.');
            return;
        }
        // Sleep 200ms to ensure editor is ready
        await new Promise((resolve) => setTimeout(resolve, 200));
    }
    const document = editor.document; // Get document early
    // --- Create the Diagnostic Collection ONCE before try/finally ---
    // This collection will be used to both set and clear the diagnostic.
    const fakeDiagCollection = vscode.languages.createDiagnosticCollection(constants_1.DIAGNOSTIC_COLLECTION_NAME);
    try {
        // Use a large range or the current selection - using full doc range here
        // Consider using editor.selection if you want it tied to selected code
        const selectionOrFullDocRange = editor.selection.isEmpty
            ? new vscode.Range(0, 0, document.lineCount, 0) // Fallback to full doc if no selection
            : editor.selection; // Use actual selection if available
        // 1. Create the fake diagnostic object
        const fakeDiagnostic = new vscode.Diagnostic(selectionOrFullDocRange, promptWithPrefix, vscode.DiagnosticSeverity.Error);
        fakeDiagnostic.source = constants_1.DIAGNOSTIC_COLLECTION_NAME;
        // 2. Set the diagnostic using the collection created outside the try block
        fakeDiagCollection.set(document.uri, [fakeDiagnostic]);
        // 3. Ensure cursor is within the diagnostic range (e.g., start)
        // This might help with the '@composer.isCursorOnLint' context, but may not be sufficient
        editor.selection = new vscode.Selection(selectionOrFullDocRange.start, selectionOrFullDocRange.start);
        // 5. Execute the command
        await vscode.commands.executeCommand('composer.fixerrormessage');
        vscode.window.showInformationMessage(`Triggered agent for prompt.`); // Simplified message
    }
    catch (error) {
        vscode.window.showErrorMessage(`Failed to inject prompt: ${error}`);
    }
    finally {
        // --- CRUCIAL: Use the SAME collection instance created ABOVE the try block ---
        if (document) {
            // Check if document still valid (it should be)
            // Clear the specific diagnostic for this URI from the collection
            fakeDiagCollection.delete(document.uri);
            // Alternatively, clear all diagnostics managed by this collection:
            // fakeDiagCollection.clear();
        }
        else {
            fakeDiagCollection.clear(); // Clear everything if URI is lost
        }
        // --- Dispose the collection to clean up resources ---
        fakeDiagCollection.dispose();
    }
}
//# sourceMappingURL=call-cursor-agent.js.map