"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.findAvailablePort = findAvailablePort;
const node_net_1 = __importDefault(require("node:net"));
async function findAvailablePort(startPort, maxPort = startPort + 30) {
    return new Promise((resolve, reject) => {
        const tryPort = (port) => {
            if (port > maxPort) {
                reject(new Error(`No available ports found between ${startPort} and ${maxPort}`));
                return;
            }
            const server = node_net_1.default.createServer();
            server.once('error', (err) => {
                if (err.code === 'EADDRINUSE') {
                    // Port is in use, try next port
                    server.close();
                    tryPort(port + 1);
                }
                else {
                    reject(err);
                }
            });
            server.once('listening', () => {
                server.close();
                resolve(port);
            });
            server.listen(port);
        };
        tryPort(startPort);
    });
}
//# sourceMappingURL=find-available-port.js.map