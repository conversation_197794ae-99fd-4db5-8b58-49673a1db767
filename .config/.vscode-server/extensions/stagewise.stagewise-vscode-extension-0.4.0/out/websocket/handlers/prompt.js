"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handlePrompt = void 0;
exports.processPrompt = processPrompt;
const call_cursor_agent_1 = require("../../utils/call-cursor-agent");
async function processPrompt(prompt) {
    if (!prompt) {
        throw new Error('Prompt is required');
    }
    await (0, call_cursor_agent_1.callCursorAgent)(prompt);
    return { success: true, message: 'Prompt processed successfully' };
}
const handlePrompt = async (req, res) => {
    if (!req.body) {
        res.status(400).json({
            success: false,
            error: 'Request body is required',
        });
        return;
    }
    const { prompt } = req.body;
    try {
        const result = await processPrompt(prompt);
        res.json(result);
    }
    catch (error) {
        res.status(400).json({
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error occurred',
        });
    }
};
exports.handlePrompt = handlePrompt;
//# sourceMappingURL=prompt.js.map