"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.stopServer = exports.startServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const mcp_1 = require("./handlers/mcp");
const sse_1 = require("./handlers/sse");
const error_1 = require("./middleware/error");
const extension_toolbar_srpc_contract_1 = require("@stagewise/extension-toolbar-srpc-contract");
const createServer = (port) => {
    const app = (0, express_1.default)();
    // Middleware
    app.use(express_1.default.json());
    app.use((0, cors_1.default)({
        origin: '*',
        methods: ['GET', 'POST'],
        allowedHeaders: ['Content-Type'],
    }));
    // Routes
    // Ping-route which will allow the toolbar to find out the correct port, starting with DEFAULT_PORT
    app.get(extension_toolbar_srpc_contract_1.PING_ENDPOINT, (_req, res) => {
        res.send(extension_toolbar_srpc_contract_1.PING_RESPONSE);
    });
    app.all('/mcp', mcp_1.handleStreamableHttp);
    app.get('/sse', sse_1.handleSse);
    app.post('/sse-messages', sse_1.handleSsePost);
    // Error handling
    app.use(error_1.errorHandler);
    // 404 handler
    app.use((_req, _res, next) => {
        _res.status(404).json({ error: 'Not found' });
    });
    return app;
};
let server = null;
const startServer = async (port = extension_toolbar_srpc_contract_1.DEFAULT_PORT) => {
    const app = createServer(port);
    return await app.listen(port, () => { });
};
exports.startServer = startServer;
const stopServer = () => {
    return new Promise((resolve, reject) => {
        if (!server) {
            resolve();
            return;
        }
        server.close((err) => {
            if (err) {
                reject(err);
            }
            else {
                resolve();
            }
        });
        server = null;
    });
};
exports.stopServer = stopServer;
//# sourceMappingURL=server.js.map