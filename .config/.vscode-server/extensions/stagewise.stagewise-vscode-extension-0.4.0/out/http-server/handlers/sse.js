"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleSsePost = exports.handleSse = void 0;
const sse_js_1 = require("@modelcontextprotocol/sdk/server/sse.js");
const server_1 = require("../../mcp/server");
const transport_1 = require("../transport");
const handleSse = async (req, res) => {
    const transport = new sse_js_1.SSEServerTransport('/sse-messages', res);
    (0, transport_1.addTransport)('sse', transport.sessionId, transport);
    res.on('close', () => (0, transport_1.removeTransport)('sse', transport.sessionId));
    await server_1.mcpServer.connect(transport);
};
exports.handleSse = handleSse;
const handleSsePost = async (req, res) => {
    const sessionId = req.query.sessionId;
    const transport = (0, transport_1.getTransport)('sse', sessionId);
    if (!transport || !('handlePostMessage' in transport)) {
        res.status(400).json({
            jsonrpc: '2.0',
            error: {
                code: -32000,
                message: 'No transport found for sessionId',
            },
            id: null,
        });
        return;
    }
    await transport.handlePostMessage(req, res, req.body);
};
exports.handleSsePost = handleSsePost;
//# sourceMappingURL=sse.js.map