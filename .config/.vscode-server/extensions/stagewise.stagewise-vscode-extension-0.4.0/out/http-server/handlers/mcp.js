"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleStreamableHttp = void 0;
const streamableHttp_js_1 = require("@modelcontextprotocol/sdk/server/streamableHttp.js");
const server_1 = require("../../mcp/server");
const transport_1 = require("../transport");
const handleStreamableHttp = async (req, res) => {
    const sessionId = req.query.sessionId;
    if (sessionId) {
        const transport = (0, transport_1.getTransport)('streamable', sessionId);
        if (transport && 'handleRequest' in transport) {
            await transport.handleRequest(req, res);
            return;
        }
        res.status(400).json({
            jsonrpc: '2.0',
            error: {
                code: -32000,
                message: 'No transport found for sessionId',
            },
            id: null,
        });
    }
    const newSessionId = `streamable-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const transport = new streamableHttp_js_1.StreamableHTTPServerTransport({
        sessionIdGenerator: () => newSessionId,
        onsessioninitialized: (sessionId) => {
            (0, transport_1.addTransport)('streamable', sessionId, transport);
        },
    });
    (0, transport_1.addTransport)('streamable', newSessionId, transport);
    res.on('close', () => (0, transport_1.removeTransport)('streamable', newSessionId));
    await server_1.mcpServer.connect(transport);
    await transport.handleRequest(req, res);
};
exports.handleStreamableHttp = handleStreamableHttp;
//# sourceMappingURL=mcp.js.map