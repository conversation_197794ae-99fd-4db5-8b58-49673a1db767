"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeTransport = exports.addTransport = exports.getTransport = void 0;
const transports = {
    sse: {},
    streamable: {},
};
const getTransport = (type, sessionId) => transports[type][sessionId];
exports.getTransport = getTransport;
const addTransport = (type, sessionId, transport) => {
    transports[type][sessionId] = transport;
};
exports.addTransport = addTransport;
const removeTransport = (type, sessionId) => {
    delete transports[type][sessionId];
};
exports.removeTransport = removeTransport;
//# sourceMappingURL=transport.js.map