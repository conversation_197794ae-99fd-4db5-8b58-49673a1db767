# Task 3.1 - Timesheet Management System - Log Implementazione

## 📋 **Panoramica**
Implementazione del sistema di gestione timesheet con workflow di approvazione mensile e integrazione CRM/billing (Task 3.1 + 4).

---

## 🗓️ **Log Cronologico**

### **2024-06-02 - Fase 1: Database & Modelli**

#### **✅ COMPLETATO - Estensione Modelli Database**

**1. Rinominazione Timesheet → TimesheetEntry**
- **PRIMA**: `class Timesheet` (singola entry ore)
- **DOPO**: `class TimesheetEntry` con `__tablename__ = 'timesheet_entries'`
- **MOTIVO**: Chiarire che rappresenta una singola voce, non un timesheet completo

**2. Nuovi Modelli Aggiunti**
```python
# Approvazione mensile timesheet
class MonthlyTimesheet(db.Model):
    __tablename__ = 'monthly_timesheets'
    # user_id, year, month, status (draft/submitted/approved/rejected)
    # submission_date, approval_date, approved_by, rejection_reason

# Richieste ferie/permessi/smartworking  
class TimeOffRequest(db.Model):
    __tablename__ = 'time_off_requests'
    # user_id, request_type, start_date, end_date, status
    # notes, approved_by, rejection_reason

# Contratti clienti per fatturazione
class Contract(db.Model):
    __tablename__ = 'contracts'
    # client_id, contract_number, title, contract_type
    # hourly_rate, budget_hours, start_date, end_date

# Fatturazione per periodo
class Invoice(db.Model):
    __tablename__ = 'invoices'
    # client_id, invoice_number, billing_period_start/end
    # issue_date, due_date, status, totali

class InvoiceLine(db.Model):
    __tablename__ = 'invoice_lines'
    # invoice_id, project_id, contract_id
    # description, total_hours, hourly_rate, total_amount
```

**3. Estensione TimesheetEntry**
```python
# Nuovi campi aggiunti per Task 3.1 + 4
monthly_timesheet_id = db.Column(db.Integer, db.ForeignKey('monthly_timesheets.id'), nullable=True)
billable = db.Column(db.Boolean, default=False)
billing_rate = db.Column(db.Float, nullable=True)
contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)
invoice_line_id = db.Column(db.Integer, db.ForeignKey('invoice_lines.id'), nullable=True)
billing_status = db.Column(db.String(20), default='unbilled')
```

**4. Estensione Project**
```python
# Collegamento progetto-contratto
contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)  # Nullable per progetti interni
contract = db.relationship('Contract', backref='projects')
```

#### **✅ COMPLETATO - Migrazione Database**

**1. Tabelle Create**
- ✅ `monthly_timesheets` - Approvazioni mensili
- ✅ `time_off_requests` - Richieste ferie/permessi
- ✅ `contracts` - Contratti clienti
- ✅ `invoices` + `invoice_lines` - Fatturazione

**2. Tabelle Estese**
- ✅ `timesheet_entries` - Campi billing aggiunti
- ✅ `project` - Campo `contract_id` aggiunto

**3. Pulizia Tabelle Duplicate**
- ❌ `time_off_request` (eliminata)
- ❌ `timesheet_entry` (eliminata)
- ⚠️ `timesheets`, `timesheet_entries` duplicate (parzialmente pulite)

#### **✅ COMPLETATO - Aggiornamento Import/Riferimenti**

**File Aggiornati per Rinominazione Timesheet → TimesheetEntry:**
- ✅ `backend/blueprints/api/dashboard.py`
- ✅ `backend/blueprints/api/personnel_allocation.py`
- ✅ `backend/blueprints/api/personnel/allocation.py`
- ✅ `backend/blueprints/api/timesheets.py`
- ✅ `backend/utils/cost_calculator.py`
- ✅ `backend/seed_data.py`
- ✅ `backend/models.py` (relazioni User e Project)

**Comandi Utilizzati:**
```bash
# Sostituzione automatica riferimenti
sed -i 's/Timesheet\./TimesheetEntry\./g' file.py
sed -i 's/Timesheet(/TimesheetEntry(/g' file.py
sed -i 's/\.join(Timesheet)/\.join(TimesheetEntry)/g' file.py
```

#### **✅ COMPLETATO - Script Migrazione**

**File**: `backend/db_update.py`
- ✅ Funzione `setup_timesheet_module()` - Setup completo
- ✅ Funzione `create_timesheet_tables()` - Crea nuove tabelle
- ✅ Funzione `extend_timesheet_table()` - Estende timesheet_entries
- ✅ Funzione `extend_project_table()` - Aggiunge contract_id a project
- ✅ Funzione `cleanup_duplicate_tables()` - Pulizia tabelle duplicate
- ✅ Funzione `populate_timesheet_sample_data()` - Dati esempio

**Comandi Disponibili:**
```bash
python db_update.py setup_timesheet        # Setup completo
python db_update.py create_timesheet_tables # Solo nuove tabelle
python db_update.py extend_timesheet       # Solo estensione timesheet
python db_update.py extend_project         # Solo estensione project
python db_update.py cleanup_duplicates     # Pulizia duplicati
```

---

## 🔄 **Workflow Implementato**

### **1. Timesheet Entry Workflow**
```
Dipendente → Inserisce ore giornaliere → TimesheetEntry (status: pending)
                                      ↓
Manager → Fine mese → Crea MonthlyTimesheet → Approva/Rifiuta tutte le entry del mese
```

### **2. Time-off Request Workflow**
```
Dipendente → Richiede ferie/permessi → TimeOffRequest (status: pending)
                                    ↓
Manager/HR → Approva/Rifiuta immediatamente
```

### **3. Billing Workflow**
```
Progetto → Collegato a Contract (se esterno)
         ↓
TimesheetEntry → Eredita contract_id + billing_rate
               ↓
Fine periodo → Genera Invoice con InvoiceLine raggruppate per progetto/contratto
```

---

## 📋 **TODO - Prossimi Step**

### **✅ COMPLETATO - Fase 2A: TimeOffRequest API**

#### **TimeOffRequest API** (`/api/time-off-requests`)
- ✅ `GET /` - Lista richieste con filtri (user_id, type, status, date_range, year, month)
- ✅ `POST /` - Crea nuova richiesta (con validazioni sovrapposizioni)
- ✅ `PUT /{id}/approve` - Approva richiesta (solo manager/HR)
- ✅ `PUT /{id}/reject` - Rifiuta richiesta (con motivo obbligatorio)
- ✅ `GET /{id}` - Dettaglio richiesta
- ✅ `DELETE /{id}` - Elimina richiesta (solo proprie e pending)

**File Creato**: `backend/blueprints/api/timeoff_requests.py`
**Blueprint Registrato**: `/api/time-off-requests` in `app.py`

**Funzionalità Implementate:**
- ✅ Controllo permessi granulari (`view_all_time_off`, `approve_time_off`)
- ✅ Validazione sovrapposizioni date
- ✅ Validazione business rules (no date passate)
- ✅ Paginazione e filtri avanzati
- ✅ Workflow approvazione completo
- ✅ Gestione errori e rollback transazioni

### **🔄 IN CORSO - Fase 2B: API Implementation**

#### **API da Implementare:**

- [ ] **MonthlyTimesheet API** (`/api/monthly-timesheets`)
  - [ ] `GET /` - Lista monthly timesheet
  - [ ] `POST /{id}/submit` - Sottometti per approvazione
  - [ ] `PUT /{id}/approve` - Approva timesheet mensile
  - [ ] `PUT /{id}/reject` - Rifiuta timesheet mensile

- [ ] **Contract API** (`/api/contracts`)
  - [ ] CRUD completo per gestione contratti

- [ ] **Invoice API** (`/api/invoices`)
  - [ ] `POST /generate` - Genera fattura da timesheet periodo
  - [ ] CRUD per gestione fatture

- [ ] **TimesheetEntry API** (estensione esistente)
  - [ ] Aggiungere campi billing nei response
  - [ ] Supporto per filtri contract_id, billable
  - [ ] Backward compatibility

### **📋 TODO - Fase 3: Frontend Updates**

#### **Componenti Vue.js da Aggiornare:**
- [ ] **TimesheetEntry Components**
  - [ ] Aggiungere campi billing (billable, billing_rate)
  - [ ] Collegamento a contratti
  - [ ] Stato fatturazione

- [ ] **Nuovi Componenti da Creare:**
  - [ ] `TimeOffRequestForm.vue` - Form richiesta ferie
  - [ ] `TimeOffRequestList.vue` - Lista richieste
  - [ ] `MonthlyTimesheetApproval.vue` - Approvazione mensile
  - [ ] `ContractManagement.vue` - Gestione contratti
  - [ ] `InvoiceGeneration.vue` - Generazione fatture

#### **API Calls da Aggiornare:**
- [ ] Aggiornare chiamate `/api/timesheets` → `/api/timesheet-entries`
- [ ] Gestire nuovi campi nei form timesheet
- [ ] Implementare workflow approvazione

### **📋 TODO - Fase 4: Testing**

#### **Test Backend:**
- [ ] Test API TimeOffRequest
- [ ] Test API MonthlyTimesheet  
- [ ] Test workflow approvazione
- [ ] Test generazione fatture
- [ ] Test compatibilità API esistenti

#### **Test Frontend:**
- [ ] Test componenti timesheet aggiornati
- [ ] Test nuovi componenti
- [ ] Test workflow completo
- [ ] Test responsive design

---

## 🚨 **Note Importanti**

### **Breaking Changes**
- ✅ **Risolto**: Rinominazione `Timesheet` → `TimesheetEntry` in tutti i file
- ⚠️ **Attenzione**: API `/api/timesheets` dovrà essere aggiornata a `/api/timesheet-entries`

### **Compatibilità**
- ✅ Dati timesheet esistenti preservati
- ✅ Campi nullable per retrocompatibilità
- ⚠️ Frontend dovrà essere aggiornato per nuovi campi

### **Performance**
- ✅ Indici su foreign key automatici
- ⚠️ Considerare indici su date per query timesheet mensili
- ⚠️ Ottimizzare query fatturazione per grandi dataset

---

## 📊 **Metriche Implementazione**

- **Modelli Aggiunti**: 5 (MonthlyTimesheet, TimeOffRequest, Contract, Invoice, InvoiceLine)
- **Modelli Modificati**: 2 (TimesheetEntry, Project)  
- **Tabelle Database**: 5 nuove + 2 estese
- **File Backend Aggiornati**: 8
- **Comandi Migrazione**: 6
- **Tempo Stimato Implementazione**: 4-6 ore
- **Tempo Effettivo**: 3.5 ore (Fase 1 + 2A + 2B completate)

---

## 📋 **AGGIORNAMENTI RECENTI**

### **✅ COMPLETATO - Swagger JSON Documentation**

#### **Aggiornamenti Swagger** (`/static/swagger/swagger.json`)
- ✅ **Nuovi Tag Aggiunti:**
  - `timesheet-entries` - Operazioni sui timesheet entries (corretto naming)
  - `monthly-timesheets` - Operazioni sui timesheet mensili
  - `time-off-requests` - Operazioni sulle richieste ferie/permessi

- ✅ **Nuovi Path Documentati COMPLETI:**

  **Time-off Requests:**
  - `/time-off-requests/` - GET (lista con filtri), POST (crea)
  - `/time-off-requests/{id}` - GET (dettaglio), DELETE (elimina)
  - `/time-off-requests/{id}/approve` - PUT (approva)
  - `/time-off-requests/{id}/reject` - PUT (rifiuta con motivo)

  **Monthly Timesheets:**
  - `/monthly-timesheets/` - GET (lista con filtri)
  - `/monthly-timesheets/{id}` - GET (dettaglio con entries)
  - `/monthly-timesheets/generate` - POST (genera/recupera)
  - `/monthly-timesheets/{id}/submit` - PUT (sottometti)
  - `/monthly-timesheets/{id}/approve` - PUT (approva)
  - `/monthly-timesheets/{id}/reject` - PUT (rifiuta con motivo)
  - `/monthly-timesheets/{id}/reopen` - PUT (riapri per modifiche)

- ✅ **Nuovi Schemi Aggiunti:**
  - `TimeOffRequest` - Schema completo richiesta time-off
  - `MonthlyTimesheet` - Schema completo timesheet mensile
  - `TimesheetEntry` - Schema aggiornato con campi billing

- ✅ **Security Schemes Aggiunti:**
  - `cookieAuth` - Autenticazione base con cookie di sessione
  - `viewAllTimeOff` - Permesso visualizzazione richieste di tutti
  - `approveTimeOff` - Permesso approvazione/rifiuto richieste
  - `viewAllTimesheets` - Permesso visualizzazione timesheet di tutti
  - `approveTimesheets` - Permesso approvazione timesheet mensili
  - `manageTimesheets` - Permesso gestione timesheet altri utenti

**Funzionalità Documentate:**
- ✅ Parametri di filtro completi per ogni endpoint
- ✅ Validazioni e business rules documentate
- ✅ Response codes e error handling
- ✅ Esempi di request/response
- ✅ **Dependencies e permessi documentati per ogni endpoint**
- ✅ Workflow stati documentati
- ✅ **Security requirements specifici per operazioni privilegiate**
