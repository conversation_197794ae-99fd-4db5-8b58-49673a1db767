"""
API Blueprint per la gestione dei timesheet.
"""
from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import extract, func
from datetime import datetime, date
from calendar import monthrange

from models import TimesheetEntry, Task, Project, User
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db
from extensions import csrf

api_timesheets = Blueprint('api_timesheets', __name__)

@api_timesheets.route('/', methods=['GET'])
@csrf.exempt
@login_required
def get_timesheets():
    """Recupera i timesheet con filtri generali."""
    try:
        # Parametri query
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        user_id = request.args.get('user_id', type=int)
        project_id = request.args.get('project_id', type=int)
        task_id = request.args.get('task_id', type=int)
        limit = request.args.get('limit', type=int, default=50)
        offset = request.args.get('offset', type=int, default=0)

        # Query base
        query = TimesheetEntry.query

        # Verifica permessi
        if not user_has_permission(current_user.role, 'view_all_timesheets'):
            # L'utente può vedere solo i propri timesheet
            query = query.filter(TimesheetEntry.user_id == current_user.id)

        # Applica filtri
        if start_date:
            query = query.filter(TimesheetEntry.date >= datetime.strptime(start_date, '%Y-%m-%d').date())
        if end_date:
            query = query.filter(TimesheetEntry.date <= datetime.strptime(end_date, '%Y-%m-%d').date())
        if user_id:
            # Verifica permessi per vedere timesheet di altri utenti
            if not user_has_permission(current_user.role, 'view_all_timesheets') and user_id != current_user.id:
                return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)
            query = query.filter(TimesheetEntry.user_id == user_id)
        if project_id:
            query = query.filter(TimesheetEntry.project_id == project_id)
        if task_id:
            query = query.filter(TimesheetEntry.task_id == task_id)

        # Conta totale
        total = query.count()

        # Applica paginazione e ordina per data
        timesheets = query.order_by(TimesheetEntry.date.desc()).offset(offset).limit(limit).all()

        # Prepara dati
        timesheets_data = []
        for ts in timesheets:
            timesheets_data.append({
                'id': ts.id,
                'user_id': ts.user_id,
                'project_id': ts.project_id,
                'task_id': ts.task_id,
                'date': ts.date.isoformat(),
                'hours': ts.hours,
                'description': ts.description,
                'status': ts.status,
                'user_name': f"{ts.user.first_name} {ts.user.last_name}" if ts.user else None,
                'project_name': ts.project.name if ts.project else None,
                'task_name': ts.task.name if ts.task else None,
                'created_at': ts.created_at.isoformat() if ts.created_at else None
            })

        return api_response(
            data=timesheets_data,
            message=f"Recuperati {len(timesheets_data)} timesheet",
            meta={
                'total': total,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total
            }
        )

    except Exception as e:
        return handle_api_error(e)

@api_timesheets.route('/project/<int:project_id>/monthly', methods=['GET'])
@csrf.exempt
@login_required
def get_project_monthly_timesheet(project_id):
    """Recupera i timesheet mensili per un progetto con layout tabellare."""
    try:
        # Parametri query
        year = int(request.args.get('year', datetime.now().year))
        month = int(request.args.get('month', datetime.now().month))
        member_id = request.args.get('member_id', type=int)

        # Verifica permessi progetto
        project = Project.query.get_or_404(project_id)
        if not user_has_permission(current_user.role, 'view_all_projects'):
            # Verifica se l'utente è membro del team
            if not any(member.id == current_user.id for member in project.team_members):
                return api_response(False, 'Accesso negato al progetto', status_code=403)

        # Calcola giorni del mese
        days_in_month = monthrange(year, month)[1]

        # Query timesheet del progetto per il mese
        query = TimesheetEntry.query.filter(
            TimesheetEntry.project_id == project_id,
            extract('year', TimesheetEntry.date) == year,
            extract('month', TimesheetEntry.date) == month
        )

        # Filtra per membro se specificato
        if member_id:
            query = query.filter(TimesheetEntry.user_id == member_id)

        timesheets = query.all()

        # Query task del progetto
        tasks = Task.query.filter(Task.project_id == project_id).all()

        # Organizza dati per task e giorni
        task_daily_data = {}
        task_daily_billing = {}
        task_totals = {}

        for task in tasks:
            task_daily_data[task.id] = {day: 0 for day in range(1, days_in_month + 1)}
            task_daily_billing[task.id] = {day: False for day in range(1, days_in_month + 1)}
            task_totals[task.id] = 0

        # Popola dati timesheet
        for ts in timesheets:
            day = ts.date.day
            task_id = ts.task_id
            if task_id and task_id in task_daily_data:
                task_daily_data[task_id][day] += ts.hours
                task_totals[task_id] += ts.hours
                # Se almeno una entry del giorno è fatturabile, segna il giorno come fatturabile
                if ts.billable:
                    task_daily_billing[task_id][day] = True

        # Prepara dati task con timesheet
        tasks_data = []
        for task in tasks:
            # Trova lavoratori per questo task nel mese
            workers = db.session.query(User).join(TimesheetEntry).filter(
                TimesheetEntry.task_id == task.id,
                TimesheetEntry.project_id == project_id,
                extract('year', TimesheetEntry.date) == year,
                extract('month', TimesheetEntry.date) == month
            ).distinct().all()

            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'description': task.description,
                'daily_hours': task_daily_data[task.id],
                'daily_billing': task_daily_billing[task.id],
                'total_hours': task_totals[task.id],
                'workers': [f"{w.first_name} {w.last_name}" for w in workers]
            })

        # Calcola totali giornalieri
        daily_totals = {day: 0 for day in range(1, days_in_month + 1)}
        for task_id, daily_data in task_daily_data.items():
            for day, hours in daily_data.items():
                daily_totals[day] += hours

        return api_response(
            data={
                'year': year,
                'month': month,
                'days_in_month': days_in_month,
                'tasks': tasks_data,
                'daily_totals': daily_totals,
                'grand_total': sum(daily_totals.values()),
                'project': {
                    'id': project.id,
                    'name': project.name
                }
            },
            message="Timesheet mensile recuperato con successo"
        )

    except Exception as e:
        return handle_api_error(e)


@api_timesheets.route('/project/<int:project_id>', methods=['GET'])
@csrf.exempt
@login_required
def get_project_timesheets(project_id):
    """Recupera i timesheet per un progetto con filtri."""
    try:
        # Parametri query
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        task_id = request.args.get('task_id', type=int)
        user_id = request.args.get('user_id', type=int)

        # Verifica permessi progetto
        project = Project.query.get_or_404(project_id)
        if not user_has_permission(current_user.role, 'view_all_projects'):
            if not any(member.id == current_user.id for member in project.team_members):
                return api_response(False, 'Accesso negato al progetto', status_code=403)

        # Query base
        query = TimesheetEntry.query.filter(TimesheetEntry.project_id == project_id)

        # Applica filtri
        if start_date:
            query = query.filter(TimesheetEntry.date >= datetime.strptime(start_date, '%Y-%m-%d').date())
        if end_date:
            query = query.filter(TimesheetEntry.date <= datetime.strptime(end_date, '%Y-%m-%d').date())
        if task_id:
            query = query.filter(TimesheetEntry.task_id == task_id)
        if user_id:
            query = query.filter(TimesheetEntry.user_id == user_id)

        # Ordina per data
        timesheets = query.order_by(TimesheetEntry.date.desc()).all()

        # Prepara dati
        timesheets_data = []
        for ts in timesheets:
            timesheets_data.append({
                'id': ts.id,
                'user_id': ts.user_id,
                'task_id': ts.task_id,
                'date': ts.date.isoformat(),
                'hours': ts.hours,
                'description': ts.description,
                'status': ts.status,
                'user': {
                    'id': ts.user.id,
                    'first_name': ts.user.first_name,
                    'last_name': ts.user.last_name
                } if ts.user else None,
                'task': {
                    'id': ts.task.id,
                    'name': ts.task.name
                } if ts.task else None
            })

        return api_response(
            data=timesheets_data,
            message="Timesheet recuperati con successo"
        )

    except Exception as e:
        return handle_api_error(e)


@api_timesheets.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_timesheet():
    """Crea un nuovo timesheet."""
    try:
        data = request.get_json()

        # Validazione campi richiesti
        required_fields = ['project_id', 'date', 'hours']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )

        # Verifica permessi progetto
        project = Project.query.get_or_404(data['project_id'])
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            # Verifica se può inserire timesheet per se stesso
            user_id = data.get('user_id', current_user.id)
            if user_id != current_user.id:
                return api_response(False, 'Non puoi inserire timesheet per altri utenti', status_code=403)

        # Crea timesheet
        timesheet = TimesheetEntry(
            user_id=data.get('user_id', current_user.id),
            project_id=data['project_id'],
            task_id=data.get('task_id'),
            date=datetime.strptime(data['date'], '%Y-%m-%d').date(),
            hours=float(data['hours']),
            description=data.get('description', ''),
            status=data.get('status', 'pending'),
            billable=data.get('billable', False),
            billing_rate=float(data['billing_rate']) if data.get('billing_rate') else None
        )

        db.session.add(timesheet)
        db.session.commit()

        return api_response(
            data={'id': timesheet.id},
            message='Timesheet creato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_timesheets.route('/<int:timesheet_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_timesheet(timesheet_id):
    """Aggiorna un timesheet esistente."""
    try:
        timesheet = TimesheetEntry.query.get_or_404(timesheet_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            if timesheet.user_id != current_user.id:
                return api_response(False, 'Non puoi modificare timesheet di altri utenti', status_code=403)

        data = request.get_json()

        # Aggiorna campi
        if 'date' in data:
            timesheet.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        if 'hours' in data:
            timesheet.hours = float(data['hours'])
        if 'description' in data:
            timesheet.description = data['description']
        if 'status' in data:
            timesheet.status = data['status']
        if 'task_id' in data:
            timesheet.task_id = data['task_id']
        if 'billable' in data:
            timesheet.billable = data['billable']
        if 'billing_rate' in data:
            timesheet.billing_rate = float(data['billing_rate']) if data['billing_rate'] else None

        db.session.commit()

        return api_response(
            data={},
            message='Timesheet aggiornato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_timesheets.route('/<int:timesheet_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_timesheet(timesheet_id):
    """Elimina un timesheet."""
    try:
        timesheet = TimesheetEntry.query.get_or_404(timesheet_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            if timesheet.user_id != current_user.id:
                return api_response(False, 'Non puoi eliminare timesheet di altri utenti', status_code=403)

        db.session.delete(timesheet)
        db.session.commit()

        return api_response(
            data={},
            message='Timesheet eliminato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)