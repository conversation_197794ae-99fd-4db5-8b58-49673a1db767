"""
API Blueprint per la gestione delle proposte/opportunità.
Task 4 - CRM Integration
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import and_, or_, extract
from datetime import datetime, date

from models import Proposal, Client, User
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db, csrf

api_proposals = Blueprint('api_proposals', __name__)


@api_proposals.route('/', methods=['GET'])
@login_required
def get_proposals():
    """Recupera lista proposte con filtri"""
    try:
        # Parametri filtro
        client_id = request.args.get('client_id', type=int)
        status = request.args.get('status')  # draft, sent, negotiating, accepted, rejected
        created_by = request.args.get('created_by', type=int)
        start_date = request.args.get('start_date')  # YYYY-MM-DD
        end_date = request.args.get('end_date')  # YYYY-MM-DD
        search = request.args.get('search')  # Ricerca in titolo
        
        # Paginazione
        page = request.args.get('page', type=int, default=1)
        per_page = request.args.get('per_page', type=int, default=50)
        
        # Query base
        query = Proposal.query
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_proposals'):
            # L'utente può vedere solo le proprie proposte
            query = query.filter(Proposal.created_by == current_user.id)
        
        # Applica filtri
        if client_id:
            query = query.filter(Proposal.client_id == client_id)
            
        if status:
            query = query.filter(Proposal.status == status)
            
        if created_by:
            query = query.filter(Proposal.created_by == created_by)
            
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Proposal.created_at >= start_date_obj)
            
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Proposal.created_at <= end_date_obj)
            
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(Proposal.title.ilike(search_pattern))
        
        # Ordina per data di creazione (più recenti prima)
        query = query.order_by(Proposal.created_at.desc())
        
        # Applica paginazione
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Prepara dati
        proposals_data = []
        for proposal in paginated.items:
            proposals_data.append({
                'id': proposal.id,
                'client_id': proposal.client_id,
                'client': {
                    'id': proposal.client.id,
                    'name': proposal.client.name,
                    'company': getattr(proposal.client, 'company', None)
                } if proposal.client else None,
                'title': proposal.title,
                'description': proposal.description,
                'value': proposal.value,
                'status': proposal.status,
                'created_by': proposal.created_by,
                'creator': {
                    'id': proposal.creator.id,
                    'first_name': proposal.creator.first_name,
                    'last_name': proposal.creator.last_name
                } if proposal.creator else None,
                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,
                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,
                'created_at': proposal.created_at.isoformat(),
                'updated_at': proposal.updated_at.isoformat()
            })
        
        return api_response(
            data={
                'proposals': proposals_data,
                'pagination': {
                    'page': paginated.page,
                    'pages': paginated.pages,
                    'per_page': paginated.per_page,
                    'total': paginated.total,
                    'has_next': paginated.has_next,
                    'has_prev': paginated.has_prev
                }
            },
            message=f"Recuperate {len(proposals_data)} proposte"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_proposals.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_proposal():
    """Crea una nuova proposta"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            return api_response(False, 'Non hai i permessi per creare proposte', status_code=403)
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['client_id', 'title']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Verifica che il cliente esista
        client = Client.query.get(data['client_id'])
        if not client:
            return api_response(
                False,
                'Cliente non trovato',
                status_code=404
            )
        
        # Validazione status
        valid_statuses = ['draft', 'sent', 'negotiating', 'accepted', 'rejected']
        status = data.get('status', 'draft')
        if status not in valid_statuses:
            return api_response(
                False,
                f'Status non valido. Valori ammessi: {", ".join(valid_statuses)}',
                status_code=400
            )
        
        # Parsing date
        sent_date = None
        expiry_date = None
        
        if 'sent_date' in data and data['sent_date']:
            try:
                sent_date = datetime.strptime(data['sent_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato sent_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        
        if 'expiry_date' in data and data['expiry_date']:
            try:
                expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato expiry_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )
        
        # Validazione logica date
        if sent_date and expiry_date and sent_date > expiry_date:
            return api_response(
                False,
                'La data di invio non può essere successiva alla data di scadenza',
                status_code=400
            )
        
        # Crea nuova proposta
        proposal = Proposal(
            client_id=data['client_id'],
            title=data['title'],
            description=data.get('description', ''),
            value=data.get('value'),
            status=status,
            created_by=current_user.id,
            sent_date=sent_date,
            expiry_date=expiry_date
        )
        
        db.session.add(proposal)
        db.session.commit()
        
        return api_response(
            data={
                'id': proposal.id,
                'client_id': proposal.client_id,
                'client': {
                    'id': proposal.client.id,
                    'name': proposal.client.name
                },
                'title': proposal.title,
                'description': proposal.description,
                'value': proposal.value,
                'status': proposal.status,
                'created_by': proposal.created_by,
                'creator': {
                    'id': current_user.id,
                    'first_name': current_user.first_name,
                    'last_name': current_user.last_name
                },
                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,
                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,
                'created_at': proposal.created_at.isoformat()
            },
            message='Proposta creata con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_proposals.route('/<int:proposal_id>', methods=['GET'])
@login_required
def get_proposal(proposal_id):
    """Recupera dettaglio singola proposta"""
    try:
        proposal = Proposal.query.get_or_404(proposal_id)
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_proposals'):
            if proposal.created_by != current_user.id:
                return api_response(False, 'Non hai i permessi per visualizzare questa proposta', status_code=403)
        
        return api_response(
            data={
                'id': proposal.id,
                'client_id': proposal.client_id,
                'client': {
                    'id': proposal.client.id,
                    'name': proposal.client.name,
                    'company': getattr(proposal.client, 'company', None),
                    'industry': proposal.client.industry,
                    'website': proposal.client.website,
                    'address': proposal.client.address
                } if proposal.client else None,
                'title': proposal.title,
                'description': proposal.description,
                'value': proposal.value,
                'status': proposal.status,
                'created_by': proposal.created_by,
                'creator': {
                    'id': proposal.creator.id,
                    'first_name': proposal.creator.first_name,
                    'last_name': proposal.creator.last_name,
                    'email': proposal.creator.email
                } if proposal.creator else None,
                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,
                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,
                'created_at': proposal.created_at.isoformat(),
                'updated_at': proposal.updated_at.isoformat()
            },
            message="Dettaglio proposta recuperato con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_proposals.route('/<int:proposal_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_proposal(proposal_id):
    """Aggiorna una proposta esistente"""
    try:
        proposal = Proposal.query.get_or_404(proposal_id)

        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            if proposal.created_by != current_user.id:
                return api_response(False, 'Non hai i permessi per modificare questa proposta', status_code=403)

        data = request.get_json()

        # Non permettere modifiche a proposte accettate/rifiutate
        if proposal.status in ['accepted', 'rejected'] and 'status' not in data:
            return api_response(
                False,
                'Non è possibile modificare proposte già accettate o rifiutate',
                status_code=400
            )

        # Aggiorna campi se forniti
        if 'title' in data:
            proposal.title = data['title']

        if 'description' in data:
            proposal.description = data['description']

        if 'value' in data:
            proposal.value = data['value']

        if 'status' in data:
            valid_statuses = ['draft', 'sent', 'negotiating', 'accepted', 'rejected']
            if data['status'] not in valid_statuses:
                return api_response(
                    False,
                    f'Status non valido. Valori ammessi: {", ".join(valid_statuses)}',
                    status_code=400
                )
            proposal.status = data['status']

            # Se status diventa 'sent' e non c'è sent_date, impostala a oggi
            if data['status'] == 'sent' and not proposal.sent_date:
                proposal.sent_date = date.today()

        # Aggiorna date se fornite
        if 'sent_date' in data:
            if data['sent_date']:
                try:
                    proposal.sent_date = datetime.strptime(data['sent_date'], '%Y-%m-%d').date()
                except ValueError:
                    return api_response(
                        False,
                        'Formato sent_date non valido. Utilizzare YYYY-MM-DD',
                        status_code=400
                    )
            else:
                proposal.sent_date = None

        if 'expiry_date' in data:
            if data['expiry_date']:
                try:
                    proposal.expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()
                except ValueError:
                    return api_response(
                        False,
                        'Formato expiry_date non valido. Utilizzare YYYY-MM-DD',
                        status_code=400
                    )
            else:
                proposal.expiry_date = None

        # Validazione logica date
        if proposal.sent_date and proposal.expiry_date and proposal.sent_date > proposal.expiry_date:
            return api_response(
                False,
                'La data di invio non può essere successiva alla data di scadenza',
                status_code=400
            )

        db.session.commit()

        return api_response(
            data={
                'id': proposal.id,
                'title': proposal.title,
                'value': proposal.value,
                'status': proposal.status,
                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,
                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,
                'updated_at': proposal.updated_at.isoformat()
            },
            message='Proposta aggiornata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_proposals.route('/<int:proposal_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_proposal(proposal_id):
    """Elimina una proposta"""
    try:
        proposal = Proposal.query.get_or_404(proposal_id)

        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_proposals'):
            if proposal.created_by != current_user.id:
                return api_response(False, 'Non hai i permessi per eliminare questa proposta', status_code=403)

        # Non permettere eliminazione di proposte accettate
        if proposal.status == 'accepted':
            return api_response(
                False,
                'Non è possibile eliminare proposte accettate',
                status_code=400
            )

        proposal_title = proposal.title
        db.session.delete(proposal)
        db.session.commit()

        return api_response(
            message=f'Proposta "{proposal_title}" eliminata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)
