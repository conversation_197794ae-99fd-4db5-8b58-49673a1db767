<template>
  <div class="space-y-6">
    <!-- Header con filtri -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Task del Progetto</h3>
          <button 
            v-if="canManageTasks"
            @click="showCreateModal = true"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nuovo Task
          </button>
        </div>

        <!-- Filtri Task -->
        <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Stato</label>
            <select 
              v-model="filters.status" 
              @change="loadTasks"
              class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">Tutti gli stati</option>
              <option value="todo">Da fare</option>
              <option value="in-progress">In corso</option>
              <option value="review">In revisione</option>
              <option value="done">Completato</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priorità</label>
            <select 
              v-model="filters.priority" 
              @change="loadTasks"
              class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">Tutte le priorità</option>
              <option value="low">Bassa</option>
              <option value="medium">Media</option>
              <option value="high">Alta</option>
              <option value="urgent">Urgente</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Assegnatario</label>
            <select 
              v-model="filters.assignee_id" 
              @change="loadTasks"
              class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="">Tutti</option>
              <option 
                v-for="member in project?.team_members || []" 
                :key="member.id" 
                :value="member.id"
              >
                {{ member.first_name }} {{ member.last_name }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ricerca</label>
            <input 
              v-model="filters.search" 
              @input="debouncedSearch"
              type="text" 
              placeholder="Cerca task..."
              class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
          </div>
        </div>

        <!-- Controlli vista -->
        <div class="mt-4 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500 dark:text-gray-400">Vista:</span>
            <button
              @click="viewMode = 'list'"
              :class="viewMode === 'list' ? 'bg-primary-100 text-primary-700' : 'text-gray-500 hover:text-gray-700'"
              class="px-3 py-1 rounded-md text-sm font-medium"
            >
              Lista
            </button>
            <button
              @click="viewMode = 'kanban'"
              :class="viewMode === 'kanban' ? 'bg-primary-100 text-primary-700' : 'text-gray-500 hover:text-gray-700'"
              class="px-3 py-1 rounded-md text-sm font-medium"
            >
              Kanban
            </button>
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ tasks.length }} task trovati
          </div>
        </div>
      </div>
    </div>

    <!-- Loading -->
    <div v-if="tasksLoading" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
    </div>

    <!-- Error -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <p class="text-red-600">{{ error }}</p>
    </div>

    <!-- Vista Lista -->
    <div v-if="!tasksLoading && viewMode === 'list'" class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <!-- Header tabella -->
        <div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
          <div class="col-span-4">Task</div>
          <div class="col-span-2">Assegnatario</div>
          <div class="col-span-1">Stato</div>
          <div class="col-span-1">Priorità</div>
          <div class="col-span-2">Scadenza</div>
          <div class="col-span-1">Ore</div>
          <div class="col-span-1">Azioni</div>
        </div>

        <!-- Righe task -->
        <div v-for="task in tasks" :key="task.id" class="px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700">
          <div class="col-span-4">
            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ task.name }}</div>
            <div v-if="task.description" class="text-sm text-gray-500 dark:text-gray-400 truncate">{{ task.description }}</div>
          </div>
          <div class="col-span-2">
            <div v-if="task.assignee" class="flex items-center">
              <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700">
                {{ getInitials(task.assignee.first_name, task.assignee.last_name) }}
              </div>
              <div class="ml-2">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ task.assignee.first_name }} {{ task.assignee.last_name }}
                </div>
              </div>
            </div>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400">Non assegnato</span>
          </div>
          <div class="col-span-1">
            <span :class="getStatusClass(task.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
              {{ getStatusLabel(task.status) }}
            </span>
          </div>
          <div class="col-span-1">
            <span :class="getPriorityClass(task.priority)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
              {{ getPriorityLabel(task.priority) }}
            </span>
          </div>
          <div class="col-span-2">
            <div v-if="task.due_date" class="text-sm text-gray-900 dark:text-white">
              {{ formatDate(task.due_date) }}
            </div>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400">-</span>
          </div>
          <div class="col-span-1">
            <div class="text-sm text-gray-900 dark:text-white">
              {{ task.actual_hours || 0 }}h
              <span v-if="task.estimated_hours" class="text-gray-500">/ {{ task.estimated_hours }}h</span>
            </div>
          </div>
          <div class="col-span-1">
            <div class="flex items-center space-x-2">
              <button 
                @click="editTask(task)"
                class="text-primary-600 hover:text-primary-900 text-sm"
              >
                Modifica
              </button>
            </div>
          </div>
        </div>

        <!-- Empty state -->
        <div v-if="tasks.length === 0" class="px-6 py-12 text-center">
          <p class="text-gray-500 dark:text-gray-400">Nessun task trovato</p>
        </div>
      </div>
    </div>

    <!-- Vista Kanban -->
    <div v-if="!tasksLoading && viewMode === 'kanban'" class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div v-for="status in kanbanColumns" :key="status.value" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <div class="flex items-center justify-between mb-4">
          <h4 class="font-medium text-gray-900 dark:text-white">{{ status.label }}</h4>
          <span class="bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs">
            {{ getTasksByStatus(status.value).length }}
          </span>
        </div>
        <div class="space-y-3">
          <div 
            v-for="task in getTasksByStatus(status.value)" 
            :key="task.id"
            class="bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow"
            @click="editTask(task)"
          >
            <div class="font-medium text-sm text-gray-900 dark:text-white mb-1">{{ task.name }}</div>
            <div v-if="task.description" class="text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2">
              {{ task.description }}
            </div>
            <div class="flex items-center justify-between">
              <span :class="getPriorityClass(task.priority)" class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium">
                {{ getPriorityLabel(task.priority) }}
              </span>
              <div v-if="task.assignee" class="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700">
                {{ getInitials(task.assignee.first_name, task.assignee.last_name) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Creazione/Modifica Task -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeModal">
      <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {{ showEditModal ? 'Modifica Task' : 'Nuovo Task' }}
          </h3>
          
          <form @submit.prevent="saveTask">
            <div class="grid grid-cols-1 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Nome</label>
                <input 
                  v-model="formData.name" 
                  type="text" 
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Descrizione</label>
                <textarea 
                  v-model="formData.description" 
                  rows="3"
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                ></textarea>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Stato</label>
                  <select 
                    v-model="formData.status"
                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="todo">Da fare</option>
                    <option value="in-progress">In corso</option>
                    <option value="review">In revisione</option>
                    <option value="done">Completato</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priorità</label>
                  <select 
                    v-model="formData.priority"
                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                    <option value="low">Bassa</option>
                    <option value="medium">Media</option>
                    <option value="high">Alta</option>
                    <option value="urgent">Urgente</option>
                  </select>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Assegnatario</label>
                <select 
                  v-model="formData.assignee_id"
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Non assegnato</option>
                  <option 
                    v-for="member in project?.team_members || []" 
                    :key="member.id" 
                    :value="member.id"
                  >
                    {{ member.first_name }} {{ member.last_name }}
                  </option>
                </select>
              </div>

              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Scadenza</label>
                  <input 
                    v-model="formData.due_date" 
                    type="date"
                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ore stimate</label>
                  <input 
                    v-model="formData.estimated_hours" 
                    type="number" 
                    step="0.5"
                    min="0"
                    class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  >
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
              <button 
                type="button" 
                @click="closeModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"
              >
                Annulla
              </button>
              <button 
                type="submit"
                :disabled="saving"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"
              >
                {{ saving ? 'Salvataggio...' : (showEditModal ? 'Aggiorna' : 'Crea') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { usePermissions } from '@/composables/usePermissions'
import api from '@/utils/api'

const props = defineProps({
  project: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const authStore = useAuthStore()
const { hasPermission } = usePermissions()

// State
const tasks = ref([])
const tasksLoading = ref(false)
const error = ref('')
const viewMode = ref('list')
const saving = ref(false)

// Filtri
const filters = ref({
  status: '',
  priority: '',
  assignee_id: '',
  search: ''
})

// Modal
const showCreateModal = ref(false)
const showEditModal = ref(false)
const editingTask = ref(null)

// Form data
const formData = ref({
  name: '',
  description: '',
  status: 'todo',
  priority: 'medium',
  assignee_id: '',
  due_date: '',
  estimated_hours: null
})

// Computed
const canManageTasks = computed(() => {
  return hasPermission.value('manage_project_tasks')
})

const kanbanColumns = [
  { value: 'todo', label: 'Da fare' },
  { value: 'in-progress', label: 'In corso' },
  { value: 'review', label: 'In revisione' },
  { value: 'done', label: 'Completato' }
]

// Methods
const loadTasks = async () => {
  if (!props.project?.id) return
  
  tasksLoading.value = true
  error.value = ''
  
  try {
    const params = new URLSearchParams({
      project_id: props.project.id,
      ...filters.value
    })

    const response = await fetch(`/api/tasks?${params}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) {
      throw new Error('Errore nel caricamento dei task')
    }

    const result = await response.json()
    tasks.value = result.data?.tasks || result.tasks || []
  } catch (err) {
    error.value = err.message
  } finally {
    tasksLoading.value = false
  }
}

const saveTask = async () => {
  saving.value = true
  
  try {
    const url = showEditModal.value 
      ? `/api/tasks/${editingTask.value.id}`
      : '/api/tasks'
    
    const method = showEditModal.value ? 'PUT' : 'POST'
    const data = { ...formData.value, project_id: props.project.id }

    const response = await fetch(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      throw new Error('Errore nel salvataggio del task')
    }

    await loadTasks()
    closeModal()
  } catch (err) {
    error.value = err.message
  } finally {
    saving.value = false
  }
}

const editTask = (task) => {
  editingTask.value = task
  formData.value = {
    name: task.name,
    description: task.description || '',
    status: task.status,
    priority: task.priority,
    assignee_id: task.assignee_id || '',
    due_date: task.due_date ? task.due_date.split('T')[0] : '',
    estimated_hours: task.estimated_hours
  }
  showEditModal.value = true
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingTask.value = null
  formData.value = {
    name: '',
    description: '',
    status: 'todo',
    priority: 'medium',
    assignee_id: '',
    due_date: '',
    estimated_hours: null
  }
}

const getTasksByStatus = (status) => {
  return tasks.value.filter(task => task.status === status)
}

const getStatusClass = (status) => {
  const classes = {
    todo: 'bg-gray-100 text-gray-800',
    'in-progress': 'bg-blue-100 text-blue-800',
    review: 'bg-yellow-100 text-yellow-800',
    done: 'bg-green-100 text-green-800'
  }
  return classes[status] || 'bg-gray-100 text-gray-800'
}

const getStatusLabel = (status) => {
  const labels = {
    todo: 'Da fare',
    'in-progress': 'In corso',
    review: 'In revisione',
    done: 'Completato'
  }
  return labels[status] || status
}

const getPriorityClass = (priority) => {
  const classes = {
    low: 'bg-green-100 text-green-800',
    medium: 'bg-yellow-100 text-yellow-800',
    high: 'bg-orange-100 text-orange-800',
    urgent: 'bg-red-100 text-red-800'
  }
  return classes[priority] || 'bg-gray-100 text-gray-800'
}

const getPriorityLabel = (priority) => {
  const labels = {
    low: 'Bassa',
    medium: 'Media',
    high: 'Alta',
    urgent: 'Urgente'
  }
  return labels[priority] || priority
}

const getInitials = (firstName, lastName) => {
  return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase()
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT')
}

// Debounced search
let searchTimeout
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    loadTasks()
  }, 300)
}

// Watchers
watch(() => props.project?.id, (newId) => {
  if (newId) {
    loadTasks()
  }
})

// Lifecycle
onMounted(() => {
  if (props.project?.id) {
    loadTasks()
  }
})

// Expose methods to parent
defineExpose({
  refresh: loadTasks
})
</script>