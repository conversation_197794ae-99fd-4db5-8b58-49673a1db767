<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><PERSON><PERSON></h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Gestisci le tue richieste di ferie, permessi e smart working
          </p>
        </div>
        
        <div class="flex space-x-3">
          <button 
            @click="showRequestModal('vacation')"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            <PERSON><PERSON>
          </button>
          <button 
            @click="showRequestModal('leave')"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            <PERSON><PERSON>
          </button>
          <button 
            @click="showRequestModal('smart_working')"
            class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            Smart Working
          </button>
        </div>
      </div>
    </div>

    <!-- Filtri -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Tipo Richiesta
          </label>
          <select 
            v-model="selectedType"
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
            <option value="">Tutti i tipi</option>
            <option value="vacation">Ferie</option>
            <option value="leave">Permessi</option>
            <option value="smart_working">Smart Working</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Stato
          </label>
          <select 
            v-model="selectedStatus"
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
            <option value="">Tutti gli stati</option>
            <option value="pending">In Attesa</option>
            <option value="approved">Approvato</option>
            <option value="rejected">Rifiutato</option>
          </select>
        </div>
        
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Da Data
          </label>
          <input 
            v-model="dateFrom"
            type="date"
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
        </div>
        
        <div class="flex items-end">
          <button 
            @click="loadRequests"
            class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            Filtra
          </button>
        </div>
      </div>
    </div>

    <!-- Lista Richieste -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Le Mie Richieste
        </h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Tipo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Periodo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Durata
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Motivo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Stato
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Richiesta il
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Azioni
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="request in requests" :key="request.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getTypeClass(request.type)"
                >
                  {{ getTypeText(request.type) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatPeriod(request) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatDuration(request) }}
              </td>
              <td class="px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate">
                {{ request.reason || 'N/A' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(request.status)"
                >
                  {{ getStatusText(request.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatDate(request.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                  v-if="request.status === 'pending'"
                  @click="editRequest(request)"
                  class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"
                >
                  Modifica
                </button>
                <button 
                  v-if="request.status === 'pending'"
                  @click="deleteRequest(request.id)"
                  class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                >
                  Elimina
                </button>
                <span 
                  v-else
                  class="text-gray-400 dark:text-gray-500"
                >
                  {{ request.status === 'approved' ? 'Approvata' : 'Rifiutata' }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- Empty state -->
        <div v-if="requests.length === 0" class="text-center py-8">
          <div class="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessuna richiesta</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Non hai ancora effettuato richieste per il periodo selezionato.
          </p>
        </div>
      </div>
    </div>

    <!-- Statistiche -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ferie Rimanenti
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ remainingVacationDays }} giorni
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Permessi Usati
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ usedLeaveHours }}h / {{ totalLeaveHours }}h
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 012-2h6.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H19a2 2 0 012 2v0a2 2 0 00-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Smart Working
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ smartWorkingDays }} giorni
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                In Attesa
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ pendingRequests }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Nuova Richiesta -->
    <div v-if="showModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeModal">
      <div class="relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {{ getModalTitle() }}
          </h3>
          
          <form @submit.prevent="submitRequest">
            <div class="grid grid-cols-1 gap-4">
              <!-- Campi comuni -->
              <div v-if="requestType === 'vacation' || requestType === 'smart_working'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Data Inizio
                </label>
                <input 
                  v-model="formData.start_date" 
                  type="date"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>

              <div v-if="requestType === 'vacation' || requestType === 'smart_working'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Data Fine
                </label>
                <input 
                  v-model="formData.end_date" 
                  type="date"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>

              <!-- Campi specifici per permessi -->
              <div v-if="requestType === 'leave'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Data
                </label>
                <input 
                  v-model="formData.date" 
                  type="date"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>

              <div v-if="requestType === 'leave'">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Ore Richieste
                </label>
                <input 
                  v-model="formData.hours" 
                  type="number"
                  step="0.5"
                  min="0.5"
                  max="8"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>

              <!-- Motivo -->
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {{ requestType === 'smart_working' ? 'Note (opzionale)' : 'Motivo' }}
                </label>
                <textarea
                  v-model="formData.reason"
                  rows="3"
                  :required="requestType !== 'smart_working'"
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  :placeholder="requestType === 'smart_working' ? 'Note aggiuntive...' : 'Descrivi il motivo della richiesta...'"
                ></textarea>
              </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
              <button 
                type="button" 
                @click="closeModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"
              >
                Annulla
              </button>
              <button 
                type="submit"
                :disabled="saving"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"
              >
                {{ saving ? 'Invio...' : 'Invia Richiesta' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const requests = ref([])
const loading = ref(false)
const showModal = ref(false)
const saving = ref(false)
const requestType = ref('')

// Filters
const selectedType = ref('')
const selectedStatus = ref('')
const dateFrom = ref('')

// Form data
const formData = ref({
  start_date: '',
  end_date: '',
  date: '',
  hours: 1,
  reason: ''
})

// Computed
const remainingVacationDays = computed(() => {
  // TODO: Calcolare dalle API
  return 20
})

const usedLeaveHours = computed(() => {
  return requests.value
    .filter(r => r.type === 'leave' && r.status === 'approved')
    .reduce((sum, r) => sum + (r.hours || 0), 0)
})

const totalLeaveHours = computed(() => {
  // TODO: Configurabile per utente
  return 104 // 13 giorni * 8 ore
})

const smartWorkingDays = computed(() => {
  return requests.value
    .filter(r => r.type === 'smart_working' && r.status === 'approved')
    .reduce((sum, r) => sum + (r.days || 0), 0)
})

const pendingRequests = computed(() => {
  return requests.value.filter(r => r.status === 'pending').length
})

// Methods
const loadRequests = async () => {
  loading.value = true

  try {
    const params = new URLSearchParams()

    if (selectedType.value) {
      params.append('type', selectedType.value)
    }

    if (selectedStatus.value) {
      params.append('status', selectedStatus.value)
    }

    if (dateFrom.value) {
      params.append('start_date', dateFrom.value)
    }

    const response = await fetch(`/api/time-off-requests/?${params}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      requests.value = result.data || []
    }
  } catch (err) {
    console.error('Error loading requests:', err)
  } finally {
    loading.value = false
  }
}

const showRequestModal = (type) => {
  requestType.value = type
  formData.value = {
    start_date: '',
    end_date: '',
    date: '',
    hours: 1,
    reason: ''
  }
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  requestType.value = ''
}

const getModalTitle = () => {
  switch (requestType.value) {
    case 'vacation':
      return 'Richiesta Ferie'
    case 'leave':
      return 'Richiesta Permesso'
    case 'smart_working':
      return 'Richiesta Smart Working'
    default:
      return 'Nuova Richiesta'
  }
}

const submitRequest = async () => {
  saving.value = true
  
  try {
    const data = {
      type: requestType.value,
      ...formData.value
    }

    const response = await fetch('/api/timesheet-requests/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify(data)
    })

    if (response.ok) {
      await loadRequests()
      closeModal()
    }
  } catch (err) {
    console.error('Error submitting request:', err)
  } finally {
    saving.value = false
  }
}

const editRequest = (request) => {
  // TODO: Implementare modifica
  console.log('Edit request:', request)
}

const deleteRequest = async (requestId) => {
  if (!confirm('Sei sicuro di voler eliminare questa richiesta?')) return
  
  try {
    const response = await fetch(`/api/timesheet-requests/${requestId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      await loadRequests()
    }
  } catch (err) {
    console.error('Error deleting request:', err)
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT')
}

const formatPeriod = (request) => {
  if (request.type === 'leave') {
    return formatDate(request.date)
  }
  return `${formatDate(request.start_date)} - ${formatDate(request.end_date)}`
}

const formatDuration = (request) => {
  if (request.type === 'leave') {
    return `${request.hours}h`
  }
  return `${request.days || 0} giorni`
}

const getTypeClass = (type) => {
  switch (type) {
    case 'vacation':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    case 'leave':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'smart_working':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
}

const getTypeText = (type) => {
  switch (type) {
    case 'vacation':
      return 'Ferie'
    case 'leave':
      return 'Permesso'
    case 'smart_working':
      return 'Smart Working'
    default:
      return 'Altro'
  }
}

const getStatusClass = (status) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    default:
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'approved':
      return 'Approvato'
    case 'rejected':
      return 'Rifiutato'
    default:
      return 'In Attesa'
  }
}

// Watchers
watch([selectedType, selectedStatus, dateFrom], () => {
  loadRequests()
})

// Lifecycle
onMounted(() => {
  loadRequests()
})
</script>
