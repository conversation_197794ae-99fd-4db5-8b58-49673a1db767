<template>
  <div class="space-y-6">
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Diagramma di Gantt
          </h3>
          <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">Vista:</span>
              <select
                v-model="timeScale"
                @change="calculateTimeline"
                class="text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="weeks">Settimane</option>
                <option value="months">Mesi</option>
              </select>
            </div>
            <button
              @click="resetToToday"
              class="px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"
            >
              Oggi
            </button>
          </div>
        </div>
      </div>

      <!-- Gantt Chart -->
      <div v-if="!loading && tasksWithDates.length > 0" class="p-6">
        <div class="overflow-x-auto">
          <div class="min-w-[1000px]">
            <!-- Header with dates -->
            <div class="flex mb-4">
              <div class="w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Task
              </div>
              <div class="flex-1 flex">
                <div
                  v-for="(period, index) in timelinePeriods"
                  :key="index"
                  class="flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600"
                  :class="{ 'bg-blue-50 dark:bg-blue-900': isCurrentPeriod(period) }"
                >
                  {{ formatPeriodLabel(period) }}
                </div>
              </div>
            </div>

            <!-- Task rows -->
            <div class="space-y-1">
              <div
                v-for="task in tasksWithDates"
                :key="task.id"
                class="flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"
              >
                <!-- Task info -->
                <div class="w-80 flex-shrink-0 px-4 py-3">
                  <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 rounded-full" :class="getStatusColor(task.status)"></div>
                    <div class="flex-1 min-w-0">
                      <p class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ task.name }}</p>
                      <div class="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                        <span v-if="task.assignee">{{ task.assignee.full_name }}</span>
                        <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium" :class="getPriorityClass(task.priority)">
                          {{ getPriorityLabel(task.priority) }}
                        </span>
                      </div>
                      <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {{ formatDate(task.start_date) }} - {{ formatDate(task.due_date) }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Gantt timeline -->
                <div class="flex-1 relative h-12 flex items-center">
                  <div
                    v-if="task.timeline"
                    class="absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer"
                    :class="getTaskBarColor(task.status)"
                    :style="{
                      left: task.timeline.leftPercent + '%',
                      width: task.timeline.widthPercent + '%',
                      minWidth: '60px'
                    }"
                    :title="`${task.name} - ${getTaskProgress(task)}% completato`"
                  >
                    <span class="truncate">{{ task.name.length > 15 ? task.name.substring(0, 15) + '...' : task.name }}</span>
                    <span class="ml-2">{{ getTaskProgress(task) }}%</span>
                  </div>

                  <!-- Progress overlay -->
                  <div
                    v-if="task.timeline && getTaskProgress(task) > 0 && getTaskProgress(task) < 100"
                    class="absolute h-6 rounded-md bg-green-600 opacity-80"
                    :style="{
                      left: task.timeline.leftPercent + '%',
                      width: (task.timeline.widthPercent * getTaskProgress(task) / 100) + '%',
                      minWidth: '2px'
                    }"
                  ></div>

                  <!-- Period dividers -->
                  <div
                    v-for="(period, index) in timelinePeriods"
                    :key="index"
                    class="absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600"
                    :style="{ left: (index / timelinePeriods.length * 100) + '%' }"
                  ></div>


                </div>
              </div>
            </div>

            <!-- Today line with label -->
            <div
              v-if="todayPosition >= 0 && todayPosition <= 100"
              class="absolute top-16 bottom-4 w-0.5 bg-red-500 z-20 pointer-events-none"
              :style="{ left: `calc(320px + ${todayPosition}% * (100% - 320px) / 100)` }"
            >
              <div class="absolute -top-6 -left-8 bg-red-500 text-white text-xs px-2 py-1 rounded shadow-lg">
                Oggi
              </div>
            </div>
          </div>
        </div>

        <!-- Legend -->
        <div class="mt-6 flex items-center space-x-6 text-xs">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-gray-400 rounded"></div>
            <span class="text-gray-600 dark:text-gray-400">Da fare</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-blue-500 rounded"></div>
            <span class="text-gray-600 dark:text-gray-400">In corso</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-yellow-500 rounded"></div>
            <span class="text-gray-600 dark:text-gray-400">In revisione</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-500 rounded"></div>
            <span class="text-gray-600 dark:text-gray-400">Completato</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="w-0.5 h-4 bg-red-500"></div>
            <span class="text-gray-600 dark:text-gray-400">Oggi</span>
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div v-else-if="!loading" class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun task pianificato</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">I task con date di inizio e fine appariranno nel diagramma di Gantt.</p>
      </div>

      <!-- Loading -->
      <div v-if="loading" class="flex justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'

const props = defineProps({
  project: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// State
const timeScale = ref('weeks')
const viewStartDate = ref(new Date())
const timelinePeriods = ref([])
const todayPosition = ref(0)

// Computed
const tasks = computed(() => {
  return props.project?.tasks || []
})

const tasksWithDates = computed(() => {
  return tasks.value.filter(task => task.start_date && task.due_date).map(task => {
    const timeline = calculateTaskTimeline(task)
    return { ...task, timeline }
  })
})

// Methods
const calculateTimeline = () => {
  const now = new Date()
  const startDate = new Date(viewStartDate.value)

  // Calculate periods based on time scale
  const periods = []
  const periodCount = 12

  for (let i = 0; i < periodCount; i++) {
    const periodStart = new Date(startDate)

    if (timeScale.value === 'weeks') {
      periodStart.setDate(startDate.getDate() + (i * 7))
    } else if (timeScale.value === 'months') {
      periodStart.setMonth(startDate.getMonth() + i)
    }

    periods.push(periodStart)
  }

  timelinePeriods.value = periods

  // Calculate today position more accurately
  if (periods.length > 1) {
    const timelineStart = periods[0]
    const timelineEnd = new Date(periods[periods.length - 1])

    // Add one period to get the actual end
    if (timeScale.value === 'weeks') {
      timelineEnd.setDate(timelineEnd.getDate() + 7)
    } else if (timeScale.value === 'months') {
      timelineEnd.setMonth(timelineEnd.getMonth() + 1)
    }

    const totalDuration = timelineEnd - timelineStart
    const todayOffset = now - timelineStart
    todayPosition.value = Math.max(0, Math.min(100, (todayOffset / totalDuration) * 100))
  } else {
    todayPosition.value = 0
  }
}

const calculateTaskTimeline = (task) => {
  if (!timelinePeriods.value.length) return null

  const taskStart = new Date(task.start_date)
  const taskEnd = new Date(task.due_date)
  const timelineStart = timelinePeriods.value[0]
  const timelineEnd = timelinePeriods.value[timelinePeriods.value.length - 1]

  const totalDuration = timelineEnd - timelineStart
  const taskStartOffset = taskStart - timelineStart
  const taskDuration = taskEnd - taskStart

  const leftPercent = Math.max(0, (taskStartOffset / totalDuration) * 100)
  const widthPercent = Math.min(100 - leftPercent, (taskDuration / totalDuration) * 100)

  return {
    leftPercent: leftPercent,
    widthPercent: Math.max(5, widthPercent) // Minimum width for visibility
  }
}

const formatPeriodLabel = (period) => {
  if (timeScale.value === 'weeks') {
    return `${period.getDate()}/${period.getMonth() + 1}`
  } else if (timeScale.value === 'months') {
    return period.toLocaleDateString('it-IT', { month: 'short', year: '2-digit' })
  }
  return ''
}

const isCurrentPeriod = (period) => {
  const now = new Date()
  const periodDate = new Date(period)

  if (timeScale.value === 'weeks') {
    const weekStart = new Date(periodDate)
    const weekEnd = new Date(periodDate)
    weekEnd.setDate(weekEnd.getDate() + 6)
    return now >= weekStart && now <= weekEnd
  } else if (timeScale.value === 'months') {
    return periodDate.getMonth() === now.getMonth() && periodDate.getFullYear() === now.getFullYear()
  }
  return false
}

const resetToToday = () => {
  const now = new Date()
  // Start from beginning of current month/week
  if (timeScale.value === 'weeks') {
    const startOfWeek = new Date(now)
    startOfWeek.setDate(now.getDate() - now.getDay())
    viewStartDate.value = startOfWeek
  } else {
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    viewStartDate.value = startOfMonth
  }
  calculateTimeline()
}

const getStatusColor = (status) => {
  const colors = {
    'todo': 'bg-gray-400',
    'in-progress': 'bg-blue-500',
    'review': 'bg-yellow-500',
    'done': 'bg-green-500'
  }
  return colors[status] || 'bg-gray-400'
}

const getTaskBarColor = (status) => {
  const colors = {
    'todo': 'bg-gray-500',
    'in-progress': 'bg-blue-600',
    'review': 'bg-yellow-600',
    'done': 'bg-green-600'
  }
  return colors[status] || 'bg-gray-500'
}

const getPriorityClass = (priority) => {
  const classes = {
    'low': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    'medium': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'high': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    'urgent': 'bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100'
  }
  return classes[priority] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
}

const getPriorityLabel = (priority) => {
  const labels = {
    'low': 'Bassa',
    'medium': 'Media',
    'high': 'Alta',
    'urgent': 'Urgente'
  }
  return labels[priority] || 'Non specificata'
}

const getTaskProgress = (task) => {
  const statusProgress = {
    'todo': 0,
    'in-progress': 50,
    'review': 75,
    'done': 100
  }
  return statusProgress[task.status] || 0
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('it-IT', {
    day: '2-digit',
    month: '2-digit'
  })
}

// Watchers
watch(() => props.project, () => {
  calculateTimeline()
}, { immediate: true })

// Lifecycle
onMounted(() => {
  resetToToday()
})

// Expose methods
defineExpose({
  refresh: calculateTimeline
})
</script>