# 🎯 **PIANO IMPLEMENTAZIONE FRONTEND TIMESHEET + CRM**

## 📋 **OVERVIEW ARCHITETTURA**

### **🎯 OBIETTIVI:**
- Menu Timesheet separato con workflow approvazioni centralizzato
- Bulk approval con AI anomaly detection
- Sistema notifiche in-app (navbar)
- Gerarchia manager per approvazioni
- Integrazione CRM completa

---

## 🚀 **FASE 1: STRUTTURA BASE TIMESHEET**

### **1.1 Nuove Rotte Router**

**File:** `frontend/src/router/index.js`

```javascript
// Timesheet routes - Menu separato
{
  path: '/app/timesheet',
  meta: { requiresAuth: true },
  children: [
    {
      path: '',
      name: 'timesheet-dashboard',
      component: () => import('@/views/timesheet/TimesheetDashboard.vue'),
      meta: { requiredPermission: 'view_timesheet' }
    },
    {
      path: 'entries',
      name: 'timesheet-entries',
      component: () => import('@/views/timesheet/TimesheetEntries.vue'),
      meta: { requiredPermission: 'manage_timesheet' }
    },
    {
      path: 'approvals',
      name: 'timesheet-approvals',
      component: () => import('@/views/timesheet/TimesheetApprovals.vue'),
      meta: { requiredPermission: 'approve_timesheets' }
    },
    {
      path: 'time-off',
      name: 'time-off-requests',
      component: () => import('@/views/timesheet/TimeOffRequests.vue'),
      meta: { requiredPermission: 'manage_time_off' }
    },
    {
      path: 'reports',
      name: 'timesheet-reports',
      component: () => import('@/views/timesheet/TimesheetReports.vue'),
      meta: { requiredPermission: 'view_timesheet_reports' }
    }
  ]
}
```

### **1.2 Aggiornamento Sidebar Navigation**

**File:** `frontend/src/components/layout/SidebarNavigation.vue`

```javascript
// Aggiungere menu Timesheet separato
{
  name: 'Timesheet',
  icon: 'clock',
  children: [
    { 
      name: 'Dashboard', 
      path: '/app/timesheet', 
      icon: 'chart-bar',
      permission: 'view_timesheet'
    },
    { 
      name: 'Registra Ore', 
      path: '/app/timesheet/entries', 
      icon: 'plus-circle',
      permission: 'manage_timesheet'
    },
    { 
      name: 'Approvazioni', 
      path: '/app/timesheet/approvals', 
      icon: 'check-circle',
      permission: 'approve_timesheets'
    },
    { 
      name: 'Ferie/Permessi', 
      path: '/app/timesheet/time-off', 
      icon: 'calendar',
      permission: 'manage_time_off'
    },
    { 
      name: 'Report', 
      path: '/app/timesheet/reports', 
      icon: 'document-report',
      permission: 'view_timesheet_reports'
    }
  ]
}
```

---

## 🎯 **FASE 2: COMPONENTI TIMESHEET CORE**

### **2.1 Timesheet Dashboard**

**File:** `frontend/src/views/timesheet/TimesheetDashboard.vue`

```javascript
<template>
  <div class="timesheet-dashboard">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Dashboard Timesheet</h1>
      <p class="text-gray-600">Panoramica ore e approvazioni</p>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <StatCard 
        title="Ore Questa Settimana" 
        :value="stats.weeklyHours" 
        color="blue"
        icon="clock"
      />
      <StatCard 
        title="Ore Questo Mese" 
        :value="stats.monthlyHours" 
        color="green"
        icon="calendar"
      />
      <StatCard 
        title="Da Approvare" 
        :value="stats.pendingApprovals" 
        color="yellow"
        icon="exclamation"
        v-if="canApprove"
      />
      <StatCard 
        title="Efficienza" 
        :value="stats.efficiency + '%'" 
        color="purple"
        icon="trending-up"
      />
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- Registrazione Rapida -->
      <QuickTimeEntry @saved="refreshStats" />
      
      <!-- Calendario Ore -->
      <TimesheetCalendar :month="currentMonth" />
    </div>

    <!-- Recent Activity & Notifications -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <RecentTimesheetActivity />
      <PendingApprovals v-if="canApprove" />
    </div>
  </div>
</template>
```

### **2.2 Timesheet Entries**

**File:** `frontend/src/views/timesheet/TimesheetEntries.vue`

```javascript
<template>
  <div class="timesheet-entries">
    <!-- Header con filtri -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Registrazione Ore</h1>
        <button @click="showQuickEntry = true" 
                class="btn-primary">
          <PlusIcon class="w-4 h-4 mr-2" />
          Registra Ore
        </button>
      </div>
      
      <!-- Filtri -->
      <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
        <DateRangePicker v-model="dateRange" />
        <ProjectSelector v-model="selectedProject" />
        <StatusFilter v-model="selectedStatus" />
        <SearchInput v-model="searchQuery" placeholder="Cerca..." />
      </div>
    </div>

    <!-- Lista Entries -->
    <div class="bg-white shadow rounded-lg">
      <TimesheetEntriesTable 
        :entries="filteredEntries"
        :loading="loading"
        @edit="editEntry"
        @delete="deleteEntry"
        @bulk-action="handleBulkAction"
      />
    </div>

    <!-- Modals -->
    <TimesheetEntryModal 
      v-if="showQuickEntry || editingEntry"
      :entry="editingEntry"
      @close="closeModal"
      @saved="handleEntrySaved"
    />
  </div>
</template>
```

---

## 🎯 **FASE 3: SISTEMA APPROVAZIONI CON AI**

### **3.1 Timesheet Approvals - Vista Manager**

**File:** `frontend/src/views/timesheet/TimesheetApprovals.vue`

```javascript
<template>
  <div class="timesheet-approvals">
    <!-- Header con AI Analysis -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold">Approvazioni Timesheet</h1>
          <p class="text-gray-600">Gestisci approvazioni con assistenza AI</p>
        </div>
        <div class="flex items-center space-x-3">
          <!-- AI Anomaly Detection -->
          <button @click="runAnomalyDetection" 
                  :disabled="analyzingAnomalies"
                  class="btn-purple">
            <BeakerIcon class="w-4 h-4 mr-2" />
            {{ analyzingAnomalies ? 'Analizzando...' : 'Rileva Anomalie' }}
          </button>
          
          <!-- Bulk Actions -->
          <div class="relative" v-if="selectedTimesheets.length">
            <button @click="showBulkMenu = !showBulkMenu" 
                    class="btn-secondary">
              Azioni Multiple ({{ selectedTimesheets.length }})
            </button>
            <BulkActionsMenu 
              v-if="showBulkMenu"
              @approve-all="bulkApprove"
              @reject-all="bulkReject"
              @close="showBulkMenu = false"
            />
          </div>
        </div>
      </div>

      <!-- AI Anomalies Alert -->
      <div v-if="anomalies.length" 
           class="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-start">
          <ExclamationTriangleIcon class="w-5 h-5 text-red-400 mt-0.5" />
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              Anomalie Rilevate ({{ anomalies.length }})
            </h3>
            <div class="mt-2 space-y-1">
              <div v-for="anomaly in anomalies" :key="anomaly.id"
                   class="text-sm text-red-700">
                • {{ anomaly.user_name }}: {{ anomaly.description }}
                <button @click="viewAnomalyDetails(anomaly)"
                        class="ml-2 text-red-600 underline">
                  Dettagli
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <StatCard title="Da Approvare" :value="stats.pending" color="yellow" />
      <StatCard title="Approvati" :value="stats.approved" color="green" />
      <StatCard title="Con Anomalie" :value="anomalies.length" color="red" />
      <StatCard title="Ore Totali" :value="stats.totalHours" color="blue" />
    </div>

    <!-- Filtri -->
    <div class="bg-white shadow rounded-lg p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <MonthSelector v-model="selectedMonth" />
        <StatusFilter v-model="selectedStatus" />
        <TeamMemberFilter v-model="selectedMember" :members="teamMembers" />
        <AnomalyFilter v-model="showOnlyAnomalies" />
        <SearchInput v-model="searchQuery" />
      </div>
    </div>

    <!-- Lista Timesheet -->
    <div class="bg-white shadow rounded-lg">
      <TimesheetApprovalsTable 
        :timesheets="filteredTimesheets"
        :loading="loading"
        :selected="selectedTimesheets"
        :anomalies="anomalies"
        @select="handleSelection"
        @view-details="viewDetails"
        @approve="approveTimesheet"
        @reject="rejectTimesheet"
      />
    </div>

    <!-- Modals -->
    <TimesheetDetailModal 
      v-if="showDetailModal"
      :timesheet="selectedTimesheet"
      :anomaly="getAnomalyForTimesheet(selectedTimesheet)"
      @close="showDetailModal = false"
      @approve="approveTimesheet"
      @reject="rejectTimesheet"
    />

    <AnomalyDetailModal
      v-if="showAnomalyModal"
      :anomaly="selectedAnomaly"
      @close="showAnomalyModal = false"
    />
  </div>
</template>
```

### **3.2 AI Anomaly Detection Service**

**File:** `frontend/src/services/aiAnomalyService.js`

```javascript
export const aiAnomalyService = {
  // Rileva anomalie nei timesheet
  detectAnomalies: async (filters = {}) => {
    const response = await apiClient.post('/ai-timesheet/detect-anomalies', {
      month: filters.month,
      year: filters.year,
      team_members: filters.teamMembers,
      analysis_types: [
        'overtime_patterns',
        'weekend_work',
        'unusual_hours',
        'project_switching',
        'productivity_drops',
        'duplicate_entries'
      ]
    })
    return response.data
  },

  // Analizza pattern specifico utente
  analyzeUserPattern: async (userId, timeRange) => {
    const response = await apiClient.post(`/ai-timesheet/analyze-user/${userId}`, {
      start_date: timeRange.start,
      end_date: timeRange.end,
      include_recommendations: true
    })
    return response.data
  },

  // Suggerimenti approvazione
  getApprovalSuggestions: async (timesheetId) => {
    const response = await apiClient.get(`/ai-timesheet/approval-suggestions/${timesheetId}`)
    return response.data
  }
}
```

---

## 🎯 **FASE 4: SISTEMA NOTIFICHE IN-APP**

### **4.1 Notification Service**

**File:** `frontend/src/services/notificationService.js`

```javascript
export const notificationService = {
  // Ottieni notifiche utente
  getUserNotifications: async (filters = {}) => {
    const response = await apiClient.get('/notifications/', {
      params: {
        unread_only: filters.unreadOnly || false,
        type: filters.type,
        limit: filters.limit || 50
      }
    })
    return response.data
  },

  // Marca come letta
  markAsRead: async (notificationId) => {
    const response = await apiClient.put(`/notifications/${notificationId}/read`)
    return response.data
  },

  // Marca tutte come lette
  markAllAsRead: async () => {
    const response = await apiClient.put('/notifications/mark-all-read')
    return response.data
  },

  // Elimina notifica
  deleteNotification: async (notificationId) => {
    const response = await apiClient.delete(`/notifications/${notificationId}`)
    return response.data
  }
}
```

### **4.2 Notification Store**

**File:** `frontend/src/stores/notifications.js`

```javascript
import { defineStore } from 'pinia'
import { notificationService } from '@/services/notificationService'

export const useNotificationStore = defineStore('notifications', {
  state: () => ({
    notifications: [],
    unreadCount: 0,
    loading: false,
    polling: null
  }),

  getters: {
    unreadNotifications: (state) => 
      state.notifications.filter(n => !n.read),
    
    timesheetNotifications: (state) =>
      state.notifications.filter(n => n.type.includes('timesheet')),
    
    approvalNotifications: (state) =>
      state.notifications.filter(n => n.type.includes('approval'))
  },

  actions: {
    async fetchNotifications() {
      this.loading = true
      try {
        const result = await notificationService.getUserNotifications()
        this.notifications = result.data.notifications
        this.unreadCount = result.data.unread_count
      } catch (error) {
        console.error('Error fetching notifications:', error)
      } finally {
        this.loading = false
      }
    },

    async markAsRead(notificationId) {
      try {
        await notificationService.markAsRead(notificationId)
        const notification = this.notifications.find(n => n.id === notificationId)
        if (notification) {
          notification.read = true
          this.unreadCount = Math.max(0, this.unreadCount - 1)
        }
      } catch (error) {
        console.error('Error marking notification as read:', error)
      }
    },

    async markAllAsRead() {
      try {
        await notificationService.markAllAsRead()
        this.notifications.forEach(n => n.read = true)
        this.unreadCount = 0
      } catch (error) {
        console.error('Error marking all notifications as read:', error)
      }
    },

    startPolling() {
      this.polling = setInterval(() => {
        this.fetchNotifications()
      }, 30000) // Poll ogni 30 secondi
    },

    stopPolling() {
      if (this.polling) {
        clearInterval(this.polling)
        this.polling = null
      }
    }
  }
})
```

### **4.3 Notification Bell Component**

**File:** `frontend/src/components/layout/HeaderNotifications.vue`

```javascript
<template>
  <div class="relative">
    <!-- Notification Bell -->
    <button @click="showDropdown = !showDropdown"
            class="relative p-2 text-gray-400 hover:text-gray-600 focus:outline-none">
      <BellIcon class="w-6 h-6" />
      
      <!-- Badge -->
      <span v-if="unreadCount > 0"
            class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
        {{ unreadCount > 99 ? '99+' : unreadCount }}
      </span>
    </button>

    <!-- Dropdown -->
    <div v-if="showDropdown" 
         class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
      <!-- Header -->
      <div class="px-4 py-3 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-sm font-medium text-gray-900">Notifiche</h3>
          <button v-if="unreadCount > 0" 
                  @click="markAllAsRead"
                  class="text-xs text-blue-600 hover:text-blue-800">
            Segna tutte come lette
          </button>
        </div>
      </div>

      <!-- Lista Notifiche -->
      <div class="max-h-96 overflow-y-auto">
        <div v-if="notifications.length === 0" 
             class="px-4 py-8 text-center text-gray-500">
          Nessuna notifica
        </div>
        
        <div v-for="notification in notifications.slice(0, 10)" 
             :key="notification.id"
             class="px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
             :class="{ 'bg-blue-50': !notification.read }"
             @click="handleNotificationClick(notification)">
          
          <div class="flex items-start space-x-3">
            <!-- Icon -->
            <div class="flex-shrink-0">
              <component :is="getNotificationIcon(notification.type)"
                         class="w-5 h-5"
                         :class="getNotificationIconColor(notification.type)" />
            </div>
            
            <!-- Content -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">
                {{ notification.title }}
              </p>
              <p class="text-sm text-gray-500 truncate">
                {{ notification.message }}
              </p>
              <p class="text-xs text-gray-400 mt-1">
                {{ formatRelativeTime(notification.created_at) }}
              </p>
            </div>
            
            <!-- Unread indicator -->
            <div v-if="!notification.read" 
                 class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full">
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="px-4 py-3 border-t border-gray-200">
        <router-link to="/app/notifications" 
                     class="text-sm text-blue-600 hover:text-blue-800">
          Vedi tutte le notifiche
        </router-link>
      </div>
    </div>
  </div>
</template>
```

---

## 🎯 **FASE 5: GERARCHIA MANAGER**

### **5.1 Manager Hierarchy Service**

**File:** `frontend/src/services/managerHierarchyService.js`

```javascript
export const managerHierarchyService = {
  // Ottieni team members sotto il manager
  getTeamMembers: async (managerId = null) => {
    const response = await apiClient.get('/personnel/team-hierarchy', {
      params: { manager_id: managerId }
    })
    return response.data
  },

  // Ottieni timesheet da approvare per il manager
  getPendingApprovalsForManager: async (filters = {}) => {
    const response = await apiClient.get('/monthly-timesheets/pending-approvals', {
      params: {
        month: filters.month,
        year: filters.year,
        team_member_id: filters.teamMemberId
      }
    })
    return response.data
  },

  // Verifica se può approvare timesheet
  canApproveTimesheet: async (timesheetId) => {
    const response = await apiClient.get(`/monthly-timesheets/${timesheetId}/can-approve`)
    return response.data
  }
}
```

---

## 📊 **STRUTTURA FILE COMPLETA**

```
frontend/src/
├── views/timesheet/
│   ├── TimesheetDashboard.vue
│   ├── TimesheetEntries.vue
│   ├── TimesheetApprovals.vue
│   ├── TimeOffRequests.vue
│   ├── TimesheetReports.vue
│   └── components/
│       ├── QuickTimeEntry.vue
│       ├── TimesheetCalendar.vue
│       ├── TimesheetEntriesTable.vue
│       ├── TimesheetApprovalsTable.vue
│       ├── TimesheetDetailModal.vue
│       ├── AnomalyDetailModal.vue
│       ├── BulkActionsMenu.vue
│       └── filters/
│           ├── DateRangePicker.vue
│           ├── ProjectSelector.vue
│           ├── StatusFilter.vue
│           ├── TeamMemberFilter.vue
│           └── AnomalyFilter.vue
├── services/
│   ├── timesheetApi.js
│   ├── aiAnomalyService.js
│   ├── notificationService.js
│   └── managerHierarchyService.js
├── stores/
│   ├── timesheet.js
│   ├── notifications.js
│   └── managerHierarchy.js
└── composables/
    ├── useTimesheetApprovals.js
    ├── useAnomalyDetection.js
    └── useNotifications.js
```

---

## ⏱️ **TIMELINE IMPLEMENTAZIONE**

### **Settimana 1:**
- ✅ Struttura router e sidebar
- ✅ TimesheetDashboard base
- ✅ TimesheetEntries CRUD

### **Settimana 2:**
- ✅ Sistema approvazioni
- ✅ AI anomaly detection
- ✅ Bulk operations

### **Settimana 3:**
- ✅ Sistema notifiche
- ✅ Gerarchia manager
- ✅ Testing e refinement

---

## 🎯 **FASE 6: SISTEMA CRM COMPLETO**

### **6.1 Rotte CRM**

**File:** `frontend/src/router/index.js`

```javascript
// CRM routes - Menu separato
{
  path: '/app/crm',
  meta: { requiresAuth: true },
  children: [
    {
      path: '',
      name: 'crm-dashboard',
      component: () => import('@/views/crm/CRMDashboard.vue'),
      meta: { requiredPermission: 'view_crm' }
    },
    {
      path: 'clients',
      name: 'crm-clients',
      component: () => import('@/views/crm/ClientList.vue'),
      meta: { requiredPermission: 'view_all_clients' }
    },
    {
      path: 'clients/:id',
      name: 'client-detail',
      component: () => import('@/views/crm/ClientDetail.vue'),
      meta: { requiredPermission: 'view_all_clients' }
    },
    {
      path: 'contacts',
      name: 'crm-contacts',
      component: () => import('@/views/crm/ContactList.vue'),
      meta: { requiredPermission: 'view_all_clients' }
    },
    {
      path: 'proposals',
      name: 'crm-proposals',
      component: () => import('@/views/crm/ProposalList.vue'),
      meta: { requiredPermission: 'view_all_proposals' }
    },
    {
      path: 'proposals/:id',
      name: 'proposal-detail',
      component: () => import('@/views/crm/ProposalDetail.vue'),
      meta: { requiredPermission: 'view_all_proposals' }
    },
    {
      path: 'contracts',
      name: 'crm-contracts',
      component: () => import('@/views/crm/ContractList.vue'),
      meta: { requiredPermission: 'view_contracts' }
    },
    {
      path: 'invoices',
      name: 'crm-invoices',
      component: () => import('@/views/crm/InvoiceList.vue'),
      meta: { requiredPermission: 'view_invoices' }
    }
  ]
}
```

### **6.2 Menu CRM in Sidebar**

```javascript
// Aggiungere in SidebarNavigation.vue
{
  name: 'CRM',
  icon: 'users',
  children: [
    {
      name: 'Dashboard',
      path: '/app/crm',
      icon: 'chart-bar',
      permission: 'view_crm'
    },
    {
      name: 'Clienti/Lead',
      path: '/app/crm/clients',
      icon: 'office-building',
      permission: 'view_all_clients'
    },
    {
      name: 'Contatti',
      path: '/app/crm/contacts',
      icon: 'user-group',
      permission: 'view_all_clients'
    },
    {
      name: 'Proposte',
      path: '/app/crm/proposals',
      icon: 'document-text',
      permission: 'view_all_proposals'
    },
    {
      name: 'Contratti',
      path: '/app/crm/contracts',
      icon: 'clipboard-check',
      permission: 'view_contracts'
    },
    {
      name: 'Fatture',
      path: '/app/crm/invoices',
      icon: 'receipt-tax',
      permission: 'view_invoices'
    }
  ]
}
```

### **6.3 CRM Dashboard**

**File:** `frontend/src/views/crm/CRMDashboard.vue`

```javascript
<template>
  <div class="crm-dashboard">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">CRM Dashboard</h1>
      <p class="text-gray-600">Panoramica vendite e opportunità</p>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
      <StatCard
        title="Lead Attivi"
        :value="stats.activeLeads"
        color="blue"
        icon="user-plus"
      />
      <StatCard
        title="Proposte in Corso"
        :value="stats.activeProposals"
        color="yellow"
        icon="document"
      />
      <StatCard
        title="Contratti Attivi"
        :value="stats.activeContracts"
        color="green"
        icon="clipboard-check"
      />
      <StatCard
        title="Revenue Mensile"
        :value="formatCurrency(stats.monthlyRevenue)"
        color="purple"
        icon="currency-euro"
      />
    </div>

    <!-- Pipeline Overview -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <!-- Lead Pipeline -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium mb-4">Pipeline Lead</h3>
        <LeadPipelineChart :data="pipelineData.leads" />
      </div>

      <!-- Proposal Pipeline -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium mb-4">Pipeline Proposte</h3>
        <ProposalPipelineChart :data="pipelineData.proposals" />
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <RecentCRMActivity />
      <UpcomingTasks />
    </div>
  </div>
</template>
```

### **6.4 Client Management con Lead Pipeline**

**File:** `frontend/src/views/crm/ClientList.vue`

```javascript
<template>
  <div class="client-list">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Gestione Clienti</h1>
        <button @click="showCreateModal = true" class="btn-primary">
          <PlusIcon class="w-4 h-4 mr-2" />
          Nuovo Cliente/Lead
        </button>
      </div>

      <!-- Filtri -->
      <div class="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
        <StatusFilter v-model="selectedStatus"
                      :options="clientStatuses" />
        <IndustryFilter v-model="selectedIndustry" />
        <SearchInput v-model="searchQuery" />
        <ViewToggle v-model="viewMode"
                    :options="['table', 'kanban']" />
      </div>
    </div>

    <!-- Vista Kanban per Lead Management -->
    <div v-if="viewMode === 'kanban'" class="mb-6">
      <LeadKanbanBoard
        :clients="filteredClients"
        @move-client="moveClientStatus"
        @edit-client="editClient"
      />
    </div>

    <!-- Vista Tabella -->
    <div v-else class="bg-white shadow rounded-lg">
      <ClientTable
        :clients="filteredClients"
        :loading="loading"
        @edit="editClient"
        @delete="deleteClient"
        @convert="convertLead"
      />
    </div>

    <!-- Modals -->
    <ClientModal
      v-if="showCreateModal || editingClient"
      :client="editingClient"
      @close="closeModal"
      @saved="handleClientSaved"
    />
  </div>
</template>
```

### **6.5 Proposal Management con Opportunity Pipeline**

**File:** `frontend/src/views/crm/ProposalList.vue`

```javascript
<template>
  <div class="proposal-list">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-bold">Gestione Proposte</h1>
        <button @click="showCreateModal = true" class="btn-primary">
          <DocumentAddIcon class="w-4 h-4 mr-2" />
          Nuova Proposta
        </button>
      </div>

      <!-- Filtri -->
      <div class="mt-4 grid grid-cols-1 md:grid-cols-5 gap-4">
        <StatusFilter v-model="selectedStatus"
                      :options="proposalStatuses" />
        <ClientFilter v-model="selectedClient" />
        <DateRangeFilter v-model="dateRange" />
        <ValueRangeFilter v-model="valueRange" />
        <ViewToggle v-model="viewMode"
                    :options="['table', 'kanban']" />
      </div>
    </div>

    <!-- Vista Kanban per Opportunity Management -->
    <div v-if="viewMode === 'kanban'" class="mb-6">
      <OpportunityKanbanBoard
        :proposals="filteredProposals"
        @move-proposal="moveProposalStatus"
        @edit-proposal="editProposal"
      />
    </div>

    <!-- Vista Tabella -->
    <div v-else class="bg-white shadow rounded-lg">
      <ProposalTable
        :proposals="filteredProposals"
        :loading="loading"
        @edit="editProposal"
        @delete="deleteProposal"
        @convert="convertToContract"
      />
    </div>

    <!-- Modals -->
    <ProposalModal
      v-if="showCreateModal || editingProposal"
      :proposal="editingProposal"
      @close="closeModal"
      @saved="handleProposalSaved"
    />
  </div>
</template>
```

---

## 🎯 **FASE 7: INTEGRAZIONE PROGETTI-CONTRATTI**

### **7.1 Modifica ProjectOverview.vue**

**File:** `frontend/src/views/projects/components/ProjectOverview.vue`

```javascript
// Aggiungere sezione Contract Info
<div class="bg-white shadow rounded-lg p-6" v-if="project.contract">
  <div class="flex items-center justify-between mb-4">
    <h3 class="text-lg font-medium text-gray-900">Contratto Collegato</h3>
    <router-link :to="`/app/crm/contracts/${project.contract.id}`"
                 class="text-blue-600 hover:text-blue-800 text-sm">
      Gestisci Contratto →
    </router-link>
  </div>

  <div class="grid grid-cols-2 gap-4">
    <div>
      <dt class="text-sm text-gray-500">Numero Contratto</dt>
      <dd class="text-sm font-medium">{{ project.contract.contract_number }}</dd>
    </div>
    <div>
      <dt class="text-sm text-gray-500">Tipo</dt>
      <dd class="text-sm font-medium">{{ project.contract.contract_type }}</dd>
    </div>
    <div>
      <dt class="text-sm text-gray-500">Tariffa Oraria</dt>
      <dd class="text-sm font-medium">{{ formatCurrency(project.contract.hourly_rate) }}</dd>
    </div>
    <div>
      <dt class="text-sm text-gray-500">Stato</dt>
      <dd>
        <StatusBadge :status="project.contract.status" />
      </dd>
    </div>
  </div>
</div>
```

### **7.2 Modifica ProjectTimesheet.vue**

```javascript
// Aggiungere campi billing nel form timesheet
<div class="grid grid-cols-2 gap-4">
  <div>
    <label class="block text-sm font-medium">Fatturabile</label>
    <select v-model="formData.billable" class="form-select">
      <option :value="true">Sì</option>
      <option :value="false">No</option>
    </select>
  </div>
  <div v-if="formData.billable">
    <label class="block text-sm font-medium">Tariffa (€/h)</label>
    <input v-model="formData.billing_rate"
           type="number"
           step="0.01"
           :placeholder="defaultBillingRate"
           class="form-input">
  </div>
</div>

// Aggiungere indicatori billing nella tabella
<td class="px-2 py-3 text-center">
  <div class="flex items-center justify-center space-x-1">
    <span class="text-xs font-medium">{{ formatHours(task.daily_hours[day]) }}</span>
    <span v-if="task.daily_billable[day]"
          class="w-2 h-2 bg-green-500 rounded-full"
          title="Fatturabile"></span>
  </div>
</td>
```

---

## 🎯 **FASE 8: COMPONENTI CONDIVISI**

### **8.1 Kanban Board Component**

**File:** `frontend/src/components/ui/KanbanBoard.vue`

```javascript
<template>
  <div class="kanban-board">
    <div class="flex space-x-6 overflow-x-auto pb-4">
      <div v-for="column in columns"
           :key="column.id"
           class="flex-shrink-0 w-80">

        <!-- Column Header -->
        <div class="bg-gray-100 rounded-t-lg p-4">
          <div class="flex items-center justify-between">
            <h3 class="font-medium text-gray-900">
              {{ column.title }}
            </h3>
            <span class="bg-gray-200 text-gray-600 px-2 py-1 rounded-full text-xs">
              {{ getItemsForColumn(column.id).length }}
            </span>
          </div>
        </div>

        <!-- Column Content -->
        <div class="bg-gray-50 rounded-b-lg p-4 min-h-96">
          <draggable
            v-model="columnItems[column.id]"
            group="kanban"
            @change="handleMove"
            class="space-y-3">

            <KanbanCard
              v-for="item in getItemsForColumn(column.id)"
              :key="item.id"
              :item="item"
              :type="cardType"
              @edit="$emit('edit-item', item)"
              @delete="$emit('delete-item', item)"
            />
          </draggable>
        </div>
      </div>
    </div>
  </div>
</template>
```

### **8.2 AI Anomaly Detection Component**

**File:** `frontend/src/components/timesheet/AnomalyDetector.vue`

```javascript
<template>
  <div class="anomaly-detector">
    <!-- AI Analysis Button -->
    <button @click="runDetection"
            :disabled="analyzing"
            class="btn-purple">
      <BeakerIcon class="w-4 h-4 mr-2" />
      {{ analyzing ? 'Analizzando...' : 'Rileva Anomalie AI' }}
    </button>

    <!-- Anomalies Display -->
    <div v-if="anomalies.length" class="mt-4 space-y-3">
      <div v-for="anomaly in anomalies"
           :key="anomaly.id"
           class="bg-red-50 border border-red-200 rounded-lg p-4">

        <div class="flex items-start">
          <ExclamationTriangleIcon class="w-5 h-5 text-red-400 mt-0.5" />
          <div class="ml-3 flex-1">
            <h4 class="text-sm font-medium text-red-800">
              {{ anomaly.type_label }}
            </h4>
            <p class="text-sm text-red-700 mt-1">
              {{ anomaly.description }}
            </p>

            <!-- Anomaly Details -->
            <div class="mt-2 text-xs text-red-600">
              <span>Utente: {{ anomaly.user_name }}</span>
              <span class="mx-2">•</span>
              <span>Confidenza: {{ anomaly.confidence }}%</span>
              <span class="mx-2">•</span>
              <span>{{ formatDate(anomaly.detected_at) }}</span>
            </div>

            <!-- Suggested Actions -->
            <div v-if="anomaly.suggested_actions" class="mt-3">
              <p class="text-xs font-medium text-red-800">Azioni Suggerite:</p>
              <ul class="text-xs text-red-700 mt-1 space-y-1">
                <li v-for="action in anomaly.suggested_actions" :key="action">
                  • {{ action }}
                </li>
              </ul>
            </div>
          </div>

          <button @click="dismissAnomaly(anomaly)"
                  class="text-red-400 hover:text-red-600">
            <XMarkIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
```

---

## 📊 **STRUTTURA FILE CRM COMPLETA**

```
frontend/src/
├── views/crm/
│   ├── CRMDashboard.vue
│   ├── ClientList.vue
│   ├── ClientDetail.vue
│   ├── ContactList.vue
│   ├── ProposalList.vue
│   ├── ProposalDetail.vue
│   ├── ContractList.vue
│   ├── InvoiceList.vue
│   └── components/
│       ├── LeadKanbanBoard.vue
│       ├── OpportunityKanbanBoard.vue
│       ├── ClientTable.vue
│       ├── ProposalTable.vue
│       ├── ClientModal.vue
│       ├── ProposalModal.vue
│       ├── LeadPipelineChart.vue
│       ├── ProposalPipelineChart.vue
│       └── filters/
│           ├── StatusFilter.vue
│           ├── IndustryFilter.vue
│           ├── ClientFilter.vue
│           ├── DateRangeFilter.vue
│           └── ValueRangeFilter.vue
├── components/ui/
│   ├── KanbanBoard.vue
│   ├── KanbanCard.vue
│   ├── StatusBadge.vue
│   └── ViewToggle.vue
└── components/timesheet/
    ├── AnomalyDetector.vue
    ├── BulkApprovalActions.vue
    └── TimesheetNotifications.vue
```

---

## ⚡ **FEATURES AVANZATE**

### **🤖 AI Integration:**
- Anomaly detection per timesheet
- Lead scoring automatico
- Proposal success prediction
- Resource allocation optimization

### **📱 Real-time Features:**
- Notifiche in-app live
- Status updates real-time
- Collaborative editing

### **📊 Analytics:**
- Pipeline conversion rates
- Team productivity metrics
- Revenue forecasting
- Client satisfaction tracking

### **🔐 Security:**
- Role-based permissions
- Data encryption
- Audit logging
- GDPR compliance

**Procediamo con l'implementazione?**
