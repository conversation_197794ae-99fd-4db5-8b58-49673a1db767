import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from extensions import db
import sqlalchemy as sa

# Crea l'applicazione
app = create_app()

def show_tables():
    """Mostra tutte le tabelle nel database"""
    with app.app_context():
        inspector = sa.inspect(db.engine)
        tables = inspector.get_table_names()
        print("\nTabelle nel database:")
        for table in sorted(tables):
            print(f"- {table}")
        print("")

def execute_sql(sql_statement):
    """Esegue uno statement SQL"""
    with app.app_context():
        try:
            db.session.execute(sa.text(sql_statement))
            db.session.commit()
            print(f"SQL eseguito con successo: {sql_statement[:50]}...")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Errore durante l'esecuzione SQL: {e}")
            return False

def run_sql_file(filename):
    """Esegue tutti gli statement SQL da un file"""
    try:
        with open(filename, 'r') as file:
            sql_script = file.read()
            statements = sql_script.split(';')
            success_count = 0

            for statement in statements:
                if statement.strip():
                    if execute_sql(statement):
                        success_count += 1

            print(f"\nEseguiti {success_count} statements SQL da {filename}")
            return True
    except Exception as e:
        print(f"Errore durante la lettura/esecuzione del file SQL: {e}")
        return False

def add_start_date_to_task():
    """Aggiunge il campo start_date alla tabella task"""
    with app.app_context():
        try:
            # Verifica se la colonna esiste già
            inspector = sa.inspect(db.engine)
            columns = inspector.get_columns('task')
            column_names = [col['name'] for col in columns]

            if 'start_date' in column_names:
                print("Campo start_date già presente nella tabella task")
                return True

            # Aggiungi la colonna start_date
            sql = "ALTER TABLE task ADD COLUMN start_date DATE"
            db.session.execute(sa.text(sql))
            db.session.commit()
            print("Campo start_date aggiunto con successo alla tabella task")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"Errore durante l'aggiunta del campo start_date: {e}")
            return False

def create_hr_tables():
    """Crea le nuove tabelle HR: departments, user_profiles, user_skills_detailed"""
    with app.app_context():
        try:
            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            print("🏗️  Creazione tabelle HR...")

            # 1. Tabella departments
            if 'departments' not in existing_tables:
                sql_departments = """
                CREATE TABLE departments (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100) NOT NULL UNIQUE,
                    description TEXT,
                    manager_id INTEGER,
                    parent_id INTEGER,
                    budget FLOAT DEFAULT 0.0,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (manager_id) REFERENCES "user" (id),
                    FOREIGN KEY (parent_id) REFERENCES departments (id)
                )
                """
                db.session.execute(sa.text(sql_departments))
                print("  ✅ Tabella 'departments' creata")
            else:
                print("  ⚠️  Tabella 'departments' già esistente")

            # 2. Tabella user_profiles
            if 'user_profiles' not in existing_tables:
                sql_user_profiles = """
                CREATE TABLE user_profiles (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL UNIQUE,
                    employee_id VARCHAR(20) UNIQUE,
                    job_title VARCHAR(100),
                    birth_date DATE,
                    address TEXT,
                    emergency_contact_name VARCHAR(100),
                    emergency_contact_phone VARCHAR(20),
                    emergency_contact_relationship VARCHAR(50),
                    employment_type VARCHAR(50) DEFAULT 'full_time',
                    work_location VARCHAR(100),
                    salary FLOAT,
                    salary_currency VARCHAR(3) DEFAULT 'EUR',
                    probation_end_date DATE,
                    contract_end_date DATE,
                    notice_period_days INTEGER DEFAULT 30,
                    weekly_hours FLOAT DEFAULT 40.0,
                    daily_hours FLOAT DEFAULT 8.0,
                    profile_completion FLOAT DEFAULT 0.0,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES "user" (id)
                )
                """
                db.session.execute(sa.text(sql_user_profiles))
                print("  ✅ Tabella 'user_profiles' creata")
            else:
                print("  ⚠️  Tabella 'user_profiles' già esistente")

            # 3. Tabella user_skills_detailed
            if 'user_skills_detailed' not in existing_tables:
                sql_user_skills_detailed = """
                CREATE TABLE user_skills_detailed (
                    id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    skill_id INTEGER NOT NULL,
                    proficiency_level INTEGER DEFAULT 1,
                    years_experience FLOAT DEFAULT 0.0,
                    is_certified BOOLEAN DEFAULT FALSE,
                    certification_name VARCHAR(100),
                    certification_date DATE,
                    certification_expiry DATE,
                    self_assessed BOOLEAN DEFAULT TRUE,
                    manager_assessed BOOLEAN DEFAULT FALSE,
                    manager_assessment_date DATE,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES "user" (id),
                    FOREIGN KEY (skill_id) REFERENCES skill (id),
                    UNIQUE (user_id, skill_id)
                )
                """
                db.session.execute(sa.text(sql_user_skills_detailed))
                print("  ✅ Tabella 'user_skills_detailed' creata")
            else:
                print("  ⚠️  Tabella 'user_skills_detailed' già esistente")

            # 4. Aggiungi campo department_id alla tabella user se non esiste
            user_columns = inspector.get_columns('user')
            user_column_names = [col['name'] for col in user_columns]

            if 'department_id' not in user_column_names:
                sql_add_department_id = 'ALTER TABLE "user" ADD COLUMN department_id INTEGER REFERENCES departments(id)'
                db.session.execute(sa.text(sql_add_department_id))
                print("  ✅ Campo 'department_id' aggiunto alla tabella 'user'")
            else:
                print("  ⚠️  Campo 'department_id' già presente nella tabella 'user'")

            db.session.commit()
            print("🎉 Tabelle HR create con successo!")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione delle tabelle HR: {e}")
            return False

def migrate_user_skills():
    """Migra i dati dalla tabella user_skills alla nuova user_skills_detailed"""
    with app.app_context():
        try:
            print("🔄 Migrazione competenze utenti...")

            # Verifica se ci sono dati da migrare
            result = db.session.execute(sa.text("SELECT COUNT(*) FROM user_skills")).fetchone()
            old_skills_count = result[0] if result else 0

            if old_skills_count == 0:
                print("  ⚠️  Nessuna competenza da migrare dalla tabella user_skills")
                return True

            # Migra i dati
            migrate_sql = """
            INSERT INTO user_skills_detailed (user_id, skill_id, proficiency_level, self_assessed, created_at)
            SELECT user_id, skill_id, 3, 1, CURRENT_TIMESTAMP
            FROM user_skills
            WHERE NOT EXISTS (
                SELECT 1 FROM user_skills_detailed
                WHERE user_skills_detailed.user_id = user_skills.user_id
                AND user_skills_detailed.skill_id = user_skills.skill_id
            )
            """

            result = db.session.execute(sa.text(migrate_sql))
            migrated_count = result.rowcount

            db.session.commit()
            print(f"  ✅ Migrate {migrated_count} competenze dalla tabella user_skills")
            print("  ℹ️  Le competenze sono state impostate con livello 3 (Intermedio) di default")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la migrazione delle competenze: {e}")
            return False

def populate_orgchart_data():
    """Popola il database con dati di esempio per l'organigramma"""
    with app.app_context():
        try:
            print("🏢 Popolamento dati organigramma...")

            # Verifica se ci sono già dipartimenti
            result = db.session.execute(sa.text("SELECT COUNT(*) FROM departments")).fetchone()
            dept_count = result[0] if result else 0

            if dept_count > 0:
                print(f"  ⚠️  Trovati {dept_count} dipartimenti esistenti. Aggiorno solo se necessario...")

            # 1. Crea dipartimenti principali
            departments_data = [
                # Dipartimenti di primo livello
                ("Direzione Generale", "Direzione e coordinamento strategico aziendale", None, None, 500000.0),
                ("IT & Technology", "Sviluppo software e infrastruttura tecnologica", None, None, 200000.0),
                ("Risorse Umane", "Gestione del personale e sviluppo organizzativo", None, None, 80000.0),
                ("Amministrazione", "Gestione amministrativa e finanziaria", None, None, 120000.0),
                ("Commerciale", "Vendite e sviluppo business", None, None, 150000.0),
                ("Marketing", "Marketing e comunicazione", None, None, 100000.0),

                # Sottidipartimenti IT
                ("Development", "Team di sviluppo software", None, 2, 120000.0),
                ("Infrastructure", "Gestione infrastruttura e DevOps", None, 2, 80000.0),
                ("QA & Testing", "Quality Assurance e testing", None, 2, 60000.0),

                # Sottosottidipartimenti Development
                ("Frontend Team", "Sviluppo interfacce utente", None, 7, 50000.0),
                ("Backend Team", "Sviluppo servizi e API", None, 7, 70000.0),
                ("Mobile Team", "Sviluppo applicazioni mobile", None, 7, 40000.0),

                # Sottidipartimenti HR
                ("Recruiting", "Selezione e acquisizione talenti", None, 3, 30000.0),
                ("Training & Development", "Formazione e sviluppo competenze", None, 3, 25000.0),

                # Sottidipartimenti Commerciale
                ("Sales", "Vendite dirette", None, 5, 80000.0),
                ("Business Development", "Sviluppo nuovi mercati", None, 5, 70000.0),
                ("Customer Success", "Gestione clienti esistenti", None, 5, 50000.0),
            ]

            # Inserisci dipartimenti
            for name, description, manager_id, parent_id, budget in departments_data:
                # Verifica se il dipartimento esiste già
                existing = db.session.execute(
                    sa.text("SELECT id FROM departments WHERE name = :name"),
                    {"name": name}
                ).fetchone()

                if not existing:
                    insert_sql = """
                    INSERT INTO departments (name, description, manager_id, parent_id, budget, is_active, created_at, updated_at)
                    VALUES (:name, :description, :manager_id, :parent_id, :budget, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                    """
                    db.session.execute(sa.text(insert_sql), {
                        "name": name,
                        "description": description,
                        "manager_id": manager_id,
                        "parent_id": parent_id,
                        "budget": budget
                    })
                    print(f"  ✅ Dipartimento '{name}' creato")
                else:
                    print(f"  ⚠️  Dipartimento '{name}' già esistente")

            # 2. Crea utenti di esempio se non esistono
            users_data = [
                ("mario.rossi", "<EMAIL>", "Mario", "Rossi", "admin", "CEO", 1),
                ("anna.verdi", "<EMAIL>", "Anna", "Verdi", "manager", "CTO", 2),
                ("luca.bianchi", "<EMAIL>", "Luca", "Bianchi", "manager", "HR Director", 3),
                ("sara.neri", "<EMAIL>", "Sara", "Neri", "manager", "CFO", 4),
                ("paolo.gialli", "<EMAIL>", "Paolo", "Gialli", "manager", "Sales Director", 5),
                ("elena.blu", "<EMAIL>", "Elena", "Blu", "manager", "Marketing Director", 6),
                ("marco.viola", "<EMAIL>", "Marco", "Viola", "manager", "Dev Manager", 7),
                ("giulia.rosa", "<EMAIL>", "Giulia", "Rosa", "manager", "Infrastructure Manager", 8),
                ("andrea.oro", "<EMAIL>", "Andrea", "Oro", "manager", "QA Manager", 9),
                ("francesca.argento", "<EMAIL>", "Francesca", "Argento", "employee", "Frontend Lead", 10),
                ("roberto.bronzo", "<EMAIL>", "Roberto", "Bronzo", "employee", "Backend Lead", 11),
                ("chiara.rame", "<EMAIL>", "Chiara", "Rame", "employee", "Mobile Lead", 12),
                ("davide.ferro", "<EMAIL>", "Davide", "Ferro", "employee", "Recruiter", 13),
                ("laura.acciaio", "<EMAIL>", "Laura", "Acciaio", "employee", "Training Manager", 14),
                ("simone.titanio", "<EMAIL>", "Simone", "Titanio", "employee", "Sales Manager", 15),
                ("valentina.platino", "<EMAIL>", "Valentina", "Platino", "employee", "Business Dev", 16),
                ("alessandro.zinco", "<EMAIL>", "Alessandro", "Zinco", "employee", "Customer Success", 17),
            ]

            for username, email, first_name, last_name, role, position, dept_id in users_data:
                # Verifica se l'utente esiste già
                existing = db.session.execute(
                    sa.text("SELECT id FROM \"user\" WHERE username = :username"),
                    {"username": username}
                ).fetchone()

                if not existing:
                    insert_sql = """
                    INSERT INTO "user" (username, email, first_name, last_name, role, position, department_id, is_active, created_at)
                    VALUES (:username, :email, :first_name, :last_name, :role, :position, :dept_id, TRUE, CURRENT_TIMESTAMP)
                    """
                    db.session.execute(sa.text(insert_sql), {
                        "username": username,
                        "email": email,
                        "first_name": first_name,
                        "last_name": last_name,
                        "role": role,
                        "position": position,
                        "dept_id": dept_id
                    })
                    print(f"  ✅ Utente '{username}' creato")
                else:
                    print(f"  ⚠️  Utente '{username}' già esistente")

            # 3. Aggiorna i manager dei dipartimenti
            manager_assignments = [
                (1, 1),  # Direzione Generale -> Mario Rossi
                (2, 2),  # IT & Technology -> Anna Verdi
                (3, 3),  # Risorse Umane -> Luca Bianchi
                (4, 4),  # Amministrazione -> Sara Neri
                (5, 5),  # Commerciale -> Paolo Gialli
                (6, 6),  # Marketing -> Elena Blu
                (7, 7),  # Development -> Marco Viola
                (8, 8),  # Infrastructure -> Giulia Rosa
                (9, 9),  # QA & Testing -> Andrea Oro
                (10, 10), # Frontend Team -> Francesca Argento
                (11, 11), # Backend Team -> Roberto Bronzo
                (12, 12), # Mobile Team -> Chiara Rame
                (13, 13), # Recruiting -> Davide Ferro
                (14, 14), # Training & Development -> Laura Acciaio
                (15, 15), # Sales -> Simone Titanio
                (16, 16), # Business Development -> Valentina Platino
                (17, 17), # Customer Success -> Alessandro Zinco
            ]

            for dept_id, manager_id in manager_assignments:
                # Verifica se l'utente esiste
                user_exists = db.session.execute(
                    sa.text("SELECT id FROM \"user\" WHERE id = :user_id"),
                    {"user_id": manager_id}
                ).fetchone()

                if user_exists:
                    update_sql = "UPDATE departments SET manager_id = :manager_id WHERE id = :dept_id"
                    db.session.execute(sa.text(update_sql), {
                        "manager_id": manager_id,
                        "dept_id": dept_id
                    })
                    print(f"  ✅ Manager assegnato al dipartimento {dept_id}")

            db.session.commit()
            print("🎉 Dati organigramma popolati con successo!")

            # Mostra statistiche
            dept_count = db.session.execute(sa.text("SELECT COUNT(*) FROM departments")).fetchone()[0]
            user_count = db.session.execute(sa.text("SELECT COUNT(*) FROM \"user\"")).fetchone()[0]
            print(f"📊 Statistiche: {dept_count} dipartimenti, {user_count} utenti")

            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante il popolamento dati organigramma: {e}")
            return False

def setup_timesheet_module():
    """Setup completo del modulo Timesheet - Task 3.1 + 4"""
    print("🚀 Setup modulo Timesheet & CRM - Task 3.1 + 4")
    print("=" * 50)

    success = True

    # 1. Crea le nuove tabelle
    if not create_timesheet_tables():
        success = False

    # 2. Estendi tabella timesheet esistente
    if not extend_timesheet_table():
        success = False

    # 3. Popola dati di esempio
    if not populate_timesheet_sample_data():
        success = False

    if success:
        print("\n🎉 Setup modulo Timesheet completato con successo!")
        print("📋 Funzionalità disponibili:")
        print("   - MonthlyTimesheet: Approvazione mensile")
        print("   - TimeOffRequest: Ferie/permessi/smartworking")
        print("   - Contract: Contratti clienti")
        print("   - Invoice: Fatturazione per periodo")
        print("   - TimesheetEntry: Ore con billing info")
    else:
        print("\n❌ Setup modulo Timesheet fallito. Controlla gli errori sopra.")

    return success

def create_timesheet_tables():
    """Crea le nuove tabelle per Task 3.1 + 4"""
    with app.app_context():
        try:
            print("🏗️  Creazione tabelle Timesheet & CRM...")

            # Usa SQLAlchemy per creare tutte le tabelle
            from models import (
                MonthlyTimesheet, TimeOffRequest, Contract,
                Invoice, InvoiceLine
            )

            db.create_all()

            # Verifica che le tabelle siano state create
            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            required_tables = [
                'monthly_timesheets',
                'time_off_requests',
                'contracts',
                'invoices',
                'invoice_lines'
            ]

            for table in required_tables:
                if table in existing_tables:
                    print(f"  ✅ Tabella '{table}' creata/verificata")
                else:
                    print(f"  ❌ Tabella '{table}' NON trovata")
                    return False

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante la creazione tabelle Timesheet: {e}")
            return False

def extend_timesheet_table():
    """Estende la tabella timesheet esistente con nuovi campi"""
    with app.app_context():
        try:
            print("🔧 Estensione tabella timesheet...")

            inspector = sa.inspect(db.engine)
            columns = inspector.get_columns('timesheet')
            column_names = [col['name'] for col in columns]

            # Campi da aggiungere
            new_columns = [
                ("monthly_timesheet_id", "INTEGER"),
                ("billable", "BOOLEAN DEFAULT FALSE"),
                ("billing_rate", "FLOAT"),
                ("contract_id", "INTEGER"),
                ("invoice_line_id", "INTEGER"),
                ("billing_status", "VARCHAR(20) DEFAULT 'unbilled'")
            ]

            for column_name, column_type in new_columns:
                if column_name not in column_names:
                    sql = f"ALTER TABLE timesheet ADD COLUMN {column_name} {column_type}"
                    db.session.execute(sa.text(sql))
                    print(f"  ✅ Campo '{column_name}' aggiunto")
                else:
                    print(f"  ⚠️  Campo '{column_name}' già esistente")

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante l'estensione tabella timesheet: {e}")
            return False

def populate_timesheet_sample_data():
    """Popola dati di esempio per testing"""
    with app.app_context():
        try:
            print("🌱 Popolamento dati esempio Timesheet...")

            # Verifica se ci sono già dati
            contract_count = db.session.execute(sa.text("SELECT COUNT(*) FROM contracts")).fetchone()[0]
            if contract_count > 0:
                print("  ⚠️  Dati esempio già presenti")
                return True

            # Trova primo cliente
            client_result = db.session.execute(sa.text("SELECT id FROM client LIMIT 1")).fetchone()
            if not client_result:
                print("  ⚠️  Nessun cliente trovato, salto dati esempio")
                return True

            client_id = client_result[0]

            # Crea contratto esempio
            contract_sql = """
            INSERT INTO contracts (client_id, contract_number, title, description, contract_type, hourly_rate, budget_hours, start_date, status, created_at, updated_at)
            VALUES (:client_id, 'CTR-2024-001', 'Contratto Sviluppo Software', 'Contratto per sviluppo applicazione web', 'hourly', 80.0, 100.0, CURRENT_DATE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """

            db.session.execute(sa.text(contract_sql), {"client_id": client_id})
            print("  ✅ Contratto esempio creato")

            db.session.commit()
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante popolamento dati esempio: {e}")
            return False

def cleanup_duplicate_tables():
    """Rimuove tabelle duplicate mantenendo solo quelle corrette"""
    with app.app_context():
        try:
            print("🧹 Pulizia tabelle duplicate...")

            # Tabelle da eliminare
            tables_to_drop = [
                'time_off_request',    # Mantieni time_off_requests
                'timesheet_entries',   # Mantieni timesheet
                'timesheet_entry',     # Mantieni timesheet
                'timesheets'           # Mantieni timesheet
            ]

            inspector = sa.inspect(db.engine)
            existing_tables = inspector.get_table_names()

            for table in tables_to_drop:
                if table in existing_tables:
                    try:
                        db.session.execute(sa.text(f"DROP TABLE {table}"))
                        print(f"  ✅ Tabella '{table}' eliminata")
                    except Exception as e:
                        print(f"  ⚠️  Errore eliminazione '{table}': {e}")
                else:
                    print(f"  ⏭️  Tabella '{table}' non trovata")

            db.session.commit()
            print("✅ Pulizia completata")
            return True

        except Exception as e:
            db.session.rollback()
            print(f"❌ Errore durante pulizia: {e}")
            return False

def setup_hr_module():
    """Setup completo del modulo HR"""
    print("🚀 Setup modulo HR - Task 7.1")
    print("=" * 50)

    success = True

    # 1. Crea le tabelle HR
    if not create_hr_tables():
        success = False

    # 2. Migra le competenze esistenti
    if not migrate_user_skills():
        success = False

    if success:
        print("\n🎉 Setup modulo HR completato con successo!")
        print("📋 Prossimi passi:")
        print("   - Subtask 7.2: Employee Profile Management")
        print("   - Subtask 7.3: Skills Management System")
        print("   - Subtask 7.4: Department Management Module")
    else:
        print("\n❌ Setup modulo HR fallito. Controlla gli errori sopra.")

    return success

if __name__ == "__main__":
    # Mostra le tabelle all'inizio
    show_tables()

    # Se viene passato un file come argomento, eseguilo
    if len(sys.argv) > 1:
        if sys.argv[1] == "add_start_date":
            add_start_date_to_task()
        elif sys.argv[1] == "setup_hr":
            setup_hr_module()
        elif sys.argv[1] == "setup_timesheet":
            setup_timesheet_module()
        elif sys.argv[1] == "create_hr_tables":
            create_hr_tables()
        elif sys.argv[1] == "create_timesheet_tables":
            create_timesheet_tables()
        elif sys.argv[1] == "extend_timesheet":
            extend_timesheet_table()
        elif sys.argv[1] == "cleanup_duplicates":
            cleanup_duplicate_tables()
        elif sys.argv[1] == "migrate_skills":
            migrate_user_skills()
        elif sys.argv[1] == "populate_orgchart":
            populate_orgchart_data()
        else:
            run_sql_file(sys.argv[1])

        # Mostra le tabelle dopo l'esecuzione
        show_tables()
    else:
        print("Uso: python db_update.py [comando | file_sql]")
        print("\nComandi disponibili:")
        print("  setup_timesheet     - Setup completo modulo Timesheet & CRM (Task 3.1 + 4)")
        print("  create_timesheet_tables - Crea solo le nuove tabelle Timesheet")
        print("  extend_timesheet    - Estende tabella timesheet esistente")
        print("  setup_hr            - Setup completo modulo HR (Task 7.1)")
        print("  create_hr_tables    - Crea solo le tabelle HR")
        print("  migrate_skills      - Migra competenze da user_skills a user_skills_detailed")
        print("  populate_orgchart   - Popola database con dati organigramma completo")
        print("  add_start_date      - Aggiunge campo start_date alla tabella task")
        print("\nSe non viene specificato un comando, verranno mostrate solo le tabelle esistenti.")