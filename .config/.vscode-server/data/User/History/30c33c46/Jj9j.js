import axios from 'axios'

const api = axios.create({
  baseURL: import.meta.env.DEV ? '' : '', // Forza l'uso del proxy in sviluppo
  timeout: 60000, // 60 secondi per analisi AI
  withCredentials: true, // Importante per le sessioni Flask
  headers: {
    'Content-Type': 'application/json',
  }
})

// Debug per verificare la configurazione
if (import.meta.env.DEV) {
  console.log('🔧 API in modalità sviluppo - usando proxy Vite')
  console.log('🔧 BaseURL:', api.defaults.baseURL)
}

// Request interceptor per aggiungere CSRF token
api.interceptors.request.use(
  (config) => {
    // Aggiungi CSRF token per richieste che ne hanno bisogno
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    if (csrfToken && ['post', 'put', 'patch', 'delete'].includes(config.method?.toLowerCase())) {
      config.headers['X-CSRFToken'] = csrfToken
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor per gestire errori globali
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Sessione scaduta, pulisci i dati locali e redirect al login
      localStorage.removeItem('user')
      // Non fare redirect automatico, lascia che sia il router a gestirlo
      console.warn('Sessione scaduta, autenticazione richiesta')
    }
    return Promise.reject(error)
  }
)

export default api