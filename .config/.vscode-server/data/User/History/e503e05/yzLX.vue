<template>
  <div class="py-6">
    <!-- Dashboard Header -->
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
          Benvenuto! Ecco una panoramica delle attività della tua azienda.
        </p>
      </div>
      <div class="mt-4 md:mt-0 flex space-x-3">
        <div class="relative">
          <select
            v-model="selectedPeriod"
            @change="refreshData"
            class="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="7">Ultimi 7 giorni</option>
            <option value="30">Ultimo mese</option>
            <option value="90">Ultimi 3 mesi</option>
          </select>
        </div>

        <button
          @click="refreshData"
          :disabled="isLoading"
          type="button"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" :class="{ 'animate-spin': isLoading }" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
          </svg>
          Aggiorna
        </button>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Active Projects -->
      <StatsCard
        title="Progetti Attivi"
        :value="stats.projects?.active || 0"
        :subtitle="`di ${stats.projects?.total || 0} totali`"
        icon="project"
        color="primary"
        :link="'/app/projects?status=active'"
      />

      <!-- Clients -->
      <StatsCard
        title="Clienti"
        :value="stats.team?.clients || 0"
        icon="users"
        color="secondary"
        :link="'/app/crm/clients'"
      />

      <!-- Pending Tasks -->
      <StatsCard
        title="Task Pendenti"
        :value="stats.tasks?.pending || 0"
        :subtitle="`${stats.tasks?.overdue || 0} in ritardo`"
        icon="clock"
        :color="stats.tasks?.overdue > 0 ? 'red' : 'yellow'"
        :link="'/app/tasks?status=pending'"
      />

      <!-- Team Members -->
      <StatsCard
        title="Team Members"
        :value="stats.team?.users || 0"
        :subtitle="`${stats.team?.departments || 0} dipartimenti`"
        icon="team"
        color="blue"
        :link="'/app/personnel'"
      />
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Project Status Chart -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">Stato Progetti</h2>
        </div>
        <div class="relative h-64">
          <canvas ref="projectChart"></canvas>
        </div>
      </div>

      <!-- Task Status Chart -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white">Stato Attività</h2>
        </div>
        <div class="relative h-64">
          <canvas ref="taskChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Activities Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Upcoming Tasks -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Attività in Scadenza</h2>
          <div v-if="upcomingTasks.length === 0" class="text-center py-8 text-gray-500">
            Nessuna attività in scadenza
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="task in upcomingTasks"
              :key="task.id"
              class="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ task.name }}</h3>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ task.project_name }}</p>
                </div>
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getPriorityClasses(task.priority)"
                >
                  {{ task.priority }}
                </span>
              </div>
              <div class="mt-2 flex justify-between items-center">
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  Scadenza: {{ formatDate(task.due_date) }}
                </span>
                <span
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  :class="getStatusClasses(task.status)"
                >
                  {{ task.status }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 dark:bg-gray-700 px-6 py-3">
          <div class="text-sm">
            <router-link to="/app/tasks" class="font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500">
              Vedi tutte le attività
            </router-link>
          </div>
        </div>
      </div>

      <!-- Recent Activities -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Attività Recenti</h2>
          <div v-if="recentActivities.length === 0" class="text-center py-8 text-gray-500">
            Nessuna attività recente
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="activity in recentActivities"
              :key="`${activity.type}-${activity.id}`"
              class="flex items-start space-x-3"
            >
              <div class="flex-shrink-0">
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center"
                  :class="getActivityIconClasses(activity.type)"
                >
                  <component :is="getActivityIcon(activity.type)" class="w-4 h-4" />
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ activity.title }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ activity.description }}</p>
                <p class="text-xs text-gray-400 dark:text-gray-500">{{ formatTimestamp(activity.timestamp) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- KPIs -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="p-6">
          <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">KPIs Principali</h2>
          <div v-if="kpis.length === 0" class="text-center py-8 text-gray-500">
            Nessun KPI configurato
          </div>
          <div v-else class="space-y-4">
            <div
              v-for="kpi in kpis"
              :key="kpi.id"
              class="border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <h3 class="text-sm font-medium text-gray-900 dark:text-white">{{ kpi.name }}</h3>
                  <p class="text-xs text-gray-500 dark:text-gray-400">{{ kpi.description }}</p>
                </div>
                <div class="text-right">
                  <p class="text-sm font-bold text-gray-900 dark:text-white">
                    {{ kpi.current_value }}{{ kpi.unit }}
                  </p>
                  <p class="text-xs text-gray-500">
                    Target: {{ kpi.target_value }}{{ kpi.unit }}
                  </p>
                </div>
              </div>
              <div class="mt-2">
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="h-2 rounded-full"
                    :class="getKpiProgressClasses(kpi.performance_percentage)"
                    :style="{ width: Math.min(kpi.performance_percentage, 100) + '%' }"
                  ></div>
                </div>
                <p class="text-xs text-gray-500 mt-1">{{ Math.round(kpi.performance_percentage) }}% del target</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import StatsCard from './components/StatsCard.vue'
import { Chart, registerables } from 'chart.js'

// Register Chart.js components
Chart.register(...registerables)

const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const isLoading = ref(false)
const selectedPeriod = ref('7')
const stats = ref({})
const upcomingTasks = ref([])
const recentActivities = ref([])
const kpis = ref([])

// Chart refs
const projectChart = ref(null)
const taskChart = ref(null)
let projectChartInstance = null
let taskChartInstance = null

// API functions
const fetchDashboardStats = async () => {
  try {
    const response = await fetch('/api/dashboard/stats')
    if (!response.ok) throw new Error('Failed to fetch stats')
    const data = await response.json()
    stats.value = data.data
  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    stats.value = {}
  }
}

const fetchUpcomingTasks = async () => {
  try {
    const response = await fetch(`/api/dashboard/upcoming-tasks?days=${selectedPeriod.value}&limit=5`)
    if (!response.ok) throw new Error('Failed to fetch upcoming tasks')
    const data = await response.json()
    upcomingTasks.value = data.data.tasks
  } catch (error) {
    console.error('Error fetching upcoming tasks:', error)
    upcomingTasks.value = []
  }
}

const fetchRecentActivities = async () => {
  try {
    const response = await fetch('/api/dashboard/recent-activities?limit=5')
    if (!response.ok) throw new Error('Failed to fetch recent activities')
    const data = await response.json()
    recentActivities.value = data.data.activities
  } catch (error) {
    console.error('Error fetching recent activities:', error)
    recentActivities.value = []
  }
}

const fetchKpis = async () => {
  try {
    const response = await fetch('/api/dashboard/kpis?limit=3')
    if (!response.ok) throw new Error('Failed to fetch KPIs')
    const data = await response.json()
    kpis.value = data.data.kpis
  } catch (error) {
    console.error('Error fetching KPIs:', error)
    kpis.value = []
  }
}

const fetchProjectChart = async () => {
  try {
    const response = await fetch('/api/dashboard/charts/project-status')
    if (!response.ok) throw new Error('Failed to fetch project chart data')
    const data = await response.json()
    updateProjectChart(data.data.chart)
  } catch (error) {
    console.error('Error fetching project chart:', error)
  }
}

const fetchTaskChart = async () => {
  try {
    const response = await fetch('/api/dashboard/charts/task-status')
    if (!response.ok) throw new Error('Failed to fetch task chart data')
    const data = await response.json()
    updateTaskChart(data.data.chart)
  } catch (error) {
    console.error('Error fetching task chart:', error)
  }
}

// Chart functions
const updateProjectChart = (chartData) => {
  if (!projectChart.value) return

  const ctx = projectChart.value.getContext('2d')

  if (projectChartInstance) {
    projectChartInstance.destroy()
  }

  projectChartInstance = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: chartData.labels,
      datasets: [{
        data: chartData.data,
        backgroundColor: [
          '#3B82F6', // Blue
          '#10B981', // Green
          '#F59E0B', // Yellow
          '#EF4444', // Red
          '#8B5CF6'  // Purple
        ],
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            padding: 20,
            usePointStyle: true
          }
        }
      }
    }
  })
}

const updateTaskChart = (chartData) => {
  if (!taskChart.value) return

  const ctx = taskChart.value.getContext('2d')

  if (taskChartInstance) {
    taskChartInstance.destroy()
  }

  taskChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: chartData.labels,
      datasets: [{
        label: 'Tasks',
        data: chartData.data,
        backgroundColor: [
          '#60A5FA', // Light blue
          '#34D399', // Light green
          '#FBBF24', // Light yellow
          '#F87171'  // Light red
        ],
        borderColor: [
          '#3B82F6',
          '#10B981',
          '#F59E0B',
          '#EF4444'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1
          }
        }
      }
    }
  })
}

// Utility functions
const refreshData = async () => {
  isLoading.value = true
  try {
    await Promise.all([
      fetchDashboardStats(),
      fetchUpcomingTasks(),
      fetchRecentActivities(),
      fetchKpis(),
      fetchProjectChart(),
      fetchTaskChart()
    ])
  } finally {
    isLoading.value = false
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('it-IT')
}

const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diffInMinutes = Math.floor((now - date) / (1000 * 60))

  if (diffInMinutes < 60) {
    return `${diffInMinutes} minuti fa`
  } else if (diffInMinutes < 1440) {
    return `${Math.floor(diffInMinutes / 60)} ore fa`
  } else {
    return `${Math.floor(diffInMinutes / 1440)} giorni fa`
  }
}

const getPriorityClasses = (priority) => {
  const classes = {
    high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[priority] || classes.medium
}

const getStatusClasses = (status) => {
  const classes = {
    'todo': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    'review': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    'done': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[status] || classes.todo
}

const getActivityIcon = (type) => {
  const icons = {
    task: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>`,
    timesheet: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`,
    event: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`
  }
  return icons[type] || icons.task
}

const getActivityIconClasses = (type) => {
  const classes = {
    task: 'bg-blue-100 text-blue-600',
    timesheet: 'bg-green-100 text-green-600',
    event: 'bg-purple-100 text-purple-600'
  }
  return classes[type] || classes.task
}

const getKpiProgressClasses = (percentage) => {
  if (percentage >= 90) return 'bg-green-500'
  if (percentage >= 70) return 'bg-yellow-500'
  return 'bg-red-500'
}

// Lifecycle
onMounted(async () => {
  await refreshData()

  // Initialize charts after DOM update
  await nextTick()
  if (projectChart.value && taskChart.value) {
    await fetchProjectChart()
    await fetchTaskChart()
  }
})
</script>