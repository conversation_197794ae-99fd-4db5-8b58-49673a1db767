{"openapi": "3.0.0", "info": {"title": "DatPortal API", "description": "API RESTful per DatPortal - Gestione Progetti, Task, Risorse e KPI", "version": "1.0.0", "contact": {"name": "<PERSON>t<PERSON><PERSON><PERSON>", "url": "https://datvinci.com"}}, "servers": [{"url": "/api", "description": "<PERSON> di s<PERSON><PERSON><PERSON>"}], "tags": [{"name": "projects", "description": "Operazioni sui progetti"}, {"name": "tasks", "description": "Operazioni sui task"}, {"name": "resources", "description": "Operazioni sulle risorse di progetto"}, {"name": "dependencies", "description": "Operazioni sulle dipendenze tra task"}, {"name": "kpis", "description": "Operazioni sui KPI generali"}, {"name": "project-kpis", "description": "Operazioni sui KPI di progetto"}, {"name": "personnel", "description": "Operazioni su personale, dipartimenti e competenze"}, {"name": "dashboard", "description": "Operazioni per dashboard, statistiche e attività recenti"}, {"name": "auth", "description": "Operazioni per autenticazione e gestione sessione utente"}, {"name": "timesheet-entries", "description": "Operazioni sui timesheet entries - registrazione ore lavorate"}, {"name": "monthly-timesheets", "description": "Operazioni sui timesheet mensili - approvazione e workflow"}, {"name": "time-off-requests", "description": "Operazioni sulle richieste di ferie, permessi e smartworking"}, {"name": "contracts", "description": "Operazioni sui contratti clienti per fatturazione"}, {"name": "invoices", "description": "Operazioni sulle fatture e fatturazione automatica"}], "paths": {"/projects/": {"get": {"tags": ["projects"], "summary": "<PERSON><PERSON>ene la lista dei progetti", "description": "Ottiene la lista dei progetti con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "status", "in": "query", "description": "Filtra per stato del progetto", "schema": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"]}}, {"name": "client_id", "in": "query", "description": "Filtra per ID cliente", "schema": {"type": "integer"}}, {"name": "search", "in": "query", "description": "Cerca nei nomi e nelle descrizioni dei progetti", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista di progetti", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"projects": {"type": "array", "items": {"$ref": "#/components/schemas/Project"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["projects"], "summary": "Crea un nuovo progetto", "description": "Crea un nuovo progetto con i dati forniti.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Nome del progetto"}, "description": {"type": "string", "description": "Descrizione del progetto"}, "client_id": {"type": "integer", "description": "ID del cliente associato"}, "start_date": {"type": "string", "format": "date", "description": "Data di inizio (YYYY-MM-DD)"}, "end_date": {"type": "string", "format": "date", "description": "Data di fine (YYYY-MM-DD)"}, "status": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"], "default": "planning", "description": "Stato del progetto"}, "budget": {"type": "number", "description": "Budget del progetto"}, "team_members": {"type": "array", "items": {"type": "integer"}, "description": "Lista di ID utenti da assegnare al progetto"}}}}}}, "responses": {"201": {"description": "<PERSON><PERSON><PERSON> creato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project": {"$ref": "#/components/schemas/Project"}}}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Nome del progetto obbligatorio"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/tasks/": {"get": {"tags": ["tasks"], "summary": "<PERSON><PERSON><PERSON> la lista dei task", "description": "Ottiene la lista dei task con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "project_id", "in": "query", "description": "Filtra per ID progetto", "schema": {"type": "integer"}}, {"name": "status", "in": "query", "description": "Filtra per stato del task", "schema": {"type": "string", "enum": ["todo", "in-progress", "review", "done"]}}, {"name": "priority", "in": "query", "description": "Filtra per priorità del task", "schema": {"type": "string", "enum": ["low", "medium", "high", "urgent"]}}, {"name": "assignee_id", "in": "query", "description": "Filtra per ID dell'assegnatario", "schema": {"type": "integer"}}, {"name": "search", "in": "query", "description": "Cerca nei nomi e nelle descrizioni dei task", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista di task", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/components/schemas/Task"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["tasks"], "summary": "Crea un nuovo task", "description": "Crea un nuovo task con i dati forniti.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "project_id"], "properties": {"name": {"type": "string", "description": "Nome del task"}, "description": {"type": "string", "description": "Descrizione del task"}, "project_id": {"type": "integer", "description": "ID del progetto associato"}, "assignee_id": {"type": "integer", "description": "ID dell'utente assegnato al task"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "default": "todo", "description": "Stato del task"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "default": "medium", "description": "Priorità del task"}, "due_date": {"type": "string", "format": "date", "description": "Data di scadenza (YYYY-MM-DD)"}}}}}}, "responses": {"201": {"description": "Task creato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"task": {"$ref": "#/components/schemas/Task"}}}, "message": {"type": "string", "example": "Task creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Nome del task e ID progetto obbligatori"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/resources/": {"get": {"tags": ["resources"], "summary": "Ottiene la lista delle risorse dei progetti", "description": "Ottiene la lista delle risorse dei progetti con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "project_id", "in": "query", "description": "Filtra per ID progetto", "schema": {"type": "integer"}}, {"name": "user_id", "in": "query", "description": "Filtra per ID utente", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Lista di risorse", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"resources": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectResource"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["resources"], "summary": "Assegna una nuova risorsa a un progetto", "description": "Assegna una nuova risorsa a un progetto.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["project_id", "user_id"], "properties": {"project_id": {"type": "integer", "description": "ID del progetto"}, "user_id": {"type": "integer", "description": "ID dell'utente da assegnare"}, "allocation_percentage": {"type": "integer", "description": "Percentuale di allocazione (1-100)", "default": 100}, "role": {"type": "string", "description": "Ruolo dell'utente nel progetto"}}}}}}, "responses": {"201": {"description": "Risorsa assegnata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"resource": {"$ref": "#/components/schemas/ProjectResource"}}}, "message": {"type": "string", "example": "Risorsa assegnata con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "ID progetto e ID utente obbligatori"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/task-dependencies/": {"get": {"tags": ["task-dependencies"], "summary": "O<PERSON>ene la lista delle dipendenze tra task", "description": "Ottiene la lista delle dipendenze tra task con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "task_id", "in": "query", "description": "Filtra per ID del task", "schema": {"type": "integer"}}, {"name": "depends_on_id", "in": "query", "description": "Filtra per ID del task dipendente", "schema": {"type": "integer"}}, {"name": "project_id", "in": "query", "description": "Filtra per ID del progetto", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Lista di dipendenze tra task", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"dependencies": {"type": "array", "items": {"$ref": "#/components/schemas/TaskDependency"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["task-dependencies"], "summary": "Crea una nuova dipendenza tra task", "description": "Crea una nuova dipendenza tra task.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["task_id", "depends_on_id"], "properties": {"task_id": {"type": "integer", "description": "ID del task che dipende"}, "depends_on_id": {"type": "integer", "description": "ID del task da cui dipende"}}}}}}, "responses": {"201": {"description": "Dipendenza creata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"dependency": {"$ref": "#/components/schemas/TaskDependency"}}}, "message": {"type": "string", "example": "Dipendenza creata con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "ID task e ID task dipendente obbligatori"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/kpis/": {"get": {"tags": ["kpis"], "summary": "Ottiene la lista dei KPI", "description": "Ottiene la lista dei KPI con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "category", "in": "query", "description": "Filtra per categoria KPI", "schema": {"type": "string"}}, {"name": "frequency", "in": "query", "description": "Filtra per frequenza KPI", "schema": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "annually"]}}, {"name": "search", "in": "query", "description": "Cerca nei nomi e descrizioni dei KPI", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista di KPI", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpis": {"type": "array", "items": {"$ref": "#/components/schemas/KPI"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["kpis"], "summary": "Crea un nuovo KPI", "description": "Crea un nuovo KPI con i dati forniti.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "description": "Nome del KPI"}, "description": {"type": "string", "description": "Descrizione del KPI"}, "category": {"type": "string", "description": "Categoria del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI", "default": 0}, "unit": {"type": "string", "description": "Unità di misura"}, "frequency": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "annually"], "description": "Frequenza di misurazione"}}}}}}, "responses": {"201": {"description": "KPI creato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpi": {"$ref": "#/components/schemas/KPI"}}}, "message": {"type": "string", "example": "KPI creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Nome del KPI obbligatorio"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/kpis/{kpi_id}": {"get": {"tags": ["kpis"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un KPI", "description": "<PERSON><PERSON><PERSON> i dettagli di un KPI specifico.", "parameters": [{"name": "kpi_id", "in": "path", "required": true, "description": "ID del KPI", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli del KPI", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpi": {"$ref": "#/components/schemas/KPI"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["kpis"], "summary": "Aggiorna un KPI esistente", "description": "Aggiorna un KPI esistente.", "parameters": [{"name": "kpi_id", "in": "path", "required": true, "description": "ID del KPI da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Nome del KPI"}, "description": {"type": "string", "description": "Descrizione del KPI"}, "category": {"type": "string", "description": "Categoria del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI"}, "unit": {"type": "string", "description": "Unità di misura"}, "frequency": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "annually"], "description": "Frequenza di misurazione"}}}}}}, "responses": {"200": {"description": "KPI aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpi": {"$ref": "#/components/schemas/KPI"}}}, "message": {"type": "string", "example": "KPI aggiornato con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["kpis"], "summary": "Elimina un KPI", "description": "Elimina un KPI.", "parameters": [{"name": "kpi_id", "in": "path", "required": true, "description": "ID del KPI da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "KPI eliminato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "KPI eliminato con successo"}}}}}}, "400": {"description": "KPI in uso, impossibile eliminare", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Impossibile eliminare KPI: è utilizzato in progetti"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/project-kpis/": {"get": {"tags": ["project-kpis"], "summary": "Ottiene la lista dei KPI di progetto", "description": "Ottiene la lista dei KPI di progetto con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "project_id", "in": "query", "description": "Filtra per ID progetto", "schema": {"type": "integer"}}, {"name": "kpi_id", "in": "query", "description": "Filtra per ID KPI", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Lista di KPI di progetto", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project_kpis": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectKPI"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["project-kpis"], "summary": "Crea un nuovo KPI di progetto", "description": "Crea un nuovo KPI di progetto.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["project_id", "kpi_id"], "properties": {"project_id": {"type": "integer", "description": "ID del progetto"}, "kpi_id": {"type": "integer", "description": "ID del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI", "default": 0}}}}}}, "responses": {"201": {"description": "KPI di progetto creato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project_kpi": {"$ref": "#/components/schemas/ProjectKPI"}}}, "message": {"type": "string", "example": "KPI di progetto creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "ID progetto e ID KPI obbligatori"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/project-kpis/{project_kpi_id}": {"get": {"tags": ["project-kpis"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un KPI di progetto", "description": "<PERSON><PERSON><PERSON> i dettagli di un KPI di progetto specifico.", "parameters": [{"name": "project_kpi_id", "in": "path", "required": true, "description": "ID del KPI di progetto", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli del KPI di progetto", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project_kpi": {"$ref": "#/components/schemas/ProjectKPI"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["project-kpis"], "summary": "Aggiorna un KPI di progetto esistente", "description": "Aggiorna un KPI di progetto esistente.", "parameters": [{"name": "project_kpi_id", "in": "path", "required": true, "description": "ID del KPI di progetto da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI"}}}}}}, "responses": {"200": {"description": "KPI di progetto aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project_kpi": {"$ref": "#/components/schemas/ProjectKPI"}}}, "message": {"type": "string", "example": "KPI di progetto aggiornato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["project-kpis"], "summary": "Elimina un KPI di progetto", "description": "Elimina un KPI di progetto.", "parameters": [{"name": "project_kpi_id", "in": "path", "required": true, "description": "ID del KPI di progetto da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "KPI di progetto eliminato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "KPI di progetto eliminato con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/task-dependencies/{dependency_id}": {"get": {"tags": ["task-dependencies"], "summary": "<PERSON><PERSON><PERSON> i dettagli di una dipendenza", "description": "<PERSON><PERSON><PERSON> i dettagli di una dipendenza specifica.", "parameters": [{"name": "dependency_id", "in": "path", "required": true, "description": "ID della dipendenza", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli della dipendenza", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"dependency": {"$ref": "#/components/schemas/TaskDependency"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["task-dependencies"], "summary": "Elimina una dipendenza", "description": "Elimina una dipendenza tra task.", "parameters": [{"name": "dependency_id", "in": "path", "required": true, "description": "ID della dipendenza da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dipendenza eliminata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Dipendenza eliminata con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/resources/{resource_id}": {"get": {"tags": ["resources"], "summary": "<PERSON><PERSON><PERSON> i dettagli di una risorsa", "description": "<PERSON><PERSON><PERSON> i dettagli di una risorsa specifica.", "parameters": [{"name": "resource_id", "in": "path", "required": true, "description": "ID della risorsa", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli della risorsa", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"resource": {"$ref": "#/components/schemas/ProjectResource"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["resources"], "summary": "Aggiorna un'assegnazione di risorsa esistente", "description": "Aggiorna un'assegnazione di risorsa esistente.", "parameters": [{"name": "resource_id", "in": "path", "required": true, "description": "ID della risorsa da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"allocation_percentage": {"type": "integer", "description": "Percentuale di allocazione (1-100)"}, "role": {"type": "string", "description": "Ruolo dell'utente nel progetto"}}}}}}, "responses": {"200": {"description": "Risorsa aggiornata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"resource": {"$ref": "#/components/schemas/ProjectResource"}}}, "message": {"type": "string", "example": "Risorsa aggiornata con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["resources"], "summary": "Rimuove un'assegnazione di risorsa", "description": "Rimuove un'assegnazione di risorsa.", "parameters": [{"name": "resource_id", "in": "path", "required": true, "description": "ID della risorsa da rimuovere", "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON><PERSON><PERSON><PERSON> rimossa con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> rimossa con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/tasks/status": {"patch": {"tags": ["tasks"], "summary": "Aggiorna lo stato di più task contemporaneamente", "description": "Aggiorna lo stato di più task contemporaneamente.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tasks"], "properties": {"tasks": {"type": "array", "items": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "integer", "description": "ID del task"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "description": "Nuovo stato del task"}}}}}}}}}, "responses": {"200": {"description": "Stato dei task aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"task_id": {"type": "integer", "example": 1}, "success": {"type": "boolean", "example": true}, "old_status": {"type": "string", "example": "todo"}, "new_status": {"type": "string", "example": "in-progress"}, "message": {"type": "string", "example": "Stato aggiornato da 'todo' a 'in-progress'"}}}}, "message": {"type": "string", "example": "Aggiornamento completato: 3 task aggiornati, 0 falliti"}}}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi: è richiesta una lista di task"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/tasks/{task_id}": {"get": {"tags": ["tasks"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un task", "description": "<PERSON><PERSON><PERSON> i dettagli di un task specifico.", "parameters": [{"name": "task_id", "in": "path", "required": true, "description": "ID del task", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli del task", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"task": {"$ref": "#/components/schemas/Task"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["tasks"], "summary": "Aggiorna un task esistente", "description": "Aggiorna un task esistente con i dati forniti.", "parameters": [{"name": "task_id", "in": "path", "required": true, "description": "ID del task da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Nome del task"}, "description": {"type": "string", "description": "Descrizione del task"}, "project_id": {"type": "integer", "description": "ID del progetto associato"}, "assignee_id": {"type": "integer", "description": "ID dell'utente assegnato al task"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "description": "Stato del task"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "description": "Priorità del task"}, "due_date": {"type": "string", "format": "date", "description": "Data di scadenza (YYYY-MM-DD)"}}}}}}, "responses": {"200": {"description": "Task aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"task": {"$ref": "#/components/schemas/Task"}}}, "message": {"type": "string", "example": "Task aggiornato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["tasks"], "summary": "Elimina un task esistente", "description": "Elimina un task esistente.", "parameters": [{"name": "task_id", "in": "path", "required": true, "description": "ID del task da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Task eliminato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Task eliminato con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/projects/batch": {"post": {"tags": ["projects"], "summary": "Esegue operazioni batch sui progetti", "description": "Esegue operazioni batch sui progetti.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["operations"], "properties": {"operations": {"type": "array", "items": {"type": "object", "required": ["operation"], "properties": {"operation": {"type": "string", "enum": ["create", "update", "delete"], "description": "Tipo di operazione da eseguire"}, "project_id": {"type": "integer", "description": "ID del progetto (richiesto per update e delete)"}, "data": {"type": "object", "description": "<PERSON>ti del progetto (richiesto per create e update)"}}}}}}}}}, "responses": {"200": {"description": "Operazioni batch eseguite con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"results": {"type": "array", "items": {"type": "object", "properties": {"operation": {"type": "string", "example": "create"}, "success": {"type": "boolean", "example": true}, "project_id": {"type": "integer", "example": 1}, "message": {"type": "string", "example": "<PERSON><PERSON><PERSON> creato con successo"}}}}, "message": {"type": "string", "example": "Operazioni batch eseguite con successo"}}}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/projects/{project_id}": {"get": {"tags": ["projects"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un progetto", "description": "<PERSON><PERSON><PERSON> i dettagli di un progetto specifico.", "parameters": [{"name": "project_id", "in": "path", "required": true, "description": "ID del progetto", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli del progetto", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project": {"$ref": "#/components/schemas/Project"}}}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["projects"], "summary": "Aggiorna un progetto esistente", "description": "Aggiorna un progetto esistente con i dati forniti.", "parameters": [{"name": "project_id", "in": "path", "required": true, "description": "ID del progetto da aggiornare", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string", "description": "Nome del progetto"}, "description": {"type": "string", "description": "Descrizione del progetto"}, "client_id": {"type": "integer", "description": "ID del cliente associato"}, "start_date": {"type": "string", "format": "date", "description": "Data di inizio (YYYY-MM-DD)"}, "end_date": {"type": "string", "format": "date", "description": "Data di fine (YYYY-MM-DD)"}, "status": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"], "description": "Stato del progetto"}, "budget": {"type": "number", "description": "Budget del progetto"}, "team_members": {"type": "array", "items": {"type": "integer"}, "description": "Lista di ID utenti da assegnare al progetto"}}}}}}, "responses": {"200": {"description": "Progetto aggiornato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"project": {"$ref": "#/components/schemas/Project"}}}, "message": {"type": "string", "example": "Progetto aggiornato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Dati non validi"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["projects"], "summary": "Elimina un progetto esistente", "description": "Elimina un progetto esistente.", "parameters": [{"name": "project_id", "in": "path", "required": true, "description": "ID del progetto da eliminare", "schema": {"type": "integer"}}], "responses": {"200": {"description": "<PERSON><PERSON>to eliminato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON>to eliminato con successo"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/personnel/users": {"get": {"tags": ["personnel"], "summary": "<PERSON><PERSON><PERSON> la lista degli utenti", "description": "Ottiene la lista degli utenti con supporto per filtri, paginazione e ricerca.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "search", "in": "query", "description": "Cerca in nome, cognome, username, email", "schema": {"type": "string"}}, {"name": "department_id", "in": "query", "description": "Filtra per ID dipartimento", "schema": {"type": "integer"}}, {"name": "role", "in": "query", "description": "Filtra per ruolo utente", "schema": {"type": "string", "enum": ["admin", "manager", "employee", "human_resources"]}}, {"name": "is_active", "in": "query", "description": "Filtra per stato attivo", "schema": {"type": "boolean"}}, {"name": "skills", "in": "query", "description": "Filtra per competenze (ID separati da virgola)", "schema": {"type": "string"}}, {"name": "order_by", "in": "query", "description": "Campo per ordinamento", "schema": {"type": "string", "default": "last_name"}}, {"name": "order_dir", "in": "query", "description": "Direzione ordinamento", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "asc"}}], "responses": {"200": {"description": "Lista utenti", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/UserSummary"}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}, "message": {"type": "string", "example": "Retrieved 25 users"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/personnel/users/{user_id}": {"get": {"tags": ["personnel"], "summary": "<PERSON><PERSON><PERSON> i dettagli di un utente", "description": "Ottiene informazioni dettagliate su un utente specifico.", "parameters": [{"name": "user_id", "in": "path", "required": true, "description": "ID dell'utente", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettagli utente", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/UserDetail"}}}, "message": {"type": "string", "example": "Retrieved user <PERSON>"}}}}}}, "404": {"$ref": "#/components/responses/NotFound"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/personnel/departments": {"get": {"tags": ["personnel"], "summary": "Ottiene la lista dei dipartimenti", "description": "O<PERSON>ene la lista dei dipartimenti con dati per l'organigramma.", "responses": {"200": {"description": "Lista dipartimenti", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"departments": {"type": "array", "items": {"$ref": "#/components/schemas/Department"}}}}, "message": {"type": "string", "example": "Retrieved 5 departments"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/personnel/skills": {"get": {"tags": ["personnel"], "summary": "O<PERSON>ene la lista delle competenze", "description": "Ottiene la lista delle competenze con statistiche di utilizzo.", "parameters": [{"name": "category", "in": "query", "description": "Filtra per categoria competenza", "schema": {"type": "string"}}, {"name": "search", "in": "query", "description": "Cerca in nome e descrizione competenze", "schema": {"type": "string"}}], "responses": {"200": {"description": "Lista competenze", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"skills": {"type": "array", "items": {"$ref": "#/components/schemas/Skill"}}, "categories": {"type": "array", "items": {"type": "string"}, "description": "Lista delle categorie disponibili"}}}, "message": {"type": "string", "example": "Retrieved 42 skills"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/dashboard/stats": {"get": {"tags": ["dashboard"], "summary": "<PERSON><PERSON><PERSON> le statistiche della dashboard", "description": "Ottiene statistiche aggregate per la dashboard principale.", "responses": {"200": {"description": "Statistiche dashboard", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"projects": {"type": "object", "properties": {"active": {"type": "integer"}, "total": {"type": "integer"}}}, "tasks": {"type": "object", "properties": {"total": {"type": "integer"}, "pending": {"type": "integer"}, "completed": {"type": "integer"}, "overdue": {"type": "integer"}}}, "team": {"type": "object", "properties": {"users": {"type": "integer"}, "departments": {"type": "integer"}, "clients": {"type": "integer"}}}, "activities": {"type": "object", "properties": {"recent_timesheets": {"type": "integer"}, "unread_notifications": {"type": "integer"}}}}}, "message": {"type": "string", "example": "Dashboard statistics retrieved successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/dashboard/recent-activities": {"get": {"tags": ["dashboard"], "summary": "<PERSON><PERSON><PERSON> le attività recenti", "description": "Ottiene la lista delle attività recenti per la dashboard.", "parameters": [{"name": "limit", "in": "query", "description": "Numero massimo di attività da restituire", "schema": {"type": "integer", "default": 10, "maximum": 50}}], "responses": {"200": {"description": "Lista attività recenti", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"activities": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardActivity"}}}}, "message": {"type": "string", "example": "Retrieved 10 recent activities"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/dashboard/upcoming-tasks": {"get": {"tags": ["dashboard"], "summary": "<PERSON><PERSON><PERSON> i task in scadenza", "description": "<PERSON><PERSON><PERSON> la lista dei task in scadenza per la dashboard.", "parameters": [{"name": "days", "in": "query", "description": "Numero di giorni da considerare per le scadenze", "schema": {"type": "integer", "default": 7}}, {"name": "limit", "in": "query", "description": "Numero massimo di task da restituire", "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "Lista task in scadenza", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"tasks": {"type": "array", "items": {"$ref": "#/components/schemas/UpcomingTask"}}}}, "message": {"type": "string", "example": "Retrieved 5 upcoming tasks"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/dashboard/kpis": {"get": {"tags": ["dashboard"], "summary": "Ottiene i KPI per la dashboard", "description": "<PERSON>ttiene i KPI principali per la dashboard.", "parameters": [{"name": "category", "in": "query", "description": "Filtra per categoria KPI", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "Numero massimo di KPI da restituire", "schema": {"type": "integer", "default": 6, "maximum": 20}}], "responses": {"200": {"description": "Lista KPI dashboard", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"kpis": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardKPI"}}}}, "message": {"type": "string", "example": "Retrieved 6 KPIs"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/auth/me": {"get": {"tags": ["auth"], "summary": "Ottiene i dati dell'utente corrente", "description": "Ottiene informazioni complete dell'utente corrente inclusi permessi e preferenze.", "responses": {"200": {"description": "Dati utente corrente", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"user": {"$ref": "#/components/schemas/CurrentUser"}}}, "message": {"type": "string", "example": "Current user data retrieved successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/auth/check-session": {"get": {"tags": ["auth"], "summary": "Verifica validità sessione", "description": "Verifica se la sessione corrente è valida.", "responses": {"200": {"description": "Stato sessione", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"valid": {"type": "boolean", "example": true}, "user_id": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "mario.rossi"}, "role": {"type": "string", "example": "admin"}, "last_activity": {"type": "string", "format": "date-time"}}}, "message": {"type": "string", "example": "Session is valid"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/auth/preferences": {"get": {"tags": ["auth"], "summary": "<PERSON><PERSON><PERSON> preferenze utente", "description": "<PERSON><PERSON><PERSON> le preferenze dell'utente corrente.", "responses": {"200": {"description": "Preferenze utente", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"preferences": {"$ref": "#/components/schemas/UserPreferences"}}}, "message": {"type": "string", "example": "User preferences retrieved successfully"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}, "put": {"tags": ["auth"], "summary": "Aggiorna preferenze utente", "description": "Aggiorna le preferenze dell'utente corrente.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"dark_mode": {"type": "boolean", "description": "Modalità scura"}}}}}}, "responses": {"200": {"description": "Preferenze aggiornate", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"preferences": {"$ref": "#/components/schemas/UserPreferences"}}}, "message": {"type": "string", "example": "User preferences updated successfully"}}}}}}, "400": {"description": "Dati non validi"}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/time-off-requests/": {"get": {"tags": ["time-off-requests"], "summary": "O<PERSON>ene la lista delle richieste time-off", "description": "Ottiene la lista delle richieste di ferie, permessi e smartworking con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "user_id", "in": "query", "description": "Filtra per ID utente", "schema": {"type": "integer"}}, {"name": "type", "in": "query", "description": "Filtra per tipo richiesta", "schema": {"type": "string", "enum": ["vacation", "leave", "smartworking"]}}, {"name": "status", "in": "query", "description": "Filtra per stato richiesta", "schema": {"type": "string", "enum": ["pending", "approved", "rejected"]}}, {"name": "start_date", "in": "query", "description": "Filtra per data inizio (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "description": "Filtra per data fine (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}, {"name": "year", "in": "query", "description": "Filtra per anno", "schema": {"type": "integer"}}, {"name": "month", "in": "query", "description": "Filtra per mese (1-12)", "schema": {"type": "integer", "minimum": 1, "maximum": 12}}], "responses": {"200": {"description": "Lista di richieste time-off", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"requests": {"type": "array", "items": {"$ref": "#/components/schemas/TimeOffRequest"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}, "post": {"tags": ["time-off-requests"], "summary": "<PERSON>rea una nuova richiesta time-off", "description": "Crea una nuova richiesta di ferie, permessi o smartworking.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["request_type", "start_date", "end_date"], "properties": {"request_type": {"type": "string", "enum": ["vacation", "leave", "smartworking"], "description": "Tipo di richiesta"}, "start_date": {"type": "string", "format": "date", "description": "Data di inizio (YYYY-MM-DD)"}, "end_date": {"type": "string", "format": "date", "description": "Data di fine (YYYY-MM-DD)"}, "notes": {"type": "string", "description": "Note aggiuntive"}}}}}}, "responses": {"201": {"description": "<PERSON><PERSON> time-off creata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/TimeOffRequest"}, "message": {"type": "string", "example": "<PERSON><PERSON> time-off creata con successo"}}}}}}, "400": {"description": "Dati non validi o sovrapposizione con richieste esistenti", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}}, "security": [{"cookieAuth": []}]}}, "/time-off-requests/{id}": {"get": {"tags": ["time-off-requests"], "summary": "<PERSON><PERSON><PERSON> richiesta time-off", "description": "<PERSON><PERSON><PERSON> i dettagli di una specifica richiesta time-off.", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID della richiesta", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettaglio richiesta time-off", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/TimeOffRequest"}, "message": {"type": "string", "example": "Dettaglio richiesta recuperato con successo"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": []}]}, "delete": {"tags": ["time-off-requests"], "summary": "Elimina richiesta time-off", "description": "Elimina una richiesta time-off (solo se pending e propria).", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID della richiesta", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Richiesta eliminata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "<PERSON><PERSON> time-off eliminata con successo"}}}}}}, "400": {"description": "Richiesta non eliminabile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": []}]}}, "/time-off-requests/{id}/approve": {"put": {"tags": ["time-off-requests"], "summary": "A<PERSON><PERSON><PERSON> richiesta time-off", "description": "Approva una richiesta time-off (solo manager/HR).", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID della richiesta", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Richiesta approvata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string", "example": "approved"}, "approved_by": {"type": "integer"}, "approval_date": {"type": "string", "format": "date-time"}}}, "message": {"type": "string", "example": "Richiesta vacation approvata con successo"}}}}}}, "400": {"description": "Richiesta non approvabile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": [], "approveTimeOff": ["approve_time_off"]}]}}, "/time-off-requests/{id}/reject": {"put": {"tags": ["time-off-requests"], "summary": "<PERSON><PERSON><PERSON><PERSON> richiesta time-off", "description": "Rif<PERSON><PERSON> una richiesta time-off con motivo (solo manager/HR).", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID della richiesta", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["reason"], "properties": {"reason": {"type": "string", "description": "Motivo del rifiuto"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON> rifiutata", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string", "example": "rejected"}, "approved_by": {"type": "integer"}, "approval_date": {"type": "string", "format": "date-time"}, "rejection_reason": {"type": "string"}}}, "message": {"type": "string", "example": "Richiesta vacation rifiutata"}}}}}}, "400": {"description": "Dati non validi o richiesta non rifiutabile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": [], "approveTimeOff": ["approve_time_off"]}]}}, "/monthly-timesheets/": {"get": {"tags": ["monthly-timesheets"], "summary": "O<PERSON><PERSON> la lista dei timesheet mensili", "description": "Ottiene la lista dei timesheet mensili con supporto per filtri e paginazione.", "parameters": [{"$ref": "#/components/parameters/pageParam"}, {"$ref": "#/components/parameters/perPageParam"}, {"name": "user_id", "in": "query", "description": "Filtra per ID utente", "schema": {"type": "integer"}}, {"name": "year", "in": "query", "description": "Filtra per anno", "schema": {"type": "integer"}}, {"name": "month", "in": "query", "description": "Filtra per mese (1-12)", "schema": {"type": "integer", "minimum": 1, "maximum": 12}}, {"name": "status", "in": "query", "description": "Filtra per stato", "schema": {"type": "string", "enum": ["draft", "submitted", "approved", "rejected"]}}], "responses": {"200": {"description": "Lista di timesheet mensili", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"timesheets": {"type": "array", "items": {"$ref": "#/components/schemas/MonthlyTimesheet"}}}}, "pagination": {"$ref": "#/components/schemas/Pagination"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/monthly-timesheets/{id}": {"get": {"tags": ["monthly-timesheets"], "summary": "<PERSON><PERSON><PERSON> de<PERSON>o timesheet mensile", "description": "<PERSON><PERSON><PERSON> i dettagli di un timesheet mensile con le entries associate.", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID del timesheet mensile", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Dettaglio timesheet mensile", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"$ref": "#/components/schemas/MonthlyTimesheet"}, "message": {"type": "string", "example": "Dettaglio monthly timesheet recuperato con successo"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": []}]}}, "/monthly-timesheets/generate": {"post": {"tags": ["monthly-timesheets"], "summary": "Genera timesheet mensile", "description": "Genera o recupera un timesheet mensile per un mese specifico.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["year", "month"], "properties": {"year": {"type": "integer", "description": "Anno di riferimento"}, "month": {"type": "integer", "minimum": 1, "maximum": 12, "description": "Mese di riferimento (1-12)"}, "user_id": {"type": "integer", "description": "ID utente (solo per manager)"}}}}}}, "responses": {"201": {"description": "Timesheet mensile generato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "year": {"type": "integer"}, "month": {"type": "integer"}, "status": {"type": "string", "example": "draft"}, "total_hours": {"type": "number"}, "billable_hours": {"type": "number"}, "entries_count": {"type": "integer"}}}, "message": {"type": "string", "example": "Monthly timesheet generato con successo"}}}}}}, "400": {"description": "Dati non validi o nessuna entry trovata", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/monthly-timesheets/{id}/submit": {"put": {"tags": ["monthly-timesheets"], "summary": "Sottometti timesheet mensile", "description": "So<PERSON><PERSON>i un timesheet mensile per approvazione (solo proprietario).", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID del timesheet mensile", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Timesheet sottomesso con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string", "example": "submitted"}, "submission_date": {"type": "string", "format": "date-time"}, "total_hours": {"type": "number"}}}, "message": {"type": "string", "example": "Timesheet sottomesso per approvazione"}}}}}}, "400": {"description": "Timesheet non sottomettibile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": []}]}}, "/monthly-timesheets/{id}/approve": {"put": {"tags": ["monthly-timesheets"], "summary": "<PERSON><PERSON><PERSON>a timesheet mensile", "description": "<PERSON><PERSON><PERSON><PERSON> un timesheet mensile (solo manager).", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID del timesheet mensile", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Timesheet approvato con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string", "example": "approved"}, "approved_by": {"type": "integer"}, "approval_date": {"type": "string", "format": "date-time"}, "total_hours": {"type": "number"}}}, "message": {"type": "string", "example": "Timesheet 12/2024 approvato con successo"}}}}}}, "400": {"description": "Timesheet non approvabile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": []}]}}, "/monthly-timesheets/{id}/reject": {"put": {"tags": ["monthly-timesheets"], "summary": "Rifiuta timesheet mensile", "description": "R<PERSON><PERSON><PERSON> un timesheet mensile con motivo (solo manager).", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID del timesheet mensile", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["reason"], "properties": {"reason": {"type": "string", "description": "Motivo del rifiuto"}}}}}}, "responses": {"200": {"description": "Timesheet rifiutato", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string", "example": "rejected"}, "approved_by": {"type": "integer"}, "approval_date": {"type": "string", "format": "date-time"}, "rejection_reason": {"type": "string"}}}, "message": {"type": "string", "example": "Timesheet 12/2024 rifiutato"}}}}}}, "400": {"description": "Dati non validi o timesheet non rifiutabile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": []}]}}, "/monthly-timesheets/{id}/reopen": {"put": {"tags": ["monthly-timesheets"], "summary": "Riapre timesheet mensile", "description": "Ria<PERSON>re un timesheet mensile rifiutato per modifiche (solo proprietario).", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID del timesheet mensile", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Timesheet riaperto con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}, "status": {"type": "string", "example": "draft"}}}, "message": {"type": "string", "example": "Timesheet riaperto per modifiche"}}}}}}, "400": {"description": "Timesheet non riapribile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": []}]}}, "/timesheets/": {"get": {"tags": ["timesheet-entries"], "summary": "Ottiene la lista delle timesheet entries", "description": "Ottiene la lista delle timesheet entries con supporto per filtri e paginazione.", "parameters": [{"name": "start_date", "in": "query", "description": "Filtra per data inizio (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}, {"name": "end_date", "in": "query", "description": "Filtra per data fine (YYYY-MM-DD)", "schema": {"type": "string", "format": "date"}}, {"name": "user_id", "in": "query", "description": "Filtra per ID utente", "schema": {"type": "integer"}}, {"name": "project_id", "in": "query", "description": "Filtra per ID progetto", "schema": {"type": "integer"}}, {"name": "task_id", "in": "query", "description": "Filtra per ID task", "schema": {"type": "integer"}}, {"name": "limit", "in": "query", "description": "Numero massimo di risultati", "schema": {"type": "integer", "default": 50, "maximum": 100}}, {"name": "offset", "in": "query", "description": "Offset per paginazione", "schema": {"type": "integer", "default": 0}}], "responses": {"200": {"description": "Lista di timesheet entries", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TimesheetEntry"}}, "message": {"type": "string", "example": "Re<PERSON><PERSON><PERSON> 25 timesheet"}, "meta": {"type": "object", "properties": {"total": {"type": "integer"}, "limit": {"type": "integer"}, "offset": {"type": "integer"}, "has_more": {"type": "boolean"}}}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": [], "viewAllTimesheets": ["view_all_timesheets"]}]}, "post": {"tags": ["timesheet-entries"], "summary": "Crea una nuova timesheet entry", "description": "Crea una nuova entry per registrare ore lavorate su un progetto/task.", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["project_id", "date", "hours"], "properties": {"project_id": {"type": "integer", "description": "ID del progetto"}, "task_id": {"type": "integer", "nullable": true, "description": "ID del task (opzionale)"}, "date": {"type": "string", "format": "date", "description": "Data dell'entry (YYYY-MM-DD)"}, "hours": {"type": "number", "minimum": 0.1, "maximum": 24, "description": "Ore lavorate"}, "description": {"type": "string", "description": "Descrizione del lavoro svolto"}, "user_id": {"type": "integer", "description": "ID utente (solo per manager)"}, "billable": {"type": "boolean", "description": "Se l'entry è fatturabile"}, "billing_rate": {"type": "number", "description": "Tariffa oraria specifica"}}}}}}, "responses": {"201": {"description": "Timesheet entry creata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object", "properties": {"id": {"type": "integer"}}}, "message": {"type": "string", "example": "Timesheet creato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}}, "security": [{"cookieAuth": []}]}}, "/timesheets/{id}": {"put": {"tags": ["timesheet-entries"], "summary": "Aggiorna timesheet entry", "description": "Aggiorna una timesheet entry esistente.", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID della timesheet entry", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"date": {"type": "string", "format": "date", "description": "Data dell'entry (YYYY-MM-DD)"}, "hours": {"type": "number", "minimum": 0.1, "maximum": 24, "description": "Ore lavorate"}, "description": {"type": "string", "description": "Descrizione del lavoro svolto"}, "task_id": {"type": "integer", "nullable": true, "description": "ID del task"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected"], "description": "Stato dell'entry"}, "billable": {"type": "boolean", "description": "Se l'entry è fatturabile"}, "billing_rate": {"type": "number", "description": "Tariffa oraria specifica"}}}}}}, "responses": {"200": {"description": "Timesheet entry aggiornata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object"}, "message": {"type": "string", "example": "Timesheet aggiornato con successo"}}}}}}, "400": {"description": "Dati non validi", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": [], "manageTimesheets": ["manage_timesheets"]}]}, "delete": {"tags": ["timesheet-entries"], "summary": "Elimina timesheet entry", "description": "Elimina una timesheet entry esistente.", "parameters": [{"name": "id", "in": "path", "required": true, "description": "ID della timesheet entry", "schema": {"type": "integer"}}], "responses": {"200": {"description": "Timesheet entry eliminata con successo", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "object"}, "message": {"type": "string", "example": "Timesheet eliminato con successo"}}}}}}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "404": {"$ref": "#/components/responses/NotFound"}}, "security": [{"cookieAuth": [], "manageTimesheets": ["manage_timesheets"]}]}}}, "components": {"responses": {"Unauthorized": {"description": "Autenticazione richiesta", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"success": false, "message": "Autenticazione richiesta"}}}}, "Forbidden": {"description": "<PERSON><PERSON><PERSON> negato", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"success": false, "message": "Non hai i permessi necessari per accedere a questa risorsa"}}}}, "NotFound": {"description": "Risorsa non trovata", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}, "example": {"success": false, "message": "Risorsa non trovata"}}}}}, "schemas": {"Project": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "ID univoco del progetto"}, "name": {"type": "string", "description": "Nome del progetto"}, "description": {"type": "string", "description": "Descrizione del progetto"}, "client_id": {"type": "integer", "format": "int64", "nullable": true, "description": "ID del cliente associato al progetto"}, "start_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di inizio del progetto (YYYY-MM-DD)"}, "end_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di fine del progetto (YYYY-MM-DD)"}, "status": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"], "description": "Stato del progetto"}, "budget": {"type": "number", "format": "float", "nullable": true, "description": "Budget del progetto"}, "expenses": {"type": "number", "format": "float", "description": "Spese attuali del progetto"}, "created_at": {"type": "string", "format": "date-time", "description": "Data e ora di creazione del progetto"}, "updated_at": {"type": "string", "format": "date-time", "description": "Data e ora dell'ultimo aggiornamento del progetto"}}, "required": ["name"]}, "Pagination": {"type": "object", "properties": {"page": {"type": "integer", "description": "<PERSON><PERSON><PERSON> corrente"}, "per_page": {"type": "integer", "description": "Elementi per pagina"}, "total": {"type": "integer", "description": "Numero totale di elementi"}, "pages": {"type": "integer", "description": "Numero totale di pagine"}, "has_next": {"type": "boolean", "description": "Indica se esiste una pagina successiva"}, "has_prev": {"type": "boolean", "description": "Indica se esiste una pagina precedente"}}}, "KPI": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del KPI"}, "name": {"type": "string", "description": "Nome del KPI"}, "description": {"type": "string", "description": "Descrizione del KPI"}, "category": {"type": "string", "description": "Categoria del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI"}, "unit": {"type": "string", "description": "Unità di misura"}, "frequency": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "annually"], "description": "Frequenza di misurazione"}, "progress": {"type": "number", "description": "Percentuale di completamento del KPI"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}, "updated_at": {"type": "string", "format": "date-time", "description": "Data di ultimo aggiornamento"}}}, "ProjectKPI": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del KPI di progetto"}, "project_id": {"type": "integer", "description": "ID del progetto"}, "kpi_id": {"type": "integer", "description": "ID del KPI"}, "target_value": {"type": "number", "description": "Valore target del KPI"}, "current_value": {"type": "number", "description": "Valore attuale del KPI"}, "project_name": {"type": "string", "description": "Nome del progetto"}, "kpi_name": {"type": "string", "description": "Nome del KPI"}, "kpi_unit": {"type": "string", "description": "Unità di misura del KPI"}, "progress": {"type": "number", "description": "Percentuale di completamento del KPI"}}}, "TaskDependency": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della dipendenza"}, "task_id": {"type": "integer", "description": "ID del task che dipende"}, "depends_on_id": {"type": "integer", "description": "ID del task da cui dipende"}, "task_name": {"type": "string", "description": "Nome del task che dipende"}, "depends_on_name": {"type": "string", "description": "Nome del task da cui dipende"}}}, "ProjectResource": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della risorsa"}, "project_id": {"type": "integer", "description": "ID del progetto"}, "user_id": {"type": "integer", "description": "ID dell'utente"}, "allocation_percentage": {"type": "integer", "description": "Percentuale di allocazione (1-100)"}, "role": {"type": "string", "description": "Ruolo dell'utente nel progetto"}, "project_name": {"type": "string", "description": "Nome del progetto"}, "user_name": {"type": "string", "description": "Nome dell'utente"}}}, "Task": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "description": "ID univoco del task"}, "name": {"type": "string", "description": "Nome del task"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del task"}, "project_id": {"type": "integer", "format": "int64", "description": "ID del progetto a cui appartiene il task"}, "assignee_id": {"type": "integer", "format": "int64", "nullable": true, "description": "ID dell'utente assegnato al task"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "description": "Stato del task"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "description": "Priorità del task"}, "due_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di scadenza del task (YYYY-MM-DD)"}, "created_at": {"type": "string", "format": "date-time", "description": "Data e ora di creazione del task"}, "updated_at": {"type": "string", "format": "date-time", "description": "Data e ora dell'ultimo aggiornamento del task"}}, "required": ["name", "project_id"]}, "Error": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string"}}}, "UserSummary": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID dell'utente"}, "username": {"type": "string", "description": "Nome utente"}, "email": {"type": "string", "description": "Email dell'utente"}, "first_name": {"type": "string", "description": "Nome"}, "last_name": {"type": "string", "description": "Cognome"}, "full_name": {"type": "string", "description": "Nome completo"}, "role": {"type": "string", "enum": ["admin", "manager", "employee", "human_resources"], "description": "Ruolo dell'utente"}, "department_id": {"type": "integer", "nullable": true, "description": "ID del dipartimento"}, "department_name": {"type": "string", "nullable": true, "description": "Nome del dipartimento"}, "position": {"type": "string", "nullable": true, "description": "Posizione lavorativa"}, "hire_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di assunzione"}, "phone": {"type": "string", "nullable": true, "description": "Numero di telefono"}, "profile_image": {"type": "string", "nullable": true, "description": "URL immagine profilo"}, "is_active": {"type": "boolean", "description": "Stato attivo dell'utente"}, "last_login": {"type": "string", "format": "date-time", "nullable": true, "description": "Ultimo accesso"}, "skills": {"type": "array", "items": {"$ref": "#/components/schemas/UserSkill"}, "description": "Competenze dell'utente"}, "profile_completion": {"type": "number", "description": "Percentuale completamento profilo"}}}, "UserDetail": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID dell'utente"}, "username": {"type": "string", "description": "Nome utente"}, "email": {"type": "string", "description": "Email dell'utente"}, "first_name": {"type": "string", "description": "Nome"}, "last_name": {"type": "string", "description": "Cognome"}, "full_name": {"type": "string", "description": "Nome completo"}, "role": {"type": "string", "enum": ["admin", "manager", "employee", "human_resources"], "description": "Ruolo dell'utente"}, "department_id": {"type": "integer", "nullable": true, "description": "ID del dipartimento"}, "department": {"$ref": "#/components/schemas/Department", "nullable": true, "description": "Dettagli del dipartimento"}, "position": {"type": "string", "nullable": true, "description": "Posizione lavorativa"}, "hire_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di assunzione"}, "phone": {"type": "string", "nullable": true, "description": "Numero di telefono"}, "profile_image": {"type": "string", "nullable": true, "description": "URL immagine profilo"}, "bio": {"type": "string", "nullable": true, "description": "Biografia dell'utente"}, "is_active": {"type": "boolean", "description": "Stato attivo dell'utente"}, "dark_mode": {"type": "boolean", "description": "Preferenza modalità scura"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}, "last_login": {"type": "string", "format": "date-time", "nullable": true, "description": "Ultimo accesso"}, "skills": {"type": "array", "items": {"$ref": "#/components/schemas/UserSkillDetail"}, "description": "Competenze dettagliate dell'utente"}, "projects": {"type": "array", "items": {"$ref": "#/components/schemas/UserProject"}, "description": "Progetti dell'utente"}, "profile": {"$ref": "#/components/schemas/UserProfile", "nullable": true, "description": "Profilo esteso dell'utente"}}}, "Department": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del dipartimento"}, "name": {"type": "string", "description": "Nome del dipartimento"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del dipartimento"}, "manager_id": {"type": "integer", "nullable": true, "description": "ID del manager del dipartimento"}, "manager": {"$ref": "#/components/schemas/UserSummary", "nullable": true, "description": "Manager del dipartimento"}, "user_count": {"type": "integer", "description": "Numero di utenti nel dipartimento"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/UserSummary"}, "description": "Utenti del dipartimento"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}}}, "Skill": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della competenza"}, "name": {"type": "string", "description": "Nome della competenza"}, "category": {"type": "string", "description": "Categoria della competenza"}, "description": {"type": "string", "nullable": true, "description": "Descrizione della competenza"}, "user_count": {"type": "integer", "description": "Numero di utenti con questa competenza"}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/UserSkill"}, "description": "Utenti con questa competenza"}}}, "UserSkill": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della competenza"}, "name": {"type": "string", "description": "Nome della competenza"}, "category": {"type": "string", "description": "Categoria della competenza"}, "proficiency_level": {"type": "integer", "minimum": 1, "maximum": 5, "description": "Livello di competenza (1-5)"}, "years_experience": {"type": "integer", "nullable": true, "description": "Anni di esperienza"}}}, "UserSkillDetail": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID della competenza"}, "name": {"type": "string", "description": "Nome della competenza"}, "category": {"type": "string", "description": "Categoria della competenza"}, "description": {"type": "string", "nullable": true, "description": "Descrizione della competenza"}, "proficiency_level": {"type": "integer", "minimum": 1, "maximum": 5, "description": "Livello di competenza (1-5)"}, "years_experience": {"type": "integer", "nullable": true, "description": "Anni di esperienza"}, "certified": {"type": "boolean", "description": "Certificazione ottenuta"}, "last_used": {"type": "string", "format": "date", "nullable": true, "description": "Data certificazione"}}}, "UserProject": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del progetto"}, "name": {"type": "string", "description": "Nome del progetto"}, "status": {"type": "string", "enum": ["planning", "active", "completed", "on-hold"], "description": "Stato del progetto"}, "role": {"type": "string", "description": "Ruolo dell'utente nel progetto"}}}, "UserProfile": {"type": "object", "properties": {"emergency_contact_name": {"type": "string", "nullable": true, "description": "Nome contatto di emergenza"}, "emergency_contact_phone": {"type": "string", "nullable": true, "description": "Telefono contatto di emergenza"}, "address": {"type": "string", "nullable": true, "description": "<PERSON><PERSON><PERSON><PERSON>"}, "profile_completion": {"type": "number", "description": "Percentuale completamento profilo"}, "notes": {"type": "string", "nullable": true, "description": "Note (solo per admin/HR)"}}}, "DashboardActivity": {"type": "object", "properties": {"type": {"type": "string", "enum": ["task", "timesheet", "event"], "description": "Tipo di attività"}, "id": {"type": "integer", "description": "ID dell'attività"}, "title": {"type": "string", "description": "Titolo dell'attività"}, "description": {"type": "string", "description": "Descrizione dell'attività"}, "timestamp": {"type": "string", "format": "date-time", "description": "Timestamp dell'attività"}, "link": {"type": "string", "description": "Link per visualiz<PERSON>e l'attività"}}}, "UpcomingTask": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del task"}, "name": {"type": "string", "description": "Nome del task"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del task"}, "project_id": {"type": "integer", "description": "ID del progetto"}, "project_name": {"type": "string", "description": "Nome del progetto"}, "assignee_id": {"type": "integer", "nullable": true, "description": "ID dell'assegnatario"}, "assignee_name": {"type": "string", "nullable": true, "description": "Nome dell'assegnatario"}, "status": {"type": "string", "enum": ["todo", "in-progress", "review", "done"], "description": "Stato del task"}, "priority": {"type": "string", "enum": ["low", "medium", "high", "urgent"], "description": "Priorità del task"}, "due_date": {"type": "string", "format": "date", "description": "Data di scadenza"}, "days_until_due": {"type": "integer", "description": "<PERSON><PERSON><PERSON> alla scadenza"}, "estimated_hours": {"type": "number", "nullable": true, "description": "Ore stimate"}, "is_overdue": {"type": "boolean", "description": "Indica se il task è in ritardo"}, "link": {"type": "string", "description": "Link per visualizzare il task"}}}, "DashboardKPI": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID del KPI"}, "name": {"type": "string", "description": "Nome del KPI"}, "category": {"type": "string", "description": "Categoria del KPI"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del KPI"}, "current_value": {"type": "number", "description": "<PERSON><PERSON> at<PERSON>"}, "target_value": {"type": "number", "description": "Valore target"}, "unit": {"type": "string", "nullable": true, "description": "Unità di misura"}, "trend": {"type": "string", "enum": ["up", "down", "stable"], "nullable": true, "description": "Trend del KPI"}, "last_updated": {"type": "string", "format": "date-time", "nullable": true, "description": "Ultimo aggiornamento"}, "performance_percentage": {"type": "number", "description": "Percentuale di performance rispetto al target"}}}, "CurrentUser": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID dell'utente"}, "username": {"type": "string", "description": "Nome utente"}, "email": {"type": "string", "description": "Email dell'utente"}, "first_name": {"type": "string", "description": "Nome"}, "last_name": {"type": "string", "description": "Cognome"}, "full_name": {"type": "string", "description": "Nome completo"}, "role": {"type": "string", "enum": ["admin", "manager", "employee", "human_resources", "sales"], "description": "Ruolo dell'utente"}, "department_id": {"type": "integer", "nullable": true, "description": "ID del dipartimento"}, "department_name": {"type": "string", "nullable": true, "description": "Nome del dipartimento"}, "position": {"type": "string", "nullable": true, "description": "Posizione lavorativa"}, "phone": {"type": "string", "nullable": true, "description": "Numero di telefono"}, "bio": {"type": "string", "nullable": true, "description": "Biografia"}, "profile_image": {"type": "string", "nullable": true, "description": "URL immagine profilo"}, "is_active": {"type": "boolean", "description": "Stato attivo dell'utente"}, "dark_mode": {"type": "boolean", "description": "Preferenza modalità scura"}, "created_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Data di creazione"}, "last_login": {"type": "string", "format": "date-time", "nullable": true, "description": "Ultimo accesso"}, "hire_date": {"type": "string", "format": "date", "nullable": true, "description": "Data di assunzione"}, "permissions": {"type": "array", "items": {"type": "string"}, "description": "Lista dei permessi dell'utente"}, "profile_completion": {"type": "number", "description": "Percentuale completamento profilo"}, "preferences": {"$ref": "#/components/schemas/UserPreferences"}}}, "UserPreferences": {"type": "object", "properties": {"dark_mode": {"type": "boolean", "description": "Modalità scura"}, "language": {"type": "string", "description": "Lingua preferita", "default": "it"}, "timezone": {"type": "string", "description": "<PERSON><PERSON> orario", "default": "Europe/Rome"}, "notifications": {"type": "object", "properties": {"email": {"type": "boolean", "description": "Notifiche email"}, "browser": {"type": "boolean", "description": "Notifiche browser"}, "mobile": {"type": "boolean", "description": "Notifiche mobile"}}}}}, "TimeOffRequest": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID univoco della richiesta"}, "user_id": {"type": "integer", "description": "ID dell'utente che ha fatto la richiesta"}, "user": {"$ref": "#/components/schemas/UserSummary", "description": "Dati dell'utente"}, "request_type": {"type": "string", "enum": ["vacation", "leave", "smartworking"], "description": "Tipo di richiesta"}, "start_date": {"type": "string", "format": "date", "description": "Data di inizio (YYYY-MM-DD)"}, "end_date": {"type": "string", "format": "date", "description": "Data di fine (YYYY-MM-DD)"}, "duration_days": {"type": "integer", "description": "Durata in giorni"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected"], "description": "Stato della richiesta"}, "notes": {"type": "string", "nullable": true, "description": "Note aggiuntive"}, "submission_date": {"type": "string", "format": "date-time", "nullable": true, "description": "Data di sottomissione"}, "approval_date": {"type": "string", "format": "date-time", "nullable": true, "description": "Data di approvazione/rifiuto"}, "approved_by": {"type": "integer", "nullable": true, "description": "ID dell'utente che ha approvato/rifiutato"}, "approver": {"$ref": "#/components/schemas/UserSummary", "nullable": true, "description": "Dati dell'approvatore"}, "rejection_reason": {"type": "string", "nullable": true, "description": "Motivo del rifiuto"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}, "updated_at": {"type": "string", "format": "date-time", "description": "Data di ultimo aggiornamento"}}, "required": ["request_type", "start_date", "end_date"]}, "MonthlyTimesheet": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID univoco del timesheet mensile"}, "user_id": {"type": "integer", "description": "ID dell'utente"}, "user": {"$ref": "#/components/schemas/UserSummary", "description": "Dati dell'utente"}, "year": {"type": "integer", "description": "Anno di riferimento"}, "month": {"type": "integer", "minimum": 1, "maximum": 12, "description": "Mese di riferimento (1-12)"}, "status": {"type": "string", "enum": ["draft", "submitted", "approved", "rejected"], "description": "Stato del timesheet mensile"}, "total_hours": {"type": "number", "description": "Totale ore del mese"}, "billable_hours": {"type": "number", "description": "Ore fatturabili del mese"}, "submission_date": {"type": "string", "format": "date-time", "nullable": true, "description": "Data di sottomissione"}, "approval_date": {"type": "string", "format": "date-time", "nullable": true, "description": "Data di approvazione/rifiuto"}, "approved_by": {"type": "integer", "nullable": true, "description": "ID dell'utente che ha approvato/rifiutato"}, "approver": {"$ref": "#/components/schemas/UserSummary", "nullable": true, "description": "Dati dell'approvatore"}, "rejection_reason": {"type": "string", "nullable": true, "description": "Motivo del rifiuto"}, "entries": {"type": "array", "items": {"$ref": "#/components/schemas/TimesheetEntry"}, "description": "Entries del mese (solo in dettaglio)"}, "entries_count": {"type": "integer", "description": "Numero di entries del mese"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}, "updated_at": {"type": "string", "format": "date-time", "description": "Data di ultimo aggiornamento"}}, "required": ["year", "month"]}, "TimesheetEntry": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID univoco dell'entry"}, "user_id": {"type": "integer", "description": "ID dell'utente"}, "project_id": {"type": "integer", "description": "ID del progetto"}, "project": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}}, "description": "<PERSON><PERSON> del progetto"}, "task_id": {"type": "integer", "nullable": true, "description": "ID del task"}, "task": {"type": "object", "nullable": true, "properties": {"id": {"type": "integer"}, "title": {"type": "string"}}, "description": "<PERSON><PERSON> del <PERSON>"}, "date": {"type": "string", "format": "date", "description": "Data dell'entry (YYYY-MM-DD)"}, "hours": {"type": "number", "description": "Ore lavorate"}, "description": {"type": "string", "nullable": true, "description": "Descrizione del lavoro svolto"}, "status": {"type": "string", "enum": ["pending", "approved", "rejected"], "description": "Stato dell'entry"}, "monthly_timesheet_id": {"type": "integer", "nullable": true, "description": "ID del timesheet mensile associato"}, "billable": {"type": "boolean", "description": "Se l'entry è fatturabile"}, "billing_rate": {"type": "number", "nullable": true, "description": "Tariffa oraria per questa entry"}, "contract_id": {"type": "integer", "nullable": true, "description": "ID del contratto associato"}, "billing_status": {"type": "string", "enum": ["unbilled", "billed", "non-billable"], "description": "Stato di fatturazione"}, "created_at": {"type": "string", "format": "date-time", "description": "Data di creazione"}}, "required": ["user_id", "project_id", "date", "hours"]}}, "parameters": {"pageParam": {"name": "page", "in": "query", "description": "Numero di pagina", "schema": {"type": "integer", "default": 1, "minimum": 1}}, "perPageParam": {"name": "per_page", "in": "query", "description": "Elementi per pagina", "schema": {"type": "integer", "default": 10, "minimum": 1, "maximum": 100}}}, "securitySchemes": {"cookieAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "session"}, "viewAllTimeOff": {"type": "oauth2", "description": "<PERSON><PERSON><PERSON> per visualizzare richieste time-off di tutti gli utenti", "flows": {"implicit": {"authorizationUrl": "/auth/permissions", "scopes": {"view_all_time_off": "Visualizza richieste time-off di tutti gli utenti"}}}}, "approveTimeOff": {"type": "oauth2", "description": "Permesso per approvare/rifiutare richieste time-off", "flows": {"implicit": {"authorizationUrl": "/auth/permissions", "scopes": {"approve_time_off": "Approva/rifiuta richieste time-off"}}}}, "viewAllTimesheets": {"type": "oauth2", "description": "Permesso per visualizzare timesheet di tutti gli utenti", "flows": {"implicit": {"authorizationUrl": "/auth/permissions", "scopes": {"view_all_timesheets": "Visualizza timesheet di tutti gli utenti"}}}}, "approveTimesheets": {"type": "oauth2", "description": "Permesso per approvare/rifiutare timesheet mensili", "flows": {"implicit": {"authorizationUrl": "/auth/permissions", "scopes": {"approve_timesheets": "A<PERSON>rova/rifiuta timesheet mensili"}}}}, "manageTimesheets": {"type": "oauth2", "description": "Permesso per gestire timesheet di altri utenti", "flows": {"implicit": {"authorizationUrl": "/auth/permissions", "scopes": {"manage_timesheets": "Gestisce timesheet di altri utenti"}}}}}}, "security": [{"cookieAuth": []}]}