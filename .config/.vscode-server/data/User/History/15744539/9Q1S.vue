<template>
  <div>
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Template KPI</h1>
        <p class="text-gray-600 dark:text-gray-400">Configura KPI di default per tipologie progetto</p>
      </div>
      <div class="flex space-x-3">
        <button 
          @click="showCreateModal = true"
          class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          + Nuovo Template
        </button>
        <button 
          @click="resetToDefaults()"
          class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"
        >
          🔄 Reset Default
        </button>
      </div>
    </div>

    <!-- Filtri -->
    <div class="mb-6">
      <div class="flex space-x-4 mb-3">
        <select 
          v-model="selectedType" 
          @change="filterTemplates()"
          class="border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="">Tutte le tipologie</option>
          <option 
            v-for="type in projectTypes" 
            :key="type.key" 
            :value="type.key"
          >
            {{ type.name }}
          </option>
        </select>

        <select 
          v-model="selectedKPI" 
          @change="filterTemplates()"
          class="border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="">Tutti i KPI</option>
          <option value="margin_percentage">Margine Netto</option>
          <option value="utilization_rate">Utilization Rate</option>
          <option value="cost_per_hour">Costo per Ora</option>
          <option value="cost_revenue_ratio">Rapporto C/R</option>
        </select>

        <!-- Reset filtri -->
        <button 
          v-if="selectedType || selectedKPI"
          @click="resetFilters()"
          class="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          🗑️ Reset
        </button>
      </div>
    </div>

    <!-- Loading -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
    </div>

    <!-- Error -->
    <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <p class="text-red-600">{{ error }}</p>
    </div>

    <!-- Tabella Template -->
    <div v-if="!loading" class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
      <ul class="divide-y divide-gray-200 dark:divide-gray-700">
        <li v-for="template in filteredTemplates" :key="template.id" class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center">
                <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ getProjectTypeName(template.project_type) }} - {{ template.display_name }}
                </h3>
                <span v-if="!template.is_active" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  Inattivo
                </span>
              </div>
              <div class="mt-1 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span>Target: {{ template.target_min }} - {{ template.target_max }} {{ template.unit }}</span>
                <span v-if="template.warning_threshold">Warning: {{ template.warning_threshold }} {{ template.unit }}</span>
              </div>
              <p v-if="template.description" class="mt-1 text-sm text-gray-600 dark:text-gray-300">
                {{ template.description }}
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <button 
                @click="editTemplate(template)"
                class="text-primary-600 hover:text-primary-900 text-sm font-medium"
              >
                Modifica
              </button>
              <button 
                @click="toggleTemplate(template)"
                class="text-gray-600 hover:text-gray-900 text-sm font-medium"
              >
                {{ template.is_active ? 'Disattiva' : 'Attiva' }}
              </button>
              <button 
                @click="deleteTemplate(template)"
                class="text-red-600 hover:text-red-900 text-sm font-medium"
              >
                Elimina
              </button>
            </div>
          </div>
        </li>
      </ul>

      <!-- Empty state -->
      <div v-if="filteredTemplates.length === 0" class="text-center py-12">
        <p class="text-gray-500 dark:text-gray-400">Nessun template trovato</p>
      </div>
    </div>

    <!-- Modal Creazione/Modifica -->
    <div v-if="showCreateModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeModal">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {{ showEditModal ? 'Modifica Template KPI' : 'Nuovo Template KPI' }}
          </h3>
          
          <form @submit.prevent="saveTemplate">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tipologia Progetto
              </label>
              <select 
                v-model="formData.project_type" 
                required
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Seleziona tipologia</option>
                <option 
                  v-for="type in projectTypes" 
                  :key="type.key" 
                  :value="type.key"
                >
                  {{ type.name }}
                </option>
              </select>
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                KPI
              </label>
              <select 
                v-model="formData.kpi_name" 
                required
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Seleziona KPI</option>
                <option value="margin_percentage">Margine Netto</option>
                <option value="utilization_rate">Utilization Rate</option>
                <option value="cost_per_hour">Costo per Ora</option>
                <option value="cost_revenue_ratio">Rapporto C/R</option>
              </select>
            </div>

            <div class="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Target Min
                </label>
                <input 
                  v-model="formData.target_min" 
                  type="number" 
                  step="0.01"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                >
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Target Max
                </label>
                <input 
                  v-model="formData.target_max" 
                  type="number" 
                  step="0.01"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                >
              </div>
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Soglia Warning
              </label>
              <input 
                v-model="formData.warning_threshold" 
                type="number" 
                step="0.01"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Unità
              </label>
              <select 
                v-model="formData.unit"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="%">%</option>
                <option value="€">€</option>
                <option value="ratio">ratio</option>
                <option value="giorni">giorni</option>
              </select>
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Descrizione
              </label>
              <textarea 
                v-model="formData.description" 
                rows="3"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              ></textarea>
            </div>

            <div class="flex justify-end space-x-3">
              <button 
                type="button" 
                @click="closeModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"
              >
                Annulla
              </button>
              <button 
                type="submit"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md"
              >
                {{ showEditModal ? 'Aggiorna' : 'Crea' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'

const authStore = useAuthStore()

// State
const templates = ref([])
const loading = ref(false)
const error = ref('')

// Filtri
const selectedType = ref('')
const selectedKPI = ref('')

// Modal
const showCreateModal = ref(false)
const showEditModal = ref(false)
const editingTemplate = ref(null)

// Form data
const formData = ref({
  project_type: '',
  kpi_name: '',
  target_min: null,
  target_max: null,
  warning_threshold: null,
  unit: '%',
  description: ''
})

// Project types
const projectTypes = ref([
  { key: 'service', name: 'Servizio' },
  { key: 'license', name: 'Licenza' },
  { key: 'consulting', name: 'Consulenza' },
  { key: 'product', name: 'Prodotto' },
  { key: 'rd', name: 'R&D' },
  { key: 'internal', name: 'Interno' }
])

// Computed
const filteredTemplates = computed(() => {
  let filtered = templates.value

  if (selectedType.value) {
    filtered = filtered.filter(t => t.project_type === selectedType.value)
  }

  if (selectedKPI.value) {
    filtered = filtered.filter(t => t.kpi_name === selectedKPI.value)
  }

  return filtered
})

// Methods
const fetchTemplates = async () => {
  loading.value = true
  error.value = ''

  try {
    const response = await api.get('/api/admin/kpi-templates')
    templates.value = response.data.data || response.data
  } catch (err) {
    error.value = err.response?.data?.message || err.message || 'Errore nel caricamento dei template'
  } finally {
    loading.value = false
  }
}

const saveTemplate = async () => {
  try {
    if (showEditModal.value) {
      await api.put(`/api/admin/kpi-templates/${editingTemplate.value.id}`, formData.value)
    } else {
      await api.post('/api/admin/kpi-templates', formData.value)
    }

    await fetchTemplates()
    closeModal()
  } catch (err) {
    error.value = err.response?.data?.message || err.message || 'Errore nel salvataggio del template'
  }
}

const editTemplate = (template) => {
  editingTemplate.value = template
  formData.value = { ...template }
  showEditModal.value = true
}

const toggleTemplate = async (template) => {
  try {
    await api.put(`/api/admin/kpi-templates/${template.id}/toggle`)
    await fetchTemplates()
  } catch (err) {
    error.value = err.response?.data?.message || err.message || 'Errore nel toggle del template'
  }
}

const deleteTemplate = async (template) => {
  if (!confirm('Sei sicuro di voler eliminare questo template?')) {
    return
  }

  try {
    await api.delete(`/api/admin/kpi-templates/${template.id}`)
    await fetchTemplates()
  } catch (err) {
    error.value = err.response?.data?.message || err.message || 'Errore nell\'eliminazione del template'
  }
}

const resetToDefaults = async () => {
  if (!confirm('Sei sicuro di voler ripristinare i template di default? Questa azione eliminerà tutti i template personalizzati.')) {
    return
  }

  try {
    const response = await fetch('/api/admin/kpi-templates/reset', {
      method: 'POST',
      headers: {
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) {
      throw new Error('Errore nel reset dei template')
    }

    await fetchTemplates()
  } catch (err) {
    error.value = err.message
  }
}

const filterTemplates = () => {
  // Filtri applicati automaticamente dal computed
}

const resetFilters = () => {
  selectedType.value = ''
  selectedKPI.value = ''
}

const closeModal = () => {
  showCreateModal.value = false
  showEditModal.value = false
  editingTemplate.value = null
  formData.value = {
    project_type: '',
    kpi_name: '',
    target_min: null,
    target_max: null,
    warning_threshold: null,
    unit: '%',
    description: ''
  }
}

const getProjectTypeName = (key) => {
  const type = projectTypes.value.find(t => t.key === key)
  return type ? type.name : key
}

// Lifecycle
onMounted(() => {
  fetchTemplates()
})
</script>