"""
Utilità per calcolo costi e ricavi dei progetti
"""
from datetime import date, datetime
from models import PersonnelRate, ProjectExpense, TimesheetEntry, Project
from extensions import db


def get_user_daily_rate_for_date(user_id, target_date):
    """
    Ottiene la tariffa giornaliera valida per un utente in una data specifica

    Args:
        user_id (int): ID dell'utente
        target_date (date): Data per cui cercare la tariffa

    Returns:
        float: Tariffa giornaliera o None se non trovata
    """
    rate = PersonnelRate.query.filter(
        PersonnelRate.user_id == user_id,
        PersonnelRate.valid_from <= target_date,
        db.or_(
            PersonnelRate.valid_to.is_(None),
            PersonnelRate.valid_to >= target_date
        )
    ).order_by(PersonnelRate.valid_from.desc()).first()

    return rate.daily_rate if rate else None


def get_user_current_daily_rate(user_id):
    """
    Ottiene la tariffa giornaliera attuale per un utente

    Args:
        user_id (int): ID dell'utente

    Returns:
        float: Tariffa giornaliera attuale o None
    """
    return get_user_daily_rate_for_date(user_id, date.today())


def calculate_personnel_cost_for_period(project_id, start_date, end_date):
    """
    Calcola il costo del personale per un progetto in un periodo

    Args:
        project_id (int): ID del progetto
        start_date (date): Data inizio
        end_date (date): Data fine

    Returns:
        dict: {
            'total_cost': float,
            'total_days': int,
            'user_costs': [{'user_id': int, 'days': int, 'cost': float}]
        }
    """
    # Ottieni tutti i timesheet del progetto nel periodo
    timesheets = Timesheet.query.filter(
        Timesheet.project_id == project_id,
        Timesheet.date >= start_date,
        Timesheet.date <= end_date
    ).all()

    # Raggruppa per utente e data
    user_days = {}
    for ts in timesheets:
        if ts.user_id not in user_days:
            user_days[ts.user_id] = set()
        user_days[ts.user_id].add(ts.date)

    # Calcola costi per utente
    user_costs = []
    total_cost = 0.0
    total_days = 0

    for user_id, dates in user_days.items():
        user_total_cost = 0.0
        days_worked = len(dates)

        # Calcola costo per ogni giorno lavorato
        for work_date in dates:
            daily_rate = get_user_daily_rate_for_date(user_id, work_date)
            if daily_rate:
                user_total_cost += daily_rate

        user_costs.append({
            'user_id': user_id,
            'days': days_worked,
            'cost': user_total_cost
        })

        total_cost += user_total_cost
        total_days += days_worked

    return {
        'total_cost': total_cost,
        'total_days': total_days,
        'user_costs': user_costs
    }


def calculate_project_expenses(project_id, start_date=None, end_date=None, billing_type=None):
    """
    Calcola le spese di progetto (non personale)

    Args:
        project_id (int): ID del progetto
        start_date (date, optional): Data inizio filtro
        end_date (date, optional): Data fine filtro
        billing_type (str, optional): Tipo fatturazione (billable, non-billable, reimbursable)

    Returns:
        dict: {
            'total_amount': float,
            'by_category': dict,
            'by_billing_type': dict
        }
    """
    query = ProjectExpense.query.filter(ProjectExpense.project_id == project_id)

    if start_date:
        query = query.filter(ProjectExpense.date >= start_date)
    if end_date:
        query = query.filter(ProjectExpense.date <= end_date)
    if billing_type:
        query = query.filter(ProjectExpense.billing_type == billing_type)

    expenses = query.all()

    total_amount = sum(exp.amount for exp in expenses)

    # Raggruppa per categoria
    by_category = {}
    for exp in expenses:
        if exp.category not in by_category:
            by_category[exp.category] = 0.0
        by_category[exp.category] += exp.amount

    # Raggruppa per tipo fatturazione
    by_billing_type = {}
    for exp in expenses:
        if exp.billing_type not in by_billing_type:
            by_billing_type[exp.billing_type] = 0.0
        by_billing_type[exp.billing_type] += exp.amount

    return {
        'total_amount': total_amount,
        'by_category': by_category,
        'by_billing_type': by_billing_type
    }


def calculate_project_profitability(project_id):
    """
    Calcola la redditività completa di un progetto

    Args:
        project_id (int): ID del progetto

    Returns:
        dict: Analisi completa costi/ricavi
    """
    project = Project.query.get(project_id)
    if not project:
        return None

    # Calcola costi personale
    personnel_costs = calculate_personnel_cost_for_period(
        project_id,
        project.start_date or date(2020, 1, 1),
        project.end_date or date.today()
    )

    # Calcola altre spese
    other_expenses = calculate_project_expenses(project_id)

    # Calcola ricavi potenziali
    potential_revenue = 0.0
    if project.is_billable and project.client_daily_rate:
        potential_revenue = project.client_daily_rate * personnel_costs['total_days']

    # Totali
    total_costs = personnel_costs['total_cost'] + other_expenses['total_amount']
    billable_expenses = calculate_project_expenses(project_id, billing_type='billable')['total_amount']

    # Calcola margini
    gross_margin = potential_revenue - personnel_costs['total_cost']
    net_margin = potential_revenue - total_costs

    return {
        'project': {
            'id': project_id,
            'name': project.name,
            'is_billable': project.is_billable,
            'client_daily_rate': project.client_daily_rate,
            'budget': project.budget
        },
        'personnel': personnel_costs,
        'expenses': other_expenses,
        'revenue': {
            'potential': potential_revenue,
            'billable_expenses': billable_expenses,
            'total_billable': potential_revenue + billable_expenses
        },
        'profitability': {
            'total_costs': total_costs,
            'gross_margin': gross_margin,
            'net_margin': net_margin,
            'gross_margin_percentage': (gross_margin / potential_revenue * 100) if potential_revenue > 0 else 0,
            'net_margin_percentage': (net_margin / potential_revenue * 100) if potential_revenue > 0 else 0
        }
    }


def calculate_project_kpis(project_id):
    """
    Calcola tutti i KPI per un progetto

    Returns:
        dict: {
            'margin_percentage': float,
            'utilization_rate': float,
            'cost_per_hour': float,
            'cost_revenue_ratio': float
        }
    """
    profitability = calculate_project_profitability(project_id)
    if not profitability:
        return {}

    # Recupera l'oggetto project per accedere alle date
    project = Project.query.get(project_id)
    if not project:
        return {}

    # Margine Netto %
    margin_percentage = profitability['profitability']['net_margin_percentage']

    # Utilization Rate % - Calcolo ibrido basato su dati disponibili
    total_days = profitability['personnel']['total_days']
    team_size = len(profitability['personnel']['user_costs'])

    # Priorità 1: Budget-based (se disponibili budget e tariffa cliente)
    if profitability['project']['budget'] and profitability['project']['client_daily_rate']:
        theoretical_days = profitability['project']['budget'] / profitability['project']['client_daily_rate']
    # Priorità 2: Timeline-based (se disponibili date progetto)
    elif project.start_date and project.end_date:
        project_duration_days = (project.end_date - project.start_date).days
        working_days = project_duration_days * 5/7  # Solo giorni lavorativi (esclude weekend)
        theoretical_days = team_size * working_days * 0.8  # 80% efficienza attesa del team
    # Fallback: Metodo precedente migliorato
    else:
        theoretical_days = team_size * 22 if team_size > 0 else 1  # 22 giorni lavorativi/mese medio

    utilization_rate = (total_days / theoretical_days * 100) if theoretical_days > 0 else 0

    # Costo per Ora
    total_costs = profitability['profitability']['total_costs']
    total_hours = sum(ts.hours for ts in Timesheet.query.filter_by(project_id=project_id).all())
    cost_per_hour = (total_costs / total_hours) if total_hours > 0 else 0

    # Cost/Revenue Ratio
    total_revenue = profitability['revenue']['potential']
    cost_revenue_ratio = (total_costs / total_revenue) if total_revenue > 0 else float('inf')

    return {
        'margin_percentage': round(margin_percentage, 1),
        'utilization_rate': round(utilization_rate, 1),
        'cost_per_hour': round(cost_per_hour, 2),
        'cost_revenue_ratio': round(cost_revenue_ratio, 3)
    }


def get_kpi_status(kpi_value, kpi_name, project_type, project_id=None):
    """
    Determina lo status di un KPI (good, warning, critical)

    Args:
        kpi_value: Valore attuale del KPI
        kpi_name: Nome del KPI
        project_type: Tipologia progetto
        project_id: ID progetto (per KPI personalizzati)

    Returns:
        dict: {'status': 'good|warning|critical', 'color': 'green|yellow|red'}
    """
    from models import ProjectKPITemplate, ProjectKPITarget

    # Prima cerca KPI personalizzati per il progetto
    if project_id:
        custom_kpi = ProjectKPITarget.query.filter_by(
            project_id=project_id,
            kpi_name=kpi_name
        ).first()

        if custom_kpi:
            if kpi_value >= custom_kpi.target_value:
                return {'status': 'good', 'color': 'green'}
            elif kpi_value >= custom_kpi.warning_threshold:
                return {'status': 'warning', 'color': 'yellow'}
            else:
                return {'status': 'critical', 'color': 'red'}

    # Fallback su template default
    template = ProjectKPITemplate.query.filter_by(
        project_type=project_type,
        kpi_name=kpi_name,
        is_active=True
    ).first()

    if template:
        if kpi_value >= template.target_min:
            return {'status': 'good', 'color': 'green'}
        elif kpi_value >= template.warning_threshold:
            return {'status': 'warning', 'color': 'yellow'}
        else:
            return {'status': 'critical', 'color': 'red'}

    # Default neutro se non ci sono template
    return {'status': 'unknown', 'color': 'gray'}


def get_default_daily_rates():
    """
    Restituisce tariffe giornaliere di default per ruolo
    Usato quando non ci sono tariffe specifiche
    """
    return {
        'admin': 400.0,
        'manager': 350.0,
        'employee': 250.0,
        'intern': 100.0
    }


def get_default_kpi_templates():
    """
    Restituisce template KPI di default per tipologie progetto
    """
    return {
        'service': {
            'margin_percentage': {'target_min': 25, 'target_max': 40, 'warning_threshold': 15, 'unit': '%'},
            'utilization_rate': {'target_min': 75, 'target_max': 85, 'warning_threshold': 60, 'unit': '%'},
            'cost_per_hour': {'target_min': 30, 'target_max': 50, 'warning_threshold': 60, 'unit': '€'},
            'cost_revenue_ratio': {'target_min': 0.6, 'target_max': 0.75, 'warning_threshold': 0.85, 'unit': 'ratio'}
        },
        'license': {
            'margin_percentage': {'target_min': 70, 'target_max': 90, 'warning_threshold': 50, 'unit': '%'},
            'utilization_rate': {'target_min': 60, 'target_max': 80, 'warning_threshold': 40, 'unit': '%'},
            'cost_per_hour': {'target_min': 20, 'target_max': 40, 'warning_threshold': 50, 'unit': '€'},
            'cost_revenue_ratio': {'target_min': 0.1, 'target_max': 0.3, 'warning_threshold': 0.5, 'unit': 'ratio'}
        },
        'consulting': {
            'margin_percentage': {'target_min': 40, 'target_max': 60, 'warning_threshold': 25, 'unit': '%'},
            'utilization_rate': {'target_min': 80, 'target_max': 90, 'warning_threshold': 65, 'unit': '%'},
            'cost_per_hour': {'target_min': 40, 'target_max': 70, 'warning_threshold': 80, 'unit': '€'},
            'cost_revenue_ratio': {'target_min': 0.4, 'target_max': 0.6, 'warning_threshold': 0.75, 'unit': 'ratio'}
        },
        'product': {
            'margin_percentage': {'target_min': 30, 'target_max': 50, 'warning_threshold': 20, 'unit': '%'},
            'utilization_rate': {'target_min': 70, 'target_max': 85, 'warning_threshold': 55, 'unit': '%'},
            'cost_per_hour': {'target_min': 25, 'target_max': 45, 'warning_threshold': 55, 'unit': '€'},
            'cost_revenue_ratio': {'target_min': 0.5, 'target_max': 0.7, 'warning_threshold': 0.8, 'unit': 'ratio'}
        },
        'rd': {
            'margin_percentage': {'target_min': 0, 'target_max': 20, 'warning_threshold': -20, 'unit': '%'},
            'utilization_rate': {'target_min': 60, 'target_max': 80, 'warning_threshold': 40, 'unit': '%'},
            'cost_per_hour': {'target_min': 50, 'target_max': 80, 'warning_threshold': 100, 'unit': '€'},
            'cost_revenue_ratio': {'target_min': 0.8, 'target_max': 1.2, 'warning_threshold': 1.5, 'unit': 'ratio'}
        },
        'internal': {
            'margin_percentage': {'target_min': 0, 'target_max': 0, 'warning_threshold': -10, 'unit': '%'},
            'utilization_rate': {'target_min': 70, 'target_max': 85, 'warning_threshold': 50, 'unit': '%'},
            'cost_per_hour': {'target_min': 30, 'target_max': 50, 'warning_threshold': 70, 'unit': '€'},
            'cost_revenue_ratio': {'target_min': 1.0, 'target_max': 1.0, 'warning_threshold': 1.2, 'unit': 'ratio'}
        }
    }
