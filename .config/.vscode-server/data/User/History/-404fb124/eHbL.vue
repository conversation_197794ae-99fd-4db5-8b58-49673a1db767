<template>
  <div class="space-y-6">
    <!-- Current CV Section -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">CV Attuale</h3>
        <button v-if="canEdit && !uploading"
                @click="triggerFileUpload"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200">
          <svg class="w-4 h-4 inline mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
          {{ user.profile?.current_cv_path ? 'Aggiorna CV' : 'Carica CV' }}
        </button>
      </div>

      <!-- CV Upload Area -->
      <div v-if="!user.profile?.current_cv_path && canEdit"
           @drop="handleDrop"
           @dragover.prevent
           @dragenter.prevent
           :class="[
             'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200',
             isDragOver ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'
           ]">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Carica il tuo CV</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Trascina qui il file o <button @click="triggerFileUpload" class="text-blue-600 hover:text-blue-500">sfoglia</button>
        </p>
        <p class="mt-1 text-xs text-gray-400">PDF, DOCX, DOC, TXT (max 10MB)</p>
      </div>

      <!-- Existing CV Display -->
      <div v-else-if="user.profile?.current_cv_path" class="flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <svg class="w-8 h-8 text-red-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
        </svg>
        <div class="flex-1">
          <p class="text-sm font-medium text-gray-900 dark:text-white">CV_{{ user.full_name }}.pdf</p>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            Caricato il {{ formatDate(user.profile.cv_last_updated) }}
          </p>
          <div v-if="user.profile.cv_analysis_data" class="mt-1">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              ✨ Analisi AI completata
            </span>
          </div>
        </div>
        <div class="flex space-x-2">
          <button @click="downloadCV" 
                  class="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                  title="Scarica CV">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
          <button v-if="canEdit" 
                  @click="deleteCV" 
                  class="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                  title="Elimina CV">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" clip-rule="evenodd"></path>
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 112 0v4a1 1 0 11-2 0V7z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Upload Progress -->
      <div v-if="uploading || analyzingWithAI" class="mt-4">
        <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
          <div class="flex items-center">
            <svg v-if="analyzingWithAI" class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>{{ analyzingWithAI ? 'Analisi AI in corso...' : 'Caricamento in corso...' }}</span>
          </div>
          <span>{{ analyzingWithAI ? aiAnalysisProgress : uploadProgress }}%</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
          <div :class="[
                 'h-2 rounded-full transition-all duration-300',
                 analyzingWithAI ? 'bg-purple-600' : 'bg-blue-600'
               ]"
               :style="{ width: (analyzingWithAI ? aiAnalysisProgress : uploadProgress) + '%' }"></div>
        </div>
      </div>
    </div>

    <!-- AI Analysis Section -->
    <div v-if="cvAnalysis" class="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3">
            <svg class="w-4 h-4 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-purple-900 dark:text-purple-100">
            Analisi AI del CV
          </h3>
        </div>
        <div class="flex items-center space-x-3">
          <button v-if="canEdit && cvAnalysis.skills?.length > 0"
                  @click="showSkillsModal = true"
                  class="text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
            </svg>
            Aggiungi Competenze
          </button>
        </div>
      </div>

      <!-- Professional Summary -->
      <div v-if="cvAnalysis.summary" class="mb-4">
        <h4 class="text-sm font-medium text-purple-800 dark:text-purple-200 mb-2">Profilo Professionale</h4>
        <p class="text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700">{{ cvAnalysis.summary }}</p>
      </div>

      <!-- Experience Years -->
      <div v-if="cvAnalysis.experience_years" class="mb-4">
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200">
          <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
          </svg>
          {{ cvAnalysis.experience_years }} anni di esperienza
        </span>
      </div>

      <!-- Extracted Skills -->
      <div v-if="cvAnalysis.skills?.length > 0">
        <h4 class="text-sm font-medium text-purple-800 dark:text-purple-200 mb-3">Competenze Estratte</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
          <div v-for="(skill, index) in cvAnalysis.skills.slice(0, 8)"
               :key="index"
               class="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700">
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ skill.name }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span v-if="skill.category" class="text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded">
                {{ skill.category }}
              </span>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ getLevelLabel(skill.level) }}
              </span>
            </div>
          </div>
        </div>
        <div v-if="cvAnalysis.skills.length > 8" class="text-center">
          <span class="text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full">
            +{{ cvAnalysis.skills.length - 8 }} altre competenze disponibili
          </span>
        </div>
      </div>
    </div>

    <!-- Hidden file input -->
    <input ref="fileInput" 
           type="file" 
           accept=".pdf,.docx,.doc,.txt" 
           @change="handleFileSelect" 
           class="hidden">

    <!-- Skills Selection Modal -->
    <div v-if="showSkillsModal" 
         class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"
         @click="showSkillsModal = false">
      <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800"
           @click.stop>
        <div class="mt-3">
          <!-- Modal Header -->
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Seleziona Competenze da Aggiungere
            </h3>
            <button @click="showSkillsModal = false"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>

          <!-- Skills List -->
          <div class="max-h-96 overflow-y-auto mb-4">
            <div class="space-y-2">
              <div v-for="(skill, index) in cvAnalysis.skills" 
                   :key="index"
                   class="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
                <input type="checkbox" 
                       :id="`skill-${index}`"
                       v-model="selectedSkills"
                       :value="index"
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label :for="`skill-${index}`" class="ml-3 flex-1 cursor-pointer">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ skill.name }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ skill.category }} • Livello {{ skill.level || 3 }}
                    <span v-if="skill.years_experience"> • {{ skill.years_experience }} anni</span>
                  </div>
                  <div v-if="skill.context" class="text-xs text-gray-400 mt-1">{{ skill.context }}</div>
                </label>
              </div>
            </div>
          </div>

          <!-- Modal Actions -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
            <button @click="showSkillsModal = false"
                    class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200">
              Annulla
            </button>
            <button @click="addSelectedSkills"
                    :disabled="selectedSkills.length === 0 || addingSkills"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200">
              {{ addingSkills ? 'Aggiungendo...' : `Aggiungi ${selectedSkills.length} competenze` }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useToast } from '@/composables/useToast'

// Props
const props = defineProps({
  user: {
    type: Object,
    required: true
  },
  canEdit: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['user-updated'])

// Composables
const { success: showSuccess, error: showError, info: showInfo } = useToast()

// Reactive state
const uploading = ref(false)
const analyzingWithAI = ref(false)
const uploadProgress = ref(0)
const isDragOver = ref(false)
const showSkillsModal = ref(false)
const selectedSkills = ref([])
const addingSkills = ref(false)
const fileInput = ref(null)
const aiAnalysisProgress = ref(0)

// Computed
const cvAnalysis = computed(() => {
  if (!props.user.profile?.cv_analysis_data) return null
  try {
    return JSON.parse(props.user.profile.cv_analysis_data)
  } catch (e) {
    console.error('Error parsing CV analysis data:', e)
    return null
  }
})

// Methods
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getLevelLabel = (level) => {
  const levels = {
    'beginner': 'Principiante',
    'intermediate': 'Intermedio',
    'advanced': 'Avanzato',
    'expert': 'Esperto',
    1: 'Principiante',
    2: 'Base',
    3: 'Intermedio',
    4: 'Avanzato',
    5: 'Esperto'
  }
  return levels[level] || 'Intermedio'
}



const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    uploadCV(file)
  }
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer.files
  if (files.length > 0) {
    uploadCV(files[0])
  }
}

const uploadCV = async (file) => {
  // Validate file
  const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/plain']
  if (!allowedTypes.includes(file.type)) {
    showError('Formato file non supportato', 'Usa PDF, DOCX, DOC o TXT.')
    return
  }

  if (file.size > 10 * 1024 * 1024) { // 10MB
    showError('File troppo grande', 'Dimensione massima: 10MB.')
    return
  }

  uploading.value = true
  uploadProgress.value = 0

  try {
    // Step 1: Upload file
    const formData = new FormData()
    formData.append('cv_file', file)
    formData.append('analyze_skills', 'true')
    formData.append('auto_add_skills', 'false') // We'll let user choose

    // Simulate upload progress
    const uploadInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 15
      }
    }, 200)

    const response = await fetch(`/api/personnel/users/${props.user.id}/cv/upload`, {
      method: 'POST',
      body: formData,
      headers: {
        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      }
    })

    clearInterval(uploadInterval)
    uploadProgress.value = 100

    const data = await response.json()

    if (data.success) {
      uploading.value = false

      // Update user data first
      emit('user-updated', data.data)

      // Check if AI analysis is already present
      if (data.data.profile?.cv_analysis_data) {
        try {
          const analysisData = JSON.parse(data.data.profile.cv_analysis_data)
          if (analysisData.skills && analysisData.skills.length > 0) {
            showSuccess('CV caricato e analizzato con successo!', `Trovate ${analysisData.skills.length} competenze`)
          } else {
            showSuccess('CV caricato con successo!')
          }
        } catch (e) {
          showSuccess('CV caricato con successo!')
        }
      } else {
        showSuccess('CV caricato con successo!')
        // Note: AI analysis should happen on the backend during upload
        // If no analysis data is present, there might be an issue with the backend
        console.log('No AI analysis data found in response')
      }

      // Reset file input
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    } else {
      throw new Error(data.message || 'Errore durante il caricamento')
    }
  } catch (error) {
    console.error('Errore durante il caricamento del CV:', error)
    showError('Errore durante il caricamento del CV', error.message)
  } finally {
    uploading.value = false
    uploadProgress.value = 0
  }
}

const startAIAnalysis = async () => {
  analyzingWithAI.value = true
  aiAnalysisProgress.value = 0

  try {
    // Simulate AI analysis progress
    const analysisInterval = setInterval(() => {
      if (aiAnalysisProgress.value < 90) {
        aiAnalysisProgress.value += Math.random() * 10
      }
    }, 300)

    // Wait a bit to simulate AI processing
    await new Promise(resolve => setTimeout(resolve, 2000))

    clearInterval(analysisInterval)
    aiAnalysisProgress.value = 100

    showSuccess('Analisi AI completata!')

    // Refresh user data to get the analysis
    emit('user-updated')

  } catch (error) {
    console.error('Errore durante l\'analisi AI:', error)
    showError('Errore durante l\'analisi AI', error.message)
  } finally {
    analyzingWithAI.value = false
    aiAnalysisProgress.value = 0
  }
}

const downloadCV = async () => {
  try {
    const response = await fetch(`/api/personnel/users/${props.user.id}/cv/download`, {
      method: 'GET',
      credentials: 'include'
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `CV_${props.user.full_name}.pdf`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } else {
      const data = await response.json()
      throw new Error(data.message || 'Errore durante il download')
    }
  } catch (error) {
    console.error('Errore durante il download del CV:', error)
    alert('Errore durante il download del CV: ' + error.message)
  }
}

const deleteCV = async () => {
  if (!confirm('Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata.')) {
    return
  }

  try {
    const response = await fetch(`/api/personnel/users/${props.user.id}/cv`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      }
    })

    const data = await response.json()

    if (data.success) {
      // Update user data
      emit('user-updated', data.data)
      showSuccess('CV eliminato con successo')
    } else {
      throw new Error(data.message || 'Errore durante l\'eliminazione')
    }
  } catch (error) {
    console.error('Errore durante l\'eliminazione del CV:', error)
    showError('Errore durante l\'eliminazione del CV', error.message)
  }
}

const addSelectedSkills = async () => {
  if (selectedSkills.value.length === 0) return

  addingSkills.value = true

  try {
    // Prepare skills data with proper level conversion
    const skillsToAdd = selectedSkills.value.map(skillIndex => {
      const skill = cvAnalysis.value.skills[skillIndex]
      return {
        ...skill,
        level: convertLevelToNumber(skill.level)
      }
    })

    const response = await fetch(`/api/personnel/users/${props.user.id}/skills/from-cv`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
      },
      body: JSON.stringify({
        selected_skills: skillsToAdd
      })
    })

    const data = await response.json()

    if (data.success) {
      const { total_added, total_skipped } = data.data

      if (total_added > 0) {
        showSuccess(`Aggiunte ${total_added} competenze al profilo!`)
      }

      if (total_skipped > 0) {
        showInfo(`${total_skipped} competenze erano già presenti nel profilo`)
      }

      // Close modal and reset selection
      showSkillsModal.value = false
      selectedSkills.value = []

      // Emit event to refresh user data
      emit('user-updated')
    } else {
      throw new Error(data.message || 'Errore durante l\'aggiunta delle competenze')
    }
  } catch (error) {
    console.error('Errore durante l\'aggiunta delle competenze:', error)
    showError('Errore durante l\'aggiunta delle competenze', error.message)
  } finally {
    addingSkills.value = false
  }
}

const convertLevelToNumber = (level) => {
  const levelMap = {
    'beginner': 1,
    'intermediate': 3,
    'advanced': 4,
    'expert': 5
  }

  // If it's already a number, return it
  if (typeof level === 'number') {
    return Math.max(1, Math.min(5, level))
  }

  // If it's a string, convert it
  if (typeof level === 'string') {
    return levelMap[level.toLowerCase()] || 3
  }

  // Default to intermediate
  return 3
}

// Watch for upload state changes
watch(uploading, (newValue) => {
  if (!newValue) {
    uploadProgress.value = 0
  }
})

watch(analyzingWithAI, (newValue) => {
  if (!newValue) {
    aiAnalysisProgress.value = 0
  }
})
</script>

<style scoped>
/* Custom styles for CV tab */
</style>
