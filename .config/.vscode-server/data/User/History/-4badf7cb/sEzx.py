from datetime import datetime
from extensions import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from utils.permissions import ROLE_EMPLOYEE

# Association tables

project_team = db.Table('project_team',
    db.Column('project_id', db.Integer, db.<PERSON>('project.id'), primary_key=True),
    db.<PERSON>umn('user_id', db.Integer, db.Foreign<PERSON>ey('user.id'), primary_key=True),
    db.Column('role', db.String(50)),
    extend_existing=True
)

# User and Authentication models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(64), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256))
    first_name = db.Column(db.String(64))
    last_name = db.Column(db.String(64))
    role = db.Column(db.String(50), default=ROLE_EMPLOYEE, nullable=False)
    department = db.Column(db.String(64))  # DEPRECATED: use department_id instead
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)
    position = db.Column(db.String(64))
    hire_date = db.Column(db.Date)
    phone = db.Column(db.String(20))
    profile_image = db.Column(db.String(255))
    bio = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    dark_mode = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    # Password Reset Fields
    reset_token = db.Column(db.String(100), nullable=True, unique=True)
    reset_token_expiry = db.Column(db.DateTime, nullable=True)

    # Relationships
    timesheet_entries = db.relationship('Timesheet', backref='user', lazy='dynamic')
    projects = db.relationship('Project', secondary=project_team, backref='team_members')
    created_news = db.relationship('News', backref='author', lazy='dynamic')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f'<User {self.username}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    def to_dict(self):
        """Convert user object to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'role': self.role,
            'department': self.department,
            'department_id': self.department_id,
            'position': self.position,
            'phone': self.phone,
            'profile_image': self.profile_image,
            'bio': self.bio,
            'is_active': self.is_active,
            'dark_mode': self.dark_mode,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

    def can_view_project(self, project):
        """Verifica se l'utente può visualizzare un progetto"""
        from utils.permissions import user_has_permission, PERMISSION_VIEW_ALL_PROJECTS

        # Admin e manager possono vedere tutti i progetti
        if user_has_permission(self.role, PERMISSION_VIEW_ALL_PROJECTS):
            return True

        # Altrimenti, l'utente può vedere solo i progetti a cui è assegnato
        return self in project.team_members

    def has_permission(self, permission_name):
        """Verifica se l'utente ha un permesso specifico"""
        from utils.permissions import user_has_permission
        return user_has_permission(self.role, permission_name)

# Skill Management models
class Skill(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    category = db.Column(db.String(64))
    description = db.Column(db.Text)

    # Relationships
    user_skills = db.relationship('UserSkill', backref='skill', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Skill {self.name}>'

# HR Management models
class Department(db.Model):
    """Modello per la gestione dei dipartimenti aziendali con struttura gerarchica"""
    __tablename__ = 'departments'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    manager_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    parent_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)
    budget = db.Column(db.Float, default=0.0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')
    employees = db.relationship('User', foreign_keys='User.department_id', backref='department_obj', lazy='dynamic')
    subdepartments = db.relationship('Department', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')

    def __repr__(self):
        return f'<Department {self.name}>'

    @property
    def employee_count(self):
        """Conta il numero di dipendenti nel dipartimento"""
        return self.employees.filter_by(is_active=True).count()

    @property
    def full_path(self):
        """Restituisce il percorso completo del dipartimento (es: IT > Development > Frontend)"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name

class UserProfile(db.Model):
    """Profilo HR esteso per gli utenti"""
    __tablename__ = 'user_profiles'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    employee_id = db.Column(db.String(20), unique=True)
    job_title = db.Column(db.String(100))
    birth_date = db.Column(db.Date)
    address = db.Column(db.Text)
    emergency_contact_name = db.Column(db.String(100))
    emergency_contact_phone = db.Column(db.String(20))
    emergency_contact_relationship = db.Column(db.String(50))

    # Informazioni lavorative
    employment_type = db.Column(db.String(50), default='full_time')  # full_time, part_time, contractor, intern
    work_location = db.Column(db.String(100))  # office, remote, hybrid
    salary = db.Column(db.Float)  # Stipendio base
    salary_currency = db.Column(db.String(3), default='EUR')

    # Informazioni HR
    probation_end_date = db.Column(db.Date)
    contract_end_date = db.Column(db.Date)  # Per contratti a termine
    notice_period_days = db.Column(db.Integer, default=30)

    # Capacità lavorativa
    weekly_hours = db.Column(db.Float, default=40.0)  # Ore settimanali standard
    daily_hours = db.Column(db.Float, default=8.0)   # Ore giornaliere standard

    # CV e documenti
    current_cv_path = db.Column(db.String(255))  # Path del CV attuale
    cv_last_updated = db.Column(db.DateTime)
    cv_analysis_data = db.Column(db.Text)  # JSON con analisi AI del CV

    # Metadati
    profile_completion = db.Column(db.Float, default=0.0)  # Percentuale completamento profilo
    notes = db.Column(db.Text)  # Note HR riservate
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref=db.backref('profile', uselist=False))

    def __repr__(self):
        return f'<UserProfile {self.user.username if self.user else self.id}>'

    def calculate_completion(self):
        """Calcola la percentuale di completamento del profilo"""
        # Campi UserProfile
        profile_fields = [
            self.employee_id, self.job_title, self.birth_date, self.address,
            self.emergency_contact_name, self.emergency_contact_phone,
            self.employment_type, self.work_location, self.current_cv_path
        ]

        # Campi User base (se disponibili)
        user_fields = []
        if self.user:
            user_fields = [
                self.user.first_name, self.user.last_name, self.user.phone,
                self.user.position, self.user.bio
            ]

        # Combina tutti i campi
        all_fields = profile_fields + user_fields
        completed = sum(1 for field in all_fields if field)

        # Calcola percentuale e arrotonda
        if len(all_fields) > 0:
            self.profile_completion = round((completed / len(all_fields)) * 100)
        else:
            self.profile_completion = 0.0

        return self.profile_completion

class UserSkill(db.Model):
    """Modello per le competenze degli utenti con livelli di proficiency"""
    __tablename__ = 'user_skills_detailed'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    skill_id = db.Column(db.Integer, db.ForeignKey('skill.id'), nullable=False)
    proficiency_level = db.Column(db.Integer, default=1)  # 1-5 scale (1=Beginner, 5=Expert)
    years_experience = db.Column(db.Float, default=0.0)
    is_certified = db.Column(db.Boolean, default=False)
    certification_name = db.Column(db.String(100))
    certification_date = db.Column(db.Date)
    certification_expiry = db.Column(db.Date)
    self_assessed = db.Column(db.Boolean, default=True)  # Auto-valutazione vs valutazione manager
    manager_assessed = db.Column(db.Boolean, default=False)
    manager_assessment_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='detailed_skills')

    # Constraints
    __table_args__ = (
        db.UniqueConstraint('user_id', 'skill_id', name='unique_user_skill'),
    )

    def __repr__(self):
        return f'<UserSkill {self.user.username if self.user else self.user_id}:{self.skill.name if self.skill else self.skill_id} L{self.proficiency_level}>'

    @property
    def proficiency_label(self):
        """Restituisce l'etichetta del livello di competenza"""
        levels = {
            1: 'Principiante',
            2: 'Base',
            3: 'Intermedio',
            4: 'Avanzato',
            5: 'Esperto'
        }
        return levels.get(self.proficiency_level, 'Non definito')

    @property
    def is_certification_valid(self):
        """Verifica se la certificazione è ancora valida"""
        if not self.is_certified or not self.certification_expiry:
            return self.is_certified
        from datetime import date
        return date.today() <= self.certification_expiry

# Project Management models
class Project(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'))
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='planning')  # planning, active, completed, on-hold
    budget = db.Column(db.Float)
    expenses = db.Column(db.Float, default=0.0)
    project_type = db.Column(db.String(50), default='service')  # service, license, consulting, product, rd, internal
    is_billable = db.Column(db.Boolean, default=True)  # Progetto fatturabile?
    client_daily_rate = db.Column(db.Float)  # Tariffa giornaliera al cliente
    markup_percentage = db.Column(db.Float, default=0.0)  # Markup sui costi
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    tasks = db.relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')
    timesheet_entries = db.relationship('Timesheet', backref='project', lazy='dynamic')
    events = db.relationship('Event', backref='project', lazy='dynamic')
    client = db.relationship('Client', backref='projects')

    def __repr__(self):
        return f'<Project {self.name}>'

    @property
    def remaining_budget(self):
        return self.budget - self.expenses

class Task(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    assignee_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    status = db.Column(db.String(20), default='todo')  # todo, in-progress, review, done
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent
    start_date = db.Column(db.Date)  # Data di inizio pianificata
    due_date = db.Column(db.Date)    # Data di fine pianificata
    estimated_hours = db.Column(db.Float)  # Ore stimate per completare il task
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    assignee = db.relationship('User', backref='assigned_tasks')

    def __repr__(self):
        return f'<Task {self.name}>'

    @property
    def actual_hours(self):
        """Calcola le ore effettive lavorate sul task dai timesheet"""
        return sum(entry.hours for entry in self.timesheet_entries)

    @property
    def hours_variance(self):
        """Calcola la varianza tra ore stimate e ore effettive"""
        if not self.estimated_hours or self.actual_hours == 0:
            return None
        return self.actual_hours - self.estimated_hours

    @property
    def hours_efficiency(self):
        """Calcola l'efficienza in percentuale (stimate/effettive * 100)"""
        if not self.estimated_hours or self.actual_hours == 0:
            return None
        return (self.estimated_hours / self.actual_hours) * 100

    @property
    def duration_days(self):
        """Calcola la durata pianificata in giorni"""
        if not self.start_date or not self.due_date:
            return None
        return (self.due_date - self.start_date).days + 1

class Timesheet(db.Model):
    """Singola entry di timesheet - sarà rinominata in TimesheetEntry"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    task_id = db.Column(db.Integer, db.ForeignKey('task.id'))
    date = db.Column(db.Date, nullable=False)
    hours = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Nuovi campi per Task 3.1 + 4
    monthly_timesheet_id = db.Column(db.Integer, db.ForeignKey('monthly_timesheets.id'), nullable=True)
    billable = db.Column(db.Boolean, default=False)
    billing_rate = db.Column(db.Float, nullable=True)  # Tariffa oraria per questa entry
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)
    invoice_line_id = db.Column(db.Integer, db.ForeignKey('invoice_lines.id'), nullable=True)
    billing_status = db.Column(db.String(20), default='unbilled')  # unbilled, billed, non-billable

    # Relationships
    task = db.relationship('Task', backref='timesheet_entries')
    monthly_timesheet = db.relationship('MonthlyTimesheet', backref='entries')
    contract = db.relationship('Contract', backref='timesheet_entries')
    invoice_line = db.relationship('InvoiceLine', backref='timesheet_entries')

    def __repr__(self):
        return f'<Timesheet {self.user_id} - {self.date}>'


# Task 3.1 + 4: Timesheet Management & CRM Integration

class MonthlyTimesheet(db.Model):
    """Contenitore per approvazione mensile timesheet"""
    __tablename__ = 'monthly_timesheets'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    year = db.Column(db.Integer, nullable=False)
    month = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected
    submission_date = db.Column(db.DateTime, nullable=True)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='monthly_timesheets')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_monthly_timesheets')

    # Unique constraint per user/year/month
    __table_args__ = (db.UniqueConstraint('user_id', 'year', 'month', name='unique_user_month'),)

    def __repr__(self):
        return f'<MonthlyTimesheet {self.user_id} - {self.year}/{self.month}>'

    @property
    def total_hours(self):
        """Calcola il totale ore del mese"""
        return sum(entry.hours for entry in self.entries)

    @property
    def billable_hours(self):
        """Calcola le ore fatturabili del mese"""
        return sum(entry.hours for entry in self.entries if entry.billable)


class TimeOffRequest(db.Model):
    """Richieste di ferie, permessi e smartworking"""
    __tablename__ = 'time_off_requests'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    request_type = db.Column(db.String(20), nullable=False)  # vacation, leave, smartworking
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    notes = db.Column(db.Text)
    submission_date = db.Column(db.DateTime, default=datetime.utcnow)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='time_off_requests')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_time_off_requests')

    def __repr__(self):
        return f'<TimeOffRequest {self.user_id} - {self.request_type}: {self.start_date} to {self.end_date}>'

    @property
    def duration_days(self):
        """Calcola la durata in giorni della richiesta"""
        return (self.end_date - self.start_date).days + 1


class Event(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))
    start_time = db.Column(db.DateTime, nullable=False)
    end_time = db.Column(db.DateTime, nullable=False)
    location = db.Column(db.String(128))
    event_type = db.Column(db.String(20))  # meeting, deadline, milestone
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='created_events')

    def __repr__(self):
        return f'<Event {self.title}>'

# CRM models
class Client(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    industry = db.Column(db.String(64))
    description = db.Column(db.Text)
    website = db.Column(db.String(128))
    address = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships defined in Project model
    contacts = db.relationship('Contact', backref='client', lazy='dynamic', cascade='all, delete-orphan')
    proposals = db.relationship('Proposal', backref='client', lazy='dynamic')

    def __repr__(self):
        return f'<Client {self.name}>'

class Contact(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    first_name = db.Column(db.String(64), nullable=False)
    last_name = db.Column(db.String(64), nullable=False)
    position = db.Column(db.String(64))
    email = db.Column(db.String(120))
    phone = db.Column(db.String(20))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Contact {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

class Proposal(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    description = db.Column(db.Text)
    value = db.Column(db.Float)
    status = db.Column(db.String(20), default='draft')  # draft, sent, negotiating, accepted, rejected
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    sent_date = db.Column(db.Date)
    expiry_date = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = db.relationship('User', backref='created_proposals')

    def __repr__(self):
        return f'<Proposal {self.title}>'


# Task 4: CRM & Billing Integration

class Contract(db.Model):
    """Contratti con clienti per fatturazione"""
    __tablename__ = 'contracts'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    contract_number = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    contract_type = db.Column(db.String(20), default='hourly')  # hourly, fixed, retainer
    hourly_rate = db.Column(db.Float, nullable=True)  # Tariffa base oraria
    budget_hours = db.Column(db.Float, nullable=True)
    budget_amount = db.Column(db.Float, nullable=True)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=True)
    status = db.Column(db.String(20), default='active')  # active, completed, cancelled
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    client = db.relationship('Client', backref='contracts')

    def __repr__(self):
        return f'<Contract {self.contract_number} - {self.client_id}>'


class Invoice(db.Model):
    """Fatture generate per periodo"""
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)

    # Periodo di fatturazione
    billing_period_start = db.Column(db.Date, nullable=False)
    billing_period_end = db.Column(db.Date, nullable=False)

    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, sent, paid, cancelled

    # Totali
    subtotal = db.Column(db.Float, default=0.0)
    tax_rate = db.Column(db.Float, default=0.22)  # 22% IVA default
    tax_amount = db.Column(db.Float, default=0.0)
    total_amount = db.Column(db.Float, default=0.0)

    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    client = db.relationship('Client', backref='invoices')
    lines = db.relationship('InvoiceLine', backref='invoice', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Invoice {self.invoice_number} - {self.client_id}>'


class InvoiceLine(db.Model):
    """Righe fattura raggruppate per progetto/contratto"""
    __tablename__ = 'invoice_lines'

    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoices.id'), nullable=False)

    # Raggruppa per progetto/contratto
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)

    description = db.Column(db.Text, nullable=False)  # "Sviluppo CRM - Gennaio 2024"
    total_hours = db.Column(db.Float, nullable=False)  # Somma ore del periodo
    hourly_rate = db.Column(db.Float, nullable=False)
    total_amount = db.Column(db.Float, nullable=False)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='invoice_lines')
    contract = db.relationship('Contract', backref='invoice_lines')

    def __repr__(self):
        return f'<InvoiceLine {self.invoice_id} - {self.project_id}: {self.total_hours}h>'


# Product Catalog models
class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(64))
    price = db.Column(db.Float)
    status = db.Column(db.String(20), default='active')  # active, discontinued
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Product {self.name}>'

class Service(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(64))
    hourly_rate = db.Column(db.Float)
    status = db.Column(db.String(20), default='active')  # active, discontinued
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Service {self.name}>'

# Performance Management models
class KPI(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(64))
    target_value = db.Column(db.Float)
    current_value = db.Column(db.Float, default=0.0)
    unit = db.Column(db.String(20))
    frequency = db.Column(db.String(20))  # daily, weekly, monthly, quarterly, annually
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<KPI {self.name}>'

    @property
    def progress(self):
        if self.target_value and self.target_value > 0 and self.current_value is not None:
            return (self.current_value / self.target_value) * 100
        return 0

class BusinessProcess(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    status = db.Column(db.String(20), default='active')  # draft, active, under_review, archived
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    owner = db.relationship('User', backref='owned_processes')
    steps = db.relationship('ProcessStep', backref='process', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<BusinessProcess {self.name}>'

class ProcessStep(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    process_id = db.Column(db.Integer, db.ForeignKey('business_process.id'), nullable=False)
    name = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    order = db.Column(db.Integer, nullable=False)
    responsible_role = db.Column(db.String(64))
    estimated_time = db.Column(db.Float)  # in hours

    def __repr__(self):
        return f'<ProcessStep {self.name}>'

# Communication models
class News(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    content = db.Column(db.Text, nullable=False)
    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    image_url = db.Column(db.String(255))
    is_published = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<News {self.title}>'

class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    category = db.Column(db.String(64))
    file_path = db.Column(db.String(255), nullable=False)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    version = db.Column(db.String(20), default='1.0')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    uploader = db.relationship('User', backref='uploaded_documents')

    def __repr__(self):
        return f'<Document {self.title}>'

class Regulation(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    content = db.Column(db.Text, nullable=False)
    category = db.Column(db.String(64))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Regulation {self.title}>'

# Funding and Grant models
class FundingOpportunity(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    source = db.Column(db.String(128))
    amount = db.Column(db.Float)
    application_deadline = db.Column(db.Date)
    status = db.Column(db.String(20), default='open')  # open, applied, awarded, rejected, closed
    requirements = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    applications = db.relationship('FundingApplication', backref='opportunity', lazy='dynamic')

    def __repr__(self):
        return f'<FundingOpportunity {self.title}>'

class FundingApplication(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    opportunity_id = db.Column(db.Integer, db.ForeignKey('funding_opportunity.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))
    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected
    submission_date = db.Column(db.Date)
    notes = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='funding_applications')
    creator = db.relationship('User', backref='created_applications')
    expenses = db.relationship('FundingExpense', backref='application', lazy='dynamic')

    def __repr__(self):
        return f'<FundingApplication {self.id}>'

class FundingExpense(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    application_id = db.Column(db.Integer, db.ForeignKey('funding_application.id'), nullable=False)
    description = db.Column(db.String(255), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    date = db.Column(db.Date, nullable=False)
    category = db.Column(db.String(64))
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    receipt_path = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<FundingExpense {self.description}>'

# Startup Resources models
class StartupResource(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(128), nullable=False)
    description = db.Column(db.Text)
    resource_type = db.Column(db.String(64))  # incentive, tax_benefit, mentorship, funding, other
    link = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<StartupResource {self.title}>'

# Notification system
class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(128), nullable=False)
    message = db.Column(db.Text, nullable=False)
    link = db.Column(db.String(255))
    type = db.Column(db.String(50), default='info') # info, success, warning, danger
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='notifications')

    def __repr__(self):
        return f'<Notification {self.id} for {self.user_id}>'

class AdminLog(db.Model):
    """Registra le azioni amministrative sugli utenti."""
    __tablename__ = 'admin_logs'

    id = db.Column(db.Integer, primary_key=True)
    admin_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(255), nullable=False)
    target_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relazioni
    admin = db.relationship('User', foreign_keys=[admin_id], backref='admin_logs')
    target_user = db.relationship('User', foreign_keys=[target_user_id], backref='target_logs')

    def __repr__(self):
        return f'<AdminLog {self.id}: {self.action}>'

class ProjectResource(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    allocation_percentage = db.Column(db.Integer, default=100)
    role = db.Column(db.String(50))
    # Relationships opzionali
    project = db.relationship('Project', backref='project_resources')
    user = db.relationship('User', backref='resource_assignments')

class TaskDependency(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=False)
    depends_on_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=False)
    # Relationships opzionali
    task = db.relationship('Task', foreign_keys=[task_id], backref='dependencies')
    depends_on = db.relationship('Task', foreign_keys=[depends_on_id])

class ProjectKPI(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    kpi_id = db.Column(db.Integer, db.ForeignKey('kpi.id'), nullable=False)
    target_value = db.Column(db.Float)
    current_value = db.Column(db.Float)
    # Relationships opzionali
    project = db.relationship('Project', backref='kpis')
    kpi = db.relationship('KPI', backref='project_links')

    @property
    def progress(self):
        if self.target_value and self.target_value > 0 and self.current_value is not None:
            return (self.current_value / self.target_value) * 100
        return 0

# Cost Management models
class PersonnelRate(db.Model):
    """Storico tariffe giornaliere del personale"""
    __tablename__ = 'personnel_rates'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    daily_rate = db.Column(db.Float, nullable=False)  # Tariffa giornaliera
    valid_from = db.Column(db.Date, nullable=False)  # Data inizio validità
    valid_to = db.Column(db.Date, nullable=True)  # Data fine (NULL = attuale)
    currency = db.Column(db.String(3), default='EUR')
    notes = db.Column(db.String(255))  # Es: "Aumento annuale", "Promozione"
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='rate_history')

    def __repr__(self):
        return f'<PersonnelRate {self.user_id}: €{self.daily_rate}/day>'

    @property
    def is_current(self):
        """Verifica se questa tariffa è attualmente valida"""
        from datetime import date
        today = date.today()
        return (self.valid_from <= today and
                (self.valid_to is None or self.valid_to >= today))

class ProjectExpense(db.Model):
    """Spese di progetto (non personale)"""
    __tablename__ = 'project_expenses'

    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # Chi ha sostenuto
    category = db.Column(db.String(50), nullable=False)  # licenses, travel, meals, equipment, external, other
    description = db.Column(db.String(255), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    billing_type = db.Column(db.String(20), default='billable')  # billable, non-billable, reimbursable
    date = db.Column(db.Date, nullable=False)
    receipt_path = db.Column(db.String(255))  # Allegato ricevuta
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='expenses_detailed')
    user = db.relationship('User', backref='submitted_expenses')

    def __repr__(self):
        return f'<ProjectExpense {self.project_id}: €{self.amount} ({self.category})>'

class ProjectKPITemplate(db.Model):
    """Template KPI di default per tipologie progetto (solo Admin)"""
    __tablename__ = 'project_kpi_templates'

    id = db.Column(db.Integer, primary_key=True)
    project_type = db.Column(db.String(50), nullable=False)  # service, license, consulting, product, rd, internal
    kpi_name = db.Column(db.String(100), nullable=False)  # margin_percentage, utilization_rate, cost_per_hour, cost_revenue_ratio
    target_min = db.Column(db.Float)  # Soglia minima accettabile
    target_max = db.Column(db.Float)  # Soglia ottimale
    warning_threshold = db.Column(db.Float)  # Soglia di warning
    unit = db.Column(db.String(10), default='%')  # %, €, ratio, giorni
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<KPITemplate {self.project_type}.{self.kpi_name}>'

    @property
    def display_name(self):
        """Nome leggibile del KPI"""
        names = {
            'margin_percentage': 'Margine Netto',
            'utilization_rate': 'Utilization Rate',
            'cost_per_hour': 'Costo per Ora',
            'cost_revenue_ratio': 'Rapporto C/R'
        }
        return names.get(self.kpi_name, self.kpi_name)

class ProjectKPITarget(db.Model):
    """KPI target personalizzati per singolo progetto (Admin + Project Owner)"""
    __tablename__ = 'project_kpi_targets'

    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    kpi_name = db.Column(db.String(100), nullable=False)
    target_value = db.Column(db.Float)  # Valore target personalizzato
    warning_threshold = db.Column(db.Float)  # Soglia warning personalizzata
    custom_description = db.Column(db.Text)  # Descrizione personalizzata
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    project = db.relationship('Project', backref='kpi_targets')
    creator = db.relationship('User', backref='created_kpi_targets')

    def __repr__(self):
        return f'<ProjectKPITarget {self.project_id}.{self.kpi_name}: {self.target_value}>'

    @property
    def display_name(self):
        """Nome leggibile del KPI"""
        names = {
            'margin_percentage': 'Margine Netto',
            'utilization_rate': 'Utilization Rate',
            'cost_per_hour': 'Costo per Ora',
            'cost_revenue_ratio': 'Rapporto C/R'
        }
        return names.get(self.kpi_name, self.kpi_name)