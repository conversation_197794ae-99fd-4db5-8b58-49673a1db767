<template>
  <div class="project-overview">
    <div v-if="loading" class="animate-pulse space-y-4">
      <div class="h-4 bg-gray-200 rounded w-3/4"></div>
      <div class="h-4 bg-gray-200 rounded w-1/2"></div>
      <div class="h-32 bg-gray-200 rounded"></div>
    </div>

    <div v-else-if="project" class="space-y-6">
      <!-- Project Description -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Descrizione Progetto</h3>
        <p v-if="project.description" class="text-gray-600">{{ project.description }}</p>
        <p v-else class="text-gray-400 italic">Nessuna descrizione disponibile</p>
      </div>

      <!-- Contract Info -->
      <div v-if="project.contract" class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Contratto Collegato</h3>
          <router-link
            :to="`/app/crm/contracts/${project.contract.id}`"
            class="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            Gestisci Contratto →
          </router-link>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <dt class="text-sm text-gray-500">Numero Contratto</dt>
            <dd class="text-sm font-medium text-gray-900">{{ project.contract.contract_number }}</dd>
          </div>
          <div>
            <dt class="text-sm text-gray-500">Tipo</dt>
            <dd class="text-sm font-medium text-gray-900">{{ formatContractType(project.contract.contract_type) }}</dd>
          </div>
          <div>
            <dt class="text-sm text-gray-500">Tariffa Oraria</dt>
            <dd class="text-sm font-medium text-gray-900">{{ formatCurrency(project.contract.hourly_rate) }}/h</dd>
          </div>
          <div>
            <dt class="text-sm text-gray-500">Stato</dt>
            <dd>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getContractStatusClass(project.contract.status)">
                {{ formatContractStatus(project.contract.status) }}
              </span>
            </dd>
          </div>
        </div>

        <!-- Contract Budget Info -->
        <div v-if="project.contract.budget_hours || project.contract.budget_amount" class="mt-4 pt-4 border-t border-gray-200">
          <div class="grid grid-cols-2 gap-4">
            <div v-if="project.contract.budget_hours">
              <dt class="text-sm text-gray-500">Budget Ore</dt>
              <dd class="text-sm font-medium text-gray-900">{{ project.contract.budget_hours }}h</dd>
            </div>
            <div v-if="project.contract.budget_amount">
              <dt class="text-sm text-gray-500">Budget Importo</dt>
              <dd class="text-sm font-medium text-gray-900">{{ formatCurrency(project.contract.budget_amount) }}</dd>
            </div>
          </div>
        </div>
      </div>

      <!-- Project Stats -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Task Totali</dt>
                <dd class="text-lg font-medium text-gray-900">{{ project.task_count || 0 }}</dd>
              </dl>
            </div>
          </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Task Completati</dt>
                <dd class="text-lg font-medium text-gray-900">{{ project.completed_tasks || 0 }}</dd>
              </dl>
            </div>
          </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Membri Team</dt>
                <dd class="text-lg font-medium text-gray-900">{{ project.team_count || 0 }}</dd>
              </dl>
            </div>
          </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">Budget</dt>
                <dd class="text-lg font-medium text-gray-900">{{ formatCurrency(project.budget) }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Progresso Progetto</h3>
        <div class="w-full bg-gray-200 rounded-full h-2.5">
          <div
            class="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
            :style="{ width: `${projectProgress}%` }"
          ></div>
        </div>
        <p class="text-sm text-gray-500 mt-2">{{ projectProgress }}% completato</p>
      </div>

      <!-- Budget vs Spese Chart -->
      <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Budget vs Spese</h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Budget Totale</span>
            <span class="text-sm font-medium">{{ formatCurrency(project.budget) }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div
              class="bg-blue-600 h-3 rounded-full transition-all duration-300"
              :style="{ width: '100%' }"
            ></div>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-600">Spese Sostenute</span>
            <span class="text-sm font-medium">{{ formatCurrency(estimatedExpenses) }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-3">
            <div
              class="h-3 rounded-full transition-all duration-300"
              :class="expenseProgressColor"
              :style="{ width: expensePercentage + '%' }"
            ></div>
          </div>
          <div class="flex justify-between items-center text-sm">
            <span class="text-gray-600">Rimanente</span>
            <span class="font-medium" :class="remainingBudgetTextColor">{{ formatCurrency(remainingBudget) }}</span>
          </div>
        </div>
      </div>

      <!-- Team Members -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Team Members</h3>
          <button class="text-sm text-blue-600 hover:text-blue-800">Visualizza tutti</button>
        </div>
        <div class="space-y-3">
          <div v-for="member in teamMembers" :key="member.id" class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <img
                v-if="member.profile_image"
                :src="member.profile_image"
                :alt="member.full_name"
                class="w-8 h-8 rounded-full"
              >
              <div v-else class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <span class="text-xs font-medium text-gray-600">{{ getInitials(member.full_name) }}</span>
              </div>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">{{ member.full_name }}</p>
              <p class="text-xs text-gray-500">{{ member.role || 'Team Member' }}</p>
            </div>
            <div class="text-right">
              <p class="text-xs text-gray-500">{{ member.hours_worked || 0 }}h</p>
            </div>
          </div>
          <div v-if="teamMembers.length === 0" class="text-center py-4">
            <p class="text-gray-500">Nessun membro del team assegnato</p>
          </div>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Attività Recenti</h3>
          <button class="text-sm text-blue-600 hover:text-blue-800">Visualizza tutte</button>
        </div>
        <div class="space-y-3">
          <div v-for="activity in recentTasks" :key="activity.id" class="flex items-start space-x-3">
            <div class="flex-shrink-0">
              <div class="w-2 h-2 rounded-full mt-2" :class="getActivityColor(activity.type)"></div>
            </div>
            <div class="flex-1">
              <p class="text-sm text-gray-900">{{ activity.description }}</p>
              <div class="flex items-center space-x-2 mt-1">
                <p class="text-xs text-gray-500">{{ formatDate(activity.created_at) }}</p>
                <span class="text-xs text-gray-400">•</span>
                <p class="text-xs text-gray-500">{{ activity.user_name }}</p>
              </div>
            </div>
          </div>
          <div v-if="recentTasks.length === 0" class="text-center py-4">
            <p class="text-gray-500">Nessuna attività recente</p>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="text-center py-8">
      <p class="text-gray-500">Progetto non trovato</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  project: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Computed
const projectProgress = computed(() => {
  if (!props.project || !props.project.task_count) return 0
  const completed = props.project.completed_tasks || 0
  const total = props.project.task_count || 1
  return Math.round((completed / total) * 100)
})

const teamMembers = computed(() => {
  return props.project?.team_members || []
})

const estimatedExpenses = computed(() => {
  // Usa le spese reali se disponibili, altrimenti calcola da ore lavorate
  if (props.project?.expenses) {
    return props.project.expenses
  }
  // Calcolo da ore totali con tariffa media
  const totalHours = props.project?.total_hours || 0
  const averageHourlyRate = props.project?.client_daily_rate ? props.project.client_daily_rate / 8 : 50
  return totalHours * averageHourlyRate
})

const remainingBudget = computed(() => {
  const budget = props.project?.budget || 0
  return budget - estimatedExpenses.value
})

const expensePercentage = computed(() => {
  const budget = props.project?.budget || 1
  return Math.min(Math.round((estimatedExpenses.value / budget) * 100), 100)
})

const expenseProgressColor = computed(() => {
  const percentage = expensePercentage.value
  if (percentage >= 90) return 'bg-red-600'
  if (percentage >= 75) return 'bg-yellow-600'
  return 'bg-green-600'
})

const remainingBudgetTextColor = computed(() => {
  const remaining = remainingBudget.value
  if (remaining < 0) return 'text-red-600'
  if (remaining < (props.project?.budget || 0) * 0.1) return 'text-yellow-600'
  return 'text-green-600'
})

// Task recenti (ultimi 5 task modificati)
const recentTasks = computed(() => {
  if (!props.project?.tasks) return []
  return [...props.project.tasks]
    .sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at))
    .slice(0, 5)
    .map(task => ({
      id: task.id,
      description: `Task "${task.name}" ${getTaskStatusText(task.status)}`,
      created_at: task.updated_at,
      user_name: task.assignee?.full_name || 'Non assegnato',
      type: getTaskActivityType(task.status)
    }))
})

const getTaskStatusText = (status) => {
  const statusMap = {
    'todo': 'creato',
    'in-progress': 'in corso',
    'review': 'in revisione', 
    'done': 'completato'
  }
  return statusMap[status] || status
}

const getTaskActivityType = (status) => {
  const typeMap = {
    'todo': 'task_created',
    'in-progress': 'task_updated',
    'review': 'task_updated',
    'done': 'task_completed'
  }
  return typeMap[status] || 'task_updated'
}

// Methods
const formatCurrency = (amount) => {
  if (!amount) return 'Non specificato'
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('it-IT', {
    day: 'numeric',
    month: 'short',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getInitials = (fullName) => {
  if (!fullName) return '??'
  return fullName
    .split(' ')
    .map(name => name.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('')
}

const getActivityColor = (activityType) => {
  const colors = {
    task_created: 'bg-blue-600',
    task_completed: 'bg-green-600',
    task_updated: 'bg-yellow-600',
    comment_added: 'bg-purple-600',
    file_uploaded: 'bg-indigo-600',
    member_added: 'bg-pink-600',
    default: 'bg-gray-600'
  }
  return colors[activityType] || colors.default
}
</script>

<style scoped>
.project-overview {
  @apply space-y-6;
}
</style>
