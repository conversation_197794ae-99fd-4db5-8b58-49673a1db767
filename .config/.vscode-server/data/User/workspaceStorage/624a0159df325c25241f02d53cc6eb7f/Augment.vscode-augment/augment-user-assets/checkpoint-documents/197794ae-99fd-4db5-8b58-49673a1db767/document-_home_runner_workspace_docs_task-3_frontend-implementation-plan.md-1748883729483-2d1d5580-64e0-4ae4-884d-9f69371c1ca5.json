{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task-3/frontend-implementation-plan.md"}, "originalCode": "# 🎯 **PIANO IMPLEMENTAZIONE FRONTEND TIMESHEET + CRM**\n\n## 📋 **OVERVIEW ARCHITETTURA**\n\n### **🎯 OBIETTIVI:**\n- Menu Timesheet separato con workflow approvazioni centralizzato\n- Bulk approval con AI anomaly detection\n- Sistema notifiche in-app (navbar)\n- Gerarchia manager per approvazioni\n- Integrazione CRM completa\n\n---\n\n## 🚀 **FASE 1: STRUTTURA BASE TIMESHEET**\n\n### **1.1 Nuove Rotte Router**\n\n**File:** `frontend/src/router/index.js`\n\n```javascript\n// Timesheet routes - Menu separato\n{\n  path: '/app/timesheet',\n  meta: { requiresAuth: true },\n  children: [\n    {\n      path: '',\n      name: 'timesheet-dashboard',\n      component: () => import('@/views/timesheet/TimesheetDashboard.vue'),\n      meta: { requiredPermission: 'view_timesheet' }\n    },\n    {\n      path: 'entries',\n      name: 'timesheet-entries',\n      component: () => import('@/views/timesheet/TimesheetEntries.vue'),\n      meta: { requiredPermission: 'manage_timesheet' }\n    },\n    {\n      path: 'approvals',\n      name: 'timesheet-approvals',\n      component: () => import('@/views/timesheet/TimesheetApprovals.vue'),\n      meta: { requiredPermission: 'approve_timesheets' }\n    },\n    {\n      path: 'time-off',\n      name: 'time-off-requests',\n      component: () => import('@/views/timesheet/TimeOffRequests.vue'),\n      meta: { requiredPermission: 'manage_time_off' }\n    },\n    {\n      path: 'reports',\n      name: 'timesheet-reports',\n      component: () => import('@/views/timesheet/TimesheetReports.vue'),\n      meta: { requiredPermission: 'view_timesheet_reports' }\n    }\n  ]\n}\n```\n\n### **1.2 Aggiornamento Sidebar Navigation**\n\n**File:** `frontend/src/components/layout/SidebarNavigation.vue`\n\n```javascript\n// Aggiungere menu Timesheet separato\n{\n  name: 'Timesheet',\n  icon: 'clock',\n  children: [\n    { \n      name: 'Dashboard', \n      path: '/app/timesheet', \n      icon: 'chart-bar',\n      permission: 'view_timesheet'\n    },\n    { \n      name: 'Registra Ore', \n      path: '/app/timesheet/entries', \n      icon: 'plus-circle',\n      permission: 'manage_timesheet'\n    },\n    { \n      name: 'Approvazioni', \n      path: '/app/timesheet/approvals', \n      icon: 'check-circle',\n      permission: 'approve_timesheets'\n    },\n    { \n      name: 'Ferie/Permessi', \n      path: '/app/timesheet/time-off', \n      icon: 'calendar',\n      permission: 'manage_time_off'\n    },\n    { \n      name: 'Report', \n      path: '/app/timesheet/reports', \n      icon: 'document-report',\n      permission: 'view_timesheet_reports'\n    }\n  ]\n}\n```\n\n---\n\n## 🎯 **FASE 2: COMPONENTI TIMESHEET CORE**\n\n### **2.1 Timesheet Dashboard**\n\n**File:** `frontend/src/views/timesheet/TimesheetDashboard.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-dashboard\">\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <h1 class=\"text-2xl font-bold text-gray-900\">Dashboard Timesheet</h1>\n      <p class=\"text-gray-600\">Panoramica ore e approvazioni</p>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard \n        title=\"Ore Questa Settimana\" \n        :value=\"stats.weeklyHours\" \n        color=\"blue\"\n        icon=\"clock\"\n      />\n      <StatCard \n        title=\"Ore Questo Mese\" \n        :value=\"stats.monthlyHours\" \n        color=\"green\"\n        icon=\"calendar\"\n      />\n      <StatCard \n        title=\"Da Approvare\" \n        :value=\"stats.pendingApprovals\" \n        color=\"yellow\"\n        icon=\"exclamation\"\n        v-if=\"canApprove\"\n      />\n      <StatCard \n        title=\"Efficienza\" \n        :value=\"stats.efficiency + '%'\" \n        color=\"purple\"\n        icon=\"trending-up\"\n      />\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n      <!-- Registrazione Rapida -->\n      <QuickTimeEntry @saved=\"refreshStats\" />\n      \n      <!-- Calendario Ore -->\n      <TimesheetCalendar :month=\"currentMonth\" />\n    </div>\n\n    <!-- Recent Activity & Notifications -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <RecentTimesheetActivity />\n      <PendingApprovals v-if=\"canApprove\" />\n    </div>\n  </div>\n</template>\n```\n\n### **2.2 Timesheet Entries**\n\n**File:** `frontend/src/views/timesheet/TimesheetEntries.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-entries\">\n    <!-- Header con filtri -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h1 class=\"text-2xl font-bold\">Registrazione Ore</h1>\n        <button @click=\"showQuickEntry = true\" \n                class=\"btn-primary\">\n          <PlusIcon class=\"w-4 h-4 mr-2\" />\n          Registra Ore\n        </button>\n      </div>\n      \n      <!-- Filtri -->\n      <div class=\"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <DateRangePicker v-model=\"dateRange\" />\n        <ProjectSelector v-model=\"selectedProject\" />\n        <StatusFilter v-model=\"selectedStatus\" />\n        <SearchInput v-model=\"searchQuery\" placeholder=\"Cerca...\" />\n      </div>\n    </div>\n\n    <!-- Lista Entries -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <TimesheetEntriesTable \n        :entries=\"filteredEntries\"\n        :loading=\"loading\"\n        @edit=\"editEntry\"\n        @delete=\"deleteEntry\"\n        @bulk-action=\"handleBulkAction\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <TimesheetEntryModal \n      v-if=\"showQuickEntry || editingEntry\"\n      :entry=\"editingEntry\"\n      @close=\"closeModal\"\n      @saved=\"handleEntrySaved\"\n    />\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 3: SISTEMA APPROVAZIONI CON AI**\n\n### **3.1 Timesheet Approvals - Vista Manager**\n\n**File:** `frontend/src/views/timesheet/TimesheetApprovals.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-approvals\">\n    <!-- Header con AI Analysis -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold\">Approvazioni Timesheet</h1>\n          <p class=\"text-gray-600\">Gestisci approvazioni con assistenza AI</p>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <!-- AI Anomaly Detection -->\n          <button @click=\"runAnomalyDetection\" \n                  :disabled=\"analyzingAnomalies\"\n                  class=\"btn-purple\">\n            <BeakerIcon class=\"w-4 h-4 mr-2\" />\n            {{ analyzingAnomalies ? 'Analizzando...' : 'Rileva Anomalie' }}\n          </button>\n          \n          <!-- Bulk Actions -->\n          <div class=\"relative\" v-if=\"selectedTimesheets.length\">\n            <button @click=\"showBulkMenu = !showBulkMenu\" \n                    class=\"btn-secondary\">\n              Azioni Multiple ({{ selectedTimesheets.length }})\n            </button>\n            <BulkActionsMenu \n              v-if=\"showBulkMenu\"\n              @approve-all=\"bulkApprove\"\n              @reject-all=\"bulkReject\"\n              @close=\"showBulkMenu = false\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- AI Anomalies Alert -->\n      <div v-if=\"anomalies.length\" \n           class=\"mt-4 bg-red-50 border border-red-200 rounded-lg p-4\">\n        <div class=\"flex items-start\">\n          <ExclamationTriangleIcon class=\"w-5 h-5 text-red-400 mt-0.5\" />\n          <div class=\"ml-3\">\n            <h3 class=\"text-sm font-medium text-red-800\">\n              Anomalie Rilevate ({{ anomalies.length }})\n            </h3>\n            <div class=\"mt-2 space-y-1\">\n              <div v-for=\"anomaly in anomalies\" :key=\"anomaly.id\"\n                   class=\"text-sm text-red-700\">\n                • {{ anomaly.user_name }}: {{ anomaly.description }}\n                <button @click=\"viewAnomalyDetails(anomaly)\"\n                        class=\"ml-2 text-red-600 underline\">\n                  Dettagli\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard title=\"Da Approvare\" :value=\"stats.pending\" color=\"yellow\" />\n      <StatCard title=\"Approvati\" :value=\"stats.approved\" color=\"green\" />\n      <StatCard title=\"Con Anomalie\" :value=\"anomalies.length\" color=\"red\" />\n      <StatCard title=\"Ore Totali\" :value=\"stats.totalHours\" color=\"blue\" />\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <MonthSelector v-model=\"selectedMonth\" />\n        <StatusFilter v-model=\"selectedStatus\" />\n        <TeamMemberFilter v-model=\"selectedMember\" :members=\"teamMembers\" />\n        <AnomalyFilter v-model=\"showOnlyAnomalies\" />\n        <SearchInput v-model=\"searchQuery\" />\n      </div>\n    </div>\n\n    <!-- Lista Timesheet -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <TimesheetApprovalsTable \n        :timesheets=\"filteredTimesheets\"\n        :loading=\"loading\"\n        :selected=\"selectedTimesheets\"\n        :anomalies=\"anomalies\"\n        @select=\"handleSelection\"\n        @view-details=\"viewDetails\"\n        @approve=\"approveTimesheet\"\n        @reject=\"rejectTimesheet\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <TimesheetDetailModal \n      v-if=\"showDetailModal\"\n      :timesheet=\"selectedTimesheet\"\n      :anomaly=\"getAnomalyForTimesheet(selectedTimesheet)\"\n      @close=\"showDetailModal = false\"\n      @approve=\"approveTimesheet\"\n      @reject=\"rejectTimesheet\"\n    />\n\n    <AnomalyDetailModal\n      v-if=\"showAnomalyModal\"\n      :anomaly=\"selectedAnomaly\"\n      @close=\"showAnomalyModal = false\"\n    />\n  </div>\n</template>\n```\n\n### **3.2 AI Anomaly Detection Service**\n\n**File:** `frontend/src/services/aiAnomalyService.js`\n\n```javascript\nexport const aiAnomalyService = {\n  // Rileva anomalie nei timesheet\n  detectAnomalies: async (filters = {}) => {\n    const response = await apiClient.post('/ai-timesheet/detect-anomalies', {\n      month: filters.month,\n      year: filters.year,\n      team_members: filters.teamMembers,\n      analysis_types: [\n        'overtime_patterns',\n        'weekend_work',\n        'unusual_hours',\n        'project_switching',\n        'productivity_drops',\n        'duplicate_entries'\n      ]\n    })\n    return response.data\n  },\n\n  // Analizza pattern specifico utente\n  analyzeUserPattern: async (userId, timeRange) => {\n    const response = await apiClient.post(`/ai-timesheet/analyze-user/${userId}`, {\n      start_date: timeRange.start,\n      end_date: timeRange.end,\n      include_recommendations: true\n    })\n    return response.data\n  },\n\n  // Suggerimenti approvazione\n  getApprovalSuggestions: async (timesheetId) => {\n    const response = await apiClient.get(`/ai-timesheet/approval-suggestions/${timesheetId}`)\n    return response.data\n  }\n}\n```\n\n---\n\n## 🎯 **FASE 4: SISTEMA NOTIFICHE IN-APP**\n\n### **4.1 Notification Service**\n\n**File:** `frontend/src/services/notificationService.js`\n\n```javascript\nexport const notificationService = {\n  // Ottieni notifiche utente\n  getUserNotifications: async (filters = {}) => {\n    const response = await apiClient.get('/notifications/', {\n      params: {\n        unread_only: filters.unreadOnly || false,\n        type: filters.type,\n        limit: filters.limit || 50\n      }\n    })\n    return response.data\n  },\n\n  // Marca come letta\n  markAsRead: async (notificationId) => {\n    const response = await apiClient.put(`/notifications/${notificationId}/read`)\n    return response.data\n  },\n\n  // Marca tutte come lette\n  markAllAsRead: async () => {\n    const response = await apiClient.put('/notifications/mark-all-read')\n    return response.data\n  },\n\n  // Elimina notifica\n  deleteNotification: async (notificationId) => {\n    const response = await apiClient.delete(`/notifications/${notificationId}`)\n    return response.data\n  }\n}\n```\n\n### **4.2 Notification Store**\n\n**File:** `frontend/src/stores/notifications.js`\n\n```javascript\nimport { defineStore } from 'pinia'\nimport { notificationService } from '@/services/notificationService'\n\nexport const useNotificationStore = defineStore('notifications', {\n  state: () => ({\n    notifications: [],\n    unreadCount: 0,\n    loading: false,\n    polling: null\n  }),\n\n  getters: {\n    unreadNotifications: (state) => \n      state.notifications.filter(n => !n.read),\n    \n    timesheetNotifications: (state) =>\n      state.notifications.filter(n => n.type.includes('timesheet')),\n    \n    approvalNotifications: (state) =>\n      state.notifications.filter(n => n.type.includes('approval'))\n  },\n\n  actions: {\n    async fetchNotifications() {\n      this.loading = true\n      try {\n        const result = await notificationService.getUserNotifications()\n        this.notifications = result.data.notifications\n        this.unreadCount = result.data.unread_count\n      } catch (error) {\n        console.error('Error fetching notifications:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async markAsRead(notificationId) {\n      try {\n        await notificationService.markAsRead(notificationId)\n        const notification = this.notifications.find(n => n.id === notificationId)\n        if (notification) {\n          notification.read = true\n          this.unreadCount = Math.max(0, this.unreadCount - 1)\n        }\n      } catch (error) {\n        console.error('Error marking notification as read:', error)\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        await notificationService.markAllAsRead()\n        this.notifications.forEach(n => n.read = true)\n        this.unreadCount = 0\n      } catch (error) {\n        console.error('Error marking all notifications as read:', error)\n      }\n    },\n\n    startPolling() {\n      this.polling = setInterval(() => {\n        this.fetchNotifications()\n      }, 30000) // Poll ogni 30 secondi\n    },\n\n    stopPolling() {\n      if (this.polling) {\n        clearInterval(this.polling)\n        this.polling = null\n      }\n    }\n  }\n})\n```\n\n### **4.3 Notification Bell Component**\n\n**File:** `frontend/src/components/layout/HeaderNotifications.vue`\n\n```javascript\n<template>\n  <div class=\"relative\">\n    <!-- Notification Bell -->\n    <button @click=\"showDropdown = !showDropdown\"\n            class=\"relative p-2 text-gray-400 hover:text-gray-600 focus:outline-none\">\n      <BellIcon class=\"w-6 h-6\" />\n      \n      <!-- Badge -->\n      <span v-if=\"unreadCount > 0\"\n            class=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n        {{ unreadCount > 99 ? '99+' : unreadCount }}\n      </span>\n    </button>\n\n    <!-- Dropdown -->\n    <div v-if=\"showDropdown\" \n         class=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50\">\n      <!-- Header -->\n      <div class=\"px-4 py-3 border-b border-gray-200\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-sm font-medium text-gray-900\">Notifiche</h3>\n          <button v-if=\"unreadCount > 0\" \n                  @click=\"markAllAsRead\"\n                  class=\"text-xs text-blue-600 hover:text-blue-800\">\n            Segna tutte come lette\n          </button>\n        </div>\n      </div>\n\n      <!-- Lista Notifiche -->\n      <div class=\"max-h-96 overflow-y-auto\">\n        <div v-if=\"notifications.length === 0\" \n             class=\"px-4 py-8 text-center text-gray-500\">\n          Nessuna notifica\n        </div>\n        \n        <div v-for=\"notification in notifications.slice(0, 10)\" \n             :key=\"notification.id\"\n             class=\"px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer\"\n             :class=\"{ 'bg-blue-50': !notification.read }\"\n             @click=\"handleNotificationClick(notification)\">\n          \n          <div class=\"flex items-start space-x-3\">\n            <!-- Icon -->\n            <div class=\"flex-shrink-0\">\n              <component :is=\"getNotificationIcon(notification.type)\"\n                         class=\"w-5 h-5\"\n                         :class=\"getNotificationIconColor(notification.type)\" />\n            </div>\n            \n            <!-- Content -->\n            <div class=\"flex-1 min-w-0\">\n              <p class=\"text-sm font-medium text-gray-900\">\n                {{ notification.title }}\n              </p>\n              <p class=\"text-sm text-gray-500 truncate\">\n                {{ notification.message }}\n              </p>\n              <p class=\"text-xs text-gray-400 mt-1\">\n                {{ formatRelativeTime(notification.created_at) }}\n              </p>\n            </div>\n            \n            <!-- Unread indicator -->\n            <div v-if=\"!notification.read\" \n                 class=\"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full\">\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Footer -->\n      <div class=\"px-4 py-3 border-t border-gray-200\">\n        <router-link to=\"/app/notifications\" \n                     class=\"text-sm text-blue-600 hover:text-blue-800\">\n          Vedi tutte le notifiche\n        </router-link>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 5: GERARCHIA MANAGER**\n\n### **5.1 Manager Hierarchy Service**\n\n**File:** `frontend/src/services/managerHierarchyService.js`\n\n```javascript\nexport const managerHierarchyService = {\n  // Ottieni team members sotto il manager\n  getTeamMembers: async (managerId = null) => {\n    const response = await apiClient.get('/personnel/team-hierarchy', {\n      params: { manager_id: managerId }\n    })\n    return response.data\n  },\n\n  // Ottieni timesheet da approvare per il manager\n  getPendingApprovalsForManager: async (filters = {}) => {\n    const response = await apiClient.get('/monthly-timesheets/pending-approvals', {\n      params: {\n        month: filters.month,\n        year: filters.year,\n        team_member_id: filters.teamMemberId\n      }\n    })\n    return response.data\n  },\n\n  // Verifica se può approvare timesheet\n  canApproveTimesheet: async (timesheetId) => {\n    const response = await apiClient.get(`/monthly-timesheets/${timesheetId}/can-approve`)\n    return response.data\n  }\n}\n```\n\n---\n\n## 📊 **STRUTTURA FILE COMPLETA**\n\n```\nfrontend/src/\n├── views/timesheet/\n│   ├── TimesheetDashboard.vue\n│   ├── TimesheetEntries.vue\n│   ├── TimesheetApprovals.vue\n│   ├── TimeOffRequests.vue\n│   ├── TimesheetReports.vue\n│   └── components/\n│       ├── QuickTimeEntry.vue\n│       ├── TimesheetCalendar.vue\n│       ├── TimesheetEntriesTable.vue\n│       ├── TimesheetApprovalsTable.vue\n│       ├── TimesheetDetailModal.vue\n│       ├── AnomalyDetailModal.vue\n│       ├── BulkActionsMenu.vue\n│       └── filters/\n│           ├── DateRangePicker.vue\n│           ├── ProjectSelector.vue\n│           ├── StatusFilter.vue\n│           ├── TeamMemberFilter.vue\n│           └── AnomalyFilter.vue\n├── services/\n│   ├── timesheetApi.js\n│   ├── aiAnomalyService.js\n│   ├── notificationService.js\n│   └── managerHierarchyService.js\n├── stores/\n│   ├── timesheet.js\n│   ├── notifications.js\n│   └── managerHierarchy.js\n└── composables/\n    ├── useTimesheetApprovals.js\n    ├── useAnomalyDetection.js\n    └── useNotifications.js\n```\n\n---\n\n## ⏱️ **TIMELINE IMPLEMENTAZIONE**\n\n### **Settimana 1:**\n- ✅ Struttura router e sidebar\n- ✅ TimesheetDashboard base\n- ✅ TimesheetEntries CRUD\n\n### **Settimana 2:**\n- ✅ Sistema approvazioni\n- ✅ AI anomaly detection\n- ✅ Bulk operations\n\n### **Settimana 3:**\n- ✅ Sistema notifiche\n- ✅ Gerarchia manager\n- ✅ Testing e refinement\n\n---\n\n## 🎯 **FASE 6: SISTEMA CRM COMPLETO**\n\n### **6.1 Rotte CRM**\n\n**File:** `frontend/src/router/index.js`\n\n```javascript\n// CRM routes - Menu separato\n{\n  path: '/app/crm',\n  meta: { requiresAuth: true },\n  children: [\n    {\n      path: '',\n      name: 'crm-dashboard',\n      component: () => import('@/views/crm/CRMDashboard.vue'),\n      meta: { requiredPermission: 'view_crm' }\n    },\n    {\n      path: 'clients',\n      name: 'crm-clients',\n      component: () => import('@/views/crm/ClientList.vue'),\n      meta: { requiredPermission: 'view_all_clients' }\n    },\n    {\n      path: 'clients/:id',\n      name: 'client-detail',\n      component: () => import('@/views/crm/ClientDetail.vue'),\n      meta: { requiredPermission: 'view_all_clients' }\n    },\n    {\n      path: 'contacts',\n      name: 'crm-contacts',\n      component: () => import('@/views/crm/ContactList.vue'),\n      meta: { requiredPermission: 'view_all_clients' }\n    },\n    {\n      path: 'proposals',\n      name: 'crm-proposals',\n      component: () => import('@/views/crm/ProposalList.vue'),\n      meta: { requiredPermission: 'view_all_proposals' }\n    },\n    {\n      path: 'proposals/:id',\n      name: 'proposal-detail',\n      component: () => import('@/views/crm/ProposalDetail.vue'),\n      meta: { requiredPermission: 'view_all_proposals' }\n    },\n    {\n      path: 'contracts',\n      name: 'crm-contracts',\n      component: () => import('@/views/crm/ContractList.vue'),\n      meta: { requiredPermission: 'view_contracts' }\n    },\n    {\n      path: 'invoices',\n      name: 'crm-invoices',\n      component: () => import('@/views/crm/InvoiceList.vue'),\n      meta: { requiredPermission: 'view_invoices' }\n    }\n  ]\n}\n```\n\n### **6.2 Menu CRM in Sidebar**\n\n```javascript\n// Aggiungere in SidebarNavigation.vue\n{\n  name: 'CRM',\n  icon: 'users',\n  children: [\n    {\n      name: 'Dashboard',\n      path: '/app/crm',\n      icon: 'chart-bar',\n      permission: 'view_crm'\n    },\n    {\n      name: 'Clienti/Lead',\n      path: '/app/crm/clients',\n      icon: 'office-building',\n      permission: 'view_all_clients'\n    },\n    {\n      name: 'Contatti',\n      path: '/app/crm/contacts',\n      icon: 'user-group',\n      permission: 'view_all_clients'\n    },\n    {\n      name: 'Proposte',\n      path: '/app/crm/proposals',\n      icon: 'document-text',\n      permission: 'view_all_proposals'\n    },\n    {\n      name: 'Contratti',\n      path: '/app/crm/contracts',\n      icon: 'clipboard-check',\n      permission: 'view_contracts'\n    },\n    {\n      name: 'Fatture',\n      path: '/app/crm/invoices',\n      icon: 'receipt-tax',\n      permission: 'view_invoices'\n    }\n  ]\n}\n```\n\n### **6.3 CRM Dashboard**\n\n**File:** `frontend/src/views/crm/CRMDashboard.vue`\n\n```javascript\n<template>\n  <div class=\"crm-dashboard\">\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <h1 class=\"text-2xl font-bold text-gray-900\">CRM Dashboard</h1>\n      <p class=\"text-gray-600\">Panoramica vendite e opportunità</p>\n    </div>\n\n    <!-- KPI Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard\n        title=\"Lead Attivi\"\n        :value=\"stats.activeLeads\"\n        color=\"blue\"\n        icon=\"user-plus\"\n      />\n      <StatCard\n        title=\"Proposte in Corso\"\n        :value=\"stats.activeProposals\"\n        color=\"yellow\"\n        icon=\"document\"\n      />\n      <StatCard\n        title=\"Contratti Attivi\"\n        :value=\"stats.activeContracts\"\n        color=\"green\"\n        icon=\"clipboard-check\"\n      />\n      <StatCard\n        title=\"Revenue Mensile\"\n        :value=\"formatCurrency(stats.monthlyRevenue)\"\n        color=\"purple\"\n        icon=\"currency-euro\"\n      />\n    </div>\n\n    <!-- Pipeline Overview -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n      <!-- Lead Pipeline -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium mb-4\">Pipeline Lead</h3>\n        <LeadPipelineChart :data=\"pipelineData.leads\" />\n      </div>\n\n      <!-- Proposal Pipeline -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium mb-4\">Pipeline Proposte</h3>\n        <ProposalPipelineChart :data=\"pipelineData.proposals\" />\n      </div>\n    </div>\n\n    <!-- Recent Activity -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <RecentCRMActivity />\n      <UpcomingTasks />\n    </div>\n  </div>\n</template>\n```\n\n### **6.4 Client Management con Lead Pipeline**\n\n**File:** `frontend/src/views/crm/ClientList.vue`\n\n```javascript\n<template>\n  <div class=\"client-list\">\n    <!-- Header -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h1 class=\"text-2xl font-bold\">Gestione Clienti</h1>\n        <button @click=\"showCreateModal = true\" class=\"btn-primary\">\n          <PlusIcon class=\"w-4 h-4 mr-2\" />\n          Nuovo Cliente/Lead\n        </button>\n      </div>\n\n      <!-- Filtri -->\n      <div class=\"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <StatusFilter v-model=\"selectedStatus\"\n                      :options=\"clientStatuses\" />\n        <IndustryFilter v-model=\"selectedIndustry\" />\n        <SearchInput v-model=\"searchQuery\" />\n        <ViewToggle v-model=\"viewMode\"\n                    :options=\"['table', 'kanban']\" />\n      </div>\n    </div>\n\n    <!-- Vista Kanban per Lead Management -->\n    <div v-if=\"viewMode === 'kanban'\" class=\"mb-6\">\n      <LeadKanbanBoard\n        :clients=\"filteredClients\"\n        @move-client=\"moveClientStatus\"\n        @edit-client=\"editClient\"\n      />\n    </div>\n\n    <!-- Vista Tabella -->\n    <div v-else class=\"bg-white shadow rounded-lg\">\n      <ClientTable\n        :clients=\"filteredClients\"\n        :loading=\"loading\"\n        @edit=\"editClient\"\n        @delete=\"deleteClient\"\n        @convert=\"convertLead\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <ClientModal\n      v-if=\"showCreateModal || editingClient\"\n      :client=\"editingClient\"\n      @close=\"closeModal\"\n      @saved=\"handleClientSaved\"\n    />\n  </div>\n</template>\n```\n\n### **6.5 Proposal Management con Opportunity Pipeline**\n\n**File:** `frontend/src/views/crm/ProposalList.vue`\n\n```javascript\n<template>\n  <div class=\"proposal-list\">\n    <!-- Header -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h1 class=\"text-2xl font-bold\">Gestione Proposte</h1>\n        <button @click=\"showCreateModal = true\" class=\"btn-primary\">\n          <DocumentAddIcon class=\"w-4 h-4 mr-2\" />\n          Nuova Proposta\n        </button>\n      </div>\n\n      <!-- Filtri -->\n      <div class=\"mt-4 grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <StatusFilter v-model=\"selectedStatus\"\n                      :options=\"proposalStatuses\" />\n        <ClientFilter v-model=\"selectedClient\" />\n        <DateRangeFilter v-model=\"dateRange\" />\n        <ValueRangeFilter v-model=\"valueRange\" />\n        <ViewToggle v-model=\"viewMode\"\n                    :options=\"['table', 'kanban']\" />\n      </div>\n    </div>\n\n    <!-- Vista Kanban per Opportunity Management -->\n    <div v-if=\"viewMode === 'kanban'\" class=\"mb-6\">\n      <OpportunityKanbanBoard\n        :proposals=\"filteredProposals\"\n        @move-proposal=\"moveProposalStatus\"\n        @edit-proposal=\"editProposal\"\n      />\n    </div>\n\n    <!-- Vista Tabella -->\n    <div v-else class=\"bg-white shadow rounded-lg\">\n      <ProposalTable\n        :proposals=\"filteredProposals\"\n        :loading=\"loading\"\n        @edit=\"editProposal\"\n        @delete=\"deleteProposal\"\n        @convert=\"convertToContract\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <ProposalModal\n      v-if=\"showCreateModal || editingProposal\"\n      :proposal=\"editingProposal\"\n      @close=\"closeModal\"\n      @saved=\"handleProposalSaved\"\n    />\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 7: INTEGRAZIONE PROGETTI-CONTRATTI**\n\n### **7.1 Modifica ProjectOverview.vue**\n\n**File:** `frontend/src/views/projects/components/ProjectOverview.vue`\n\n```javascript\n// Aggiungere sezione Contract Info\n<div class=\"bg-white shadow rounded-lg p-6\" v-if=\"project.contract\">\n  <div class=\"flex items-center justify-between mb-4\">\n    <h3 class=\"text-lg font-medium text-gray-900\">Contratto Collegato</h3>\n    <router-link :to=\"`/app/crm/contracts/${project.contract.id}`\"\n                 class=\"text-blue-600 hover:text-blue-800 text-sm\">\n      Gestisci Contratto →\n    </router-link>\n  </div>\n\n  <div class=\"grid grid-cols-2 gap-4\">\n    <div>\n      <dt class=\"text-sm text-gray-500\">Numero Contratto</dt>\n      <dd class=\"text-sm font-medium\">{{ project.contract.contract_number }}</dd>\n    </div>\n    <div>\n      <dt class=\"text-sm text-gray-500\">Tipo</dt>\n      <dd class=\"text-sm font-medium\">{{ project.contract.contract_type }}</dd>\n    </div>\n    <div>\n      <dt class=\"text-sm text-gray-500\">Tariffa Oraria</dt>\n      <dd class=\"text-sm font-medium\">{{ formatCurrency(project.contract.hourly_rate) }}</dd>\n    </div>\n    <div>\n      <dt class=\"text-sm text-gray-500\">Stato</dt>\n      <dd>\n        <StatusBadge :status=\"project.contract.status\" />\n      </dd>\n    </div>\n  </div>\n</div>\n```\n\n### **7.2 Modifica ProjectTimesheet.vue**\n\n```javascript\n// Aggiungere campi billing nel form timesheet\n<div class=\"grid grid-cols-2 gap-4\">\n  <div>\n    <label class=\"block text-sm font-medium\">Fatturabile</label>\n    <select v-model=\"formData.billable\" class=\"form-select\">\n      <option :value=\"true\">Sì</option>\n      <option :value=\"false\">No</option>\n    </select>\n  </div>\n  <div v-if=\"formData.billable\">\n    <label class=\"block text-sm font-medium\">Tariffa (€/h)</label>\n    <input v-model=\"formData.billing_rate\"\n           type=\"number\"\n           step=\"0.01\"\n           :placeholder=\"defaultBillingRate\"\n           class=\"form-input\">\n  </div>\n</div>\n\n// Aggiungere indicatori billing nella tabella\n<td class=\"px-2 py-3 text-center\">\n  <div class=\"flex items-center justify-center space-x-1\">\n    <span class=\"text-xs font-medium\">{{ formatHours(task.daily_hours[day]) }}</span>\n    <span v-if=\"task.daily_billable[day]\"\n          class=\"w-2 h-2 bg-green-500 rounded-full\"\n          title=\"Fatturabile\"></span>\n  </div>\n</td>\n```\n\n---\n\n## 🎯 **FASE 8: COMPONENTI CONDIVISI**\n\n### **8.1 Kanban Board Component**\n\n**File:** `frontend/src/components/ui/KanbanBoard.vue`\n\n```javascript\n<template>\n  <div class=\"kanban-board\">\n    <div class=\"flex space-x-6 overflow-x-auto pb-4\">\n      <div v-for=\"column in columns\"\n           :key=\"column.id\"\n           class=\"flex-shrink-0 w-80\">\n\n        <!-- Column Header -->\n        <div class=\"bg-gray-100 rounded-t-lg p-4\">\n          <div class=\"flex items-center justify-between\">\n            <h3 class=\"font-medium text-gray-900\">\n              {{ column.title }}\n            </h3>\n            <span class=\"bg-gray-200 text-gray-600 px-2 py-1 rounded-full text-xs\">\n              {{ getItemsForColumn(column.id).length }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Column Content -->\n        <div class=\"bg-gray-50 rounded-b-lg p-4 min-h-96\">\n          <draggable\n            v-model=\"columnItems[column.id]\"\n            group=\"kanban\"\n            @change=\"handleMove\"\n            class=\"space-y-3\">\n\n            <KanbanCard\n              v-for=\"item in getItemsForColumn(column.id)\"\n              :key=\"item.id\"\n              :item=\"item\"\n              :type=\"cardType\"\n              @edit=\"$emit('edit-item', item)\"\n              @delete=\"$emit('delete-item', item)\"\n            />\n          </draggable>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n### **8.2 AI Anomaly Detection Component**\n\n**File:** `frontend/src/components/timesheet/AnomalyDetector.vue`\n\n```javascript\n<template>\n  <div class=\"anomaly-detector\">\n    <!-- AI Analysis Button -->\n    <button @click=\"runDetection\"\n            :disabled=\"analyzing\"\n            class=\"btn-purple\">\n      <BeakerIcon class=\"w-4 h-4 mr-2\" />\n      {{ analyzing ? 'Analizzando...' : 'Rileva Anomalie AI' }}\n    </button>\n\n    <!-- Anomalies Display -->\n    <div v-if=\"anomalies.length\" class=\"mt-4 space-y-3\">\n      <div v-for=\"anomaly in anomalies\"\n           :key=\"anomaly.id\"\n           class=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n\n        <div class=\"flex items-start\">\n          <ExclamationTriangleIcon class=\"w-5 h-5 text-red-400 mt-0.5\" />\n          <div class=\"ml-3 flex-1\">\n            <h4 class=\"text-sm font-medium text-red-800\">\n              {{ anomaly.type_label }}\n            </h4>\n            <p class=\"text-sm text-red-700 mt-1\">\n              {{ anomaly.description }}\n            </p>\n\n            <!-- Anomaly Details -->\n            <div class=\"mt-2 text-xs text-red-600\">\n              <span>Utente: {{ anomaly.user_name }}</span>\n              <span class=\"mx-2\">•</span>\n              <span>Confidenza: {{ anomaly.confidence }}%</span>\n              <span class=\"mx-2\">•</span>\n              <span>{{ formatDate(anomaly.detected_at) }}</span>\n            </div>\n\n            <!-- Suggested Actions -->\n            <div v-if=\"anomaly.suggested_actions\" class=\"mt-3\">\n              <p class=\"text-xs font-medium text-red-800\">Azioni Suggerite:</p>\n              <ul class=\"text-xs text-red-700 mt-1 space-y-1\">\n                <li v-for=\"action in anomaly.suggested_actions\" :key=\"action\">\n                  • {{ action }}\n                </li>\n              </ul>\n            </div>\n          </div>\n\n          <button @click=\"dismissAnomaly(anomaly)\"\n                  class=\"text-red-400 hover:text-red-600\">\n            <XMarkIcon class=\"w-4 h-4\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n---\n\n## 📊 **STRUTTURA FILE CRM COMPLETA**\n\n```\nfrontend/src/\n├── views/crm/\n│   ├── CRMDashboard.vue\n│   ├── ClientList.vue\n│   ├── ClientDetail.vue\n│   ├── ContactList.vue\n│   ├── ProposalList.vue\n│   ├── ProposalDetail.vue\n│   ├── ContractList.vue\n│   ├── InvoiceList.vue\n│   └── components/\n│       ├── LeadKanbanBoard.vue\n│       ├── OpportunityKanbanBoard.vue\n│       ├── ClientTable.vue\n│       ├── ProposalTable.vue\n│       ├── ClientModal.vue\n│       ├── ProposalModal.vue\n│       ├── LeadPipelineChart.vue\n│       ├── ProposalPipelineChart.vue\n│       └── filters/\n│           ├── StatusFilter.vue\n│           ├── IndustryFilter.vue\n│           ├── ClientFilter.vue\n│           ├── DateRangeFilter.vue\n│           └── ValueRangeFilter.vue\n├── components/ui/\n│   ├── KanbanBoard.vue\n│   ├── KanbanCard.vue\n│   ├── StatusBadge.vue\n│   └── ViewToggle.vue\n└── components/timesheet/\n    ├── AnomalyDetector.vue\n    ├── BulkApprovalActions.vue\n    └── TimesheetNotifications.vue\n```\n\n---\n\n## ⚡ **FEATURES AVANZATE**\n\n### **🤖 AI Integration:**\n- Anomaly detection per timesheet\n- Lead scoring automatico\n- Proposal success prediction\n- Resource allocation optimization\n\n### **📱 Real-time Features:**\n- Notifiche in-app live\n- Status updates real-time\n- Collaborative editing\n\n### **📊 Analytics:**\n- Pipeline conversion rates\n- Team productivity metrics\n- Revenue forecasting\n- Client satisfaction tracking\n\n### **🔐 Security:**\n- Role-based permissions\n- Data encryption\n- Audit logging\n- GDPR compliance\n\n**Procediamo con l'implementazione?**\n", "modifiedCode": "# 🎯 **PIANO IMPLEMENTAZIONE FRONTEND TIMESHEET + CRM**\n\n## 📋 **OVERVIEW ARCHITETTURA**\n\n### **🎯 OBIETTIVI:**\n- Menu Timesheet separato con workflow approvazioni centralizzato\n- Bulk approval con AI anomaly detection\n- Sistema notifiche in-app (navbar)\n- Gerarchia manager per approvazioni\n- Integrazione CRM completa\n\n---\n\n## 🚀 **FASE 1: STRUTTURA BASE TIMESHEET**\n\n### **1.1 Nuove Rotte Router**\n\n**File:** `frontend/src/router/index.js`\n\n```javascript\n// Timesheet routes - Menu separato\n{\n  path: '/app/timesheet',\n  meta: { requiresAuth: true },\n  children: [\n    {\n      path: '',\n      name: 'timesheet-dashboard',\n      component: () => import('@/views/timesheet/TimesheetDashboard.vue'),\n      meta: { requiredPermission: 'view_timesheet' }\n    },\n    {\n      path: 'entries',\n      name: 'timesheet-entries',\n      component: () => import('@/views/timesheet/TimesheetEntries.vue'),\n      meta: { requiredPermission: 'manage_timesheet' }\n    },\n    {\n      path: 'approvals',\n      name: 'timesheet-approvals',\n      component: () => import('@/views/timesheet/TimesheetApprovals.vue'),\n      meta: { requiredPermission: 'approve_timesheets' }\n    },\n    {\n      path: 'time-off',\n      name: 'time-off-requests',\n      component: () => import('@/views/timesheet/TimeOffRequests.vue'),\n      meta: { requiredPermission: 'manage_time_off' }\n    },\n    {\n      path: 'reports',\n      name: 'timesheet-reports',\n      component: () => import('@/views/timesheet/TimesheetReports.vue'),\n      meta: { requiredPermission: 'view_timesheet_reports' }\n    }\n  ]\n}\n```\n\n### **1.2 Aggiornamento Sidebar Navigation**\n\n**File:** `frontend/src/components/layout/SidebarNavigation.vue`\n\n```javascript\n// Aggiungere menu Timesheet separato\n{\n  name: 'Timesheet',\n  icon: 'clock',\n  children: [\n    { \n      name: 'Dashboard', \n      path: '/app/timesheet', \n      icon: 'chart-bar',\n      permission: 'view_timesheet'\n    },\n    { \n      name: 'Registra Ore', \n      path: '/app/timesheet/entries', \n      icon: 'plus-circle',\n      permission: 'manage_timesheet'\n    },\n    { \n      name: 'Approvazioni', \n      path: '/app/timesheet/approvals', \n      icon: 'check-circle',\n      permission: 'approve_timesheets'\n    },\n    { \n      name: 'Ferie/Permessi', \n      path: '/app/timesheet/time-off', \n      icon: 'calendar',\n      permission: 'manage_time_off'\n    },\n    { \n      name: 'Report', \n      path: '/app/timesheet/reports', \n      icon: 'document-report',\n      permission: 'view_timesheet_reports'\n    }\n  ]\n}\n```\n\n---\n\n## 🎯 **FASE 2: COMPONENTI TIMESHEET CORE**\n\n### **2.1 Timesheet Dashboard**\n\n**File:** `frontend/src/views/timesheet/TimesheetDashboard.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-dashboard\">\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <h1 class=\"text-2xl font-bold text-gray-900\">Dashboard Timesheet</h1>\n      <p class=\"text-gray-600\">Panoramica ore e approvazioni</p>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard \n        title=\"Ore Questa Settimana\" \n        :value=\"stats.weeklyHours\" \n        color=\"blue\"\n        icon=\"clock\"\n      />\n      <StatCard \n        title=\"Ore Questo Mese\" \n        :value=\"stats.monthlyHours\" \n        color=\"green\"\n        icon=\"calendar\"\n      />\n      <StatCard \n        title=\"Da Approvare\" \n        :value=\"stats.pendingApprovals\" \n        color=\"yellow\"\n        icon=\"exclamation\"\n        v-if=\"canApprove\"\n      />\n      <StatCard \n        title=\"Efficienza\" \n        :value=\"stats.efficiency + '%'\" \n        color=\"purple\"\n        icon=\"trending-up\"\n      />\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n      <!-- Registrazione Rapida -->\n      <QuickTimeEntry @saved=\"refreshStats\" />\n      \n      <!-- Calendario Ore -->\n      <TimesheetCalendar :month=\"currentMonth\" />\n    </div>\n\n    <!-- Recent Activity & Notifications -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <RecentTimesheetActivity />\n      <PendingApprovals v-if=\"canApprove\" />\n    </div>\n  </div>\n</template>\n```\n\n### **2.2 Timesheet Entries**\n\n**File:** `frontend/src/views/timesheet/TimesheetEntries.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-entries\">\n    <!-- Header con filtri -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h1 class=\"text-2xl font-bold\">Registrazione Ore</h1>\n        <button @click=\"showQuickEntry = true\" \n                class=\"btn-primary\">\n          <PlusIcon class=\"w-4 h-4 mr-2\" />\n          Registra Ore\n        </button>\n      </div>\n      \n      <!-- Filtri -->\n      <div class=\"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <DateRangePicker v-model=\"dateRange\" />\n        <ProjectSelector v-model=\"selectedProject\" />\n        <StatusFilter v-model=\"selectedStatus\" />\n        <SearchInput v-model=\"searchQuery\" placeholder=\"Cerca...\" />\n      </div>\n    </div>\n\n    <!-- Lista Entries -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <TimesheetEntriesTable \n        :entries=\"filteredEntries\"\n        :loading=\"loading\"\n        @edit=\"editEntry\"\n        @delete=\"deleteEntry\"\n        @bulk-action=\"handleBulkAction\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <TimesheetEntryModal \n      v-if=\"showQuickEntry || editingEntry\"\n      :entry=\"editingEntry\"\n      @close=\"closeModal\"\n      @saved=\"handleEntrySaved\"\n    />\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 3: SISTEMA APPROVAZIONI CON AI**\n\n### **3.1 Timesheet Approvals - Vista Manager**\n\n**File:** `frontend/src/views/timesheet/TimesheetApprovals.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-approvals\">\n    <!-- Header con AI Analysis -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold\">Approvazioni Timesheet</h1>\n          <p class=\"text-gray-600\">Gestisci approvazioni con assistenza AI</p>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <!-- AI Anomaly Detection -->\n          <button @click=\"runAnomalyDetection\" \n                  :disabled=\"analyzingAnomalies\"\n                  class=\"btn-purple\">\n            <BeakerIcon class=\"w-4 h-4 mr-2\" />\n            {{ analyzingAnomalies ? 'Analizzando...' : 'Rileva Anomalie' }}\n          </button>\n          \n          <!-- Bulk Actions -->\n          <div class=\"relative\" v-if=\"selectedTimesheets.length\">\n            <button @click=\"showBulkMenu = !showBulkMenu\" \n                    class=\"btn-secondary\">\n              Azioni Multiple ({{ selectedTimesheets.length }})\n            </button>\n            <BulkActionsMenu \n              v-if=\"showBulkMenu\"\n              @approve-all=\"bulkApprove\"\n              @reject-all=\"bulkReject\"\n              @close=\"showBulkMenu = false\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- AI Anomalies Alert -->\n      <div v-if=\"anomalies.length\" \n           class=\"mt-4 bg-red-50 border border-red-200 rounded-lg p-4\">\n        <div class=\"flex items-start\">\n          <ExclamationTriangleIcon class=\"w-5 h-5 text-red-400 mt-0.5\" />\n          <div class=\"ml-3\">\n            <h3 class=\"text-sm font-medium text-red-800\">\n              Anomalie Rilevate ({{ anomalies.length }})\n            </h3>\n            <div class=\"mt-2 space-y-1\">\n              <div v-for=\"anomaly in anomalies\" :key=\"anomaly.id\"\n                   class=\"text-sm text-red-700\">\n                • {{ anomaly.user_name }}: {{ anomaly.description }}\n                <button @click=\"viewAnomalyDetails(anomaly)\"\n                        class=\"ml-2 text-red-600 underline\">\n                  Dettagli\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard title=\"Da Approvare\" :value=\"stats.pending\" color=\"yellow\" />\n      <StatCard title=\"Approvati\" :value=\"stats.approved\" color=\"green\" />\n      <StatCard title=\"Con Anomalie\" :value=\"anomalies.length\" color=\"red\" />\n      <StatCard title=\"Ore Totali\" :value=\"stats.totalHours\" color=\"blue\" />\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <MonthSelector v-model=\"selectedMonth\" />\n        <StatusFilter v-model=\"selectedStatus\" />\n        <TeamMemberFilter v-model=\"selectedMember\" :members=\"teamMembers\" />\n        <AnomalyFilter v-model=\"showOnlyAnomalies\" />\n        <SearchInput v-model=\"searchQuery\" />\n      </div>\n    </div>\n\n    <!-- Lista Timesheet -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <TimesheetApprovalsTable \n        :timesheets=\"filteredTimesheets\"\n        :loading=\"loading\"\n        :selected=\"selectedTimesheets\"\n        :anomalies=\"anomalies\"\n        @select=\"handleSelection\"\n        @view-details=\"viewDetails\"\n        @approve=\"approveTimesheet\"\n        @reject=\"rejectTimesheet\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <TimesheetDetailModal \n      v-if=\"showDetailModal\"\n      :timesheet=\"selectedTimesheet\"\n      :anomaly=\"getAnomalyForTimesheet(selectedTimesheet)\"\n      @close=\"showDetailModal = false\"\n      @approve=\"approveTimesheet\"\n      @reject=\"rejectTimesheet\"\n    />\n\n    <AnomalyDetailModal\n      v-if=\"showAnomalyModal\"\n      :anomaly=\"selectedAnomaly\"\n      @close=\"showAnomalyModal = false\"\n    />\n  </div>\n</template>\n```\n\n### **3.2 AI Anomaly Detection Service**\n\n**File:** `frontend/src/services/aiAnomalyService.js`\n\n```javascript\nexport const aiAnomalyService = {\n  // Rileva anomalie nei timesheet\n  detectAnomalies: async (filters = {}) => {\n    const response = await apiClient.post('/ai-timesheet/detect-anomalies', {\n      month: filters.month,\n      year: filters.year,\n      team_members: filters.teamMembers,\n      analysis_types: [\n        'overtime_patterns',\n        'weekend_work',\n        'unusual_hours',\n        'project_switching',\n        'productivity_drops',\n        'duplicate_entries'\n      ]\n    })\n    return response.data\n  },\n\n  // Analizza pattern specifico utente\n  analyzeUserPattern: async (userId, timeRange) => {\n    const response = await apiClient.post(`/ai-timesheet/analyze-user/${userId}`, {\n      start_date: timeRange.start,\n      end_date: timeRange.end,\n      include_recommendations: true\n    })\n    return response.data\n  },\n\n  // Suggerimenti approvazione\n  getApprovalSuggestions: async (timesheetId) => {\n    const response = await apiClient.get(`/ai-timesheet/approval-suggestions/${timesheetId}`)\n    return response.data\n  }\n}\n```\n\n---\n\n## 🎯 **FASE 4: SISTEMA NOTIFICHE IN-APP**\n\n### **4.1 Notification Service**\n\n**File:** `frontend/src/services/notificationService.js`\n\n```javascript\nexport const notificationService = {\n  // Ottieni notifiche utente\n  getUserNotifications: async (filters = {}) => {\n    const response = await apiClient.get('/notifications/', {\n      params: {\n        unread_only: filters.unreadOnly || false,\n        type: filters.type,\n        limit: filters.limit || 50\n      }\n    })\n    return response.data\n  },\n\n  // Marca come letta\n  markAsRead: async (notificationId) => {\n    const response = await apiClient.put(`/notifications/${notificationId}/read`)\n    return response.data\n  },\n\n  // Marca tutte come lette\n  markAllAsRead: async () => {\n    const response = await apiClient.put('/notifications/mark-all-read')\n    return response.data\n  },\n\n  // Elimina notifica\n  deleteNotification: async (notificationId) => {\n    const response = await apiClient.delete(`/notifications/${notificationId}`)\n    return response.data\n  }\n}\n```\n\n### **4.2 Notification Store**\n\n**File:** `frontend/src/stores/notifications.js`\n\n```javascript\nimport { defineStore } from 'pinia'\nimport { notificationService } from '@/services/notificationService'\n\nexport const useNotificationStore = defineStore('notifications', {\n  state: () => ({\n    notifications: [],\n    unreadCount: 0,\n    loading: false,\n    polling: null\n  }),\n\n  getters: {\n    unreadNotifications: (state) => \n      state.notifications.filter(n => !n.read),\n    \n    timesheetNotifications: (state) =>\n      state.notifications.filter(n => n.type.includes('timesheet')),\n    \n    approvalNotifications: (state) =>\n      state.notifications.filter(n => n.type.includes('approval'))\n  },\n\n  actions: {\n    async fetchNotifications() {\n      this.loading = true\n      try {\n        const result = await notificationService.getUserNotifications()\n        this.notifications = result.data.notifications\n        this.unreadCount = result.data.unread_count\n      } catch (error) {\n        console.error('Error fetching notifications:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async markAsRead(notificationId) {\n      try {\n        await notificationService.markAsRead(notificationId)\n        const notification = this.notifications.find(n => n.id === notificationId)\n        if (notification) {\n          notification.read = true\n          this.unreadCount = Math.max(0, this.unreadCount - 1)\n        }\n      } catch (error) {\n        console.error('Error marking notification as read:', error)\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        await notificationService.markAllAsRead()\n        this.notifications.forEach(n => n.read = true)\n        this.unreadCount = 0\n      } catch (error) {\n        console.error('Error marking all notifications as read:', error)\n      }\n    },\n\n    startPolling() {\n      this.polling = setInterval(() => {\n        this.fetchNotifications()\n      }, 30000) // Poll ogni 30 secondi\n    },\n\n    stopPolling() {\n      if (this.polling) {\n        clearInterval(this.polling)\n        this.polling = null\n      }\n    }\n  }\n})\n```\n\n### **4.3 Notification Bell Component**\n\n**File:** `frontend/src/components/layout/HeaderNotifications.vue`\n\n```javascript\n<template>\n  <div class=\"relative\">\n    <!-- Notification Bell -->\n    <button @click=\"showDropdown = !showDropdown\"\n            class=\"relative p-2 text-gray-400 hover:text-gray-600 focus:outline-none\">\n      <BellIcon class=\"w-6 h-6\" />\n      \n      <!-- Badge -->\n      <span v-if=\"unreadCount > 0\"\n            class=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n        {{ unreadCount > 99 ? '99+' : unreadCount }}\n      </span>\n    </button>\n\n    <!-- Dropdown -->\n    <div v-if=\"showDropdown\" \n         class=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50\">\n      <!-- Header -->\n      <div class=\"px-4 py-3 border-b border-gray-200\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-sm font-medium text-gray-900\">Notifiche</h3>\n          <button v-if=\"unreadCount > 0\" \n                  @click=\"markAllAsRead\"\n                  class=\"text-xs text-blue-600 hover:text-blue-800\">\n            Segna tutte come lette\n          </button>\n        </div>\n      </div>\n\n      <!-- Lista Notifiche -->\n      <div class=\"max-h-96 overflow-y-auto\">\n        <div v-if=\"notifications.length === 0\" \n             class=\"px-4 py-8 text-center text-gray-500\">\n          Nessuna notifica\n        </div>\n        \n        <div v-for=\"notification in notifications.slice(0, 10)\" \n             :key=\"notification.id\"\n             class=\"px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer\"\n             :class=\"{ 'bg-blue-50': !notification.read }\"\n             @click=\"handleNotificationClick(notification)\">\n          \n          <div class=\"flex items-start space-x-3\">\n            <!-- Icon -->\n            <div class=\"flex-shrink-0\">\n              <component :is=\"getNotificationIcon(notification.type)\"\n                         class=\"w-5 h-5\"\n                         :class=\"getNotificationIconColor(notification.type)\" />\n            </div>\n            \n            <!-- Content -->\n            <div class=\"flex-1 min-w-0\">\n              <p class=\"text-sm font-medium text-gray-900\">\n                {{ notification.title }}\n              </p>\n              <p class=\"text-sm text-gray-500 truncate\">\n                {{ notification.message }}\n              </p>\n              <p class=\"text-xs text-gray-400 mt-1\">\n                {{ formatRelativeTime(notification.created_at) }}\n              </p>\n            </div>\n            \n            <!-- Unread indicator -->\n            <div v-if=\"!notification.read\" \n                 class=\"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full\">\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Footer -->\n      <div class=\"px-4 py-3 border-t border-gray-200\">\n        <router-link to=\"/app/notifications\" \n                     class=\"text-sm text-blue-600 hover:text-blue-800\">\n          Vedi tutte le notifiche\n        </router-link>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 5: GERARCHIA MANAGER**\n\n### **5.1 Manager Hierarchy Service**\n\n**File:** `frontend/src/services/managerHierarchyService.js`\n\n```javascript\nexport const managerHierarchyService = {\n  // Ottieni team members sotto il manager\n  getTeamMembers: async (managerId = null) => {\n    const response = await apiClient.get('/personnel/team-hierarchy', {\n      params: { manager_id: managerId }\n    })\n    return response.data\n  },\n\n  // Ottieni timesheet da approvare per il manager\n  getPendingApprovalsForManager: async (filters = {}) => {\n    const response = await apiClient.get('/monthly-timesheets/pending-approvals', {\n      params: {\n        month: filters.month,\n        year: filters.year,\n        team_member_id: filters.teamMemberId\n      }\n    })\n    return response.data\n  },\n\n  // Verifica se può approvare timesheet\n  canApproveTimesheet: async (timesheetId) => {\n    const response = await apiClient.get(`/monthly-timesheets/${timesheetId}/can-approve`)\n    return response.data\n  }\n}\n```\n\n---\n\n## 📊 **STRUTTURA FILE COMPLETA**\n\n```\nfrontend/src/\n├── views/timesheet/\n│   ├── TimesheetDashboard.vue\n│   ├── TimesheetEntries.vue\n│   ├── TimesheetApprovals.vue\n│   ├── TimeOffRequests.vue\n│   ├── TimesheetReports.vue\n│   └── components/\n│       ├── QuickTimeEntry.vue\n│       ├── TimesheetCalendar.vue\n│       ├── TimesheetEntriesTable.vue\n│       ├── TimesheetApprovalsTable.vue\n│       ├── TimesheetDetailModal.vue\n│       ├── AnomalyDetailModal.vue\n│       ├── BulkActionsMenu.vue\n│       └── filters/\n│           ├── DateRangePicker.vue\n│           ├── ProjectSelector.vue\n│           ├── StatusFilter.vue\n│           ├── TeamMemberFilter.vue\n│           └── AnomalyFilter.vue\n├── services/\n│   ├── timesheetApi.js\n│   ├── aiAnomalyService.js\n│   ├── notificationService.js\n│   └── managerHierarchyService.js\n├── stores/\n│   ├── timesheet.js\n│   ├── notifications.js\n│   └── managerHierarchy.js\n└── composables/\n    ├── useTimesheetApprovals.js\n    ├── useAnomalyDetection.js\n    └── useNotifications.js\n```\n\n---\n\n## ⏱️ **TIMELINE IMPLEMENTAZIONE**\n\n### **Settimana 1:**\n- ✅ Struttura router e sidebar\n- ✅ TimesheetDashboard base\n- ✅ TimesheetEntries CRUD\n\n### **Settimana 2:**\n- ✅ Sistema approvazioni\n- ✅ AI anomaly detection\n- ✅ Bulk operations\n\n### **Settimana 3:**\n- ✅ Sistema notifiche\n- ✅ Gerarchia manager\n- ✅ Testing e refinement\n\n---\n\n## 🎯 **FASE 6: SISTEMA CRM COMPLETO**\n\n### **6.1 Rotte CRM**\n\n**File:** `frontend/src/router/index.js`\n\n```javascript\n// CRM routes - Menu separato\n{\n  path: '/app/crm',\n  meta: { requiresAuth: true },\n  children: [\n    {\n      path: '',\n      name: 'crm-dashboard',\n      component: () => import('@/views/crm/CRMDashboard.vue'),\n      meta: { requiredPermission: 'view_crm' }\n    },\n    {\n      path: 'clients',\n      name: 'crm-clients',\n      component: () => import('@/views/crm/ClientList.vue'),\n      meta: { requiredPermission: 'view_all_clients' }\n    },\n    {\n      path: 'clients/:id',\n      name: 'client-detail',\n      component: () => import('@/views/crm/ClientDetail.vue'),\n      meta: { requiredPermission: 'view_all_clients' }\n    },\n    {\n      path: 'contacts',\n      name: 'crm-contacts',\n      component: () => import('@/views/crm/ContactList.vue'),\n      meta: { requiredPermission: 'view_all_clients' }\n    },\n    {\n      path: 'proposals',\n      name: 'crm-proposals',\n      component: () => import('@/views/crm/ProposalList.vue'),\n      meta: { requiredPermission: 'view_all_proposals' }\n    },\n    {\n      path: 'proposals/:id',\n      name: 'proposal-detail',\n      component: () => import('@/views/crm/ProposalDetail.vue'),\n      meta: { requiredPermission: 'view_all_proposals' }\n    },\n    {\n      path: 'contracts',\n      name: 'crm-contracts',\n      component: () => import('@/views/crm/ContractList.vue'),\n      meta: { requiredPermission: 'view_contracts' }\n    },\n    {\n      path: 'invoices',\n      name: 'crm-invoices',\n      component: () => import('@/views/crm/InvoiceList.vue'),\n      meta: { requiredPermission: 'view_invoices' }\n    }\n  ]\n}\n```\n\n### **6.2 Menu CRM in Sidebar**\n\n```javascript\n// Aggiungere in SidebarNavigation.vue\n{\n  name: 'CRM',\n  icon: 'users',\n  children: [\n    {\n      name: 'Dashboard',\n      path: '/app/crm',\n      icon: 'chart-bar',\n      permission: 'view_crm'\n    },\n    {\n      name: 'Clienti/Lead',\n      path: '/app/crm/clients',\n      icon: 'office-building',\n      permission: 'view_all_clients'\n    },\n    {\n      name: 'Contatti',\n      path: '/app/crm/contacts',\n      icon: 'user-group',\n      permission: 'view_all_clients'\n    },\n    {\n      name: 'Proposte',\n      path: '/app/crm/proposals',\n      icon: 'document-text',\n      permission: 'view_all_proposals'\n    },\n    {\n      name: 'Contratti',\n      path: '/app/crm/contracts',\n      icon: 'clipboard-check',\n      permission: 'view_contracts'\n    },\n    {\n      name: 'Fatture',\n      path: '/app/crm/invoices',\n      icon: 'receipt-tax',\n      permission: 'view_invoices'\n    }\n  ]\n}\n```\n\n### **6.3 CRM Dashboard**\n\n**File:** `frontend/src/views/crm/CRMDashboard.vue`\n\n```javascript\n<template>\n  <div class=\"crm-dashboard\">\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <h1 class=\"text-2xl font-bold text-gray-900\">CRM Dashboard</h1>\n      <p class=\"text-gray-600\">Panoramica vendite e opportunità</p>\n    </div>\n\n    <!-- KPI Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard\n        title=\"Lead Attivi\"\n        :value=\"stats.activeLeads\"\n        color=\"blue\"\n        icon=\"user-plus\"\n      />\n      <StatCard\n        title=\"Proposte in Corso\"\n        :value=\"stats.activeProposals\"\n        color=\"yellow\"\n        icon=\"document\"\n      />\n      <StatCard\n        title=\"Contratti Attivi\"\n        :value=\"stats.activeContracts\"\n        color=\"green\"\n        icon=\"clipboard-check\"\n      />\n      <StatCard\n        title=\"Revenue Mensile\"\n        :value=\"formatCurrency(stats.monthlyRevenue)\"\n        color=\"purple\"\n        icon=\"currency-euro\"\n      />\n    </div>\n\n    <!-- Pipeline Overview -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n      <!-- Lead Pipeline -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium mb-4\">Pipeline Lead</h3>\n        <LeadPipelineChart :data=\"pipelineData.leads\" />\n      </div>\n\n      <!-- Proposal Pipeline -->\n      <div class=\"bg-white shadow rounded-lg p-6\">\n        <h3 class=\"text-lg font-medium mb-4\">Pipeline Proposte</h3>\n        <ProposalPipelineChart :data=\"pipelineData.proposals\" />\n      </div>\n    </div>\n\n    <!-- Recent Activity -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <RecentCRMActivity />\n      <UpcomingTasks />\n    </div>\n  </div>\n</template>\n```\n\n### **6.4 Client Management con Lead Pipeline**\n\n**File:** `frontend/src/views/crm/ClientList.vue`\n\n```javascript\n<template>\n  <div class=\"client-list\">\n    <!-- Header -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h1 class=\"text-2xl font-bold\">Gestione Clienti</h1>\n        <button @click=\"showCreateModal = true\" class=\"btn-primary\">\n          <PlusIcon class=\"w-4 h-4 mr-2\" />\n          Nuovo Cliente/Lead\n        </button>\n      </div>\n\n      <!-- Filtri -->\n      <div class=\"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <StatusFilter v-model=\"selectedStatus\"\n                      :options=\"clientStatuses\" />\n        <IndustryFilter v-model=\"selectedIndustry\" />\n        <SearchInput v-model=\"searchQuery\" />\n        <ViewToggle v-model=\"viewMode\"\n                    :options=\"['table', 'kanban']\" />\n      </div>\n    </div>\n\n    <!-- Vista Kanban per Lead Management -->\n    <div v-if=\"viewMode === 'kanban'\" class=\"mb-6\">\n      <LeadKanbanBoard\n        :clients=\"filteredClients\"\n        @move-client=\"moveClientStatus\"\n        @edit-client=\"editClient\"\n      />\n    </div>\n\n    <!-- Vista Tabella -->\n    <div v-else class=\"bg-white shadow rounded-lg\">\n      <ClientTable\n        :clients=\"filteredClients\"\n        :loading=\"loading\"\n        @edit=\"editClient\"\n        @delete=\"deleteClient\"\n        @convert=\"convertLead\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <ClientModal\n      v-if=\"showCreateModal || editingClient\"\n      :client=\"editingClient\"\n      @close=\"closeModal\"\n      @saved=\"handleClientSaved\"\n    />\n  </div>\n</template>\n```\n\n### **6.5 Proposal Management con Opportunity Pipeline**\n\n**File:** `frontend/src/views/crm/ProposalList.vue`\n\n```javascript\n<template>\n  <div class=\"proposal-list\">\n    <!-- Header -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h1 class=\"text-2xl font-bold\">Gestione Proposte</h1>\n        <button @click=\"showCreateModal = true\" class=\"btn-primary\">\n          <DocumentAddIcon class=\"w-4 h-4 mr-2\" />\n          Nuova Proposta\n        </button>\n      </div>\n\n      <!-- Filtri -->\n      <div class=\"mt-4 grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <StatusFilter v-model=\"selectedStatus\"\n                      :options=\"proposalStatuses\" />\n        <ClientFilter v-model=\"selectedClient\" />\n        <DateRangeFilter v-model=\"dateRange\" />\n        <ValueRangeFilter v-model=\"valueRange\" />\n        <ViewToggle v-model=\"viewMode\"\n                    :options=\"['table', 'kanban']\" />\n      </div>\n    </div>\n\n    <!-- Vista Kanban per Opportunity Management -->\n    <div v-if=\"viewMode === 'kanban'\" class=\"mb-6\">\n      <OpportunityKanbanBoard\n        :proposals=\"filteredProposals\"\n        @move-proposal=\"moveProposalStatus\"\n        @edit-proposal=\"editProposal\"\n      />\n    </div>\n\n    <!-- Vista Tabella -->\n    <div v-else class=\"bg-white shadow rounded-lg\">\n      <ProposalTable\n        :proposals=\"filteredProposals\"\n        :loading=\"loading\"\n        @edit=\"editProposal\"\n        @delete=\"deleteProposal\"\n        @convert=\"convertToContract\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <ProposalModal\n      v-if=\"showCreateModal || editingProposal\"\n      :proposal=\"editingProposal\"\n      @close=\"closeModal\"\n      @saved=\"handleProposalSaved\"\n    />\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 7: INTEGRAZIONE PROGETTI-CONTRATTI**\n\n### **7.1 Modifica ProjectOverview.vue**\n\n**File:** `frontend/src/views/projects/components/ProjectOverview.vue`\n\n```javascript\n// Aggiungere sezione Contract Info\n<div class=\"bg-white shadow rounded-lg p-6\" v-if=\"project.contract\">\n  <div class=\"flex items-center justify-between mb-4\">\n    <h3 class=\"text-lg font-medium text-gray-900\">Contratto Collegato</h3>\n    <router-link :to=\"`/app/crm/contracts/${project.contract.id}`\"\n                 class=\"text-blue-600 hover:text-blue-800 text-sm\">\n      Gestisci Contratto →\n    </router-link>\n  </div>\n\n  <div class=\"grid grid-cols-2 gap-4\">\n    <div>\n      <dt class=\"text-sm text-gray-500\">Numero Contratto</dt>\n      <dd class=\"text-sm font-medium\">{{ project.contract.contract_number }}</dd>\n    </div>\n    <div>\n      <dt class=\"text-sm text-gray-500\">Tipo</dt>\n      <dd class=\"text-sm font-medium\">{{ project.contract.contract_type }}</dd>\n    </div>\n    <div>\n      <dt class=\"text-sm text-gray-500\">Tariffa Oraria</dt>\n      <dd class=\"text-sm font-medium\">{{ formatCurrency(project.contract.hourly_rate) }}</dd>\n    </div>\n    <div>\n      <dt class=\"text-sm text-gray-500\">Stato</dt>\n      <dd>\n        <StatusBadge :status=\"project.contract.status\" />\n      </dd>\n    </div>\n  </div>\n</div>\n```\n\n### **7.2 Integrazione ProjectTimesheet.vue Esistente**\n\n**IMPORTANTE:** Il componente `ProjectTimesheet.vue` è già implementato e funzionante!\nDobbiamo solo estenderlo per integrarsi con il nuovo sistema.\n\n**Modifiche da apportare:**\n\n```javascript\n// 1. Aggiungere campi billing nel form esistente\n<div class=\"grid grid-cols-2 gap-4 mt-4\">\n  <div>\n    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n      Fatturabile\n    </label>\n    <select v-model=\"formData.billable\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700\">\n      <option :value=\"true\">Sì - Fatturabile</option>\n      <option :value=\"false\">No - Interno</option>\n    </select>\n  </div>\n  <div v-if=\"formData.billable && project.contract\">\n    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n      Tariffa (€/h)\n    </label>\n    <input v-model=\"formData.billing_rate\"\n           type=\"number\"\n           step=\"0.01\"\n           :placeholder=\"project.contract.hourly_rate\"\n           class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700\">\n  </div>\n</div>\n\n// 2. Aggiungere indicatori billing nella tabella esistente\n<td class=\"px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\"\n    :class=\"{ 'bg-blue-50 dark:bg-blue-900': isToday(day) }\"\n    @click=\"editCell(task.id, day)\">\n  <div class=\"flex items-center justify-center space-x-1\">\n    <span v-if=\"task.daily_hours[day] > 0\"\n          class=\"text-xs font-medium text-primary-600 dark:text-primary-400\">\n      {{ formatHours(task.daily_hours[day]) }}\n    </span>\n    <span v-else class=\"text-gray-300 dark:text-gray-600\">-</span>\n\n    <!-- Indicatore fatturabile -->\n    <span v-if=\"task.daily_hours[day] > 0 && task.daily_billable[day]\"\n          class=\"w-2 h-2 bg-green-500 rounded-full ml-1\"\n          title=\"Ore fatturabili\"></span>\n  </div>\n</td>\n\n// 3. Aggiungere stato approvazione mensile\n<div class=\"bg-white dark:bg-gray-800 shadow rounded-lg mb-6\"\n     v-if=\"monthlyTimesheetStatus\">\n  <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n    <div class=\"flex items-center justify-between\">\n      <h4 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n        Stato Approvazione {{ currentMonth }}/{{ currentYear }}\n      </h4>\n      <div class=\"flex items-center space-x-3\">\n        <StatusBadge :status=\"monthlyTimesheetStatus.status\" />\n        <button v-if=\"canSubmitTimesheet && monthlyTimesheetStatus.status === 'draft'\"\n                @click=\"submitForApproval\"\n                class=\"btn-primary\">\n          Sottometti per Approvazione\n        </button>\n      </div>\n    </div>\n\n    <!-- Feedback approvazione -->\n    <div v-if=\"monthlyTimesheetStatus.feedback\"\n         class=\"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded\">\n      <p class=\"text-sm text-yellow-800\">\n        <strong>Feedback:</strong> {{ monthlyTimesheetStatus.feedback }}\n      </p>\n    </div>\n  </div>\n</div>\n\n// 4. Aggiungere metodi per gestione approvazioni\nconst monthlyTimesheetStatus = ref(null)\n\nconst loadMonthlyStatus = async () => {\n  try {\n    const response = await fetch(`/api/monthly-timesheets/status?project_id=${props.project.id}&month=${currentMonth.value}&year=${currentYear.value}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      monthlyTimesheetStatus.value = result.data\n    }\n  } catch (error) {\n    console.error('Error loading monthly status:', error)\n  }\n}\n\nconst submitForApproval = async () => {\n  try {\n    const response = await fetch('/api/monthly-timesheets/submit', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        project_id: props.project.id,\n        month: currentMonth.value,\n        year: currentYear.value\n      })\n    })\n\n    if (response.ok) {\n      await loadMonthlyStatus()\n      // Mostra notifica successo\n    }\n  } catch (error) {\n    console.error('Error submitting timesheet:', error)\n  }\n}\n```\n\n### **7.3 Integrazione PersonnelProfile.vue**\n\n**IMPORTANTE:** Anche il profilo utente deve mostrare il timesheet personale!\n\n**File:** `frontend/src/views/personnel/PersonnelProfile.vue`\n\n```javascript\n// Aggiungere tab Timesheet nel profilo esistente\nconst availableTabs = computed(() => {\n  const tabs = [\n    { id: 'overview', label: 'Panoramica', icon: 'user' },\n    { id: 'projects', label: 'Progetti', icon: 'briefcase' },\n    { id: 'tasks', label: 'Task', icon: 'clipboard-list' },\n    { id: 'skills', label: 'Competenze', icon: 'academic-cap' },\n    { id: 'timesheet', label: 'Timesheet', icon: 'clock' }, // NUOVO\n    { id: 'cv', label: 'CV', icon: 'document-text' }\n  ]\n  return tabs\n})\n\n// Aggiungere componente timesheet personale\nconst currentTabComponent = computed(() => {\n  const components = {\n    overview: PersonnelOverview,\n    projects: PersonnelProjects,\n    tasks: PersonnelTasks,\n    skills: PersonnelSkills,\n    timesheet: PersonnelTimesheet, // NUOVO\n    cv: PersonnelCV\n  }\n  return components[activeTab.value] || PersonnelOverview\n})\n```\n\n**File:** `frontend/src/views/personnel/components/PersonnelTimesheet.vue`\n\n```javascript\n<template>\n  <div class=\"personnel-timesheet\">\n    <!-- Header con navigazione mesi -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h3 class=\"text-lg font-medium\">Timesheet Personale</h3>\n        <div class=\"flex items-center space-x-4\">\n          <!-- Navigazione mesi -->\n          <MonthNavigator v-model:month=\"currentMonth\"\n                          v-model:year=\"currentYear\" />\n\n          <!-- Link a registrazione rapida -->\n          <router-link to=\"/app/timesheet/entries\"\n                       class=\"btn-primary\">\n            Registra Ore\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Stats mensili -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard title=\"Ore Lavorate\" :value=\"monthlyStats.totalHours\" />\n      <StatCard title=\"Ore Fatturabili\" :value=\"monthlyStats.billableHours\" />\n      <StatCard title=\"Progetti Attivi\" :value=\"monthlyStats.activeProjects\" />\n      <StatCard title=\"Efficienza\" :value=\"monthlyStats.efficiency + '%'\" />\n    </div>\n\n    <!-- Stato approvazione -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h4 class=\"text-lg font-medium\">Stato Approvazione</h4>\n        <StatusBadge :status=\"approvalStatus.status\" />\n      </div>\n\n      <div class=\"mt-4\">\n        <div class=\"flex items-center space-x-4\">\n          <span class=\"text-sm text-gray-600\">\n            Ore totali: {{ approvalStatus.totalHours }}h\n          </span>\n          <span class=\"text-sm text-gray-600\">\n            Sottomesso: {{ formatDate(approvalStatus.submittedAt) }}\n          </span>\n        </div>\n\n        <div v-if=\"approvalStatus.feedback\" class=\"mt-3 p-3 bg-yellow-50 rounded\">\n          <p class=\"text-sm text-yellow-800\">\n            <strong>Feedback:</strong> {{ approvalStatus.feedback }}\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Timesheet dettaglio per progetti -->\n    <div class=\"space-y-6\">\n      <div v-for=\"project in projectTimesheets\" :key=\"project.id\"\n           class=\"bg-white shadow rounded-lg\">\n        <div class=\"px-6 py-4 border-b border-gray-200\">\n          <div class=\"flex items-center justify-between\">\n            <h4 class=\"text-lg font-medium\">{{ project.name }}</h4>\n            <router-link :to=\"`/app/projects/${project.id}#timesheet`\"\n                         class=\"text-blue-600 hover:text-blue-800 text-sm\">\n              Vedi Dettaglio →\n            </router-link>\n          </div>\n        </div>\n\n        <!-- Mini timesheet per progetto -->\n        <div class=\"p-6\">\n          <ProjectTimesheetSummary :project=\"project\"\n                                   :month=\"currentMonth\"\n                                   :year=\"currentYear\" />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 8: COMPONENTI CONDIVISI**\n\n### **8.1 Kanban Board Component**\n\n**File:** `frontend/src/components/ui/KanbanBoard.vue`\n\n```javascript\n<template>\n  <div class=\"kanban-board\">\n    <div class=\"flex space-x-6 overflow-x-auto pb-4\">\n      <div v-for=\"column in columns\"\n           :key=\"column.id\"\n           class=\"flex-shrink-0 w-80\">\n\n        <!-- Column Header -->\n        <div class=\"bg-gray-100 rounded-t-lg p-4\">\n          <div class=\"flex items-center justify-between\">\n            <h3 class=\"font-medium text-gray-900\">\n              {{ column.title }}\n            </h3>\n            <span class=\"bg-gray-200 text-gray-600 px-2 py-1 rounded-full text-xs\">\n              {{ getItemsForColumn(column.id).length }}\n            </span>\n          </div>\n        </div>\n\n        <!-- Column Content -->\n        <div class=\"bg-gray-50 rounded-b-lg p-4 min-h-96\">\n          <draggable\n            v-model=\"columnItems[column.id]\"\n            group=\"kanban\"\n            @change=\"handleMove\"\n            class=\"space-y-3\">\n\n            <KanbanCard\n              v-for=\"item in getItemsForColumn(column.id)\"\n              :key=\"item.id\"\n              :item=\"item\"\n              :type=\"cardType\"\n              @edit=\"$emit('edit-item', item)\"\n              @delete=\"$emit('delete-item', item)\"\n            />\n          </draggable>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n### **8.2 AI Anomaly Detection Component**\n\n**File:** `frontend/src/components/timesheet/AnomalyDetector.vue`\n\n```javascript\n<template>\n  <div class=\"anomaly-detector\">\n    <!-- AI Analysis Button -->\n    <button @click=\"runDetection\"\n            :disabled=\"analyzing\"\n            class=\"btn-purple\">\n      <BeakerIcon class=\"w-4 h-4 mr-2\" />\n      {{ analyzing ? 'Analizzando...' : 'Rileva Anomalie AI' }}\n    </button>\n\n    <!-- Anomalies Display -->\n    <div v-if=\"anomalies.length\" class=\"mt-4 space-y-3\">\n      <div v-for=\"anomaly in anomalies\"\n           :key=\"anomaly.id\"\n           class=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n\n        <div class=\"flex items-start\">\n          <ExclamationTriangleIcon class=\"w-5 h-5 text-red-400 mt-0.5\" />\n          <div class=\"ml-3 flex-1\">\n            <h4 class=\"text-sm font-medium text-red-800\">\n              {{ anomaly.type_label }}\n            </h4>\n            <p class=\"text-sm text-red-700 mt-1\">\n              {{ anomaly.description }}\n            </p>\n\n            <!-- Anomaly Details -->\n            <div class=\"mt-2 text-xs text-red-600\">\n              <span>Utente: {{ anomaly.user_name }}</span>\n              <span class=\"mx-2\">•</span>\n              <span>Confidenza: {{ anomaly.confidence }}%</span>\n              <span class=\"mx-2\">•</span>\n              <span>{{ formatDate(anomaly.detected_at) }}</span>\n            </div>\n\n            <!-- Suggested Actions -->\n            <div v-if=\"anomaly.suggested_actions\" class=\"mt-3\">\n              <p class=\"text-xs font-medium text-red-800\">Azioni Suggerite:</p>\n              <ul class=\"text-xs text-red-700 mt-1 space-y-1\">\n                <li v-for=\"action in anomaly.suggested_actions\" :key=\"action\">\n                  • {{ action }}\n                </li>\n              </ul>\n            </div>\n          </div>\n\n          <button @click=\"dismissAnomaly(anomaly)\"\n                  class=\"text-red-400 hover:text-red-600\">\n            <XMarkIcon class=\"w-4 h-4\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n---\n\n## 📊 **STRUTTURA FILE CRM COMPLETA**\n\n```\nfrontend/src/\n├── views/crm/\n│   ├── CRMDashboard.vue\n│   ├── ClientList.vue\n│   ├── ClientDetail.vue\n│   ├── ContactList.vue\n│   ├── ProposalList.vue\n│   ├── ProposalDetail.vue\n│   ├── ContractList.vue\n│   ├── InvoiceList.vue\n│   └── components/\n│       ├── LeadKanbanBoard.vue\n│       ├── OpportunityKanbanBoard.vue\n│       ├── ClientTable.vue\n│       ├── ProposalTable.vue\n│       ├── ClientModal.vue\n│       ├── ProposalModal.vue\n│       ├── LeadPipelineChart.vue\n│       ├── ProposalPipelineChart.vue\n│       └── filters/\n│           ├── StatusFilter.vue\n│           ├── IndustryFilter.vue\n│           ├── ClientFilter.vue\n│           ├── DateRangeFilter.vue\n│           └── ValueRangeFilter.vue\n├── components/ui/\n│   ├── KanbanBoard.vue\n│   ├── KanbanCard.vue\n│   ├── StatusBadge.vue\n│   └── ViewToggle.vue\n└── components/timesheet/\n    ├── AnomalyDetector.vue\n    ├── BulkApprovalActions.vue\n    └── TimesheetNotifications.vue\n```\n\n---\n\n## ⚡ **FEATURES AVANZATE**\n\n### **🤖 AI Integration:**\n- Anomaly detection per timesheet\n- Lead scoring automatico\n- Proposal success prediction\n- Resource allocation optimization\n\n### **📱 Real-time Features:**\n- Notifiche in-app live\n- Status updates real-time\n- Collaborative editing\n\n### **📊 Analytics:**\n- Pipeline conversion rates\n- Team productivity metrics\n- Revenue forecasting\n- Client satisfaction tracking\n\n### **🔐 Security:**\n- Role-based permissions\n- Data encryption\n- Audit logging\n- GDPR compliance\n\n**Procediamo con l'implementazione?**\n"}