{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task-3/log.md"}, "originalCode": "# Task 3.1 - Timesheet Management System - Log Implementazione\n\n## 📋 **Panoramica**\nImplementazione del sistema di gestione timesheet con workflow di approvazione mensile e integrazione CRM/billing (Task 3.1 + 4).\n\n---\n\n## 🗓️ **Log Cronologico**\n\n### **2024-06-02 - Fase 1: Database & Modelli**\n\n#### **✅ COMPLETATO - Estensione Modelli Database**\n\n**1. Rinominazione Timesheet → TimesheetEntry**\n- **PRIMA**: `class Timesheet` (singola entry ore)\n- **DOPO**: `class TimesheetEntry` con `__tablename__ = 'timesheet_entries'`\n- **MOTIVO**: Chiarire che rappresenta una singola voce, non un timesheet completo\n\n**2. Nuovi Modelli Aggiunti**\n```python\n# Approvazione mensile timesheet\nclass MonthlyTimesheet(db.Model):\n    __tablename__ = 'monthly_timesheets'\n    # user_id, year, month, status (draft/submitted/approved/rejected)\n    # submission_date, approval_date, approved_by, rejection_reason\n\n# Richieste ferie/permessi/smartworking  \nclass TimeOffRequest(db.Model):\n    __tablename__ = 'time_off_requests'\n    # user_id, request_type, start_date, end_date, status\n    # notes, approved_by, rejection_reason\n\n# Contratti clienti per fatturazione\nclass Contract(db.Model):\n    __tablename__ = 'contracts'\n    # client_id, contract_number, title, contract_type\n    # hourly_rate, budget_hours, start_date, end_date\n\n# Fatturazione per periodo\nclass Invoice(db.Model):\n    __tablename__ = 'invoices'\n    # client_id, invoice_number, billing_period_start/end\n    # issue_date, due_date, status, totali\n\nclass InvoiceLine(db.Model):\n    __tablename__ = 'invoice_lines'\n    # invoice_id, project_id, contract_id\n    # description, total_hours, hourly_rate, total_amount\n```\n\n**3. Estensione TimesheetEntry**\n```python\n# Nuovi campi aggiunti per Task 3.1 + 4\nmonthly_timesheet_id = db.Column(db.Integer, db.ForeignKey('monthly_timesheets.id'), nullable=True)\nbillable = db.Column(db.Boolean, default=False)\nbilling_rate = db.Column(db.Float, nullable=True)\ncontract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)\ninvoice_line_id = db.Column(db.Integer, db.ForeignKey('invoice_lines.id'), nullable=True)\nbilling_status = db.Column(db.String(20), default='unbilled')\n```\n\n**4. Estensione Project**\n```python\n# Collegamento progetto-contratto\ncontract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)  # Nullable per progetti interni\ncontract = db.relationship('Contract', backref='projects')\n```\n\n#### **✅ COMPLETATO - Migrazione Database**\n\n**1. Tabelle Create**\n- ✅ `monthly_timesheets` - Approvazioni mensili\n- ✅ `time_off_requests` - Richieste ferie/permessi\n- ✅ `contracts` - Contratti clienti\n- ✅ `invoices` + `invoice_lines` - Fatturazione\n\n**2. Tabelle Estese**\n- ✅ `timesheet_entries` - Campi billing aggiunti\n- ✅ `project` - Campo `contract_id` aggiunto\n\n**3. Pulizia Tabelle Duplicate**\n- ❌ `time_off_request` (eliminata)\n- ❌ `timesheet_entry` (eliminata)\n- ⚠️ `timesheets`, `timesheet_entries` duplicate (parzialmente pulite)\n\n#### **✅ COMPLETATO - Aggiornamento Import/Riferimenti**\n\n**File Aggiornati per Rinominazione Timesheet → TimesheetEntry:**\n- ✅ `backend/blueprints/api/dashboard.py`\n- ✅ `backend/blueprints/api/personnel_allocation.py`\n- ✅ `backend/blueprints/api/personnel/allocation.py`\n- ✅ `backend/blueprints/api/timesheets.py`\n- ✅ `backend/utils/cost_calculator.py`\n- ✅ `backend/seed_data.py`\n- ✅ `backend/models.py` (relazioni User e Project)\n\n**Comandi Utilizzati:**\n```bash\n# Sostituzione automatica riferimenti\nsed -i 's/Timesheet\\./TimesheetEntry\\./g' file.py\nsed -i 's/Timesheet(/TimesheetEntry(/g' file.py\nsed -i 's/\\.join(Timesheet)/\\.join(TimesheetEntry)/g' file.py\n```\n\n#### **✅ COMPLETATO - Script Migrazione**\n\n**File**: `backend/db_update.py`\n- ✅ Funzione `setup_timesheet_module()` - Setup completo\n- ✅ Funzione `create_timesheet_tables()` - Crea nuove tabelle\n- ✅ Funzione `extend_timesheet_table()` - Estende timesheet_entries\n- ✅ Funzione `extend_project_table()` - Aggiunge contract_id a project\n- ✅ Funzione `cleanup_duplicate_tables()` - Pulizia tabelle duplicate\n- ✅ Funzione `populate_timesheet_sample_data()` - Dati esempio\n\n**Comandi Disponibili:**\n```bash\npython db_update.py setup_timesheet        # Setup completo\npython db_update.py create_timesheet_tables # Solo nuove tabelle\npython db_update.py extend_timesheet       # Solo estensione timesheet\npython db_update.py extend_project         # Solo estensione project\npython db_update.py cleanup_duplicates     # Pulizia duplicati\n```\n\n---\n\n## 🔄 **Workflow Implementato**\n\n### **1. Timesheet Entry Workflow**\n```\nDipendente → Inserisce ore giornaliere → TimesheetEntry (status: pending)\n                                      ↓\nManager → Fine mese → Crea MonthlyTimesheet → Approva/Rifiuta tutte le entry del mese\n```\n\n### **2. Time-off Request Workflow**\n```\nDipendente → Richiede ferie/permessi → TimeOffRequest (status: pending)\n                                    ↓\nManager/HR → Approva/Rifiuta immediatamente\n```\n\n### **3. Billing Workflow**\n```\nProgetto → Collegato a Contract (se esterno)\n         ↓\nTimesheetEntry → Eredita contract_id + billing_rate\n               ↓\nFine periodo → Genera Invoice con InvoiceLine raggruppate per progetto/contratto\n```\n\n---\n\n## 📋 **TODO - Prossimi Step**\n\n### **✅ COMPLETATO - Fase 2A: TimeOffRequest API**\n\n#### **TimeOffRequest API** (`/api/time-off-requests`)\n- ✅ `GET /` - Lista richieste con filtri (user_id, type, status, date_range, year, month)\n- ✅ `POST /` - Crea nuova richiesta (con validazioni sovrapposizioni)\n- ✅ `PUT /{id}/approve` - Approva richiesta (solo manager/HR)\n- ✅ `PUT /{id}/reject` - Rifiuta richiesta (con motivo obbligatorio)\n- ✅ `GET /{id}` - Dettaglio richiesta\n- ✅ `DELETE /{id}` - Elimina richiesta (solo proprie e pending)\n\n**File Creato**: `backend/blueprints/api/timeoff_requests.py`\n**Blueprint Registrato**: `/api/time-off-requests` in `app.py`\n\n**Funzionalità Implementate:**\n- ✅ Controllo permessi granulari (`view_all_time_off`, `approve_time_off`)\n- ✅ Validazione sovrapposizioni date\n- ✅ Validazione business rules (no date passate)\n- ✅ Paginazione e filtri avanzati\n- ✅ Workflow approvazione completo\n- ✅ Gestione errori e rollback transazioni\n\n### **🔄 IN CORSO - Fase 2B: API Implementation**\n\n#### **API da Implementare:**\n\n- [ ] **MonthlyTimesheet API** (`/api/monthly-timesheets`)\n  - [ ] `GET /` - Lista monthly timesheet\n  - [ ] `POST /{id}/submit` - Sottometti per approvazione\n  - [ ] `PUT /{id}/approve` - Approva timesheet mensile\n  - [ ] `PUT /{id}/reject` - Rifiuta timesheet mensile\n\n- [ ] **Contract API** (`/api/contracts`)\n  - [ ] CRUD completo per gestione contratti\n\n- [ ] **Invoice API** (`/api/invoices`)\n  - [ ] `POST /generate` - Genera fattura da timesheet periodo\n  - [ ] CRUD per gestione fatture\n\n- [ ] **TimesheetEntry API** (estensione esistente)\n  - [ ] Aggiungere campi billing nei response\n  - [ ] Supporto per filtri contract_id, billable\n  - [ ] Backward compatibility\n\n### **📋 TODO - Fase 3: Frontend Updates**\n\n#### **Componenti Vue.js da Aggiornare:**\n- [ ] **TimesheetEntry Components**\n  - [ ] Aggiungere campi billing (billable, billing_rate)\n  - [ ] Collegamento a contratti\n  - [ ] Stato fatturazione\n\n- [ ] **Nuovi Componenti da Creare:**\n  - [ ] `TimeOffRequestForm.vue` - Form richiesta ferie\n  - [ ] `TimeOffRequestList.vue` - Lista richieste\n  - [ ] `MonthlyTimesheetApproval.vue` - Approvazione mensile\n  - [ ] `ContractManagement.vue` - Gestione contratti\n  - [ ] `InvoiceGeneration.vue` - Generazione fatture\n\n#### **API Calls da Aggiornare:**\n- [ ] Aggiornare chiamate `/api/timesheets` → `/api/timesheet-entries`\n- [ ] Gestire nuovi campi nei form timesheet\n- [ ] Implementare workflow approvazione\n\n### **📋 TODO - Fase 4: Testing**\n\n#### **Test Backend:**\n- [ ] Test API TimeOffRequest\n- [ ] Test API MonthlyTimesheet  \n- [ ] Test workflow approvazione\n- [ ] Test generazione fatture\n- [ ] Test compatibilità API esistenti\n\n#### **Test Frontend:**\n- [ ] Test componenti timesheet aggiornati\n- [ ] Test nuovi componenti\n- [ ] Test workflow completo\n- [ ] Test responsive design\n\n---\n\n## 🚨 **Note Importanti**\n\n### **Breaking Changes**\n- ✅ **Risolto**: Rinominazione `Timesheet` → `TimesheetEntry` in tutti i file\n- ⚠️ **Attenzione**: API `/api/timesheets` dovrà essere aggiornata a `/api/timesheet-entries`\n\n### **Compatibilità**\n- ✅ Dati timesheet esistenti preservati\n- ✅ Campi nullable per retrocompatibilità\n- ⚠️ Frontend dovrà essere aggiornato per nuovi campi\n\n### **Performance**\n- ✅ Indici su foreign key automatici\n- ⚠️ Considerare indici su date per query timesheet mensili\n- ⚠️ Ottimizzare query fatturazione per grandi dataset\n\n---\n\n## 📊 **Metriche Implementazione**\n\n- **Modelli Aggiunti**: 5 (MonthlyTimesheet, TimeOffRequest, Contract, Invoice, InvoiceLine)\n- **Modelli Modificati**: 2 (TimesheetEntry, Project)  \n- **Tabelle Database**: 5 nuove + 2 estese\n- **File Backend Aggiornati**: 8\n- **Comandi Migrazione**: 6\n- **Tempo Stimato Implementazione**: 4-6 ore\n- **Tempo Effettivo**: 3.5 ore (Fase 1 + 2A + 2B completate)\n\n---\n\n## 📋 **AGGIORNAMENTI RECENTI**\n\n### **✅ COMPLETATO - Swagger JSON Documentation**\n\n#### **Aggiornamenti Swagger** (`/static/swagger/swagger.json`)\n- ✅ **Nuovi Tag Aggiunti:**\n  - `timesheet-entries` - Operazioni sui timesheet entries (corretto naming)\n  - `monthly-timesheets` - Operazioni sui timesheet mensili\n  - `time-off-requests` - Operazioni sulle richieste ferie/permessi\n\n- ✅ **Nuovi Path Documentati COMPLETI:**\n\n  **Time-off Requests:**\n  - `/time-off-requests/` - GET (lista con filtri), POST (crea)\n  - `/time-off-requests/{id}` - GET (dettaglio), DELETE (elimina)\n  - `/time-off-requests/{id}/approve` - PUT (approva)\n  - `/time-off-requests/{id}/reject` - PUT (rifiuta con motivo)\n\n  **Monthly Timesheets:**\n  - `/monthly-timesheets/` - GET (lista con filtri)\n  - `/monthly-timesheets/{id}` - GET (dettaglio con entries)\n  - `/monthly-timesheets/generate` - POST (genera/recupera)\n  - `/monthly-timesheets/{id}/submit` - PUT (sottometti)\n  - `/monthly-timesheets/{id}/approve` - PUT (approva)\n  - `/monthly-timesheets/{id}/reject` - PUT (rifiuta con motivo)\n  - `/monthly-timesheets/{id}/reopen` - PUT (riapri per modifiche)\n\n- ✅ **Nuovi Schemi Aggiunti:**\n  - `TimeOffRequest` - Schema completo richiesta time-off\n  - `MonthlyTimesheet` - Schema completo timesheet mensile\n  - `TimesheetEntry` - Schema aggiornato con campi billing\n\n**Funzionalità Documentate:**\n- ✅ Parametri di filtro completi per ogni endpoint\n- ✅ Validazioni e business rules documentate\n- ✅ Response codes e error handling\n- ✅ Esempi di request/response\n- ✅ Controlli permessi documentati\n- ✅ Workflow stati documentati\n", "modifiedCode": "# Task 3.1 - Timesheet Management System - Log Implementazione\n\n## 📋 **Panoramica**\nImplementazione del sistema di gestione timesheet con workflow di approvazione mensile e integrazione CRM/billing (Task 3.1 + 4).\n\n---\n\n## 🗓️ **Log Cronologico**\n\n### **2024-06-02 - Fase 1: Database & Modelli**\n\n#### **✅ COMPLETATO - Estensione Modelli Database**\n\n**1. Rinominazione Timesheet → TimesheetEntry**\n- **PRIMA**: `class Timesheet` (singola entry ore)\n- **DOPO**: `class TimesheetEntry` con `__tablename__ = 'timesheet_entries'`\n- **MOTIVO**: Chiarire che rappresenta una singola voce, non un timesheet completo\n\n**2. Nuovi Modelli Aggiunti**\n```python\n# Approvazione mensile timesheet\nclass MonthlyTimesheet(db.Model):\n    __tablename__ = 'monthly_timesheets'\n    # user_id, year, month, status (draft/submitted/approved/rejected)\n    # submission_date, approval_date, approved_by, rejection_reason\n\n# Richieste ferie/permessi/smartworking  \nclass TimeOffRequest(db.Model):\n    __tablename__ = 'time_off_requests'\n    # user_id, request_type, start_date, end_date, status\n    # notes, approved_by, rejection_reason\n\n# Contratti clienti per fatturazione\nclass Contract(db.Model):\n    __tablename__ = 'contracts'\n    # client_id, contract_number, title, contract_type\n    # hourly_rate, budget_hours, start_date, end_date\n\n# Fatturazione per periodo\nclass Invoice(db.Model):\n    __tablename__ = 'invoices'\n    # client_id, invoice_number, billing_period_start/end\n    # issue_date, due_date, status, totali\n\nclass InvoiceLine(db.Model):\n    __tablename__ = 'invoice_lines'\n    # invoice_id, project_id, contract_id\n    # description, total_hours, hourly_rate, total_amount\n```\n\n**3. Estensione TimesheetEntry**\n```python\n# Nuovi campi aggiunti per Task 3.1 + 4\nmonthly_timesheet_id = db.Column(db.Integer, db.ForeignKey('monthly_timesheets.id'), nullable=True)\nbillable = db.Column(db.Boolean, default=False)\nbilling_rate = db.Column(db.Float, nullable=True)\ncontract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)\ninvoice_line_id = db.Column(db.Integer, db.ForeignKey('invoice_lines.id'), nullable=True)\nbilling_status = db.Column(db.String(20), default='unbilled')\n```\n\n**4. Estensione Project**\n```python\n# Collegamento progetto-contratto\ncontract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)  # Nullable per progetti interni\ncontract = db.relationship('Contract', backref='projects')\n```\n\n#### **✅ COMPLETATO - Migrazione Database**\n\n**1. Tabelle Create**\n- ✅ `monthly_timesheets` - Approvazioni mensili\n- ✅ `time_off_requests` - Richieste ferie/permessi\n- ✅ `contracts` - Contratti clienti\n- ✅ `invoices` + `invoice_lines` - Fatturazione\n\n**2. Tabelle Estese**\n- ✅ `timesheet_entries` - Campi billing aggiunti\n- ✅ `project` - Campo `contract_id` aggiunto\n\n**3. Pulizia Tabelle Duplicate**\n- ❌ `time_off_request` (eliminata)\n- ❌ `timesheet_entry` (eliminata)\n- ⚠️ `timesheets`, `timesheet_entries` duplicate (parzialmente pulite)\n\n#### **✅ COMPLETATO - Aggiornamento Import/Riferimenti**\n\n**File Aggiornati per Rinominazione Timesheet → TimesheetEntry:**\n- ✅ `backend/blueprints/api/dashboard.py`\n- ✅ `backend/blueprints/api/personnel_allocation.py`\n- ✅ `backend/blueprints/api/personnel/allocation.py`\n- ✅ `backend/blueprints/api/timesheets.py`\n- ✅ `backend/utils/cost_calculator.py`\n- ✅ `backend/seed_data.py`\n- ✅ `backend/models.py` (relazioni User e Project)\n\n**Comandi Utilizzati:**\n```bash\n# Sostituzione automatica riferimenti\nsed -i 's/Timesheet\\./TimesheetEntry\\./g' file.py\nsed -i 's/Timesheet(/TimesheetEntry(/g' file.py\nsed -i 's/\\.join(Timesheet)/\\.join(TimesheetEntry)/g' file.py\n```\n\n#### **✅ COMPLETATO - Script Migrazione**\n\n**File**: `backend/db_update.py`\n- ✅ Funzione `setup_timesheet_module()` - Setup completo\n- ✅ Funzione `create_timesheet_tables()` - Crea nuove tabelle\n- ✅ Funzione `extend_timesheet_table()` - Estende timesheet_entries\n- ✅ Funzione `extend_project_table()` - Aggiunge contract_id a project\n- ✅ Funzione `cleanup_duplicate_tables()` - Pulizia tabelle duplicate\n- ✅ Funzione `populate_timesheet_sample_data()` - Dati esempio\n\n**Comandi Disponibili:**\n```bash\npython db_update.py setup_timesheet        # Setup completo\npython db_update.py create_timesheet_tables # Solo nuove tabelle\npython db_update.py extend_timesheet       # Solo estensione timesheet\npython db_update.py extend_project         # Solo estensione project\npython db_update.py cleanup_duplicates     # Pulizia duplicati\n```\n\n---\n\n## 🔄 **Workflow Implementato**\n\n### **1. Timesheet Entry Workflow**\n```\nDipendente → Inserisce ore giornaliere → TimesheetEntry (status: pending)\n                                      ↓\nManager → Fine mese → Crea MonthlyTimesheet → Approva/Rifiuta tutte le entry del mese\n```\n\n### **2. Time-off Request Workflow**\n```\nDipendente → Richiede ferie/permessi → TimeOffRequest (status: pending)\n                                    ↓\nManager/HR → Approva/Rifiuta immediatamente\n```\n\n### **3. Billing Workflow**\n```\nProgetto → Collegato a Contract (se esterno)\n         ↓\nTimesheetEntry → Eredita contract_id + billing_rate\n               ↓\nFine periodo → Genera Invoice con InvoiceLine raggruppate per progetto/contratto\n```\n\n---\n\n## 📋 **TODO - Prossimi Step**\n\n### **✅ COMPLETATO - Fase 2A: TimeOffRequest API**\n\n#### **TimeOffRequest API** (`/api/time-off-requests`)\n- ✅ `GET /` - Lista richieste con filtri (user_id, type, status, date_range, year, month)\n- ✅ `POST /` - Crea nuova richiesta (con validazioni sovrapposizioni)\n- ✅ `PUT /{id}/approve` - Approva richiesta (solo manager/HR)\n- ✅ `PUT /{id}/reject` - Rifiuta richiesta (con motivo obbligatorio)\n- ✅ `GET /{id}` - Dettaglio richiesta\n- ✅ `DELETE /{id}` - Elimina richiesta (solo proprie e pending)\n\n**File Creato**: `backend/blueprints/api/timeoff_requests.py`\n**Blueprint Registrato**: `/api/time-off-requests` in `app.py`\n\n**Funzionalità Implementate:**\n- ✅ Controllo permessi granulari (`view_all_time_off`, `approve_time_off`)\n- ✅ Validazione sovrapposizioni date\n- ✅ Validazione business rules (no date passate)\n- ✅ Paginazione e filtri avanzati\n- ✅ Workflow approvazione completo\n- ✅ Gestione errori e rollback transazioni\n\n### **🔄 IN CORSO - Fase 2B: API Implementation**\n\n#### **API da Implementare:**\n\n- [ ] **MonthlyTimesheet API** (`/api/monthly-timesheets`)\n  - [ ] `GET /` - Lista monthly timesheet\n  - [ ] `POST /{id}/submit` - Sottometti per approvazione\n  - [ ] `PUT /{id}/approve` - Approva timesheet mensile\n  - [ ] `PUT /{id}/reject` - Rifiuta timesheet mensile\n\n- [ ] **Contract API** (`/api/contracts`)\n  - [ ] CRUD completo per gestione contratti\n\n- [ ] **Invoice API** (`/api/invoices`)\n  - [ ] `POST /generate` - Genera fattura da timesheet periodo\n  - [ ] CRUD per gestione fatture\n\n- [ ] **TimesheetEntry API** (estensione esistente)\n  - [ ] Aggiungere campi billing nei response\n  - [ ] Supporto per filtri contract_id, billable\n  - [ ] Backward compatibility\n\n### **📋 TODO - Fase 3: Frontend Updates**\n\n#### **Componenti Vue.js da Aggiornare:**\n- [ ] **TimesheetEntry Components**\n  - [ ] Aggiungere campi billing (billable, billing_rate)\n  - [ ] Collegamento a contratti\n  - [ ] Stato fatturazione\n\n- [ ] **Nuovi Componenti da Creare:**\n  - [ ] `TimeOffRequestForm.vue` - Form richiesta ferie\n  - [ ] `TimeOffRequestList.vue` - Lista richieste\n  - [ ] `MonthlyTimesheetApproval.vue` - Approvazione mensile\n  - [ ] `ContractManagement.vue` - Gestione contratti\n  - [ ] `InvoiceGeneration.vue` - Generazione fatture\n\n#### **API Calls da Aggiornare:**\n- [ ] Aggiornare chiamate `/api/timesheets` → `/api/timesheet-entries`\n- [ ] Gestire nuovi campi nei form timesheet\n- [ ] Implementare workflow approvazione\n\n### **📋 TODO - Fase 4: Testing**\n\n#### **Test Backend:**\n- [ ] Test API TimeOffRequest\n- [ ] Test API MonthlyTimesheet  \n- [ ] Test workflow approvazione\n- [ ] Test generazione fatture\n- [ ] Test compatibilità API esistenti\n\n#### **Test Frontend:**\n- [ ] Test componenti timesheet aggiornati\n- [ ] Test nuovi componenti\n- [ ] Test workflow completo\n- [ ] Test responsive design\n\n---\n\n## 🚨 **Note Importanti**\n\n### **Breaking Changes**\n- ✅ **Risolto**: Rinominazione `Timesheet` → `TimesheetEntry` in tutti i file\n- ⚠️ **Attenzione**: API `/api/timesheets` dovrà essere aggiornata a `/api/timesheet-entries`\n\n### **Compatibilità**\n- ✅ Dati timesheet esistenti preservati\n- ✅ Campi nullable per retrocompatibilità\n- ⚠️ Frontend dovrà essere aggiornato per nuovi campi\n\n### **Performance**\n- ✅ Indici su foreign key automatici\n- ⚠️ Considerare indici su date per query timesheet mensili\n- ⚠️ Ottimizzare query fatturazione per grandi dataset\n\n---\n\n## 📊 **Metriche Implementazione**\n\n- **Modelli Aggiunti**: 5 (MonthlyTimesheet, TimeOffRequest, Contract, Invoice, InvoiceLine)\n- **Modelli Modificati**: 2 (TimesheetEntry, Project)  \n- **Tabelle Database**: 5 nuove + 2 estese\n- **File Backend Aggiornati**: 8\n- **Comandi Migrazione**: 6\n- **Tempo Stimato Implementazione**: 4-6 ore\n- **Tempo Effettivo**: 3.5 ore (Fase 1 + 2A + 2B completate)\n\n---\n\n## 📋 **AGGIORNAMENTI RECENTI**\n\n### **✅ COMPLETATO - Swagger JSON Documentation**\n\n#### **Aggiornamenti Swagger** (`/static/swagger/swagger.json`)\n- ✅ **Nuovi Tag Aggiunti:**\n  - `timesheet-entries` - Operazioni sui timesheet entries (corretto naming)\n  - `monthly-timesheets` - Operazioni sui timesheet mensili\n  - `time-off-requests` - Operazioni sulle richieste ferie/permessi\n\n- ✅ **Nuovi Path Documentati COMPLETI:**\n\n  **Time-off Requests:**\n  - `/time-off-requests/` - GET (lista con filtri), POST (crea)\n  - `/time-off-requests/{id}` - GET (dettaglio), DELETE (elimina)\n  - `/time-off-requests/{id}/approve` - PUT (approva)\n  - `/time-off-requests/{id}/reject` - PUT (rifiuta con motivo)\n\n  **Monthly Timesheets:**\n  - `/monthly-timesheets/` - GET (lista con filtri)\n  - `/monthly-timesheets/{id}` - GET (dettaglio con entries)\n  - `/monthly-timesheets/generate` - POST (genera/recupera)\n  - `/monthly-timesheets/{id}/submit` - PUT (sottometti)\n  - `/monthly-timesheets/{id}/approve` - PUT (approva)\n  - `/monthly-timesheets/{id}/reject` - PUT (rifiuta con motivo)\n  - `/monthly-timesheets/{id}/reopen` - PUT (riapri per modifiche)\n\n- ✅ **Nuovi Schemi Aggiunti:**\n  - `TimeOffRequest` - Schema completo richiesta time-off\n  - `MonthlyTimesheet` - Schema completo timesheet mensile\n  - `TimesheetEntry` - Schema aggiornato con campi billing\n\n- ✅ **Security Schemes Aggiunti:**\n  - `cookieAuth` - Autenticazione base con cookie di sessione\n  - `viewAllTimeOff` - Permesso visualizzazione richieste di tutti\n  - `approveTimeOff` - Permesso approvazione/rifiuto richieste\n  - `viewAllTimesheets` - Permesso visualizzazione timesheet di tutti\n  - `approveTimesheets` - Permesso approvazione timesheet mensili\n  - `manageTimesheets` - Permesso gestione timesheet altri utenti\n\n**Funzionalità Documentate:**\n- ✅ Parametri di filtro completi per ogni endpoint\n- ✅ Validazioni e business rules documentate\n- ✅ Response codes e error handling\n- ✅ Esempi di request/response\n- ✅ **Dependencies e permessi documentati per ogni endpoint**\n- ✅ Workflow stati documentati\n- ✅ **Security requirements specifici per operazioni privilegiate**\n"}