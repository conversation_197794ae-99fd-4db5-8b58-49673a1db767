{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/contacts.py"}, "modifiedCode": "\"\"\"\nAPI Blueprint per la gestione dei contatti clienti.\nTask 4 - CRM Integration\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, or_\n\nfrom models import Contact, Client, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_contacts = Blueprint('api_contacts', __name__)\n\n\n@api_contacts.route('/', methods=['GET'])\n@login_required\ndef get_contacts():\n    \"\"\"Recupera lista contatti con filtri\"\"\"\n    try:\n        # Parametri filtro\n        client_id = request.args.get('client_id', type=int)\n        search = request.args.get('search')  # Ricerca in nome/email\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = Contact.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_clients'):\n            return api_response(False, 'Non hai i permessi per visualizzare i contatti', status_code=403)\n        \n        # Applica filtri\n        if client_id:\n            query = query.filter(Contact.client_id == client_id)\n            \n        if search:\n            search_pattern = f\"%{search}%\"\n            query = query.filter(\n                or_(\n                    Contact.first_name.ilike(search_pattern),\n                    Contact.last_name.ilike(search_pattern),\n                    Contact.email.ilike(search_pattern),\n                    Contact.position.ilike(search_pattern)\n                )\n            )\n        \n        # Ordina per cognome, nome\n        query = query.order_by(Contact.last_name, Contact.first_name)\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        contacts_data = []\n        for contact in paginated.items:\n            contacts_data.append({\n                'id': contact.id,\n                'client_id': contact.client_id,\n                'client': {\n                    'id': contact.client.id,\n                    'name': contact.client.name,\n                    'company': getattr(contact.client, 'company', None)\n                } if contact.client else None,\n                'first_name': contact.first_name,\n                'last_name': contact.last_name,\n                'full_name': contact.full_name,\n                'position': contact.position,\n                'email': contact.email,\n                'phone': contact.phone,\n                'notes': contact.notes,\n                'created_at': contact.created_at.isoformat(),\n                'updated_at': contact.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'contacts': contacts_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperati {len(contacts_data)} contatti\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_contacts.route('/', methods=['POST'])\*************\n@login_required\ndef create_contact():\n    \"\"\"Crea un nuovo contatto\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_clients'):\n            return api_response(False, 'Non hai i permessi per creare contatti', status_code=403)\n        \n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['client_id', 'first_name', 'last_name']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        # Verifica che il cliente esista\n        client = Client.query.get(data['client_id'])\n        if not client:\n            return api_response(\n                False,\n                'Cliente non trovato',\n                status_code=404\n            )\n        \n        # Verifica unicità email se fornita\n        if data.get('email'):\n            existing_contact = Contact.query.filter(Contact.email == data['email']).first()\n            if existing_contact:\n                return api_response(\n                    False,\n                    f'Esiste già un contatto con email {data[\"email\"]}',\n                    status_code=400\n                )\n        \n        # Crea nuovo contatto\n        contact = Contact(\n            client_id=data['client_id'],\n            first_name=data['first_name'],\n            last_name=data['last_name'],\n            position=data.get('position'),\n            email=data.get('email'),\n            phone=data.get('phone'),\n            notes=data.get('notes', '')\n        )\n        \n        db.session.add(contact)\n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': contact.id,\n                'client_id': contact.client_id,\n                'client': {\n                    'id': contact.client.id,\n                    'name': contact.client.name\n                },\n                'first_name': contact.first_name,\n                'last_name': contact.last_name,\n                'full_name': contact.full_name,\n                'position': contact.position,\n                'email': contact.email,\n                'phone': contact.phone,\n                'notes': contact.notes,\n                'created_at': contact.created_at.isoformat()\n            },\n            message='Contatto creato con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_contacts.route('/<int:contact_id>', methods=['GET'])\n@login_required\ndef get_contact(contact_id):\n    \"\"\"Recupera dettaglio singolo contatto\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_clients'):\n            return api_response(False, 'Non hai i permessi per visualizzare i contatti', status_code=403)\n        \n        contact = Contact.query.get_or_404(contact_id)\n        \n        return api_response(\n            data={\n                'id': contact.id,\n                'client_id': contact.client_id,\n                'client': {\n                    'id': contact.client.id,\n                    'name': contact.client.name,\n                    'company': getattr(contact.client, 'company', None),\n                    'industry': contact.client.industry,\n                    'website': contact.client.website\n                } if contact.client else None,\n                'first_name': contact.first_name,\n                'last_name': contact.last_name,\n                'full_name': contact.full_name,\n                'position': contact.position,\n                'email': contact.email,\n                'phone': contact.phone,\n                'notes': contact.notes,\n                'created_at': contact.created_at.isoformat(),\n                'updated_at': contact.updated_at.isoformat()\n            },\n            message=\"Dettaglio contatto recuperato con successo\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_contacts.route('/<int:contact_id>', methods=['PUT'])\*************\n@login_required\ndef update_contact(contact_id):\n    \"\"\"Aggiorna un contatto esistente\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_clients'):\n            return api_response(False, 'Non hai i permessi per modificare contatti', status_code=403)\n        \n        contact = Contact.query.get_or_404(contact_id)\n        data = request.get_json()\n        \n        # Aggiorna campi se forniti\n        if 'first_name' in data:\n            contact.first_name = data['first_name']\n        \n        if 'last_name' in data:\n            contact.last_name = data['last_name']\n        \n        if 'position' in data:\n            contact.position = data['position']\n        \n        if 'email' in data:\n            # Verifica unicità email se cambiata\n            if data['email'] != contact.email:\n                existing_contact = Contact.query.filter(\n                    and_(Contact.email == data['email'], Contact.id != contact.id)\n                ).first()\n                if existing_contact:\n                    return api_response(\n                        False,\n                        f'Esiste già un contatto con email {data[\"email\"]}',\n                        status_code=400\n                    )\n            contact.email = data['email']\n        \n        if 'phone' in data:\n            contact.phone = data['phone']\n        \n        if 'notes' in data:\n            contact.notes = data['notes']\n        \n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': contact.id,\n                'first_name': contact.first_name,\n                'last_name': contact.last_name,\n                'full_name': contact.full_name,\n                'position': contact.position,\n                'email': contact.email,\n                'phone': contact.phone,\n                'updated_at': contact.updated_at.isoformat()\n            },\n            message='Contatto aggiornato con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_contacts.route('/<int:contact_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_contact(contact_id):\n    \"\"\"Elimina un contatto\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_clients'):\n            return api_response(False, 'Non hai i permessi per eliminare contatti', status_code=403)\n        \n        contact = Contact.query.get_or_404(contact_id)\n        \n        contact_name = contact.full_name\n        db.session.delete(contact)\n        db.session.commit()\n        \n        return api_response(\n            message=f'Contatto {contact_name} eliminato con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n"}