{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db_update.py"}, "originalCode": "import sys\nimport os\nsys.path.append(os.path.dirname(os.path.abspath(__file__)))\n\nfrom app import create_app\nfrom extensions import db\nimport sqlalchemy as sa\n\n# Crea l'applicazione\napp = create_app()\n\ndef show_tables():\n    \"\"\"Mostra tutte le tabelle nel database\"\"\"\n    with app.app_context():\n        inspector = sa.inspect(db.engine)\n        tables = inspector.get_table_names()\n        print(\"\\nTabelle nel database:\")\n        for table in sorted(tables):\n            print(f\"- {table}\")\n        print(\"\")\n\ndef execute_sql(sql_statement):\n    \"\"\"Esegue uno statement SQL\"\"\"\n    with app.app_context():\n        try:\n            db.session.execute(sa.text(sql_statement))\n            db.session.commit()\n            print(f\"SQL eseguito con successo: {sql_statement[:50]}...\")\n            return True\n        except Exception as e:\n            db.session.rollback()\n            print(f\"Errore durante l'esecuzione SQL: {e}\")\n            return False\n\ndef run_sql_file(filename):\n    \"\"\"Esegue tutti gli statement SQL da un file\"\"\"\n    try:\n        with open(filename, 'r') as file:\n            sql_script = file.read()\n            statements = sql_script.split(';')\n            success_count = 0\n\n            for statement in statements:\n                if statement.strip():\n                    if execute_sql(statement):\n                        success_count += 1\n\n            print(f\"\\nEseguiti {success_count} statements SQL da {filename}\")\n            return True\n    except Exception as e:\n        print(f\"Errore durante la lettura/esecuzione del file SQL: {e}\")\n        return False\n\ndef add_start_date_to_task():\n    \"\"\"Aggiunge il campo start_date alla tabella task\"\"\"\n    with app.app_context():\n        try:\n            # Verifica se la colonna esiste già\n            inspector = sa.inspect(db.engine)\n            columns = inspector.get_columns('task')\n            column_names = [col['name'] for col in columns]\n\n            if 'start_date' in column_names:\n                print(\"Campo start_date già presente nella tabella task\")\n                return True\n\n            # Aggiungi la colonna start_date\n            sql = \"ALTER TABLE task ADD COLUMN start_date DATE\"\n            db.session.execute(sa.text(sql))\n            db.session.commit()\n            print(\"Campo start_date aggiunto con successo alla tabella task\")\n            return True\n        except Exception as e:\n            db.session.rollback()\n            print(f\"Errore durante l'aggiunta del campo start_date: {e}\")\n            return False\n\ndef create_hr_tables():\n    \"\"\"Crea le nuove tabelle HR: departments, user_profiles, user_skills_detailed\"\"\"\n    with app.app_context():\n        try:\n            inspector = sa.inspect(db.engine)\n            existing_tables = inspector.get_table_names()\n\n            print(\"🏗️  Creazione tabelle HR...\")\n\n            # 1. Tabella departments\n            if 'departments' not in existing_tables:\n                sql_departments = \"\"\"\n                CREATE TABLE departments (\n                    id SERIAL PRIMARY KEY,\n                    name VARCHAR(100) NOT NULL UNIQUE,\n                    description TEXT,\n                    manager_id INTEGER,\n                    parent_id INTEGER,\n                    budget FLOAT DEFAULT 0.0,\n                    is_active BOOLEAN DEFAULT TRUE,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREIGN KEY (manager_id) REFERENCES \"user\" (id),\n                    FOREIGN KEY (parent_id) REFERENCES departments (id)\n                )\n                \"\"\"\n                db.session.execute(sa.text(sql_departments))\n                print(\"  ✅ Tabella 'departments' creata\")\n            else:\n                print(\"  ⚠️  Tabella 'departments' già esistente\")\n\n            # 2. Tabella user_profiles\n            if 'user_profiles' not in existing_tables:\n                sql_user_profiles = \"\"\"\n                CREATE TABLE user_profiles (\n                    id SERIAL PRIMARY KEY,\n                    user_id INTEGER NOT NULL UNIQUE,\n                    employee_id VARCHAR(20) UNIQUE,\n                    job_title VARCHAR(100),\n                    birth_date DATE,\n                    address TEXT,\n                    emergency_contact_name VARCHAR(100),\n                    emergency_contact_phone VARCHAR(20),\n                    emergency_contact_relationship VARCHAR(50),\n                    employment_type VARCHAR(50) DEFAULT 'full_time',\n                    work_location VARCHAR(100),\n                    salary FLOAT,\n                    salary_currency VARCHAR(3) DEFAULT 'EUR',\n                    probation_end_date DATE,\n                    contract_end_date DATE,\n                    notice_period_days INTEGER DEFAULT 30,\n                    weekly_hours FLOAT DEFAULT 40.0,\n                    daily_hours FLOAT DEFAULT 8.0,\n                    profile_completion FLOAT DEFAULT 0.0,\n                    notes TEXT,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREIGN KEY (user_id) REFERENCES \"user\" (id)\n                )\n                \"\"\"\n                db.session.execute(sa.text(sql_user_profiles))\n                print(\"  ✅ Tabella 'user_profiles' creata\")\n            else:\n                print(\"  ⚠️  Tabella 'user_profiles' già esistente\")\n\n            # 3. Tabella user_skills_detailed\n            if 'user_skills_detailed' not in existing_tables:\n                sql_user_skills_detailed = \"\"\"\n                CREATE TABLE user_skills_detailed (\n                    id SERIAL PRIMARY KEY,\n                    user_id INTEGER NOT NULL,\n                    skill_id INTEGER NOT NULL,\n                    proficiency_level INTEGER DEFAULT 1,\n                    years_experience FLOAT DEFAULT 0.0,\n                    is_certified BOOLEAN DEFAULT FALSE,\n                    certification_name VARCHAR(100),\n                    certification_date DATE,\n                    certification_expiry DATE,\n                    self_assessed BOOLEAN DEFAULT TRUE,\n                    manager_assessed BOOLEAN DEFAULT FALSE,\n                    manager_assessment_date DATE,\n                    notes TEXT,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREIGN KEY (user_id) REFERENCES \"user\" (id),\n                    FOREIGN KEY (skill_id) REFERENCES skill (id),\n                    UNIQUE (user_id, skill_id)\n                )\n                \"\"\"\n                db.session.execute(sa.text(sql_user_skills_detailed))\n                print(\"  ✅ Tabella 'user_skills_detailed' creata\")\n            else:\n                print(\"  ⚠️  Tabella 'user_skills_detailed' già esistente\")\n\n            # 4. Aggiungi campo department_id alla tabella user se non esiste\n            user_columns = inspector.get_columns('user')\n            user_column_names = [col['name'] for col in user_columns]\n\n            if 'department_id' not in user_column_names:\n                sql_add_department_id = 'ALTER TABLE \"user\" ADD COLUMN department_id INTEGER REFERENCES departments(id)'\n                db.session.execute(sa.text(sql_add_department_id))\n                print(\"  ✅ Campo 'department_id' aggiunto alla tabella 'user'\")\n            else:\n                print(\"  ⚠️  Campo 'department_id' già presente nella tabella 'user'\")\n\n            db.session.commit()\n            print(\"🎉 Tabelle HR create con successo!\")\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante la creazione delle tabelle HR: {e}\")\n            return False\n\ndef migrate_user_skills():\n    \"\"\"Migra i dati dalla tabella user_skills alla nuova user_skills_detailed\"\"\"\n    with app.app_context():\n        try:\n            print(\"🔄 Migrazione competenze utenti...\")\n\n            # Verifica se ci sono dati da migrare\n            result = db.session.execute(sa.text(\"SELECT COUNT(*) FROM user_skills\")).fetchone()\n            old_skills_count = result[0] if result else 0\n\n            if old_skills_count == 0:\n                print(\"  ⚠️  Nessuna competenza da migrare dalla tabella user_skills\")\n                return True\n\n            # Migra i dati\n            migrate_sql = \"\"\"\n            INSERT INTO user_skills_detailed (user_id, skill_id, proficiency_level, self_assessed, created_at)\n            SELECT user_id, skill_id, 3, 1, CURRENT_TIMESTAMP\n            FROM user_skills\n            WHERE NOT EXISTS (\n                SELECT 1 FROM user_skills_detailed\n                WHERE user_skills_detailed.user_id = user_skills.user_id\n                AND user_skills_detailed.skill_id = user_skills.skill_id\n            )\n            \"\"\"\n\n            result = db.session.execute(sa.text(migrate_sql))\n            migrated_count = result.rowcount\n\n            db.session.commit()\n            print(f\"  ✅ Migrate {migrated_count} competenze dalla tabella user_skills\")\n            print(\"  ℹ️  Le competenze sono state impostate con livello 3 (Intermedio) di default\")\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante la migrazione delle competenze: {e}\")\n            return False\n\ndef populate_orgchart_data():\n    \"\"\"Popola il database con dati di esempio per l'organigramma\"\"\"\n    with app.app_context():\n        try:\n            print(\"🏢 Popolamento dati organigramma...\")\n\n            # Verifica se ci sono già dipartimenti\n            result = db.session.execute(sa.text(\"SELECT COUNT(*) FROM departments\")).fetchone()\n            dept_count = result[0] if result else 0\n\n            if dept_count > 0:\n                print(f\"  ⚠️  Trovati {dept_count} dipartimenti esistenti. Aggiorno solo se necessario...\")\n\n            # 1. Crea dipartimenti principali\n            departments_data = [\n                # Dipartimenti di primo livello\n                (\"Direzione Generale\", \"Direzione e coordinamento strategico aziendale\", None, None, 500000.0),\n                (\"IT & Technology\", \"Sviluppo software e infrastruttura tecnologica\", None, None, 200000.0),\n                (\"Risorse Umane\", \"Gestione del personale e sviluppo organizzativo\", None, None, 80000.0),\n                (\"Amministrazione\", \"Gestione amministrativa e finanziaria\", None, None, 120000.0),\n                (\"Commerciale\", \"Vendite e sviluppo business\", None, None, 150000.0),\n                (\"Marketing\", \"Marketing e comunicazione\", None, None, 100000.0),\n\n                # Sottidipartimenti IT\n                (\"Development\", \"Team di sviluppo software\", None, 2, 120000.0),\n                (\"Infrastructure\", \"Gestione infrastruttura e DevOps\", None, 2, 80000.0),\n                (\"QA & Testing\", \"Quality Assurance e testing\", None, 2, 60000.0),\n\n                # Sottosottidipartimenti Development\n                (\"Frontend Team\", \"Sviluppo interfacce utente\", None, 7, 50000.0),\n                (\"Backend Team\", \"Sviluppo servizi e API\", None, 7, 70000.0),\n                (\"Mobile Team\", \"Sviluppo applicazioni mobile\", None, 7, 40000.0),\n\n                # Sottidipartimenti HR\n                (\"Recruiting\", \"Selezione e acquisizione talenti\", None, 3, 30000.0),\n                (\"Training & Development\", \"Formazione e sviluppo competenze\", None, 3, 25000.0),\n\n                # Sottidipartimenti Commerciale\n                (\"Sales\", \"Vendite dirette\", None, 5, 80000.0),\n                (\"Business Development\", \"Sviluppo nuovi mercati\", None, 5, 70000.0),\n                (\"Customer Success\", \"Gestione clienti esistenti\", None, 5, 50000.0),\n            ]\n\n            # Inserisci dipartimenti\n            for name, description, manager_id, parent_id, budget in departments_data:\n                # Verifica se il dipartimento esiste già\n                existing = db.session.execute(\n                    sa.text(\"SELECT id FROM departments WHERE name = :name\"),\n                    {\"name\": name}\n                ).fetchone()\n\n                if not existing:\n                    insert_sql = \"\"\"\n                    INSERT INTO departments (name, description, manager_id, parent_id, budget, is_active, created_at, updated_at)\n                    VALUES (:name, :description, :manager_id, :parent_id, :budget, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)\n                    \"\"\"\n                    db.session.execute(sa.text(insert_sql), {\n                        \"name\": name,\n                        \"description\": description,\n                        \"manager_id\": manager_id,\n                        \"parent_id\": parent_id,\n                        \"budget\": budget\n                    })\n                    print(f\"  ✅ Dipartimento '{name}' creato\")\n                else:\n                    print(f\"  ⚠️  Dipartimento '{name}' già esistente\")\n\n            # 2. Crea utenti di esempio se non esistono\n            users_data = [\n                (\"mario.rossi\", \"<EMAIL>\", \"Mario\", \"Rossi\", \"admin\", \"CEO\", 1),\n                (\"anna.verdi\", \"<EMAIL>\", \"Anna\", \"Verdi\", \"manager\", \"CTO\", 2),\n                (\"luca.bianchi\", \"<EMAIL>\", \"Luca\", \"Bianchi\", \"manager\", \"HR Director\", 3),\n                (\"sara.neri\", \"<EMAIL>\", \"Sara\", \"Neri\", \"manager\", \"CFO\", 4),\n                (\"paolo.gialli\", \"<EMAIL>\", \"Paolo\", \"Gialli\", \"manager\", \"Sales Director\", 5),\n                (\"elena.blu\", \"<EMAIL>\", \"Elena\", \"Blu\", \"manager\", \"Marketing Director\", 6),\n                (\"marco.viola\", \"<EMAIL>\", \"Marco\", \"Viola\", \"manager\", \"Dev Manager\", 7),\n                (\"giulia.rosa\", \"<EMAIL>\", \"Giulia\", \"Rosa\", \"manager\", \"Infrastructure Manager\", 8),\n                (\"andrea.oro\", \"<EMAIL>\", \"Andrea\", \"Oro\", \"manager\", \"QA Manager\", 9),\n                (\"francesca.argento\", \"<EMAIL>\", \"Francesca\", \"Argento\", \"employee\", \"Frontend Lead\", 10),\n                (\"roberto.bronzo\", \"<EMAIL>\", \"Roberto\", \"Bronzo\", \"employee\", \"Backend Lead\", 11),\n                (\"chiara.rame\", \"<EMAIL>\", \"Chiara\", \"Rame\", \"employee\", \"Mobile Lead\", 12),\n                (\"davide.ferro\", \"<EMAIL>\", \"Davide\", \"Ferro\", \"employee\", \"Recruiter\", 13),\n                (\"laura.acciaio\", \"<EMAIL>\", \"Laura\", \"Acciaio\", \"employee\", \"Training Manager\", 14),\n                (\"simone.titanio\", \"<EMAIL>\", \"Simone\", \"Titanio\", \"employee\", \"Sales Manager\", 15),\n                (\"valentina.platino\", \"<EMAIL>\", \"Valentina\", \"Platino\", \"employee\", \"Business Dev\", 16),\n                (\"alessandro.zinco\", \"<EMAIL>\", \"Alessandro\", \"Zinco\", \"employee\", \"Customer Success\", 17),\n            ]\n\n            for username, email, first_name, last_name, role, position, dept_id in users_data:\n                # Verifica se l'utente esiste già\n                existing = db.session.execute(\n                    sa.text(\"SELECT id FROM \\\"user\\\" WHERE username = :username\"),\n                    {\"username\": username}\n                ).fetchone()\n\n                if not existing:\n                    insert_sql = \"\"\"\n                    INSERT INTO \"user\" (username, email, first_name, last_name, role, position, department_id, is_active, created_at)\n                    VALUES (:username, :email, :first_name, :last_name, :role, :position, :dept_id, TRUE, CURRENT_TIMESTAMP)\n                    \"\"\"\n                    db.session.execute(sa.text(insert_sql), {\n                        \"username\": username,\n                        \"email\": email,\n                        \"first_name\": first_name,\n                        \"last_name\": last_name,\n                        \"role\": role,\n                        \"position\": position,\n                        \"dept_id\": dept_id\n                    })\n                    print(f\"  ✅ Utente '{username}' creato\")\n                else:\n                    print(f\"  ⚠️  Utente '{username}' già esistente\")\n\n            # 3. Aggiorna i manager dei dipartimenti\n            manager_assignments = [\n                (1, 1),  # Direzione Generale -> Mario Rossi\n                (2, 2),  # IT & Technology -> Anna Verdi\n                (3, 3),  # Risorse Umane -> Luca Bianchi\n                (4, 4),  # Amministrazione -> Sara Neri\n                (5, 5),  # Commerciale -> Paolo Gialli\n                (6, 6),  # Marketing -> Elena Blu\n                (7, 7),  # Development -> Marco Viola\n                (8, 8),  # Infrastructure -> Giulia Rosa\n                (9, 9),  # QA & Testing -> Andrea Oro\n                (10, 10), # Frontend Team -> Francesca Argento\n                (11, 11), # Backend Team -> Roberto Bronzo\n                (12, 12), # Mobile Team -> Chiara Rame\n                (13, 13), # Recruiting -> Davide Ferro\n                (14, 14), # Training & Development -> Laura Acciaio\n                (15, 15), # Sales -> Simone Titanio\n                (16, 16), # Business Development -> Valentina Platino\n                (17, 17), # Customer Success -> Alessandro Zinco\n            ]\n\n            for dept_id, manager_id in manager_assignments:\n                # Verifica se l'utente esiste\n                user_exists = db.session.execute(\n                    sa.text(\"SELECT id FROM \\\"user\\\" WHERE id = :user_id\"),\n                    {\"user_id\": manager_id}\n                ).fetchone()\n\n                if user_exists:\n                    update_sql = \"UPDATE departments SET manager_id = :manager_id WHERE id = :dept_id\"\n                    db.session.execute(sa.text(update_sql), {\n                        \"manager_id\": manager_id,\n                        \"dept_id\": dept_id\n                    })\n                    print(f\"  ✅ Manager assegnato al dipartimento {dept_id}\")\n\n            db.session.commit()\n            print(\"🎉 Dati organigramma popolati con successo!\")\n\n            # Mostra statistiche\n            dept_count = db.session.execute(sa.text(\"SELECT COUNT(*) FROM departments\")).fetchone()[0]\n            user_count = db.session.execute(sa.text(\"SELECT COUNT(*) FROM \\\"user\\\"\")).fetchone()[0]\n            print(f\"📊 Statistiche: {dept_count} dipartimenti, {user_count} utenti\")\n\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante il popolamento dati organigramma: {e}\")\n            return False\n\ndef setup_timesheet_module():\n    \"\"\"Setup completo del modulo Timesheet - Task 3.1 + 4\"\"\"\n    print(\"🚀 Setup modulo Timesheet & CRM - Task 3.1 + 4\")\n    print(\"=\" * 50)\n\n    success = True\n\n    # 1. Crea le nuove tabelle\n    if not create_timesheet_tables():\n        success = False\n\n    # 2. Estendi tabella timesheet esistente\n    if not extend_timesheet_table():\n        success = False\n\n    # 3. Popola dati di esempio\n    if not populate_timesheet_sample_data():\n        success = False\n\n    if success:\n        print(\"\\n🎉 Setup modulo Timesheet completato con successo!\")\n        print(\"📋 Funzionalità disponibili:\")\n        print(\"   - MonthlyTimesheet: Approvazione mensile\")\n        print(\"   - TimeOffRequest: Ferie/permessi/smartworking\")\n        print(\"   - Contract: Contratti clienti\")\n        print(\"   - Invoice: Fatturazione per periodo\")\n        print(\"   - TimesheetEntry: Ore con billing info\")\n    else:\n        print(\"\\n❌ Setup modulo Timesheet fallito. Controlla gli errori sopra.\")\n\n    return success\n\ndef create_timesheet_tables():\n    \"\"\"Crea le nuove tabelle per Task 3.1 + 4\"\"\"\n    with app.app_context():\n        try:\n            print(\"🏗️  Creazione tabelle Timesheet & CRM...\")\n\n            # Usa SQLAlchemy per creare tutte le tabelle\n            from models import (\n                MonthlyTimesheet, TimeOffRequest, Contract,\n                Invoice, InvoiceLine\n            )\n\n            db.create_all()\n\n            # Verifica che le tabelle siano state create\n            inspector = sa.inspect(db.engine)\n            existing_tables = inspector.get_table_names()\n\n            required_tables = [\n                'monthly_timesheets',\n                'time_off_requests',\n                'contracts',\n                'invoices',\n                'invoice_lines'\n            ]\n\n            for table in required_tables:\n                if table in existing_tables:\n                    print(f\"  ✅ Tabella '{table}' creata/verificata\")\n                else:\n                    print(f\"  ❌ Tabella '{table}' NON trovata\")\n                    return False\n\n            db.session.commit()\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante la creazione tabelle Timesheet: {e}\")\n            return False\n\ndef extend_timesheet_table():\n    \"\"\"Estende la tabella timesheet_entries con nuovi campi\"\"\"\n    with app.app_context():\n        try:\n            print(\"🔧 Estensione tabella timesheet_entries...\")\n\n            inspector = sa.inspect(db.engine)\n\n            # Determina il nome della tabella (timesheet o timesheet_entries)\n            existing_tables = inspector.get_table_names()\n            table_name = 'timesheet_entries' if 'timesheet_entries' in existing_tables else 'timesheet'\n\n            columns = inspector.get_columns(table_name)\n            column_names = [col['name'] for col in columns]\n\n            # Campi da aggiungere a timesheet_entries\n            new_columns = [\n                (\"monthly_timesheet_id\", \"INTEGER\"),\n                (\"billable\", \"BOOLEAN DEFAULT FALSE\"),\n                (\"billing_rate\", \"FLOAT\"),\n                (\"contract_id\", \"INTEGER\"),\n                (\"invoice_line_id\", \"INTEGER\"),\n                (\"billing_status\", \"VARCHAR(20) DEFAULT 'unbilled'\")\n            ]\n\n            for column_name, column_type in new_columns:\n                if column_name not in column_names:\n                    sql = f\"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}\"\n                    db.session.execute(sa.text(sql))\n                    print(f\"  ✅ Campo '{column_name}' aggiunto a {table_name}\")\n                else:\n                    print(f\"  ⚠️  Campo '{column_name}' già esistente in {table_name}\")\n\n            db.session.commit()\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante l'estensione tabella timesheet: {e}\")\n            return False\n\ndef populate_timesheet_sample_data():\n    \"\"\"Popola dati di esempio per testing\"\"\"\n    with app.app_context():\n        try:\n            print(\"🌱 Popolamento dati esempio Timesheet...\")\n\n            # Verifica se ci sono già dati\n            contract_count = db.session.execute(sa.text(\"SELECT COUNT(*) FROM contracts\")).fetchone()[0]\n            if contract_count > 0:\n                print(\"  ⚠️  Dati esempio già presenti\")\n                return True\n\n            # Trova primo cliente\n            client_result = db.session.execute(sa.text(\"SELECT id FROM client LIMIT 1\")).fetchone()\n            if not client_result:\n                print(\"  ⚠️  Nessun cliente trovato, salto dati esempio\")\n                return True\n\n            client_id = client_result[0]\n\n            # Crea contratto esempio\n            contract_sql = \"\"\"\n            INSERT INTO contracts (client_id, contract_number, title, description, contract_type, hourly_rate, budget_hours, start_date, status, created_at, updated_at)\n            VALUES (:client_id, 'CTR-2024-001', 'Contratto Sviluppo Software', 'Contratto per sviluppo applicazione web', 'hourly', 80.0, 100.0, CURRENT_DATE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)\n            \"\"\"\n\n            db.session.execute(sa.text(contract_sql), {\"client_id\": client_id})\n            print(\"  ✅ Contratto esempio creato\")\n\n            db.session.commit()\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante popolamento dati esempio: {e}\")\n            return False\n\ndef cleanup_duplicate_tables():\n    \"\"\"Rimuove tabelle duplicate con CASCADE e rinomina timesheet in timesheet_entries\"\"\"\n    with app.app_context():\n        try:\n            print(\"🧹 Pulizia drastica tabelle duplicate...\")\n\n            inspector = sa.inspect(db.engine)\n            existing_tables = inspector.get_table_names()\n\n            # Step 1: Elimina TUTTE le tabelle duplicate con CASCADE\n            tables_to_drop_cascade = [\n                'timesheets',          # Duplicato con vincoli\n                'timesheet_entries',   # Duplicato vuoto\n                'timesheet_entry',     # Duplicato\n                'time_off_request'     # Duplicato (mantieni time_off_requests)\n            ]\n\n            for table in tables_to_drop_cascade:\n                if table in existing_tables:\n                    try:\n                        db.session.execute(sa.text(f\"DROP TABLE {table} CASCADE\"))\n                        print(f\"  ✅ Tabella '{table}' eliminata con CASCADE\")\n                    except Exception as e:\n                        print(f\"  ⚠️  Errore eliminazione '{table}': {e}\")\n                else:\n                    print(f\"  ⏭️  Tabella '{table}' non trovata\")\n\n            # Commit eliminazioni\n            db.session.commit()\n\n            # Step 2: Rinomina timesheet → timesheet_entries (ora che non ci sono conflitti)\n            inspector = sa.inspect(db.engine)\n            existing_tables = inspector.get_table_names()\n\n            if 'timesheet' in existing_tables and 'timesheet_entries' not in existing_tables:\n                db.session.execute(sa.text(\"ALTER TABLE timesheet RENAME TO timesheet_entries\"))\n                print(\"  ✅ Tabella 'timesheet' rinominata in 'timesheet_entries'\")\n            elif 'timesheet' in existing_tables and 'timesheet_entries' in existing_tables:\n                print(\"  ⚠️  Conflitto: entrambe 'timesheet' e 'timesheet_entries' esistono\")\n            else:\n                print(\"  ⏭️  Rinominazione non necessaria\")\n\n            db.session.commit()\n            print(\"✅ Pulizia drastica completata\")\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante pulizia drastica: {e}\")\n            return False\n\ndef setup_hr_module():\n    \"\"\"Setup completo del modulo HR\"\"\"\n    print(\"🚀 Setup modulo HR - Task 7.1\")\n    print(\"=\" * 50)\n\n    success = True\n\n    # 1. Crea le tabelle HR\n    if not create_hr_tables():\n        success = False\n\n    # 2. Migra le competenze esistenti\n    if not migrate_user_skills():\n        success = False\n\n    if success:\n        print(\"\\n🎉 Setup modulo HR completato con successo!\")\n        print(\"📋 Prossimi passi:\")\n        print(\"   - Subtask 7.2: Employee Profile Management\")\n        print(\"   - Subtask 7.3: Skills Management System\")\n        print(\"   - Subtask 7.4: Department Management Module\")\n    else:\n        print(\"\\n❌ Setup modulo HR fallito. Controlla gli errori sopra.\")\n\n    return success\n\nif __name__ == \"__main__\":\n    # Mostra le tabelle all'inizio\n    show_tables()\n\n    # Se viene passato un file come argomento, eseguilo\n    if len(sys.argv) > 1:\n        if sys.argv[1] == \"add_start_date\":\n            add_start_date_to_task()\n        elif sys.argv[1] == \"setup_hr\":\n            setup_hr_module()\n        elif sys.argv[1] == \"setup_timesheet\":\n            setup_timesheet_module()\n        elif sys.argv[1] == \"create_hr_tables\":\n            create_hr_tables()\n        elif sys.argv[1] == \"create_timesheet_tables\":\n            create_timesheet_tables()\n        elif sys.argv[1] == \"extend_timesheet\":\n            extend_timesheet_table()\n        elif sys.argv[1] == \"cleanup_duplicates\":\n            cleanup_duplicate_tables()\n        elif sys.argv[1] == \"migrate_skills\":\n            migrate_user_skills()\n        elif sys.argv[1] == \"populate_orgchart\":\n            populate_orgchart_data()\n        else:\n            run_sql_file(sys.argv[1])\n\n        # Mostra le tabelle dopo l'esecuzione\n        show_tables()\n    else:\n        print(\"Uso: python db_update.py [comando | file_sql]\")\n        print(\"\\nComandi disponibili:\")\n        print(\"  setup_timesheet     - Setup completo modulo Timesheet & CRM (Task 3.1 + 4)\")\n        print(\"  create_timesheet_tables - Crea solo le nuove tabelle Timesheet\")\n        print(\"  extend_timesheet    - Estende tabella timesheet esistente\")\n        print(\"  cleanup_duplicates  - Rimuove tabelle duplicate\")\n        print(\"  setup_hr            - Setup completo modulo HR (Task 7.1)\")\n        print(\"  create_hr_tables    - Crea solo le tabelle HR\")\n        print(\"  migrate_skills      - Migra competenze da user_skills a user_skills_detailed\")\n        print(\"  populate_orgchart   - Popola database con dati organigramma completo\")\n        print(\"  add_start_date      - Aggiunge campo start_date alla tabella task\")\n        print(\"\\nSe non viene specificato un comando, verranno mostrate solo le tabelle esistenti.\")", "modifiedCode": "import sys\nimport os\nsys.path.append(os.path.dirname(os.path.abspath(__file__)))\n\nfrom app import create_app\nfrom extensions import db\nimport sqlalchemy as sa\n\n# Crea l'applicazione\napp = create_app()\n\ndef show_tables():\n    \"\"\"Mostra tutte le tabelle nel database\"\"\"\n    with app.app_context():\n        inspector = sa.inspect(db.engine)\n        tables = inspector.get_table_names()\n        print(\"\\nTabelle nel database:\")\n        for table in sorted(tables):\n            print(f\"- {table}\")\n        print(\"\")\n\ndef execute_sql(sql_statement):\n    \"\"\"Esegue uno statement SQL\"\"\"\n    with app.app_context():\n        try:\n            db.session.execute(sa.text(sql_statement))\n            db.session.commit()\n            print(f\"SQL eseguito con successo: {sql_statement[:50]}...\")\n            return True\n        except Exception as e:\n            db.session.rollback()\n            print(f\"Errore durante l'esecuzione SQL: {e}\")\n            return False\n\ndef run_sql_file(filename):\n    \"\"\"Esegue tutti gli statement SQL da un file\"\"\"\n    try:\n        with open(filename, 'r') as file:\n            sql_script = file.read()\n            statements = sql_script.split(';')\n            success_count = 0\n\n            for statement in statements:\n                if statement.strip():\n                    if execute_sql(statement):\n                        success_count += 1\n\n            print(f\"\\nEseguiti {success_count} statements SQL da {filename}\")\n            return True\n    except Exception as e:\n        print(f\"Errore durante la lettura/esecuzione del file SQL: {e}\")\n        return False\n\ndef add_start_date_to_task():\n    \"\"\"Aggiunge il campo start_date alla tabella task\"\"\"\n    with app.app_context():\n        try:\n            # Verifica se la colonna esiste già\n            inspector = sa.inspect(db.engine)\n            columns = inspector.get_columns('task')\n            column_names = [col['name'] for col in columns]\n\n            if 'start_date' in column_names:\n                print(\"Campo start_date già presente nella tabella task\")\n                return True\n\n            # Aggiungi la colonna start_date\n            sql = \"ALTER TABLE task ADD COLUMN start_date DATE\"\n            db.session.execute(sa.text(sql))\n            db.session.commit()\n            print(\"Campo start_date aggiunto con successo alla tabella task\")\n            return True\n        except Exception as e:\n            db.session.rollback()\n            print(f\"Errore durante l'aggiunta del campo start_date: {e}\")\n            return False\n\ndef create_hr_tables():\n    \"\"\"Crea le nuove tabelle HR: departments, user_profiles, user_skills_detailed\"\"\"\n    with app.app_context():\n        try:\n            inspector = sa.inspect(db.engine)\n            existing_tables = inspector.get_table_names()\n\n            print(\"🏗️  Creazione tabelle HR...\")\n\n            # 1. Tabella departments\n            if 'departments' not in existing_tables:\n                sql_departments = \"\"\"\n                CREATE TABLE departments (\n                    id SERIAL PRIMARY KEY,\n                    name VARCHAR(100) NOT NULL UNIQUE,\n                    description TEXT,\n                    manager_id INTEGER,\n                    parent_id INTEGER,\n                    budget FLOAT DEFAULT 0.0,\n                    is_active BOOLEAN DEFAULT TRUE,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREIGN KEY (manager_id) REFERENCES \"user\" (id),\n                    FOREIGN KEY (parent_id) REFERENCES departments (id)\n                )\n                \"\"\"\n                db.session.execute(sa.text(sql_departments))\n                print(\"  ✅ Tabella 'departments' creata\")\n            else:\n                print(\"  ⚠️  Tabella 'departments' già esistente\")\n\n            # 2. Tabella user_profiles\n            if 'user_profiles' not in existing_tables:\n                sql_user_profiles = \"\"\"\n                CREATE TABLE user_profiles (\n                    id SERIAL PRIMARY KEY,\n                    user_id INTEGER NOT NULL UNIQUE,\n                    employee_id VARCHAR(20) UNIQUE,\n                    job_title VARCHAR(100),\n                    birth_date DATE,\n                    address TEXT,\n                    emergency_contact_name VARCHAR(100),\n                    emergency_contact_phone VARCHAR(20),\n                    emergency_contact_relationship VARCHAR(50),\n                    employment_type VARCHAR(50) DEFAULT 'full_time',\n                    work_location VARCHAR(100),\n                    salary FLOAT,\n                    salary_currency VARCHAR(3) DEFAULT 'EUR',\n                    probation_end_date DATE,\n                    contract_end_date DATE,\n                    notice_period_days INTEGER DEFAULT 30,\n                    weekly_hours FLOAT DEFAULT 40.0,\n                    daily_hours FLOAT DEFAULT 8.0,\n                    profile_completion FLOAT DEFAULT 0.0,\n                    notes TEXT,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREIGN KEY (user_id) REFERENCES \"user\" (id)\n                )\n                \"\"\"\n                db.session.execute(sa.text(sql_user_profiles))\n                print(\"  ✅ Tabella 'user_profiles' creata\")\n            else:\n                print(\"  ⚠️  Tabella 'user_profiles' già esistente\")\n\n            # 3. Tabella user_skills_detailed\n            if 'user_skills_detailed' not in existing_tables:\n                sql_user_skills_detailed = \"\"\"\n                CREATE TABLE user_skills_detailed (\n                    id SERIAL PRIMARY KEY,\n                    user_id INTEGER NOT NULL,\n                    skill_id INTEGER NOT NULL,\n                    proficiency_level INTEGER DEFAULT 1,\n                    years_experience FLOAT DEFAULT 0.0,\n                    is_certified BOOLEAN DEFAULT FALSE,\n                    certification_name VARCHAR(100),\n                    certification_date DATE,\n                    certification_expiry DATE,\n                    self_assessed BOOLEAN DEFAULT TRUE,\n                    manager_assessed BOOLEAN DEFAULT FALSE,\n                    manager_assessment_date DATE,\n                    notes TEXT,\n                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n                    FOREIGN KEY (user_id) REFERENCES \"user\" (id),\n                    FOREIGN KEY (skill_id) REFERENCES skill (id),\n                    UNIQUE (user_id, skill_id)\n                )\n                \"\"\"\n                db.session.execute(sa.text(sql_user_skills_detailed))\n                print(\"  ✅ Tabella 'user_skills_detailed' creata\")\n            else:\n                print(\"  ⚠️  Tabella 'user_skills_detailed' già esistente\")\n\n            # 4. Aggiungi campo department_id alla tabella user se non esiste\n            user_columns = inspector.get_columns('user')\n            user_column_names = [col['name'] for col in user_columns]\n\n            if 'department_id' not in user_column_names:\n                sql_add_department_id = 'ALTER TABLE \"user\" ADD COLUMN department_id INTEGER REFERENCES departments(id)'\n                db.session.execute(sa.text(sql_add_department_id))\n                print(\"  ✅ Campo 'department_id' aggiunto alla tabella 'user'\")\n            else:\n                print(\"  ⚠️  Campo 'department_id' già presente nella tabella 'user'\")\n\n            db.session.commit()\n            print(\"🎉 Tabelle HR create con successo!\")\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante la creazione delle tabelle HR: {e}\")\n            return False\n\ndef migrate_user_skills():\n    \"\"\"Migra i dati dalla tabella user_skills alla nuova user_skills_detailed\"\"\"\n    with app.app_context():\n        try:\n            print(\"🔄 Migrazione competenze utenti...\")\n\n            # Verifica se ci sono dati da migrare\n            result = db.session.execute(sa.text(\"SELECT COUNT(*) FROM user_skills\")).fetchone()\n            old_skills_count = result[0] if result else 0\n\n            if old_skills_count == 0:\n                print(\"  ⚠️  Nessuna competenza da migrare dalla tabella user_skills\")\n                return True\n\n            # Migra i dati\n            migrate_sql = \"\"\"\n            INSERT INTO user_skills_detailed (user_id, skill_id, proficiency_level, self_assessed, created_at)\n            SELECT user_id, skill_id, 3, 1, CURRENT_TIMESTAMP\n            FROM user_skills\n            WHERE NOT EXISTS (\n                SELECT 1 FROM user_skills_detailed\n                WHERE user_skills_detailed.user_id = user_skills.user_id\n                AND user_skills_detailed.skill_id = user_skills.skill_id\n            )\n            \"\"\"\n\n            result = db.session.execute(sa.text(migrate_sql))\n            migrated_count = result.rowcount\n\n            db.session.commit()\n            print(f\"  ✅ Migrate {migrated_count} competenze dalla tabella user_skills\")\n            print(\"  ℹ️  Le competenze sono state impostate con livello 3 (Intermedio) di default\")\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante la migrazione delle competenze: {e}\")\n            return False\n\ndef populate_orgchart_data():\n    \"\"\"Popola il database con dati di esempio per l'organigramma\"\"\"\n    with app.app_context():\n        try:\n            print(\"🏢 Popolamento dati organigramma...\")\n\n            # Verifica se ci sono già dipartimenti\n            result = db.session.execute(sa.text(\"SELECT COUNT(*) FROM departments\")).fetchone()\n            dept_count = result[0] if result else 0\n\n            if dept_count > 0:\n                print(f\"  ⚠️  Trovati {dept_count} dipartimenti esistenti. Aggiorno solo se necessario...\")\n\n            # 1. Crea dipartimenti principali\n            departments_data = [\n                # Dipartimenti di primo livello\n                (\"Direzione Generale\", \"Direzione e coordinamento strategico aziendale\", None, None, 500000.0),\n                (\"IT & Technology\", \"Sviluppo software e infrastruttura tecnologica\", None, None, 200000.0),\n                (\"Risorse Umane\", \"Gestione del personale e sviluppo organizzativo\", None, None, 80000.0),\n                (\"Amministrazione\", \"Gestione amministrativa e finanziaria\", None, None, 120000.0),\n                (\"Commerciale\", \"Vendite e sviluppo business\", None, None, 150000.0),\n                (\"Marketing\", \"Marketing e comunicazione\", None, None, 100000.0),\n\n                # Sottidipartimenti IT\n                (\"Development\", \"Team di sviluppo software\", None, 2, 120000.0),\n                (\"Infrastructure\", \"Gestione infrastruttura e DevOps\", None, 2, 80000.0),\n                (\"QA & Testing\", \"Quality Assurance e testing\", None, 2, 60000.0),\n\n                # Sottosottidipartimenti Development\n                (\"Frontend Team\", \"Sviluppo interfacce utente\", None, 7, 50000.0),\n                (\"Backend Team\", \"Sviluppo servizi e API\", None, 7, 70000.0),\n                (\"Mobile Team\", \"Sviluppo applicazioni mobile\", None, 7, 40000.0),\n\n                # Sottidipartimenti HR\n                (\"Recruiting\", \"Selezione e acquisizione talenti\", None, 3, 30000.0),\n                (\"Training & Development\", \"Formazione e sviluppo competenze\", None, 3, 25000.0),\n\n                # Sottidipartimenti Commerciale\n                (\"Sales\", \"Vendite dirette\", None, 5, 80000.0),\n                (\"Business Development\", \"Sviluppo nuovi mercati\", None, 5, 70000.0),\n                (\"Customer Success\", \"Gestione clienti esistenti\", None, 5, 50000.0),\n            ]\n\n            # Inserisci dipartimenti\n            for name, description, manager_id, parent_id, budget in departments_data:\n                # Verifica se il dipartimento esiste già\n                existing = db.session.execute(\n                    sa.text(\"SELECT id FROM departments WHERE name = :name\"),\n                    {\"name\": name}\n                ).fetchone()\n\n                if not existing:\n                    insert_sql = \"\"\"\n                    INSERT INTO departments (name, description, manager_id, parent_id, budget, is_active, created_at, updated_at)\n                    VALUES (:name, :description, :manager_id, :parent_id, :budget, TRUE, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)\n                    \"\"\"\n                    db.session.execute(sa.text(insert_sql), {\n                        \"name\": name,\n                        \"description\": description,\n                        \"manager_id\": manager_id,\n                        \"parent_id\": parent_id,\n                        \"budget\": budget\n                    })\n                    print(f\"  ✅ Dipartimento '{name}' creato\")\n                else:\n                    print(f\"  ⚠️  Dipartimento '{name}' già esistente\")\n\n            # 2. Crea utenti di esempio se non esistono\n            users_data = [\n                (\"mario.rossi\", \"<EMAIL>\", \"Mario\", \"Rossi\", \"admin\", \"CEO\", 1),\n                (\"anna.verdi\", \"<EMAIL>\", \"Anna\", \"Verdi\", \"manager\", \"CTO\", 2),\n                (\"luca.bianchi\", \"<EMAIL>\", \"Luca\", \"Bianchi\", \"manager\", \"HR Director\", 3),\n                (\"sara.neri\", \"<EMAIL>\", \"Sara\", \"Neri\", \"manager\", \"CFO\", 4),\n                (\"paolo.gialli\", \"<EMAIL>\", \"Paolo\", \"Gialli\", \"manager\", \"Sales Director\", 5),\n                (\"elena.blu\", \"<EMAIL>\", \"Elena\", \"Blu\", \"manager\", \"Marketing Director\", 6),\n                (\"marco.viola\", \"<EMAIL>\", \"Marco\", \"Viola\", \"manager\", \"Dev Manager\", 7),\n                (\"giulia.rosa\", \"<EMAIL>\", \"Giulia\", \"Rosa\", \"manager\", \"Infrastructure Manager\", 8),\n                (\"andrea.oro\", \"<EMAIL>\", \"Andrea\", \"Oro\", \"manager\", \"QA Manager\", 9),\n                (\"francesca.argento\", \"<EMAIL>\", \"Francesca\", \"Argento\", \"employee\", \"Frontend Lead\", 10),\n                (\"roberto.bronzo\", \"<EMAIL>\", \"Roberto\", \"Bronzo\", \"employee\", \"Backend Lead\", 11),\n                (\"chiara.rame\", \"<EMAIL>\", \"Chiara\", \"Rame\", \"employee\", \"Mobile Lead\", 12),\n                (\"davide.ferro\", \"<EMAIL>\", \"Davide\", \"Ferro\", \"employee\", \"Recruiter\", 13),\n                (\"laura.acciaio\", \"<EMAIL>\", \"Laura\", \"Acciaio\", \"employee\", \"Training Manager\", 14),\n                (\"simone.titanio\", \"<EMAIL>\", \"Simone\", \"Titanio\", \"employee\", \"Sales Manager\", 15),\n                (\"valentina.platino\", \"<EMAIL>\", \"Valentina\", \"Platino\", \"employee\", \"Business Dev\", 16),\n                (\"alessandro.zinco\", \"<EMAIL>\", \"Alessandro\", \"Zinco\", \"employee\", \"Customer Success\", 17),\n            ]\n\n            for username, email, first_name, last_name, role, position, dept_id in users_data:\n                # Verifica se l'utente esiste già\n                existing = db.session.execute(\n                    sa.text(\"SELECT id FROM \\\"user\\\" WHERE username = :username\"),\n                    {\"username\": username}\n                ).fetchone()\n\n                if not existing:\n                    insert_sql = \"\"\"\n                    INSERT INTO \"user\" (username, email, first_name, last_name, role, position, department_id, is_active, created_at)\n                    VALUES (:username, :email, :first_name, :last_name, :role, :position, :dept_id, TRUE, CURRENT_TIMESTAMP)\n                    \"\"\"\n                    db.session.execute(sa.text(insert_sql), {\n                        \"username\": username,\n                        \"email\": email,\n                        \"first_name\": first_name,\n                        \"last_name\": last_name,\n                        \"role\": role,\n                        \"position\": position,\n                        \"dept_id\": dept_id\n                    })\n                    print(f\"  ✅ Utente '{username}' creato\")\n                else:\n                    print(f\"  ⚠️  Utente '{username}' già esistente\")\n\n            # 3. Aggiorna i manager dei dipartimenti\n            manager_assignments = [\n                (1, 1),  # Direzione Generale -> Mario Rossi\n                (2, 2),  # IT & Technology -> Anna Verdi\n                (3, 3),  # Risorse Umane -> Luca Bianchi\n                (4, 4),  # Amministrazione -> Sara Neri\n                (5, 5),  # Commerciale -> Paolo Gialli\n                (6, 6),  # Marketing -> Elena Blu\n                (7, 7),  # Development -> Marco Viola\n                (8, 8),  # Infrastructure -> Giulia Rosa\n                (9, 9),  # QA & Testing -> Andrea Oro\n                (10, 10), # Frontend Team -> Francesca Argento\n                (11, 11), # Backend Team -> Roberto Bronzo\n                (12, 12), # Mobile Team -> Chiara Rame\n                (13, 13), # Recruiting -> Davide Ferro\n                (14, 14), # Training & Development -> Laura Acciaio\n                (15, 15), # Sales -> Simone Titanio\n                (16, 16), # Business Development -> Valentina Platino\n                (17, 17), # Customer Success -> Alessandro Zinco\n            ]\n\n            for dept_id, manager_id in manager_assignments:\n                # Verifica se l'utente esiste\n                user_exists = db.session.execute(\n                    sa.text(\"SELECT id FROM \\\"user\\\" WHERE id = :user_id\"),\n                    {\"user_id\": manager_id}\n                ).fetchone()\n\n                if user_exists:\n                    update_sql = \"UPDATE departments SET manager_id = :manager_id WHERE id = :dept_id\"\n                    db.session.execute(sa.text(update_sql), {\n                        \"manager_id\": manager_id,\n                        \"dept_id\": dept_id\n                    })\n                    print(f\"  ✅ Manager assegnato al dipartimento {dept_id}\")\n\n            db.session.commit()\n            print(\"🎉 Dati organigramma popolati con successo!\")\n\n            # Mostra statistiche\n            dept_count = db.session.execute(sa.text(\"SELECT COUNT(*) FROM departments\")).fetchone()[0]\n            user_count = db.session.execute(sa.text(\"SELECT COUNT(*) FROM \\\"user\\\"\")).fetchone()[0]\n            print(f\"📊 Statistiche: {dept_count} dipartimenti, {user_count} utenti\")\n\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante il popolamento dati organigramma: {e}\")\n            return False\n\ndef setup_timesheet_module():\n    \"\"\"Setup completo del modulo Timesheet - Task 3.1 + 4\"\"\"\n    print(\"🚀 Setup modulo Timesheet & CRM - Task 3.1 + 4\")\n    print(\"=\" * 50)\n\n    success = True\n\n    # 1. Crea le nuove tabelle\n    if not create_timesheet_tables():\n        success = False\n\n    # 2. Estendi tabella timesheet esistente\n    if not extend_timesheet_table():\n        success = False\n\n    # 3. Popola dati di esempio\n    if not populate_timesheet_sample_data():\n        success = False\n\n    if success:\n        print(\"\\n🎉 Setup modulo Timesheet completato con successo!\")\n        print(\"📋 Funzionalità disponibili:\")\n        print(\"   - MonthlyTimesheet: Approvazione mensile\")\n        print(\"   - TimeOffRequest: Ferie/permessi/smartworking\")\n        print(\"   - Contract: Contratti clienti\")\n        print(\"   - Invoice: Fatturazione per periodo\")\n        print(\"   - TimesheetEntry: Ore con billing info\")\n    else:\n        print(\"\\n❌ Setup modulo Timesheet fallito. Controlla gli errori sopra.\")\n\n    return success\n\ndef create_timesheet_tables():\n    \"\"\"Crea le nuove tabelle per Task 3.1 + 4\"\"\"\n    with app.app_context():\n        try:\n            print(\"🏗️  Creazione tabelle Timesheet & CRM...\")\n\n            # Usa SQLAlchemy per creare tutte le tabelle\n            from models import (\n                MonthlyTimesheet, TimeOffRequest, Contract,\n                Invoice, InvoiceLine\n            )\n\n            db.create_all()\n\n            # Verifica che le tabelle siano state create\n            inspector = sa.inspect(db.engine)\n            existing_tables = inspector.get_table_names()\n\n            required_tables = [\n                'monthly_timesheets',\n                'time_off_requests',\n                'contracts',\n                'invoices',\n                'invoice_lines'\n            ]\n\n            for table in required_tables:\n                if table in existing_tables:\n                    print(f\"  ✅ Tabella '{table}' creata/verificata\")\n                else:\n                    print(f\"  ❌ Tabella '{table}' NON trovata\")\n                    return False\n\n            db.session.commit()\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante la creazione tabelle Timesheet: {e}\")\n            return False\n\ndef extend_timesheet_table():\n    \"\"\"Estende la tabella timesheet_entries con nuovi campi\"\"\"\n    with app.app_context():\n        try:\n            print(\"🔧 Estensione tabella timesheet_entries...\")\n\n            inspector = sa.inspect(db.engine)\n\n            # Determina il nome della tabella (timesheet o timesheet_entries)\n            existing_tables = inspector.get_table_names()\n            table_name = 'timesheet_entries' if 'timesheet_entries' in existing_tables else 'timesheet'\n\n            columns = inspector.get_columns(table_name)\n            column_names = [col['name'] for col in columns]\n\n            # Campi da aggiungere a timesheet_entries\n            new_columns = [\n                (\"monthly_timesheet_id\", \"INTEGER\"),\n                (\"billable\", \"BOOLEAN DEFAULT FALSE\"),\n                (\"billing_rate\", \"FLOAT\"),\n                (\"contract_id\", \"INTEGER\"),\n                (\"invoice_line_id\", \"INTEGER\"),\n                (\"billing_status\", \"VARCHAR(20) DEFAULT 'unbilled'\")\n            ]\n\n            for column_name, column_type in new_columns:\n                if column_name not in column_names:\n                    sql = f\"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}\"\n                    db.session.execute(sa.text(sql))\n                    print(f\"  ✅ Campo '{column_name}' aggiunto a {table_name}\")\n                else:\n                    print(f\"  ⚠️  Campo '{column_name}' già esistente in {table_name}\")\n\n            db.session.commit()\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante l'estensione tabella timesheet: {e}\")\n            return False\n\ndef extend_project_table():\n    \"\"\"Aggiunge contract_id alla tabella project\"\"\"\n    with app.app_context():\n        try:\n            print(\"🔧 Estensione tabella project...\")\n\n            inspector = sa.inspect(db.engine)\n            columns = inspector.get_columns('project')\n            column_names = [col['name'] for col in columns]\n\n            # Campo da aggiungere\n            if 'contract_id' not in column_names:\n                sql = \"ALTER TABLE project ADD COLUMN contract_id INTEGER\"\n                db.session.execute(sa.text(sql))\n                print(\"  ✅ Campo 'contract_id' aggiunto a project\")\n            else:\n                print(\"  ⚠️  Campo 'contract_id' già esistente in project\")\n\n            db.session.commit()\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante l'estensione tabella project: {e}\")\n            return False\n\ndef populate_timesheet_sample_data():\n    \"\"\"Popola dati di esempio per testing\"\"\"\n    with app.app_context():\n        try:\n            print(\"🌱 Popolamento dati esempio Timesheet...\")\n\n            # Verifica se ci sono già dati\n            contract_count = db.session.execute(sa.text(\"SELECT COUNT(*) FROM contracts\")).fetchone()[0]\n            if contract_count > 0:\n                print(\"  ⚠️  Dati esempio già presenti\")\n                return True\n\n            # Trova primo cliente\n            client_result = db.session.execute(sa.text(\"SELECT id FROM client LIMIT 1\")).fetchone()\n            if not client_result:\n                print(\"  ⚠️  Nessun cliente trovato, salto dati esempio\")\n                return True\n\n            client_id = client_result[0]\n\n            # Crea contratto esempio\n            contract_sql = \"\"\"\n            INSERT INTO contracts (client_id, contract_number, title, description, contract_type, hourly_rate, budget_hours, start_date, status, created_at, updated_at)\n            VALUES (:client_id, 'CTR-2024-001', 'Contratto Sviluppo Software', 'Contratto per sviluppo applicazione web', 'hourly', 80.0, 100.0, CURRENT_DATE, 'active', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)\n            \"\"\"\n\n            db.session.execute(sa.text(contract_sql), {\"client_id\": client_id})\n            print(\"  ✅ Contratto esempio creato\")\n\n            db.session.commit()\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante popolamento dati esempio: {e}\")\n            return False\n\ndef cleanup_duplicate_tables():\n    \"\"\"Rimuove tabelle duplicate con CASCADE e rinomina timesheet in timesheet_entries\"\"\"\n    with app.app_context():\n        try:\n            print(\"🧹 Pulizia drastica tabelle duplicate...\")\n\n            inspector = sa.inspect(db.engine)\n            existing_tables = inspector.get_table_names()\n\n            # Step 1: Elimina TUTTE le tabelle duplicate con CASCADE\n            tables_to_drop_cascade = [\n                'timesheets',          # Duplicato con vincoli\n                'timesheet_entries',   # Duplicato vuoto\n                'timesheet_entry',     # Duplicato\n                'time_off_request'     # Duplicato (mantieni time_off_requests)\n            ]\n\n            for table in tables_to_drop_cascade:\n                if table in existing_tables:\n                    try:\n                        db.session.execute(sa.text(f\"DROP TABLE {table} CASCADE\"))\n                        print(f\"  ✅ Tabella '{table}' eliminata con CASCADE\")\n                    except Exception as e:\n                        print(f\"  ⚠️  Errore eliminazione '{table}': {e}\")\n                else:\n                    print(f\"  ⏭️  Tabella '{table}' non trovata\")\n\n            # Commit eliminazioni\n            db.session.commit()\n\n            # Step 2: Rinomina timesheet → timesheet_entries (ora che non ci sono conflitti)\n            inspector = sa.inspect(db.engine)\n            existing_tables = inspector.get_table_names()\n\n            if 'timesheet' in existing_tables and 'timesheet_entries' not in existing_tables:\n                db.session.execute(sa.text(\"ALTER TABLE timesheet RENAME TO timesheet_entries\"))\n                print(\"  ✅ Tabella 'timesheet' rinominata in 'timesheet_entries'\")\n            elif 'timesheet' in existing_tables and 'timesheet_entries' in existing_tables:\n                print(\"  ⚠️  Conflitto: entrambe 'timesheet' e 'timesheet_entries' esistono\")\n            else:\n                print(\"  ⏭️  Rinominazione non necessaria\")\n\n            db.session.commit()\n            print(\"✅ Pulizia drastica completata\")\n            return True\n\n        except Exception as e:\n            db.session.rollback()\n            print(f\"❌ Errore durante pulizia drastica: {e}\")\n            return False\n\ndef setup_hr_module():\n    \"\"\"Setup completo del modulo HR\"\"\"\n    print(\"🚀 Setup modulo HR - Task 7.1\")\n    print(\"=\" * 50)\n\n    success = True\n\n    # 1. Crea le tabelle HR\n    if not create_hr_tables():\n        success = False\n\n    # 2. Migra le competenze esistenti\n    if not migrate_user_skills():\n        success = False\n\n    if success:\n        print(\"\\n🎉 Setup modulo HR completato con successo!\")\n        print(\"📋 Prossimi passi:\")\n        print(\"   - Subtask 7.2: Employee Profile Management\")\n        print(\"   - Subtask 7.3: Skills Management System\")\n        print(\"   - Subtask 7.4: Department Management Module\")\n    else:\n        print(\"\\n❌ Setup modulo HR fallito. Controlla gli errori sopra.\")\n\n    return success\n\nif __name__ == \"__main__\":\n    # Mostra le tabelle all'inizio\n    show_tables()\n\n    # Se viene passato un file come argomento, eseguilo\n    if len(sys.argv) > 1:\n        if sys.argv[1] == \"add_start_date\":\n            add_start_date_to_task()\n        elif sys.argv[1] == \"setup_hr\":\n            setup_hr_module()\n        elif sys.argv[1] == \"setup_timesheet\":\n            setup_timesheet_module()\n        elif sys.argv[1] == \"create_hr_tables\":\n            create_hr_tables()\n        elif sys.argv[1] == \"create_timesheet_tables\":\n            create_timesheet_tables()\n        elif sys.argv[1] == \"extend_timesheet\":\n            extend_timesheet_table()\n        elif sys.argv[1] == \"cleanup_duplicates\":\n            cleanup_duplicate_tables()\n        elif sys.argv[1] == \"migrate_skills\":\n            migrate_user_skills()\n        elif sys.argv[1] == \"populate_orgchart\":\n            populate_orgchart_data()\n        else:\n            run_sql_file(sys.argv[1])\n\n        # Mostra le tabelle dopo l'esecuzione\n        show_tables()\n    else:\n        print(\"Uso: python db_update.py [comando | file_sql]\")\n        print(\"\\nComandi disponibili:\")\n        print(\"  setup_timesheet     - Setup completo modulo Timesheet & CRM (Task 3.1 + 4)\")\n        print(\"  create_timesheet_tables - Crea solo le nuove tabelle Timesheet\")\n        print(\"  extend_timesheet    - Estende tabella timesheet esistente\")\n        print(\"  cleanup_duplicates  - Rimuove tabelle duplicate\")\n        print(\"  setup_hr            - Setup completo modulo HR (Task 7.1)\")\n        print(\"  create_hr_tables    - Crea solo le tabelle HR\")\n        print(\"  migrate_skills      - Migra competenze da user_skills a user_skills_detailed\")\n        print(\"  populate_orgchart   - Popola database con dati organigramma completo\")\n        print(\"  add_start_date      - Aggiunge campo start_date alla tabella task\")\n        print(\"\\nSe non viene specificato un comando, verranno mostrate solo le tabelle esistenti.\")"}