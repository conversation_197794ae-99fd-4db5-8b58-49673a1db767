{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/proposals.py"}, "originalCode": "\"\"\"\nAPI Blueprint per la gestione delle proposte/opportunità.\nTask 4 - CRM Integration\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, or_, extract\nfrom datetime import datetime, date\n\nfrom models import Proposal, Client, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_proposals = Blueprint('api_proposals', __name__)\n\n\n@api_proposals.route('/', methods=['GET'])\n@login_required\ndef get_proposals():\n    \"\"\"Recupera lista proposte con filtri\"\"\"\n    try:\n        # Parametri filtro\n        client_id = request.args.get('client_id', type=int)\n        status = request.args.get('status')  # draft, sent, negotiating, accepted, rejected\n        created_by = request.args.get('created_by', type=int)\n        start_date = request.args.get('start_date')  # YYYY-MM-DD\n        end_date = request.args.get('end_date')  # YYYY-MM-DD\n        search = request.args.get('search')  # Ricerca in titolo\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = Proposal.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_proposals'):\n            # L'utente può vedere solo le proprie proposte\n            query = query.filter(Proposal.created_by == current_user.id)\n        \n        # Applica filtri\n        if client_id:\n            query = query.filter(Proposal.client_id == client_id)\n            \n        if status:\n            query = query.filter(Proposal.status == status)\n            \n        if created_by:\n            query = query.filter(Proposal.created_by == created_by)\n            \n        if start_date:\n            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()\n            query = query.filter(Proposal.created_at >= start_date_obj)\n            \n        if end_date:\n            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()\n            query = query.filter(Proposal.created_at <= end_date_obj)\n            \n        if search:\n            search_pattern = f\"%{search}%\"\n            query = query.filter(Proposal.title.ilike(search_pattern))\n        \n        # Ordina per data di creazione (più recenti prima)\n        query = query.order_by(Proposal.created_at.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        proposals_data = []\n        for proposal in paginated.items:\n            proposals_data.append({\n                'id': proposal.id,\n                'client_id': proposal.client_id,\n                'client': {\n                    'id': proposal.client.id,\n                    'name': proposal.client.name,\n                    'company': getattr(proposal.client, 'company', None)\n                } if proposal.client else None,\n                'title': proposal.title,\n                'description': proposal.description,\n                'value': proposal.value,\n                'status': proposal.status,\n                'created_by': proposal.created_by,\n                'creator': {\n                    'id': proposal.creator.id,\n                    'first_name': proposal.creator.first_name,\n                    'last_name': proposal.creator.last_name\n                } if proposal.creator else None,\n                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,\n                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,\n                'created_at': proposal.created_at.isoformat(),\n                'updated_at': proposal.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'proposals': proposals_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperate {len(proposals_data)} proposte\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_proposals.route('/', methods=['POST'])\*************\n@login_required\ndef create_proposal():\n    \"\"\"Crea una nuova proposta\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_proposals'):\n            return api_response(False, 'Non hai i permessi per creare proposte', status_code=403)\n        \n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['client_id', 'title']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        # Verifica che il cliente esista\n        client = Client.query.get(data['client_id'])\n        if not client:\n            return api_response(\n                False,\n                'Cliente non trovato',\n                status_code=404\n            )\n        \n        # Validazione status\n        valid_statuses = ['draft', 'sent', 'negotiating', 'accepted', 'rejected']\n        status = data.get('status', 'draft')\n        if status not in valid_statuses:\n            return api_response(\n                False,\n                f'Status non valido. Valori ammessi: {\", \".join(valid_statuses)}',\n                status_code=400\n            )\n        \n        # Parsing date\n        sent_date = None\n        expiry_date = None\n        \n        if 'sent_date' in data and data['sent_date']:\n            try:\n                sent_date = datetime.strptime(data['sent_date'], '%Y-%m-%d').date()\n            except ValueError:\n                return api_response(\n                    False,\n                    'Formato sent_date non valido. Utilizzare YYYY-MM-DD',\n                    status_code=400\n                )\n        \n        if 'expiry_date' in data and data['expiry_date']:\n            try:\n                expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()\n            except ValueError:\n                return api_response(\n                    False,\n                    'Formato expiry_date non valido. Utilizzare YYYY-MM-DD',\n                    status_code=400\n                )\n        \n        # Validazione logica date\n        if sent_date and expiry_date and sent_date > expiry_date:\n            return api_response(\n                False,\n                'La data di invio non può essere successiva alla data di scadenza',\n                status_code=400\n            )\n        \n        # Crea nuova proposta\n        proposal = Proposal(\n            client_id=data['client_id'],\n            title=data['title'],\n            description=data.get('description', ''),\n            value=data.get('value'),\n            status=status,\n            created_by=current_user.id,\n            sent_date=sent_date,\n            expiry_date=expiry_date\n        )\n        \n        db.session.add(proposal)\n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': proposal.id,\n                'client_id': proposal.client_id,\n                'client': {\n                    'id': proposal.client.id,\n                    'name': proposal.client.name\n                },\n                'title': proposal.title,\n                'description': proposal.description,\n                'value': proposal.value,\n                'status': proposal.status,\n                'created_by': proposal.created_by,\n                'creator': {\n                    'id': current_user.id,\n                    'first_name': current_user.first_name,\n                    'last_name': current_user.last_name\n                },\n                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,\n                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,\n                'created_at': proposal.created_at.isoformat()\n            },\n            message='Proposta creata con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_proposals.route('/<int:proposal_id>', methods=['GET'])\n@login_required\ndef get_proposal(proposal_id):\n    \"\"\"Recupera dettaglio singola proposta\"\"\"\n    try:\n        proposal = Proposal.query.get_or_404(proposal_id)\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_proposals'):\n            if proposal.created_by != current_user.id:\n                return api_response(False, 'Non hai i permessi per visualizzare questa proposta', status_code=403)\n        \n        return api_response(\n            data={\n                'id': proposal.id,\n                'client_id': proposal.client_id,\n                'client': {\n                    'id': proposal.client.id,\n                    'name': proposal.client.name,\n                    'company': getattr(proposal.client, 'company', None),\n                    'industry': proposal.client.industry,\n                    'website': proposal.client.website,\n                    'address': proposal.client.address\n                } if proposal.client else None,\n                'title': proposal.title,\n                'description': proposal.description,\n                'value': proposal.value,\n                'status': proposal.status,\n                'created_by': proposal.created_by,\n                'creator': {\n                    'id': proposal.creator.id,\n                    'first_name': proposal.creator.first_name,\n                    'last_name': proposal.creator.last_name,\n                    'email': proposal.creator.email\n                } if proposal.creator else None,\n                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,\n                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,\n                'created_at': proposal.created_at.isoformat(),\n                'updated_at': proposal.updated_at.isoformat()\n            },\n            message=\"Dettaglio proposta recuperato con successo\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n", "modifiedCode": "\"\"\"\nAPI Blueprint per la gestione delle proposte/opportunità.\nTask 4 - CRM Integration\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, or_, extract\nfrom datetime import datetime, date\n\nfrom models import Proposal, Client, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_proposals = Blueprint('api_proposals', __name__)\n\n\n@api_proposals.route('/', methods=['GET'])\n@login_required\ndef get_proposals():\n    \"\"\"Recupera lista proposte con filtri\"\"\"\n    try:\n        # Parametri filtro\n        client_id = request.args.get('client_id', type=int)\n        status = request.args.get('status')  # draft, sent, negotiating, accepted, rejected\n        created_by = request.args.get('created_by', type=int)\n        start_date = request.args.get('start_date')  # YYYY-MM-DD\n        end_date = request.args.get('end_date')  # YYYY-MM-DD\n        search = request.args.get('search')  # Ricerca in titolo\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = Proposal.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_proposals'):\n            # L'utente può vedere solo le proprie proposte\n            query = query.filter(Proposal.created_by == current_user.id)\n        \n        # Applica filtri\n        if client_id:\n            query = query.filter(Proposal.client_id == client_id)\n            \n        if status:\n            query = query.filter(Proposal.status == status)\n            \n        if created_by:\n            query = query.filter(Proposal.created_by == created_by)\n            \n        if start_date:\n            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()\n            query = query.filter(Proposal.created_at >= start_date_obj)\n            \n        if end_date:\n            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()\n            query = query.filter(Proposal.created_at <= end_date_obj)\n            \n        if search:\n            search_pattern = f\"%{search}%\"\n            query = query.filter(Proposal.title.ilike(search_pattern))\n        \n        # Ordina per data di creazione (più recenti prima)\n        query = query.order_by(Proposal.created_at.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        proposals_data = []\n        for proposal in paginated.items:\n            proposals_data.append({\n                'id': proposal.id,\n                'client_id': proposal.client_id,\n                'client': {\n                    'id': proposal.client.id,\n                    'name': proposal.client.name,\n                    'company': getattr(proposal.client, 'company', None)\n                } if proposal.client else None,\n                'title': proposal.title,\n                'description': proposal.description,\n                'value': proposal.value,\n                'status': proposal.status,\n                'created_by': proposal.created_by,\n                'creator': {\n                    'id': proposal.creator.id,\n                    'first_name': proposal.creator.first_name,\n                    'last_name': proposal.creator.last_name\n                } if proposal.creator else None,\n                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,\n                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,\n                'created_at': proposal.created_at.isoformat(),\n                'updated_at': proposal.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'proposals': proposals_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperate {len(proposals_data)} proposte\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_proposals.route('/', methods=['POST'])\*************\n@login_required\ndef create_proposal():\n    \"\"\"Crea una nuova proposta\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_proposals'):\n            return api_response(False, 'Non hai i permessi per creare proposte', status_code=403)\n        \n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['client_id', 'title']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        # Verifica che il cliente esista\n        client = Client.query.get(data['client_id'])\n        if not client:\n            return api_response(\n                False,\n                'Cliente non trovato',\n                status_code=404\n            )\n        \n        # Validazione status\n        valid_statuses = ['draft', 'sent', 'negotiating', 'accepted', 'rejected']\n        status = data.get('status', 'draft')\n        if status not in valid_statuses:\n            return api_response(\n                False,\n                f'Status non valido. Valori ammessi: {\", \".join(valid_statuses)}',\n                status_code=400\n            )\n        \n        # Parsing date\n        sent_date = None\n        expiry_date = None\n        \n        if 'sent_date' in data and data['sent_date']:\n            try:\n                sent_date = datetime.strptime(data['sent_date'], '%Y-%m-%d').date()\n            except ValueError:\n                return api_response(\n                    False,\n                    'Formato sent_date non valido. Utilizzare YYYY-MM-DD',\n                    status_code=400\n                )\n        \n        if 'expiry_date' in data and data['expiry_date']:\n            try:\n                expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()\n            except ValueError:\n                return api_response(\n                    False,\n                    'Formato expiry_date non valido. Utilizzare YYYY-MM-DD',\n                    status_code=400\n                )\n        \n        # Validazione logica date\n        if sent_date and expiry_date and sent_date > expiry_date:\n            return api_response(\n                False,\n                'La data di invio non può essere successiva alla data di scadenza',\n                status_code=400\n            )\n        \n        # Crea nuova proposta\n        proposal = Proposal(\n            client_id=data['client_id'],\n            title=data['title'],\n            description=data.get('description', ''),\n            value=data.get('value'),\n            status=status,\n            created_by=current_user.id,\n            sent_date=sent_date,\n            expiry_date=expiry_date\n        )\n        \n        db.session.add(proposal)\n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': proposal.id,\n                'client_id': proposal.client_id,\n                'client': {\n                    'id': proposal.client.id,\n                    'name': proposal.client.name\n                },\n                'title': proposal.title,\n                'description': proposal.description,\n                'value': proposal.value,\n                'status': proposal.status,\n                'created_by': proposal.created_by,\n                'creator': {\n                    'id': current_user.id,\n                    'first_name': current_user.first_name,\n                    'last_name': current_user.last_name\n                },\n                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,\n                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,\n                'created_at': proposal.created_at.isoformat()\n            },\n            message='Proposta creata con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_proposals.route('/<int:proposal_id>', methods=['GET'])\n@login_required\ndef get_proposal(proposal_id):\n    \"\"\"Recupera dettaglio singola proposta\"\"\"\n    try:\n        proposal = Proposal.query.get_or_404(proposal_id)\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_proposals'):\n            if proposal.created_by != current_user.id:\n                return api_response(False, 'Non hai i permessi per visualizzare questa proposta', status_code=403)\n        \n        return api_response(\n            data={\n                'id': proposal.id,\n                'client_id': proposal.client_id,\n                'client': {\n                    'id': proposal.client.id,\n                    'name': proposal.client.name,\n                    'company': getattr(proposal.client, 'company', None),\n                    'industry': proposal.client.industry,\n                    'website': proposal.client.website,\n                    'address': proposal.client.address\n                } if proposal.client else None,\n                'title': proposal.title,\n                'description': proposal.description,\n                'value': proposal.value,\n                'status': proposal.status,\n                'created_by': proposal.created_by,\n                'creator': {\n                    'id': proposal.creator.id,\n                    'first_name': proposal.creator.first_name,\n                    'last_name': proposal.creator.last_name,\n                    'email': proposal.creator.email\n                } if proposal.creator else None,\n                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,\n                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,\n                'created_at': proposal.created_at.isoformat(),\n                'updated_at': proposal.updated_at.isoformat()\n            },\n            message=\"Dettaglio proposta recuperato con successo\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_proposals.route('/<int:proposal_id>', methods=['PUT'])\*************\n@login_required\ndef update_proposal(proposal_id):\n    \"\"\"Aggiorna una proposta esistente\"\"\"\n    try:\n        proposal = Proposal.query.get_or_404(proposal_id)\n\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_proposals'):\n            if proposal.created_by != current_user.id:\n                return api_response(False, 'Non hai i permessi per modificare questa proposta', status_code=403)\n\n        data = request.get_json()\n\n        # Non permettere modifiche a proposte accettate/rifiutate\n        if proposal.status in ['accepted', 'rejected'] and 'status' not in data:\n            return api_response(\n                False,\n                'Non è possibile modificare proposte già accettate o rifiutate',\n                status_code=400\n            )\n\n        # Aggiorna campi se forniti\n        if 'title' in data:\n            proposal.title = data['title']\n\n        if 'description' in data:\n            proposal.description = data['description']\n\n        if 'value' in data:\n            proposal.value = data['value']\n\n        if 'status' in data:\n            valid_statuses = ['draft', 'sent', 'negotiating', 'accepted', 'rejected']\n            if data['status'] not in valid_statuses:\n                return api_response(\n                    False,\n                    f'Status non valido. Valori ammessi: {\", \".join(valid_statuses)}',\n                    status_code=400\n                )\n            proposal.status = data['status']\n\n            # Se status diventa 'sent' e non c'è sent_date, impostala a oggi\n            if data['status'] == 'sent' and not proposal.sent_date:\n                proposal.sent_date = date.today()\n\n        # Aggiorna date se fornite\n        if 'sent_date' in data:\n            if data['sent_date']:\n                try:\n                    proposal.sent_date = datetime.strptime(data['sent_date'], '%Y-%m-%d').date()\n                except ValueError:\n                    return api_response(\n                        False,\n                        'Formato sent_date non valido. Utilizzare YYYY-MM-DD',\n                        status_code=400\n                    )\n            else:\n                proposal.sent_date = None\n\n        if 'expiry_date' in data:\n            if data['expiry_date']:\n                try:\n                    proposal.expiry_date = datetime.strptime(data['expiry_date'], '%Y-%m-%d').date()\n                except ValueError:\n                    return api_response(\n                        False,\n                        'Formato expiry_date non valido. Utilizzare YYYY-MM-DD',\n                        status_code=400\n                    )\n            else:\n                proposal.expiry_date = None\n\n        # Validazione logica date\n        if proposal.sent_date and proposal.expiry_date and proposal.sent_date > proposal.expiry_date:\n            return api_response(\n                False,\n                'La data di invio non può essere successiva alla data di scadenza',\n                status_code=400\n            )\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': proposal.id,\n                'title': proposal.title,\n                'value': proposal.value,\n                'status': proposal.status,\n                'sent_date': proposal.sent_date.isoformat() if proposal.sent_date else None,\n                'expiry_date': proposal.expiry_date.isoformat() if proposal.expiry_date else None,\n                'updated_at': proposal.updated_at.isoformat()\n            },\n            message='Proposta aggiornata con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_proposals.route('/<int:proposal_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_proposal(proposal_id):\n    \"\"\"Elimina una proposta\"\"\"\n    try:\n        proposal = Proposal.query.get_or_404(proposal_id)\n\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_proposals'):\n            if proposal.created_by != current_user.id:\n                return api_response(False, 'Non hai i permessi per eliminare questa proposta', status_code=403)\n\n        # Non permettere eliminazione di proposte accettate\n        if proposal.status == 'accepted':\n            return api_response(\n                False,\n                'Non è possibile eliminare proposte accettate',\n                status_code=400\n            )\n\n        proposal_title = proposal.title\n        db.session.delete(proposal)\n        db.session.commit()\n\n        return api_response(\n            message=f'Proposta \"{proposal_title}\" eliminata con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n"}