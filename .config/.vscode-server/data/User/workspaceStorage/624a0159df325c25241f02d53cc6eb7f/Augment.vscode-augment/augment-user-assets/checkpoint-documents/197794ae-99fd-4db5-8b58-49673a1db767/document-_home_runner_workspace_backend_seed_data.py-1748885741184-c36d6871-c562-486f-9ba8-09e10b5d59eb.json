{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/seed_data.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nScript per popolare il database con dati di test.\nUso: python seed_data.py [--clear]\n\"\"\"\n\nimport sys\nimport argparse\nfrom datetime import datetime, timedelta\nfrom werkzeug.security import generate_password_hash\n\nfrom app import create_app, db\nfrom models import (\n    User, Client, Project, Task, KPI, ProjectKPI,\n    TimesheetEntry, Event, Contact, Skill, AdminLog, Notification\n)\n\ndef clear_test_data():\n    \"\"\"Cancella solo i dati di test, preservando gli utenti esistenti.\"\"\"\n    print(\"🗑️  Cancellazione dati di test esistenti...\")\n\n    # Ordine importante per rispettare le foreign key\n    # NON cancelliamo AdminLog e Notification per preservare la cronologia\n    db.session.query(ProjectKPI).delete()\n    db.session.query(Timesheet).delete()\n    db.session.query(Task).delete()\n    db.session.query(Event).delete()\n    db.session.query(Contact).delete()\n\n    # Rimuovi associazioni many-to-many\n    for project in Project.query.all():\n        project.team_members.clear()\n\n    db.session.query(Project).delete()\n    db.session.query(Client).delete()\n    db.session.query(KPI).delete()\n    db.session.query(Skill).delete()\n\n    # NON cancelliamo gli utenti esistenti!\n\n    db.session.commit()\n    print(\"✅ Dati di test puliti (utenti preservati)!\")\n\ndef seed_users():\n    \"\"\"Crea utenti di test solo se non esistono già.\"\"\"\n    print(\"👥 Verifica/Creazione utenti...\")\n\n    users_data = [\n        {\n            'username': 'admin',\n            'email': '<EMAIL>',\n            'first_name': 'Admin',\n            'last_name': 'User',\n            'role': 'admin',\n            'password': 'admin123'\n        },\n        {\n            'username': 'mario.rossi',\n            'email': '<EMAIL>',\n            'first_name': 'Mario',\n            'last_name': 'Rossi',\n            'role': 'manager',\n            'password': 'manager123'\n        },\n        {\n            'username': 'giulia.bianchi',\n            'email': '<EMAIL>',\n            'first_name': 'Giulia',\n            'last_name': 'Bianchi',\n            'role': 'employee',\n            'password': 'dev123'\n        },\n        {\n            'username': 'marco.verdi',\n            'email': '<EMAIL>',\n            'first_name': 'Marco',\n            'last_name': 'Verdi',\n            'role': 'employee',\n            'password': 'dev123'\n        },\n        {\n            'username': 'sara.neri',\n            'email': '<EMAIL>',\n            'first_name': 'Sara',\n            'last_name': 'Neri',\n            'role': 'employee',\n            'password': 'design123'\n        }\n    ]\n\n    users = []\n    created_count = 0\n\n    for user_data in users_data:\n        # Controlla se l'utente esiste già (per email o username)\n        existing_user = User.query.filter(\n            (User.email == user_data['email']) |\n            (User.username == user_data['username'])\n        ).first()\n        if existing_user:\n            users.append(existing_user)\n            print(f\"  ✓ Utente {user_data['email']} già esistente\")\n        else:\n            user = User(\n                username=user_data['username'],\n                email=user_data['email'],\n                first_name=user_data['first_name'],\n                last_name=user_data['last_name'],\n                role=user_data['role'],\n                password_hash=generate_password_hash(user_data['password']),\n                is_active=True,\n                created_at=datetime.utcnow()\n            )\n            db.session.add(user)\n            users.append(user)\n            created_count += 1\n            print(f\"  + Creato utente {user_data['email']}\")\n\n    db.session.commit()\n    print(f\"✅ {created_count} nuovi utenti creati, {len(users)} totali disponibili\")\n    return users\n\ndef seed_clients():\n    \"\"\"Crea clienti di test.\"\"\"\n    print(\"🏢 Creazione clienti...\")\n\n    clients_data = [\n        {\n            'name': 'TechCorp S.r.l.',\n            'industry': 'Technology',\n            'description': 'Azienda leader nel settore tecnologico',\n            'website': 'https://techcorp.example.com',\n            'address': 'Via Roma 123, Milano'\n        },\n        {\n            'name': 'Fashion House',\n            'industry': 'Fashion',\n            'description': 'Brand di moda di lusso',\n            'website': 'https://fashionhouse.example.com',\n            'address': 'Via Montenapoleone 45, Milano'\n        },\n        {\n            'name': 'Green Energy Solutions',\n            'industry': 'Energy',\n            'description': 'Soluzioni per energie rinnovabili',\n            'website': 'https://greenenergy.example.com',\n            'address': 'Corso Buenos Aires 78, Milano'\n        }\n    ]\n\n    clients = []\n    for client_data in clients_data:\n        client = Client(\n            name=client_data['name'],\n            industry=client_data['industry'],\n            description=client_data['description'],\n            website=client_data['website'],\n            address=client_data['address'],\n            created_at=datetime.utcnow()\n        )\n        db.session.add(client)\n        clients.append(client)\n\n    db.session.commit()\n    print(f\"✅ Creati {len(clients)} clienti\")\n    return clients\n\ndef seed_skills():\n    \"\"\"Crea competenze di test.\"\"\"\n    print(\"🎯 Creazione competenze...\")\n\n    skills_data = [\n        {'name': 'Python', 'category': 'Programming', 'description': 'Linguaggio di programmazione Python'},\n        {'name': 'JavaScript', 'category': 'Programming', 'description': 'Linguaggio di programmazione JavaScript'},\n        {'name': 'React', 'category': 'Frontend', 'description': 'Libreria React per UI'},\n        {'name': 'Vue.js', 'category': 'Frontend', 'description': 'Framework Vue.js'},\n        {'name': 'Node.js', 'category': 'Backend', 'description': 'Runtime Node.js'},\n        {'name': 'PostgreSQL', 'category': 'Database', 'description': 'Database PostgreSQL'},\n        {'name': 'Docker', 'category': 'DevOps', 'description': 'Containerizzazione con Docker'},\n        {'name': 'Project Management', 'category': 'Management', 'description': 'Gestione progetti'},\n        {'name': 'UI/UX Design', 'category': 'Design', 'description': 'Design interfacce utente'},\n        {'name': 'Digital Marketing', 'category': 'Marketing', 'description': 'Marketing digitale'}\n    ]\n\n    skills = []\n    for skill_data in skills_data:\n        skill = Skill(\n            name=skill_data['name'],\n            category=skill_data['category'],\n            description=skill_data['description']\n        )\n        db.session.add(skill)\n        skills.append(skill)\n\n    db.session.commit()\n    print(f\"✅ Creati {len(skills)} competenze\")\n    return skills\n\ndef seed_kpis():\n    \"\"\"Crea KPI di test.\"\"\"\n    print(\"📊 Creazione KPI...\")\n\n    kpis_data = [\n        {\n            'name': 'Fatturato Mensile',\n            'description': 'Fatturato totale mensile dell\\'azienda',\n            'category': 'Finanziario',\n            'frequency': 'monthly',\n            'unit': 'EUR'\n        },\n        {\n            'name': 'Soddisfazione Cliente',\n            'description': 'Punteggio medio di soddisfazione clienti',\n            'category': 'Qualità',\n            'frequency': 'quarterly',\n            'unit': 'score'\n        },\n        {\n            'name': 'Progetti Completati',\n            'description': 'Numero di progetti completati nel periodo',\n            'category': 'Operativo',\n            'frequency': 'monthly',\n            'unit': 'count'\n        },\n        {\n            'name': 'Ore Fatturabili',\n            'description': 'Ore fatturabili per progetto',\n            'category': 'Operativo',\n            'frequency': 'weekly',\n            'unit': 'hours'\n        },\n        {\n            'name': 'ROI Progetto',\n            'description': 'Return on Investment del progetto',\n            'category': 'Finanziario',\n            'frequency': 'project_end',\n            'unit': 'percentage'\n        }\n    ]\n\n    kpis = []\n    for kpi_data in kpis_data:\n        kpi = KPI(\n            name=kpi_data['name'],\n            description=kpi_data['description'],\n            category=kpi_data['category'],\n            frequency=kpi_data['frequency'],\n            unit=kpi_data['unit'],\n            created_at=datetime.utcnow()\n        )\n        db.session.add(kpi)\n        kpis.append(kpi)\n\n    db.session.commit()\n    print(f\"✅ Creati {len(kpis)} KPI\")\n    return kpis\n\ndef seed_projects(users, clients, kpis):\n    \"\"\"Crea progetti di test.\"\"\"\n    print(\"🚀 Creazione progetti...\")\n\n    projects_data = [\n        {\n            'name': 'E-commerce Platform',\n            'description': 'Sviluppo di una piattaforma e-commerce completa con gestione ordini, pagamenti e inventario',\n            'client_id': clients[0].id,\n            'status': 'active',\n            'budget': 50000.0,\n            'expenses': 15000.0,\n            'start_date': datetime.now().date() - timedelta(days=30),\n            'end_date': datetime.now().date() + timedelta(days=60),\n            'team_members': [users[1], users[2], users[3]]  # Manager + 2 dev\n        },\n        {\n            'name': 'Brand Redesign',\n            'description': 'Redesign completo del brand e creazione di nuova identità visiva',\n            'client_id': clients[1].id,\n            'status': 'planning',\n            'budget': 25000.0,\n            'expenses': 2000.0,\n            'start_date': datetime.now().date() + timedelta(days=7),\n            'end_date': datetime.now().date() + timedelta(days=45),\n            'team_members': [users[1], users[4]]  # Manager + Designer\n        },\n        {\n            'name': 'Energy Management System',\n            'description': 'Sistema di gestione e monitoraggio per impianti di energia rinnovabile',\n            'client_id': clients[2].id,\n            'status': 'active',\n            'budget': 75000.0,\n            'expenses': 25000.0,\n            'start_date': datetime.now().date() - timedelta(days=15),\n            'end_date': datetime.now().date() + timedelta(days=90),\n            'team_members': [users[1], users[2], users[3], users[4]]  # Tutto il team\n        },\n        {\n            'name': 'Internal CRM',\n            'description': 'Sistema CRM interno per gestione clienti e opportunità',\n            'client_id': None,  # Progetto interno\n            'status': 'completed',\n            'budget': 30000.0,\n            'expenses': 28000.0,\n            'start_date': datetime.now().date() - timedelta(days=120),\n            'end_date': datetime.now().date() - timedelta(days=30),\n            'team_members': [users[2], users[3]]  # Solo dev\n        }\n    ]\n\n    projects = []\n    for project_data in projects_data:\n        project = Project(\n            name=project_data['name'],\n            description=project_data['description'],\n            client_id=project_data['client_id'],\n            status=project_data['status'],\n            budget=project_data['budget'],\n            expenses=project_data['expenses'],\n            start_date=project_data['start_date'],\n            end_date=project_data['end_date'],\n            created_at=datetime.utcnow()\n        )\n\n        # Aggiungi team members\n        for member in project_data['team_members']:\n            project.team_members.append(member)\n\n        db.session.add(project)\n        projects.append(project)\n\n    db.session.commit()\n\n    # Aggiungi KPI ai progetti\n    print(\"📈 Collegamento KPI ai progetti...\")\n    project_kpis_data = [\n        {'project': projects[0], 'kpi': kpis[0], 'target_value': 45000.0, 'current_value': 30000.0},\n        {'project': projects[0], 'kpi': kpis[2], 'target_value': 1.0, 'current_value': 0.0},\n        {'project': projects[1], 'kpi': kpis[1], 'target_value': 9.0, 'current_value': 8.5},\n        {'project': projects[2], 'kpi': kpis[3], 'target_value': 500.0, 'current_value': 320.0},\n        {'project': projects[3], 'kpi': kpis[4], 'target_value': 150.0, 'current_value': 140.0},\n    ]\n\n    for pk_data in project_kpis_data:\n        project_kpi = ProjectKPI(\n            project_id=pk_data['project'].id,\n            kpi_id=pk_data['kpi'].id,\n            target_value=pk_data['target_value'],\n            current_value=pk_data['current_value']\n        )\n        db.session.add(project_kpi)\n\n    db.session.commit()\n    print(f\"✅ Creati {len(projects)} progetti con KPI collegati\")\n\n    # Aggiungi task ai progetti\n    print(\"📋 Creazione task di esempio...\")\n    seed_tasks(projects, users)\n\n    # Aggiungi timesheet di esempio\n    print(\"⏱️ Creazione timesheet di esempio...\")\n    seed_timesheets(projects, users)\n\n    return projects\n\ndef seed_tasks(projects, users):\n    \"\"\"Crea task di esempio per i progetti.\"\"\"\n    from random import choice\n\n    tasks_data = [\n        # Tasks per E-commerce Platform\n        {'name': 'Setup Database', 'description': 'Configurazione database PostgreSQL', 'priority': 'high', 'status': 'done'},\n        {'name': 'User Authentication', 'description': 'Sistema di login e registrazione', 'priority': 'high', 'status': 'done'},\n        {'name': 'Product Catalog', 'description': 'Gestione catalogo prodotti', 'priority': 'medium', 'status': 'in-progress'},\n        {'name': 'Shopping Cart', 'description': 'Carrello della spesa', 'priority': 'medium', 'status': 'todo'},\n        {'name': 'Payment Integration', 'description': 'Integrazione gateway pagamenti', 'priority': 'high', 'status': 'todo'},\n\n        # Tasks per Brand Redesign\n        {'name': 'Brand Research', 'description': 'Ricerca e analisi brand esistente', 'priority': 'high', 'status': 'in-progress'},\n        {'name': 'Logo Design', 'description': 'Progettazione nuovo logo', 'priority': 'high', 'status': 'todo'},\n        {'name': 'Color Palette', 'description': 'Definizione palette colori', 'priority': 'medium', 'status': 'todo'},\n\n        # Tasks per Energy Management System\n        {'name': 'System Architecture', 'description': 'Architettura del sistema', 'priority': 'high', 'status': 'done'},\n        {'name': 'Sensor Integration', 'description': 'Integrazione sensori IoT', 'priority': 'high', 'status': 'in-progress'},\n        {'name': 'Dashboard Development', 'description': 'Sviluppo dashboard monitoraggio', 'priority': 'medium', 'status': 'in-progress'},\n        {'name': 'Alert System', 'description': 'Sistema di allerte', 'priority': 'medium', 'status': 'todo'},\n\n        # Tasks per Internal CRM\n        {'name': 'Contact Management', 'description': 'Gestione contatti clienti', 'priority': 'high', 'status': 'done'},\n        {'name': 'Opportunity Tracking', 'description': 'Tracciamento opportunità', 'priority': 'medium', 'status': 'done'},\n        {'name': 'Reporting Module', 'description': 'Modulo di reportistica', 'priority': 'low', 'status': 'done'},\n    ]\n\n    # Assegna task ai progetti\n    project_task_mapping = [\n        (0, [0, 1, 2, 3, 4]),      # E-commerce Platform\n        (1, [5, 6, 7]),            # Brand Redesign\n        (2, [8, 9, 10, 11]),       # Energy Management System\n        (3, [12, 13, 14])          # Internal CRM\n    ]\n\n    task_count = 0\n    for project_idx, task_indices in project_task_mapping:\n        project = projects[project_idx]\n\n        for task_idx in task_indices:\n            task_data = tasks_data[task_idx]\n\n            # Assegna task a un membro del team casuale\n            assignee = choice(project.team_members) if project.team_members else None\n\n            # Calcola date basate sullo status\n            if task_data['status'] == 'done':\n                due_date = project.start_date + timedelta(days=7)\n            elif task_data['status'] == 'in-progress':\n                due_date = project.start_date + timedelta(days=15)\n            else:  # todo\n                due_date = project.start_date + timedelta(days=25)\n\n            task = Task(\n                name=task_data['name'],\n                description=task_data['description'],\n                project_id=project.id,\n                assignee_id=assignee.id if assignee else None,\n                priority=task_data['priority'],\n                status=task_data['status'],\n                due_date=due_date,\n                created_at=datetime.utcnow()\n            )\n            db.session.add(task)\n            task_count += 1\n\n    db.session.commit()\n    print(f\"✅ Creati {task_count} task di esempio\")\n\ndef seed_timesheets(projects, users):\n    \"\"\"Crea timesheet di esempio per gli ultimi 3 mesi.\"\"\"\n    from random import randint, choice\n\n    timesheet_count = 0\n\n    # Genera timesheet per gli ultimi 3 mesi\n    for month_offset in range(3):\n        current_date = datetime.now() - timedelta(days=30 * month_offset)\n        year = current_date.year\n        month = current_date.month\n\n        # Giorni del mese\n        from calendar import monthrange\n        days_in_month = monthrange(year, month)[1]\n\n        # Per ogni progetto attivo\n        for project in projects:  # Tutti i progetti\n            if not project.team_members:\n                continue\n\n            # Per ogni membro del team\n            for member in project.team_members:\n                # Genera timesheet per giorni casuali del mese (15-22 giorni lavorativi)\n                working_days = randint(15, 22)\n                worked_days = []\n\n                # Seleziona giorni casuali (evita weekend)\n                for _ in range(working_days):\n                    day = randint(1, days_in_month)\n                    if day not in worked_days:\n                        worked_days.append(day)\n\n                # Per ogni giorno lavorato\n                for day in worked_days:\n                    work_date = datetime(year, month, day).date()\n\n                    # Seleziona task casuali del progetto\n                    project_tasks = [t for t in Task.query.filter_by(project_id=project.id).all()]\n                    if not project_tasks:\n                        continue\n\n                    # Lavora su 1-3 task per giorno\n                    daily_tasks = choice([1, 1, 2, 2, 3])  # Più probabilità di 1-2 task\n                    selected_tasks = []\n\n                    for _ in range(min(daily_tasks, len(project_tasks))):\n                        task = choice(project_tasks)\n                        if task not in selected_tasks:\n                            selected_tasks.append(task)\n\n                    # Distribuisci 6-9 ore tra i task del giorno\n                    total_daily_hours = randint(6, 9)\n                    hours_per_task = total_daily_hours / len(selected_tasks)\n\n                    for task in selected_tasks:\n                        # Varia leggermente le ore per task\n                        task_hours = round(hours_per_task + randint(-1, 1) * 0.5, 1)\n                        if task_hours <= 0:\n                            task_hours = 0.5\n\n                        timesheet = TimesheetEntry(\n                            user_id=member.id,\n                            project_id=project.id,\n                            task_id=task.id,\n                            date=work_date,\n                            hours=task_hours,\n                            description=f\"Lavoro su {task.name}\",\n                            created_at=datetime.utcnow()\n                        )\n                        db.session.add(timesheet)\n                        timesheet_count += 1\n\n    db.session.commit()\n    print(f\"✅ Creati {timesheet_count} timesheet di esempio per 3 mesi\")\n\ndef main():\n    parser = argparse.ArgumentParser(description='Popola il database con dati di test')\n    parser.add_argument('--clear', action='store_true', help='Cancella i dati di test esistenti prima di creare i nuovi (preserva utenti)')\n    args = parser.parse_args()\n\n    app = create_app()\n\n    with app.app_context():\n        print(\"🌱 Avvio seeding del database...\")\n\n        if args.clear:\n            clear_test_data()\n\n        # Crea i dati in ordine di dipendenza\n        users = seed_users()\n        clients = seed_clients()\n        skills = seed_skills()\n        kpis = seed_kpis()\n        projects = seed_projects(users, clients, kpis)\n\n        print(\"\\n🎉 Seeding completato!\")\n        print(\"\\n📋 Credenziali di accesso (se creati):\")\n        print(\"Admin: <EMAIL> / admin123\")\n        print(\"Manager: <EMAIL> / manager123\")\n        print(\"Employee: <EMAIL> / dev123\")\n        print(\"\\n🔗 Ora puoi testare l'applicazione con dati realistici!\")\n\nif __name__ == '__main__':\n    main()\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nScript per popolare il database con dati di test.\nUso: python seed_data.py [--clear]\n\"\"\"\n\nimport sys\nimport argparse\nfrom datetime import datetime, timedelta\nfrom werkzeug.security import generate_password_hash\n\nfrom app import create_app, db\nfrom models import (\n    User, Client, Project, Task, KPI, ProjectKPI,\n    TimesheetEntry, Event, Contact, Skill, AdminLog, Notification\n)\n\ndef clear_test_data():\n    \"\"\"Cancella solo i dati di test, preservando gli utenti esistenti.\"\"\"\n    print(\"🗑️  Cancellazione dati di test esistenti...\")\n\n    # Ordine importante per rispettare le foreign key\n    # NON cancelliamo AdminLog e Notification per preservare la cronologia\n    db.session.query(ProjectKPI).delete()\n    db.session.query(TimesheetEntry).delete()\n    db.session.query(Task).delete()\n    db.session.query(Event).delete()\n    db.session.query(Contact).delete()\n\n    # Rimuovi associazioni many-to-many\n    for project in Project.query.all():\n        project.team_members.clear()\n\n    db.session.query(Project).delete()\n    db.session.query(Client).delete()\n    db.session.query(KPI).delete()\n    db.session.query(Skill).delete()\n\n    # NON cancelliamo gli utenti esistenti!\n\n    db.session.commit()\n    print(\"✅ Dati di test puliti (utenti preservati)!\")\n\ndef seed_users():\n    \"\"\"Crea utenti di test solo se non esistono già.\"\"\"\n    print(\"👥 Verifica/Creazione utenti...\")\n\n    users_data = [\n        {\n            'username': 'admin',\n            'email': '<EMAIL>',\n            'first_name': 'Admin',\n            'last_name': 'User',\n            'role': 'admin',\n            'password': 'admin123'\n        },\n        {\n            'username': 'mario.rossi',\n            'email': '<EMAIL>',\n            'first_name': 'Mario',\n            'last_name': 'Rossi',\n            'role': 'manager',\n            'password': 'manager123'\n        },\n        {\n            'username': 'giulia.bianchi',\n            'email': '<EMAIL>',\n            'first_name': 'Giulia',\n            'last_name': 'Bianchi',\n            'role': 'employee',\n            'password': 'dev123'\n        },\n        {\n            'username': 'marco.verdi',\n            'email': '<EMAIL>',\n            'first_name': 'Marco',\n            'last_name': 'Verdi',\n            'role': 'employee',\n            'password': 'dev123'\n        },\n        {\n            'username': 'sara.neri',\n            'email': '<EMAIL>',\n            'first_name': 'Sara',\n            'last_name': 'Neri',\n            'role': 'employee',\n            'password': 'design123'\n        }\n    ]\n\n    users = []\n    created_count = 0\n\n    for user_data in users_data:\n        # Controlla se l'utente esiste già (per email o username)\n        existing_user = User.query.filter(\n            (User.email == user_data['email']) |\n            (User.username == user_data['username'])\n        ).first()\n        if existing_user:\n            users.append(existing_user)\n            print(f\"  ✓ Utente {user_data['email']} già esistente\")\n        else:\n            user = User(\n                username=user_data['username'],\n                email=user_data['email'],\n                first_name=user_data['first_name'],\n                last_name=user_data['last_name'],\n                role=user_data['role'],\n                password_hash=generate_password_hash(user_data['password']),\n                is_active=True,\n                created_at=datetime.utcnow()\n            )\n            db.session.add(user)\n            users.append(user)\n            created_count += 1\n            print(f\"  + Creato utente {user_data['email']}\")\n\n    db.session.commit()\n    print(f\"✅ {created_count} nuovi utenti creati, {len(users)} totali disponibili\")\n    return users\n\ndef seed_clients():\n    \"\"\"Crea clienti di test.\"\"\"\n    print(\"🏢 Creazione clienti...\")\n\n    clients_data = [\n        {\n            'name': 'TechCorp S.r.l.',\n            'industry': 'Technology',\n            'description': 'Azienda leader nel settore tecnologico',\n            'website': 'https://techcorp.example.com',\n            'address': 'Via Roma 123, Milano'\n        },\n        {\n            'name': 'Fashion House',\n            'industry': 'Fashion',\n            'description': 'Brand di moda di lusso',\n            'website': 'https://fashionhouse.example.com',\n            'address': 'Via Montenapoleone 45, Milano'\n        },\n        {\n            'name': 'Green Energy Solutions',\n            'industry': 'Energy',\n            'description': 'Soluzioni per energie rinnovabili',\n            'website': 'https://greenenergy.example.com',\n            'address': 'Corso Buenos Aires 78, Milano'\n        }\n    ]\n\n    clients = []\n    for client_data in clients_data:\n        client = Client(\n            name=client_data['name'],\n            industry=client_data['industry'],\n            description=client_data['description'],\n            website=client_data['website'],\n            address=client_data['address'],\n            created_at=datetime.utcnow()\n        )\n        db.session.add(client)\n        clients.append(client)\n\n    db.session.commit()\n    print(f\"✅ Creati {len(clients)} clienti\")\n    return clients\n\ndef seed_skills():\n    \"\"\"Crea competenze di test.\"\"\"\n    print(\"🎯 Creazione competenze...\")\n\n    skills_data = [\n        {'name': 'Python', 'category': 'Programming', 'description': 'Linguaggio di programmazione Python'},\n        {'name': 'JavaScript', 'category': 'Programming', 'description': 'Linguaggio di programmazione JavaScript'},\n        {'name': 'React', 'category': 'Frontend', 'description': 'Libreria React per UI'},\n        {'name': 'Vue.js', 'category': 'Frontend', 'description': 'Framework Vue.js'},\n        {'name': 'Node.js', 'category': 'Backend', 'description': 'Runtime Node.js'},\n        {'name': 'PostgreSQL', 'category': 'Database', 'description': 'Database PostgreSQL'},\n        {'name': 'Docker', 'category': 'DevOps', 'description': 'Containerizzazione con Docker'},\n        {'name': 'Project Management', 'category': 'Management', 'description': 'Gestione progetti'},\n        {'name': 'UI/UX Design', 'category': 'Design', 'description': 'Design interfacce utente'},\n        {'name': 'Digital Marketing', 'category': 'Marketing', 'description': 'Marketing digitale'}\n    ]\n\n    skills = []\n    for skill_data in skills_data:\n        skill = Skill(\n            name=skill_data['name'],\n            category=skill_data['category'],\n            description=skill_data['description']\n        )\n        db.session.add(skill)\n        skills.append(skill)\n\n    db.session.commit()\n    print(f\"✅ Creati {len(skills)} competenze\")\n    return skills\n\ndef seed_kpis():\n    \"\"\"Crea KPI di test.\"\"\"\n    print(\"📊 Creazione KPI...\")\n\n    kpis_data = [\n        {\n            'name': 'Fatturato Mensile',\n            'description': 'Fatturato totale mensile dell\\'azienda',\n            'category': 'Finanziario',\n            'frequency': 'monthly',\n            'unit': 'EUR'\n        },\n        {\n            'name': 'Soddisfazione Cliente',\n            'description': 'Punteggio medio di soddisfazione clienti',\n            'category': 'Qualità',\n            'frequency': 'quarterly',\n            'unit': 'score'\n        },\n        {\n            'name': 'Progetti Completati',\n            'description': 'Numero di progetti completati nel periodo',\n            'category': 'Operativo',\n            'frequency': 'monthly',\n            'unit': 'count'\n        },\n        {\n            'name': 'Ore Fatturabili',\n            'description': 'Ore fatturabili per progetto',\n            'category': 'Operativo',\n            'frequency': 'weekly',\n            'unit': 'hours'\n        },\n        {\n            'name': 'ROI Progetto',\n            'description': 'Return on Investment del progetto',\n            'category': 'Finanziario',\n            'frequency': 'project_end',\n            'unit': 'percentage'\n        }\n    ]\n\n    kpis = []\n    for kpi_data in kpis_data:\n        kpi = KPI(\n            name=kpi_data['name'],\n            description=kpi_data['description'],\n            category=kpi_data['category'],\n            frequency=kpi_data['frequency'],\n            unit=kpi_data['unit'],\n            created_at=datetime.utcnow()\n        )\n        db.session.add(kpi)\n        kpis.append(kpi)\n\n    db.session.commit()\n    print(f\"✅ Creati {len(kpis)} KPI\")\n    return kpis\n\ndef seed_projects(users, clients, kpis):\n    \"\"\"Crea progetti di test.\"\"\"\n    print(\"🚀 Creazione progetti...\")\n\n    projects_data = [\n        {\n            'name': 'E-commerce Platform',\n            'description': 'Sviluppo di una piattaforma e-commerce completa con gestione ordini, pagamenti e inventario',\n            'client_id': clients[0].id,\n            'status': 'active',\n            'budget': 50000.0,\n            'expenses': 15000.0,\n            'start_date': datetime.now().date() - timedelta(days=30),\n            'end_date': datetime.now().date() + timedelta(days=60),\n            'team_members': [users[1], users[2], users[3]]  # Manager + 2 dev\n        },\n        {\n            'name': 'Brand Redesign',\n            'description': 'Redesign completo del brand e creazione di nuova identità visiva',\n            'client_id': clients[1].id,\n            'status': 'planning',\n            'budget': 25000.0,\n            'expenses': 2000.0,\n            'start_date': datetime.now().date() + timedelta(days=7),\n            'end_date': datetime.now().date() + timedelta(days=45),\n            'team_members': [users[1], users[4]]  # Manager + Designer\n        },\n        {\n            'name': 'Energy Management System',\n            'description': 'Sistema di gestione e monitoraggio per impianti di energia rinnovabile',\n            'client_id': clients[2].id,\n            'status': 'active',\n            'budget': 75000.0,\n            'expenses': 25000.0,\n            'start_date': datetime.now().date() - timedelta(days=15),\n            'end_date': datetime.now().date() + timedelta(days=90),\n            'team_members': [users[1], users[2], users[3], users[4]]  # Tutto il team\n        },\n        {\n            'name': 'Internal CRM',\n            'description': 'Sistema CRM interno per gestione clienti e opportunità',\n            'client_id': None,  # Progetto interno\n            'status': 'completed',\n            'budget': 30000.0,\n            'expenses': 28000.0,\n            'start_date': datetime.now().date() - timedelta(days=120),\n            'end_date': datetime.now().date() - timedelta(days=30),\n            'team_members': [users[2], users[3]]  # Solo dev\n        }\n    ]\n\n    projects = []\n    for project_data in projects_data:\n        project = Project(\n            name=project_data['name'],\n            description=project_data['description'],\n            client_id=project_data['client_id'],\n            status=project_data['status'],\n            budget=project_data['budget'],\n            expenses=project_data['expenses'],\n            start_date=project_data['start_date'],\n            end_date=project_data['end_date'],\n            created_at=datetime.utcnow()\n        )\n\n        # Aggiungi team members\n        for member in project_data['team_members']:\n            project.team_members.append(member)\n\n        db.session.add(project)\n        projects.append(project)\n\n    db.session.commit()\n\n    # Aggiungi KPI ai progetti\n    print(\"📈 Collegamento KPI ai progetti...\")\n    project_kpis_data = [\n        {'project': projects[0], 'kpi': kpis[0], 'target_value': 45000.0, 'current_value': 30000.0},\n        {'project': projects[0], 'kpi': kpis[2], 'target_value': 1.0, 'current_value': 0.0},\n        {'project': projects[1], 'kpi': kpis[1], 'target_value': 9.0, 'current_value': 8.5},\n        {'project': projects[2], 'kpi': kpis[3], 'target_value': 500.0, 'current_value': 320.0},\n        {'project': projects[3], 'kpi': kpis[4], 'target_value': 150.0, 'current_value': 140.0},\n    ]\n\n    for pk_data in project_kpis_data:\n        project_kpi = ProjectKPI(\n            project_id=pk_data['project'].id,\n            kpi_id=pk_data['kpi'].id,\n            target_value=pk_data['target_value'],\n            current_value=pk_data['current_value']\n        )\n        db.session.add(project_kpi)\n\n    db.session.commit()\n    print(f\"✅ Creati {len(projects)} progetti con KPI collegati\")\n\n    # Aggiungi task ai progetti\n    print(\"📋 Creazione task di esempio...\")\n    seed_tasks(projects, users)\n\n    # Aggiungi timesheet di esempio\n    print(\"⏱️ Creazione timesheet di esempio...\")\n    seed_timesheets(projects, users)\n\n    return projects\n\ndef seed_tasks(projects, users):\n    \"\"\"Crea task di esempio per i progetti.\"\"\"\n    from random import choice\n\n    tasks_data = [\n        # Tasks per E-commerce Platform\n        {'name': 'Setup Database', 'description': 'Configurazione database PostgreSQL', 'priority': 'high', 'status': 'done'},\n        {'name': 'User Authentication', 'description': 'Sistema di login e registrazione', 'priority': 'high', 'status': 'done'},\n        {'name': 'Product Catalog', 'description': 'Gestione catalogo prodotti', 'priority': 'medium', 'status': 'in-progress'},\n        {'name': 'Shopping Cart', 'description': 'Carrello della spesa', 'priority': 'medium', 'status': 'todo'},\n        {'name': 'Payment Integration', 'description': 'Integrazione gateway pagamenti', 'priority': 'high', 'status': 'todo'},\n\n        # Tasks per Brand Redesign\n        {'name': 'Brand Research', 'description': 'Ricerca e analisi brand esistente', 'priority': 'high', 'status': 'in-progress'},\n        {'name': 'Logo Design', 'description': 'Progettazione nuovo logo', 'priority': 'high', 'status': 'todo'},\n        {'name': 'Color Palette', 'description': 'Definizione palette colori', 'priority': 'medium', 'status': 'todo'},\n\n        # Tasks per Energy Management System\n        {'name': 'System Architecture', 'description': 'Architettura del sistema', 'priority': 'high', 'status': 'done'},\n        {'name': 'Sensor Integration', 'description': 'Integrazione sensori IoT', 'priority': 'high', 'status': 'in-progress'},\n        {'name': 'Dashboard Development', 'description': 'Sviluppo dashboard monitoraggio', 'priority': 'medium', 'status': 'in-progress'},\n        {'name': 'Alert System', 'description': 'Sistema di allerte', 'priority': 'medium', 'status': 'todo'},\n\n        # Tasks per Internal CRM\n        {'name': 'Contact Management', 'description': 'Gestione contatti clienti', 'priority': 'high', 'status': 'done'},\n        {'name': 'Opportunity Tracking', 'description': 'Tracciamento opportunità', 'priority': 'medium', 'status': 'done'},\n        {'name': 'Reporting Module', 'description': 'Modulo di reportistica', 'priority': 'low', 'status': 'done'},\n    ]\n\n    # Assegna task ai progetti\n    project_task_mapping = [\n        (0, [0, 1, 2, 3, 4]),      # E-commerce Platform\n        (1, [5, 6, 7]),            # Brand Redesign\n        (2, [8, 9, 10, 11]),       # Energy Management System\n        (3, [12, 13, 14])          # Internal CRM\n    ]\n\n    task_count = 0\n    for project_idx, task_indices in project_task_mapping:\n        project = projects[project_idx]\n\n        for task_idx in task_indices:\n            task_data = tasks_data[task_idx]\n\n            # Assegna task a un membro del team casuale\n            assignee = choice(project.team_members) if project.team_members else None\n\n            # Calcola date basate sullo status\n            if task_data['status'] == 'done':\n                due_date = project.start_date + timedelta(days=7)\n            elif task_data['status'] == 'in-progress':\n                due_date = project.start_date + timedelta(days=15)\n            else:  # todo\n                due_date = project.start_date + timedelta(days=25)\n\n            task = Task(\n                name=task_data['name'],\n                description=task_data['description'],\n                project_id=project.id,\n                assignee_id=assignee.id if assignee else None,\n                priority=task_data['priority'],\n                status=task_data['status'],\n                due_date=due_date,\n                created_at=datetime.utcnow()\n            )\n            db.session.add(task)\n            task_count += 1\n\n    db.session.commit()\n    print(f\"✅ Creati {task_count} task di esempio\")\n\ndef seed_timesheets(projects, users):\n    \"\"\"Crea timesheet di esempio per gli ultimi 3 mesi.\"\"\"\n    from random import randint, choice\n\n    timesheet_count = 0\n\n    # Genera timesheet per gli ultimi 3 mesi\n    for month_offset in range(3):\n        current_date = datetime.now() - timedelta(days=30 * month_offset)\n        year = current_date.year\n        month = current_date.month\n\n        # Giorni del mese\n        from calendar import monthrange\n        days_in_month = monthrange(year, month)[1]\n\n        # Per ogni progetto attivo\n        for project in projects:  # Tutti i progetti\n            if not project.team_members:\n                continue\n\n            # Per ogni membro del team\n            for member in project.team_members:\n                # Genera timesheet per giorni casuali del mese (15-22 giorni lavorativi)\n                working_days = randint(15, 22)\n                worked_days = []\n\n                # Seleziona giorni casuali (evita weekend)\n                for _ in range(working_days):\n                    day = randint(1, days_in_month)\n                    if day not in worked_days:\n                        worked_days.append(day)\n\n                # Per ogni giorno lavorato\n                for day in worked_days:\n                    work_date = datetime(year, month, day).date()\n\n                    # Seleziona task casuali del progetto\n                    project_tasks = [t for t in Task.query.filter_by(project_id=project.id).all()]\n                    if not project_tasks:\n                        continue\n\n                    # Lavora su 1-3 task per giorno\n                    daily_tasks = choice([1, 1, 2, 2, 3])  # Più probabilità di 1-2 task\n                    selected_tasks = []\n\n                    for _ in range(min(daily_tasks, len(project_tasks))):\n                        task = choice(project_tasks)\n                        if task not in selected_tasks:\n                            selected_tasks.append(task)\n\n                    # Distribuisci 6-9 ore tra i task del giorno\n                    total_daily_hours = randint(6, 9)\n                    hours_per_task = total_daily_hours / len(selected_tasks)\n\n                    for task in selected_tasks:\n                        # Varia leggermente le ore per task\n                        task_hours = round(hours_per_task + randint(-1, 1) * 0.5, 1)\n                        if task_hours <= 0:\n                            task_hours = 0.5\n\n                        timesheet = TimesheetEntry(\n                            user_id=member.id,\n                            project_id=project.id,\n                            task_id=task.id,\n                            date=work_date,\n                            hours=task_hours,\n                            description=f\"Lavoro su {task.name}\",\n                            created_at=datetime.utcnow()\n                        )\n                        db.session.add(timesheet)\n                        timesheet_count += 1\n\n    db.session.commit()\n    print(f\"✅ Creati {timesheet_count} timesheet di esempio per 3 mesi\")\n\ndef main():\n    parser = argparse.ArgumentParser(description='Popola il database con dati di test')\n    parser.add_argument('--clear', action='store_true', help='Cancella i dati di test esistenti prima di creare i nuovi (preserva utenti)')\n    args = parser.parse_args()\n\n    app = create_app()\n\n    with app.app_context():\n        print(\"🌱 Avvio seeding del database...\")\n\n        if args.clear:\n            clear_test_data()\n\n        # Crea i dati in ordine di dipendenza\n        users = seed_users()\n        clients = seed_clients()\n        skills = seed_skills()\n        kpis = seed_kpis()\n        projects = seed_projects(users, clients, kpis)\n\n        print(\"\\n🎉 Seeding completato!\")\n        print(\"\\n📋 Credenziali di accesso (se creati):\")\n        print(\"Admin: <EMAIL> / admin123\")\n        print(\"Manager: <EMAIL> / manager123\")\n        print(\"Employee: <EMAIL> / dev123\")\n        print(\"\\n🔗 Ora puoi testare l'applicazione con dati realistici!\")\n\nif __name__ == '__main__':\n    main()\n"}