{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/utils/api.js"}, "originalCode": "import axios from 'axios'\n\nconst api = axios.create({\n  baseURL: '', // Usa il proxy di Vite in sviluppo, nessun baseURL in produzione\n  timeout: 10000,\n  withCredentials: true, // Importante per le sessioni Flask\n  headers: {\n    'Content-Type': 'application/json',\n  }\n})\n\n// Request interceptor per aggiungere CSRF token\napi.interceptors.request.use(\n  (config) => {\n    // Aggiungi CSRF token per richieste che ne hanno bisogno\n    const csrfToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content')\n    if (csrfToken && ['post', 'put', 'patch', 'delete'].includes(config.method?.toLowerCase())) {\n      config.headers['X-CSRFToken'] = csrfToken\n    }\n\n    return config\n  },\n  (error) => {\n    return Promise.reject(error)\n  }\n)\n\n// Response interceptor per gestire errori globali\napi.interceptors.response.use(\n  (response) => {\n    return response\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Sessione scaduta, pulisci i dati locali e redirect al login\n      localStorage.removeItem('user')\n      // Non fare redirect automatico, lascia che sia il router a gestirlo\n      console.warn('Sessione scaduta, autenticazione richiesta')\n    }\n    return Promise.reject(error)\n  }\n)\n\nexport default api", "modifiedCode": "import axios from 'axios'\n\nconst api = axios.create({\n  baseURL: import.meta.env.DEV ? '' : '', // Forza l'uso del proxy in sviluppo\n  timeout: 10000,\n  withCredentials: true, // Importante per le sessioni Flask\n  headers: {\n    'Content-Type': 'application/json',\n  }\n})\n\n// Debug per verificare la configurazione\nif (import.meta.env.DEV) {\n  console.log('🔧 API in modalità sviluppo - usando proxy Vite')\n  console.log('🔧 BaseURL:', api.defaults.baseURL)\n}\n\n// Request interceptor per aggiungere CSRF token\napi.interceptors.request.use(\n  (config) => {\n    // Aggiungi CSRF token per richieste che ne hanno bisogno\n    const csrfToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content')\n    if (csrfToken && ['post', 'put', 'patch', 'delete'].includes(config.method?.toLowerCase())) {\n      config.headers['X-CSRFToken'] = csrfToken\n    }\n\n    return config\n  },\n  (error) => {\n    return Promise.reject(error)\n  }\n)\n\n// Response interceptor per gestire errori globali\napi.interceptors.response.use(\n  (response) => {\n    return response\n  },\n  (error) => {\n    if (error.response?.status === 401) {\n      // Sessione scaduta, pulisci i dati locali e redirect al login\n      localStorage.removeItem('user')\n      // Non fare redirect automatico, lascia che sia il router a gestirlo\n      console.warn('Sessione scaduta, autenticazione richiesta')\n    }\n    return Promise.reject(error)\n  }\n)\n\nexport default api"}