{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/projects.py"}, "originalCode": "\"\"\"\nAPI RESTful per la gestione dei progetti.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom sqlalchemy import desc\nfrom models import Project, Task, ProjectResource, ProjectKPI, User\nfrom utils.api_utils import (\n    api_response, handle_api_error, get_pagination_params,\n    format_pagination, api_permission_required\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_EDIT_PROJECT,\n    PERMISSION_DELETE_PROJECT\n)\nfrom extensions import db\n\n# Crea il blueprint per le API dei progetti\napi_projects = Blueprint('api_projects', __name__)\n\ndef calculate_project_kpis(project):\n    \"\"\"\n    Calcola i KPI aggregati per un progetto.\n    \"\"\"\n    try:\n        # Calcola ore totali dai timesheet\n        total_hours = 0\n        for entry in project.timesheet_entries:\n            total_hours += entry.hours if entry.hours else 0\n\n        # Calcola ore stimate dai task\n        estimated_hours = 0\n        for task in project.tasks:\n            estimated_hours += task.estimated_hours if task.estimated_hours else 0\n\n        # Calcola utilizzo budget\n        budget_usage = 0\n        if project.budget and project.budget > 0:\n            budget_usage = round((project.expenses / project.budget) * 100, 1) if project.expenses else 0\n\n        # Calcola utilizzo tempo\n        time_usage = 0\n        if estimated_hours > 0:\n            time_usage = round((total_hours / estimated_hours) * 100, 1)\n\n        # Calcola margine\n        margin = 0\n        if project.budget and project.budget > 0:\n            # Calcola costi del personale (ore * tariffa media)\n            personnel_cost = total_hours * 50  # Assumiamo 50€/ora come media\n            total_costs = personnel_cost + (project.expenses or 0)\n            margin = round(((project.budget - total_costs) / project.budget) * 100, 1)\n\n        return {\n            'budget_usage': budget_usage,\n            'time_usage': time_usage,\n            'margin': margin,\n            'total_hours': total_hours,\n            'estimated_hours': estimated_hours\n        }\n    except Exception as e:\n        current_app.logger.error(f\"Error calculating KPIs for project {project.id}: {str(e)}\")\n        return {\n            'budget_usage': 0,\n            'time_usage': 0,\n            'margin': 0,\n            'total_hours': 0,\n            'estimated_hours': 0\n        }\n\n@api_projects.route('/', methods=['GET'])\n@login_required\ndef get_projects():\n    \"\"\"\n    Ottiene la lista dei progetti con supporto per filtri e paginazione.\n    ---\n    tags:\n      - projects\n    parameters:\n      - $ref: '#/components/parameters/pageParam'\n      - $ref: '#/components/parameters/perPageParam'\n      - name: status\n        in: query\n        description: Filtra per stato del progetto\n        schema:\n          type: string\n          enum: [planning, active, completed, on-hold]\n      - name: client_id\n        in: query\n        description: Filtra per ID cliente\n        schema:\n          type: integer\n      - name: search\n        in: query\n        description: Cerca nei nomi e nelle descrizioni dei progetti\n        schema:\n          type: string\n    responses:\n      200:\n        description: Lista di progetti\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    projects:\n                      type: array\n                      items:\n                        $ref: '#/components/schemas/Project'\n                pagination:\n                  $ref: '#/components/schemas/Pagination'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n    \"\"\"\n    try:\n        # Ottieni parametri di paginazione\n        page, per_page = get_pagination_params()\n\n        # Inizia la query\n        query = Project.query\n\n        # Applica filtri\n        status = request.args.get('status')\n        if status:\n            query = query.filter(Project.status == status)\n\n        client_id = request.args.get('client_id', type=int)\n        if client_id:\n            query = query.filter(Project.client_id == client_id)\n\n        search = request.args.get('search')\n        if search:\n            search_term = f\"%{search}%\"\n            query = query.filter(\n                (Project.name.ilike(search_term)) |\n                (Project.description.ilike(search_term))\n            )\n\n        # Applica ordinamento\n        query = query.order_by(desc(Project.updated_at))\n\n        # Esegui query con paginazione\n        pagination = query.paginate(page=page, per_page=per_page)\n\n        # Prepara i dati dei progetti\n        projects_data = []\n        for project in pagination.items:\n            # Serializza i dati del cliente se presente\n            client_data = None\n            if project.client:\n                client_data = {\n                    'id': project.client.id,\n                    'name': project.client.name,\n                    'industry': project.client.industry\n                }\n\n            # Serializza i dati del contratto se presente\n            contract_data = None\n            if project.contract:\n                contract_data = {\n                    'id': project.contract.id,\n                    'contract_number': project.contract.contract_number,\n                    'title': project.contract.title,\n                    'contract_type': project.contract.contract_type,\n                    'hourly_rate': float(project.contract.hourly_rate) if project.contract.hourly_rate else None,\n                    'status': project.contract.status\n                }\n\n            # Calcola KPI aggregati per il progetto\n            kpis_data = calculate_project_kpis(project)\n\n            projects_data.append({\n                'id': project.id,\n                'name': project.name,\n                'description': project.description,\n                'client_id': project.client_id,\n                'client': client_data,\n                'project_type': project.project_type,\n                'is_billable': project.is_billable,\n                'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,\n                'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,\n                'start_date': project.start_date.isoformat() if project.start_date else None,\n                'end_date': project.end_date.isoformat() if project.end_date else None,\n                'status': project.status,\n                'budget': float(project.budget) if project.budget else None,\n                'expenses': float(project.expenses) if project.expenses else None,\n                'created_at': project.created_at.isoformat(),\n                'updated_at': project.updated_at.isoformat(),\n                'kpis': kpis_data\n            })\n\n        # Restituisci risposta\n        return api_response(\n            data={'projects': projects_data},\n            pagination=format_pagination(pagination)\n        )\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_projects: {str(e)}\")\n        return handle_api_error(e)\n\n@api_projects.route('/<int:project_id>', methods=['GET'])\n@login_required\ndef get_project(project_id):\n    \"\"\"\n    Ottiene i dettagli di un progetto specifico.\n    ---\n    tags:\n      - projects\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Dettagli del progetto\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    project:\n                      $ref: '#/components/schemas/Project'\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n    \"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Serializza i dati del cliente se presente\n        client_data = None\n        if project.client:\n            client_data = {\n                'id': project.client.id,\n                'name': project.client.name,\n                'industry': project.client.industry\n            }\n\n        # Calcola dati aggregati\n        tasks = project.tasks.all()\n        task_count = len(tasks)\n        completed_tasks = len([t for t in tasks if t.status == 'completed'])\n\n        # Serializza task\n        tasks_data = []\n        for task in tasks:\n            assignee_data = None\n            if task.assignee_id:\n                assignee = next((m for m in project.team_members if m.id == task.assignee_id), None)\n                if assignee:\n                    assignee_data = {\n                        'id': assignee.id,\n                        'full_name': assignee.full_name\n                    }\n\n            tasks_data.append({\n                'id': task.id,\n                'name': task.name,\n                'description': task.description,\n                'status': task.status,\n                'priority': task.priority,\n                'assignee_id': task.assignee_id,\n                'assignee': assignee_data,\n                'start_date': task.start_date.isoformat() if task.start_date else None,\n                'due_date': task.due_date.isoformat() if task.due_date else None,\n                'estimated_hours': task.estimated_hours,\n                'actual_hours': task.actual_hours,\n                'created_at': task.created_at.isoformat() if task.created_at else None\n            })\n\n        # Calcola ore totali dai timesheet\n        total_hours = 0\n        for entry in project.timesheet_entries:\n            total_hours += entry.hours if entry.hours else 0\n\n        # Serializza team members\n        team_members_data = []\n        for member in project.team_members:\n            # Calcola ore lavorate per questo membro\n            member_hours = 0\n            for entry in project.timesheet_entries:\n                if entry.user_id == member.id:\n                    member_hours += entry.hours if entry.hours else 0\n\n            team_members_data.append({\n                'id': member.id,\n                'full_name': member.full_name,\n                'email': member.email,\n                'profile_image': member.profile_image,\n                'role': 'Team Member',  # TODO: Get actual role from project_team table\n                'hours_worked': member_hours\n            })\n\n        # Prepara i dati del progetto\n        project_data = {\n            'id': project.id,\n            'name': project.name,\n            'description': project.description,\n            'client_id': project.client_id,\n            'client': client_data,\n            'project_type': project.project_type,\n            'is_billable': project.is_billable,\n            'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,\n            'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,\n            'start_date': project.start_date.isoformat() if project.start_date else None,\n            'end_date': project.end_date.isoformat() if project.end_date else None,\n            'status': project.status,\n            'budget': float(project.budget) if project.budget else None,\n            'expenses': float(project.expenses) if project.expenses else None,\n            'created_at': project.created_at.isoformat(),\n            'updated_at': project.updated_at.isoformat(),\n            # Dati aggregati per l'overview\n            'task_count': task_count,\n            'completed_tasks': completed_tasks,\n            'team_count': len(project.team_members),\n            'team_members': team_members_data,\n            'tasks': tasks_data,\n            'total_hours': total_hours,\n            'estimated_hours': sum(t.estimated_hours or 0 for t in tasks),\n            'remaining_budget': float(project.budget - project.expenses) if project.budget and project.expenses else None\n        }\n\n        return api_response(data={'project': project_data})\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_project: {str(e)}\")\n        return handle_api_error(e)\n\n@api_projects.route('/<int:project_id>/team', methods=['POST'])\n@login_required\ndef add_team_member(project_id):\n    \"\"\"Aggiunge un membro al team del progetto.\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n        data = request.get_json()\n\n        user_id = data.get('user_id')\n        role = data.get('role', 'Team Member')\n\n        if not user_id:\n            return api_response(False, 'user_id richiesto', status_code=400)\n\n        user = User.query.get_or_404(user_id)\n\n        # Verifica se l'utente è già nel team\n        if user in project.team_members:\n            return api_response(False, 'Utente già nel team', status_code=400)\n\n        # Aggiungi al team\n        project.team_members.append(user)\n        db.session.commit()\n\n        return api_response(data={}, message='Membro aggiunto al team con successo')\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_projects.route('/<int:project_id>/team/<int:user_id>', methods=['DELETE'])\n@login_required\ndef remove_team_member(project_id, user_id):\n    \"\"\"Rimuove un membro dal team del progetto.\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n        user = User.query.get_or_404(user_id)\n\n        # Non rimuovere il project manager\n        if user_id == project.manager_id:\n            return api_response(False, 'Non è possibile rimuovere il project manager', status_code=400)\n\n        # Verifica se l'utente è nel team\n        if user not in project.team_members:\n            return api_response(False, 'Utente non nel team', status_code=400)\n\n        # Rimuovi dal team\n        project.team_members.remove(user)\n        db.session.commit()\n\n        return api_response(data={}, message='Membro rimosso dal team con successo')\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_projects.route('/', methods=['POST'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef create_project():\n    \"\"\"\n    Crea un nuovo progetto.\n    ---\n    tags:\n      - projects\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - name\n            properties:\n              name:\n                type: string\n                description: Nome del progetto\n              description:\n                type: string\n                description: Descrizione del progetto\n              client_id:\n                type: integer\n                description: ID del cliente associato\n              client:\n                type: string\n                description: Nome del cliente (alternativo a client_id)\n              project_type:\n                type: string\n                enum: [service, license, consulting, product, rd, internal]\n                default: service\n                description: Tipologia del progetto\n              is_billable:\n                type: boolean\n                default: true\n                description: Se il progetto è fatturabile\n              client_daily_rate:\n                type: number\n                description: Tariffa giornaliera del cliente\n              markup_percentage:\n                type: number\n                description: Percentuale di markup applicata\n              start_date:\n                type: string\n                format: date\n                description: Data di inizio (YYYY-MM-DD)\n              end_date:\n                type: string\n                format: date\n                description: Data di fine (YYYY-MM-DD)\n              status:\n                type: string\n                enum: [planning, active, completed, on-hold, cancelled]\n                default: planning\n                description: Stato del progetto\n              budget:\n                type: number\n                description: Budget del progetto\n              team_members:\n                type: array\n                items:\n                  type: integer\n                description: Lista di ID utenti da assegnare al progetto\n    responses:\n      201:\n        description: Progetto creato con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    project:\n                      $ref: '#/components/schemas/Project'\n                    message:\n                      type: string\n                      example: Progetto creato con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: Nome del progetto obbligatorio\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Ottieni i dati dalla richiesta\n        data = request.json\n\n        # Validazione dei dati\n        if not data or not data.get('name'):\n            return api_response(\n                message=\"Nome del progetto obbligatorio\",\n                status_code=400\n            )\n\n        # Crea il nuovo progetto\n        new_project = Project(\n            name=data.get('name'),\n            description=data.get('description', ''),\n            client_id=data.get('client_id'),\n            project_type=data.get('project_type', 'service'),\n            is_billable=data.get('is_billable', True),\n            client_daily_rate=data.get('client_daily_rate'),\n            markup_percentage=data.get('markup_percentage'),\n            start_date=data.get('start_date'),\n            end_date=data.get('end_date'),\n            status=data.get('status', 'planning'),\n            budget=data.get('budget')\n        )\n\n        # Aggiungi il progetto al database\n        db.session.add(new_project)\n        db.session.flush()  # Per ottenere l'ID del progetto\n\n        # Aggiungi i membri del team se specificati\n        team_members = data.get('team_members', [])\n        if team_members:\n            for user_id in team_members:\n                user = User.query.get(user_id)\n                if user:\n                    new_project.team_members.append(user)\n\n        # Aggiungi l'utente corrente al team se non è già incluso\n        if current_user.id not in team_members:\n            new_project.team_members.append(current_user)\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        # Serializza i dati del cliente se presente\n        client_data = None\n        if new_project.client:\n            client_data = {\n                'id': new_project.client.id,\n                'name': new_project.client.name,\n                'industry': new_project.client.industry\n            }\n\n        # Prepara i dati del progetto per la risposta\n        project_data = {\n            'id': new_project.id,\n            'name': new_project.name,\n            'description': new_project.description,\n            'client_id': new_project.client_id,\n            'client': client_data,\n            'project_type': new_project.project_type,\n            'is_billable': new_project.is_billable,\n            'client_daily_rate': float(new_project.client_daily_rate) if new_project.client_daily_rate else None,\n            'markup_percentage': float(new_project.markup_percentage) if new_project.markup_percentage else None,\n            'start_date': new_project.start_date.isoformat() if new_project.start_date else None,\n            'end_date': new_project.end_date.isoformat() if new_project.end_date else None,\n            'status': new_project.status,\n            'budget': float(new_project.budget) if new_project.budget else None,\n            'expenses': float(new_project.expenses) if new_project.expenses else None,\n            'created_at': new_project.created_at.isoformat(),\n            'updated_at': new_project.updated_at.isoformat()\n        }\n\n        return api_response(\n            data={'project': project_data},\n            message=\"Progetto creato con successo\",\n            status_code=201\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in create_project: {str(e)}\")\n        return handle_api_error(e)\n\n@api_projects.route('/<int:project_id>', methods=['PUT'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef update_project(project_id):\n    \"\"\"\n    Aggiorna un progetto esistente.\n    ---\n    tags:\n      - projects\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto da aggiornare\n        schema:\n          type: integer\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              name:\n                type: string\n                description: Nome del progetto\n              description:\n                type: string\n                description: Descrizione del progetto\n              client_id:\n                type: integer\n                description: ID del cliente associato\n              start_date:\n                type: string\n                format: date\n                description: Data di inizio (YYYY-MM-DD)\n              end_date:\n                type: string\n                format: date\n                description: Data di fine (YYYY-MM-DD)\n              status:\n                type: string\n                enum: [planning, active, completed, on-hold]\n                description: Stato del progetto\n              budget:\n                type: number\n                description: Budget del progetto\n              team_members:\n                type: array\n                items:\n                  type: integer\n                description: Lista di ID utenti da assegnare al progetto\n    responses:\n      200:\n        description: Progetto aggiornato con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    project:\n                      $ref: '#/components/schemas/Project'\n                    message:\n                      type: string\n                      example: Progetto aggiornato con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: Dati non validi\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Ottieni il progetto dal database\n        project = Project.query.get_or_404(project_id)\n\n        # Ottieni i dati dalla richiesta\n        data = request.json\n        if not data:\n            return api_response(\n                message=\"Nessun dato fornito per l'aggiornamento\",\n                status_code=400\n            )\n\n        # Aggiorna i campi del progetto\n        if 'name' in data:\n            project.name = data['name']\n        if 'description' in data:\n            project.description = data['description']\n        if 'client_id' in data:\n            project.client_id = data['client_id']\n        if 'project_type' in data:\n            project.project_type = data['project_type']\n        if 'is_billable' in data:\n            project.is_billable = data['is_billable']\n        if 'client_daily_rate' in data:\n            project.client_daily_rate = data['client_daily_rate']\n        if 'markup_percentage' in data:\n            project.markup_percentage = data['markup_percentage']\n        if 'start_date' in data:\n            project.start_date = data['start_date']\n        if 'end_date' in data:\n            project.end_date = data['end_date']\n        if 'status' in data:\n            project.status = data['status']\n        if 'budget' in data:\n            project.budget = data['budget']\n\n        # Aggiorna i membri del team se specificati\n        if 'team_members' in data:\n            # Rimuovi tutti i membri attuali\n            project.team_members = []\n\n            # Aggiungi i nuovi membri\n            for user_id in data['team_members']:\n                user = User.query.get(user_id)\n                if user:\n                    project.team_members.append(user)\n\n            # Assicurati che l'utente corrente sia nel team\n            if current_user.id not in data['team_members']:\n                project.team_members.append(current_user)\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        # Serializza i dati del cliente se presente\n        client_data = None\n        if project.client:\n            client_data = {\n                'id': project.client.id,\n                'name': project.client.name,\n                'industry': project.client.industry\n            }\n\n        # Prepara i dati del progetto per la risposta\n        project_data = {\n            'id': project.id,\n            'name': project.name,\n            'description': project.description,\n            'client_id': project.client_id,\n            'client': client_data,\n            'project_type': project.project_type,\n            'is_billable': project.is_billable,\n            'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,\n            'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,\n            'start_date': project.start_date.isoformat() if project.start_date else None,\n            'end_date': project.end_date.isoformat() if project.end_date else None,\n            'status': project.status,\n            'budget': float(project.budget) if project.budget else None,\n            'expenses': float(project.expenses) if project.expenses else None,\n            'created_at': project.created_at.isoformat(),\n            'updated_at': project.updated_at.isoformat()\n        }\n\n        return api_response(\n            data={'project': project_data},\n            message=\"Progetto aggiornato con successo\"\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in update_project: {str(e)}\")\n        return handle_api_error(e)\n\n@api_projects.route('/<int:project_id>', methods=['DELETE'])\n@login_required\n@api_permission_required(PERMISSION_DELETE_PROJECT)\ndef delete_project(project_id):\n    \"\"\"\n    Elimina un progetto esistente.\n    ---\n    tags:\n      - projects\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto da eliminare\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Progetto eliminato con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                message:\n                  type: string\n                  example: \"Progetto eliminato con successo\"\n      403:\n        description: Permessi insufficienti\n      404:\n        description: Progetto non trovato\n      500:\n        description: Errore interno del server\n    \"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Verifica permessi aggiuntivi se necessario\n        # (il decoratore già controlla PERMISSION_DELETE_PROJECT)\n\n        # Elimina il progetto (cascade eliminerà automaticamente task, timesheet, etc.)\n        db.session.delete(project)\n        db.session.commit()\n\n        current_app.logger.info(f\"Progetto {project_id} eliminato da utente {current_user.id}\")\n\n        return api_response(\n            message=f\"Progetto '{project.name}' eliminato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Errore nell'eliminazione progetto {project_id}: {str(e)}\")\n        return handle_api_error(e)\n\n\n@api_projects.route('/batch', methods=['POST'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef batch_projects():\n    \"\"\"\n    Esegue operazioni batch sui progetti.\n    ---\n    tags:\n      - projects\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - operations\n            properties:\n              operations:\n                type: array\n                items:\n                  type: object\n                  required:\n                    - operation\n                  properties:\n                    operation:\n                      type: string\n                      enum: [create, update, delete]\n                      description: Tipo di operazione da eseguire\n                    project_id:\n                      type: integer\n                      description: ID del progetto (richiesto per update e delete)\n                    data:\n                      type: object\n                      description: Dati del progetto (richiesto per create e update)\n    responses:\n      200:\n        description: Operazioni batch eseguite con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    results:\n                      type: array\n                      items:\n                        type: object\n                        properties:\n                          operation:\n                            type: string\n                            example: create\n                          success:\n                            type: boolean\n                            example: true\n                          project_id:\n                            type: integer\n                            example: 1\n                          message:\n                            type: string\n                            example: Progetto creato con successo\n                    message:\n                      type: string\n                      example: Operazioni batch eseguite con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: Dati non validi\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Ottieni i dati dalla richiesta\n        data = request.json\n\n        # Validazione dei dati\n        if not data or 'operations' not in data or not isinstance(data['operations'], list):\n            return api_response(\n                message=\"Operazioni batch non valide\",\n                status_code=400\n            )\n\n        # Risultati delle operazioni\n        results = []\n\n        # Esegui le operazioni\n        for operation_data in data['operations']:\n            # Validazione dell'operazione\n            if 'operation' not in operation_data:\n                results.append({\n                    'operation': 'unknown',\n                    'success': False,\n                    'message': \"Tipo di operazione non specificato\"\n                })\n                continue\n\n            operation = operation_data['operation']\n\n            # Operazione CREATE\n            if operation == 'create':\n                if 'data' not in operation_data or not operation_data['data'].get('name'):\n                    results.append({\n                        'operation': 'create',\n                        'success': False,\n                        'message': \"Nome del progetto obbligatorio\"\n                    })\n                    continue\n\n                try:\n                    # Crea il nuovo progetto\n                    project_data = operation_data['data']\n                    new_project = Project(\n                        name=project_data.get('name'),\n                        description=project_data.get('description', ''),\n                        client_id=project_data.get('client_id'),\n                        start_date=project_data.get('start_date'),\n                        end_date=project_data.get('end_date'),\n                        status=project_data.get('status', 'planning'),\n                        budget=project_data.get('budget')\n                    )\n\n                    # Aggiungi il progetto al database\n                    db.session.add(new_project)\n                    db.session.flush()  # Per ottenere l'ID del progetto\n\n                    # Aggiungi i membri del team se specificati\n                    team_members = project_data.get('team_members', [])\n                    if team_members:\n                        for user_id in team_members:\n                            user = User.query.get(user_id)\n                            if user:\n                                new_project.team_members.append(user)\n\n                    # Aggiungi l'utente corrente al team se non è già incluso\n                    if current_user.id not in team_members:\n                        new_project.team_members.append(current_user)\n\n                    results.append({\n                        'operation': 'create',\n                        'success': True,\n                        'project_id': new_project.id,\n                        'message': \"Progetto creato con successo\"\n                    })\n                except Exception as e:\n                    results.append({\n                        'operation': 'create',\n                        'success': False,\n                        'message': f\"Errore durante la creazione del progetto: {str(e)}\"\n                    })\n\n            # Operazione UPDATE\n            elif operation == 'update':\n                if 'project_id' not in operation_data or 'data' not in operation_data:\n                    results.append({\n                        'operation': 'update',\n                        'success': False,\n                        'message': \"ID progetto e dati obbligatori\"\n                    })\n                    continue\n\n                try:\n                    # Ottieni il progetto dal database\n                    project = Project.query.get(operation_data['project_id'])\n                    if not project:\n                        results.append({\n                            'operation': 'update',\n                            'success': False,\n                            'project_id': operation_data['project_id'],\n                            'message': \"Progetto non trovato\"\n                        })\n                        continue\n\n                    # Aggiorna i campi del progetto\n                    project_data = operation_data['data']\n                    if 'name' in project_data:\n                        project.name = project_data['name']\n                    if 'description' in project_data:\n                        project.description = project_data['description']\n                    if 'client_id' in project_data:\n                        project.client_id = project_data['client_id']\n                    if 'start_date' in project_data:\n                        project.start_date = project_data['start_date']\n                    if 'end_date' in project_data:\n                        project.end_date = project_data['end_date']\n                    if 'status' in project_data:\n                        project.status = project_data['status']\n                    if 'budget' in project_data:\n                        project.budget = project_data['budget']\n\n                    # Aggiorna i membri del team se specificati\n                    if 'team_members' in project_data:\n                        # Rimuovi tutti i membri attuali\n                        project.team_members = []\n\n                        # Aggiungi i nuovi membri\n                        for user_id in project_data['team_members']:\n                            user = User.query.get(user_id)\n                            if user:\n                                project.team_members.append(user)\n\n                        # Assicurati che l'utente corrente sia nel team\n                        if current_user.id not in project_data['team_members']:\n                            project.team_members.append(current_user)\n\n                    results.append({\n                        'operation': 'update',\n                        'success': True,\n                        'project_id': project.id,\n                        'message': \"Progetto aggiornato con successo\"\n                    })\n                except Exception as e:\n                    results.append({\n                        'operation': 'update',\n                        'success': False,\n                        'project_id': operation_data.get('project_id'),\n                        'message': f\"Errore durante l'aggiornamento del progetto: {str(e)}\"\n                    })\n\n            # Operazione DELETE\n            elif operation == 'delete':\n                if 'project_id' not in operation_data:\n                    results.append({\n                        'operation': 'delete',\n                        'success': False,\n                        'message': \"ID progetto obbligatorio\"\n                    })\n                    continue\n\n                try:\n                    # Ottieni il progetto dal database\n                    project = Project.query.get(operation_data['project_id'])\n                    if not project:\n                        results.append({\n                            'operation': 'delete',\n                            'success': False,\n                            'project_id': operation_data['project_id'],\n                            'message': \"Progetto non trovato\"\n                        })\n                        continue\n\n                    # Verifica se ci sono task associati\n                    tasks = Task.query.filter_by(project_id=project.id).count()\n                    if tasks > 0:\n                        results.append({\n                            'operation': 'delete',\n                            'success': False,\n                            'project_id': project.id,\n                            'message': f\"Impossibile eliminare il progetto: ci sono {tasks} task associati\"\n                        })\n                        continue\n\n                    # Verifica se ci sono risorse associate\n                    resources = ProjectResource.query.filter_by(project_id=project.id).count()\n                    if resources > 0:\n                        results.append({\n                            'operation': 'delete',\n                            'success': False,\n                            'project_id': project.id,\n                            'message': f\"Impossibile eliminare il progetto: ci sono {resources} risorse associate\"\n                        })\n                        continue\n\n                    # Verifica se ci sono KPI associati\n                    kpis = ProjectKPI.query.filter_by(project_id=project.id).count()\n                    if kpis > 0:\n                        results.append({\n                            'operation': 'delete',\n                            'success': False,\n                            'project_id': project.id,\n                            'message': f\"Impossibile eliminare il progetto: ci sono {kpis} KPI associati\"\n                        })\n                        continue\n\n                    # Elimina il progetto\n                    db.session.delete(project)\n\n                    results.append({\n                        'operation': 'delete',\n                        'success': True,\n                        'project_id': operation_data['project_id'],\n                        'message': \"Progetto eliminato con successo\"\n                    })\n                except Exception as e:\n                    results.append({\n                        'operation': 'delete',\n                        'success': False,\n                        'project_id': operation_data.get('project_id'),\n                        'message': f\"Errore durante l'eliminazione del progetto: {str(e)}\"\n                    })\n\n            # Operazione non supportata\n            else:\n                results.append({\n                    'operation': operation,\n                    'success': False,\n                    'message': f\"Operazione '{operation}' non supportata\"\n                })\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        return api_response(\n            data={'results': results},\n            message=\"Operazioni batch eseguite con successo\"\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in batch_projects: {str(e)}\")\n        return handle_api_error(e)\n\n", "modifiedCode": "\"\"\"\nAPI RESTful per la gestione dei progetti.\n\"\"\"\nfrom flask import Blueprint, request, jsonify, current_app\nfrom flask_login import login_required, current_user\nfrom sqlalchemy import desc\nfrom models import Project, Task, ProjectResource, ProjectKPI, User\nfrom utils.api_utils import (\n    api_response, handle_api_error, get_pagination_params,\n    format_pagination, api_permission_required\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_EDIT_PROJECT,\n    PERMISSION_DELETE_PROJECT\n)\nfrom extensions import db\n\n# Crea il blueprint per le API dei progetti\napi_projects = Blueprint('api_projects', __name__)\n\ndef calculate_project_kpis(project):\n    \"\"\"\n    Calcola i KPI aggregati per un progetto.\n    \"\"\"\n    try:\n        # Calcola ore totali dai timesheet\n        total_hours = 0\n        for entry in project.timesheet_entries:\n            total_hours += entry.hours if entry.hours else 0\n\n        # Calcola ore stimate dai task\n        estimated_hours = 0\n        for task in project.tasks:\n            estimated_hours += task.estimated_hours if task.estimated_hours else 0\n\n        # Calcola utilizzo budget\n        budget_usage = 0\n        if project.budget and project.budget > 0:\n            budget_usage = round((project.expenses / project.budget) * 100, 1) if project.expenses else 0\n\n        # Calcola utilizzo tempo\n        time_usage = 0\n        if estimated_hours > 0:\n            time_usage = round((total_hours / estimated_hours) * 100, 1)\n\n        # Calcola margine\n        margin = 0\n        if project.budget and project.budget > 0:\n            # Calcola costi del personale (ore * tariffa media)\n            personnel_cost = total_hours * 50  # Assumiamo 50€/ora come media\n            total_costs = personnel_cost + (project.expenses or 0)\n            margin = round(((project.budget - total_costs) / project.budget) * 100, 1)\n\n        return {\n            'budget_usage': budget_usage,\n            'time_usage': time_usage,\n            'margin': margin,\n            'total_hours': total_hours,\n            'estimated_hours': estimated_hours\n        }\n    except Exception as e:\n        current_app.logger.error(f\"Error calculating KPIs for project {project.id}: {str(e)}\")\n        return {\n            'budget_usage': 0,\n            'time_usage': 0,\n            'margin': 0,\n            'total_hours': 0,\n            'estimated_hours': 0\n        }\n\n@api_projects.route('/', methods=['GET'])\n@login_required\ndef get_projects():\n    \"\"\"\n    Ottiene la lista dei progetti con supporto per filtri e paginazione.\n    ---\n    tags:\n      - projects\n    parameters:\n      - $ref: '#/components/parameters/pageParam'\n      - $ref: '#/components/parameters/perPageParam'\n      - name: status\n        in: query\n        description: Filtra per stato del progetto\n        schema:\n          type: string\n          enum: [planning, active, completed, on-hold]\n      - name: client_id\n        in: query\n        description: Filtra per ID cliente\n        schema:\n          type: integer\n      - name: search\n        in: query\n        description: Cerca nei nomi e nelle descrizioni dei progetti\n        schema:\n          type: string\n    responses:\n      200:\n        description: Lista di progetti\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    projects:\n                      type: array\n                      items:\n                        $ref: '#/components/schemas/Project'\n                pagination:\n                  $ref: '#/components/schemas/Pagination'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n    \"\"\"\n    try:\n        # Ottieni parametri di paginazione\n        page, per_page = get_pagination_params()\n\n        # Inizia la query\n        query = Project.query\n\n        # Applica filtri\n        status = request.args.get('status')\n        if status:\n            query = query.filter(Project.status == status)\n\n        client_id = request.args.get('client_id', type=int)\n        if client_id:\n            query = query.filter(Project.client_id == client_id)\n\n        search = request.args.get('search')\n        if search:\n            search_term = f\"%{search}%\"\n            query = query.filter(\n                (Project.name.ilike(search_term)) |\n                (Project.description.ilike(search_term))\n            )\n\n        # Applica ordinamento\n        query = query.order_by(desc(Project.updated_at))\n\n        # Esegui query con paginazione\n        pagination = query.paginate(page=page, per_page=per_page)\n\n        # Prepara i dati dei progetti\n        projects_data = []\n        for project in pagination.items:\n            # Serializza i dati del cliente se presente\n            client_data = None\n            if project.client:\n                client_data = {\n                    'id': project.client.id,\n                    'name': project.client.name,\n                    'industry': project.client.industry\n                }\n\n            # Serializza i dati del contratto se presente\n            contract_data = None\n            if project.contract:\n                contract_data = {\n                    'id': project.contract.id,\n                    'contract_number': project.contract.contract_number,\n                    'title': project.contract.title,\n                    'contract_type': project.contract.contract_type,\n                    'hourly_rate': float(project.contract.hourly_rate) if project.contract.hourly_rate else None,\n                    'status': project.contract.status\n                }\n\n            # Calcola KPI aggregati per il progetto\n            kpis_data = calculate_project_kpis(project)\n\n            projects_data.append({\n                'id': project.id,\n                'name': project.name,\n                'description': project.description,\n                'client_id': project.client_id,\n                'client': client_data,\n                'contract_id': project.contract_id,\n                'contract': contract_data,\n                'project_type': project.project_type,\n                'is_billable': project.is_billable,\n                'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,\n                'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,\n                'start_date': project.start_date.isoformat() if project.start_date else None,\n                'end_date': project.end_date.isoformat() if project.end_date else None,\n                'status': project.status,\n                'budget': float(project.budget) if project.budget else None,\n                'expenses': float(project.expenses) if project.expenses else None,\n                'created_at': project.created_at.isoformat(),\n                'updated_at': project.updated_at.isoformat(),\n                'kpis': kpis_data\n            })\n\n        # Restituisci risposta\n        return api_response(\n            data={'projects': projects_data},\n            pagination=format_pagination(pagination)\n        )\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_projects: {str(e)}\")\n        return handle_api_error(e)\n\n@api_projects.route('/<int:project_id>', methods=['GET'])\n@login_required\ndef get_project(project_id):\n    \"\"\"\n    Ottiene i dettagli di un progetto specifico.\n    ---\n    tags:\n      - projects\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Dettagli del progetto\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    project:\n                      $ref: '#/components/schemas/Project'\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n    \"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Serializza i dati del cliente se presente\n        client_data = None\n        if project.client:\n            client_data = {\n                'id': project.client.id,\n                'name': project.client.name,\n                'industry': project.client.industry\n            }\n\n        # Calcola dati aggregati\n        tasks = project.tasks.all()\n        task_count = len(tasks)\n        completed_tasks = len([t for t in tasks if t.status == 'completed'])\n\n        # Serializza task\n        tasks_data = []\n        for task in tasks:\n            assignee_data = None\n            if task.assignee_id:\n                assignee = next((m for m in project.team_members if m.id == task.assignee_id), None)\n                if assignee:\n                    assignee_data = {\n                        'id': assignee.id,\n                        'full_name': assignee.full_name\n                    }\n\n            tasks_data.append({\n                'id': task.id,\n                'name': task.name,\n                'description': task.description,\n                'status': task.status,\n                'priority': task.priority,\n                'assignee_id': task.assignee_id,\n                'assignee': assignee_data,\n                'start_date': task.start_date.isoformat() if task.start_date else None,\n                'due_date': task.due_date.isoformat() if task.due_date else None,\n                'estimated_hours': task.estimated_hours,\n                'actual_hours': task.actual_hours,\n                'created_at': task.created_at.isoformat() if task.created_at else None\n            })\n\n        # Calcola ore totali dai timesheet\n        total_hours = 0\n        for entry in project.timesheet_entries:\n            total_hours += entry.hours if entry.hours else 0\n\n        # Serializza team members\n        team_members_data = []\n        for member in project.team_members:\n            # Calcola ore lavorate per questo membro\n            member_hours = 0\n            for entry in project.timesheet_entries:\n                if entry.user_id == member.id:\n                    member_hours += entry.hours if entry.hours else 0\n\n            team_members_data.append({\n                'id': member.id,\n                'full_name': member.full_name,\n                'email': member.email,\n                'profile_image': member.profile_image,\n                'role': 'Team Member',  # TODO: Get actual role from project_team table\n                'hours_worked': member_hours\n            })\n\n        # Prepara i dati del progetto\n        project_data = {\n            'id': project.id,\n            'name': project.name,\n            'description': project.description,\n            'client_id': project.client_id,\n            'client': client_data,\n            'project_type': project.project_type,\n            'is_billable': project.is_billable,\n            'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,\n            'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,\n            'start_date': project.start_date.isoformat() if project.start_date else None,\n            'end_date': project.end_date.isoformat() if project.end_date else None,\n            'status': project.status,\n            'budget': float(project.budget) if project.budget else None,\n            'expenses': float(project.expenses) if project.expenses else None,\n            'created_at': project.created_at.isoformat(),\n            'updated_at': project.updated_at.isoformat(),\n            # Dati aggregati per l'overview\n            'task_count': task_count,\n            'completed_tasks': completed_tasks,\n            'team_count': len(project.team_members),\n            'team_members': team_members_data,\n            'tasks': tasks_data,\n            'total_hours': total_hours,\n            'estimated_hours': sum(t.estimated_hours or 0 for t in tasks),\n            'remaining_budget': float(project.budget - project.expenses) if project.budget and project.expenses else None\n        }\n\n        return api_response(data={'project': project_data})\n    except Exception as e:\n        current_app.logger.error(f\"Error in get_project: {str(e)}\")\n        return handle_api_error(e)\n\n@api_projects.route('/<int:project_id>/team', methods=['POST'])\n@login_required\ndef add_team_member(project_id):\n    \"\"\"Aggiunge un membro al team del progetto.\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n        data = request.get_json()\n\n        user_id = data.get('user_id')\n        role = data.get('role', 'Team Member')\n\n        if not user_id:\n            return api_response(False, 'user_id richiesto', status_code=400)\n\n        user = User.query.get_or_404(user_id)\n\n        # Verifica se l'utente è già nel team\n        if user in project.team_members:\n            return api_response(False, 'Utente già nel team', status_code=400)\n\n        # Aggiungi al team\n        project.team_members.append(user)\n        db.session.commit()\n\n        return api_response(data={}, message='Membro aggiunto al team con successo')\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_projects.route('/<int:project_id>/team/<int:user_id>', methods=['DELETE'])\n@login_required\ndef remove_team_member(project_id, user_id):\n    \"\"\"Rimuove un membro dal team del progetto.\"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n        user = User.query.get_or_404(user_id)\n\n        # Non rimuovere il project manager\n        if user_id == project.manager_id:\n            return api_response(False, 'Non è possibile rimuovere il project manager', status_code=400)\n\n        # Verifica se l'utente è nel team\n        if user not in project.team_members:\n            return api_response(False, 'Utente non nel team', status_code=400)\n\n        # Rimuovi dal team\n        project.team_members.remove(user)\n        db.session.commit()\n\n        return api_response(data={}, message='Membro rimosso dal team con successo')\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_projects.route('/', methods=['POST'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef create_project():\n    \"\"\"\n    Crea un nuovo progetto.\n    ---\n    tags:\n      - projects\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - name\n            properties:\n              name:\n                type: string\n                description: Nome del progetto\n              description:\n                type: string\n                description: Descrizione del progetto\n              client_id:\n                type: integer\n                description: ID del cliente associato\n              client:\n                type: string\n                description: Nome del cliente (alternativo a client_id)\n              project_type:\n                type: string\n                enum: [service, license, consulting, product, rd, internal]\n                default: service\n                description: Tipologia del progetto\n              is_billable:\n                type: boolean\n                default: true\n                description: Se il progetto è fatturabile\n              client_daily_rate:\n                type: number\n                description: Tariffa giornaliera del cliente\n              markup_percentage:\n                type: number\n                description: Percentuale di markup applicata\n              start_date:\n                type: string\n                format: date\n                description: Data di inizio (YYYY-MM-DD)\n              end_date:\n                type: string\n                format: date\n                description: Data di fine (YYYY-MM-DD)\n              status:\n                type: string\n                enum: [planning, active, completed, on-hold, cancelled]\n                default: planning\n                description: Stato del progetto\n              budget:\n                type: number\n                description: Budget del progetto\n              team_members:\n                type: array\n                items:\n                  type: integer\n                description: Lista di ID utenti da assegnare al progetto\n    responses:\n      201:\n        description: Progetto creato con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    project:\n                      $ref: '#/components/schemas/Project'\n                    message:\n                      type: string\n                      example: Progetto creato con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: Nome del progetto obbligatorio\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Ottieni i dati dalla richiesta\n        data = request.json\n\n        # Validazione dei dati\n        if not data or not data.get('name'):\n            return api_response(\n                message=\"Nome del progetto obbligatorio\",\n                status_code=400\n            )\n\n        # Crea il nuovo progetto\n        new_project = Project(\n            name=data.get('name'),\n            description=data.get('description', ''),\n            client_id=data.get('client_id'),\n            project_type=data.get('project_type', 'service'),\n            is_billable=data.get('is_billable', True),\n            client_daily_rate=data.get('client_daily_rate'),\n            markup_percentage=data.get('markup_percentage'),\n            start_date=data.get('start_date'),\n            end_date=data.get('end_date'),\n            status=data.get('status', 'planning'),\n            budget=data.get('budget')\n        )\n\n        # Aggiungi il progetto al database\n        db.session.add(new_project)\n        db.session.flush()  # Per ottenere l'ID del progetto\n\n        # Aggiungi i membri del team se specificati\n        team_members = data.get('team_members', [])\n        if team_members:\n            for user_id in team_members:\n                user = User.query.get(user_id)\n                if user:\n                    new_project.team_members.append(user)\n\n        # Aggiungi l'utente corrente al team se non è già incluso\n        if current_user.id not in team_members:\n            new_project.team_members.append(current_user)\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        # Serializza i dati del cliente se presente\n        client_data = None\n        if new_project.client:\n            client_data = {\n                'id': new_project.client.id,\n                'name': new_project.client.name,\n                'industry': new_project.client.industry\n            }\n\n        # Prepara i dati del progetto per la risposta\n        project_data = {\n            'id': new_project.id,\n            'name': new_project.name,\n            'description': new_project.description,\n            'client_id': new_project.client_id,\n            'client': client_data,\n            'project_type': new_project.project_type,\n            'is_billable': new_project.is_billable,\n            'client_daily_rate': float(new_project.client_daily_rate) if new_project.client_daily_rate else None,\n            'markup_percentage': float(new_project.markup_percentage) if new_project.markup_percentage else None,\n            'start_date': new_project.start_date.isoformat() if new_project.start_date else None,\n            'end_date': new_project.end_date.isoformat() if new_project.end_date else None,\n            'status': new_project.status,\n            'budget': float(new_project.budget) if new_project.budget else None,\n            'expenses': float(new_project.expenses) if new_project.expenses else None,\n            'created_at': new_project.created_at.isoformat(),\n            'updated_at': new_project.updated_at.isoformat()\n        }\n\n        return api_response(\n            data={'project': project_data},\n            message=\"Progetto creato con successo\",\n            status_code=201\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in create_project: {str(e)}\")\n        return handle_api_error(e)\n\n@api_projects.route('/<int:project_id>', methods=['PUT'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef update_project(project_id):\n    \"\"\"\n    Aggiorna un progetto esistente.\n    ---\n    tags:\n      - projects\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto da aggiornare\n        schema:\n          type: integer\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            properties:\n              name:\n                type: string\n                description: Nome del progetto\n              description:\n                type: string\n                description: Descrizione del progetto\n              client_id:\n                type: integer\n                description: ID del cliente associato\n              start_date:\n                type: string\n                format: date\n                description: Data di inizio (YYYY-MM-DD)\n              end_date:\n                type: string\n                format: date\n                description: Data di fine (YYYY-MM-DD)\n              status:\n                type: string\n                enum: [planning, active, completed, on-hold]\n                description: Stato del progetto\n              budget:\n                type: number\n                description: Budget del progetto\n              team_members:\n                type: array\n                items:\n                  type: integer\n                description: Lista di ID utenti da assegnare al progetto\n    responses:\n      200:\n        description: Progetto aggiornato con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    project:\n                      $ref: '#/components/schemas/Project'\n                    message:\n                      type: string\n                      example: Progetto aggiornato con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: Dati non validi\n      404:\n        $ref: '#/components/responses/NotFound'\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Ottieni il progetto dal database\n        project = Project.query.get_or_404(project_id)\n\n        # Ottieni i dati dalla richiesta\n        data = request.json\n        if not data:\n            return api_response(\n                message=\"Nessun dato fornito per l'aggiornamento\",\n                status_code=400\n            )\n\n        # Aggiorna i campi del progetto\n        if 'name' in data:\n            project.name = data['name']\n        if 'description' in data:\n            project.description = data['description']\n        if 'client_id' in data:\n            project.client_id = data['client_id']\n        if 'project_type' in data:\n            project.project_type = data['project_type']\n        if 'is_billable' in data:\n            project.is_billable = data['is_billable']\n        if 'client_daily_rate' in data:\n            project.client_daily_rate = data['client_daily_rate']\n        if 'markup_percentage' in data:\n            project.markup_percentage = data['markup_percentage']\n        if 'start_date' in data:\n            project.start_date = data['start_date']\n        if 'end_date' in data:\n            project.end_date = data['end_date']\n        if 'status' in data:\n            project.status = data['status']\n        if 'budget' in data:\n            project.budget = data['budget']\n\n        # Aggiorna i membri del team se specificati\n        if 'team_members' in data:\n            # Rimuovi tutti i membri attuali\n            project.team_members = []\n\n            # Aggiungi i nuovi membri\n            for user_id in data['team_members']:\n                user = User.query.get(user_id)\n                if user:\n                    project.team_members.append(user)\n\n            # Assicurati che l'utente corrente sia nel team\n            if current_user.id not in data['team_members']:\n                project.team_members.append(current_user)\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        # Serializza i dati del cliente se presente\n        client_data = None\n        if project.client:\n            client_data = {\n                'id': project.client.id,\n                'name': project.client.name,\n                'industry': project.client.industry\n            }\n\n        # Prepara i dati del progetto per la risposta\n        project_data = {\n            'id': project.id,\n            'name': project.name,\n            'description': project.description,\n            'client_id': project.client_id,\n            'client': client_data,\n            'project_type': project.project_type,\n            'is_billable': project.is_billable,\n            'client_daily_rate': float(project.client_daily_rate) if project.client_daily_rate else None,\n            'markup_percentage': float(project.markup_percentage) if project.markup_percentage else None,\n            'start_date': project.start_date.isoformat() if project.start_date else None,\n            'end_date': project.end_date.isoformat() if project.end_date else None,\n            'status': project.status,\n            'budget': float(project.budget) if project.budget else None,\n            'expenses': float(project.expenses) if project.expenses else None,\n            'created_at': project.created_at.isoformat(),\n            'updated_at': project.updated_at.isoformat()\n        }\n\n        return api_response(\n            data={'project': project_data},\n            message=\"Progetto aggiornato con successo\"\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in update_project: {str(e)}\")\n        return handle_api_error(e)\n\n@api_projects.route('/<int:project_id>', methods=['DELETE'])\n@login_required\n@api_permission_required(PERMISSION_DELETE_PROJECT)\ndef delete_project(project_id):\n    \"\"\"\n    Elimina un progetto esistente.\n    ---\n    tags:\n      - projects\n    parameters:\n      - name: project_id\n        in: path\n        required: true\n        description: ID del progetto da eliminare\n        schema:\n          type: integer\n    responses:\n      200:\n        description: Progetto eliminato con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                message:\n                  type: string\n                  example: \"Progetto eliminato con successo\"\n      403:\n        description: Permessi insufficienti\n      404:\n        description: Progetto non trovato\n      500:\n        description: Errore interno del server\n    \"\"\"\n    try:\n        project = Project.query.get_or_404(project_id)\n\n        # Verifica permessi aggiuntivi se necessario\n        # (il decoratore già controlla PERMISSION_DELETE_PROJECT)\n\n        # Elimina il progetto (cascade eliminerà automaticamente task, timesheet, etc.)\n        db.session.delete(project)\n        db.session.commit()\n\n        current_app.logger.info(f\"Progetto {project_id} eliminato da utente {current_user.id}\")\n\n        return api_response(\n            message=f\"Progetto '{project.name}' eliminato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Errore nell'eliminazione progetto {project_id}: {str(e)}\")\n        return handle_api_error(e)\n\n\n@api_projects.route('/batch', methods=['POST'])\n@login_required\n@api_permission_required(PERMISSION_EDIT_PROJECT)\ndef batch_projects():\n    \"\"\"\n    Esegue operazioni batch sui progetti.\n    ---\n    tags:\n      - projects\n    requestBody:\n      required: true\n      content:\n        application/json:\n          schema:\n            type: object\n            required:\n              - operations\n            properties:\n              operations:\n                type: array\n                items:\n                  type: object\n                  required:\n                    - operation\n                  properties:\n                    operation:\n                      type: string\n                      enum: [create, update, delete]\n                      description: Tipo di operazione da eseguire\n                    project_id:\n                      type: integer\n                      description: ID del progetto (richiesto per update e delete)\n                    data:\n                      type: object\n                      description: Dati del progetto (richiesto per create e update)\n    responses:\n      200:\n        description: Operazioni batch eseguite con successo\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: true\n                data:\n                  type: object\n                  properties:\n                    results:\n                      type: array\n                      items:\n                        type: object\n                        properties:\n                          operation:\n                            type: string\n                            example: create\n                          success:\n                            type: boolean\n                            example: true\n                          project_id:\n                            type: integer\n                            example: 1\n                          message:\n                            type: string\n                            example: Progetto creato con successo\n                    message:\n                      type: string\n                      example: Operazioni batch eseguite con successo\n      400:\n        description: Dati non validi\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                success:\n                  type: boolean\n                  example: false\n                message:\n                  type: string\n                  example: Dati non validi\n      401:\n        $ref: '#/components/responses/Unauthorized'\n      403:\n        $ref: '#/components/responses/Forbidden'\n    \"\"\"\n    try:\n        # Ottieni i dati dalla richiesta\n        data = request.json\n\n        # Validazione dei dati\n        if not data or 'operations' not in data or not isinstance(data['operations'], list):\n            return api_response(\n                message=\"Operazioni batch non valide\",\n                status_code=400\n            )\n\n        # Risultati delle operazioni\n        results = []\n\n        # Esegui le operazioni\n        for operation_data in data['operations']:\n            # Validazione dell'operazione\n            if 'operation' not in operation_data:\n                results.append({\n                    'operation': 'unknown',\n                    'success': False,\n                    'message': \"Tipo di operazione non specificato\"\n                })\n                continue\n\n            operation = operation_data['operation']\n\n            # Operazione CREATE\n            if operation == 'create':\n                if 'data' not in operation_data or not operation_data['data'].get('name'):\n                    results.append({\n                        'operation': 'create',\n                        'success': False,\n                        'message': \"Nome del progetto obbligatorio\"\n                    })\n                    continue\n\n                try:\n                    # Crea il nuovo progetto\n                    project_data = operation_data['data']\n                    new_project = Project(\n                        name=project_data.get('name'),\n                        description=project_data.get('description', ''),\n                        client_id=project_data.get('client_id'),\n                        start_date=project_data.get('start_date'),\n                        end_date=project_data.get('end_date'),\n                        status=project_data.get('status', 'planning'),\n                        budget=project_data.get('budget')\n                    )\n\n                    # Aggiungi il progetto al database\n                    db.session.add(new_project)\n                    db.session.flush()  # Per ottenere l'ID del progetto\n\n                    # Aggiungi i membri del team se specificati\n                    team_members = project_data.get('team_members', [])\n                    if team_members:\n                        for user_id in team_members:\n                            user = User.query.get(user_id)\n                            if user:\n                                new_project.team_members.append(user)\n\n                    # Aggiungi l'utente corrente al team se non è già incluso\n                    if current_user.id not in team_members:\n                        new_project.team_members.append(current_user)\n\n                    results.append({\n                        'operation': 'create',\n                        'success': True,\n                        'project_id': new_project.id,\n                        'message': \"Progetto creato con successo\"\n                    })\n                except Exception as e:\n                    results.append({\n                        'operation': 'create',\n                        'success': False,\n                        'message': f\"Errore durante la creazione del progetto: {str(e)}\"\n                    })\n\n            # Operazione UPDATE\n            elif operation == 'update':\n                if 'project_id' not in operation_data or 'data' not in operation_data:\n                    results.append({\n                        'operation': 'update',\n                        'success': False,\n                        'message': \"ID progetto e dati obbligatori\"\n                    })\n                    continue\n\n                try:\n                    # Ottieni il progetto dal database\n                    project = Project.query.get(operation_data['project_id'])\n                    if not project:\n                        results.append({\n                            'operation': 'update',\n                            'success': False,\n                            'project_id': operation_data['project_id'],\n                            'message': \"Progetto non trovato\"\n                        })\n                        continue\n\n                    # Aggiorna i campi del progetto\n                    project_data = operation_data['data']\n                    if 'name' in project_data:\n                        project.name = project_data['name']\n                    if 'description' in project_data:\n                        project.description = project_data['description']\n                    if 'client_id' in project_data:\n                        project.client_id = project_data['client_id']\n                    if 'start_date' in project_data:\n                        project.start_date = project_data['start_date']\n                    if 'end_date' in project_data:\n                        project.end_date = project_data['end_date']\n                    if 'status' in project_data:\n                        project.status = project_data['status']\n                    if 'budget' in project_data:\n                        project.budget = project_data['budget']\n\n                    # Aggiorna i membri del team se specificati\n                    if 'team_members' in project_data:\n                        # Rimuovi tutti i membri attuali\n                        project.team_members = []\n\n                        # Aggiungi i nuovi membri\n                        for user_id in project_data['team_members']:\n                            user = User.query.get(user_id)\n                            if user:\n                                project.team_members.append(user)\n\n                        # Assicurati che l'utente corrente sia nel team\n                        if current_user.id not in project_data['team_members']:\n                            project.team_members.append(current_user)\n\n                    results.append({\n                        'operation': 'update',\n                        'success': True,\n                        'project_id': project.id,\n                        'message': \"Progetto aggiornato con successo\"\n                    })\n                except Exception as e:\n                    results.append({\n                        'operation': 'update',\n                        'success': False,\n                        'project_id': operation_data.get('project_id'),\n                        'message': f\"Errore durante l'aggiornamento del progetto: {str(e)}\"\n                    })\n\n            # Operazione DELETE\n            elif operation == 'delete':\n                if 'project_id' not in operation_data:\n                    results.append({\n                        'operation': 'delete',\n                        'success': False,\n                        'message': \"ID progetto obbligatorio\"\n                    })\n                    continue\n\n                try:\n                    # Ottieni il progetto dal database\n                    project = Project.query.get(operation_data['project_id'])\n                    if not project:\n                        results.append({\n                            'operation': 'delete',\n                            'success': False,\n                            'project_id': operation_data['project_id'],\n                            'message': \"Progetto non trovato\"\n                        })\n                        continue\n\n                    # Verifica se ci sono task associati\n                    tasks = Task.query.filter_by(project_id=project.id).count()\n                    if tasks > 0:\n                        results.append({\n                            'operation': 'delete',\n                            'success': False,\n                            'project_id': project.id,\n                            'message': f\"Impossibile eliminare il progetto: ci sono {tasks} task associati\"\n                        })\n                        continue\n\n                    # Verifica se ci sono risorse associate\n                    resources = ProjectResource.query.filter_by(project_id=project.id).count()\n                    if resources > 0:\n                        results.append({\n                            'operation': 'delete',\n                            'success': False,\n                            'project_id': project.id,\n                            'message': f\"Impossibile eliminare il progetto: ci sono {resources} risorse associate\"\n                        })\n                        continue\n\n                    # Verifica se ci sono KPI associati\n                    kpis = ProjectKPI.query.filter_by(project_id=project.id).count()\n                    if kpis > 0:\n                        results.append({\n                            'operation': 'delete',\n                            'success': False,\n                            'project_id': project.id,\n                            'message': f\"Impossibile eliminare il progetto: ci sono {kpis} KPI associati\"\n                        })\n                        continue\n\n                    # Elimina il progetto\n                    db.session.delete(project)\n\n                    results.append({\n                        'operation': 'delete',\n                        'success': True,\n                        'project_id': operation_data['project_id'],\n                        'message': \"Progetto eliminato con successo\"\n                    })\n                except Exception as e:\n                    results.append({\n                        'operation': 'delete',\n                        'success': False,\n                        'project_id': operation_data.get('project_id'),\n                        'message': f\"Errore durante l'eliminazione del progetto: {str(e)}\"\n                    })\n\n            # Operazione non supportata\n            else:\n                results.append({\n                    'operation': operation,\n                    'success': False,\n                    'message': f\"Operazione '{operation}' non supportata\"\n                })\n\n        # Commit delle modifiche\n        db.session.commit()\n\n        return api_response(\n            data={'results': results},\n            message=\"Operazioni batch eseguite con successo\"\n        )\n    except Exception as e:\n        db.session.rollback()\n        current_app.logger.error(f\"Error in batch_projects: {str(e)}\")\n        return handle_api_error(e)\n\n"}