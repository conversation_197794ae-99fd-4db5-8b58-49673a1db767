{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/invoices.py"}, "originalCode": "\"\"\"\nAPI Blueprint per la gestione delle fatture e fatturazione automatica.\nTask 3.1 + 4 - CRM/Billing Integration\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, or_, extract, func\nfrom datetime import datetime, date, timedelta\nfrom calendar import monthrange\n\nfrom models import Invoice, InvoiceLine, Contract, Client, Project, TimesheetEntry, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_invoices = Blueprint('api_invoices', __name__)\n\n\n@api_invoices.route('/', methods=['GET'])\n@login_required\ndef get_invoices():\n    \"\"\"Recupera lista fatture con filtri\"\"\"\n    try:\n        # Parametri filtro\n        client_id = request.args.get('client_id', type=int)\n        status = request.args.get('status')  # draft, sent, paid, overdue\n        start_date = request.args.get('start_date')  # YYYY-MM-DD\n        end_date = request.args.get('end_date')  # YYYY-MM-DD\n        year = request.args.get('year', type=int)\n        month = request.args.get('month', type=int)\n        search = request.args.get('search')  # Ricerca in numero fattura\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = Invoice.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_invoices'):\n            return api_response(False, 'Non hai i permessi per visualizzare le fatture', status_code=403)\n        \n        # Applica filtri\n        if client_id:\n            query = query.filter(Invoice.client_id == client_id)\n            \n        if status:\n            query = query.filter(Invoice.status == status)\n            \n        if start_date:\n            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()\n            query = query.filter(Invoice.issue_date >= start_date_obj)\n            \n        if end_date:\n            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()\n            query = query.filter(Invoice.issue_date <= end_date_obj)\n            \n        if year:\n            query = query.filter(extract('year', Invoice.issue_date) == year)\n            \n        if month:\n            query = query.filter(extract('month', Invoice.issue_date) == month)\n            \n        if search:\n            search_pattern = f\"%{search}%\"\n            query = query.filter(Invoice.invoice_number.ilike(search_pattern))\n        \n        # Ordina per data emissione (più recenti prima)\n        query = query.order_by(Invoice.issue_date.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        invoices_data = []\n        for invoice in paginated.items:\n            invoices_data.append({\n                'id': invoice.id,\n                'client_id': invoice.client_id,\n                'client': {\n                    'id': invoice.client.id,\n                    'name': invoice.client.name,\n                    'company': invoice.client.company\n                } if invoice.client else None,\n                'invoice_number': invoice.invoice_number,\n                'status': invoice.status,\n                'billing_period_start': invoice.billing_period_start.isoformat() if invoice.billing_period_start else None,\n                'billing_period_end': invoice.billing_period_end.isoformat() if invoice.billing_period_end else None,\n                'issue_date': invoice.issue_date.isoformat() if invoice.issue_date else None,\n                'due_date': invoice.due_date.isoformat() if invoice.due_date else None,\n                'subtotal': invoice.subtotal,\n                'tax_amount': invoice.tax_amount,\n                'total_amount': invoice.total_amount,\n                'lines_count': len(invoice.lines) if invoice.lines else 0,\n                'created_at': invoice.created_at.isoformat(),\n                'updated_at': invoice.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'invoices': invoices_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperate {len(invoices_data)} fatture\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_invoices.route('/generate', methods=['POST'])\*************\n@login_required\ndef generate_invoice():\n    \"\"\"Genera fattura automatica da timesheet entries per periodo\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_invoices'):\n            return api_response(False, 'Non hai i permessi per generare fatture', status_code=403)\n        \n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['client_id', 'billing_period_start', 'billing_period_end']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        # Verifica che il cliente esista\n        client = Client.query.get(data['client_id'])\n        if not client:\n            return api_response(\n                False,\n                'Cliente non trovato',\n                status_code=404\n            )\n        \n        # Parsing date periodo fatturazione\n        try:\n            billing_start = datetime.strptime(data['billing_period_start'], '%Y-%m-%d').date()\n            billing_end = datetime.strptime(data['billing_period_end'], '%Y-%m-%d').date()\n        except ValueError:\n            return api_response(\n                False,\n                'Formato date non valido. Utilizzare YYYY-MM-DD',\n                status_code=400\n            )\n        \n        # Validazione logica date\n        if billing_start > billing_end:\n            return api_response(\n                False,\n                'La data di inizio periodo non può essere successiva alla data di fine',\n                status_code=400\n            )\n        \n        # Trova timesheet entries fatturabili per il cliente nel periodo\n        timesheet_entries = TimesheetEntry.query.join(Project).join(Contract).filter(\n            and_(\n                Contract.client_id == data['client_id'],\n                TimesheetEntry.date >= billing_start,\n                TimesheetEntry.date <= billing_end,\n                TimesheetEntry.billable == True,\n                TimesheetEntry.billing_status == 'unbilled'\n            )\n        ).all()\n        \n        if not timesheet_entries:\n            return api_response(\n                False,\n                f'Nessuna timesheet entry fatturabile trovata per il cliente nel periodo {billing_start} - {billing_end}',\n                status_code=400\n            )\n        \n        # Genera numero fattura\n        year = datetime.now().year\n        last_invoice = Invoice.query.filter(\n            Invoice.invoice_number.like(f'INV-{year}-%')\n        ).order_by(Invoice.invoice_number.desc()).first()\n        \n        if last_invoice:\n            try:\n                last_num = int(last_invoice.invoice_number.split('-')[2])\n                next_num = last_num + 1\n            except:\n                next_num = 1\n        else:\n            next_num = 1\n        \n        invoice_number = f\"INV-{year}-{next_num:04d}\"\n        \n        # Crea fattura\n        invoice = Invoice(\n            client_id=data['client_id'],\n            invoice_number=invoice_number,\n            status='draft',\n            billing_period_start=billing_start,\n            billing_period_end=billing_end,\n            issue_date=date.today(),\n            due_date=date.today() + timedelta(days=30),  # Default 30 giorni\n            subtotal=0,\n            tax_rate=data.get('tax_rate', 22.0),  # Default IVA 22%\n            tax_amount=0,\n            total_amount=0\n        )\n        \n        db.session.add(invoice)\n        db.session.flush()  # Per ottenere l'ID\n        \n        # Raggruppa entries per progetto/contratto\n        project_groups = {}\n        for entry in timesheet_entries:\n            project_id = entry.project_id\n            if project_id not in project_groups:\n                project_groups[project_id] = {\n                    'project': entry.project,\n                    'contract': entry.project.contract,\n                    'entries': [],\n                    'total_hours': 0,\n                    'total_amount': 0\n                }\n            \n            project_groups[project_id]['entries'].append(entry)\n            project_groups[project_id]['total_hours'] += entry.hours\n            \n            # Calcola importo: usa billing_rate dell'entry o hourly_rate del contratto\n            rate = entry.billing_rate or (entry.project.contract.hourly_rate if entry.project.contract else 0)\n            amount = entry.hours * rate\n            project_groups[project_id]['total_amount'] += amount\n        \n        # Crea righe fattura\n        total_subtotal = 0\n        for project_id, group in project_groups.items():\n            invoice_line = InvoiceLine(\n                invoice_id=invoice.id,\n                project_id=project_id,\n                contract_id=group['contract'].id if group['contract'] else None,\n                description=f\"Servizi professionali - {group['project'].name}\",\n                total_hours=group['total_hours'],\n                hourly_rate=group['contract'].hourly_rate if group['contract'] else 0,\n                total_amount=group['total_amount']\n            )\n            \n            db.session.add(invoice_line)\n            total_subtotal += group['total_amount']\n            \n            # Marca entries come fatturate\n            for entry in group['entries']:\n                entry.billing_status = 'billed'\n                entry.invoice_line_id = invoice_line.id\n        \n        # Aggiorna totali fattura\n        invoice.subtotal = total_subtotal\n        invoice.tax_amount = total_subtotal * (invoice.tax_rate / 100)\n        invoice.total_amount = invoice.subtotal + invoice.tax_amount\n        \n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': invoice.id,\n                'invoice_number': invoice.invoice_number,\n                'client': {\n                    'id': invoice.client.id,\n                    'name': invoice.client.name,\n                    'company': invoice.client.company\n                },\n                'billing_period_start': invoice.billing_period_start.isoformat(),\n                'billing_period_end': invoice.billing_period_end.isoformat(),\n                'subtotal': invoice.subtotal,\n                'tax_amount': invoice.tax_amount,\n                'total_amount': invoice.total_amount,\n                'lines_count': len(invoice.lines),\n                'timesheet_entries_count': len(timesheet_entries)\n            },\n            message=f'Fattura {invoice_number} generata con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n", "modifiedCode": "\"\"\"\nAPI Blueprint per la gestione delle fatture e fatturazione automatica.\nTask 3.1 + 4 - CRM/Billing Integration\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, or_, extract, func\nfrom datetime import datetime, date, timedelta\nfrom calendar import monthrange\n\nfrom models import Invoice, InvoiceLine, Contract, Client, Project, TimesheetEntry, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_invoices = Blueprint('api_invoices', __name__)\n\n\n@api_invoices.route('/', methods=['GET'])\n@login_required\ndef get_invoices():\n    \"\"\"Recupera lista fatture con filtri\"\"\"\n    try:\n        # Parametri filtro\n        client_id = request.args.get('client_id', type=int)\n        status = request.args.get('status')  # draft, sent, paid, overdue\n        start_date = request.args.get('start_date')  # YYYY-MM-DD\n        end_date = request.args.get('end_date')  # YYYY-MM-DD\n        year = request.args.get('year', type=int)\n        month = request.args.get('month', type=int)\n        search = request.args.get('search')  # Ricerca in numero fattura\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = Invoice.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_invoices'):\n            return api_response(False, 'Non hai i permessi per visualizzare le fatture', status_code=403)\n        \n        # Applica filtri\n        if client_id:\n            query = query.filter(Invoice.client_id == client_id)\n            \n        if status:\n            query = query.filter(Invoice.status == status)\n            \n        if start_date:\n            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()\n            query = query.filter(Invoice.issue_date >= start_date_obj)\n            \n        if end_date:\n            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()\n            query = query.filter(Invoice.issue_date <= end_date_obj)\n            \n        if year:\n            query = query.filter(extract('year', Invoice.issue_date) == year)\n            \n        if month:\n            query = query.filter(extract('month', Invoice.issue_date) == month)\n            \n        if search:\n            search_pattern = f\"%{search}%\"\n            query = query.filter(Invoice.invoice_number.ilike(search_pattern))\n        \n        # Ordina per data emissione (più recenti prima)\n        query = query.order_by(Invoice.issue_date.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        invoices_data = []\n        for invoice in paginated.items:\n            invoices_data.append({\n                'id': invoice.id,\n                'client_id': invoice.client_id,\n                'client': {\n                    'id': invoice.client.id,\n                    'name': invoice.client.name,\n                    'company': invoice.client.company\n                } if invoice.client else None,\n                'invoice_number': invoice.invoice_number,\n                'status': invoice.status,\n                'billing_period_start': invoice.billing_period_start.isoformat() if invoice.billing_period_start else None,\n                'billing_period_end': invoice.billing_period_end.isoformat() if invoice.billing_period_end else None,\n                'issue_date': invoice.issue_date.isoformat() if invoice.issue_date else None,\n                'due_date': invoice.due_date.isoformat() if invoice.due_date else None,\n                'subtotal': invoice.subtotal,\n                'tax_amount': invoice.tax_amount,\n                'total_amount': invoice.total_amount,\n                'lines_count': len(invoice.lines) if invoice.lines else 0,\n                'created_at': invoice.created_at.isoformat(),\n                'updated_at': invoice.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'invoices': invoices_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperate {len(invoices_data)} fatture\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_invoices.route('/generate', methods=['POST'])\*************\n@login_required\ndef generate_invoice():\n    \"\"\"Genera fattura automatica da timesheet entries per periodo\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_invoices'):\n            return api_response(False, 'Non hai i permessi per generare fatture', status_code=403)\n        \n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['client_id', 'billing_period_start', 'billing_period_end']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        # Verifica che il cliente esista\n        client = Client.query.get(data['client_id'])\n        if not client:\n            return api_response(\n                False,\n                'Cliente non trovato',\n                status_code=404\n            )\n        \n        # Parsing date periodo fatturazione\n        try:\n            billing_start = datetime.strptime(data['billing_period_start'], '%Y-%m-%d').date()\n            billing_end = datetime.strptime(data['billing_period_end'], '%Y-%m-%d').date()\n        except ValueError:\n            return api_response(\n                False,\n                'Formato date non valido. Utilizzare YYYY-MM-DD',\n                status_code=400\n            )\n        \n        # Validazione logica date\n        if billing_start > billing_end:\n            return api_response(\n                False,\n                'La data di inizio periodo non può essere successiva alla data di fine',\n                status_code=400\n            )\n        \n        # Trova timesheet entries fatturabili per il cliente nel periodo\n        timesheet_entries = TimesheetEntry.query.join(Project).join(Contract).filter(\n            and_(\n                Contract.client_id == data['client_id'],\n                TimesheetEntry.date >= billing_start,\n                TimesheetEntry.date <= billing_end,\n                TimesheetEntry.billable == True,\n                TimesheetEntry.billing_status == 'unbilled'\n            )\n        ).all()\n        \n        if not timesheet_entries:\n            return api_response(\n                False,\n                f'Nessuna timesheet entry fatturabile trovata per il cliente nel periodo {billing_start} - {billing_end}',\n                status_code=400\n            )\n        \n        # Genera numero fattura\n        year = datetime.now().year\n        last_invoice = Invoice.query.filter(\n            Invoice.invoice_number.like(f'INV-{year}-%')\n        ).order_by(Invoice.invoice_number.desc()).first()\n        \n        if last_invoice:\n            try:\n                last_num = int(last_invoice.invoice_number.split('-')[2])\n                next_num = last_num + 1\n            except:\n                next_num = 1\n        else:\n            next_num = 1\n        \n        invoice_number = f\"INV-{year}-{next_num:04d}\"\n        \n        # Crea fattura\n        invoice = Invoice(\n            client_id=data['client_id'],\n            invoice_number=invoice_number,\n            status='draft',\n            billing_period_start=billing_start,\n            billing_period_end=billing_end,\n            issue_date=date.today(),\n            due_date=date.today() + timedelta(days=30),  # Default 30 giorni\n            subtotal=0,\n            tax_rate=data.get('tax_rate', 22.0),  # Default IVA 22%\n            tax_amount=0,\n            total_amount=0\n        )\n        \n        db.session.add(invoice)\n        db.session.flush()  # Per ottenere l'ID\n        \n        # Raggruppa entries per progetto/contratto\n        project_groups = {}\n        for entry in timesheet_entries:\n            project_id = entry.project_id\n            if project_id not in project_groups:\n                project_groups[project_id] = {\n                    'project': entry.project,\n                    'contract': entry.project.contract,\n                    'entries': [],\n                    'total_hours': 0,\n                    'total_amount': 0\n                }\n            \n            project_groups[project_id]['entries'].append(entry)\n            project_groups[project_id]['total_hours'] += entry.hours\n            \n            # Calcola importo: usa billing_rate dell'entry o hourly_rate del contratto\n            rate = entry.billing_rate or (entry.project.contract.hourly_rate if entry.project.contract else 0)\n            amount = entry.hours * rate\n            project_groups[project_id]['total_amount'] += amount\n        \n        # Crea righe fattura\n        total_subtotal = 0\n        for project_id, group in project_groups.items():\n            invoice_line = InvoiceLine(\n                invoice_id=invoice.id,\n                project_id=project_id,\n                contract_id=group['contract'].id if group['contract'] else None,\n                description=f\"Servizi professionali - {group['project'].name}\",\n                total_hours=group['total_hours'],\n                hourly_rate=group['contract'].hourly_rate if group['contract'] else 0,\n                total_amount=group['total_amount']\n            )\n            \n            db.session.add(invoice_line)\n            total_subtotal += group['total_amount']\n            \n            # Marca entries come fatturate\n            for entry in group['entries']:\n                entry.billing_status = 'billed'\n                entry.invoice_line_id = invoice_line.id\n        \n        # Aggiorna totali fattura\n        invoice.subtotal = total_subtotal\n        invoice.tax_amount = total_subtotal * (invoice.tax_rate / 100)\n        invoice.total_amount = invoice.subtotal + invoice.tax_amount\n        \n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': invoice.id,\n                'invoice_number': invoice.invoice_number,\n                'client': {\n                    'id': invoice.client.id,\n                    'name': invoice.client.name,\n                    'company': invoice.client.company\n                },\n                'billing_period_start': invoice.billing_period_start.isoformat(),\n                'billing_period_end': invoice.billing_period_end.isoformat(),\n                'subtotal': invoice.subtotal,\n                'tax_amount': invoice.tax_amount,\n                'total_amount': invoice.total_amount,\n                'lines_count': len(invoice.lines),\n                'timesheet_entries_count': len(timesheet_entries)\n            },\n            message=f'Fattura {invoice_number} generata con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_invoices.route('/<int:invoice_id>', methods=['GET'])\n@login_required\ndef get_invoice(invoice_id):\n    \"\"\"Recupera dettaglio singola fattura\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_invoices'):\n            return api_response(False, 'Non hai i permessi per visualizzare le fatture', status_code=403)\n\n        invoice = Invoice.query.get_or_404(invoice_id)\n\n        # Prepara righe fattura\n        lines_data = []\n        for line in invoice.lines:\n            lines_data.append({\n                'id': line.id,\n                'project_id': line.project_id,\n                'project': {\n                    'id': line.project.id,\n                    'name': line.project.name\n                } if line.project else None,\n                'contract_id': line.contract_id,\n                'contract': {\n                    'id': line.contract.id,\n                    'contract_number': line.contract.contract_number,\n                    'title': line.contract.title\n                } if line.contract else None,\n                'description': line.description,\n                'total_hours': line.total_hours,\n                'hourly_rate': line.hourly_rate,\n                'total_amount': line.total_amount\n            })\n\n        return api_response(\n            data={\n                'id': invoice.id,\n                'client_id': invoice.client_id,\n                'client': {\n                    'id': invoice.client.id,\n                    'name': invoice.client.name,\n                    'company': invoice.client.company,\n                    'email': invoice.client.email,\n                    'phone': invoice.client.phone,\n                    'address': invoice.client.address\n                } if invoice.client else None,\n                'invoice_number': invoice.invoice_number,\n                'status': invoice.status,\n                'billing_period_start': invoice.billing_period_start.isoformat() if invoice.billing_period_start else None,\n                'billing_period_end': invoice.billing_period_end.isoformat() if invoice.billing_period_end else None,\n                'issue_date': invoice.issue_date.isoformat() if invoice.issue_date else None,\n                'due_date': invoice.due_date.isoformat() if invoice.due_date else None,\n                'subtotal': invoice.subtotal,\n                'tax_rate': invoice.tax_rate,\n                'tax_amount': invoice.tax_amount,\n                'total_amount': invoice.total_amount,\n                'notes': invoice.notes,\n                'lines': lines_data,\n                'created_at': invoice.created_at.isoformat(),\n                'updated_at': invoice.updated_at.isoformat()\n            },\n            message=\"Dettaglio fattura recuperato con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_invoices.route('/<int:invoice_id>', methods=['PUT'])\*************\n@login_required\ndef update_invoice(invoice_id):\n    \"\"\"Aggiorna una fattura esistente\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_invoices'):\n            return api_response(False, 'Non hai i permessi per modificare fatture', status_code=403)\n\n        invoice = Invoice.query.get_or_404(invoice_id)\n        data = request.get_json()\n\n        # Non permettere modifiche a fatture già inviate/pagate\n        if invoice.status in ['sent', 'paid'] and 'status' not in data:\n            return api_response(\n                False,\n                'Non è possibile modificare fatture già inviate o pagate',\n                status_code=400\n            )\n\n        # Aggiorna campi se forniti\n        if 'status' in data:\n            valid_statuses = ['draft', 'sent', 'paid', 'overdue']\n            if data['status'] not in valid_statuses:\n                return api_response(\n                    False,\n                    f'Status non valido. Valori ammessi: {\", \".join(valid_statuses)}',\n                    status_code=400\n                )\n            invoice.status = data['status']\n\n        if 'issue_date' in data and data['issue_date']:\n            try:\n                invoice.issue_date = datetime.strptime(data['issue_date'], '%Y-%m-%d').date()\n            except ValueError:\n                return api_response(\n                    False,\n                    'Formato issue_date non valido. Utilizzare YYYY-MM-DD',\n                    status_code=400\n                )\n\n        if 'due_date' in data and data['due_date']:\n            try:\n                invoice.due_date = datetime.strptime(data['due_date'], '%Y-%m-%d').date()\n            except ValueError:\n                return api_response(\n                    False,\n                    'Formato due_date non valido. Utilizzare YYYY-MM-DD',\n                    status_code=400\n                )\n\n        if 'tax_rate' in data:\n            invoice.tax_rate = data['tax_rate']\n            # Ricalcola tasse\n            invoice.tax_amount = invoice.subtotal * (invoice.tax_rate / 100)\n            invoice.total_amount = invoice.subtotal + invoice.tax_amount\n\n        if 'notes' in data:\n            invoice.notes = data['notes']\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': invoice.id,\n                'invoice_number': invoice.invoice_number,\n                'status': invoice.status,\n                'total_amount': invoice.total_amount,\n                'updated_at': invoice.updated_at.isoformat()\n            },\n            message='Fattura aggiornata con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_invoices.route('/<int:invoice_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_invoice(invoice_id):\n    \"\"\"Elimina una fattura\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_invoices'):\n            return api_response(False, 'Non hai i permessi per eliminare fatture', status_code=403)\n\n        invoice = Invoice.query.get_or_404(invoice_id)\n\n        # Non permettere eliminazione di fatture già inviate/pagate\n        if invoice.status in ['sent', 'paid']:\n            return api_response(\n                False,\n                'Non è possibile eliminare fatture già inviate o pagate',\n                status_code=400\n            )\n\n        # Rimetti timesheet entries come non fatturate\n        for line in invoice.lines:\n            TimesheetEntry.query.filter(\n                TimesheetEntry.invoice_line_id == line.id\n            ).update({\n                'billing_status': 'unbilled',\n                'invoice_line_id': None\n            })\n\n        # Elimina righe fattura\n        for line in invoice.lines:\n            db.session.delete(line)\n\n        # Elimina fattura\n        invoice_number = invoice.invoice_number\n        db.session.delete(invoice)\n        db.session.commit()\n\n        return api_response(\n            message=f'Fattura {invoice_number} eliminata con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n"}