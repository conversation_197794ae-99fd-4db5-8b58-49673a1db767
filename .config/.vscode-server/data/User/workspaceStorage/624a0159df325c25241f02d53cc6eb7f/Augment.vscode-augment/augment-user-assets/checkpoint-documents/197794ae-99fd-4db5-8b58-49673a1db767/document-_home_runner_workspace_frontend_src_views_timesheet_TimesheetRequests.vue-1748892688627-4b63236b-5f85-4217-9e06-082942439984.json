{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetRequests.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\"><PERSON><PERSON></h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Gestisci le tue richieste di ferie, permessi e smart working\n          </p>\n        </div>\n        \n        <div class=\"flex space-x-3\">\n          <button \n            @click=\"showRequestModal('vacation')\"\n            class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            <PERSON><PERSON>\n          </button>\n          <button \n            @click=\"showRequestModal('leave')\"\n            class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            <PERSON><PERSON>\n          </button>\n          <button \n            @click=\"showRequestModal('smart_working')\"\n            class=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Smart Working\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Tipo Richiesta\n          </label>\n          <select \n            v-model=\"selectedType\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i tipi</option>\n            <option value=\"vacation\">Ferie</option>\n            <option value=\"leave\">Permessi</option>\n            <option value=\"smart_working\">Smart Working</option>\n          </select>\n        </div>\n        \n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Stato\n          </label>\n          <select \n            v-model=\"selectedStatus\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti gli stati</option>\n            <option value=\"pending\">In Attesa</option>\n            <option value=\"approved\">Approvato</option>\n            <option value=\"rejected\">Rifiutato</option>\n          </select>\n        </div>\n        \n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Da Data\n          </label>\n          <input \n            v-model=\"dateFrom\"\n            type=\"date\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n        </div>\n        \n        <div class=\"flex items-end\">\n          <button \n            @click=\"loadRequests\"\n            class=\"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Filtra\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Lista Richieste -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Le Mie Richieste\n        </h3>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Tipo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Periodo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Durata\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Motivo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Richiesta il\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"request in requests\" :key=\"request.id\">\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span \n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getTypeClass(request.type)\"\n                >\n                  {{ getTypeText(request.type) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatPeriod(request) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatDuration(request) }}\n              </td>\n              <td class=\"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate\">\n                {{ request.reason || 'N/A' }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span \n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getStatusClass(request.status)\"\n                >\n                  {{ getStatusText(request.status) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatDate(request.created_at) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <button \n                  v-if=\"request.status === 'pending'\"\n                  @click=\"editRequest(request)\"\n                  class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3\"\n                >\n                  Modifica\n                </button>\n                <button \n                  v-if=\"request.status === 'pending'\"\n                  @click=\"deleteRequest(request.id)\"\n                  class=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                >\n                  Elimina\n                </button>\n                <span \n                  v-else\n                  class=\"text-gray-400 dark:text-gray-500\"\n                >\n                  {{ request.status === 'approved' ? 'Approvata' : 'Rifiutata' }}\n                </span>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n        \n        <!-- Empty state -->\n        <div v-if=\"requests.length === 0\" class=\"text-center py-8\">\n          <div class=\"mx-auto h-12 w-12 text-gray-400\">\n            <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n          </div>\n          <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna richiesta</h3>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Non hai ancora effettuato richieste per il periodo selezionato.\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistiche -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ferie Rimanenti\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ remainingVacationDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Permessi Usati\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ usedLeaveHours }}h / {{ totalLeaveHours }}h\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 012-2h6.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H19a2 2 0 012 2v0a2 2 0 00-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Smart Working\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ smartWorkingDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ pendingRequests }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Nuova Richiesta -->\n    <div v-if=\"showModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ getModalTitle() }}\n          </h3>\n          \n          <form @submit.prevent=\"submitRequest\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <!-- Campi comuni -->\n              <div v-if=\"requestType === 'vacation' || requestType === 'smart_working'\">\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Inizio\n                </label>\n                <input \n                  v-model=\"formData.start_date\" \n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div v-if=\"requestType === 'vacation' || requestType === 'smart_working'\">\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Fine\n                </label>\n                <input \n                  v-model=\"formData.end_date\" \n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <!-- Campi specifici per permessi -->\n              <div v-if=\"requestType === 'leave'\">\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data\n                </label>\n                <input \n                  v-model=\"formData.date\" \n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div v-if=\"requestType === 'leave'\">\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Ore Richieste\n                </label>\n                <input \n                  v-model=\"formData.hours\" \n                  type=\"number\"\n                  step=\"0.5\"\n                  min=\"0.5\"\n                  max=\"8\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <!-- Motivo -->\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {{ requestType === 'smart_working' ? 'Note (opzionale)' : 'Motivo' }}\n                </label>\n                <textarea\n                  v-model=\"formData.reason\"\n                  rows=\"3\"\n                  :required=\"requestType !== 'smart_working'\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  :placeholder=\"requestType === 'smart_working' ? 'Note aggiuntive...' : 'Descrivi il motivo della richiesta...'\"\n                ></textarea>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Invio...' : 'Invia Richiesta' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst requests = ref([])\nconst loading = ref(false)\nconst showModal = ref(false)\nconst saving = ref(false)\nconst requestType = ref('')\n\n// Filters\nconst selectedType = ref('')\nconst selectedStatus = ref('')\nconst dateFrom = ref('')\n\n// Form data\nconst formData = ref({\n  start_date: '',\n  end_date: '',\n  date: '',\n  hours: 1,\n  reason: ''\n})\n\n// Computed\nconst remainingVacationDays = computed(() => {\n  // TODO: Calcolare dalle API\n  return 20\n})\n\nconst usedLeaveHours = computed(() => {\n  return requests.value\n    .filter(r => r.type === 'leave' && r.status === 'approved')\n    .reduce((sum, r) => sum + (r.hours || 0), 0)\n})\n\nconst totalLeaveHours = computed(() => {\n  // TODO: Configurabile per utente\n  return 104 // 13 giorni * 8 ore\n})\n\nconst smartWorkingDays = computed(() => {\n  return requests.value\n    .filter(r => r.type === 'smart_working' && r.status === 'approved')\n    .reduce((sum, r) => sum + (r.days || 0), 0)\n})\n\nconst pendingRequests = computed(() => {\n  return requests.value.filter(r => r.status === 'pending').length\n})\n\n// Methods\nconst loadRequests = async () => {\n  loading.value = true\n\n  try {\n    const params = new URLSearchParams()\n\n    if (selectedType.value) {\n      params.append('type', selectedType.value)\n    }\n\n    if (selectedStatus.value) {\n      params.append('status', selectedStatus.value)\n    }\n\n    if (dateFrom.value) {\n      params.append('start_date', dateFrom.value)\n    }\n\n    const response = await fetch(`/api/time-off-requests/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      requests.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading requests:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst showRequestModal = (type) => {\n  requestType.value = type\n  formData.value = {\n    start_date: '',\n    end_date: '',\n    date: '',\n    hours: 1,\n    reason: ''\n  }\n  showModal.value = true\n}\n\nconst closeModal = () => {\n  showModal.value = false\n  requestType.value = ''\n}\n\nconst getModalTitle = () => {\n  switch (requestType.value) {\n    case 'vacation':\n      return 'Richiesta Ferie'\n    case 'leave':\n      return 'Richiesta Permesso'\n    case 'smart_working':\n      return 'Richiesta Smart Working'\n    default:\n      return 'Nuova Richiesta'\n  }\n}\n\nconst submitRequest = async () => {\n  saving.value = true\n\n  try {\n    const data = {\n      request_type: requestType.value,\n      ...formData.value\n    }\n\n    const response = await fetch('/api/time-off-requests/', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(data)\n    })\n\n    if (response.ok) {\n      await loadRequests()\n      closeModal()\n    }\n  } catch (err) {\n    console.error('Error submitting request:', err)\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editRequest = (request) => {\n  // TODO: Implementare modifica\n  console.log('Edit request:', request)\n}\n\nconst deleteRequest = async (requestId) => {\n  if (!confirm('Sei sicuro di voler eliminare questa richiesta?')) return\n  \n  try {\n    const response = await fetch(`/api/timesheet-requests/${requestId}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      await loadRequests()\n    }\n  } catch (err) {\n    console.error('Error deleting request:', err)\n  }\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\nconst formatPeriod = (request) => {\n  if (request.type === 'leave') {\n    return formatDate(request.date)\n  }\n  return `${formatDate(request.start_date)} - ${formatDate(request.end_date)}`\n}\n\nconst formatDuration = (request) => {\n  if (request.type === 'leave') {\n    return `${request.hours}h`\n  }\n  return `${request.days || 0} giorni`\n}\n\nconst getTypeClass = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n    case 'leave':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'smart_working':\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n  }\n}\n\nconst getTypeText = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'Ferie'\n    case 'leave':\n      return 'Permesso'\n    case 'smart_working':\n      return 'Smart Working'\n    default:\n      return 'Altro'\n  }\n}\n\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n    default:\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n  }\n}\n\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'Approvato'\n    case 'rejected':\n      return 'Rifiutato'\n    default:\n      return 'In Attesa'\n  }\n}\n\n// Watchers\nwatch([selectedType, selectedStatus, dateFrom], () => {\n  loadRequests()\n})\n\n// Lifecycle\nonMounted(() => {\n  loadRequests()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\"><PERSON><PERSON></h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Gestisci le tue richieste di ferie, permessi e smart working\n          </p>\n        </div>\n        \n        <div class=\"flex space-x-3\">\n          <button \n            @click=\"showRequestModal('vacation')\"\n            class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            <PERSON><PERSON>\n          </button>\n          <button \n            @click=\"showRequestModal('leave')\"\n            class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            <PERSON><PERSON>\n          </button>\n          <button \n            @click=\"showRequestModal('smart_working')\"\n            class=\"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Smart Working\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Tipo Richiesta\n          </label>\n          <select \n            v-model=\"selectedType\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i tipi</option>\n            <option value=\"vacation\">Ferie</option>\n            <option value=\"leave\">Permessi</option>\n            <option value=\"smart_working\">Smart Working</option>\n          </select>\n        </div>\n        \n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Stato\n          </label>\n          <select \n            v-model=\"selectedStatus\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti gli stati</option>\n            <option value=\"pending\">In Attesa</option>\n            <option value=\"approved\">Approvato</option>\n            <option value=\"rejected\">Rifiutato</option>\n          </select>\n        </div>\n        \n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Da Data\n          </label>\n          <input \n            v-model=\"dateFrom\"\n            type=\"date\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n        </div>\n        \n        <div class=\"flex items-end\">\n          <button \n            @click=\"loadRequests\"\n            class=\"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Filtra\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Lista Richieste -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n          Le Mie Richieste\n        </h3>\n      </div>\n      \n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Tipo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Periodo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Durata\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Motivo\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Richiesta il\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"request in requests\" :key=\"request.id\">\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span \n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getTypeClass(request.type)\"\n                >\n                  {{ getTypeText(request.type) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatPeriod(request) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatDuration(request) }}\n              </td>\n              <td class=\"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate\">\n                {{ request.reason || 'N/A' }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap\">\n                <span \n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getStatusClass(request.status)\"\n                >\n                  {{ getStatusText(request.status) }}\n                </span>\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                {{ formatDate(request.created_at) }}\n              </td>\n              <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                <button \n                  v-if=\"request.status === 'pending'\"\n                  @click=\"editRequest(request)\"\n                  class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3\"\n                >\n                  Modifica\n                </button>\n                <button \n                  v-if=\"request.status === 'pending'\"\n                  @click=\"deleteRequest(request.id)\"\n                  class=\"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300\"\n                >\n                  Elimina\n                </button>\n                <span \n                  v-else\n                  class=\"text-gray-400 dark:text-gray-500\"\n                >\n                  {{ request.status === 'approved' ? 'Approvata' : 'Rifiutata' }}\n                </span>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n        \n        <!-- Empty state -->\n        <div v-if=\"requests.length === 0\" class=\"text-center py-8\">\n          <div class=\"mx-auto h-12 w-12 text-gray-400\">\n            <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n          </div>\n          <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessuna richiesta</h3>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Non hai ancora effettuato richieste per il periodo selezionato.\n          </p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistiche -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ferie Rimanenti\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ remainingVacationDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Permessi Usati\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ usedLeaveHours }}h / {{ totalLeaveHours }}h\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 012-2h6.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H19a2 2 0 012 2v0a2 2 0 00-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Smart Working\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ smartWorkingDays }} giorni\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ pendingRequests }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Nuova Richiesta -->\n    <div v-if=\"showModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ getModalTitle() }}\n          </h3>\n          \n          <form @submit.prevent=\"submitRequest\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <!-- Campi comuni -->\n              <div v-if=\"requestType === 'vacation' || requestType === 'smart_working'\">\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Inizio\n                </label>\n                <input \n                  v-model=\"formData.start_date\" \n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div v-if=\"requestType === 'vacation' || requestType === 'smart_working'\">\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data Fine\n                </label>\n                <input \n                  v-model=\"formData.end_date\" \n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <!-- Campi specifici per permessi -->\n              <div v-if=\"requestType === 'leave'\">\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Data\n                </label>\n                <input \n                  v-model=\"formData.date\" \n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div v-if=\"requestType === 'leave'\">\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  Ore Richieste\n                </label>\n                <input \n                  v-model=\"formData.hours\" \n                  type=\"number\"\n                  step=\"0.5\"\n                  min=\"0.5\"\n                  max=\"8\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <!-- Motivo -->\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                  {{ requestType === 'smart_working' ? 'Note (opzionale)' : 'Motivo' }}\n                </label>\n                <textarea\n                  v-model=\"formData.reason\"\n                  rows=\"3\"\n                  :required=\"requestType !== 'smart_working'\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  :placeholder=\"requestType === 'smart_working' ? 'Note aggiuntive...' : 'Descrivi il motivo della richiesta...'\"\n                ></textarea>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Invio...' : 'Invia Richiesta' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst requests = ref([])\nconst loading = ref(false)\nconst showModal = ref(false)\nconst saving = ref(false)\nconst requestType = ref('')\n\n// Filters\nconst selectedType = ref('')\nconst selectedStatus = ref('')\nconst dateFrom = ref('')\n\n// Form data\nconst formData = ref({\n  start_date: '',\n  end_date: '',\n  date: '',\n  hours: 1,\n  reason: ''\n})\n\n// Computed\nconst remainingVacationDays = computed(() => {\n  // TODO: Calcolare dalle API\n  return 20\n})\n\nconst usedLeaveHours = computed(() => {\n  return requests.value\n    .filter(r => r.type === 'leave' && r.status === 'approved')\n    .reduce((sum, r) => sum + (r.hours || 0), 0)\n})\n\nconst totalLeaveHours = computed(() => {\n  // TODO: Configurabile per utente\n  return 104 // 13 giorni * 8 ore\n})\n\nconst smartWorkingDays = computed(() => {\n  return requests.value\n    .filter(r => r.type === 'smart_working' && r.status === 'approved')\n    .reduce((sum, r) => sum + (r.days || 0), 0)\n})\n\nconst pendingRequests = computed(() => {\n  return requests.value.filter(r => r.status === 'pending').length\n})\n\n// Methods\nconst loadRequests = async () => {\n  loading.value = true\n\n  try {\n    const params = new URLSearchParams()\n\n    if (selectedType.value) {\n      params.append('type', selectedType.value)\n    }\n\n    if (selectedStatus.value) {\n      params.append('status', selectedStatus.value)\n    }\n\n    if (dateFrom.value) {\n      params.append('start_date', dateFrom.value)\n    }\n\n    const response = await fetch(`/api/time-off-requests/?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      requests.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading requests:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst showRequestModal = (type) => {\n  requestType.value = type\n  formData.value = {\n    start_date: '',\n    end_date: '',\n    date: '',\n    hours: 1,\n    reason: ''\n  }\n  showModal.value = true\n}\n\nconst closeModal = () => {\n  showModal.value = false\n  requestType.value = ''\n}\n\nconst getModalTitle = () => {\n  switch (requestType.value) {\n    case 'vacation':\n      return 'Richiesta Ferie'\n    case 'leave':\n      return 'Richiesta Permesso'\n    case 'smart_working':\n      return 'Richiesta Smart Working'\n    default:\n      return 'Nuova Richiesta'\n  }\n}\n\nconst submitRequest = async () => {\n  saving.value = true\n\n  try {\n    const data = {\n      request_type: requestType.value,\n      ...formData.value\n    }\n\n    const response = await fetch('/api/time-off-requests/', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(data)\n    })\n\n    if (response.ok) {\n      await loadRequests()\n      closeModal()\n    }\n  } catch (err) {\n    console.error('Error submitting request:', err)\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editRequest = (request) => {\n  // TODO: Implementare modifica\n  console.log('Edit request:', request)\n}\n\nconst deleteRequest = async (requestId) => {\n  if (!confirm('Sei sicuro di voler eliminare questa richiesta?')) return\n\n  try {\n    const response = await fetch(`/api/time-off-requests/${requestId}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      await loadRequests()\n    }\n  } catch (err) {\n    console.error('Error deleting request:', err)\n  }\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\nconst formatPeriod = (request) => {\n  if (request.type === 'leave') {\n    return formatDate(request.date)\n  }\n  return `${formatDate(request.start_date)} - ${formatDate(request.end_date)}`\n}\n\nconst formatDuration = (request) => {\n  if (request.type === 'leave') {\n    return `${request.hours}h`\n  }\n  return `${request.days || 0} giorni`\n}\n\nconst getTypeClass = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'\n    case 'leave':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'smart_working':\n      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n  }\n}\n\nconst getTypeText = (type) => {\n  switch (type) {\n    case 'vacation':\n      return 'Ferie'\n    case 'leave':\n      return 'Permesso'\n    case 'smart_working':\n      return 'Smart Working'\n    default:\n      return 'Altro'\n  }\n}\n\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n    default:\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n  }\n}\n\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'Approvato'\n    case 'rejected':\n      return 'Rifiutato'\n    default:\n      return 'In Attesa'\n  }\n}\n\n// Watchers\nwatch([selectedType, selectedStatus, dateFrom], () => {\n  loadRequests()\n})\n\n// Lifecycle\nonMounted(() => {\n  loadRequests()\n})\n</script>\n"}