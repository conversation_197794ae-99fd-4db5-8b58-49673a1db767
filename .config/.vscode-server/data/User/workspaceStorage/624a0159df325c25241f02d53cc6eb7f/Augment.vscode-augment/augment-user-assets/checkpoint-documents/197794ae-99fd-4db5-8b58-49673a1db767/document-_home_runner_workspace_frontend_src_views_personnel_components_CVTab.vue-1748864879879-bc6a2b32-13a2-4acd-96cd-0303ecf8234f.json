{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/CVTab.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Current CV Section -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">CV Attuale</h3>\n        <button v-if=\"canEdit && !uploading\"\n                @click=\"triggerFileUpload\"\n                class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200\">\n          <svg class=\"w-4 h-4 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          {{ user.profile?.current_cv_path ? 'Aggiorna CV' : 'Carica CV' }}\n        </button>\n      </div>\n\n      <!-- CV Upload Area -->\n      <div v-if=\"!user.profile?.current_cv_path && canEdit\"\n           @drop=\"handleDrop\"\n           @dragover.prevent\n           @dragenter.prevent\n           :class=\"[\n             'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200',\n             isDragOver ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'\n           ]\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Carica il tuo CV</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Trascina qui il file o <button @click=\"triggerFileUpload\" class=\"text-blue-600 hover:text-blue-500\">sfoglia</button>\n        </p>\n        <p class=\"mt-1 text-xs text-gray-400\">PDF, DOCX, DOC, TXT (max 10MB)</p>\n      </div>\n\n      <!-- Existing CV Display -->\n      <div v-else-if=\"user.profile?.current_cv_path\" class=\"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n        <svg class=\"w-8 h-8 text-red-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div class=\"flex-1\">\n          <p class=\"text-sm font-medium text-gray-900 dark:text-white\">CV_{{ user.full_name }}.pdf</p>\n          <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n            Caricato il {{ formatDate(user.profile.cv_last_updated) }}\n          </p>\n          <div v-if=\"user.profile.cv_analysis_data\" class=\"mt-1\">\n            <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n              ✨ Analisi AI completata\n            </span>\n          </div>\n        </div>\n        <div class=\"flex space-x-2\">\n          <button @click=\"downloadCV\" \n                  class=\"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300\"\n                  title=\"Scarica CV\">\n            <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </button>\n          <button v-if=\"canEdit\" \n                  @click=\"deleteCV\" \n                  class=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                  title=\"Elimina CV\">\n            <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" clip-rule=\"evenodd\"></path>\n              <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 112 0v4a1 1 0 11-2 0V7z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      <!-- Upload Progress -->\n      <div v-if=\"uploading || analyzingWithAI\" class=\"mt-4\">\n        <div class=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2\">\n          <div class=\"flex items-center\">\n            <svg v-if=\"analyzingWithAI\" class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <span>{{ analyzingWithAI ? 'Analisi AI in corso...' : 'Caricamento in corso...' }}</span>\n          </div>\n          <span>{{ analyzingWithAI ? aiAnalysisProgress : uploadProgress }}%</span>\n        </div>\n        <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n          <div :class=\"[\n                 'h-2 rounded-full transition-all duration-300',\n                 analyzingWithAI ? 'bg-purple-600' : 'bg-blue-600'\n               ]\"\n               :style=\"{ width: (analyzingWithAI ? aiAnalysisProgress : uploadProgress) + '%' }\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- AI Analysis Section -->\n    <div v-if=\"cvAnalysis\" class=\"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <div class=\"flex items-center\">\n          <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3\">\n            <svg class=\"w-4 h-4 text-purple-600 dark:text-purple-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <h3 class=\"text-lg font-medium text-purple-900 dark:text-purple-100\">\n            Analisi AI del CV\n          </h3>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <button v-if=\"canEdit && cvAnalysis.skills?.length > 0\"\n                  @click=\"showSkillsModal = true\"\n                  class=\"text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center\">\n            <svg class=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Aggiungi Competenze\n          </button>\n        </div>\n      </div>\n\n      <!-- Professional Summary -->\n      <div v-if=\"cvAnalysis.summary\" class=\"mb-4\">\n        <h4 class=\"text-sm font-medium text-purple-800 dark:text-purple-200 mb-2\">Profilo Professionale</h4>\n        <p class=\"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700\">{{ cvAnalysis.summary }}</p>\n      </div>\n\n      <!-- Experience Years -->\n      <div v-if=\"cvAnalysis.experience_years\" class=\"mb-4\">\n        <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200\">\n          <svg class=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          {{ cvAnalysis.experience_years }} anni di esperienza\n        </span>\n      </div>\n\n      <!-- Extracted Skills -->\n      <div v-if=\"cvAnalysis.skills?.length > 0\">\n        <h4 class=\"text-sm font-medium text-purple-800 dark:text-purple-200 mb-3\">Competenze Estratte</h4>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-3 mb-3\">\n          <div v-for=\"(skill, index) in cvAnalysis.skills.slice(0, 8)\"\n               :key=\"index\"\n               class=\"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700\">\n            <div class=\"flex items-center space-x-2\">\n              <div class=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ skill.name }}</span>\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <span v-if=\"skill.category\" class=\"text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded\">\n                {{ skill.category }}\n              </span>\n              <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n                {{ getLevelLabel(skill.level) }}\n              </span>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"cvAnalysis.skills.length > 8\" class=\"text-center\">\n          <span class=\"text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full\">\n            +{{ cvAnalysis.skills.length - 8 }} altre competenze disponibili\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Hidden file input -->\n    <input ref=\"fileInput\" \n           type=\"file\" \n           accept=\".pdf,.docx,.doc,.txt\" \n           @change=\"handleFileSelect\" \n           class=\"hidden\">\n\n    <!-- Skills Selection Modal -->\n    <div v-if=\"showSkillsModal\" \n         class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\"\n         @click=\"showSkillsModal = false\">\n      <div class=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\"\n           @click.stop>\n        <div class=\"mt-3\">\n          <!-- Modal Header -->\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Seleziona Competenze da Aggiungere\n            </h3>\n            <button @click=\"showSkillsModal = false\"\n                    class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n              <svg class=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <!-- Skills List -->\n          <div class=\"max-h-96 overflow-y-auto mb-4\">\n            <div class=\"space-y-2\">\n              <div v-for=\"(skill, index) in cvAnalysis.skills\" \n                   :key=\"index\"\n                   class=\"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700\">\n                <input type=\"checkbox\" \n                       :id=\"`skill-${index}`\"\n                       v-model=\"selectedSkills\"\n                       :value=\"index\"\n                       class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\">\n                <label :for=\"`skill-${index}`\" class=\"ml-3 flex-1 cursor-pointer\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ skill.name }}</div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {{ skill.category }} • Livello {{ skill.level || 3 }}\n                    <span v-if=\"skill.years_experience\"> • {{ skill.years_experience }} anni</span>\n                  </div>\n                  <div v-if=\"skill.context\" class=\"text-xs text-gray-400 mt-1\">{{ skill.context }}</div>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          <!-- Modal Actions -->\n          <div class=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600\">\n            <button @click=\"showSkillsModal = false\"\n                    class=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200\">\n              Annulla\n            </button>\n            <button @click=\"addSelectedSkills\"\n                    :disabled=\"selectedSkills.length === 0 || addingSkills\"\n                    class=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200\">\n              {{ addingSkills ? 'Aggiungendo...' : `Aggiungi ${selectedSkills.length} competenze` }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch } from 'vue'\nimport { useToast } from '@/composables/useToast'\nimport api from '@/utils/api'\n\n// Props\nconst props = defineProps({\n  user: {\n    type: Object,\n    required: true\n  },\n  canEdit: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// Emits\nconst emit = defineEmits(['user-updated'])\n\n// Composables\nconst { success: showSuccess, error: showError, info: showInfo } = useToast()\n\n// Reactive state\nconst uploading = ref(false)\nconst analyzingWithAI = ref(false)\nconst uploadProgress = ref(0)\nconst isDragOver = ref(false)\nconst showSkillsModal = ref(false)\nconst selectedSkills = ref([])\nconst addingSkills = ref(false)\nconst fileInput = ref(null)\nconst aiAnalysisProgress = ref(0)\n\n// Computed\nconst cvAnalysis = computed(() => {\n  if (!props.user.profile?.cv_analysis_data) return null\n  try {\n    return JSON.parse(props.user.profile.cv_analysis_data)\n  } catch (e) {\n    console.error('Error parsing CV analysis data:', e)\n    return null\n  }\n})\n\n// Methods\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nconst getLevelLabel = (level) => {\n  const levels = {\n    'beginner': 'Principiante',\n    'intermediate': 'Intermedio',\n    'advanced': 'Avanzato',\n    'expert': 'Esperto',\n    1: 'Principiante',\n    2: 'Base',\n    3: 'Intermedio',\n    4: 'Avanzato',\n    5: 'Esperto'\n  }\n  return levels[level] || 'Intermedio'\n}\n\n\n\nconst triggerFileUpload = () => {\n  fileInput.value?.click()\n}\n\nconst handleFileSelect = (event) => {\n  const file = event.target.files[0]\n  if (file) {\n    uploadCV(file)\n  }\n}\n\nconst handleDrop = (event) => {\n  event.preventDefault()\n  isDragOver.value = false\n  \n  const files = event.dataTransfer.files\n  if (files.length > 0) {\n    uploadCV(files[0])\n  }\n}\n\nconst uploadCV = async (file) => {\n  // Validate file\n  const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/plain']\n  if (!allowedTypes.includes(file.type)) {\n    showError('Formato file non supportato', 'Usa PDF, DOCX, DOC o TXT.')\n    return\n  }\n\n  if (file.size > 10 * 1024 * 1024) { // 10MB\n    showError('File troppo grande', 'Dimensione massima: 10MB.')\n    return\n  }\n\n  uploading.value = true\n  uploadProgress.value = 0\n\n  try {\n    // Step 1: Upload file\n    const formData = new FormData()\n    formData.append('cv_file', file)\n    formData.append('analyze_skills', 'true')\n    formData.append('auto_add_skills', 'false') // We'll let user choose\n\n    // Simulate upload progress\n    const uploadInterval = setInterval(() => {\n      if (uploadProgress.value < 90) {\n        uploadProgress.value += Math.random() * 15\n      }\n    }, 200)\n\n    const response = await api.post(`/api/personnel/users/${props.user.id}/cv/upload`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    })\n\n    clearInterval(uploadInterval)\n    uploadProgress.value = 100\n\n    const data = response.data\n\n    // Debug: log the response\n    console.log('Upload response:', data)\n\n    if (data.success) {\n      uploading.value = false\n\n      // Update user data first\n      emit('user-updated', data.data)\n\n      // Check if AI analysis is already present\n      if (data.data.profile?.cv_analysis_data) {\n        try {\n          const analysisData = JSON.parse(data.data.profile.cv_analysis_data)\n          console.log('Analysis data:', analysisData)\n\n          if (analysisData.skills && analysisData.skills.length > 0) {\n            showSuccess('CV caricato e analizzato con successo!', `Trovate ${analysisData.skills.length} competenze`)\n          } else {\n            showSuccess('CV caricato con successo!', 'Nessuna competenza estratta dall\\'AI')\n          }\n        } catch (e) {\n          console.error('Error parsing analysis data:', e)\n          showSuccess('CV caricato con successo!', 'Errore nel parsing dei dati AI')\n        }\n      } else {\n        showSuccess('CV caricato con successo!', 'Analisi AI non disponibile')\n        console.log('No AI analysis data found in response. Profile data:', data.data.profile)\n      }\n\n      // Reset file input\n      if (fileInput.value) {\n        fileInput.value.value = ''\n      }\n    } else {\n      throw new Error(data.message || 'Errore durante il caricamento')\n    }\n  } catch (error) {\n    console.error('Errore durante il caricamento del CV:', error)\n    showError('Errore durante il caricamento del CV', error.message)\n  } finally {\n    uploading.value = false\n    uploadProgress.value = 0\n  }\n}\n\n\n\nconst downloadCV = async () => {\n  try {\n    const response = await fetch(`/api/personnel/users/${props.user.id}/cv/download`, {\n      method: 'GET',\n      credentials: 'include'\n    })\n\n    if (response.ok) {\n      const blob = await response.blob()\n      const url = window.URL.createObjectURL(blob)\n      const a = document.createElement('a')\n      a.href = url\n      a.download = `CV_${props.user.full_name}.pdf`\n      document.body.appendChild(a)\n      a.click()\n      window.URL.revokeObjectURL(url)\n      document.body.removeChild(a)\n    } else {\n      const data = await response.json()\n      throw new Error(data.message || 'Errore durante il download')\n    }\n  } catch (error) {\n    console.error('Errore durante il download del CV:', error)\n    alert('Errore durante il download del CV: ' + error.message)\n  }\n}\n\nconst deleteCV = async () => {\n  if (!confirm('Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata.')) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/personnel/users/${props.user.id}/cv`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || ''\n      }\n    })\n\n    const data = await response.json()\n\n    if (data.success) {\n      // Update user data\n      emit('user-updated', data.data)\n      showSuccess('CV eliminato con successo')\n    } else {\n      throw new Error(data.message || 'Errore durante l\\'eliminazione')\n    }\n  } catch (error) {\n    console.error('Errore durante l\\'eliminazione del CV:', error)\n    showError('Errore durante l\\'eliminazione del CV', error.message)\n  }\n}\n\nconst addSelectedSkills = async () => {\n  if (selectedSkills.value.length === 0) return\n\n  addingSkills.value = true\n\n  try {\n    // Prepare skills data with proper level conversion\n    const skillsToAdd = selectedSkills.value.map(skillIndex => {\n      const skill = cvAnalysis.value.skills[skillIndex]\n      return {\n        ...skill,\n        level: convertLevelToNumber(skill.level)\n      }\n    })\n\n    const response = await fetch(`/api/personnel/users/${props.user.id}/skills/from-cv`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || ''\n      },\n      body: JSON.stringify({\n        selected_skills: skillsToAdd\n      })\n    })\n\n    const data = await response.json()\n\n    if (data.success) {\n      const { total_added, total_skipped } = data.data\n\n      if (total_added > 0) {\n        showSuccess(`Aggiunte ${total_added} competenze al profilo!`)\n      }\n\n      if (total_skipped > 0) {\n        showInfo(`${total_skipped} competenze erano già presenti nel profilo`)\n      }\n\n      // Close modal and reset selection\n      showSkillsModal.value = false\n      selectedSkills.value = []\n\n      // Emit event to refresh user data\n      emit('user-updated')\n    } else {\n      throw new Error(data.message || 'Errore durante l\\'aggiunta delle competenze')\n    }\n  } catch (error) {\n    console.error('Errore durante l\\'aggiunta delle competenze:', error)\n    showError('Errore durante l\\'aggiunta delle competenze', error.message)\n  } finally {\n    addingSkills.value = false\n  }\n}\n\nconst convertLevelToNumber = (level) => {\n  const levelMap = {\n    'beginner': 1,\n    'intermediate': 3,\n    'advanced': 4,\n    'expert': 5\n  }\n\n  // If it's already a number, return it\n  if (typeof level === 'number') {\n    return Math.max(1, Math.min(5, level))\n  }\n\n  // If it's a string, convert it\n  if (typeof level === 'string') {\n    return levelMap[level.toLowerCase()] || 3\n  }\n\n  // Default to intermediate\n  return 3\n}\n\n// Watch for upload state changes\nwatch(uploading, (newValue) => {\n  if (!newValue) {\n    uploadProgress.value = 0\n  }\n})\n\nwatch(analyzingWithAI, (newValue) => {\n  if (!newValue) {\n    aiAnalysisProgress.value = 0\n  }\n})\n</script>\n\n<style scoped>\n/* Custom styles for CV tab */\n</style>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Current CV Section -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">CV Attuale</h3>\n        <button v-if=\"canEdit && !uploading\"\n                @click=\"triggerFileUpload\"\n                class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200\">\n          <svg class=\"w-4 h-4 inline mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          {{ user.profile?.current_cv_path ? 'Aggiorna CV' : 'Carica CV' }}\n        </button>\n      </div>\n\n      <!-- CV Upload Area -->\n      <div v-if=\"!user.profile?.current_cv_path && canEdit\"\n           @drop=\"handleDrop\"\n           @dragover.prevent\n           @dragenter.prevent\n           :class=\"[\n             'border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200',\n             isDragOver ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'\n           ]\">\n        <svg class=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n        </svg>\n        <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Carica il tuo CV</h3>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Trascina qui il file o <button @click=\"triggerFileUpload\" class=\"text-blue-600 hover:text-blue-500\">sfoglia</button>\n        </p>\n        <p class=\"mt-1 text-xs text-gray-400\">PDF, DOCX, DOC, TXT (max 10MB)</p>\n      </div>\n\n      <!-- Existing CV Display -->\n      <div v-else-if=\"user.profile?.current_cv_path\" class=\"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n        <svg class=\"w-8 h-8 text-red-600 mr-3\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div class=\"flex-1\">\n          <p class=\"text-sm font-medium text-gray-900 dark:text-white\">CV_{{ user.full_name }}.pdf</p>\n          <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n            Caricato il {{ formatDate(user.profile.cv_last_updated) }}\n          </p>\n          <div v-if=\"user.profile.cv_analysis_data\" class=\"mt-1\">\n            <span class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n              ✨ Analisi AI completata\n            </span>\n          </div>\n        </div>\n        <div class=\"flex space-x-2\">\n          <button @click=\"downloadCV\" \n                  class=\"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300\"\n                  title=\"Scarica CV\">\n            <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </button>\n          <button v-if=\"canEdit\" \n                  @click=\"deleteCV\" \n                  class=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n                  title=\"Elimina CV\">\n            <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z\" clip-rule=\"evenodd\"></path>\n              <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 112 0v4a1 1 0 11-2 0V7z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      <!-- Upload Progress -->\n      <div v-if=\"uploading || analyzingWithAI\" class=\"mt-4\">\n        <div class=\"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2\">\n          <div class=\"flex items-center\">\n            <svg v-if=\"analyzingWithAI\" class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            <span>{{ analyzingWithAI ? 'Analisi AI in corso...' : 'Caricamento in corso...' }}</span>\n          </div>\n          <span>{{ analyzingWithAI ? aiAnalysisProgress : uploadProgress }}%</span>\n        </div>\n        <div class=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n          <div :class=\"[\n                 'h-2 rounded-full transition-all duration-300',\n                 analyzingWithAI ? 'bg-purple-600' : 'bg-blue-600'\n               ]\"\n               :style=\"{ width: (analyzingWithAI ? aiAnalysisProgress : uploadProgress) + '%' }\"></div>\n        </div>\n      </div>\n    </div>\n\n    <!-- AI Analysis Section -->\n    <div v-if=\"cvAnalysis\" class=\"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6\">\n      <div class=\"flex items-center justify-between mb-4\">\n        <div class=\"flex items-center\">\n          <div class=\"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3\">\n            <svg class=\"w-4 h-4 text-purple-600 dark:text-purple-300\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <h3 class=\"text-lg font-medium text-purple-900 dark:text-purple-100\">\n            Analisi AI del CV\n          </h3>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <button v-if=\"canEdit && cvAnalysis.skills?.length > 0\"\n                  @click=\"showSkillsModal = true\"\n                  class=\"text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center\">\n            <svg class=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clip-rule=\"evenodd\"></path>\n            </svg>\n            Aggiungi Competenze\n          </button>\n        </div>\n      </div>\n\n      <!-- Professional Summary -->\n      <div v-if=\"cvAnalysis.summary\" class=\"mb-4\">\n        <h4 class=\"text-sm font-medium text-purple-800 dark:text-purple-200 mb-2\">Profilo Professionale</h4>\n        <p class=\"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700\">{{ cvAnalysis.summary }}</p>\n      </div>\n\n      <!-- Experience Years -->\n      <div v-if=\"cvAnalysis.experience_years\" class=\"mb-4\">\n        <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200\">\n          <svg class=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\" clip-rule=\"evenodd\"></path>\n          </svg>\n          {{ cvAnalysis.experience_years }} anni di esperienza\n        </span>\n      </div>\n\n      <!-- Extracted Skills -->\n      <div v-if=\"cvAnalysis.skills?.length > 0\">\n        <h4 class=\"text-sm font-medium text-purple-800 dark:text-purple-200 mb-3\">Competenze Estratte</h4>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-3 mb-3\">\n          <div v-for=\"(skill, index) in cvAnalysis.skills.slice(0, 8)\"\n               :key=\"index\"\n               class=\"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700\">\n            <div class=\"flex items-center space-x-2\">\n              <div class=\"w-2 h-2 bg-purple-500 rounded-full\"></div>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ skill.name }}</span>\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <span v-if=\"skill.category\" class=\"text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded\">\n                {{ skill.category }}\n              </span>\n              <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n                {{ getLevelLabel(skill.level) }}\n              </span>\n            </div>\n          </div>\n        </div>\n        <div v-if=\"cvAnalysis.skills.length > 8\" class=\"text-center\">\n          <span class=\"text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full\">\n            +{{ cvAnalysis.skills.length - 8 }} altre competenze disponibili\n          </span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Hidden file input -->\n    <input ref=\"fileInput\" \n           type=\"file\" \n           accept=\".pdf,.docx,.doc,.txt\" \n           @change=\"handleFileSelect\" \n           class=\"hidden\">\n\n    <!-- Skills Selection Modal -->\n    <div v-if=\"showSkillsModal\" \n         class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\"\n         @click=\"showSkillsModal = false\">\n      <div class=\"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800\"\n           @click.stop>\n        <div class=\"mt-3\">\n          <!-- Modal Header -->\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Seleziona Competenze da Aggiungere\n            </h3>\n            <button @click=\"showSkillsModal = false\"\n                    class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n              <svg class=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <!-- Skills List -->\n          <div class=\"max-h-96 overflow-y-auto mb-4\">\n            <div class=\"space-y-2\">\n              <div v-for=\"(skill, index) in cvAnalysis.skills\" \n                   :key=\"index\"\n                   class=\"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700\">\n                <input type=\"checkbox\" \n                       :id=\"`skill-${index}`\"\n                       v-model=\"selectedSkills\"\n                       :value=\"index\"\n                       class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\">\n                <label :for=\"`skill-${index}`\" class=\"ml-3 flex-1 cursor-pointer\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ skill.name }}</div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {{ skill.category }} • Livello {{ skill.level || 3 }}\n                    <span v-if=\"skill.years_experience\"> • {{ skill.years_experience }} anni</span>\n                  </div>\n                  <div v-if=\"skill.context\" class=\"text-xs text-gray-400 mt-1\">{{ skill.context }}</div>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          <!-- Modal Actions -->\n          <div class=\"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600\">\n            <button @click=\"showSkillsModal = false\"\n                    class=\"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200\">\n              Annulla\n            </button>\n            <button @click=\"addSelectedSkills\"\n                    :disabled=\"selectedSkills.length === 0 || addingSkills\"\n                    class=\"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200\">\n              {{ addingSkills ? 'Aggiungendo...' : `Aggiungi ${selectedSkills.length} competenze` }}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch } from 'vue'\nimport { useToast } from '@/composables/useToast'\nimport api from '@/utils/api'\n\n// Props\nconst props = defineProps({\n  user: {\n    type: Object,\n    required: true\n  },\n  canEdit: {\n    type: Boolean,\n    default: false\n  }\n})\n\n// Emits\nconst emit = defineEmits(['user-updated'])\n\n// Composables\nconst { success: showSuccess, error: showError, info: showInfo } = useToast()\n\n// Reactive state\nconst uploading = ref(false)\nconst analyzingWithAI = ref(false)\nconst uploadProgress = ref(0)\nconst isDragOver = ref(false)\nconst showSkillsModal = ref(false)\nconst selectedSkills = ref([])\nconst addingSkills = ref(false)\nconst fileInput = ref(null)\nconst aiAnalysisProgress = ref(0)\n\n// Computed\nconst cvAnalysis = computed(() => {\n  if (!props.user.profile?.cv_analysis_data) return null\n  try {\n    return JSON.parse(props.user.profile.cv_analysis_data)\n  } catch (e) {\n    console.error('Error parsing CV analysis data:', e)\n    return null\n  }\n})\n\n// Methods\nconst formatDate = (dateString) => {\n  if (!dateString) return ''\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nconst getLevelLabel = (level) => {\n  const levels = {\n    'beginner': 'Principiante',\n    'intermediate': 'Intermedio',\n    'advanced': 'Avanzato',\n    'expert': 'Esperto',\n    1: 'Principiante',\n    2: 'Base',\n    3: 'Intermedio',\n    4: 'Avanzato',\n    5: 'Esperto'\n  }\n  return levels[level] || 'Intermedio'\n}\n\n\n\nconst triggerFileUpload = () => {\n  fileInput.value?.click()\n}\n\nconst handleFileSelect = (event) => {\n  const file = event.target.files[0]\n  if (file) {\n    uploadCV(file)\n  }\n}\n\nconst handleDrop = (event) => {\n  event.preventDefault()\n  isDragOver.value = false\n  \n  const files = event.dataTransfer.files\n  if (files.length > 0) {\n    uploadCV(files[0])\n  }\n}\n\nconst uploadCV = async (file) => {\n  // Validate file\n  const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'text/plain']\n  if (!allowedTypes.includes(file.type)) {\n    showError('Formato file non supportato', 'Usa PDF, DOCX, DOC o TXT.')\n    return\n  }\n\n  if (file.size > 10 * 1024 * 1024) { // 10MB\n    showError('File troppo grande', 'Dimensione massima: 10MB.')\n    return\n  }\n\n  uploading.value = true\n  uploadProgress.value = 0\n\n  try {\n    // Step 1: Upload file\n    const formData = new FormData()\n    formData.append('cv_file', file)\n    formData.append('analyze_skills', 'true')\n    formData.append('auto_add_skills', 'false') // We'll let user choose\n\n    // Simulate upload progress\n    const uploadInterval = setInterval(() => {\n      if (uploadProgress.value < 90) {\n        uploadProgress.value += Math.random() * 15\n      }\n    }, 200)\n\n    const response = await api.post(`/api/personnel/users/${props.user.id}/cv/upload`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    })\n\n    clearInterval(uploadInterval)\n    uploadProgress.value = 100\n\n    const data = response.data\n\n    // Debug: log the response\n    console.log('Upload response:', data)\n\n    if (data.success) {\n      uploading.value = false\n\n      // Update user data first\n      emit('user-updated', data.data)\n\n      // Check if AI analysis is already present\n      if (data.data.profile?.cv_analysis_data) {\n        try {\n          const analysisData = JSON.parse(data.data.profile.cv_analysis_data)\n          console.log('Analysis data:', analysisData)\n\n          if (analysisData.skills && analysisData.skills.length > 0) {\n            showSuccess('CV caricato e analizzato con successo!', `Trovate ${analysisData.skills.length} competenze`)\n          } else {\n            showSuccess('CV caricato con successo!', 'Nessuna competenza estratta dall\\'AI')\n          }\n        } catch (e) {\n          console.error('Error parsing analysis data:', e)\n          showSuccess('CV caricato con successo!', 'Errore nel parsing dei dati AI')\n        }\n      } else {\n        showSuccess('CV caricato con successo!', 'Analisi AI non disponibile')\n        console.log('No AI analysis data found in response. Profile data:', data.data.profile)\n      }\n\n      // Reset file input\n      if (fileInput.value) {\n        fileInput.value.value = ''\n      }\n    } else {\n      throw new Error(data.message || 'Errore durante il caricamento')\n    }\n  } catch (error) {\n    console.error('Errore durante il caricamento del CV:', error)\n    showError('Errore durante il caricamento del CV', error.message)\n  } finally {\n    uploading.value = false\n    uploadProgress.value = 0\n  }\n}\n\n\n\nconst downloadCV = async () => {\n  try {\n    const response = await api.get(`/api/personnel/users/${props.user.id}/cv/download`, {\n      responseType: 'blob'\n    })\n\n    const blob = response.data\n    const url = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `CV_${props.user.full_name}.pdf`\n    document.body.appendChild(a)\n    a.click()\n    window.URL.revokeObjectURL(url)\n    document.body.removeChild(a)\n  } catch (error) {\n    console.error('Errore durante il download del CV:', error)\n    alert('Errore durante il download del CV: ' + error.message)\n  }\n}\n\nconst deleteCV = async () => {\n  if (!confirm('Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata.')) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/personnel/users/${props.user.id}/cv`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || ''\n      }\n    })\n\n    const data = await response.json()\n\n    if (data.success) {\n      // Update user data\n      emit('user-updated', data.data)\n      showSuccess('CV eliminato con successo')\n    } else {\n      throw new Error(data.message || 'Errore durante l\\'eliminazione')\n    }\n  } catch (error) {\n    console.error('Errore durante l\\'eliminazione del CV:', error)\n    showError('Errore durante l\\'eliminazione del CV', error.message)\n  }\n}\n\nconst addSelectedSkills = async () => {\n  if (selectedSkills.value.length === 0) return\n\n  addingSkills.value = true\n\n  try {\n    // Prepare skills data with proper level conversion\n    const skillsToAdd = selectedSkills.value.map(skillIndex => {\n      const skill = cvAnalysis.value.skills[skillIndex]\n      return {\n        ...skill,\n        level: convertLevelToNumber(skill.level)\n      }\n    })\n\n    const response = await fetch(`/api/personnel/users/${props.user.id}/skills/from-cv`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content') || ''\n      },\n      body: JSON.stringify({\n        selected_skills: skillsToAdd\n      })\n    })\n\n    const data = await response.json()\n\n    if (data.success) {\n      const { total_added, total_skipped } = data.data\n\n      if (total_added > 0) {\n        showSuccess(`Aggiunte ${total_added} competenze al profilo!`)\n      }\n\n      if (total_skipped > 0) {\n        showInfo(`${total_skipped} competenze erano già presenti nel profilo`)\n      }\n\n      // Close modal and reset selection\n      showSkillsModal.value = false\n      selectedSkills.value = []\n\n      // Emit event to refresh user data\n      emit('user-updated')\n    } else {\n      throw new Error(data.message || 'Errore durante l\\'aggiunta delle competenze')\n    }\n  } catch (error) {\n    console.error('Errore durante l\\'aggiunta delle competenze:', error)\n    showError('Errore durante l\\'aggiunta delle competenze', error.message)\n  } finally {\n    addingSkills.value = false\n  }\n}\n\nconst convertLevelToNumber = (level) => {\n  const levelMap = {\n    'beginner': 1,\n    'intermediate': 3,\n    'advanced': 4,\n    'expert': 5\n  }\n\n  // If it's already a number, return it\n  if (typeof level === 'number') {\n    return Math.max(1, Math.min(5, level))\n  }\n\n  // If it's a string, convert it\n  if (typeof level === 'string') {\n    return levelMap[level.toLowerCase()] || 3\n  }\n\n  // Default to intermediate\n  return 3\n}\n\n// Watch for upload state changes\nwatch(uploading, (newValue) => {\n  if (!newValue) {\n    uploadProgress.value = 0\n  }\n})\n\nwatch(analyzingWithAI, (newValue) => {\n  if (!newValue) {\n    aiAnalysisProgress.value = 0\n  }\n})\n</script>\n\n<style scoped>\n/* Custom styles for CV tab */\n</style>\n"}