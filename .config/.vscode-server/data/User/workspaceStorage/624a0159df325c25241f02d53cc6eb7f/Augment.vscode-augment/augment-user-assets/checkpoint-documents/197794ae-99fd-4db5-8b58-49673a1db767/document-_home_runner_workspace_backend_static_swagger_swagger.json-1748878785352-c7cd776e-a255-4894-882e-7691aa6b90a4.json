{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/static/swagger/swagger.json"}, "originalCode": "{\n  \"openapi\": \"3.0.0\",\n  \"info\": {\n    \"title\": \"DatPortal API\",\n    \"description\": \"API RESTful per DatPortal - Gestione Progetti, Task, Risorse e KPI\",\n    \"version\": \"1.0.0\",\n    \"contact\": {\n      \"name\": \"<PERSON>tV<PERSON><PERSON>\",\n      \"url\": \"https://datvinci.com\"\n    }\n  },\n  \"servers\": [\n    {\n      \"url\": \"/api\",\n      \"description\": \"Server di sviluppo\"\n    }\n  ],\n  \"tags\": [\n    {\n      \"name\": \"projects\",\n      \"description\": \"Operazioni sui progetti\"\n    },\n    {\n      \"name\": \"tasks\",\n      \"description\": \"Operazioni sui task\"\n    },\n    {\n      \"name\": \"resources\",\n      \"description\": \"Operazioni sulle risorse di progetto\"\n    },\n    {\n      \"name\": \"dependencies\",\n      \"description\": \"Operazioni sulle dipendenze tra task\"\n    },\n    {\n      \"name\": \"kpis\",\n      \"description\": \"Operazioni sui KPI generali\"\n    },\n    {\n      \"name\": \"project-kpis\",\n      \"description\": \"Operazioni sui KPI di progetto\"\n    },\n    {\n      \"name\": \"personnel\",\n      \"description\": \"Operazioni su personale, dipartimenti e competenze\"\n    },\n    {\n      \"name\": \"dashboard\",\n      \"description\": \"Operazioni per dashboard, statistiche e attività recenti\"\n    },\n    {\n      \"name\": \"auth\",\n      \"description\": \"Operazioni per autenticazione e gestione sessione utente\"\n    }\n  ],\n  \"paths\": {\n    \"/projects/\": {\n      \"get\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Ottiene la lista dei progetti\",\n        \"description\": \"Ottiene la lista dei progetti con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"status\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per stato del progetto\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"]\n            }\n          },\n          {\n            \"name\": \"client_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID cliente\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca nei nomi e nelle descrizioni dei progetti\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di progetti\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"projects\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/Project\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Crea un nuovo progetto\",\n        \"description\": \"Crea un nuovo progetto con i dati forniti.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"name\"],\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del progetto\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del progetto\"\n                  },\n                  \"client_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del cliente associato\"\n                  },\n                  \"start_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di inizio (YYYY-MM-DD)\"\n                  },\n                  \"end_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di fine (YYYY-MM-DD)\"\n                  },\n                  \"status\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"],\n                    \"default\": \"planning\",\n                    \"description\": \"Stato del progetto\"\n                  },\n                  \"budget\": {\n                    \"type\": \"number\",\n                    \"description\": \"Budget del progetto\"\n                  },\n                  \"team_members\": {\n                    \"type\": \"array\",\n                    \"items\": {\n                      \"type\": \"integer\"\n                    },\n                    \"description\": \"Lista di ID utenti da assegnare al progetto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"Progetto creato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project\": {\n                          \"$ref\": \"#/components/schemas/Project\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Progetto creato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Nome del progetto obbligatorio\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/tasks/\": {\n      \"get\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Ottiene la lista dei task\",\n        \"description\": \"Ottiene la lista dei task con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"project_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"status\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per stato del task\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"]\n            }\n          },\n          {\n            \"name\": \"priority\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per priorità del task\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"]\n            }\n          },\n          {\n            \"name\": \"assignee_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID dell'assegnatario\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca nei nomi e nelle descrizioni dei task\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di task\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"tasks\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/Task\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Crea un nuovo task\",\n        \"description\": \"Crea un nuovo task con i dati forniti.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"name\", \"project_id\"],\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del task\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del task\"\n                  },\n                  \"project_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del progetto associato\"\n                  },\n                  \"assignee_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID dell'utente assegnato al task\"\n                  },\n                  \"status\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n                    \"default\": \"todo\",\n                    \"description\": \"Stato del task\"\n                  },\n                  \"priority\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"],\n                    \"default\": \"medium\",\n                    \"description\": \"Priorità del task\"\n                  },\n                  \"due_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di scadenza (YYYY-MM-DD)\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"Task creato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"task\": {\n                          \"$ref\": \"#/components/schemas/Task\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Task creato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Nome del task e ID progetto obbligatori\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/resources/\": {\n      \"get\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Ottiene la lista delle risorse dei progetti\",\n        \"description\": \"Ottiene la lista delle risorse dei progetti con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"project_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"user_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID utente\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di risorse\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"resources\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/ProjectResource\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Assegna una nuova risorsa a un progetto\",\n        \"description\": \"Assegna una nuova risorsa a un progetto.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"project_id\", \"user_id\"],\n                \"properties\": {\n                  \"project_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del progetto\"\n                  },\n                  \"user_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID dell'utente da assegnare\"\n                  },\n                  \"allocation_percentage\": {\n                    \"type\": \"integer\",\n                    \"description\": \"Percentuale di allocazione (1-100)\",\n                    \"default\": 100\n                  },\n                  \"role\": {\n                    \"type\": \"string\",\n                    \"description\": \"Ruolo dell'utente nel progetto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"Risorsa assegnata con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"resource\": {\n                          \"$ref\": \"#/components/schemas/ProjectResource\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Risorsa assegnata con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"ID progetto e ID utente obbligatori\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/task-dependencies/\": {\n      \"get\": {\n        \"tags\": [\"task-dependencies\"],\n        \"summary\": \"Ottiene la lista delle dipendenze tra task\",\n        \"description\": \"Ottiene la lista delle dipendenze tra task con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"task_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID del task\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"depends_on_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID del task dipendente\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"project_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID del progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di dipendenze tra task\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"dependencies\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/TaskDependency\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"task-dependencies\"],\n        \"summary\": \"Crea una nuova dipendenza tra task\",\n        \"description\": \"Crea una nuova dipendenza tra task.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"task_id\", \"depends_on_id\"],\n                \"properties\": {\n                  \"task_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del task che dipende\"\n                  },\n                  \"depends_on_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del task da cui dipende\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"Dipendenza creata con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"dependency\": {\n                          \"$ref\": \"#/components/schemas/TaskDependency\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dipendenza creata con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"ID task e ID task dipendente obbligatori\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/kpis/\": {\n      \"get\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Ottiene la lista dei KPI\",\n        \"description\": \"Ottiene la lista dei KPI con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"category\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per categoria KPI\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"frequency\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per frequenza KPI\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"daily\", \"weekly\", \"monthly\", \"quarterly\", \"annually\"]\n            }\n          },\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca nei nomi e descrizioni dei KPI\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di KPI\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpis\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/KPI\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Crea un nuovo KPI\",\n        \"description\": \"Crea un nuovo KPI con i dati forniti.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"name\"],\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del KPI\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del KPI\"\n                  },\n                  \"category\": {\n                    \"type\": \"string\",\n                    \"description\": \"Categoria del KPI\"\n                  },\n                  \"target_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore target del KPI\"\n                  },\n                  \"current_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore attuale del KPI\",\n                    \"default\": 0\n                  },\n                  \"unit\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unità di misura\"\n                  },\n                  \"frequency\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"daily\", \"weekly\", \"monthly\", \"quarterly\", \"annually\"],\n                    \"description\": \"Frequenza di misurazione\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"KPI creato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpi\": {\n                          \"$ref\": \"#/components/schemas/KPI\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI creato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Nome del KPI obbligatorio\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/kpis/{kpi_id}\": {\n      \"get\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Ottiene i dettagli di un KPI\",\n        \"description\": \"Ottiene i dettagli di un KPI specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli del KPI\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpi\": {\n                          \"$ref\": \"#/components/schemas/KPI\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Aggiorna un KPI esistente\",\n        \"description\": \"Aggiorna un KPI esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del KPI\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del KPI\"\n                  },\n                  \"category\": {\n                    \"type\": \"string\",\n                    \"description\": \"Categoria del KPI\"\n                  },\n                  \"target_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore target del KPI\"\n                  },\n                  \"current_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore attuale del KPI\"\n                  },\n                  \"unit\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unità di misura\"\n                  },\n                  \"frequency\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"daily\", \"weekly\", \"monthly\", \"quarterly\", \"annually\"],\n                    \"description\": \"Frequenza di misurazione\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"KPI aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpi\": {\n                          \"$ref\": \"#/components/schemas/KPI\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI aggiornato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Elimina un KPI\",\n        \"description\": \"Elimina un KPI.\",\n        \"parameters\": [\n          {\n            \"name\": \"kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"KPI eliminato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI eliminato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"KPI in uso, impossibile eliminare\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Impossibile eliminare KPI: è utilizzato in progetti\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/project-kpis/\": {\n      \"get\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Ottiene la lista dei KPI di progetto\",\n        \"description\": \"Ottiene la lista dei KPI di progetto con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"project_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"kpi_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID KPI\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di KPI di progetto\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project_kpis\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/ProjectKPI\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Crea un nuovo KPI di progetto\",\n        \"description\": \"Crea un nuovo KPI di progetto.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"project_id\", \"kpi_id\"],\n                \"properties\": {\n                  \"project_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del progetto\"\n                  },\n                  \"kpi_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del KPI\"\n                  },\n                  \"target_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore target del KPI\"\n                  },\n                  \"current_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore attuale del KPI\",\n                    \"default\": 0\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"KPI di progetto creato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project_kpi\": {\n                          \"$ref\": \"#/components/schemas/ProjectKPI\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI di progetto creato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"ID progetto e ID KPI obbligatori\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/project-kpis/{project_kpi_id}\": {\n      \"get\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Ottiene i dettagli di un KPI di progetto\",\n        \"description\": \"Ottiene i dettagli di un KPI di progetto specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI di progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli del KPI di progetto\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project_kpi\": {\n                          \"$ref\": \"#/components/schemas/ProjectKPI\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Aggiorna un KPI di progetto esistente\",\n        \"description\": \"Aggiorna un KPI di progetto esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI di progetto da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"target_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore target del KPI\"\n                  },\n                  \"current_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore attuale del KPI\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"KPI di progetto aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project_kpi\": {\n                          \"$ref\": \"#/components/schemas/ProjectKPI\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI di progetto aggiornato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Elimina un KPI di progetto\",\n        \"description\": \"Elimina un KPI di progetto.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI di progetto da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"KPI di progetto eliminato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI di progetto eliminato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/task-dependencies/{dependency_id}\": {\n      \"get\": {\n        \"tags\": [\"task-dependencies\"],\n        \"summary\": \"Ottiene i dettagli di una dipendenza\",\n        \"description\": \"Ottiene i dettagli di una dipendenza specifica.\",\n        \"parameters\": [\n          {\n            \"name\": \"dependency_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della dipendenza\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli della dipendenza\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"dependency\": {\n                          \"$ref\": \"#/components/schemas/TaskDependency\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"task-dependencies\"],\n        \"summary\": \"Elimina una dipendenza\",\n        \"description\": \"Elimina una dipendenza tra task.\",\n        \"parameters\": [\n          {\n            \"name\": \"dependency_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della dipendenza da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dipendenza eliminata con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dipendenza eliminata con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/resources/{resource_id}\": {\n      \"get\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Ottiene i dettagli di una risorsa\",\n        \"description\": \"Ottiene i dettagli di una risorsa specifica.\",\n        \"parameters\": [\n          {\n            \"name\": \"resource_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della risorsa\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli della risorsa\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"resource\": {\n                          \"$ref\": \"#/components/schemas/ProjectResource\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Aggiorna un'assegnazione di risorsa esistente\",\n        \"description\": \"Aggiorna un'assegnazione di risorsa esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"resource_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della risorsa da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"allocation_percentage\": {\n                    \"type\": \"integer\",\n                    \"description\": \"Percentuale di allocazione (1-100)\"\n                  },\n                  \"role\": {\n                    \"type\": \"string\",\n                    \"description\": \"Ruolo dell'utente nel progetto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Risorsa aggiornata con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"resource\": {\n                          \"$ref\": \"#/components/schemas/ProjectResource\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Risorsa aggiornata con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Rimuove un'assegnazione di risorsa\",\n        \"description\": \"Rimuove un'assegnazione di risorsa.\",\n        \"parameters\": [\n          {\n            \"name\": \"resource_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della risorsa da rimuovere\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Risorsa rimossa con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Risorsa rimossa con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/tasks/status\": {\n      \"patch\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Aggiorna lo stato di più task contemporaneamente\",\n        \"description\": \"Aggiorna lo stato di più task contemporaneamente.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"tasks\"],\n                \"properties\": {\n                  \"tasks\": {\n                    \"type\": \"array\",\n                    \"items\": {\n                      \"type\": \"object\",\n                      \"required\": [\"id\", \"status\"],\n                      \"properties\": {\n                        \"id\": {\n                          \"type\": \"integer\",\n                          \"description\": \"ID del task\"\n                        },\n                        \"status\": {\n                          \"type\": \"string\",\n                          \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n                          \"description\": \"Nuovo stato del task\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Stato dei task aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"results\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"type\": \"object\",\n                            \"properties\": {\n                              \"task_id\": {\n                                \"type\": \"integer\",\n                                \"example\": 1\n                              },\n                              \"success\": {\n                                \"type\": \"boolean\",\n                                \"example\": true\n                              },\n                              \"old_status\": {\n                                \"type\": \"string\",\n                                \"example\": \"todo\"\n                              },\n                              \"new_status\": {\n                                \"type\": \"string\",\n                                \"example\": \"in-progress\"\n                              },\n                              \"message\": {\n                                \"type\": \"string\",\n                                \"example\": \"Stato aggiornato da 'todo' a 'in-progress'\"\n                              }\n                            }\n                          }\n                        },\n                        \"message\": {\n                          \"type\": \"string\",\n                          \"example\": \"Aggiornamento completato: 3 task aggiornati, 0 falliti\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi: è richiesta una lista di task\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/tasks/{task_id}\": {\n      \"get\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Ottiene i dettagli di un task\",\n        \"description\": \"Ottiene i dettagli di un task specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"task_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del task\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli del task\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"task\": {\n                          \"$ref\": \"#/components/schemas/Task\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Aggiorna un task esistente\",\n        \"description\": \"Aggiorna un task esistente con i dati forniti.\",\n        \"parameters\": [\n          {\n            \"name\": \"task_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del task da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del task\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del task\"\n                  },\n                  \"project_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del progetto associato\"\n                  },\n                  \"assignee_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID dell'utente assegnato al task\"\n                  },\n                  \"status\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n                    \"description\": \"Stato del task\"\n                  },\n                  \"priority\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"],\n                    \"description\": \"Priorità del task\"\n                  },\n                  \"due_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di scadenza (YYYY-MM-DD)\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Task aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"task\": {\n                          \"$ref\": \"#/components/schemas/Task\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Task aggiornato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Elimina un task esistente\",\n        \"description\": \"Elimina un task esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"task_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del task da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Task eliminato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Task eliminato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/projects/batch\": {\n      \"post\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Esegue operazioni batch sui progetti\",\n        \"description\": \"Esegue operazioni batch sui progetti.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"operations\"],\n                \"properties\": {\n                  \"operations\": {\n                    \"type\": \"array\",\n                    \"items\": {\n                      \"type\": \"object\",\n                      \"required\": [\"operation\"],\n                      \"properties\": {\n                        \"operation\": {\n                          \"type\": \"string\",\n                          \"enum\": [\"create\", \"update\", \"delete\"],\n                          \"description\": \"Tipo di operazione da eseguire\"\n                        },\n                        \"project_id\": {\n                          \"type\": \"integer\",\n                          \"description\": \"ID del progetto (richiesto per update e delete)\"\n                        },\n                        \"data\": {\n                          \"type\": \"object\",\n                          \"description\": \"Dati del progetto (richiesto per create e update)\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Operazioni batch eseguite con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"results\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"type\": \"object\",\n                            \"properties\": {\n                              \"operation\": {\n                                \"type\": \"string\",\n                                \"example\": \"create\"\n                              },\n                              \"success\": {\n                                \"type\": \"boolean\",\n                                \"example\": true\n                              },\n                              \"project_id\": {\n                                \"type\": \"integer\",\n                                \"example\": 1\n                              },\n                              \"message\": {\n                                \"type\": \"string\",\n                                \"example\": \"Progetto creato con successo\"\n                              }\n                            }\n                          }\n                        },\n                        \"message\": {\n                          \"type\": \"string\",\n                          \"example\": \"Operazioni batch eseguite con successo\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/projects/{project_id}\": {\n      \"get\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Ottiene i dettagli di un progetto\",\n        \"description\": \"Ottiene i dettagli di un progetto specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli del progetto\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project\": {\n                          \"$ref\": \"#/components/schemas/Project\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Aggiorna un progetto esistente\",\n        \"description\": \"Aggiorna un progetto esistente con i dati forniti.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del progetto da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del progetto\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del progetto\"\n                  },\n                  \"client_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del cliente associato\"\n                  },\n                  \"start_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di inizio (YYYY-MM-DD)\"\n                  },\n                  \"end_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di fine (YYYY-MM-DD)\"\n                  },\n                  \"status\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"],\n                    \"description\": \"Stato del progetto\"\n                  },\n                  \"budget\": {\n                    \"type\": \"number\",\n                    \"description\": \"Budget del progetto\"\n                  },\n                  \"team_members\": {\n                    \"type\": \"array\",\n                    \"items\": {\n                      \"type\": \"integer\"\n                    },\n                    \"description\": \"Lista di ID utenti da assegnare al progetto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Progetto aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project\": {\n                          \"$ref\": \"#/components/schemas/Project\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Progetto aggiornato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Elimina un progetto esistente\",\n        \"description\": \"Elimina un progetto esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del progetto da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Progetto eliminato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Progetto eliminato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/personnel/users\": {\n      \"get\": {\n        \"tags\": [\"personnel\"],\n        \"summary\": \"Ottiene la lista degli utenti\",\n        \"description\": \"Ottiene la lista degli utenti con supporto per filtri, paginazione e ricerca.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca in nome, cognome, username, email\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"department_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID dipartimento\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"role\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ruolo utente\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"admin\", \"manager\", \"employee\", \"human_resources\"]\n            }\n          },\n          {\n            \"name\": \"is_active\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per stato attivo\",\n            \"schema\": {\n              \"type\": \"boolean\"\n            }\n          },\n          {\n            \"name\": \"skills\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per competenze (ID separati da virgola)\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"order_by\",\n            \"in\": \"query\",\n            \"description\": \"Campo per ordinamento\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"default\": \"last_name\"\n            }\n          },\n          {\n            \"name\": \"order_dir\",\n            \"in\": \"query\",\n            \"description\": \"Direzione ordinamento\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"asc\", \"desc\"],\n              \"default\": \"asc\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista utenti\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"users\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/UserSummary\"\n                          }\n                        },\n                        \"pagination\": {\n                          \"$ref\": \"#/components/schemas/Pagination\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 25 users\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/personnel/users/{user_id}\": {\n      \"get\": {\n        \"tags\": [\"personnel\"],\n        \"summary\": \"Ottiene i dettagli di un utente\",\n        \"description\": \"Ottiene informazioni dettagliate su un utente specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"user_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID dell'utente\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli utente\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"user\": {\n                          \"$ref\": \"#/components/schemas/UserDetail\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved user Mario Rossi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/personnel/departments\": {\n      \"get\": {\n        \"tags\": [\"personnel\"],\n        \"summary\": \"Ottiene la lista dei dipartimenti\",\n        \"description\": \"Ottiene la lista dei dipartimenti con dati per l'organigramma.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista dipartimenti\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"departments\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/Department\"\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 5 departments\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/personnel/skills\": {\n      \"get\": {\n        \"tags\": [\"personnel\"],\n        \"summary\": \"Ottiene la lista delle competenze\",\n        \"description\": \"Ottiene la lista delle competenze con statistiche di utilizzo.\",\n        \"parameters\": [\n          {\n            \"name\": \"category\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per categoria competenza\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca in nome e descrizione competenze\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista competenze\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"skills\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/Skill\"\n                          }\n                        },\n                        \"categories\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"type\": \"string\"\n                          },\n                          \"description\": \"Lista delle categorie disponibili\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 42 skills\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/dashboard/stats\": {\n      \"get\": {\n        \"tags\": [\"dashboard\"],\n        \"summary\": \"Ottiene le statistiche della dashboard\",\n        \"description\": \"Ottiene statistiche aggregate per la dashboard principale.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Statistiche dashboard\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"projects\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"active\": {\"type\": \"integer\"},\n                            \"total\": {\"type\": \"integer\"}\n                          }\n                        },\n                        \"tasks\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"total\": {\"type\": \"integer\"},\n                            \"pending\": {\"type\": \"integer\"},\n                            \"completed\": {\"type\": \"integer\"},\n                            \"overdue\": {\"type\": \"integer\"}\n                          }\n                        },\n                        \"team\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"users\": {\"type\": \"integer\"},\n                            \"departments\": {\"type\": \"integer\"},\n                            \"clients\": {\"type\": \"integer\"}\n                          }\n                        },\n                        \"activities\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"recent_timesheets\": {\"type\": \"integer\"},\n                            \"unread_notifications\": {\"type\": \"integer\"}\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dashboard statistics retrieved successfully\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/dashboard/recent-activities\": {\n      \"get\": {\n        \"tags\": [\"dashboard\"],\n        \"summary\": \"Ottiene le attività recenti\",\n        \"description\": \"Ottiene la lista delle attività recenti per la dashboard.\",\n        \"parameters\": [\n          {\n            \"name\": \"limit\",\n            \"in\": \"query\",\n            \"description\": \"Numero massimo di attività da restituire\",\n            \"schema\": {\n              \"type\": \"integer\",\n              \"default\": 10,\n              \"maximum\": 50\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista attività recenti\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"activities\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/DashboardActivity\"\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 10 recent activities\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/dashboard/upcoming-tasks\": {\n      \"get\": {\n        \"tags\": [\"dashboard\"],\n        \"summary\": \"Ottiene i task in scadenza\",\n        \"description\": \"Ottiene la lista dei task in scadenza per la dashboard.\",\n        \"parameters\": [\n          {\n            \"name\": \"days\",\n            \"in\": \"query\",\n            \"description\": \"Numero di giorni da considerare per le scadenze\",\n            \"schema\": {\n              \"type\": \"integer\",\n              \"default\": 7\n            }\n          },\n          {\n            \"name\": \"limit\",\n            \"in\": \"query\",\n            \"description\": \"Numero massimo di task da restituire\",\n            \"schema\": {\n              \"type\": \"integer\",\n              \"default\": 10\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista task in scadenza\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"tasks\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/UpcomingTask\"\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 5 upcoming tasks\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/dashboard/kpis\": {\n      \"get\": {\n        \"tags\": [\"dashboard\"],\n        \"summary\": \"Ottiene i KPI per la dashboard\",\n        \"description\": \"Ottiene i KPI principali per la dashboard.\",\n        \"parameters\": [\n          {\n            \"name\": \"category\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per categoria KPI\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"limit\",\n            \"in\": \"query\",\n            \"description\": \"Numero massimo di KPI da restituire\",\n            \"schema\": {\n              \"type\": \"integer\",\n              \"default\": 6,\n              \"maximum\": 20\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista KPI dashboard\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpis\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/DashboardKPI\"\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 6 KPIs\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/auth/me\": {\n      \"get\": {\n        \"tags\": [\"auth\"],\n        \"summary\": \"Ottiene i dati dell'utente corrente\",\n        \"description\": \"Ottiene informazioni complete dell'utente corrente inclusi permessi e preferenze.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dati utente corrente\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"user\": {\n                          \"$ref\": \"#/components/schemas/CurrentUser\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Current user data retrieved successfully\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/auth/check-session\": {\n      \"get\": {\n        \"tags\": [\"auth\"],\n        \"summary\": \"Verifica validità sessione\",\n        \"description\": \"Verifica se la sessione corrente è valida.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Stato sessione\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"valid\": {\n                          \"type\": \"boolean\",\n                          \"example\": true\n                        },\n                        \"user_id\": {\n                          \"type\": \"integer\",\n                          \"example\": 1\n                        },\n                        \"username\": {\n                          \"type\": \"string\",\n                          \"example\": \"mario.rossi\"\n                        },\n                        \"role\": {\n                          \"type\": \"string\",\n                          \"example\": \"admin\"\n                        },\n                        \"last_activity\": {\n                          \"type\": \"string\",\n                          \"format\": \"date-time\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Session is valid\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/auth/preferences\": {\n      \"get\": {\n        \"tags\": [\"auth\"],\n        \"summary\": \"Ottiene preferenze utente\",\n        \"description\": \"Ottiene le preferenze dell'utente corrente.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Preferenze utente\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"preferences\": {\n                          \"$ref\": \"#/components/schemas/UserPreferences\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"User preferences retrieved successfully\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"auth\"],\n        \"summary\": \"Aggiorna preferenze utente\",\n        \"description\": \"Aggiorna le preferenze dell'utente corrente.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"dark_mode\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"Modalità scura\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Preferenze aggiornate\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"preferences\": {\n                          \"$ref\": \"#/components/schemas/UserPreferences\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"User preferences updated successfully\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    }\n  },\n  \"components\": {\n    \"responses\": {\n      \"NotFound\": {\n        \"description\": \"Risorsa non trovata\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"success\": {\n                  \"type\": \"boolean\",\n                  \"example\": false\n                },\n                \"message\": {\n                  \"type\": \"string\",\n                  \"example\": \"Risorsa non trovata\"\n                }\n              }\n            }\n          }\n        }\n      },\n      \"Unauthorized\": {\n        \"description\": \"Autenticazione richiesta\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"success\": {\n                  \"type\": \"boolean\",\n                  \"example\": false\n                },\n                \"message\": {\n                  \"type\": \"string\",\n                  \"example\": \"Autenticazione richiesta\"\n                }\n              }\n            }\n          }\n        }\n      },\n      \"Forbidden\": {\n        \"description\": \"Accesso negato\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"success\": {\n                  \"type\": \"boolean\",\n                  \"example\": false\n                },\n                \"message\": {\n                  \"type\": \"string\",\n                  \"example\": \"Non hai i permessi necessari per accedere a questa risorsa\"\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    \"schemas\": {\n      \"Project\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"description\": \"ID univoco del progetto\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"Descrizione del progetto\"\n          },\n          \"client_id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"nullable\": true,\n            \"description\": \"ID del cliente associato al progetto\"\n          },\n          \"start_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di inizio del progetto (YYYY-MM-DD)\"\n          },\n          \"end_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di fine del progetto (YYYY-MM-DD)\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"],\n            \"description\": \"Stato del progetto\"\n          },\n          \"budget\": {\n            \"type\": \"number\",\n            \"format\": \"float\",\n            \"nullable\": true,\n            \"description\": \"Budget del progetto\"\n          },\n          \"expenses\": {\n            \"type\": \"number\",\n            \"format\": \"float\",\n            \"description\": \"Spese attuali del progetto\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data e ora di creazione del progetto\"\n          },\n          \"updated_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data e ora dell'ultimo aggiornamento del progetto\"\n          }\n        },\n        \"required\": [\"name\"]\n      },\n      \"Pagination\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"page\": {\n            \"type\": \"integer\",\n            \"description\": \"Pagina corrente\"\n          },\n          \"per_page\": {\n            \"type\": \"integer\",\n            \"description\": \"Elementi per pagina\"\n          },\n          \"total\": {\n            \"type\": \"integer\",\n            \"description\": \"Numero totale di elementi\"\n          },\n          \"pages\": {\n            \"type\": \"integer\",\n            \"description\": \"Numero totale di pagine\"\n          },\n          \"has_next\": {\n            \"type\": \"boolean\",\n            \"description\": \"Indica se esiste una pagina successiva\"\n          },\n          \"has_prev\": {\n            \"type\": \"boolean\",\n            \"description\": \"Indica se esiste una pagina precedente\"\n          }\n        }\n      },\n      \"KPI\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del KPI\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del KPI\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"Descrizione del KPI\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria del KPI\"\n          },\n          \"target_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore target del KPI\"\n          },\n          \"current_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore attuale del KPI\"\n          },\n          \"unit\": {\n            \"type\": \"string\",\n            \"description\": \"Unità di misura\"\n          },\n          \"frequency\": {\n            \"type\": \"string\",\n            \"enum\": [\"daily\", \"weekly\", \"monthly\", \"quarterly\", \"annually\"],\n            \"description\": \"Frequenza di misurazione\"\n          },\n          \"progress\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale di completamento del KPI\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data di creazione\"\n          },\n          \"updated_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data di ultimo aggiornamento\"\n          }\n        }\n      },\n      \"ProjectKPI\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del KPI di progetto\"\n          },\n          \"project_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del progetto\"\n          },\n          \"kpi_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del KPI\"\n          },\n          \"target_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore target del KPI\"\n          },\n          \"current_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore attuale del KPI\"\n          },\n          \"project_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"kpi_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del KPI\"\n          },\n          \"kpi_unit\": {\n            \"type\": \"string\",\n            \"description\": \"Unità di misura del KPI\"\n          },\n          \"progress\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale di completamento del KPI\"\n          }\n        }\n      },\n      \"TaskDependency\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della dipendenza\"\n          },\n          \"task_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del task che dipende\"\n          },\n          \"depends_on_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del task da cui dipende\"\n          },\n          \"task_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del task che dipende\"\n          },\n          \"depends_on_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del task da cui dipende\"\n          }\n        }\n      },\n      \"ProjectResource\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della risorsa\"\n          },\n          \"project_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del progetto\"\n          },\n          \"user_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'utente\"\n          },\n          \"allocation_percentage\": {\n            \"type\": \"integer\",\n            \"description\": \"Percentuale di allocazione (1-100)\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"description\": \"Ruolo dell'utente nel progetto\"\n          },\n          \"project_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"user_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome dell'utente\"\n          }\n        }\n      },\n      \"Task\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"description\": \"ID univoco del task\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del task\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione del task\"\n          },\n          \"project_id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"description\": \"ID del progetto a cui appartiene il task\"\n          },\n          \"assignee_id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"nullable\": true,\n            \"description\": \"ID dell'utente assegnato al task\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n            \"description\": \"Stato del task\"\n          },\n          \"priority\": {\n            \"type\": \"string\",\n            \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"],\n            \"description\": \"Priorità del task\"\n          },\n          \"due_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di scadenza del task (YYYY-MM-DD)\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data e ora di creazione del task\"\n          },\n          \"updated_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data e ora dell'ultimo aggiornamento del task\"\n          }\n        },\n        \"required\": [\"name\", \"project_id\"]\n      },\n      \"Error\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"success\": {\n            \"type\": \"boolean\",\n            \"example\": false\n          },\n          \"message\": {\n            \"type\": \"string\"\n          }\n        }\n      },\n      \"UserSummary\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'utente\"\n          },\n          \"username\": {\n            \"type\": \"string\",\n            \"description\": \"Nome utente\"\n          },\n          \"email\": {\n            \"type\": \"string\",\n            \"description\": \"Email dell'utente\"\n          },\n          \"first_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome\"\n          },\n          \"last_name\": {\n            \"type\": \"string\",\n            \"description\": \"Cognome\"\n          },\n          \"full_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome completo\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"enum\": [\"admin\", \"manager\", \"employee\", \"human_resources\"],\n            \"description\": \"Ruolo dell'utente\"\n          },\n          \"department_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID del dipartimento\"\n          },\n          \"department_name\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Nome del dipartimento\"\n          },\n          \"position\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Posizione lavorativa\"\n          },\n          \"hire_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di assunzione\"\n          },\n          \"phone\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Numero di telefono\"\n          },\n          \"profile_image\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"URL immagine profilo\"\n          },\n          \"is_active\": {\n            \"type\": \"boolean\",\n            \"description\": \"Stato attivo dell'utente\"\n          },\n          \"last_login\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Ultimo accesso\"\n          },\n          \"skills\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserSkill\"\n            },\n            \"description\": \"Competenze dell'utente\"\n          },\n          \"profile_completion\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale completamento profilo\"\n          }\n        }\n      },\n      \"UserDetail\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'utente\"\n          },\n          \"username\": {\n            \"type\": \"string\",\n            \"description\": \"Nome utente\"\n          },\n          \"email\": {\n            \"type\": \"string\",\n            \"description\": \"Email dell'utente\"\n          },\n          \"first_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome\"\n          },\n          \"last_name\": {\n            \"type\": \"string\",\n            \"description\": \"Cognome\"\n          },\n          \"full_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome completo\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"enum\": [\"admin\", \"manager\", \"employee\", \"human_resources\"],\n            \"description\": \"Ruolo dell'utente\"\n          },\n          \"department_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID del dipartimento\"\n          },\n          \"department\": {\n            \"$ref\": \"#/components/schemas/Department\",\n            \"nullable\": true,\n            \"description\": \"Dettagli del dipartimento\"\n          },\n          \"position\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Posizione lavorativa\"\n          },\n          \"hire_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di assunzione\"\n          },\n          \"phone\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Numero di telefono\"\n          },\n          \"profile_image\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"URL immagine profilo\"\n          },\n          \"bio\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Biografia dell'utente\"\n          },\n          \"is_active\": {\n            \"type\": \"boolean\",\n            \"description\": \"Stato attivo dell'utente\"\n          },\n          \"dark_mode\": {\n            \"type\": \"boolean\",\n            \"description\": \"Preferenza modalità scura\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data di creazione\"\n          },\n          \"last_login\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Ultimo accesso\"\n          },\n          \"skills\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserSkillDetail\"\n            },\n            \"description\": \"Competenze dettagliate dell'utente\"\n          },\n          \"projects\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserProject\"\n            },\n            \"description\": \"Progetti dell'utente\"\n          },\n          \"profile\": {\n            \"$ref\": \"#/components/schemas/UserProfile\",\n            \"nullable\": true,\n            \"description\": \"Profilo esteso dell'utente\"\n          }\n        }\n      },\n      \"Department\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del dipartimento\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del dipartimento\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione del dipartimento\"\n          },\n          \"manager_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID del manager del dipartimento\"\n          },\n          \"manager\": {\n            \"$ref\": \"#/components/schemas/UserSummary\",\n            \"nullable\": true,\n            \"description\": \"Manager del dipartimento\"\n          },\n          \"user_count\": {\n            \"type\": \"integer\",\n            \"description\": \"Numero di utenti nel dipartimento\"\n          },\n          \"users\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserSummary\"\n            },\n            \"description\": \"Utenti del dipartimento\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data di creazione\"\n          }\n        }\n      },\n      \"Skill\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della competenza\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome della competenza\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria della competenza\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione della competenza\"\n          },\n          \"user_count\": {\n            \"type\": \"integer\",\n            \"description\": \"Numero di utenti con questa competenza\"\n          },\n          \"users\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserSkill\"\n            },\n            \"description\": \"Utenti con questa competenza\"\n          }\n        }\n      },\n      \"UserSkill\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della competenza\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome della competenza\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria della competenza\"\n          },\n          \"proficiency_level\": {\n            \"type\": \"integer\",\n            \"minimum\": 1,\n            \"maximum\": 5,\n            \"description\": \"Livello di competenza (1-5)\"\n          },\n          \"years_experience\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"Anni di esperienza\"\n          }\n        }\n      },\n      \"UserSkillDetail\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della competenza\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome della competenza\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria della competenza\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione della competenza\"\n          },\n          \"proficiency_level\": {\n            \"type\": \"integer\",\n            \"minimum\": 1,\n            \"maximum\": 5,\n            \"description\": \"Livello di competenza (1-5)\"\n          },\n          \"years_experience\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"Anni di esperienza\"\n          },\n          \"certified\": {\n            \"type\": \"boolean\",\n            \"description\": \"Certificazione ottenuta\"\n          },\n          \"last_used\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data certificazione\"\n          }\n        }\n      },\n      \"UserProject\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del progetto\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"],\n            \"description\": \"Stato del progetto\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"description\": \"Ruolo dell'utente nel progetto\"\n          }\n        }\n      },\n      \"UserProfile\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"emergency_contact_name\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Nome contatto di emergenza\"\n          },\n          \"emergency_contact_phone\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Telefono contatto di emergenza\"\n          },\n          \"address\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Indirizzo\"\n          },\n          \"profile_completion\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale completamento profilo\"\n          },\n          \"notes\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Note (solo per admin/HR)\"\n          }\n        }\n      },\n      \"DashboardActivity\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"type\": {\n            \"type\": \"string\",\n            \"enum\": [\"task\", \"timesheet\", \"event\"],\n            \"description\": \"Tipo di attività\"\n          },\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'attività\"\n          },\n          \"title\": {\n            \"type\": \"string\",\n            \"description\": \"Titolo dell'attività\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"Descrizione dell'attività\"\n          },\n          \"timestamp\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Timestamp dell'attività\"\n          },\n          \"link\": {\n            \"type\": \"string\",\n            \"description\": \"Link per visualizzare l'attività\"\n          }\n        }\n      },\n      \"UpcomingTask\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del task\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del task\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione del task\"\n          },\n          \"project_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del progetto\"\n          },\n          \"project_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"assignee_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID dell'assegnatario\"\n          },\n          \"assignee_name\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Nome dell'assegnatario\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n            \"description\": \"Stato del task\"\n          },\n          \"priority\": {\n            \"type\": \"string\",\n            \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"],\n            \"description\": \"Priorità del task\"\n          },\n          \"due_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"description\": \"Data di scadenza\"\n          },\n          \"days_until_due\": {\n            \"type\": \"integer\",\n            \"description\": \"Giorni rimanenti alla scadenza\"\n          },\n          \"estimated_hours\": {\n            \"type\": \"number\",\n            \"nullable\": true,\n            \"description\": \"Ore stimate\"\n          },\n          \"is_overdue\": {\n            \"type\": \"boolean\",\n            \"description\": \"Indica se il task è in ritardo\"\n          },\n          \"link\": {\n            \"type\": \"string\",\n            \"description\": \"Link per visualizzare il task\"\n          }\n        }\n      },\n      \"DashboardKPI\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del KPI\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del KPI\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria del KPI\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione del KPI\"\n          },\n          \"current_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore attuale\"\n          },\n          \"target_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore target\"\n          },\n          \"unit\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Unità di misura\"\n          },\n          \"trend\": {\n            \"type\": \"string\",\n            \"enum\": [\"up\", \"down\", \"stable\"],\n            \"nullable\": true,\n            \"description\": \"Trend del KPI\"\n          },\n          \"last_updated\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Ultimo aggiornamento\"\n          },\n          \"performance_percentage\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale di performance rispetto al target\"\n          }\n        }\n      },\n      \"CurrentUser\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'utente\"\n          },\n          \"username\": {\n            \"type\": \"string\",\n            \"description\": \"Nome utente\"\n          },\n          \"email\": {\n            \"type\": \"string\",\n            \"description\": \"Email dell'utente\"\n          },\n          \"first_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome\"\n          },\n          \"last_name\": {\n            \"type\": \"string\",\n            \"description\": \"Cognome\"\n          },\n          \"full_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome completo\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"enum\": [\"admin\", \"manager\", \"employee\", \"human_resources\", \"sales\"],\n            \"description\": \"Ruolo dell'utente\"\n          },\n          \"department_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID del dipartimento\"\n          },\n          \"department_name\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Nome del dipartimento\"\n          },\n          \"position\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Posizione lavorativa\"\n          },\n          \"phone\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Numero di telefono\"\n          },\n          \"bio\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Biografia\"\n          },\n          \"profile_image\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"URL immagine profilo\"\n          },\n          \"is_active\": {\n            \"type\": \"boolean\",\n            \"description\": \"Stato attivo dell'utente\"\n          },\n          \"dark_mode\": {\n            \"type\": \"boolean\",\n            \"description\": \"Preferenza modalità scura\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Data di creazione\"\n          },\n          \"last_login\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Ultimo accesso\"\n          },\n          \"hire_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di assunzione\"\n          },\n          \"permissions\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"string\"\n            },\n            \"description\": \"Lista dei permessi dell'utente\"\n          },\n          \"profile_completion\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale completamento profilo\"\n          },\n          \"preferences\": {\n            \"$ref\": \"#/components/schemas/UserPreferences\"\n          }\n        }\n      },\n      \"UserPreferences\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"dark_mode\": {\n            \"type\": \"boolean\",\n            \"description\": \"Modalità scura\"\n          },\n          \"language\": {\n            \"type\": \"string\",\n            \"description\": \"Lingua preferita\",\n            \"default\": \"it\"\n          },\n          \"timezone\": {\n            \"type\": \"string\",\n            \"description\": \"Fuso orario\",\n            \"default\": \"Europe/Rome\"\n          },\n          \"notifications\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"email\": {\n                \"type\": \"boolean\",\n                \"description\": \"Notifiche email\"\n              },\n              \"browser\": {\n                \"type\": \"boolean\",\n                \"description\": \"Notifiche browser\"\n              },\n              \"mobile\": {\n                \"type\": \"boolean\",\n                \"description\": \"Notifiche mobile\"\n              }\n            }\n          }\n        }\n      }\n    },\n    \"responses\": {\n      \"Unauthorized\": {\n        \"description\": \"Autenticazione richiesta\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"$ref\": \"#/components/schemas/Error\"\n            },\n            \"example\": {\n              \"success\": false,\n              \"message\": \"Autenticazione richiesta\"\n            }\n          }\n        }\n      },\n      \"Forbidden\": {\n        \"description\": \"Permesso negato\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"$ref\": \"#/components/schemas/Error\"\n            },\n            \"example\": {\n              \"success\": false,\n              \"message\": \"Non hai i permessi necessari per accedere a questa risorsa\"\n            }\n          }\n        }\n      },\n      \"NotFound\": {\n        \"description\": \"Risorsa non trovata\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"$ref\": \"#/components/schemas/Error\"\n            },\n            \"example\": {\n              \"success\": false,\n              \"message\": \"Risorsa non trovata\"\n            }\n          }\n        }\n      }\n    },\n    \"parameters\": {\n      \"pageParam\": {\n        \"name\": \"page\",\n        \"in\": \"query\",\n        \"description\": \"Numero di pagina\",\n        \"schema\": {\n          \"type\": \"integer\",\n          \"default\": 1,\n          \"minimum\": 1\n        }\n      },\n      \"perPageParam\": {\n        \"name\": \"per_page\",\n        \"in\": \"query\",\n        \"description\": \"Elementi per pagina\",\n        \"schema\": {\n          \"type\": \"integer\",\n          \"default\": 10,\n          \"minimum\": 1,\n          \"maximum\": 100\n        }\n      }\n    },\n    \"securitySchemes\": {\n      \"cookieAuth\": {\n        \"type\": \"apiKey\",\n        \"in\": \"cookie\",\n        \"name\": \"session\"\n      }\n    }\n  },\n  \"security\": [\n    {\n      \"cookieAuth\": []\n    }\n  ]\n}\n", "modifiedCode": "{\n  \"openapi\": \"3.0.0\",\n  \"info\": {\n    \"title\": \"DatPortal API\",\n    \"description\": \"API RESTful per DatPortal - Gestione Progetti, Task, Risorse e KPI\",\n    \"version\": \"1.0.0\",\n    \"contact\": {\n      \"name\": \"<PERSON>tV<PERSON><PERSON>\",\n      \"url\": \"https://datvinci.com\"\n    }\n  },\n  \"servers\": [\n    {\n      \"url\": \"/api\",\n      \"description\": \"Server di sviluppo\"\n    }\n  ],\n  \"tags\": [\n    {\n      \"name\": \"projects\",\n      \"description\": \"Operazioni sui progetti\"\n    },\n    {\n      \"name\": \"tasks\",\n      \"description\": \"Operazioni sui task\"\n    },\n    {\n      \"name\": \"resources\",\n      \"description\": \"Operazioni sulle risorse di progetto\"\n    },\n    {\n      \"name\": \"dependencies\",\n      \"description\": \"Operazioni sulle dipendenze tra task\"\n    },\n    {\n      \"name\": \"kpis\",\n      \"description\": \"Operazioni sui KPI generali\"\n    },\n    {\n      \"name\": \"project-kpis\",\n      \"description\": \"Operazioni sui KPI di progetto\"\n    },\n    {\n      \"name\": \"personnel\",\n      \"description\": \"Operazioni su personale, dipartimenti e competenze\"\n    },\n    {\n      \"name\": \"dashboard\",\n      \"description\": \"Operazioni per dashboard, statistiche e attività recenti\"\n    },\n    {\n      \"name\": \"auth\",\n      \"description\": \"Operazioni per autenticazione e gestione sessione utente\"\n    },\n    {\n      \"name\": \"timesheets\",\n      \"description\": \"Operazioni sui timesheet entries - registrazione ore lavorate\"\n    },\n    {\n      \"name\": \"monthly-timesheets\",\n      \"description\": \"Operazioni sui timesheet mensili - approvazione e workflow\"\n    },\n    {\n      \"name\": \"time-off-requests\",\n      \"description\": \"Operazioni sulle richieste di ferie, permessi e smartworking\"\n    }\n  ],\n  \"paths\": {\n    \"/projects/\": {\n      \"get\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Ottiene la lista dei progetti\",\n        \"description\": \"Ottiene la lista dei progetti con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"status\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per stato del progetto\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"]\n            }\n          },\n          {\n            \"name\": \"client_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID cliente\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca nei nomi e nelle descrizioni dei progetti\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di progetti\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"projects\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/Project\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Crea un nuovo progetto\",\n        \"description\": \"Crea un nuovo progetto con i dati forniti.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"name\"],\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del progetto\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del progetto\"\n                  },\n                  \"client_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del cliente associato\"\n                  },\n                  \"start_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di inizio (YYYY-MM-DD)\"\n                  },\n                  \"end_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di fine (YYYY-MM-DD)\"\n                  },\n                  \"status\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"],\n                    \"default\": \"planning\",\n                    \"description\": \"Stato del progetto\"\n                  },\n                  \"budget\": {\n                    \"type\": \"number\",\n                    \"description\": \"Budget del progetto\"\n                  },\n                  \"team_members\": {\n                    \"type\": \"array\",\n                    \"items\": {\n                      \"type\": \"integer\"\n                    },\n                    \"description\": \"Lista di ID utenti da assegnare al progetto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"Progetto creato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project\": {\n                          \"$ref\": \"#/components/schemas/Project\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Progetto creato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Nome del progetto obbligatorio\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/tasks/\": {\n      \"get\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Ottiene la lista dei task\",\n        \"description\": \"Ottiene la lista dei task con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"project_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"status\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per stato del task\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"]\n            }\n          },\n          {\n            \"name\": \"priority\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per priorità del task\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"]\n            }\n          },\n          {\n            \"name\": \"assignee_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID dell'assegnatario\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca nei nomi e nelle descrizioni dei task\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di task\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"tasks\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/Task\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Crea un nuovo task\",\n        \"description\": \"Crea un nuovo task con i dati forniti.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"name\", \"project_id\"],\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del task\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del task\"\n                  },\n                  \"project_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del progetto associato\"\n                  },\n                  \"assignee_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID dell'utente assegnato al task\"\n                  },\n                  \"status\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n                    \"default\": \"todo\",\n                    \"description\": \"Stato del task\"\n                  },\n                  \"priority\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"],\n                    \"default\": \"medium\",\n                    \"description\": \"Priorità del task\"\n                  },\n                  \"due_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di scadenza (YYYY-MM-DD)\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"Task creato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"task\": {\n                          \"$ref\": \"#/components/schemas/Task\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Task creato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Nome del task e ID progetto obbligatori\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/resources/\": {\n      \"get\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Ottiene la lista delle risorse dei progetti\",\n        \"description\": \"Ottiene la lista delle risorse dei progetti con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"project_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"user_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID utente\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di risorse\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"resources\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/ProjectResource\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Assegna una nuova risorsa a un progetto\",\n        \"description\": \"Assegna una nuova risorsa a un progetto.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"project_id\", \"user_id\"],\n                \"properties\": {\n                  \"project_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del progetto\"\n                  },\n                  \"user_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID dell'utente da assegnare\"\n                  },\n                  \"allocation_percentage\": {\n                    \"type\": \"integer\",\n                    \"description\": \"Percentuale di allocazione (1-100)\",\n                    \"default\": 100\n                  },\n                  \"role\": {\n                    \"type\": \"string\",\n                    \"description\": \"Ruolo dell'utente nel progetto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"Risorsa assegnata con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"resource\": {\n                          \"$ref\": \"#/components/schemas/ProjectResource\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Risorsa assegnata con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"ID progetto e ID utente obbligatori\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/task-dependencies/\": {\n      \"get\": {\n        \"tags\": [\"task-dependencies\"],\n        \"summary\": \"Ottiene la lista delle dipendenze tra task\",\n        \"description\": \"Ottiene la lista delle dipendenze tra task con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"task_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID del task\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"depends_on_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID del task dipendente\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"project_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID del progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di dipendenze tra task\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"dependencies\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/TaskDependency\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"task-dependencies\"],\n        \"summary\": \"Crea una nuova dipendenza tra task\",\n        \"description\": \"Crea una nuova dipendenza tra task.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"task_id\", \"depends_on_id\"],\n                \"properties\": {\n                  \"task_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del task che dipende\"\n                  },\n                  \"depends_on_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del task da cui dipende\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"Dipendenza creata con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"dependency\": {\n                          \"$ref\": \"#/components/schemas/TaskDependency\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dipendenza creata con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"ID task e ID task dipendente obbligatori\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/kpis/\": {\n      \"get\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Ottiene la lista dei KPI\",\n        \"description\": \"Ottiene la lista dei KPI con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"category\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per categoria KPI\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"frequency\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per frequenza KPI\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"daily\", \"weekly\", \"monthly\", \"quarterly\", \"annually\"]\n            }\n          },\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca nei nomi e descrizioni dei KPI\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di KPI\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpis\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/KPI\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Crea un nuovo KPI\",\n        \"description\": \"Crea un nuovo KPI con i dati forniti.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"name\"],\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del KPI\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del KPI\"\n                  },\n                  \"category\": {\n                    \"type\": \"string\",\n                    \"description\": \"Categoria del KPI\"\n                  },\n                  \"target_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore target del KPI\"\n                  },\n                  \"current_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore attuale del KPI\",\n                    \"default\": 0\n                  },\n                  \"unit\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unità di misura\"\n                  },\n                  \"frequency\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"daily\", \"weekly\", \"monthly\", \"quarterly\", \"annually\"],\n                    \"description\": \"Frequenza di misurazione\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"KPI creato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpi\": {\n                          \"$ref\": \"#/components/schemas/KPI\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI creato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Nome del KPI obbligatorio\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/kpis/{kpi_id}\": {\n      \"get\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Ottiene i dettagli di un KPI\",\n        \"description\": \"Ottiene i dettagli di un KPI specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli del KPI\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpi\": {\n                          \"$ref\": \"#/components/schemas/KPI\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Aggiorna un KPI esistente\",\n        \"description\": \"Aggiorna un KPI esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del KPI\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del KPI\"\n                  },\n                  \"category\": {\n                    \"type\": \"string\",\n                    \"description\": \"Categoria del KPI\"\n                  },\n                  \"target_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore target del KPI\"\n                  },\n                  \"current_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore attuale del KPI\"\n                  },\n                  \"unit\": {\n                    \"type\": \"string\",\n                    \"description\": \"Unità di misura\"\n                  },\n                  \"frequency\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"daily\", \"weekly\", \"monthly\", \"quarterly\", \"annually\"],\n                    \"description\": \"Frequenza di misurazione\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"KPI aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpi\": {\n                          \"$ref\": \"#/components/schemas/KPI\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI aggiornato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"kpis\"],\n        \"summary\": \"Elimina un KPI\",\n        \"description\": \"Elimina un KPI.\",\n        \"parameters\": [\n          {\n            \"name\": \"kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"KPI eliminato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI eliminato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"KPI in uso, impossibile eliminare\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Impossibile eliminare KPI: è utilizzato in progetti\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/project-kpis/\": {\n      \"get\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Ottiene la lista dei KPI di progetto\",\n        \"description\": \"Ottiene la lista dei KPI di progetto con supporto per filtri e paginazione.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"project_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"kpi_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID KPI\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista di KPI di progetto\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project_kpis\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/ProjectKPI\"\n                          }\n                        }\n                      }\n                    },\n                    \"pagination\": {\n                      \"$ref\": \"#/components/schemas/Pagination\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"post\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Crea un nuovo KPI di progetto\",\n        \"description\": \"Crea un nuovo KPI di progetto.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"project_id\", \"kpi_id\"],\n                \"properties\": {\n                  \"project_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del progetto\"\n                  },\n                  \"kpi_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del KPI\"\n                  },\n                  \"target_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore target del KPI\"\n                  },\n                  \"current_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore attuale del KPI\",\n                    \"default\": 0\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"201\": {\n            \"description\": \"KPI di progetto creato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project_kpi\": {\n                          \"$ref\": \"#/components/schemas/ProjectKPI\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI di progetto creato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"ID progetto e ID KPI obbligatori\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/project-kpis/{project_kpi_id}\": {\n      \"get\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Ottiene i dettagli di un KPI di progetto\",\n        \"description\": \"Ottiene i dettagli di un KPI di progetto specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI di progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli del KPI di progetto\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project_kpi\": {\n                          \"$ref\": \"#/components/schemas/ProjectKPI\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Aggiorna un KPI di progetto esistente\",\n        \"description\": \"Aggiorna un KPI di progetto esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI di progetto da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"target_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore target del KPI\"\n                  },\n                  \"current_value\": {\n                    \"type\": \"number\",\n                    \"description\": \"Valore attuale del KPI\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"KPI di progetto aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project_kpi\": {\n                          \"$ref\": \"#/components/schemas/ProjectKPI\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI di progetto aggiornato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"project-kpis\"],\n        \"summary\": \"Elimina un KPI di progetto\",\n        \"description\": \"Elimina un KPI di progetto.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_kpi_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del KPI di progetto da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"KPI di progetto eliminato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"KPI di progetto eliminato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/task-dependencies/{dependency_id}\": {\n      \"get\": {\n        \"tags\": [\"task-dependencies\"],\n        \"summary\": \"Ottiene i dettagli di una dipendenza\",\n        \"description\": \"Ottiene i dettagli di una dipendenza specifica.\",\n        \"parameters\": [\n          {\n            \"name\": \"dependency_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della dipendenza\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli della dipendenza\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"dependency\": {\n                          \"$ref\": \"#/components/schemas/TaskDependency\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"task-dependencies\"],\n        \"summary\": \"Elimina una dipendenza\",\n        \"description\": \"Elimina una dipendenza tra task.\",\n        \"parameters\": [\n          {\n            \"name\": \"dependency_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della dipendenza da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dipendenza eliminata con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dipendenza eliminata con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/resources/{resource_id}\": {\n      \"get\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Ottiene i dettagli di una risorsa\",\n        \"description\": \"Ottiene i dettagli di una risorsa specifica.\",\n        \"parameters\": [\n          {\n            \"name\": \"resource_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della risorsa\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli della risorsa\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"resource\": {\n                          \"$ref\": \"#/components/schemas/ProjectResource\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Aggiorna un'assegnazione di risorsa esistente\",\n        \"description\": \"Aggiorna un'assegnazione di risorsa esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"resource_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della risorsa da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"allocation_percentage\": {\n                    \"type\": \"integer\",\n                    \"description\": \"Percentuale di allocazione (1-100)\"\n                  },\n                  \"role\": {\n                    \"type\": \"string\",\n                    \"description\": \"Ruolo dell'utente nel progetto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Risorsa aggiornata con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"resource\": {\n                          \"$ref\": \"#/components/schemas/ProjectResource\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Risorsa aggiornata con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"resources\"],\n        \"summary\": \"Rimuove un'assegnazione di risorsa\",\n        \"description\": \"Rimuove un'assegnazione di risorsa.\",\n        \"parameters\": [\n          {\n            \"name\": \"resource_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID della risorsa da rimuovere\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Risorsa rimossa con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Risorsa rimossa con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/tasks/status\": {\n      \"patch\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Aggiorna lo stato di più task contemporaneamente\",\n        \"description\": \"Aggiorna lo stato di più task contemporaneamente.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"tasks\"],\n                \"properties\": {\n                  \"tasks\": {\n                    \"type\": \"array\",\n                    \"items\": {\n                      \"type\": \"object\",\n                      \"required\": [\"id\", \"status\"],\n                      \"properties\": {\n                        \"id\": {\n                          \"type\": \"integer\",\n                          \"description\": \"ID del task\"\n                        },\n                        \"status\": {\n                          \"type\": \"string\",\n                          \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n                          \"description\": \"Nuovo stato del task\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Stato dei task aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"results\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"type\": \"object\",\n                            \"properties\": {\n                              \"task_id\": {\n                                \"type\": \"integer\",\n                                \"example\": 1\n                              },\n                              \"success\": {\n                                \"type\": \"boolean\",\n                                \"example\": true\n                              },\n                              \"old_status\": {\n                                \"type\": \"string\",\n                                \"example\": \"todo\"\n                              },\n                              \"new_status\": {\n                                \"type\": \"string\",\n                                \"example\": \"in-progress\"\n                              },\n                              \"message\": {\n                                \"type\": \"string\",\n                                \"example\": \"Stato aggiornato da 'todo' a 'in-progress'\"\n                              }\n                            }\n                          }\n                        },\n                        \"message\": {\n                          \"type\": \"string\",\n                          \"example\": \"Aggiornamento completato: 3 task aggiornati, 0 falliti\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi: è richiesta una lista di task\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/tasks/{task_id}\": {\n      \"get\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Ottiene i dettagli di un task\",\n        \"description\": \"Ottiene i dettagli di un task specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"task_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del task\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli del task\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"task\": {\n                          \"$ref\": \"#/components/schemas/Task\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Aggiorna un task esistente\",\n        \"description\": \"Aggiorna un task esistente con i dati forniti.\",\n        \"parameters\": [\n          {\n            \"name\": \"task_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del task da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del task\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del task\"\n                  },\n                  \"project_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del progetto associato\"\n                  },\n                  \"assignee_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID dell'utente assegnato al task\"\n                  },\n                  \"status\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n                    \"description\": \"Stato del task\"\n                  },\n                  \"priority\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"],\n                    \"description\": \"Priorità del task\"\n                  },\n                  \"due_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di scadenza (YYYY-MM-DD)\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Task aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"task\": {\n                          \"$ref\": \"#/components/schemas/Task\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Task aggiornato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"tasks\"],\n        \"summary\": \"Elimina un task esistente\",\n        \"description\": \"Elimina un task esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"task_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del task da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Task eliminato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Task eliminato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/projects/batch\": {\n      \"post\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Esegue operazioni batch sui progetti\",\n        \"description\": \"Esegue operazioni batch sui progetti.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"required\": [\"operations\"],\n                \"properties\": {\n                  \"operations\": {\n                    \"type\": \"array\",\n                    \"items\": {\n                      \"type\": \"object\",\n                      \"required\": [\"operation\"],\n                      \"properties\": {\n                        \"operation\": {\n                          \"type\": \"string\",\n                          \"enum\": [\"create\", \"update\", \"delete\"],\n                          \"description\": \"Tipo di operazione da eseguire\"\n                        },\n                        \"project_id\": {\n                          \"type\": \"integer\",\n                          \"description\": \"ID del progetto (richiesto per update e delete)\"\n                        },\n                        \"data\": {\n                          \"type\": \"object\",\n                          \"description\": \"Dati del progetto (richiesto per create e update)\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Operazioni batch eseguite con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"results\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"type\": \"object\",\n                            \"properties\": {\n                              \"operation\": {\n                                \"type\": \"string\",\n                                \"example\": \"create\"\n                              },\n                              \"success\": {\n                                \"type\": \"boolean\",\n                                \"example\": true\n                              },\n                              \"project_id\": {\n                                \"type\": \"integer\",\n                                \"example\": 1\n                              },\n                              \"message\": {\n                                \"type\": \"string\",\n                                \"example\": \"Progetto creato con successo\"\n                              }\n                            }\n                          }\n                        },\n                        \"message\": {\n                          \"type\": \"string\",\n                          \"example\": \"Operazioni batch eseguite con successo\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/projects/{project_id}\": {\n      \"get\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Ottiene i dettagli di un progetto\",\n        \"description\": \"Ottiene i dettagli di un progetto specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del progetto\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli del progetto\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project\": {\n                          \"$ref\": \"#/components/schemas/Project\"\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Aggiorna un progetto esistente\",\n        \"description\": \"Aggiorna un progetto esistente con i dati forniti.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del progetto da aggiornare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"name\": {\n                    \"type\": \"string\",\n                    \"description\": \"Nome del progetto\"\n                  },\n                  \"description\": {\n                    \"type\": \"string\",\n                    \"description\": \"Descrizione del progetto\"\n                  },\n                  \"client_id\": {\n                    \"type\": \"integer\",\n                    \"description\": \"ID del cliente associato\"\n                  },\n                  \"start_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di inizio (YYYY-MM-DD)\"\n                  },\n                  \"end_date\": {\n                    \"type\": \"string\",\n                    \"format\": \"date\",\n                    \"description\": \"Data di fine (YYYY-MM-DD)\"\n                  },\n                  \"status\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"],\n                    \"description\": \"Stato del progetto\"\n                  },\n                  \"budget\": {\n                    \"type\": \"number\",\n                    \"description\": \"Budget del progetto\"\n                  },\n                  \"team_members\": {\n                    \"type\": \"array\",\n                    \"items\": {\n                      \"type\": \"integer\"\n                    },\n                    \"description\": \"Lista di ID utenti da assegnare al progetto\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Progetto aggiornato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"project\": {\n                          \"$ref\": \"#/components/schemas/Project\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Progetto aggiornato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": false\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dati non validi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"delete\": {\n        \"tags\": [\"projects\"],\n        \"summary\": \"Elimina un progetto esistente\",\n        \"description\": \"Elimina un progetto esistente.\",\n        \"parameters\": [\n          {\n            \"name\": \"project_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID del progetto da eliminare\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Progetto eliminato con successo\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Progetto eliminato con successo\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/personnel/users\": {\n      \"get\": {\n        \"tags\": [\"personnel\"],\n        \"summary\": \"Ottiene la lista degli utenti\",\n        \"description\": \"Ottiene la lista degli utenti con supporto per filtri, paginazione e ricerca.\",\n        \"parameters\": [\n          {\"$ref\": \"#/components/parameters/pageParam\"},\n          {\"$ref\": \"#/components/parameters/perPageParam\"},\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca in nome, cognome, username, email\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"department_id\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ID dipartimento\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          },\n          {\n            \"name\": \"role\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per ruolo utente\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"admin\", \"manager\", \"employee\", \"human_resources\"]\n            }\n          },\n          {\n            \"name\": \"is_active\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per stato attivo\",\n            \"schema\": {\n              \"type\": \"boolean\"\n            }\n          },\n          {\n            \"name\": \"skills\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per competenze (ID separati da virgola)\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"order_by\",\n            \"in\": \"query\",\n            \"description\": \"Campo per ordinamento\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"default\": \"last_name\"\n            }\n          },\n          {\n            \"name\": \"order_dir\",\n            \"in\": \"query\",\n            \"description\": \"Direzione ordinamento\",\n            \"schema\": {\n              \"type\": \"string\",\n              \"enum\": [\"asc\", \"desc\"],\n              \"default\": \"asc\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista utenti\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"users\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/UserSummary\"\n                          }\n                        },\n                        \"pagination\": {\n                          \"$ref\": \"#/components/schemas/Pagination\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 25 users\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/personnel/users/{user_id}\": {\n      \"get\": {\n        \"tags\": [\"personnel\"],\n        \"summary\": \"Ottiene i dettagli di un utente\",\n        \"description\": \"Ottiene informazioni dettagliate su un utente specifico.\",\n        \"parameters\": [\n          {\n            \"name\": \"user_id\",\n            \"in\": \"path\",\n            \"required\": true,\n            \"description\": \"ID dell'utente\",\n            \"schema\": {\n              \"type\": \"integer\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dettagli utente\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"user\": {\n                          \"$ref\": \"#/components/schemas/UserDetail\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved user Mario Rossi\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"404\": {\n            \"$ref\": \"#/components/responses/NotFound\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/personnel/departments\": {\n      \"get\": {\n        \"tags\": [\"personnel\"],\n        \"summary\": \"Ottiene la lista dei dipartimenti\",\n        \"description\": \"Ottiene la lista dei dipartimenti con dati per l'organigramma.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista dipartimenti\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"departments\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/Department\"\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 5 departments\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/personnel/skills\": {\n      \"get\": {\n        \"tags\": [\"personnel\"],\n        \"summary\": \"Ottiene la lista delle competenze\",\n        \"description\": \"Ottiene la lista delle competenze con statistiche di utilizzo.\",\n        \"parameters\": [\n          {\n            \"name\": \"category\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per categoria competenza\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"search\",\n            \"in\": \"query\",\n            \"description\": \"Cerca in nome e descrizione competenze\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista competenze\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"skills\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/Skill\"\n                          }\n                        },\n                        \"categories\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"type\": \"string\"\n                          },\n                          \"description\": \"Lista delle categorie disponibili\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 42 skills\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          },\n          \"403\": {\n            \"$ref\": \"#/components/responses/Forbidden\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/dashboard/stats\": {\n      \"get\": {\n        \"tags\": [\"dashboard\"],\n        \"summary\": \"Ottiene le statistiche della dashboard\",\n        \"description\": \"Ottiene statistiche aggregate per la dashboard principale.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Statistiche dashboard\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"projects\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"active\": {\"type\": \"integer\"},\n                            \"total\": {\"type\": \"integer\"}\n                          }\n                        },\n                        \"tasks\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"total\": {\"type\": \"integer\"},\n                            \"pending\": {\"type\": \"integer\"},\n                            \"completed\": {\"type\": \"integer\"},\n                            \"overdue\": {\"type\": \"integer\"}\n                          }\n                        },\n                        \"team\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"users\": {\"type\": \"integer\"},\n                            \"departments\": {\"type\": \"integer\"},\n                            \"clients\": {\"type\": \"integer\"}\n                          }\n                        },\n                        \"activities\": {\n                          \"type\": \"object\",\n                          \"properties\": {\n                            \"recent_timesheets\": {\"type\": \"integer\"},\n                            \"unread_notifications\": {\"type\": \"integer\"}\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Dashboard statistics retrieved successfully\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/dashboard/recent-activities\": {\n      \"get\": {\n        \"tags\": [\"dashboard\"],\n        \"summary\": \"Ottiene le attività recenti\",\n        \"description\": \"Ottiene la lista delle attività recenti per la dashboard.\",\n        \"parameters\": [\n          {\n            \"name\": \"limit\",\n            \"in\": \"query\",\n            \"description\": \"Numero massimo di attività da restituire\",\n            \"schema\": {\n              \"type\": \"integer\",\n              \"default\": 10,\n              \"maximum\": 50\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista attività recenti\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"activities\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/DashboardActivity\"\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 10 recent activities\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/dashboard/upcoming-tasks\": {\n      \"get\": {\n        \"tags\": [\"dashboard\"],\n        \"summary\": \"Ottiene i task in scadenza\",\n        \"description\": \"Ottiene la lista dei task in scadenza per la dashboard.\",\n        \"parameters\": [\n          {\n            \"name\": \"days\",\n            \"in\": \"query\",\n            \"description\": \"Numero di giorni da considerare per le scadenze\",\n            \"schema\": {\n              \"type\": \"integer\",\n              \"default\": 7\n            }\n          },\n          {\n            \"name\": \"limit\",\n            \"in\": \"query\",\n            \"description\": \"Numero massimo di task da restituire\",\n            \"schema\": {\n              \"type\": \"integer\",\n              \"default\": 10\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista task in scadenza\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"tasks\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/UpcomingTask\"\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 5 upcoming tasks\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/dashboard/kpis\": {\n      \"get\": {\n        \"tags\": [\"dashboard\"],\n        \"summary\": \"Ottiene i KPI per la dashboard\",\n        \"description\": \"Ottiene i KPI principali per la dashboard.\",\n        \"parameters\": [\n          {\n            \"name\": \"category\",\n            \"in\": \"query\",\n            \"description\": \"Filtra per categoria KPI\",\n            \"schema\": {\n              \"type\": \"string\"\n            }\n          },\n          {\n            \"name\": \"limit\",\n            \"in\": \"query\",\n            \"description\": \"Numero massimo di KPI da restituire\",\n            \"schema\": {\n              \"type\": \"integer\",\n              \"default\": 6,\n              \"maximum\": 20\n            }\n          }\n        ],\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Lista KPI dashboard\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"kpis\": {\n                          \"type\": \"array\",\n                          \"items\": {\n                            \"$ref\": \"#/components/schemas/DashboardKPI\"\n                          }\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Retrieved 6 KPIs\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/auth/me\": {\n      \"get\": {\n        \"tags\": [\"auth\"],\n        \"summary\": \"Ottiene i dati dell'utente corrente\",\n        \"description\": \"Ottiene informazioni complete dell'utente corrente inclusi permessi e preferenze.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Dati utente corrente\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"user\": {\n                          \"$ref\": \"#/components/schemas/CurrentUser\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Current user data retrieved successfully\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/auth/check-session\": {\n      \"get\": {\n        \"tags\": [\"auth\"],\n        \"summary\": \"Verifica validità sessione\",\n        \"description\": \"Verifica se la sessione corrente è valida.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Stato sessione\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"valid\": {\n                          \"type\": \"boolean\",\n                          \"example\": true\n                        },\n                        \"user_id\": {\n                          \"type\": \"integer\",\n                          \"example\": 1\n                        },\n                        \"username\": {\n                          \"type\": \"string\",\n                          \"example\": \"mario.rossi\"\n                        },\n                        \"role\": {\n                          \"type\": \"string\",\n                          \"example\": \"admin\"\n                        },\n                        \"last_activity\": {\n                          \"type\": \"string\",\n                          \"format\": \"date-time\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"Session is valid\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    },\n    \"/auth/preferences\": {\n      \"get\": {\n        \"tags\": [\"auth\"],\n        \"summary\": \"Ottiene preferenze utente\",\n        \"description\": \"Ottiene le preferenze dell'utente corrente.\",\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Preferenze utente\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"preferences\": {\n                          \"$ref\": \"#/components/schemas/UserPreferences\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"User preferences retrieved successfully\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      },\n      \"put\": {\n        \"tags\": [\"auth\"],\n        \"summary\": \"Aggiorna preferenze utente\",\n        \"description\": \"Aggiorna le preferenze dell'utente corrente.\",\n        \"requestBody\": {\n          \"required\": true,\n          \"content\": {\n            \"application/json\": {\n              \"schema\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"dark_mode\": {\n                    \"type\": \"boolean\",\n                    \"description\": \"Modalità scura\"\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"responses\": {\n          \"200\": {\n            \"description\": \"Preferenze aggiornate\",\n            \"content\": {\n              \"application/json\": {\n                \"schema\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"success\": {\n                      \"type\": \"boolean\",\n                      \"example\": true\n                    },\n                    \"data\": {\n                      \"type\": \"object\",\n                      \"properties\": {\n                        \"preferences\": {\n                          \"$ref\": \"#/components/schemas/UserPreferences\"\n                        }\n                      }\n                    },\n                    \"message\": {\n                      \"type\": \"string\",\n                      \"example\": \"User preferences updated successfully\"\n                    }\n                  }\n                }\n              }\n            }\n          },\n          \"400\": {\n            \"description\": \"Dati non validi\"\n          },\n          \"401\": {\n            \"$ref\": \"#/components/responses/Unauthorized\"\n          }\n        },\n        \"security\": [\n          {\n            \"cookieAuth\": []\n          }\n        ]\n      }\n    }\n  },\n  \"components\": {\n    \"responses\": {\n      \"NotFound\": {\n        \"description\": \"Risorsa non trovata\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"success\": {\n                  \"type\": \"boolean\",\n                  \"example\": false\n                },\n                \"message\": {\n                  \"type\": \"string\",\n                  \"example\": \"Risorsa non trovata\"\n                }\n              }\n            }\n          }\n        }\n      },\n      \"Unauthorized\": {\n        \"description\": \"Autenticazione richiesta\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"success\": {\n                  \"type\": \"boolean\",\n                  \"example\": false\n                },\n                \"message\": {\n                  \"type\": \"string\",\n                  \"example\": \"Autenticazione richiesta\"\n                }\n              }\n            }\n          }\n        }\n      },\n      \"Forbidden\": {\n        \"description\": \"Accesso negato\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"success\": {\n                  \"type\": \"boolean\",\n                  \"example\": false\n                },\n                \"message\": {\n                  \"type\": \"string\",\n                  \"example\": \"Non hai i permessi necessari per accedere a questa risorsa\"\n                }\n              }\n            }\n          }\n        }\n      }\n    },\n    \"schemas\": {\n      \"Project\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"description\": \"ID univoco del progetto\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"Descrizione del progetto\"\n          },\n          \"client_id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"nullable\": true,\n            \"description\": \"ID del cliente associato al progetto\"\n          },\n          \"start_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di inizio del progetto (YYYY-MM-DD)\"\n          },\n          \"end_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di fine del progetto (YYYY-MM-DD)\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"],\n            \"description\": \"Stato del progetto\"\n          },\n          \"budget\": {\n            \"type\": \"number\",\n            \"format\": \"float\",\n            \"nullable\": true,\n            \"description\": \"Budget del progetto\"\n          },\n          \"expenses\": {\n            \"type\": \"number\",\n            \"format\": \"float\",\n            \"description\": \"Spese attuali del progetto\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data e ora di creazione del progetto\"\n          },\n          \"updated_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data e ora dell'ultimo aggiornamento del progetto\"\n          }\n        },\n        \"required\": [\"name\"]\n      },\n      \"Pagination\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"page\": {\n            \"type\": \"integer\",\n            \"description\": \"Pagina corrente\"\n          },\n          \"per_page\": {\n            \"type\": \"integer\",\n            \"description\": \"Elementi per pagina\"\n          },\n          \"total\": {\n            \"type\": \"integer\",\n            \"description\": \"Numero totale di elementi\"\n          },\n          \"pages\": {\n            \"type\": \"integer\",\n            \"description\": \"Numero totale di pagine\"\n          },\n          \"has_next\": {\n            \"type\": \"boolean\",\n            \"description\": \"Indica se esiste una pagina successiva\"\n          },\n          \"has_prev\": {\n            \"type\": \"boolean\",\n            \"description\": \"Indica se esiste una pagina precedente\"\n          }\n        }\n      },\n      \"KPI\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del KPI\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del KPI\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"Descrizione del KPI\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria del KPI\"\n          },\n          \"target_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore target del KPI\"\n          },\n          \"current_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore attuale del KPI\"\n          },\n          \"unit\": {\n            \"type\": \"string\",\n            \"description\": \"Unità di misura\"\n          },\n          \"frequency\": {\n            \"type\": \"string\",\n            \"enum\": [\"daily\", \"weekly\", \"monthly\", \"quarterly\", \"annually\"],\n            \"description\": \"Frequenza di misurazione\"\n          },\n          \"progress\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale di completamento del KPI\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data di creazione\"\n          },\n          \"updated_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data di ultimo aggiornamento\"\n          }\n        }\n      },\n      \"ProjectKPI\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del KPI di progetto\"\n          },\n          \"project_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del progetto\"\n          },\n          \"kpi_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del KPI\"\n          },\n          \"target_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore target del KPI\"\n          },\n          \"current_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore attuale del KPI\"\n          },\n          \"project_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"kpi_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del KPI\"\n          },\n          \"kpi_unit\": {\n            \"type\": \"string\",\n            \"description\": \"Unità di misura del KPI\"\n          },\n          \"progress\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale di completamento del KPI\"\n          }\n        }\n      },\n      \"TaskDependency\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della dipendenza\"\n          },\n          \"task_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del task che dipende\"\n          },\n          \"depends_on_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del task da cui dipende\"\n          },\n          \"task_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del task che dipende\"\n          },\n          \"depends_on_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del task da cui dipende\"\n          }\n        }\n      },\n      \"ProjectResource\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della risorsa\"\n          },\n          \"project_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del progetto\"\n          },\n          \"user_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'utente\"\n          },\n          \"allocation_percentage\": {\n            \"type\": \"integer\",\n            \"description\": \"Percentuale di allocazione (1-100)\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"description\": \"Ruolo dell'utente nel progetto\"\n          },\n          \"project_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"user_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome dell'utente\"\n          }\n        }\n      },\n      \"Task\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"description\": \"ID univoco del task\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del task\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione del task\"\n          },\n          \"project_id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"description\": \"ID del progetto a cui appartiene il task\"\n          },\n          \"assignee_id\": {\n            \"type\": \"integer\",\n            \"format\": \"int64\",\n            \"nullable\": true,\n            \"description\": \"ID dell'utente assegnato al task\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n            \"description\": \"Stato del task\"\n          },\n          \"priority\": {\n            \"type\": \"string\",\n            \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"],\n            \"description\": \"Priorità del task\"\n          },\n          \"due_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di scadenza del task (YYYY-MM-DD)\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data e ora di creazione del task\"\n          },\n          \"updated_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data e ora dell'ultimo aggiornamento del task\"\n          }\n        },\n        \"required\": [\"name\", \"project_id\"]\n      },\n      \"Error\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"success\": {\n            \"type\": \"boolean\",\n            \"example\": false\n          },\n          \"message\": {\n            \"type\": \"string\"\n          }\n        }\n      },\n      \"UserSummary\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'utente\"\n          },\n          \"username\": {\n            \"type\": \"string\",\n            \"description\": \"Nome utente\"\n          },\n          \"email\": {\n            \"type\": \"string\",\n            \"description\": \"Email dell'utente\"\n          },\n          \"first_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome\"\n          },\n          \"last_name\": {\n            \"type\": \"string\",\n            \"description\": \"Cognome\"\n          },\n          \"full_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome completo\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"enum\": [\"admin\", \"manager\", \"employee\", \"human_resources\"],\n            \"description\": \"Ruolo dell'utente\"\n          },\n          \"department_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID del dipartimento\"\n          },\n          \"department_name\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Nome del dipartimento\"\n          },\n          \"position\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Posizione lavorativa\"\n          },\n          \"hire_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di assunzione\"\n          },\n          \"phone\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Numero di telefono\"\n          },\n          \"profile_image\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"URL immagine profilo\"\n          },\n          \"is_active\": {\n            \"type\": \"boolean\",\n            \"description\": \"Stato attivo dell'utente\"\n          },\n          \"last_login\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Ultimo accesso\"\n          },\n          \"skills\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserSkill\"\n            },\n            \"description\": \"Competenze dell'utente\"\n          },\n          \"profile_completion\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale completamento profilo\"\n          }\n        }\n      },\n      \"UserDetail\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'utente\"\n          },\n          \"username\": {\n            \"type\": \"string\",\n            \"description\": \"Nome utente\"\n          },\n          \"email\": {\n            \"type\": \"string\",\n            \"description\": \"Email dell'utente\"\n          },\n          \"first_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome\"\n          },\n          \"last_name\": {\n            \"type\": \"string\",\n            \"description\": \"Cognome\"\n          },\n          \"full_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome completo\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"enum\": [\"admin\", \"manager\", \"employee\", \"human_resources\"],\n            \"description\": \"Ruolo dell'utente\"\n          },\n          \"department_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID del dipartimento\"\n          },\n          \"department\": {\n            \"$ref\": \"#/components/schemas/Department\",\n            \"nullable\": true,\n            \"description\": \"Dettagli del dipartimento\"\n          },\n          \"position\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Posizione lavorativa\"\n          },\n          \"hire_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di assunzione\"\n          },\n          \"phone\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Numero di telefono\"\n          },\n          \"profile_image\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"URL immagine profilo\"\n          },\n          \"bio\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Biografia dell'utente\"\n          },\n          \"is_active\": {\n            \"type\": \"boolean\",\n            \"description\": \"Stato attivo dell'utente\"\n          },\n          \"dark_mode\": {\n            \"type\": \"boolean\",\n            \"description\": \"Preferenza modalità scura\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data di creazione\"\n          },\n          \"last_login\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Ultimo accesso\"\n          },\n          \"skills\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserSkillDetail\"\n            },\n            \"description\": \"Competenze dettagliate dell'utente\"\n          },\n          \"projects\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserProject\"\n            },\n            \"description\": \"Progetti dell'utente\"\n          },\n          \"profile\": {\n            \"$ref\": \"#/components/schemas/UserProfile\",\n            \"nullable\": true,\n            \"description\": \"Profilo esteso dell'utente\"\n          }\n        }\n      },\n      \"Department\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del dipartimento\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del dipartimento\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione del dipartimento\"\n          },\n          \"manager_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID del manager del dipartimento\"\n          },\n          \"manager\": {\n            \"$ref\": \"#/components/schemas/UserSummary\",\n            \"nullable\": true,\n            \"description\": \"Manager del dipartimento\"\n          },\n          \"user_count\": {\n            \"type\": \"integer\",\n            \"description\": \"Numero di utenti nel dipartimento\"\n          },\n          \"users\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserSummary\"\n            },\n            \"description\": \"Utenti del dipartimento\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Data di creazione\"\n          }\n        }\n      },\n      \"Skill\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della competenza\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome della competenza\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria della competenza\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione della competenza\"\n          },\n          \"user_count\": {\n            \"type\": \"integer\",\n            \"description\": \"Numero di utenti con questa competenza\"\n          },\n          \"users\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"$ref\": \"#/components/schemas/UserSkill\"\n            },\n            \"description\": \"Utenti con questa competenza\"\n          }\n        }\n      },\n      \"UserSkill\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della competenza\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome della competenza\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria della competenza\"\n          },\n          \"proficiency_level\": {\n            \"type\": \"integer\",\n            \"minimum\": 1,\n            \"maximum\": 5,\n            \"description\": \"Livello di competenza (1-5)\"\n          },\n          \"years_experience\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"Anni di esperienza\"\n          }\n        }\n      },\n      \"UserSkillDetail\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID della competenza\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome della competenza\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria della competenza\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione della competenza\"\n          },\n          \"proficiency_level\": {\n            \"type\": \"integer\",\n            \"minimum\": 1,\n            \"maximum\": 5,\n            \"description\": \"Livello di competenza (1-5)\"\n          },\n          \"years_experience\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"Anni di esperienza\"\n          },\n          \"certified\": {\n            \"type\": \"boolean\",\n            \"description\": \"Certificazione ottenuta\"\n          },\n          \"last_used\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data certificazione\"\n          }\n        }\n      },\n      \"UserProject\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del progetto\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\"planning\", \"active\", \"completed\", \"on-hold\"],\n            \"description\": \"Stato del progetto\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"description\": \"Ruolo dell'utente nel progetto\"\n          }\n        }\n      },\n      \"UserProfile\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"emergency_contact_name\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Nome contatto di emergenza\"\n          },\n          \"emergency_contact_phone\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Telefono contatto di emergenza\"\n          },\n          \"address\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Indirizzo\"\n          },\n          \"profile_completion\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale completamento profilo\"\n          },\n          \"notes\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Note (solo per admin/HR)\"\n          }\n        }\n      },\n      \"DashboardActivity\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"type\": {\n            \"type\": \"string\",\n            \"enum\": [\"task\", \"timesheet\", \"event\"],\n            \"description\": \"Tipo di attività\"\n          },\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'attività\"\n          },\n          \"title\": {\n            \"type\": \"string\",\n            \"description\": \"Titolo dell'attività\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"description\": \"Descrizione dell'attività\"\n          },\n          \"timestamp\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"description\": \"Timestamp dell'attività\"\n          },\n          \"link\": {\n            \"type\": \"string\",\n            \"description\": \"Link per visualizzare l'attività\"\n          }\n        }\n      },\n      \"UpcomingTask\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del task\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del task\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione del task\"\n          },\n          \"project_id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del progetto\"\n          },\n          \"project_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del progetto\"\n          },\n          \"assignee_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID dell'assegnatario\"\n          },\n          \"assignee_name\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Nome dell'assegnatario\"\n          },\n          \"status\": {\n            \"type\": \"string\",\n            \"enum\": [\"todo\", \"in-progress\", \"review\", \"done\"],\n            \"description\": \"Stato del task\"\n          },\n          \"priority\": {\n            \"type\": \"string\",\n            \"enum\": [\"low\", \"medium\", \"high\", \"urgent\"],\n            \"description\": \"Priorità del task\"\n          },\n          \"due_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"description\": \"Data di scadenza\"\n          },\n          \"days_until_due\": {\n            \"type\": \"integer\",\n            \"description\": \"Giorni rimanenti alla scadenza\"\n          },\n          \"estimated_hours\": {\n            \"type\": \"number\",\n            \"nullable\": true,\n            \"description\": \"Ore stimate\"\n          },\n          \"is_overdue\": {\n            \"type\": \"boolean\",\n            \"description\": \"Indica se il task è in ritardo\"\n          },\n          \"link\": {\n            \"type\": \"string\",\n            \"description\": \"Link per visualizzare il task\"\n          }\n        }\n      },\n      \"DashboardKPI\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID del KPI\"\n          },\n          \"name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome del KPI\"\n          },\n          \"category\": {\n            \"type\": \"string\",\n            \"description\": \"Categoria del KPI\"\n          },\n          \"description\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Descrizione del KPI\"\n          },\n          \"current_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore attuale\"\n          },\n          \"target_value\": {\n            \"type\": \"number\",\n            \"description\": \"Valore target\"\n          },\n          \"unit\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Unità di misura\"\n          },\n          \"trend\": {\n            \"type\": \"string\",\n            \"enum\": [\"up\", \"down\", \"stable\"],\n            \"nullable\": true,\n            \"description\": \"Trend del KPI\"\n          },\n          \"last_updated\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Ultimo aggiornamento\"\n          },\n          \"performance_percentage\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale di performance rispetto al target\"\n          }\n        }\n      },\n      \"CurrentUser\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"id\": {\n            \"type\": \"integer\",\n            \"description\": \"ID dell'utente\"\n          },\n          \"username\": {\n            \"type\": \"string\",\n            \"description\": \"Nome utente\"\n          },\n          \"email\": {\n            \"type\": \"string\",\n            \"description\": \"Email dell'utente\"\n          },\n          \"first_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome\"\n          },\n          \"last_name\": {\n            \"type\": \"string\",\n            \"description\": \"Cognome\"\n          },\n          \"full_name\": {\n            \"type\": \"string\",\n            \"description\": \"Nome completo\"\n          },\n          \"role\": {\n            \"type\": \"string\",\n            \"enum\": [\"admin\", \"manager\", \"employee\", \"human_resources\", \"sales\"],\n            \"description\": \"Ruolo dell'utente\"\n          },\n          \"department_id\": {\n            \"type\": \"integer\",\n            \"nullable\": true,\n            \"description\": \"ID del dipartimento\"\n          },\n          \"department_name\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Nome del dipartimento\"\n          },\n          \"position\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Posizione lavorativa\"\n          },\n          \"phone\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Numero di telefono\"\n          },\n          \"bio\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"Biografia\"\n          },\n          \"profile_image\": {\n            \"type\": \"string\",\n            \"nullable\": true,\n            \"description\": \"URL immagine profilo\"\n          },\n          \"is_active\": {\n            \"type\": \"boolean\",\n            \"description\": \"Stato attivo dell'utente\"\n          },\n          \"dark_mode\": {\n            \"type\": \"boolean\",\n            \"description\": \"Preferenza modalità scura\"\n          },\n          \"created_at\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Data di creazione\"\n          },\n          \"last_login\": {\n            \"type\": \"string\",\n            \"format\": \"date-time\",\n            \"nullable\": true,\n            \"description\": \"Ultimo accesso\"\n          },\n          \"hire_date\": {\n            \"type\": \"string\",\n            \"format\": \"date\",\n            \"nullable\": true,\n            \"description\": \"Data di assunzione\"\n          },\n          \"permissions\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"string\"\n            },\n            \"description\": \"Lista dei permessi dell'utente\"\n          },\n          \"profile_completion\": {\n            \"type\": \"number\",\n            \"description\": \"Percentuale completamento profilo\"\n          },\n          \"preferences\": {\n            \"$ref\": \"#/components/schemas/UserPreferences\"\n          }\n        }\n      },\n      \"UserPreferences\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"dark_mode\": {\n            \"type\": \"boolean\",\n            \"description\": \"Modalità scura\"\n          },\n          \"language\": {\n            \"type\": \"string\",\n            \"description\": \"Lingua preferita\",\n            \"default\": \"it\"\n          },\n          \"timezone\": {\n            \"type\": \"string\",\n            \"description\": \"Fuso orario\",\n            \"default\": \"Europe/Rome\"\n          },\n          \"notifications\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"email\": {\n                \"type\": \"boolean\",\n                \"description\": \"Notifiche email\"\n              },\n              \"browser\": {\n                \"type\": \"boolean\",\n                \"description\": \"Notifiche browser\"\n              },\n              \"mobile\": {\n                \"type\": \"boolean\",\n                \"description\": \"Notifiche mobile\"\n              }\n            }\n          }\n        }\n      }\n    },\n    \"responses\": {\n      \"Unauthorized\": {\n        \"description\": \"Autenticazione richiesta\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"$ref\": \"#/components/schemas/Error\"\n            },\n            \"example\": {\n              \"success\": false,\n              \"message\": \"Autenticazione richiesta\"\n            }\n          }\n        }\n      },\n      \"Forbidden\": {\n        \"description\": \"Permesso negato\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"$ref\": \"#/components/schemas/Error\"\n            },\n            \"example\": {\n              \"success\": false,\n              \"message\": \"Non hai i permessi necessari per accedere a questa risorsa\"\n            }\n          }\n        }\n      },\n      \"NotFound\": {\n        \"description\": \"Risorsa non trovata\",\n        \"content\": {\n          \"application/json\": {\n            \"schema\": {\n              \"$ref\": \"#/components/schemas/Error\"\n            },\n            \"example\": {\n              \"success\": false,\n              \"message\": \"Risorsa non trovata\"\n            }\n          }\n        }\n      }\n    },\n    \"parameters\": {\n      \"pageParam\": {\n        \"name\": \"page\",\n        \"in\": \"query\",\n        \"description\": \"Numero di pagina\",\n        \"schema\": {\n          \"type\": \"integer\",\n          \"default\": 1,\n          \"minimum\": 1\n        }\n      },\n      \"perPageParam\": {\n        \"name\": \"per_page\",\n        \"in\": \"query\",\n        \"description\": \"Elementi per pagina\",\n        \"schema\": {\n          \"type\": \"integer\",\n          \"default\": 10,\n          \"minimum\": 1,\n          \"maximum\": 100\n        }\n      }\n    },\n    \"securitySchemes\": {\n      \"cookieAuth\": {\n        \"type\": \"apiKey\",\n        \"in\": \"cookie\",\n        \"name\": \"session\"\n      }\n    }\n  },\n  \"security\": [\n    {\n      \"cookieAuth\": []\n    }\n  ]\n}\n"}