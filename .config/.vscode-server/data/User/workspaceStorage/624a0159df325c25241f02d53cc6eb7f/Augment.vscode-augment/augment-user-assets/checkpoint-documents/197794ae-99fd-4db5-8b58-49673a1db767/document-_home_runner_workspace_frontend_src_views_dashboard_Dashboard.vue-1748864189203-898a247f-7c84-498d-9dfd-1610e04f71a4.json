{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/dashboard/Dashboard.vue"}, "originalCode": "<template>\n  <div class=\"py-6\">\n    <!-- Dashboard Header -->\n    <div class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Dashboard</h1>\n        <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n          Benvenuto! Ecco una panoramica delle attività della tua azienda.\n        </p>\n      </div>\n      <div class=\"mt-4 md:mt-0 flex space-x-3\">\n        <div class=\"relative\">\n          <select\n            v-model=\"selectedPeriod\"\n            @change=\"refreshData\"\n            class=\"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n          >\n            <option value=\"7\">Ultimi 7 giorni</option>\n            <option value=\"30\">Ultimo mese</option>\n            <option value=\"90\">Ultimi 3 mesi</option>\n          </select>\n        </div>\n\n        <button\n          @click=\"refreshData\"\n          :disabled=\"isLoading\"\n          type=\"button\"\n          class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n        >\n          <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" :class=\"{ 'animate-spin': isLoading }\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n            <path fill-rule=\"evenodd\" d=\"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\" clip-rule=\"evenodd\" />\n          </svg>\n          Aggiorna\n        </button>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n      <!-- Active Projects -->\n      <StatsCard\n        title=\"Progetti Attivi\"\n        :value=\"stats.projects?.active || 0\"\n        :subtitle=\"`di ${stats.projects?.total || 0} totali`\"\n        icon=\"project\"\n        color=\"primary\"\n        :link=\"'/app/projects?status=active'\"\n      />\n\n      <!-- Clients -->\n      <StatsCard\n        title=\"Clienti\"\n        :value=\"stats.team?.clients || 0\"\n        icon=\"users\"\n        color=\"secondary\"\n        :link=\"'/app/crm/clients'\"\n      />\n\n      <!-- Pending Tasks -->\n      <StatsCard\n        title=\"Task Pendenti\"\n        :value=\"stats.tasks?.pending || 0\"\n        :subtitle=\"`${stats.tasks?.overdue || 0} in ritardo`\"\n        icon=\"clock\"\n        :color=\"stats.tasks?.overdue > 0 ? 'red' : 'yellow'\"\n        :link=\"'/app/tasks?status=pending'\"\n      />\n\n      <!-- Team Members -->\n      <StatsCard\n        title=\"Team Members\"\n        :value=\"stats.team?.users || 0\"\n        :subtitle=\"`${stats.team?.departments || 0} dipartimenti`\"\n        icon=\"team\"\n        color=\"blue\"\n        :link=\"'/app/personnel'\"\n      />\n    </div>\n\n    <!-- Charts Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n      <!-- Project Status Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white\">Stato Progetti</h2>\n        </div>\n        <div class=\"relative h-64\">\n          <canvas ref=\"projectChart\"></canvas>\n        </div>\n      </div>\n\n      <!-- Task Status Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white\">Stato Attività</h2>\n        </div>\n        <div class=\"relative h-64\">\n          <canvas ref=\"taskChart\"></canvas>\n        </div>\n      </div>\n    </div>\n\n    <!-- Activities Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n      <!-- Upcoming Tasks -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"p-6\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Attività in Scadenza</h2>\n          <div v-if=\"upcomingTasks.length === 0\" class=\"text-center py-8 text-gray-500\">\n            Nessuna attività in scadenza\n          </div>\n          <div v-else class=\"space-y-4\">\n            <div\n              v-for=\"task in upcomingTasks\"\n              :key=\"task.id\"\n              class=\"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0\"\n            >\n              <div class=\"flex justify-between items-start\">\n                <div class=\"flex-1\">\n                  <h3 class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ task.name }}</h3>\n                  <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ task.project_name }}</p>\n                </div>\n                <span\n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getPriorityClasses(task.priority)\"\n                >\n                  {{ task.priority }}\n                </span>\n              </div>\n              <div class=\"mt-2 flex justify-between items-center\">\n                <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Scadenza: {{ formatDate(task.due_date) }}\n                </span>\n                <span\n                  class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium\"\n                  :class=\"getStatusClasses(task.status)\"\n                >\n                  {{ task.status }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"bg-gray-50 dark:bg-gray-700 px-6 py-3\">\n          <div class=\"text-sm\">\n            <router-link to=\"/app/tasks\" class=\"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500\">\n              Vedi tutte le attività\n            </router-link>\n          </div>\n        </div>\n      </div>\n\n      <!-- Recent Activities -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"p-6\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Attività Recenti</h2>\n          <div v-if=\"recentActivities.length === 0\" class=\"text-center py-8 text-gray-500\">\n            Nessuna attività recente\n          </div>\n          <div v-else class=\"space-y-4\">\n            <div\n              v-for=\"activity in recentActivities\"\n              :key=\"`${activity.type}-${activity.id}`\"\n              class=\"flex items-start space-x-3\"\n            >\n              <div class=\"flex-shrink-0\">\n                <div\n                  class=\"w-8 h-8 rounded-full flex items-center justify-center\"\n                  :class=\"getActivityIconClasses(activity.type)\"\n                >\n                  <div class=\"w-4 h-4\" v-html=\"getActivityIcon(activity.type)\"></div>\n                </div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <p class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ activity.title }}</p>\n                <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ activity.description }}</p>\n                <p class=\"text-xs text-gray-400 dark:text-gray-500\">{{ formatTimestamp(activity.timestamp) }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- KPIs -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"p-6\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">KPIs Principali</h2>\n          <div v-if=\"kpis.length === 0\" class=\"text-center py-8 text-gray-500\">\n            Nessun KPI configurato\n          </div>\n          <div v-else class=\"space-y-4\">\n            <div\n              v-for=\"kpi in kpis\"\n              :key=\"kpi.id\"\n              class=\"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0\"\n            >\n              <div class=\"flex justify-between items-start\">\n                <div class=\"flex-1\">\n                  <h3 class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ kpi.name }}</h3>\n                  <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ kpi.description }}</p>\n                </div>\n                <div class=\"text-right\">\n                  <p class=\"text-sm font-bold text-gray-900 dark:text-white\">\n                    {{ kpi.current_value }}{{ kpi.unit }}\n                  </p>\n                  <p class=\"text-xs text-gray-500\">\n                    Target: {{ kpi.target_value }}{{ kpi.unit }}\n                  </p>\n                </div>\n              </div>\n              <div class=\"mt-2\">\n                <div class=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div\n                    class=\"h-2 rounded-full\"\n                    :class=\"getKpiProgressClasses(kpi.performance_percentage)\"\n                    :style=\"{ width: Math.min(kpi.performance_percentage, 100) + '%' }\"\n                  ></div>\n                </div>\n                <p class=\"text-xs text-gray-500 mt-1\">{{ Math.round(kpi.performance_percentage) }}% del target</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, nextTick } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\nimport StatsCard from './components/StatsCard.vue'\nimport { Chart, registerables } from 'chart.js'\n\n// Register Chart.js components\nChart.register(...registerables)\n\nconst router = useRouter()\nconst authStore = useAuthStore()\n\n// Reactive data\nconst isLoading = ref(false)\nconst selectedPeriod = ref('7')\nconst stats = ref({})\nconst upcomingTasks = ref([])\nconst recentActivities = ref([])\nconst kpis = ref([])\n\n// Chart refs\nconst projectChart = ref(null)\nconst taskChart = ref(null)\nlet projectChartInstance = null\nlet taskChartInstance = null\n\n// API functions\nconst fetchDashboardStats = async () => {\n  try {\n    const response = await fetch('/api/dashboard/stats')\n    if (!response.ok) throw new Error('Failed to fetch stats')\n    const data = await response.json()\n    stats.value = data.data\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error)\n    stats.value = {}\n  }\n}\n\nconst fetchUpcomingTasks = async () => {\n  try {\n    const response = await fetch(`/api/dashboard/upcoming-tasks?days=${selectedPeriod.value}&limit=5`)\n    if (!response.ok) throw new Error('Failed to fetch upcoming tasks')\n    const data = await response.json()\n    upcomingTasks.value = data.data.tasks\n  } catch (error) {\n    console.error('Error fetching upcoming tasks:', error)\n    upcomingTasks.value = []\n  }\n}\n\nconst fetchRecentActivities = async () => {\n  try {\n    const response = await fetch('/api/dashboard/recent-activities?limit=5')\n    if (!response.ok) throw new Error('Failed to fetch recent activities')\n    const data = await response.json()\n    recentActivities.value = data.data.activities\n  } catch (error) {\n    console.error('Error fetching recent activities:', error)\n    recentActivities.value = []\n  }\n}\n\nconst fetchKpis = async () => {\n  try {\n    const response = await fetch('/api/dashboard/kpis?limit=3')\n    if (!response.ok) throw new Error('Failed to fetch KPIs')\n    const data = await response.json()\n    kpis.value = data.data.kpis\n  } catch (error) {\n    console.error('Error fetching KPIs:', error)\n    kpis.value = []\n  }\n}\n\nconst fetchProjectChart = async () => {\n  try {\n    const response = await fetch('/api/dashboard/charts/project-status')\n    if (!response.ok) throw new Error('Failed to fetch project chart data')\n    const data = await response.json()\n    updateProjectChart(data.data.chart)\n  } catch (error) {\n    console.error('Error fetching project chart:', error)\n  }\n}\n\nconst fetchTaskChart = async () => {\n  try {\n    const response = await fetch('/api/dashboard/charts/task-status')\n    if (!response.ok) throw new Error('Failed to fetch task chart data')\n    const data = await response.json()\n    updateTaskChart(data.data.chart)\n  } catch (error) {\n    console.error('Error fetching task chart:', error)\n  }\n}\n\n// Chart functions\nconst updateProjectChart = (chartData) => {\n  if (!projectChart.value) return\n\n  const ctx = projectChart.value.getContext('2d')\n\n  if (projectChartInstance) {\n    projectChartInstance.destroy()\n  }\n\n  projectChartInstance = new Chart(ctx, {\n    type: 'doughnut',\n    data: {\n      labels: chartData.labels,\n      datasets: [{\n        data: chartData.data,\n        backgroundColor: [\n          '#3B82F6', // Blue\n          '#10B981', // Green\n          '#F59E0B', // Yellow\n          '#EF4444', // Red\n          '#8B5CF6'  // Purple\n        ],\n        borderWidth: 2,\n        borderColor: '#ffffff'\n      }]\n    },\n    options: {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          position: 'bottom',\n          labels: {\n            padding: 20,\n            usePointStyle: true\n          }\n        }\n      }\n    }\n  })\n}\n\nconst updateTaskChart = (chartData) => {\n  if (!taskChart.value) return\n\n  const ctx = taskChart.value.getContext('2d')\n\n  if (taskChartInstance) {\n    taskChartInstance.destroy()\n  }\n\n  taskChartInstance = new Chart(ctx, {\n    type: 'bar',\n    data: {\n      labels: chartData.labels,\n      datasets: [{\n        label: 'Tasks',\n        data: chartData.data,\n        backgroundColor: [\n          '#60A5FA', // Light blue\n          '#34D399', // Light green\n          '#FBBF24', // Light yellow\n          '#F87171'  // Light red\n        ],\n        borderColor: [\n          '#3B82F6',\n          '#10B981',\n          '#F59E0B',\n          '#EF4444'\n        ],\n        borderWidth: 1\n      }]\n    },\n    options: {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        y: {\n          beginAtZero: true,\n          ticks: {\n            stepSize: 1\n          }\n        }\n      }\n    }\n  })\n}\n\n// Utility functions\nconst refreshData = async () => {\n  isLoading.value = true\n  try {\n    await Promise.all([\n      fetchDashboardStats(),\n      fetchUpcomingTasks(),\n      fetchRecentActivities(),\n      fetchKpis(),\n      fetchProjectChart(),\n      fetchTaskChart()\n    ])\n  } finally {\n    isLoading.value = false\n  }\n}\n\nconst formatDate = (dateString) => {\n  return new Date(dateString).toLocaleDateString('it-IT')\n}\n\nconst formatTimestamp = (timestamp) => {\n  const date = new Date(timestamp)\n  const now = new Date()\n  const diffInMinutes = Math.floor((now - date) / (1000 * 60))\n\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minuti fa`\n  } else if (diffInMinutes < 1440) {\n    return `${Math.floor(diffInMinutes / 60)} ore fa`\n  } else {\n    return `${Math.floor(diffInMinutes / 1440)} giorni fa`\n  }\n}\n\nconst getPriorityClasses = (priority) => {\n  const classes = {\n    high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n  }\n  return classes[priority] || classes.medium\n}\n\nconst getStatusClasses = (status) => {\n  const classes = {\n    'todo': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',\n    'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n    'review': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    'done': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n  }\n  return classes[status] || classes.todo\n}\n\nconst getActivityIcon = (type) => {\n  const icons = {\n    task: `<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n      </svg>`,\n    timesheet: `<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>`,\n    event: `<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n      </svg>`\n  }\n  return icons[type] || icons.task\n}\n\nconst getActivityIconClasses = (type) => {\n  const classes = {\n    task: 'bg-blue-100 text-blue-600',\n    timesheet: 'bg-green-100 text-green-600',\n    event: 'bg-purple-100 text-purple-600'\n  }\n  return classes[type] || classes.task\n}\n\nconst getKpiProgressClasses = (percentage) => {\n  if (percentage >= 90) return 'bg-green-500'\n  if (percentage >= 70) return 'bg-yellow-500'\n  return 'bg-red-500'\n}\n\n// Lifecycle\nonMounted(async () => {\n  await refreshData()\n\n  // Initialize charts after DOM update\n  await nextTick()\n  if (projectChart.value && taskChart.value) {\n    await fetchProjectChart()\n    await fetchTaskChart()\n  }\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"py-6\">\n    <!-- Dashboard Header -->\n    <div class=\"flex flex-col md:flex-row md:items-center md:justify-between mb-8\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Dashboard</h1>\n        <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n          Benvenuto! Ecco una panoramica delle attività della tua azienda.\n        </p>\n      </div>\n      <div class=\"mt-4 md:mt-0 flex space-x-3\">\n        <div class=\"relative\">\n          <select\n            v-model=\"selectedPeriod\"\n            @change=\"refreshData\"\n            class=\"bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md py-2 pl-3 pr-10 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500\"\n          >\n            <option value=\"7\">Ultimi 7 giorni</option>\n            <option value=\"30\">Ultimo mese</option>\n            <option value=\"90\">Ultimi 3 mesi</option>\n          </select>\n        </div>\n\n        <button\n          @click=\"refreshData\"\n          :disabled=\"isLoading\"\n          type=\"button\"\n          class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50\"\n        >\n          <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-2\" :class=\"{ 'animate-spin': isLoading }\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n            <path fill-rule=\"evenodd\" d=\"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\" clip-rule=\"evenodd\" />\n          </svg>\n          Aggiorna\n        </button>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n      <!-- Active Projects -->\n      <StatsCard\n        title=\"Progetti Attivi\"\n        :value=\"stats.projects?.active || 0\"\n        :subtitle=\"`di ${stats.projects?.total || 0} totali`\"\n        icon=\"project\"\n        color=\"primary\"\n        :link=\"'/app/projects?status=active'\"\n      />\n\n      <!-- Clients -->\n      <StatsCard\n        title=\"Clienti\"\n        :value=\"stats.team?.clients || 0\"\n        icon=\"users\"\n        color=\"secondary\"\n        :link=\"'/app/crm/clients'\"\n      />\n\n      <!-- Pending Tasks -->\n      <StatsCard\n        title=\"Task Pendenti\"\n        :value=\"stats.tasks?.pending || 0\"\n        :subtitle=\"`${stats.tasks?.overdue || 0} in ritardo`\"\n        icon=\"clock\"\n        :color=\"stats.tasks?.overdue > 0 ? 'red' : 'yellow'\"\n        :link=\"'/app/tasks?status=pending'\"\n      />\n\n      <!-- Team Members -->\n      <StatsCard\n        title=\"Team Members\"\n        :value=\"stats.team?.users || 0\"\n        :subtitle=\"`${stats.team?.departments || 0} dipartimenti`\"\n        icon=\"team\"\n        color=\"blue\"\n        :link=\"'/app/personnel'\"\n      />\n    </div>\n\n    <!-- Charts Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n      <!-- Project Status Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white\">Stato Progetti</h2>\n        </div>\n        <div class=\"relative h-64\">\n          <canvas ref=\"projectChart\"></canvas>\n        </div>\n      </div>\n\n      <!-- Task Status Chart -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white\">Stato Attività</h2>\n        </div>\n        <div class=\"relative h-64\">\n          <canvas ref=\"taskChart\"></canvas>\n        </div>\n      </div>\n    </div>\n\n    <!-- Activities Section -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n      <!-- Upcoming Tasks -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"p-6\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Attività in Scadenza</h2>\n          <div v-if=\"upcomingTasks.length === 0\" class=\"text-center py-8 text-gray-500\">\n            Nessuna attività in scadenza\n          </div>\n          <div v-else class=\"space-y-4\">\n            <div\n              v-for=\"task in upcomingTasks\"\n              :key=\"task.id\"\n              class=\"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0\"\n            >\n              <div class=\"flex justify-between items-start\">\n                <div class=\"flex-1\">\n                  <h3 class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ task.name }}</h3>\n                  <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ task.project_name }}</p>\n                </div>\n                <span\n                  class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                  :class=\"getPriorityClasses(task.priority)\"\n                >\n                  {{ task.priority }}\n                </span>\n              </div>\n              <div class=\"mt-2 flex justify-between items-center\">\n                <span class=\"text-xs text-gray-500 dark:text-gray-400\">\n                  Scadenza: {{ formatDate(task.due_date) }}\n                </span>\n                <span\n                  class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium\"\n                  :class=\"getStatusClasses(task.status)\"\n                >\n                  {{ task.status }}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"bg-gray-50 dark:bg-gray-700 px-6 py-3\">\n          <div class=\"text-sm\">\n            <router-link to=\"/app/tasks\" class=\"font-medium text-primary-600 dark:text-primary-400 hover:text-primary-500\">\n              Vedi tutte le attività\n            </router-link>\n          </div>\n        </div>\n      </div>\n\n      <!-- Recent Activities -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"p-6\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Attività Recenti</h2>\n          <div v-if=\"recentActivities.length === 0\" class=\"text-center py-8 text-gray-500\">\n            Nessuna attività recente\n          </div>\n          <div v-else class=\"space-y-4\">\n            <div\n              v-for=\"activity in recentActivities\"\n              :key=\"`${activity.type}-${activity.id}`\"\n              class=\"flex items-start space-x-3\"\n            >\n              <div class=\"flex-shrink-0\">\n                <div\n                  class=\"w-8 h-8 rounded-full flex items-center justify-center\"\n                  :class=\"getActivityIconClasses(activity.type)\"\n                >\n                  <div class=\"w-4 h-4\" v-html=\"getActivityIcon(activity.type)\"></div>\n                </div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <p class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ activity.title }}</p>\n                <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ activity.description }}</p>\n                <p class=\"text-xs text-gray-400 dark:text-gray-500\">{{ formatTimestamp(activity.timestamp) }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- KPIs -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n        <div class=\"p-6\">\n          <h2 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">KPIs Principali</h2>\n          <div v-if=\"kpis.length === 0\" class=\"text-center py-8 text-gray-500\">\n            Nessun KPI configurato\n          </div>\n          <div v-else class=\"space-y-4\">\n            <div\n              v-for=\"kpi in kpis\"\n              :key=\"kpi.id\"\n              class=\"border-b border-gray-200 dark:border-gray-700 pb-4 last:border-b-0 last:pb-0\"\n            >\n              <div class=\"flex justify-between items-start\">\n                <div class=\"flex-1\">\n                  <h3 class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ kpi.name }}</h3>\n                  <p class=\"text-xs text-gray-500 dark:text-gray-400\">{{ kpi.description }}</p>\n                </div>\n                <div class=\"text-right\">\n                  <p class=\"text-sm font-bold text-gray-900 dark:text-white\">\n                    {{ kpi.current_value }}{{ kpi.unit }}\n                  </p>\n                  <p class=\"text-xs text-gray-500\">\n                    Target: {{ kpi.target_value }}{{ kpi.unit }}\n                  </p>\n                </div>\n              </div>\n              <div class=\"mt-2\">\n                <div class=\"w-full bg-gray-200 rounded-full h-2\">\n                  <div\n                    class=\"h-2 rounded-full\"\n                    :class=\"getKpiProgressClasses(kpi.performance_percentage)\"\n                    :style=\"{ width: Math.min(kpi.performance_percentage, 100) + '%' }\"\n                  ></div>\n                </div>\n                <p class=\"text-xs text-gray-500 mt-1\">{{ Math.round(kpi.performance_percentage) }}% del target</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, nextTick } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useAuthStore } from '@/stores/auth'\nimport StatsCard from './components/StatsCard.vue'\nimport { Chart, registerables } from 'chart.js'\n\n// Register Chart.js components\nChart.register(...registerables)\n\nconst router = useRouter()\nconst authStore = useAuthStore()\n\n// Reactive data\nconst isLoading = ref(false)\nconst selectedPeriod = ref('7')\nconst stats = ref({})\nconst upcomingTasks = ref([])\nconst recentActivities = ref([])\nconst kpis = ref([])\n\n// Chart refs\nconst projectChart = ref(null)\nconst taskChart = ref(null)\nlet projectChartInstance = null\nlet taskChartInstance = null\n\n// API functions\nconst fetchDashboardStats = async () => {\n  try {\n    const response = await api.get('/api/dashboard/stats')\n    stats.value = response.data.data\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error)\n    stats.value = {}\n  }\n}\n\nconst fetchUpcomingTasks = async () => {\n  try {\n    const response = await api.get(`/api/dashboard/upcoming-tasks?days=${selectedPeriod.value}&limit=5`)\n    upcomingTasks.value = response.data.data.tasks\n  } catch (error) {\n    console.error('Error fetching upcoming tasks:', error)\n    upcomingTasks.value = []\n  }\n}\n\nconst fetchRecentActivities = async () => {\n  try {\n    const response = await fetch('/api/dashboard/recent-activities?limit=5')\n    if (!response.ok) throw new Error('Failed to fetch recent activities')\n    const data = await response.json()\n    recentActivities.value = data.data.activities\n  } catch (error) {\n    console.error('Error fetching recent activities:', error)\n    recentActivities.value = []\n  }\n}\n\nconst fetchKpis = async () => {\n  try {\n    const response = await fetch('/api/dashboard/kpis?limit=3')\n    if (!response.ok) throw new Error('Failed to fetch KPIs')\n    const data = await response.json()\n    kpis.value = data.data.kpis\n  } catch (error) {\n    console.error('Error fetching KPIs:', error)\n    kpis.value = []\n  }\n}\n\nconst fetchProjectChart = async () => {\n  try {\n    const response = await fetch('/api/dashboard/charts/project-status')\n    if (!response.ok) throw new Error('Failed to fetch project chart data')\n    const data = await response.json()\n    updateProjectChart(data.data.chart)\n  } catch (error) {\n    console.error('Error fetching project chart:', error)\n  }\n}\n\nconst fetchTaskChart = async () => {\n  try {\n    const response = await fetch('/api/dashboard/charts/task-status')\n    if (!response.ok) throw new Error('Failed to fetch task chart data')\n    const data = await response.json()\n    updateTaskChart(data.data.chart)\n  } catch (error) {\n    console.error('Error fetching task chart:', error)\n  }\n}\n\n// Chart functions\nconst updateProjectChart = (chartData) => {\n  if (!projectChart.value) return\n\n  const ctx = projectChart.value.getContext('2d')\n\n  if (projectChartInstance) {\n    projectChartInstance.destroy()\n  }\n\n  projectChartInstance = new Chart(ctx, {\n    type: 'doughnut',\n    data: {\n      labels: chartData.labels,\n      datasets: [{\n        data: chartData.data,\n        backgroundColor: [\n          '#3B82F6', // Blue\n          '#10B981', // Green\n          '#F59E0B', // Yellow\n          '#EF4444', // Red\n          '#8B5CF6'  // Purple\n        ],\n        borderWidth: 2,\n        borderColor: '#ffffff'\n      }]\n    },\n    options: {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          position: 'bottom',\n          labels: {\n            padding: 20,\n            usePointStyle: true\n          }\n        }\n      }\n    }\n  })\n}\n\nconst updateTaskChart = (chartData) => {\n  if (!taskChart.value) return\n\n  const ctx = taskChart.value.getContext('2d')\n\n  if (taskChartInstance) {\n    taskChartInstance.destroy()\n  }\n\n  taskChartInstance = new Chart(ctx, {\n    type: 'bar',\n    data: {\n      labels: chartData.labels,\n      datasets: [{\n        label: 'Tasks',\n        data: chartData.data,\n        backgroundColor: [\n          '#60A5FA', // Light blue\n          '#34D399', // Light green\n          '#FBBF24', // Light yellow\n          '#F87171'  // Light red\n        ],\n        borderColor: [\n          '#3B82F6',\n          '#10B981',\n          '#F59E0B',\n          '#EF4444'\n        ],\n        borderWidth: 1\n      }]\n    },\n    options: {\n      responsive: true,\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        y: {\n          beginAtZero: true,\n          ticks: {\n            stepSize: 1\n          }\n        }\n      }\n    }\n  })\n}\n\n// Utility functions\nconst refreshData = async () => {\n  isLoading.value = true\n  try {\n    await Promise.all([\n      fetchDashboardStats(),\n      fetchUpcomingTasks(),\n      fetchRecentActivities(),\n      fetchKpis(),\n      fetchProjectChart(),\n      fetchTaskChart()\n    ])\n  } finally {\n    isLoading.value = false\n  }\n}\n\nconst formatDate = (dateString) => {\n  return new Date(dateString).toLocaleDateString('it-IT')\n}\n\nconst formatTimestamp = (timestamp) => {\n  const date = new Date(timestamp)\n  const now = new Date()\n  const diffInMinutes = Math.floor((now - date) / (1000 * 60))\n\n  if (diffInMinutes < 60) {\n    return `${diffInMinutes} minuti fa`\n  } else if (diffInMinutes < 1440) {\n    return `${Math.floor(diffInMinutes / 60)} ore fa`\n  } else {\n    return `${Math.floor(diffInMinutes / 1440)} giorni fa`\n  }\n}\n\nconst getPriorityClasses = (priority) => {\n  const classes = {\n    high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',\n    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n  }\n  return classes[priority] || classes.medium\n}\n\nconst getStatusClasses = (status) => {\n  const classes = {\n    'todo': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',\n    'in-progress': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',\n    'review': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',\n    'done': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n  }\n  return classes[status] || classes.todo\n}\n\nconst getActivityIcon = (type) => {\n  const icons = {\n    task: `<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n      </svg>`,\n    timesheet: `<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n      </svg>`,\n    event: `<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n      </svg>`\n  }\n  return icons[type] || icons.task\n}\n\nconst getActivityIconClasses = (type) => {\n  const classes = {\n    task: 'bg-blue-100 text-blue-600',\n    timesheet: 'bg-green-100 text-green-600',\n    event: 'bg-purple-100 text-purple-600'\n  }\n  return classes[type] || classes.task\n}\n\nconst getKpiProgressClasses = (percentage) => {\n  if (percentage >= 90) return 'bg-green-500'\n  if (percentage >= 70) return 'bg-yellow-500'\n  return 'bg-red-500'\n}\n\n// Lifecycle\nonMounted(async () => {\n  await refreshData()\n\n  // Initialize charts after DOM update\n  await nextTick()\n  if (projectChart.value && taskChart.value) {\n    await fetchProjectChart()\n    await fetchTaskChart()\n  }\n})\n</script>"}