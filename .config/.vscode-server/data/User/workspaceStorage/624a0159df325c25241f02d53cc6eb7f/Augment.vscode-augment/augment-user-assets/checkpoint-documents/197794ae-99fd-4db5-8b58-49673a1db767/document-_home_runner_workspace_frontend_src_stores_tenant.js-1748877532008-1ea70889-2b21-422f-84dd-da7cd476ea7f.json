{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/stores/tenant.js"}, "originalCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport api from '@/utils/api'\n\nexport const useTenantStore = defineStore('tenant', () => {\n  const config = ref(null)\n  const loading = ref(false)\n  const error = ref(null)\n\n  const company = computed(() => config.value?.company || {})\n  const contact = computed(() => config.value?.contact || {})\n  const pages = computed(() => config.value?.pages || {})\n  const navigation = computed(() => config.value?.navigation || {})\n  const footer = computed(() => config.value?.footer || {})\n\n  async function loadConfig() {\n    try {\n      loading.value = true\n      // Controlla se la configurazione è già disponibile globalmente\n      if (window.TENANT_CONFIG) {\n        config.value = window.TENANT_CONFIG\n        return\n      }\n      \n      // Altrimenti carica dall'API\n      const response = await fetch('/api/config/tenant')\n      config.value = await response.json()\n    } catch (err) {\n      error.value = 'Errore nel caricamento della configurazione'\n      console.error('Errore caricamento tenant config:', err)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  // Funzione helper per sostituire variabili nei testi\n  function interpolateText(text, variables = {}) {\n    if (!text || typeof text !== 'string') return text\n    \n    let result = text\n    \n    // Sostituzioni predefinite\n    const defaultVars = {\n      'company.name': company.value.name || 'DatVinci',\n      'company.tagline': company.value.tagline || '',\n      'company.description': company.value.description || '',\n      'company.mission': company.value.mission || '',\n      'company.vision': company.value.vision || '',\n      'company.founded': company.value.founded || '',\n      'company.team_size': company.value.team_size || '',\n      'contact.email': contact.value.email || '',\n      'contact.phone': contact.value.phone || '',\n      'contact.address': contact.value.address || '',\n      'current_year': new Date().getFullYear().toString(),\n      ...variables\n    }\n    \n    // Sostituisci tutte le variabili nel formato {variable.name}\n    for (const [key, value] of Object.entries(defaultVars)) {\n      const regex = new RegExp(`\\\\{${key}\\\\}`, 'g')\n      result = result.replace(regex, value || '')\n    }\n    \n    return result\n  }\n\n  return {\n    config,\n    loading,\n    error,\n    company,\n    contact,\n    pages,\n    navigation,\n    footer,\n    loadConfig,\n    interpolateText\n  }\n})", "modifiedCode": "import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport api from '@/utils/api'\n\nexport const useTenantStore = defineStore('tenant', () => {\n  const config = ref(null)\n  const loading = ref(false)\n  const error = ref(null)\n\n  const company = computed(() => config.value?.company || {})\n  const contact = computed(() => config.value?.contact || {})\n  const pages = computed(() => config.value?.pages || {})\n  const navigation = computed(() => config.value?.navigation || {})\n  const footer = computed(() => config.value?.footer || {})\n\n  async function loadConfig() {\n    try {\n      loading.value = true\n      // Controlla se la configurazione è già disponibile globalmente\n      if (window.TENANT_CONFIG) {\n        config.value = window.TENANT_CONFIG\n        return\n      }\n      \n      // Altrimenti carica dall'API\n      const response = await fetch('/api/config/tenant')\n      config.value = await response.json()\n    } catch (err) {\n      error.value = 'Errore nel caricamento della configurazione'\n      console.error('Errore caricamento tenant config:', err)\n    } finally {\n      loading.value = false\n    }\n  }\n\n  // Funzione helper per sostituire variabili nei testi\n  function interpolateText(text, variables = {}) {\n    if (!text || typeof text !== 'string') return text\n    \n    let result = text\n    \n    // Sostituzioni predefinite\n    const defaultVars = {\n      'company.name': company.value.name || 'DatVinci',\n      'company.tagline': company.value.tagline || '',\n      'company.description': company.value.description || '',\n      'company.mission': company.value.mission || '',\n      'company.vision': company.value.vision || '',\n      'company.founded': company.value.founded || '',\n      'company.team_size': company.value.team_size || '',\n      'contact.email': contact.value.email || '',\n      'contact.phone': contact.value.phone || '',\n      'contact.address': contact.value.address || '',\n      'current_year': new Date().getFullYear().toString(),\n      ...variables\n    }\n    \n    // Sostituisci tutte le variabili nel formato {variable.name}\n    for (const [key, value] of Object.entries(defaultVars)) {\n      const regex = new RegExp(`\\\\{${key}\\\\}`, 'g')\n      result = result.replace(regex, value || '')\n    }\n    \n    return result\n  }\n\n  return {\n    config,\n    loading,\n    error,\n    company,\n    contact,\n    pages,\n    navigation,\n    footer,\n    loadConfig,\n    interpolateText\n  }\n})"}