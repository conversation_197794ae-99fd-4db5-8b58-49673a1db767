{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/admin/KPITemplates.vue"}, "originalCode": "<template>\n  <div>\n    <!-- Header -->\n    <div class=\"flex justify-between items-center mb-6\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Template KPI</h1>\n        <p class=\"text-gray-600 dark:text-gray-400\">Configura KPI di default per tipologie progetto</p>\n      </div>\n      <div class=\"flex space-x-3\">\n        <button \n          @click=\"showCreateModal = true\"\n          class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n        >\n          + Nuovo Template\n        </button>\n        <button \n          @click=\"resetToDefaults()\"\n          class=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n        >\n          🔄 Reset Default\n        </button>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"mb-6\">\n      <div class=\"flex space-x-4 mb-3\">\n        <select \n          v-model=\"selectedType\" \n          @change=\"filterTemplates()\"\n          class=\"border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n        >\n          <option value=\"\">Tutte le tipologie</option>\n          <option \n            v-for=\"type in projectTypes\" \n            :key=\"type.key\" \n            :value=\"type.key\"\n          >\n            {{ type.name }}\n          </option>\n        </select>\n\n        <select \n          v-model=\"selectedKPI\" \n          @change=\"filterTemplates()\"\n          class=\"border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n        >\n          <option value=\"\">Tutti i KPI</option>\n          <option value=\"margin_percentage\">Margine Netto</option>\n          <option value=\"utilization_rate\">Utilization Rate</option>\n          <option value=\"cost_per_hour\">Costo per Ora</option>\n          <option value=\"cost_revenue_ratio\">Rapporto C/R</option>\n        </select>\n\n        <!-- Reset filtri -->\n        <button \n          v-if=\"selectedType || selectedKPI\"\n          @click=\"resetFilters()\"\n          class=\"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50\"\n        >\n          🗑️ Reset\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading -->\n    <div v-if=\"loading\" class=\"flex justify-center py-8\">\n      <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n    </div>\n\n    <!-- Error -->\n    <div v-if=\"error\" class=\"bg-red-50 border border-red-200 rounded-md p-4 mb-6\">\n      <p class=\"text-red-600\">{{ error }}</p>\n    </div>\n\n    <!-- Tabella Template -->\n    <div v-if=\"!loading\" class=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\n      <ul class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <li v-for=\"template in filteredTemplates\" :key=\"template.id\" class=\"px-6 py-4\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex-1\">\n              <div class=\"flex items-center\">\n                <h3 class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ getProjectTypeName(template.project_type) }} - {{ template.display_name }}\n                </h3>\n                <span v-if=\"!template.is_active\" class=\"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                  Inattivo\n                </span>\n              </div>\n              <div class=\"mt-1 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n                <span>Target: {{ template.target_min }} - {{ template.target_max }} {{ template.unit }}</span>\n                <span v-if=\"template.warning_threshold\">Warning: {{ template.warning_threshold }} {{ template.unit }}</span>\n              </div>\n              <p v-if=\"template.description\" class=\"mt-1 text-sm text-gray-600 dark:text-gray-300\">\n                {{ template.description }}\n              </p>\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <button \n                @click=\"editTemplate(template)\"\n                class=\"text-primary-600 hover:text-primary-900 text-sm font-medium\"\n              >\n                Modifica\n              </button>\n              <button \n                @click=\"toggleTemplate(template)\"\n                class=\"text-gray-600 hover:text-gray-900 text-sm font-medium\"\n              >\n                {{ template.is_active ? 'Disattiva' : 'Attiva' }}\n              </button>\n              <button \n                @click=\"deleteTemplate(template)\"\n                class=\"text-red-600 hover:text-red-900 text-sm font-medium\"\n              >\n                Elimina\n              </button>\n            </div>\n          </div>\n        </li>\n      </ul>\n\n      <!-- Empty state -->\n      <div v-if=\"filteredTemplates.length === 0\" class=\"text-center py-12\">\n        <p class=\"text-gray-500 dark:text-gray-400\">Nessun template trovato</p>\n      </div>\n    </div>\n\n    <!-- Modal Creazione/Modifica -->\n    <div v-if=\"showCreateModal || showEditModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ showEditModal ? 'Modifica Template KPI' : 'Nuovo Template KPI' }}\n          </h3>\n          \n          <form @submit.prevent=\"saveTemplate\">\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Tipologia Progetto\n              </label>\n              <select \n                v-model=\"formData.project_type\" \n                required\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"\">Seleziona tipologia</option>\n                <option \n                  v-for=\"type in projectTypes\" \n                  :key=\"type.key\" \n                  :value=\"type.key\"\n                >\n                  {{ type.name }}\n                </option>\n              </select>\n            </div>\n\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                KPI\n              </label>\n              <select \n                v-model=\"formData.kpi_name\" \n                required\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"\">Seleziona KPI</option>\n                <option value=\"margin_percentage\">Margine Netto</option>\n                <option value=\"utilization_rate\">Utilization Rate</option>\n                <option value=\"cost_per_hour\">Costo per Ora</option>\n                <option value=\"cost_revenue_ratio\">Rapporto C/R</option>\n              </select>\n            </div>\n\n            <div class=\"grid grid-cols-2 gap-4 mb-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Target Min\n                </label>\n                <input \n                  v-model=\"formData.target_min\" \n                  type=\"number\" \n                  step=\"0.01\"\n                  class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                >\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Target Max\n                </label>\n                <input \n                  v-model=\"formData.target_max\" \n                  type=\"number\" \n                  step=\"0.01\"\n                  class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                >\n              </div>\n            </div>\n\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Soglia Warning\n              </label>\n              <input \n                v-model=\"formData.warning_threshold\" \n                type=\"number\" \n                step=\"0.01\"\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              >\n            </div>\n\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Unità\n              </label>\n              <select \n                v-model=\"formData.unit\"\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"%\">%</option>\n                <option value=\"€\">€</option>\n                <option value=\"ratio\">ratio</option>\n                <option value=\"giorni\">giorni</option>\n              </select>\n            </div>\n\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Descrizione\n              </label>\n              <textarea \n                v-model=\"formData.description\" \n                rows=\"3\"\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              ></textarea>\n            </div>\n\n            <div class=\"flex justify-end space-x-3\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md\"\n              >\n                {{ showEditModal ? 'Aggiorna' : 'Crea' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst templates = ref([])\nconst loading = ref(false)\nconst error = ref('')\n\n// Filtri\nconst selectedType = ref('')\nconst selectedKPI = ref('')\n\n// Modal\nconst showCreateModal = ref(false)\nconst showEditModal = ref(false)\nconst editingTemplate = ref(null)\n\n// Form data\nconst formData = ref({\n  project_type: '',\n  kpi_name: '',\n  target_min: null,\n  target_max: null,\n  warning_threshold: null,\n  unit: '%',\n  description: ''\n})\n\n// Project types\nconst projectTypes = ref([\n  { key: 'service', name: 'Servizio' },\n  { key: 'license', name: 'Licenza' },\n  { key: 'consulting', name: 'Consulenza' },\n  { key: 'product', name: 'Prodotto' },\n  { key: 'rd', name: 'R&D' },\n  { key: 'internal', name: 'Interno' }\n])\n\n// Computed\nconst filteredTemplates = computed(() => {\n  let filtered = templates.value\n\n  if (selectedType.value) {\n    filtered = filtered.filter(t => t.project_type === selectedType.value)\n  }\n\n  if (selectedKPI.value) {\n    filtered = filtered.filter(t => t.kpi_name === selectedKPI.value)\n  }\n\n  return filtered\n})\n\n// Methods\nconst fetchTemplates = async () => {\n  loading.value = true\n  error.value = ''\n\n  try {\n    const response = await api.get('/api/admin/kpi-templates')\n    templates.value = response.data.data || response.data\n  } catch (err) {\n    error.value = err.response?.data?.message || err.message || 'Errore nel caricamento dei template'\n  } finally {\n    loading.value = false\n  }\n}\n\nconst saveTemplate = async () => {\n  try {\n    const url = showEditModal.value \n      ? `/api/admin/kpi-templates/${editingTemplate.value.id}`\n      : '/api/admin/kpi-templates'\n    \n    const method = showEditModal.value ? 'PUT' : 'POST'\n\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(formData.value)\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio del template')\n    }\n\n    await fetchTemplates()\n    closeModal()\n  } catch (err) {\n    error.value = err.message\n  }\n}\n\nconst editTemplate = (template) => {\n  editingTemplate.value = template\n  formData.value = { ...template }\n  showEditModal.value = true\n}\n\nconst toggleTemplate = async (template) => {\n  try {\n    const response = await fetch(`/api/admin/kpi-templates/${template.id}/toggle`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel toggle del template')\n    }\n\n    await fetchTemplates()\n  } catch (err) {\n    error.value = err.message\n  }\n}\n\nconst deleteTemplate = async (template) => {\n  if (!confirm('Sei sicuro di voler eliminare questo template?')) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/admin/kpi-templates/${template.id}`, {\n      method: 'DELETE',\n      headers: {\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nell\\'eliminazione del template')\n    }\n\n    await fetchTemplates()\n  } catch (err) {\n    error.value = err.message\n  }\n}\n\nconst resetToDefaults = async () => {\n  if (!confirm('Sei sicuro di voler ripristinare i template di default? Questa azione eliminerà tutti i template personalizzati.')) {\n    return\n  }\n\n  try {\n    const response = await fetch('/api/admin/kpi-templates/reset', {\n      method: 'POST',\n      headers: {\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel reset dei template')\n    }\n\n    await fetchTemplates()\n  } catch (err) {\n    error.value = err.message\n  }\n}\n\nconst filterTemplates = () => {\n  // Filtri applicati automaticamente dal computed\n}\n\nconst resetFilters = () => {\n  selectedType.value = ''\n  selectedKPI.value = ''\n}\n\nconst closeModal = () => {\n  showCreateModal.value = false\n  showEditModal.value = false\n  editingTemplate.value = null\n  formData.value = {\n    project_type: '',\n    kpi_name: '',\n    target_min: null,\n    target_max: null,\n    warning_threshold: null,\n    unit: '%',\n    description: ''\n  }\n}\n\nconst getProjectTypeName = (key) => {\n  const type = projectTypes.value.find(t => t.key === key)\n  return type ? type.name : key\n}\n\n// Lifecycle\nonMounted(() => {\n  fetchTemplates()\n})\n</script>", "modifiedCode": "<template>\n  <div>\n    <!-- Header -->\n    <div class=\"flex justify-between items-center mb-6\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Template KPI</h1>\n        <p class=\"text-gray-600 dark:text-gray-400\">Configura KPI di default per tipologie progetto</p>\n      </div>\n      <div class=\"flex space-x-3\">\n        <button \n          @click=\"showCreateModal = true\"\n          class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n        >\n          + Nuovo Template\n        </button>\n        <button \n          @click=\"resetToDefaults()\"\n          class=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n        >\n          🔄 Reset Default\n        </button>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"mb-6\">\n      <div class=\"flex space-x-4 mb-3\">\n        <select \n          v-model=\"selectedType\" \n          @change=\"filterTemplates()\"\n          class=\"border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n        >\n          <option value=\"\">Tutte le tipologie</option>\n          <option \n            v-for=\"type in projectTypes\" \n            :key=\"type.key\" \n            :value=\"type.key\"\n          >\n            {{ type.name }}\n          </option>\n        </select>\n\n        <select \n          v-model=\"selectedKPI\" \n          @change=\"filterTemplates()\"\n          class=\"border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n        >\n          <option value=\"\">Tutti i KPI</option>\n          <option value=\"margin_percentage\">Margine Netto</option>\n          <option value=\"utilization_rate\">Utilization Rate</option>\n          <option value=\"cost_per_hour\">Costo per Ora</option>\n          <option value=\"cost_revenue_ratio\">Rapporto C/R</option>\n        </select>\n\n        <!-- Reset filtri -->\n        <button \n          v-if=\"selectedType || selectedKPI\"\n          @click=\"resetFilters()\"\n          class=\"px-3 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50\"\n        >\n          🗑️ Reset\n        </button>\n      </div>\n    </div>\n\n    <!-- Loading -->\n    <div v-if=\"loading\" class=\"flex justify-center py-8\">\n      <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n    </div>\n\n    <!-- Error -->\n    <div v-if=\"error\" class=\"bg-red-50 border border-red-200 rounded-md p-4 mb-6\">\n      <p class=\"text-red-600\">{{ error }}</p>\n    </div>\n\n    <!-- Tabella Template -->\n    <div v-if=\"!loading\" class=\"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md\">\n      <ul class=\"divide-y divide-gray-200 dark:divide-gray-700\">\n        <li v-for=\"template in filteredTemplates\" :key=\"template.id\" class=\"px-6 py-4\">\n          <div class=\"flex items-center justify-between\">\n            <div class=\"flex-1\">\n              <div class=\"flex items-center\">\n                <h3 class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ getProjectTypeName(template.project_type) }} - {{ template.display_name }}\n                </h3>\n                <span v-if=\"!template.is_active\" class=\"ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                  Inattivo\n                </span>\n              </div>\n              <div class=\"mt-1 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n                <span>Target: {{ template.target_min }} - {{ template.target_max }} {{ template.unit }}</span>\n                <span v-if=\"template.warning_threshold\">Warning: {{ template.warning_threshold }} {{ template.unit }}</span>\n              </div>\n              <p v-if=\"template.description\" class=\"mt-1 text-sm text-gray-600 dark:text-gray-300\">\n                {{ template.description }}\n              </p>\n            </div>\n            <div class=\"flex items-center space-x-2\">\n              <button \n                @click=\"editTemplate(template)\"\n                class=\"text-primary-600 hover:text-primary-900 text-sm font-medium\"\n              >\n                Modifica\n              </button>\n              <button \n                @click=\"toggleTemplate(template)\"\n                class=\"text-gray-600 hover:text-gray-900 text-sm font-medium\"\n              >\n                {{ template.is_active ? 'Disattiva' : 'Attiva' }}\n              </button>\n              <button \n                @click=\"deleteTemplate(template)\"\n                class=\"text-red-600 hover:text-red-900 text-sm font-medium\"\n              >\n                Elimina\n              </button>\n            </div>\n          </div>\n        </li>\n      </ul>\n\n      <!-- Empty state -->\n      <div v-if=\"filteredTemplates.length === 0\" class=\"text-center py-12\">\n        <p class=\"text-gray-500 dark:text-gray-400\">Nessun template trovato</p>\n      </div>\n    </div>\n\n    <!-- Modal Creazione/Modifica -->\n    <div v-if=\"showCreateModal || showEditModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ showEditModal ? 'Modifica Template KPI' : 'Nuovo Template KPI' }}\n          </h3>\n          \n          <form @submit.prevent=\"saveTemplate\">\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Tipologia Progetto\n              </label>\n              <select \n                v-model=\"formData.project_type\" \n                required\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"\">Seleziona tipologia</option>\n                <option \n                  v-for=\"type in projectTypes\" \n                  :key=\"type.key\" \n                  :value=\"type.key\"\n                >\n                  {{ type.name }}\n                </option>\n              </select>\n            </div>\n\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                KPI\n              </label>\n              <select \n                v-model=\"formData.kpi_name\" \n                required\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"\">Seleziona KPI</option>\n                <option value=\"margin_percentage\">Margine Netto</option>\n                <option value=\"utilization_rate\">Utilization Rate</option>\n                <option value=\"cost_per_hour\">Costo per Ora</option>\n                <option value=\"cost_revenue_ratio\">Rapporto C/R</option>\n              </select>\n            </div>\n\n            <div class=\"grid grid-cols-2 gap-4 mb-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Target Min\n                </label>\n                <input \n                  v-model=\"formData.target_min\" \n                  type=\"number\" \n                  step=\"0.01\"\n                  class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                >\n              </div>\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Target Max\n                </label>\n                <input \n                  v-model=\"formData.target_max\" \n                  type=\"number\" \n                  step=\"0.01\"\n                  class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n                >\n              </div>\n            </div>\n\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Soglia Warning\n              </label>\n              <input \n                v-model=\"formData.warning_threshold\" \n                type=\"number\" \n                step=\"0.01\"\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              >\n            </div>\n\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Unità\n              </label>\n              <select \n                v-model=\"formData.unit\"\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              >\n                <option value=\"%\">%</option>\n                <option value=\"€\">€</option>\n                <option value=\"ratio\">ratio</option>\n                <option value=\"giorni\">giorni</option>\n              </select>\n            </div>\n\n            <div class=\"mb-4\">\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Descrizione\n              </label>\n              <textarea \n                v-model=\"formData.description\" \n                rows=\"3\"\n                class=\"w-full border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500\"\n              ></textarea>\n            </div>\n\n            <div class=\"flex justify-end space-x-3\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md\"\n              >\n                {{ showEditModal ? 'Aggiorna' : 'Crea' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\nimport api from '@/utils/api'\n\nconst authStore = useAuthStore()\n\n// State\nconst templates = ref([])\nconst loading = ref(false)\nconst error = ref('')\n\n// Filtri\nconst selectedType = ref('')\nconst selectedKPI = ref('')\n\n// Modal\nconst showCreateModal = ref(false)\nconst showEditModal = ref(false)\nconst editingTemplate = ref(null)\n\n// Form data\nconst formData = ref({\n  project_type: '',\n  kpi_name: '',\n  target_min: null,\n  target_max: null,\n  warning_threshold: null,\n  unit: '%',\n  description: ''\n})\n\n// Project types\nconst projectTypes = ref([\n  { key: 'service', name: 'Servizio' },\n  { key: 'license', name: 'Licenza' },\n  { key: 'consulting', name: 'Consulenza' },\n  { key: 'product', name: 'Prodotto' },\n  { key: 'rd', name: 'R&D' },\n  { key: 'internal', name: 'Interno' }\n])\n\n// Computed\nconst filteredTemplates = computed(() => {\n  let filtered = templates.value\n\n  if (selectedType.value) {\n    filtered = filtered.filter(t => t.project_type === selectedType.value)\n  }\n\n  if (selectedKPI.value) {\n    filtered = filtered.filter(t => t.kpi_name === selectedKPI.value)\n  }\n\n  return filtered\n})\n\n// Methods\nconst fetchTemplates = async () => {\n  loading.value = true\n  error.value = ''\n\n  try {\n    const response = await api.get('/api/admin/kpi-templates')\n    templates.value = response.data.data || response.data\n  } catch (err) {\n    error.value = err.response?.data?.message || err.message || 'Errore nel caricamento dei template'\n  } finally {\n    loading.value = false\n  }\n}\n\nconst saveTemplate = async () => {\n  try {\n    const url = showEditModal.value \n      ? `/api/admin/kpi-templates/${editingTemplate.value.id}`\n      : '/api/admin/kpi-templates'\n    \n    const method = showEditModal.value ? 'PUT' : 'POST'\n\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(formData.value)\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio del template')\n    }\n\n    await fetchTemplates()\n    closeModal()\n  } catch (err) {\n    error.value = err.message\n  }\n}\n\nconst editTemplate = (template) => {\n  editingTemplate.value = template\n  formData.value = { ...template }\n  showEditModal.value = true\n}\n\nconst toggleTemplate = async (template) => {\n  try {\n    const response = await fetch(`/api/admin/kpi-templates/${template.id}/toggle`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel toggle del template')\n    }\n\n    await fetchTemplates()\n  } catch (err) {\n    error.value = err.message\n  }\n}\n\nconst deleteTemplate = async (template) => {\n  if (!confirm('Sei sicuro di voler eliminare questo template?')) {\n    return\n  }\n\n  try {\n    const response = await fetch(`/api/admin/kpi-templates/${template.id}`, {\n      method: 'DELETE',\n      headers: {\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nell\\'eliminazione del template')\n    }\n\n    await fetchTemplates()\n  } catch (err) {\n    error.value = err.message\n  }\n}\n\nconst resetToDefaults = async () => {\n  if (!confirm('Sei sicuro di voler ripristinare i template di default? Questa azione eliminerà tutti i template personalizzati.')) {\n    return\n  }\n\n  try {\n    const response = await fetch('/api/admin/kpi-templates/reset', {\n      method: 'POST',\n      headers: {\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel reset dei template')\n    }\n\n    await fetchTemplates()\n  } catch (err) {\n    error.value = err.message\n  }\n}\n\nconst filterTemplates = () => {\n  // Filtri applicati automaticamente dal computed\n}\n\nconst resetFilters = () => {\n  selectedType.value = ''\n  selectedKPI.value = ''\n}\n\nconst closeModal = () => {\n  showCreateModal.value = false\n  showEditModal.value = false\n  editingTemplate.value = null\n  formData.value = {\n    project_type: '',\n    kpi_name: '',\n    target_min: null,\n    target_max: null,\n    warning_threshold: null,\n    unit: '%',\n    description: ''\n  }\n}\n\nconst getProjectTypeName = (key) => {\n  const type = projectTypes.value.find(t => t.key === key)\n  return type ? type.name : key\n}\n\n// Lifecycle\nonMounted(() => {\n  fetchTemplates()\n})\n</script>"}