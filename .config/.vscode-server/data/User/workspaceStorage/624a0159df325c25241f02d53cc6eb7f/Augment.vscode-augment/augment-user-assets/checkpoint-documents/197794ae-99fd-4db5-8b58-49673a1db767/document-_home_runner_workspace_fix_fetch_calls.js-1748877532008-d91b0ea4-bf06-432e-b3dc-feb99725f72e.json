{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "fix_fetch_calls.js"}, "originalCode": "#!/usr/bin/env node\n\nconst fs = require('fs');\nconst path = require('path');\n\n// Lista dei file da correggere\nconst filesToFix = [\n  'frontend/src/components/projects/ProjectTasks.vue',\n  'frontend/src/views/projects/components/ProjectTasks.vue',\n  'frontend/src/views/projects/components/ProjectTimesheet.vue',\n  'frontend/src/views/projects/components/ProjectTeam.vue',\n  'frontend/src/views/projects/components/ProjectExpenses.vue',\n  'frontend/src/views/projects/components/ExpenseModal.vue',\n  'frontend/src/views/projects/components/ProjectResourceAllocation.vue',\n  'frontend/src/views/projects/ProjectCreate.vue',\n  'frontend/src/views/projects/ProjectEdit.vue',\n  'frontend/src/views/personnel/PersonnelDirectory.vue',\n  'frontend/src/views/personnel/DepartmentList.vue',\n  'frontend/src/views/personnel/DepartmentCreate.vue',\n  'frontend/src/views/personnel/DepartmentView.vue',\n  'frontend/src/views/personnel/PersonnelAdmin.vue',\n  'frontend/src/views/personnel/PersonnelOrgChart.vue',\n  'frontend/src/views/personnel/PersonnelProfile.vue',\n  'frontend/src/views/personnel/components/admin/UsersManagement.vue',\n  'frontend/src/views/personnel/components/admin/DepartmentsManagement.vue',\n  'frontend/src/views/personnel/components/admin/SkillsManagement.vue',\n  'frontend/src/views/personnel/components/admin/BulkOperations.vue',\n  'frontend/src/views/personnel/components/admin/CreateUserModal.vue',\n  'frontend/src/views/personnel/components/admin/DepartmentModal.vue',\n  'frontend/src/views/personnel/components/admin/SkillModal.vue',\n  'frontend/src/views/personnel/components/CVTab.vue',\n  'frontend/src/views/personnel/PersonnelAllocation.vue',\n  'frontend/src/views/user/Profile.vue',\n  'frontend/src/views/user/Settings.vue',\n  'frontend/src/stores/tenant.js'\n];\n\nfunction fixFile(filePath) {\n  console.log(`Fixing ${filePath}...`);\n  \n  if (!fs.existsSync(filePath)) {\n    console.log(`File ${filePath} does not exist, skipping...`);\n    return;\n  }\n  \n  let content = fs.readFileSync(filePath, 'utf8');\n  let modified = false;\n  \n  // 1. Aggiungi import di api se non presente\n  if (content.includes('fetch(') && !content.includes(\"import api from '@/utils/api'\")) {\n    // Trova la sezione degli import\n    const importRegex = /(import .+ from .+\\n)+/;\n    const match = content.match(importRegex);\n    \n    if (match) {\n      const lastImport = match[0];\n      const newImport = lastImport + \"import api from '@/utils/api'\\n\";\n      content = content.replace(lastImport, newImport);\n      modified = true;\n    }\n  }\n  \n  // 2. Sostituisci fetch() con api.get/post/put/delete\n  \n  // Pattern per fetch GET\n  content = content.replace(\n    /const response = await fetch\\(([^,)]+)(?:,\\s*\\{\\s*(?:credentials:\\s*['\"]include['\"],?\\s*)?(?:headers:\\s*\\{[^}]*\\},?\\s*)?\\})?\\)/g,\n    'const response = await api.get($1)'\n  );\n  \n  // Pattern per fetch POST/PUT/DELETE con body\n  content = content.replace(\n    /const response = await fetch\\(([^,)]+),\\s*\\{\\s*method:\\s*['\"](\\w+)['\"],\\s*headers:\\s*\\{[^}]*\\},\\s*body:\\s*JSON\\.stringify\\(([^)]+)\\)\\s*\\}\\)/g,\n    (match, url, method, body) => {\n      const methodLower = method.toLowerCase();\n      return `const response = await api.${methodLower}(${url}, ${body})`;\n    }\n  );\n  \n  // Pattern per fetch POST/PUT/DELETE senza body\n  content = content.replace(\n    /const response = await fetch\\(([^,)]+),\\s*\\{\\s*method:\\s*['\"](\\w+)['\"],\\s*headers:\\s*\\{[^}]*\\}\\s*\\}\\)/g,\n    (match, url, method) => {\n      const methodLower = method.toLowerCase();\n      return `const response = await api.${methodLower}(${url})`;\n    }\n  );\n  \n  // 3. Aggiorna la gestione delle risposte\n  \n  // Sostituisci response.ok checks\n  content = content.replace(\n    /if \\(!response\\.ok\\) \\{\\s*throw new Error\\([^)]+\\)\\s*\\}/g,\n    '// Response handled by axios'\n  );\n  \n  // Sostituisci response.json()\n  content = content.replace(\n    /const (\\w+) = await response\\.json\\(\\)/g,\n    'const $1 = response.data'\n  );\n  \n  // Aggiorna accesso ai dati\n  content = content.replace(\n    /(\\w+)\\.value = (\\w+)\\.data\\./g,\n    '$1.value = $2.'\n  );\n  \n  if (modified || content !== fs.readFileSync(filePath, 'utf8')) {\n    fs.writeFileSync(filePath, content);\n    console.log(`✅ Fixed ${filePath}`);\n  } else {\n    console.log(`⏭️  No changes needed for ${filePath}`);\n  }\n}\n\n// Esegui la correzione su tutti i file\nfilesToFix.forEach(fixFile);\n\nconsole.log('\\n🎉 All files processed!');\n", "modifiedCode": "#!/usr/bin/env node\n\nconst fs = require('fs');\nconst path = require('path');\n\n// Lista dei file da correggere\nconst filesToFix = [\n  'frontend/src/components/projects/ProjectTasks.vue',\n  'frontend/src/views/projects/components/ProjectTasks.vue',\n  'frontend/src/views/projects/components/ProjectTimesheet.vue',\n  'frontend/src/views/projects/components/ProjectTeam.vue',\n  'frontend/src/views/projects/components/ProjectExpenses.vue',\n  'frontend/src/views/projects/components/ExpenseModal.vue',\n  'frontend/src/views/projects/components/ProjectResourceAllocation.vue',\n  'frontend/src/views/projects/ProjectCreate.vue',\n  'frontend/src/views/projects/ProjectEdit.vue',\n  'frontend/src/views/personnel/PersonnelDirectory.vue',\n  'frontend/src/views/personnel/DepartmentList.vue',\n  'frontend/src/views/personnel/DepartmentCreate.vue',\n  'frontend/src/views/personnel/DepartmentView.vue',\n  'frontend/src/views/personnel/PersonnelAdmin.vue',\n  'frontend/src/views/personnel/PersonnelOrgChart.vue',\n  'frontend/src/views/personnel/PersonnelProfile.vue',\n  'frontend/src/views/personnel/components/admin/UsersManagement.vue',\n  'frontend/src/views/personnel/components/admin/DepartmentsManagement.vue',\n  'frontend/src/views/personnel/components/admin/SkillsManagement.vue',\n  'frontend/src/views/personnel/components/admin/BulkOperations.vue',\n  'frontend/src/views/personnel/components/admin/CreateUserModal.vue',\n  'frontend/src/views/personnel/components/admin/DepartmentModal.vue',\n  'frontend/src/views/personnel/components/admin/SkillModal.vue',\n  'frontend/src/views/personnel/components/CVTab.vue',\n  'frontend/src/views/personnel/PersonnelAllocation.vue',\n  'frontend/src/views/user/Profile.vue',\n  'frontend/src/views/user/Settings.vue',\n  'frontend/src/stores/tenant.js'\n];\n\nfunction fixFile(filePath) {\n  console.log(`Fixing ${filePath}...`);\n  \n  if (!fs.existsSync(filePath)) {\n    console.log(`File ${filePath} does not exist, skipping...`);\n    return;\n  }\n  \n  let content = fs.readFileSync(filePath, 'utf8');\n  let modified = false;\n  \n  // 1. Aggiungi import di api se non presente\n  if (content.includes('fetch(') && !content.includes(\"import api from '@/utils/api'\")) {\n    // Trova la sezione degli import\n    const importRegex = /(import .+ from .+\\n)+/;\n    const match = content.match(importRegex);\n    \n    if (match) {\n      const lastImport = match[0];\n      const newImport = lastImport + \"import api from '@/utils/api'\\n\";\n      content = content.replace(lastImport, newImport);\n      modified = true;\n    }\n  }\n  \n  // 2. Sostituisci fetch() con api.get/post/put/delete\n  \n  // Pattern per fetch GET\n  content = content.replace(\n    /const response = await fetch\\(([^,)]+)(?:,\\s*\\{\\s*(?:credentials:\\s*['\"]include['\"],?\\s*)?(?:headers:\\s*\\{[^}]*\\},?\\s*)?\\})?\\)/g,\n    'const response = await api.get($1)'\n  );\n  \n  // Pattern per fetch POST/PUT/DELETE con body\n  content = content.replace(\n    /const response = await fetch\\(([^,)]+),\\s*\\{\\s*method:\\s*['\"](\\w+)['\"],\\s*headers:\\s*\\{[^}]*\\},\\s*body:\\s*JSON\\.stringify\\(([^)]+)\\)\\s*\\}\\)/g,\n    (match, url, method, body) => {\n      const methodLower = method.toLowerCase();\n      return `const response = await api.${methodLower}(${url}, ${body})`;\n    }\n  );\n  \n  // Pattern per fetch POST/PUT/DELETE senza body\n  content = content.replace(\n    /const response = await fetch\\(([^,)]+),\\s*\\{\\s*method:\\s*['\"](\\w+)['\"],\\s*headers:\\s*\\{[^}]*\\}\\s*\\}\\)/g,\n    (match, url, method) => {\n      const methodLower = method.toLowerCase();\n      return `const response = await api.${methodLower}(${url})`;\n    }\n  );\n  \n  // 3. Aggiorna la gestione delle risposte\n  \n  // Sostituisci response.ok checks\n  content = content.replace(\n    /if \\(!response\\.ok\\) \\{\\s*throw new Error\\([^)]+\\)\\s*\\}/g,\n    '// Response handled by axios'\n  );\n  \n  // Sostituisci response.json()\n  content = content.replace(\n    /const (\\w+) = await response\\.json\\(\\)/g,\n    'const $1 = response.data'\n  );\n  \n  // Aggiorna accesso ai dati\n  content = content.replace(\n    /(\\w+)\\.value = (\\w+)\\.data\\./g,\n    '$1.value = $2.'\n  );\n  \n  if (modified || content !== fs.readFileSync(filePath, 'utf8')) {\n    fs.writeFileSync(filePath, content);\n    console.log(`✅ Fixed ${filePath}`);\n  } else {\n    console.log(`⏭️  No changes needed for ${filePath}`);\n  }\n}\n\n// Esegui la correzione su tutti i file\nfilesToFix.forEach(fixFile);\n\nconsole.log('\\n🎉 All files processed!');\n"}