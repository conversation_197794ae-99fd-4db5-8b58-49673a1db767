{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}, "originalCode": "import os\nimport logging\nfrom flask import Flask, session, redirect, url_for, request, flash, make_response\nfrom flask_login import logout_user, current_user\nfrom flask_cors import CORS\nfrom werkzeug.middleware.proxy_fix import ProxyFix\nfrom datetime import datetime, timedelta\nfrom config import Config\nimport time\nfrom extensions import db, login_manager, migrate, csrf\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo\nlogger = logging.getLogger(__name__)\n\nPUBLIC_ENDPOINTS = [\n    'static',\n    'public_api.get_public_config', 'public_api.get_featured_services',\n    'public_api.get_services', 'public_api.get_service_detail',\n    'tenants_api.api_tenant_config',  # API per configurazione tenant\n    'api_auth.login', 'api_auth.logout', 'api_auth.debug',  # API di autenticazione Vue.js\n    'swagger_json.swagger_json',  # Swagger JSON\n    'swagger_ui.show',  # Swagger UI\n    'spa'  # SPA catch-all route - Vue.js gestisce tutto\n]\n\n# Fix MIME types for ES6 modules GLOBALLY\nimport mimetypes\nmimetypes.add_type('application/javascript', '.js')\nmimetypes.add_type('application/javascript', '.mjs')\nmimetypes.add_type('application/javascript', '.vue')\n\ndef create_app(config_object='config.Config', config_overrides=None):\n    \"\"\"Factory function to create and configure the Flask app.\"\"\"\n    app = Flask(__name__)\n    app.secret_key = os.environ.get(\"SESSION_SECRET\", os.urandom(24))\n    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n\n    # Load configuration\n    app.config.from_object(config_object)\n    if config_overrides:\n        app.config.from_mapping(config_overrides)\n\n    # Initialize extensions with app\n    db.init_app(app)\n    login_manager.init_app(app)\n    # login_manager.login_view = None  # Non serve più con Vue.js SPA\n    migrate.init_app(app, db)\n\n    # CORS will be handled manually in before_request and after_request hooks\n\n    # Configure CSRF protection\n    csrf.init_app(app)\n    # Make csrf_token available in templates without function call\n    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()\n\n    with app.app_context():\n        # Import models first to ensure they're registered\n        from models import User # Spostato import qui per evitare importazioni circolari\n\n        # Add datetime utility for templates\n        @app.context_processor\n        def utility_processor():\n            return {'current_year': datetime.now().year}\n\n        # Import blueprints for Vue.js SPA\n        from blueprints.api.public import public_api_bp\n        from blueprints.swagger import register_swagger_blueprints\n\n        # Register blueprints - SOLO API per Vue.js SPA\n        app.register_blueprint(public_api_bp)\n\n        # Auth API\n        from blueprints.api.auth import api_auth\n        app.register_blueprint(api_auth, url_prefix='/api/auth')\n\n        # Dashboard API\n        from blueprints.api.dashboard import api_dashboard\n        app.register_blueprint(api_dashboard, url_prefix='/api/dashboard')\n\n        # Projects API\n        from blueprints.api.projects import api_projects\n        app.register_blueprint(api_projects, url_prefix='/api/projects')\n\n        # Tasks API\n        from blueprints.api.tasks import api_tasks\n        app.register_blueprint(api_tasks, url_prefix='/api/tasks')\n\n        # Timesheets API\n        from blueprints.api.timesheets import api_timesheets\n        app.register_blueprint(api_timesheets, url_prefix='/api/timesheets')\n\n        # Time-off Requests API\n        from blueprints.api.timeoff_requests import api_timeoff_requests\n        app.register_blueprint(api_timeoff_requests, url_prefix='/api/time-off-requests')\n\n        # Monthly Timesheets API\n        from blueprints.api.monthly_timesheets import api_monthly_timesheets\n        app.register_blueprint(api_monthly_timesheets, url_prefix='/api/monthly-timesheets')\n\n        # Personnel API\n        from blueprints.api.personnel import api_personnel\n        app.register_blueprint(api_personnel, url_prefix='/api/personnel')\n\n        # Personnel Allocation API (registrato prima per evitare conflitti)\n        from blueprints.api.personnel_allocation import api_personnel_allocation\n        app.register_blueprint(api_personnel_allocation, url_prefix='/api')\n\n        # Expenses API\n        from blueprints.api.expenses import api_expenses\n        app.register_blueprint(api_expenses)\n\n        # KPIs API\n        from blueprints.api.kpis import api_kpis\n        app.register_blueprint(api_kpis, url_prefix='/api/kpis')\n\n        # Project KPIs API\n        from blueprints.api.project_kpis import api_project_kpis\n        app.register_blueprint(api_project_kpis, url_prefix='/api/project-kpis')\n\n        # Resources API\n        from blueprints.api.resources import api_resources\n        app.register_blueprint(api_resources, url_prefix='/api/resources')\n\n        # Task Dependencies API\n        from blueprints.api.task_dependencies import api_task_dependencies\n        app.register_blueprint(api_task_dependencies, url_prefix='/api/task-dependencies')\n\n        # Tenant API (senza prefix per mantenere /api/config/tenant)\n        from blueprints.api.tenants import tenants_api\n        app.register_blueprint(tenants_api, url_prefix='/api')\n\n        # Admin API\n        from blueprints.api.admin import api_admin\n        app.register_blueprint(api_admin, url_prefix='/api/admin')\n\n        # AI Resources API\n        from blueprints.api.ai_resources import api_ai_resources\n        app.register_blueprint(api_ai_resources, url_prefix='/api/ai-resources')\n\n\n\n        # Register Swagger blueprints\n        register_swagger_blueprints(app)\n\n        # Handle CORS preflight requests\n        @app.before_request\n        def handle_preflight():\n            if request.method == \"OPTIONS\":\n                response = make_response()\n                origin = request.headers.get('Origin')\n                if origin in ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000', 'http://127.0.0.1:5000']:\n                    response.headers.add(\"Access-Control-Allow-Origin\", origin)\n                    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-CSRFToken,X-Requested-With')\n                    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')\n                    response.headers.add('Access-Control-Allow-Credentials', \"true\")\n                return response\n\n        # Configure static file serving with correct MIME types\n        @app.after_request\n        def after_request(response):\n            # Add CORS headers to all responses\n            origin = request.headers.get('Origin')\n            if origin in ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000', 'http://127.0.0.1:5000']:\n                response.headers.add('Access-Control-Allow-Origin', origin)\n                response.headers.add('Access-Control-Allow-Credentials', 'true')\n                response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-CSRFToken,X-Requested-With')\n                response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')\n\n            # Fix MIME type for JavaScript modules and Vue files\n            if request.path.endswith('.js') or request.path.endswith('.mjs'):\n                response.content_type = 'application/javascript'\n                response.headers['Cache-Control'] = 'public, max-age=********'\n            elif request.path.endswith('.vue'):\n                response.content_type = 'application/javascript'\n            elif request.path.endswith('.css'):\n                response.content_type = 'text/css'\n                response.headers['Cache-Control'] = 'public, max-age=********'\n            return response\n\n\n\n        # SPA Route - Catch-all for Vue.js routing\n        @app.route('/')\n        @app.route('/<path:path>')\n        def spa(path=''):\n            \"\"\"\n            Serve the Vue.js SPA for all routes except API and auth routes.\n            This allows Vue Router to handle client-side routing.\n            \"\"\"\n            # Don't serve SPA for API routes\n            if path.startswith('api/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for auth routes (keep traditional auth)\n            if path.startswith('auth/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for static files - let Flask serve them directly\n            if path.startswith('static/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for swagger routes\n            if path.startswith('swagger') or path.startswith('docs') or path.startswith('api/swagger'):\n                from flask import abort\n                abort(404)\n\n            # Serve the Vue.js SPA template for all other routes\n            from flask import render_template\n            return render_template('vue_app.html')\n\n        # Setup user loader for Flask-Login\n        @login_manager.user_loader\n        def load_user(user_id):\n            return User.query.get(int(user_id))\n\n        # Create database tables if they don't exist\n        db.create_all()\n\n        logger.info(\"Flask app created and configured.\")\n\n        @app.before_request\n        def session_management():\n            # Simplified session management for Vue.js SPA\n            if current_user.is_authenticated:\n                session['last_activity'] = time.time()\n\n        @app.before_request\n        def global_auth_enforcement():\n            endpoint = request.endpoint\n            # Log ogni accesso\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.info(f\"Accesso: user={user}, endpoint={endpoint}, ip={request.remote_addr}\")\n\n            # Skip enforcement per endpoint pubblici\n            if not endpoint or endpoint.startswith('static') or endpoint in PUBLIC_ENDPOINTS:\n                return\n\n            # Enforcement autenticazione globale solo per endpoint protetti\n            if not current_user.is_authenticated:\n                logger.warning(f\"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}\")\n\n                # Per le API, restituisci JSON 401\n                if endpoint and (endpoint.startswith('api_') or endpoint.startswith('api.')):\n                    from flask import jsonify\n                    return jsonify({\n                        'success': False,\n                        'message': 'Autenticazione richiesta'\n                    }), 401\n\n                # Per le pagine web/SPA, restituisci JSON 401 (Vue.js gestirà il redirect)\n                from flask import jsonify\n                return jsonify({'error': 'Authentication required'}), 401\n\n        @app.errorhandler(403)\n        def forbidden(e):\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.warning(f\"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}\")\n            flash('Accesso negato: non hai i permessi necessari.', 'danger')\n            return redirect('/')\n\n        # Registra i filtri personalizzati\n        try:\n            from utils.filters import register_filters\n            register_filters(app)\n        except ImportError:\n            # Filtri non trovati, continua senza\n            pass\n\n    return app\n", "modifiedCode": "import os\nimport logging\nfrom flask import Flask, session, redirect, url_for, request, flash, make_response\nfrom flask_login import logout_user, current_user\nfrom flask_cors import CORS\nfrom werkzeug.middleware.proxy_fix import ProxyFix\nfrom datetime import datetime, timedelta\nfrom config import Config\nimport time\nfrom extensions import db, login_manager, migrate, csrf\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo\nlogger = logging.getLogger(__name__)\n\nPUBLIC_ENDPOINTS = [\n    'static',\n    'public_api.get_public_config', 'public_api.get_featured_services',\n    'public_api.get_services', 'public_api.get_service_detail',\n    'tenants_api.api_tenant_config',  # API per configurazione tenant\n    'api_auth.login', 'api_auth.logout', 'api_auth.debug',  # API di autenticazione Vue.js\n    'swagger_json.swagger_json',  # Swagger JSON\n    'swagger_ui.show',  # Swagger UI\n    'spa'  # SPA catch-all route - Vue.js gestisce tutto\n]\n\n# Fix MIME types for ES6 modules GLOBALLY\nimport mimetypes\nmimetypes.add_type('application/javascript', '.js')\nmimetypes.add_type('application/javascript', '.mjs')\nmimetypes.add_type('application/javascript', '.vue')\n\ndef create_app(config_object='config.Config', config_overrides=None):\n    \"\"\"Factory function to create and configure the Flask app.\"\"\"\n    app = Flask(__name__)\n    app.secret_key = os.environ.get(\"SESSION_SECRET\", os.urandom(24))\n    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n\n    # Load configuration\n    app.config.from_object(config_object)\n    if config_overrides:\n        app.config.from_mapping(config_overrides)\n\n    # Initialize extensions with app\n    db.init_app(app)\n    login_manager.init_app(app)\n    # login_manager.login_view = None  # Non serve più con Vue.js SPA\n    migrate.init_app(app, db)\n\n    # CORS will be handled manually in before_request and after_request hooks\n\n    # Configure CSRF protection\n    csrf.init_app(app)\n    # Make csrf_token available in templates without function call\n    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()\n\n    with app.app_context():\n        # Import models first to ensure they're registered\n        from models import User # Spostato import qui per evitare importazioni circolari\n\n        # Add datetime utility for templates\n        @app.context_processor\n        def utility_processor():\n            return {'current_year': datetime.now().year}\n\n        # Import blueprints for Vue.js SPA\n        from blueprints.api.public import public_api_bp\n        from blueprints.swagger import register_swagger_blueprints\n\n        # Register blueprints - SOLO API per Vue.js SPA\n        app.register_blueprint(public_api_bp)\n\n        # Auth API\n        from blueprints.api.auth import api_auth\n        app.register_blueprint(api_auth, url_prefix='/api/auth')\n\n        # Dashboard API\n        from blueprints.api.dashboard import api_dashboard\n        app.register_blueprint(api_dashboard, url_prefix='/api/dashboard')\n\n        # Projects API\n        from blueprints.api.projects import api_projects\n        app.register_blueprint(api_projects, url_prefix='/api/projects')\n\n        # Tasks API\n        from blueprints.api.tasks import api_tasks\n        app.register_blueprint(api_tasks, url_prefix='/api/tasks')\n\n        # Timesheets API\n        from blueprints.api.timesheets import api_timesheets\n        app.register_blueprint(api_timesheets, url_prefix='/api/timesheets')\n\n        # Time-off Requests API\n        from blueprints.api.timeoff_requests import api_timeoff_requests\n        app.register_blueprint(api_timeoff_requests, url_prefix='/api/time-off-requests')\n\n        # Monthly Timesheets API\n        from blueprints.api.monthly_timesheets import api_monthly_timesheets\n        app.register_blueprint(api_monthly_timesheets, url_prefix='/api/monthly-timesheets')\n\n        # Contracts API\n        from blueprints.api.contracts import api_contracts\n        app.register_blueprint(api_contracts, url_prefix='/api/contracts')\n\n        # Personnel API\n        from blueprints.api.personnel import api_personnel\n        app.register_blueprint(api_personnel, url_prefix='/api/personnel')\n\n        # Personnel Allocation API (registrato prima per evitare conflitti)\n        from blueprints.api.personnel_allocation import api_personnel_allocation\n        app.register_blueprint(api_personnel_allocation, url_prefix='/api')\n\n        # Expenses API\n        from blueprints.api.expenses import api_expenses\n        app.register_blueprint(api_expenses)\n\n        # KPIs API\n        from blueprints.api.kpis import api_kpis\n        app.register_blueprint(api_kpis, url_prefix='/api/kpis')\n\n        # Project KPIs API\n        from blueprints.api.project_kpis import api_project_kpis\n        app.register_blueprint(api_project_kpis, url_prefix='/api/project-kpis')\n\n        # Resources API\n        from blueprints.api.resources import api_resources\n        app.register_blueprint(api_resources, url_prefix='/api/resources')\n\n        # Task Dependencies API\n        from blueprints.api.task_dependencies import api_task_dependencies\n        app.register_blueprint(api_task_dependencies, url_prefix='/api/task-dependencies')\n\n        # Tenant API (senza prefix per mantenere /api/config/tenant)\n        from blueprints.api.tenants import tenants_api\n        app.register_blueprint(tenants_api, url_prefix='/api')\n\n        # Admin API\n        from blueprints.api.admin import api_admin\n        app.register_blueprint(api_admin, url_prefix='/api/admin')\n\n        # AI Resources API\n        from blueprints.api.ai_resources import api_ai_resources\n        app.register_blueprint(api_ai_resources, url_prefix='/api/ai-resources')\n\n\n\n        # Register Swagger blueprints\n        register_swagger_blueprints(app)\n\n        # Handle CORS preflight requests\n        @app.before_request\n        def handle_preflight():\n            if request.method == \"OPTIONS\":\n                response = make_response()\n                origin = request.headers.get('Origin')\n                if origin in ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000', 'http://127.0.0.1:5000']:\n                    response.headers.add(\"Access-Control-Allow-Origin\", origin)\n                    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-CSRFToken,X-Requested-With')\n                    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')\n                    response.headers.add('Access-Control-Allow-Credentials', \"true\")\n                return response\n\n        # Configure static file serving with correct MIME types\n        @app.after_request\n        def after_request(response):\n            # Add CORS headers to all responses\n            origin = request.headers.get('Origin')\n            if origin in ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000', 'http://127.0.0.1:5000']:\n                response.headers.add('Access-Control-Allow-Origin', origin)\n                response.headers.add('Access-Control-Allow-Credentials', 'true')\n                response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization,X-CSRFToken,X-Requested-With')\n                response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')\n\n            # Fix MIME type for JavaScript modules and Vue files\n            if request.path.endswith('.js') or request.path.endswith('.mjs'):\n                response.content_type = 'application/javascript'\n                response.headers['Cache-Control'] = 'public, max-age=********'\n            elif request.path.endswith('.vue'):\n                response.content_type = 'application/javascript'\n            elif request.path.endswith('.css'):\n                response.content_type = 'text/css'\n                response.headers['Cache-Control'] = 'public, max-age=********'\n            return response\n\n\n\n        # SPA Route - Catch-all for Vue.js routing\n        @app.route('/')\n        @app.route('/<path:path>')\n        def spa(path=''):\n            \"\"\"\n            Serve the Vue.js SPA for all routes except API and auth routes.\n            This allows Vue Router to handle client-side routing.\n            \"\"\"\n            # Don't serve SPA for API routes\n            if path.startswith('api/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for auth routes (keep traditional auth)\n            if path.startswith('auth/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for static files - let Flask serve them directly\n            if path.startswith('static/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for swagger routes\n            if path.startswith('swagger') or path.startswith('docs') or path.startswith('api/swagger'):\n                from flask import abort\n                abort(404)\n\n            # Serve the Vue.js SPA template for all other routes\n            from flask import render_template\n            return render_template('vue_app.html')\n\n        # Setup user loader for Flask-Login\n        @login_manager.user_loader\n        def load_user(user_id):\n            return User.query.get(int(user_id))\n\n        # Create database tables if they don't exist\n        db.create_all()\n\n        logger.info(\"Flask app created and configured.\")\n\n        @app.before_request\n        def session_management():\n            # Simplified session management for Vue.js SPA\n            if current_user.is_authenticated:\n                session['last_activity'] = time.time()\n\n        @app.before_request\n        def global_auth_enforcement():\n            endpoint = request.endpoint\n            # Log ogni accesso\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.info(f\"Accesso: user={user}, endpoint={endpoint}, ip={request.remote_addr}\")\n\n            # Skip enforcement per endpoint pubblici\n            if not endpoint or endpoint.startswith('static') or endpoint in PUBLIC_ENDPOINTS:\n                return\n\n            # Enforcement autenticazione globale solo per endpoint protetti\n            if not current_user.is_authenticated:\n                logger.warning(f\"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}\")\n\n                # Per le API, restituisci JSON 401\n                if endpoint and (endpoint.startswith('api_') or endpoint.startswith('api.')):\n                    from flask import jsonify\n                    return jsonify({\n                        'success': False,\n                        'message': 'Autenticazione richiesta'\n                    }), 401\n\n                # Per le pagine web/SPA, restituisci JSON 401 (Vue.js gestirà il redirect)\n                from flask import jsonify\n                return jsonify({'error': 'Authentication required'}), 401\n\n        @app.errorhandler(403)\n        def forbidden(e):\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.warning(f\"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}\")\n            flash('Accesso negato: non hai i permessi necessari.', 'danger')\n            return redirect('/')\n\n        # Registra i filtri personalizzati\n        try:\n            from utils.filters import register_filters\n            register_filters(app)\n        except ImportError:\n            # Filtri non trovati, continua senza\n            pass\n\n    return app\n"}