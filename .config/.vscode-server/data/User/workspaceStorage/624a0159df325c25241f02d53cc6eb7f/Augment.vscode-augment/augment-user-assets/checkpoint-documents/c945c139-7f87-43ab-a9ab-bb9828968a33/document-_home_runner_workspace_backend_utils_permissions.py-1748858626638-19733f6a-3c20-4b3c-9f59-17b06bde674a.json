{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/permissions.py"}, "originalCode": "# utils/permissions.py\n\n# Definizioni dei Ruoli\nROLE_ADMIN = 'admin'\nROLE_MANAGER = 'manager'\nROLE_EMPLOYEE = 'employee'\nROLE_SALES = 'sales'\nROLE_HR = 'human_resources'\n\n# Set di tutti i ruoli validi (utile per validazione o dropdown)\nALL_ROLES = {\n    ROLE_ADMIN,\n    ROLE_MANAGER,\n    ROLE_EMPLOYEE,\n    ROLE_SALES,\n    ROLE_HR,\n}\n\n# Definizioni dei Permessi (esempi, da espandere)\nPERMISSION_VIEW_DASHBOARD = 'view_dashboard'\nPERMISSION_ADMIN = 'admin'  # Permesso amministrativo generale\nPERMISSION_MANAGE_USERS = 'manage_users' # Creare, modificare, attivare/disattivare utenti\nPERMISSION_ASSIGN_ROLES = 'assign_roles'\n\nPERMISSION_VIEW_ALL_PROJECTS = 'view_all_projects'\nPERMISSION_CREATE_PROJECT = 'create_project'\nPERMISSION_EDIT_PROJECT = 'edit_project' # Modificare dettagli, budget\nPERMISSION_DELETE_PROJECT = 'delete_project'\nPERMISSION_ASSIGN_TO_PROJECT = 'assign_to_project'\nPERMISSION_MANAGE_PROJECT_TASKS = 'manage_project_tasks' # Creare/modificare/eliminare task di progetto\nPERMISSION_MANAGE_PROJECT_RESOURCES = 'manage_project_resources' # Gestire le risorse assegnate ai progetti\n\nPERMISSION_VIEW_OWN_TIMESHEETS = 'view_own_timesheets'\nPERMISSION_SUBMIT_TIMESHEET = 'submit_timesheet'\nPERMISSION_APPROVE_TIMESHEETS = 'approve_timesheets' # Tipicamente per Manager\n\n# Nuovi permessi per il sistema timesheet (Task 3.1)\nPERMISSION_VIEW_ALL_TIMESHEETS = 'view_all_timesheets'  # Manager, Admin, HR\nPERMISSION_MANAGE_TIMESHEETS = 'manage_timesheets'  # Admin, Manager (per altri utenti)\nPERMISSION_VIEW_ALL_TIME_OFF_REQUESTS = 'view_all_time_off_requests'  # Manager, Admin, HR\nPERMISSION_MANAGE_TIME_OFF_REQUESTS = 'manage_time_off_requests'  # Admin, Manager (per altri utenti)\nPERMISSION_APPROVE_TIME_OFF_REQUESTS = 'approve_time_off_requests'  # Manager, Admin, HR\n\nPERMISSION_VIEW_PERSONNEL_DATA = 'view_personnel_data' # HR e forse Manager per i propri team\nPERMISSION_EDIT_PERSONNEL_DATA = 'edit_personnel_data' # HR\nPERMISSION_VIEW_CONTRACTS = 'view_contracts' # HR\nPERMISSION_MANAGE_CONTRACTS = 'manage_contracts' # HR\n\nPERMISSION_VIEW_CRM = 'view_crm' # Sales, Manager\nPERMISSION_MANAGE_CLIENTS = 'manage_clients' # Sales, Manager, Admin\nPERMISSION_MANAGE_PROPOSALS = 'manage_proposals' # Sales, Manager, Admin\n\nPERMISSION_VIEW_REPORTS = 'view_reports' # Manager, Admin, HR (specifici)\n\nPERMISSION_VIEW_FUNDING = 'view_funding' # Admin, Manager, HR, Sales\nPERMISSION_MANAGE_FUNDING = 'manage_funding' # Admin, Manager, HR\n\nPERMISSION_VIEW_PRODUCTS = 'view_products' # Tutti i loggati, Sales, Manager, Admin\nPERMISSION_MANAGE_PRODUCTS = 'manage_products' # Sales, Manager, Admin\nPERMISSION_VIEW_PERFORMANCE = 'view_performance' # Tutti i loggati, Manager, Admin, HR\nPERMISSION_MANAGE_PERFORMANCE = 'manage_performance' # Manager, Admin, HR\nPERMISSION_VIEW_COMMUNICATIONS = 'view_communications' # Tutti i loggati, Manager, Admin, HR\nPERMISSION_MANAGE_COMMUNICATIONS = 'manage_communications' # Manager, Admin, HR\nPERMISSION_VIEW_STARTUP = 'view_startup' # Tutti i loggati, Manager, Admin, HR\nPERMISSION_MANAGE_STARTUP = 'manage_startup' # Manager, Admin, HR\n\n# Mappatura Ruoli -> Permessi\nROLE_PERMISSIONS = {\n    ROLE_ADMIN: {\n        PERMISSION_ADMIN,\n        PERMISSION_MANAGE_USERS,\n        PERMISSION_ASSIGN_ROLES,\n        PERMISSION_VIEW_ALL_PROJECTS,\n        PERMISSION_CREATE_PROJECT,\n        PERMISSION_EDIT_PROJECT,\n        PERMISSION_DELETE_PROJECT,\n        PERMISSION_ASSIGN_TO_PROJECT,\n        PERMISSION_MANAGE_PROJECT_TASKS,\n        PERMISSION_MANAGE_PROJECT_RESOURCES,\n        PERMISSION_APPROVE_TIMESHEETS,\n        PERMISSION_VIEW_PERSONNEL_DATA,\n        PERMISSION_EDIT_PERSONNEL_DATA,\n        PERMISSION_VIEW_CONTRACTS,\n        PERMISSION_MANAGE_CONTRACTS,\n        PERMISSION_VIEW_CRM,\n        PERMISSION_MANAGE_CLIENTS,\n        PERMISSION_MANAGE_PROPOSALS,\n        PERMISSION_VIEW_REPORTS,\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_SUBMIT_TIMESHEET,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_VIEW_FUNDING,\n        PERMISSION_MANAGE_FUNDING,\n        PERMISSION_VIEW_PRODUCTS,\n        PERMISSION_MANAGE_PRODUCTS,\n        PERMISSION_VIEW_PERFORMANCE,\n        PERMISSION_MANAGE_PERFORMANCE,\n        PERMISSION_VIEW_COMMUNICATIONS,\n        PERMISSION_MANAGE_COMMUNICATIONS,\n        PERMISSION_VIEW_STARTUP,\n        PERMISSION_MANAGE_STARTUP,\n    },\n    ROLE_MANAGER: {\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_VIEW_ALL_PROJECTS,\n        PERMISSION_EDIT_PROJECT,\n        PERMISSION_ASSIGN_TO_PROJECT,\n        PERMISSION_MANAGE_PROJECT_TASKS,\n        PERMISSION_MANAGE_PROJECT_RESOURCES,\n        PERMISSION_APPROVE_TIMESHEETS,\n        PERMISSION_VIEW_PERSONNEL_DATA,\n        PERMISSION_VIEW_CRM,\n        PERMISSION_VIEW_REPORTS,\n        PERMISSION_SUBMIT_TIMESHEET,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_MANAGE_CLIENTS,\n        PERMISSION_MANAGE_PROPOSALS,\n        PERMISSION_VIEW_FUNDING,\n        PERMISSION_MANAGE_FUNDING,\n        PERMISSION_VIEW_PRODUCTS,\n        PERMISSION_MANAGE_PRODUCTS,\n        PERMISSION_VIEW_PERFORMANCE,\n        PERMISSION_MANAGE_PERFORMANCE,\n        PERMISSION_VIEW_COMMUNICATIONS,\n        PERMISSION_MANAGE_COMMUNICATIONS,\n        PERMISSION_VIEW_STARTUP,\n        PERMISSION_MANAGE_STARTUP,\n    },\n    ROLE_EMPLOYEE: {\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_SUBMIT_TIMESHEET,\n    },\n    ROLE_SALES: {\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_VIEW_CRM,\n        PERMISSION_MANAGE_CLIENTS,\n        PERMISSION_MANAGE_PROPOSALS,\n        PERMISSION_SUBMIT_TIMESHEET,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_VIEW_REPORTS,\n        PERMISSION_VIEW_FUNDING,\n        PERMISSION_VIEW_PRODUCTS,\n        PERMISSION_MANAGE_PRODUCTS,\n    },\n    ROLE_HR: {\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_MANAGE_USERS,\n        PERMISSION_VIEW_PERSONNEL_DATA,\n        PERMISSION_EDIT_PERSONNEL_DATA,\n        PERMISSION_VIEW_CONTRACTS,\n        PERMISSION_MANAGE_CONTRACTS,\n        PERMISSION_SUBMIT_TIMESHEET,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_VIEW_REPORTS,\n        PERMISSION_VIEW_FUNDING,\n        PERMISSION_MANAGE_FUNDING,\n        PERMISSION_VIEW_PERFORMANCE,\n        PERMISSION_MANAGE_PERFORMANCE,\n        PERMISSION_VIEW_COMMUNICATIONS,\n        PERMISSION_MANAGE_COMMUNICATIONS,\n        PERMISSION_VIEW_STARTUP,\n        PERMISSION_MANAGE_STARTUP,\n    }\n}\n\nALL_PERMISSIONS = set()\nfor role in ROLE_PERMISSIONS:\n    ALL_PERMISSIONS.update(ROLE_PERMISSIONS[role])\n\ndef user_has_permission(user_role, permission_name):\n    if not user_role or user_role not in ROLE_PERMISSIONS:\n        return False\n    if ROLE_ADMIN == user_role:\n        return True\n    return permission_name in ROLE_PERMISSIONS.get(user_role, set())\n\ndef require_permissions(*permissions):\n    \"\"\"Decorator per richiedere permessi specifici.\"\"\"\n    from functools import wraps\n    from flask import jsonify, session\n    \n    def decorator(f):\n        @wraps(f)\n        def decorated_function(*args, **kwargs):\n            # Verifica se l'utente è autenticato tramite sessione Flask\n            if 'user_id' not in session:\n                return jsonify({\n                    'success': False,\n                    'message': 'Autenticazione richiesta'\n                }), 401\n            \n            # Importa User solo quando necessario per evitare importazioni circolari\n            from models import User\n            user = User.query.get(session['user_id'])\n            \n            if not user or not user.is_active:\n                return jsonify({\n                    'success': False,\n                    'message': 'Utente non trovato o inattivo'\n                }), 401\n            \n            user_permissions = ROLE_PERMISSIONS.get(user.role, set())\n            \n            for permission in permissions:\n                if permission not in user_permissions:\n                    return jsonify({\n                        'success': False,\n                        'message': 'Permessi insufficienti'\n                    }), 403\n            \n            return f(*args, **kwargs)\n        return decorated_function\n    return decorator", "modifiedCode": "# utils/permissions.py\n\n# Definizioni dei Ruoli\nROLE_ADMIN = 'admin'\nROLE_MANAGER = 'manager'\nROLE_EMPLOYEE = 'employee'\nROLE_SALES = 'sales'\nROLE_HR = 'human_resources'\n\n# Set di tutti i ruoli validi (utile per validazione o dropdown)\nALL_ROLES = {\n    ROLE_ADMIN,\n    ROLE_MANAGER,\n    ROLE_EMPLOYEE,\n    ROLE_SALES,\n    ROLE_HR,\n}\n\n# Definizioni dei Permessi (esempi, da espandere)\nPERMISSION_VIEW_DASHBOARD = 'view_dashboard'\nPERMISSION_ADMIN = 'admin'  # Permesso amministrativo generale\nPERMISSION_MANAGE_USERS = 'manage_users' # Creare, modificare, attivare/disattivare utenti\nPERMISSION_ASSIGN_ROLES = 'assign_roles'\n\nPERMISSION_VIEW_ALL_PROJECTS = 'view_all_projects'\nPERMISSION_CREATE_PROJECT = 'create_project'\nPERMISSION_EDIT_PROJECT = 'edit_project' # Modificare dettagli, budget\nPERMISSION_DELETE_PROJECT = 'delete_project'\nPERMISSION_ASSIGN_TO_PROJECT = 'assign_to_project'\nPERMISSION_MANAGE_PROJECT_TASKS = 'manage_project_tasks' # Creare/modificare/eliminare task di progetto\nPERMISSION_MANAGE_PROJECT_RESOURCES = 'manage_project_resources' # Gestire le risorse assegnate ai progetti\n\nPERMISSION_VIEW_OWN_TIMESHEETS = 'view_own_timesheets'\nPERMISSION_SUBMIT_TIMESHEET = 'submit_timesheet'\nPERMISSION_APPROVE_TIMESHEETS = 'approve_timesheets' # Tipicamente per Manager\n\nPERMISSION_VIEW_PERSONNEL_DATA = 'view_personnel_data' # HR e forse Manager per i propri team\nPERMISSION_EDIT_PERSONNEL_DATA = 'edit_personnel_data' # HR\nPERMISSION_VIEW_CONTRACTS = 'view_contracts' # HR\nPERMISSION_MANAGE_CONTRACTS = 'manage_contracts' # HR\n\nPERMISSION_VIEW_CRM = 'view_crm' # Sales, Manager\nPERMISSION_MANAGE_CLIENTS = 'manage_clients' # Sales, Manager, Admin\nPERMISSION_MANAGE_PROPOSALS = 'manage_proposals' # Sales, Manager, Admin\n\nPERMISSION_VIEW_REPORTS = 'view_reports' # Manager, Admin, HR (specifici)\n\nPERMISSION_VIEW_FUNDING = 'view_funding' # Admin, Manager, HR, Sales\nPERMISSION_MANAGE_FUNDING = 'manage_funding' # Admin, Manager, HR\n\nPERMISSION_VIEW_PRODUCTS = 'view_products' # Tutti i loggati, Sales, Manager, Admin\nPERMISSION_MANAGE_PRODUCTS = 'manage_products' # Sales, Manager, Admin\nPERMISSION_VIEW_PERFORMANCE = 'view_performance' # Tutti i loggati, Manager, Admin, HR\nPERMISSION_MANAGE_PERFORMANCE = 'manage_performance' # Manager, Admin, HR\nPERMISSION_VIEW_COMMUNICATIONS = 'view_communications' # Tutti i loggati, Manager, Admin, HR\nPERMISSION_MANAGE_COMMUNICATIONS = 'manage_communications' # Manager, Admin, HR\nPERMISSION_VIEW_STARTUP = 'view_startup' # Tutti i loggati, Manager, Admin, HR\nPERMISSION_MANAGE_STARTUP = 'manage_startup' # Manager, Admin, HR\n\n# Mappatura Ruoli -> Permessi\nROLE_PERMISSIONS = {\n    ROLE_ADMIN: {\n        PERMISSION_ADMIN,\n        PERMISSION_MANAGE_USERS,\n        PERMISSION_ASSIGN_ROLES,\n        PERMISSION_VIEW_ALL_PROJECTS,\n        PERMISSION_CREATE_PROJECT,\n        PERMISSION_EDIT_PROJECT,\n        PERMISSION_DELETE_PROJECT,\n        PERMISSION_ASSIGN_TO_PROJECT,\n        PERMISSION_MANAGE_PROJECT_TASKS,\n        PERMISSION_MANAGE_PROJECT_RESOURCES,\n        PERMISSION_APPROVE_TIMESHEETS,\n        PERMISSION_VIEW_PERSONNEL_DATA,\n        PERMISSION_EDIT_PERSONNEL_DATA,\n        PERMISSION_VIEW_CONTRACTS,\n        PERMISSION_MANAGE_CONTRACTS,\n        PERMISSION_VIEW_CRM,\n        PERMISSION_MANAGE_CLIENTS,\n        PERMISSION_MANAGE_PROPOSALS,\n        PERMISSION_VIEW_REPORTS,\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_SUBMIT_TIMESHEET,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_VIEW_FUNDING,\n        PERMISSION_MANAGE_FUNDING,\n        PERMISSION_VIEW_PRODUCTS,\n        PERMISSION_MANAGE_PRODUCTS,\n        PERMISSION_VIEW_PERFORMANCE,\n        PERMISSION_MANAGE_PERFORMANCE,\n        PERMISSION_VIEW_COMMUNICATIONS,\n        PERMISSION_MANAGE_COMMUNICATIONS,\n        PERMISSION_VIEW_STARTUP,\n        PERMISSION_MANAGE_STARTUP,\n    },\n    ROLE_MANAGER: {\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_VIEW_ALL_PROJECTS,\n        PERMISSION_EDIT_PROJECT,\n        PERMISSION_ASSIGN_TO_PROJECT,\n        PERMISSION_MANAGE_PROJECT_TASKS,\n        PERMISSION_MANAGE_PROJECT_RESOURCES,\n        PERMISSION_APPROVE_TIMESHEETS,\n        PERMISSION_VIEW_PERSONNEL_DATA,\n        PERMISSION_VIEW_CRM,\n        PERMISSION_VIEW_REPORTS,\n        PERMISSION_SUBMIT_TIMESHEET,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_MANAGE_CLIENTS,\n        PERMISSION_MANAGE_PROPOSALS,\n        PERMISSION_VIEW_FUNDING,\n        PERMISSION_MANAGE_FUNDING,\n        PERMISSION_VIEW_PRODUCTS,\n        PERMISSION_MANAGE_PRODUCTS,\n        PERMISSION_VIEW_PERFORMANCE,\n        PERMISSION_MANAGE_PERFORMANCE,\n        PERMISSION_VIEW_COMMUNICATIONS,\n        PERMISSION_MANAGE_COMMUNICATIONS,\n        PERMISSION_VIEW_STARTUP,\n        PERMISSION_MANAGE_STARTUP,\n    },\n    ROLE_EMPLOYEE: {\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_SUBMIT_TIMESHEET,\n    },\n    ROLE_SALES: {\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_VIEW_CRM,\n        PERMISSION_MANAGE_CLIENTS,\n        PERMISSION_MANAGE_PROPOSALS,\n        PERMISSION_SUBMIT_TIMESHEET,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_VIEW_REPORTS,\n        PERMISSION_VIEW_FUNDING,\n        PERMISSION_VIEW_PRODUCTS,\n        PERMISSION_MANAGE_PRODUCTS,\n    },\n    ROLE_HR: {\n        PERMISSION_VIEW_DASHBOARD,\n        PERMISSION_MANAGE_USERS,\n        PERMISSION_VIEW_PERSONNEL_DATA,\n        PERMISSION_EDIT_PERSONNEL_DATA,\n        PERMISSION_VIEW_CONTRACTS,\n        PERMISSION_MANAGE_CONTRACTS,\n        PERMISSION_SUBMIT_TIMESHEET,\n        PERMISSION_VIEW_OWN_TIMESHEETS,\n        PERMISSION_VIEW_REPORTS,\n        PERMISSION_VIEW_FUNDING,\n        PERMISSION_MANAGE_FUNDING,\n        PERMISSION_VIEW_PERFORMANCE,\n        PERMISSION_MANAGE_PERFORMANCE,\n        PERMISSION_VIEW_COMMUNICATIONS,\n        PERMISSION_MANAGE_COMMUNICATIONS,\n        PERMISSION_VIEW_STARTUP,\n        PERMISSION_MANAGE_STARTUP,\n    }\n}\n\nALL_PERMISSIONS = set()\nfor role in ROLE_PERMISSIONS:\n    ALL_PERMISSIONS.update(ROLE_PERMISSIONS[role])\n\ndef user_has_permission(user_role, permission_name):\n    if not user_role or user_role not in ROLE_PERMISSIONS:\n        return False\n    if ROLE_ADMIN == user_role:\n        return True\n    return permission_name in ROLE_PERMISSIONS.get(user_role, set())\n\ndef require_permissions(*permissions):\n    \"\"\"Decorator per richiedere permessi specifici.\"\"\"\n    from functools import wraps\n    from flask import jsonify, session\n    \n    def decorator(f):\n        @wraps(f)\n        def decorated_function(*args, **kwargs):\n            # Verifica se l'utente è autenticato tramite sessione Flask\n            if 'user_id' not in session:\n                return jsonify({\n                    'success': False,\n                    'message': 'Autenticazione richiesta'\n                }), 401\n            \n            # Importa User solo quando necessario per evitare importazioni circolari\n            from models import User\n            user = User.query.get(session['user_id'])\n            \n            if not user or not user.is_active:\n                return jsonify({\n                    'success': False,\n                    'message': 'Utente non trovato o inattivo'\n                }), 401\n            \n            user_permissions = ROLE_PERMISSIONS.get(user.role, set())\n            \n            for permission in permissions:\n                if permission not in user_permissions:\n                    return jsonify({\n                        'success': False,\n                        'message': 'Permessi insufficienti'\n                    }), 403\n            \n            return f(*args, **kwargs)\n        return decorated_function\n    return decorator"}