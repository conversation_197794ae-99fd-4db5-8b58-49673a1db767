{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTasks.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header con filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Task del Progetto</h3>\n          <button \n            v-if=\"canManageTasks\"\n            @click=\"showCreateModal = true\"\n            class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Nuovo Task\n          </button>\n        </div>\n\n        <!-- Filtri Task -->\n        <div class=\"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Stato</label>\n            <select \n              v-model=\"filters.status\" \n              @change=\"loadTasks\"\n              class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"\">Tutti gli stati</option>\n              <option value=\"todo\">Da fare</option>\n              <option value=\"in-progress\">In corso</option>\n              <option value=\"review\">In revisione</option>\n              <option value=\"done\">Completato</option>\n            </select>\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Priorità</label>\n            <select \n              v-model=\"filters.priority\" \n              @change=\"loadTasks\"\n              class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"\">Tutte le priorità</option>\n              <option value=\"low\">Bassa</option>\n              <option value=\"medium\">Media</option>\n              <option value=\"high\">Alta</option>\n              <option value=\"urgent\">Urgente</option>\n            </select>\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Assegnatario</label>\n            <select \n              v-model=\"filters.assignee_id\" \n              @change=\"loadTasks\"\n              class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"\">Tutti</option>\n              <option \n                v-for=\"member in project?.team_members || []\" \n                :key=\"member.id\" \n                :value=\"member.id\"\n              >\n                {{ member.first_name }} {{ member.last_name }}\n              </option>\n            </select>\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ricerca</label>\n            <input \n              v-model=\"filters.search\" \n              @input=\"debouncedSearch\"\n              type=\"text\" \n              placeholder=\"Cerca task...\"\n              class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n          </div>\n        </div>\n\n        <!-- Controlli vista -->\n        <div class=\"mt-4 flex items-center justify-between\">\n          <div class=\"flex items-center space-x-4\">\n            <span class=\"text-sm text-gray-500 dark:text-gray-400\">Vista:</span>\n            <button\n              @click=\"viewMode = 'list'\"\n              :class=\"viewMode === 'list' ? 'bg-primary-100 text-primary-700' : 'text-gray-500 hover:text-gray-700'\"\n              class=\"px-3 py-1 rounded-md text-sm font-medium\"\n            >\n              Lista\n            </button>\n            <button\n              @click=\"viewMode = 'kanban'\"\n              :class=\"viewMode === 'kanban' ? 'bg-primary-100 text-primary-700' : 'text-gray-500 hover:text-gray-700'\"\n              class=\"px-3 py-1 rounded-md text-sm font-medium\"\n            >\n              Kanban\n            </button>\n          </div>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            {{ tasks.length }} task trovati\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading -->\n    <div v-if=\"tasksLoading\" class=\"flex justify-center py-8\">\n      <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n    </div>\n\n    <!-- Error -->\n    <div v-if=\"error\" class=\"bg-red-50 border border-red-200 rounded-md p-4\">\n      <p class=\"text-red-600\">{{ error }}</p>\n    </div>\n\n    <!-- Vista Lista -->\n    <div v-if=\"!tasksLoading && viewMode === 'list'\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n        <!-- Header tabella -->\n        <div class=\"bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n          <div class=\"col-span-4\">Task</div>\n          <div class=\"col-span-2\">Assegnatario</div>\n          <div class=\"col-span-1\">Stato</div>\n          <div class=\"col-span-1\">Priorità</div>\n          <div class=\"col-span-2\">Scadenza</div>\n          <div class=\"col-span-1\">Ore</div>\n          <div class=\"col-span-1\">Azioni</div>\n        </div>\n\n        <!-- Righe task -->\n        <div v-for=\"task in tasks\" :key=\"task.id\" class=\"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700\">\n          <div class=\"col-span-4\">\n            <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ task.name }}</div>\n            <div v-if=\"task.description\" class=\"text-sm text-gray-500 dark:text-gray-400 truncate\">{{ task.description }}</div>\n          </div>\n          <div class=\"col-span-2\">\n            <div v-if=\"task.assignee\" class=\"flex items-center\">\n              <div class=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700\">\n                {{ getInitials(task.assignee.first_name, task.assignee.last_name) }}\n              </div>\n              <div class=\"ml-2\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ task.assignee.first_name }} {{ task.assignee.last_name }}\n                </div>\n              </div>\n            </div>\n            <span v-else class=\"text-sm text-gray-500 dark:text-gray-400\">Non assegnato</span>\n          </div>\n          <div class=\"col-span-1\">\n            <span :class=\"getStatusClass(task.status)\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\">\n              {{ getStatusLabel(task.status) }}\n            </span>\n          </div>\n          <div class=\"col-span-1\">\n            <span :class=\"getPriorityClass(task.priority)\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\">\n              {{ getPriorityLabel(task.priority) }}\n            </span>\n          </div>\n          <div class=\"col-span-2\">\n            <div v-if=\"task.due_date\" class=\"text-sm text-gray-900 dark:text-white\">\n              {{ formatDate(task.due_date) }}\n            </div>\n            <span v-else class=\"text-sm text-gray-500 dark:text-gray-400\">-</span>\n          </div>\n          <div class=\"col-span-1\">\n            <div class=\"text-sm text-gray-900 dark:text-white\">\n              {{ task.actual_hours || 0 }}h\n              <span v-if=\"task.estimated_hours\" class=\"text-gray-500\">/ {{ task.estimated_hours }}h</span>\n            </div>\n          </div>\n          <div class=\"col-span-1\">\n            <div class=\"flex items-center space-x-2\">\n              <button \n                @click=\"editTask(task)\"\n                class=\"text-primary-600 hover:text-primary-900 text-sm\"\n              >\n                Modifica\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Empty state -->\n        <div v-if=\"tasks.length === 0\" class=\"px-6 py-12 text-center\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Nessun task trovato</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Vista Kanban -->\n    <div v-if=\"!tasksLoading && viewMode === 'kanban'\" class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div v-for=\"status in kanbanColumns\" :key=\"status.value\" class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ status.label }}</h4>\n          <span class=\"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs\">\n            {{ getTasksByStatus(status.value).length }}\n          </span>\n        </div>\n        <div class=\"space-y-3\">\n          <div \n            v-for=\"task in getTasksByStatus(status.value)\" \n            :key=\"task.id\"\n            class=\"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow\"\n            @click=\"editTask(task)\"\n          >\n            <div class=\"font-medium text-sm text-gray-900 dark:text-white mb-1\">{{ task.name }}</div>\n            <div v-if=\"task.description\" class=\"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2\">\n              {{ task.description }}\n            </div>\n            <div class=\"flex items-center justify-between\">\n              <span :class=\"getPriorityClass(task.priority)\" class=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium\">\n                {{ getPriorityLabel(task.priority) }}\n              </span>\n              <div v-if=\"task.assignee\" class=\"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700\">\n                {{ getInitials(task.assignee.first_name, task.assignee.last_name) }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Creazione/Modifica Task -->\n    <div v-if=\"showCreateModal || showEditModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ showEditModal ? 'Modifica Task' : 'Nuovo Task' }}\n          </h3>\n          \n          <form @submit.prevent=\"saveTask\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Nome</label>\n                <input \n                  v-model=\"formData.name\" \n                  type=\"text\" \n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Descrizione</label>\n                <textarea \n                  v-model=\"formData.description\" \n                  rows=\"3\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                ></textarea>\n              </div>\n\n              <div class=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Stato</label>\n                  <select \n                    v-model=\"formData.status\"\n                    class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  >\n                    <option value=\"todo\">Da fare</option>\n                    <option value=\"in-progress\">In corso</option>\n                    <option value=\"review\">In revisione</option>\n                    <option value=\"done\">Completato</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Priorità</label>\n                  <select \n                    v-model=\"formData.priority\"\n                    class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  >\n                    <option value=\"low\">Bassa</option>\n                    <option value=\"medium\">Media</option>\n                    <option value=\"high\">Alta</option>\n                    <option value=\"urgent\">Urgente</option>\n                  </select>\n                </div>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Assegnatario</label>\n                <select \n                  v-model=\"formData.assignee_id\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Non assegnato</option>\n                  <option \n                    v-for=\"member in project?.team_members || []\" \n                    :key=\"member.id\" \n                    :value=\"member.id\"\n                  >\n                    {{ member.first_name }} {{ member.last_name }}\n                  </option>\n                </select>\n              </div>\n\n              <div class=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Scadenza</label>\n                  <input \n                    v-model=\"formData.due_date\" \n                    type=\"date\"\n                    class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  >\n                </div>\n\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore stimate</label>\n                  <input \n                    v-model=\"formData.estimated_hours\" \n                    type=\"number\" \n                    step=\"0.5\"\n                    min=\"0\"\n                    class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  >\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Salvataggio...' : (showEditModal ? 'Aggiorna' : 'Crea') }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\nimport { usePermissions } from '@/composables/usePermissions'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst authStore = useAuthStore()\nconst { hasPermission } = usePermissions()\n\n// State\nconst tasks = ref([])\nconst tasksLoading = ref(false)\nconst error = ref('')\nconst viewMode = ref('list')\nconst saving = ref(false)\n\n// Filtri\nconst filters = ref({\n  status: '',\n  priority: '',\n  assignee_id: '',\n  search: ''\n})\n\n// Modal\nconst showCreateModal = ref(false)\nconst showEditModal = ref(false)\nconst editingTask = ref(null)\n\n// Form data\nconst formData = ref({\n  name: '',\n  description: '',\n  status: 'todo',\n  priority: 'medium',\n  assignee_id: '',\n  due_date: '',\n  estimated_hours: null\n})\n\n// Computed\nconst canManageTasks = computed(() => {\n  return hasPermission.value('manage_project_tasks')\n})\n\nconst kanbanColumns = [\n  { value: 'todo', label: 'Da fare' },\n  { value: 'in-progress', label: 'In corso' },\n  { value: 'review', label: 'In revisione' },\n  { value: 'done', label: 'Completato' }\n]\n\n// Methods\nconst loadTasks = async () => {\n  if (!props.project?.id) return\n  \n  tasksLoading.value = true\n  error.value = ''\n  \n  try {\n    const params = new URLSearchParams({\n      project_id: props.project.id,\n      ...filters.value\n    })\n\n    const response = await fetch(`/api/tasks?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel caricamento dei task')\n    }\n\n    const result = await response.json()\n    tasks.value = result.data?.tasks || result.tasks || []\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    tasksLoading.value = false\n  }\n}\n\nconst saveTask = async () => {\n  saving.value = true\n  \n  try {\n    const url = showEditModal.value \n      ? `/api/tasks/${editingTask.value.id}`\n      : '/api/tasks'\n    \n    const method = showEditModal.value ? 'PUT' : 'POST'\n    const data = { ...formData.value, project_id: props.project.id }\n\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(data)\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio del task')\n    }\n\n    await loadTasks()\n    closeModal()\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editTask = (task) => {\n  editingTask.value = task\n  formData.value = {\n    name: task.name,\n    description: task.description || '',\n    status: task.status,\n    priority: task.priority,\n    assignee_id: task.assignee_id || '',\n    due_date: task.due_date ? task.due_date.split('T')[0] : '',\n    estimated_hours: task.estimated_hours\n  }\n  showEditModal.value = true\n}\n\nconst closeModal = () => {\n  showCreateModal.value = false\n  showEditModal.value = false\n  editingTask.value = null\n  formData.value = {\n    name: '',\n    description: '',\n    status: 'todo',\n    priority: 'medium',\n    assignee_id: '',\n    due_date: '',\n    estimated_hours: null\n  }\n}\n\nconst getTasksByStatus = (status) => {\n  return tasks.value.filter(task => task.status === status)\n}\n\nconst getStatusClass = (status) => {\n  const classes = {\n    todo: 'bg-gray-100 text-gray-800',\n    'in-progress': 'bg-blue-100 text-blue-800',\n    review: 'bg-yellow-100 text-yellow-800',\n    done: 'bg-green-100 text-green-800'\n  }\n  return classes[status] || 'bg-gray-100 text-gray-800'\n}\n\nconst getStatusLabel = (status) => {\n  const labels = {\n    todo: 'Da fare',\n    'in-progress': 'In corso',\n    review: 'In revisione',\n    done: 'Completato'\n  }\n  return labels[status] || status\n}\n\nconst getPriorityClass = (priority) => {\n  const classes = {\n    low: 'bg-green-100 text-green-800',\n    medium: 'bg-yellow-100 text-yellow-800',\n    high: 'bg-orange-100 text-orange-800',\n    urgent: 'bg-red-100 text-red-800'\n  }\n  return classes[priority] || 'bg-gray-100 text-gray-800'\n}\n\nconst getPriorityLabel = (priority) => {\n  const labels = {\n    low: 'Bassa',\n    medium: 'Media',\n    high: 'Alta',\n    urgent: 'Urgente'\n  }\n  return labels[priority] || priority\n}\n\nconst getInitials = (firstName, lastName) => {\n  return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase()\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\n// Debounced search\nlet searchTimeout\nconst debouncedSearch = () => {\n  clearTimeout(searchTimeout)\n  searchTimeout = setTimeout(() => {\n    loadTasks()\n  }, 300)\n}\n\n// Watchers\nwatch(() => props.project?.id, (newId) => {\n  if (newId) {\n    loadTasks()\n  }\n})\n\n// Lifecycle\nonMounted(() => {\n  if (props.project?.id) {\n    loadTasks()\n  }\n})\n\n// Expose methods to parent\ndefineExpose({\n  refresh: loadTasks\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header con filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">Task del Progetto</h3>\n          <button \n            v-if=\"canManageTasks\"\n            @click=\"showCreateModal = true\"\n            class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Nuovo Task\n          </button>\n        </div>\n\n        <!-- Filtri Task -->\n        <div class=\"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Stato</label>\n            <select \n              v-model=\"filters.status\" \n              @change=\"loadTasks\"\n              class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"\">Tutti gli stati</option>\n              <option value=\"todo\">Da fare</option>\n              <option value=\"in-progress\">In corso</option>\n              <option value=\"review\">In revisione</option>\n              <option value=\"done\">Completato</option>\n            </select>\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Priorità</label>\n            <select \n              v-model=\"filters.priority\" \n              @change=\"loadTasks\"\n              class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"\">Tutte le priorità</option>\n              <option value=\"low\">Bassa</option>\n              <option value=\"medium\">Media</option>\n              <option value=\"high\">Alta</option>\n              <option value=\"urgent\">Urgente</option>\n            </select>\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Assegnatario</label>\n            <select \n              v-model=\"filters.assignee_id\" \n              @change=\"loadTasks\"\n              class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n              <option value=\"\">Tutti</option>\n              <option \n                v-for=\"member in project?.team_members || []\" \n                :key=\"member.id\" \n                :value=\"member.id\"\n              >\n                {{ member.first_name }} {{ member.last_name }}\n              </option>\n            </select>\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ricerca</label>\n            <input \n              v-model=\"filters.search\" \n              @input=\"debouncedSearch\"\n              type=\"text\" \n              placeholder=\"Cerca task...\"\n              class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n            >\n          </div>\n        </div>\n\n        <!-- Controlli vista -->\n        <div class=\"mt-4 flex items-center justify-between\">\n          <div class=\"flex items-center space-x-4\">\n            <span class=\"text-sm text-gray-500 dark:text-gray-400\">Vista:</span>\n            <button\n              @click=\"viewMode = 'list'\"\n              :class=\"viewMode === 'list' ? 'bg-primary-100 text-primary-700' : 'text-gray-500 hover:text-gray-700'\"\n              class=\"px-3 py-1 rounded-md text-sm font-medium\"\n            >\n              Lista\n            </button>\n            <button\n              @click=\"viewMode = 'kanban'\"\n              :class=\"viewMode === 'kanban' ? 'bg-primary-100 text-primary-700' : 'text-gray-500 hover:text-gray-700'\"\n              class=\"px-3 py-1 rounded-md text-sm font-medium\"\n            >\n              Kanban\n            </button>\n          </div>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            {{ tasks.length }} task trovati\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading -->\n    <div v-if=\"tasksLoading\" class=\"flex justify-center py-8\">\n      <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n    </div>\n\n    <!-- Error -->\n    <div v-if=\"error\" class=\"bg-red-50 border border-red-200 rounded-md p-4\">\n      <p class=\"text-red-600\">{{ error }}</p>\n    </div>\n\n    <!-- Vista Lista -->\n    <div v-if=\"!tasksLoading && viewMode === 'list'\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n        <!-- Header tabella -->\n        <div class=\"bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n          <div class=\"col-span-4\">Task</div>\n          <div class=\"col-span-2\">Assegnatario</div>\n          <div class=\"col-span-1\">Stato</div>\n          <div class=\"col-span-1\">Priorità</div>\n          <div class=\"col-span-2\">Scadenza</div>\n          <div class=\"col-span-1\">Ore</div>\n          <div class=\"col-span-1\">Azioni</div>\n        </div>\n\n        <!-- Righe task -->\n        <div v-for=\"task in tasks\" :key=\"task.id\" class=\"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700\">\n          <div class=\"col-span-4\">\n            <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ task.name }}</div>\n            <div v-if=\"task.description\" class=\"text-sm text-gray-500 dark:text-gray-400 truncate\">{{ task.description }}</div>\n          </div>\n          <div class=\"col-span-2\">\n            <div v-if=\"task.assignee\" class=\"flex items-center\">\n              <div class=\"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700\">\n                {{ getInitials(task.assignee.first_name, task.assignee.last_name) }}\n              </div>\n              <div class=\"ml-2\">\n                <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ task.assignee.first_name }} {{ task.assignee.last_name }}\n                </div>\n              </div>\n            </div>\n            <span v-else class=\"text-sm text-gray-500 dark:text-gray-400\">Non assegnato</span>\n          </div>\n          <div class=\"col-span-1\">\n            <span :class=\"getStatusClass(task.status)\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\">\n              {{ getStatusLabel(task.status) }}\n            </span>\n          </div>\n          <div class=\"col-span-1\">\n            <span :class=\"getPriorityClass(task.priority)\" class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\">\n              {{ getPriorityLabel(task.priority) }}\n            </span>\n          </div>\n          <div class=\"col-span-2\">\n            <div v-if=\"task.due_date\" class=\"text-sm text-gray-900 dark:text-white\">\n              {{ formatDate(task.due_date) }}\n            </div>\n            <span v-else class=\"text-sm text-gray-500 dark:text-gray-400\">-</span>\n          </div>\n          <div class=\"col-span-1\">\n            <div class=\"text-sm text-gray-900 dark:text-white\">\n              {{ task.actual_hours || 0 }}h\n              <span v-if=\"task.estimated_hours\" class=\"text-gray-500\">/ {{ task.estimated_hours }}h</span>\n            </div>\n          </div>\n          <div class=\"col-span-1\">\n            <div class=\"flex items-center space-x-2\">\n              <button \n                @click=\"editTask(task)\"\n                class=\"text-primary-600 hover:text-primary-900 text-sm\"\n              >\n                Modifica\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Empty state -->\n        <div v-if=\"tasks.length === 0\" class=\"px-6 py-12 text-center\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Nessun task trovato</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Vista Kanban -->\n    <div v-if=\"!tasksLoading && viewMode === 'kanban'\" class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div v-for=\"status in kanbanColumns\" :key=\"status.value\" class=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n        <div class=\"flex items-center justify-between mb-4\">\n          <h4 class=\"font-medium text-gray-900 dark:text-white\">{{ status.label }}</h4>\n          <span class=\"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs\">\n            {{ getTasksByStatus(status.value).length }}\n          </span>\n        </div>\n        <div class=\"space-y-3\">\n          <div \n            v-for=\"task in getTasksByStatus(status.value)\" \n            :key=\"task.id\"\n            class=\"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow\"\n            @click=\"editTask(task)\"\n          >\n            <div class=\"font-medium text-sm text-gray-900 dark:text-white mb-1\">{{ task.name }}</div>\n            <div v-if=\"task.description\" class=\"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2\">\n              {{ task.description }}\n            </div>\n            <div class=\"flex items-center justify-between\">\n              <span :class=\"getPriorityClass(task.priority)\" class=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium\">\n                {{ getPriorityLabel(task.priority) }}\n              </span>\n              <div v-if=\"task.assignee\" class=\"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700\">\n                {{ getInitials(task.assignee.first_name, task.assignee.last_name) }}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Creazione/Modifica Task -->\n    <div v-if=\"showCreateModal || showEditModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ showEditModal ? 'Modifica Task' : 'Nuovo Task' }}\n          </h3>\n          \n          <form @submit.prevent=\"saveTask\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Nome</label>\n                <input \n                  v-model=\"formData.name\" \n                  type=\"text\" \n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Descrizione</label>\n                <textarea \n                  v-model=\"formData.description\" \n                  rows=\"3\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                ></textarea>\n              </div>\n\n              <div class=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Stato</label>\n                  <select \n                    v-model=\"formData.status\"\n                    class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  >\n                    <option value=\"todo\">Da fare</option>\n                    <option value=\"in-progress\">In corso</option>\n                    <option value=\"review\">In revisione</option>\n                    <option value=\"done\">Completato</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Priorità</label>\n                  <select \n                    v-model=\"formData.priority\"\n                    class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  >\n                    <option value=\"low\">Bassa</option>\n                    <option value=\"medium\">Media</option>\n                    <option value=\"high\">Alta</option>\n                    <option value=\"urgent\">Urgente</option>\n                  </select>\n                </div>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Assegnatario</label>\n                <select \n                  v-model=\"formData.assignee_id\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Non assegnato</option>\n                  <option \n                    v-for=\"member in project?.team_members || []\" \n                    :key=\"member.id\" \n                    :value=\"member.id\"\n                  >\n                    {{ member.first_name }} {{ member.last_name }}\n                  </option>\n                </select>\n              </div>\n\n              <div class=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Scadenza</label>\n                  <input \n                    v-model=\"formData.due_date\" \n                    type=\"date\"\n                    class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  >\n                </div>\n\n                <div>\n                  <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore stimate</label>\n                  <input \n                    v-model=\"formData.estimated_hours\" \n                    type=\"number\" \n                    step=\"0.5\"\n                    min=\"0\"\n                    class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                  >\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Salvataggio...' : (showEditModal ? 'Aggiorna' : 'Crea') }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\nimport { usePermissions } from '@/composables/usePermissions'\nimport api from '@/utils/api'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst authStore = useAuthStore()\nconst { hasPermission } = usePermissions()\n\n// State\nconst tasks = ref([])\nconst tasksLoading = ref(false)\nconst error = ref('')\nconst viewMode = ref('list')\nconst saving = ref(false)\n\n// Filtri\nconst filters = ref({\n  status: '',\n  priority: '',\n  assignee_id: '',\n  search: ''\n})\n\n// Modal\nconst showCreateModal = ref(false)\nconst showEditModal = ref(false)\nconst editingTask = ref(null)\n\n// Form data\nconst formData = ref({\n  name: '',\n  description: '',\n  status: 'todo',\n  priority: 'medium',\n  assignee_id: '',\n  due_date: '',\n  estimated_hours: null\n})\n\n// Computed\nconst canManageTasks = computed(() => {\n  return hasPermission.value('manage_project_tasks')\n})\n\nconst kanbanColumns = [\n  { value: 'todo', label: 'Da fare' },\n  { value: 'in-progress', label: 'In corso' },\n  { value: 'review', label: 'In revisione' },\n  { value: 'done', label: 'Completato' }\n]\n\n// Methods\nconst loadTasks = async () => {\n  if (!props.project?.id) return\n  \n  tasksLoading.value = true\n  error.value = ''\n  \n  try {\n    const params = new URLSearchParams({\n      project_id: props.project.id,\n      ...filters.value\n    })\n\n    const response = await fetch(`/api/tasks?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel caricamento dei task')\n    }\n\n    const result = await response.json()\n    tasks.value = result.data?.tasks || result.tasks || []\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    tasksLoading.value = false\n  }\n}\n\nconst saveTask = async () => {\n  saving.value = true\n  \n  try {\n    const url = showEditModal.value \n      ? `/api/tasks/${editingTask.value.id}`\n      : '/api/tasks'\n    \n    const method = showEditModal.value ? 'PUT' : 'POST'\n    const data = { ...formData.value, project_id: props.project.id }\n\n    const response = await fetch(url, {\n      method,\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify(data)\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio del task')\n    }\n\n    await loadTasks()\n    closeModal()\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editTask = (task) => {\n  editingTask.value = task\n  formData.value = {\n    name: task.name,\n    description: task.description || '',\n    status: task.status,\n    priority: task.priority,\n    assignee_id: task.assignee_id || '',\n    due_date: task.due_date ? task.due_date.split('T')[0] : '',\n    estimated_hours: task.estimated_hours\n  }\n  showEditModal.value = true\n}\n\nconst closeModal = () => {\n  showCreateModal.value = false\n  showEditModal.value = false\n  editingTask.value = null\n  formData.value = {\n    name: '',\n    description: '',\n    status: 'todo',\n    priority: 'medium',\n    assignee_id: '',\n    due_date: '',\n    estimated_hours: null\n  }\n}\n\nconst getTasksByStatus = (status) => {\n  return tasks.value.filter(task => task.status === status)\n}\n\nconst getStatusClass = (status) => {\n  const classes = {\n    todo: 'bg-gray-100 text-gray-800',\n    'in-progress': 'bg-blue-100 text-blue-800',\n    review: 'bg-yellow-100 text-yellow-800',\n    done: 'bg-green-100 text-green-800'\n  }\n  return classes[status] || 'bg-gray-100 text-gray-800'\n}\n\nconst getStatusLabel = (status) => {\n  const labels = {\n    todo: 'Da fare',\n    'in-progress': 'In corso',\n    review: 'In revisione',\n    done: 'Completato'\n  }\n  return labels[status] || status\n}\n\nconst getPriorityClass = (priority) => {\n  const classes = {\n    low: 'bg-green-100 text-green-800',\n    medium: 'bg-yellow-100 text-yellow-800',\n    high: 'bg-orange-100 text-orange-800',\n    urgent: 'bg-red-100 text-red-800'\n  }\n  return classes[priority] || 'bg-gray-100 text-gray-800'\n}\n\nconst getPriorityLabel = (priority) => {\n  const labels = {\n    low: 'Bassa',\n    medium: 'Media',\n    high: 'Alta',\n    urgent: 'Urgente'\n  }\n  return labels[priority] || priority\n}\n\nconst getInitials = (firstName, lastName) => {\n  return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase()\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\n// Debounced search\nlet searchTimeout\nconst debouncedSearch = () => {\n  clearTimeout(searchTimeout)\n  searchTimeout = setTimeout(() => {\n    loadTasks()\n  }, 300)\n}\n\n// Watchers\nwatch(() => props.project?.id, (newId) => {\n  if (newId) {\n    loadTasks()\n  }\n})\n\n// Lifecycle\nonMounted(() => {\n  if (props.project?.id) {\n    loadTasks()\n  }\n})\n\n// Expose methods to parent\ndefineExpose({\n  refresh: loadTasks\n})\n</script>"}