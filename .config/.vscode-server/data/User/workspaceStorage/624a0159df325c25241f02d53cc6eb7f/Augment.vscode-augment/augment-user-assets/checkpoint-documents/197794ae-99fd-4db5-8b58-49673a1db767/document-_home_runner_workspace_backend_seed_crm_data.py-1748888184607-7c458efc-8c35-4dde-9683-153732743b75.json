{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/seed_crm_data.py"}, "originalCode": "#!/usr/bin/env python3\n\"\"\"\nSeed specifico per dati CRM - Clients, Proposals, Contracts\nCollega contratti a progetti esistenti per testing\n\"\"\"\n\nimport sys\nimport os\nsys.path.append(os.path.dirname(os.path.abspath(__file__)))\n\nfrom app import create_app\nfrom extensions import db\nfrom models import Client, Contact, Proposal, Contract, Project, User\nfrom datetime import datetime, date, timedelta\nimport random\n\ndef seed_crm_clients():\n    \"\"\"Crea clienti CRM con diversi stati\"\"\"\n    print(\"🏢 Creazione clienti CRM...\")\n    \n    clients_data = [\n        {\n            'name': 'TechCorp Solutions',\n            'industry': 'Technology',\n            'description': 'Azienda leader nel settore tecnologico',\n            'website': 'https://techcorp.com',\n            'address': 'Via Milano 123, Milano',\n            'status': 'client'\n        },\n        {\n            'name': 'StartupInnovation',\n            'industry': 'Startup',\n            'description': 'Startup innovativa nel fintech',\n            'website': 'https://startupinnovation.com',\n            'address': 'Via Roma 456, Roma',\n            'status': 'prospect'\n        },\n        {\n            'name': 'MegaCorp Industries',\n            'industry': 'Manufacturing',\n            'description': 'Grande azienda manifatturiera',\n            'website': 'https://megacorp.com',\n            'address': 'Via Torino 789, Torino',\n            'status': 'lead'\n        },\n        {\n            'name': 'DigitalAgency Pro',\n            'industry': 'Marketing',\n            'description': 'Agenzia di marketing digitale',\n            'website': 'https://digitalagency.com',\n            'address': 'Via Napoli 321, Napoli',\n            'status': 'client'\n        },\n        {\n            'name': 'HealthTech Solutions',\n            'industry': 'Healthcare',\n            'description': 'Soluzioni tecnologiche per la sanità',\n            'website': 'https://healthtech.com',\n            'address': 'Via Bologna 654, Bologna',\n            'status': 'prospect'\n        }\n    ]\n    \n    created_clients = []\n    for client_data in clients_data:\n        # Verifica se esiste già\n        existing = Client.query.filter_by(name=client_data['name']).first()\n        if not existing:\n            client = Client(**client_data)\n            db.session.add(client)\n            created_clients.append(client)\n    \n    db.session.commit()\n    print(f\"✅ Creati {len(created_clients)} nuovi clienti\")\n    \n    # Ritorna tutti i clienti (esistenti + nuovi)\n    return Client.query.all()\n\ndef seed_crm_contacts(clients):\n    \"\"\"Crea contatti per i clienti\"\"\"\n    print(\"👥 Creazione contatti...\")\n    \n    contacts_data = [\n        {'first_name': 'Marco', 'last_name': 'Rossi', 'email': '<EMAIL>', 'phone': '+39 02 1234567', 'position': 'CTO'},\n        {'first_name': 'Laura', 'last_name': 'Bianchi', 'email': '<EMAIL>', 'phone': '+39 06 2345678', 'position': 'CEO'},\n        {'first_name': 'Giuseppe', 'last_name': 'Verdi', 'email': '<EMAIL>', 'phone': '+39 011 3456789', 'position': 'IT Director'},\n        {'first_name': 'Anna', 'last_name': 'Neri', 'email': '<EMAIL>', 'phone': '+39 081 4567890', 'position': 'Marketing Manager'},\n        {'first_name': 'Francesco', 'last_name': 'Blu', 'email': '<EMAIL>', 'phone': '+39 051 5678901', 'position': 'Product Manager'}\n    ]\n    \n    created_contacts = []\n    for i, contact_data in enumerate(contacts_data):\n        if i < len(clients):\n            # Verifica se esiste già\n            existing = Contact.query.filter_by(email=contact_data['email']).first()\n            if not existing:\n                contact = Contact(\n                    client_id=clients[i].id,\n                    **contact_data\n                )\n                db.session.add(contact)\n                created_contacts.append(contact)\n    \n    db.session.commit()\n    print(f\"✅ Creati {len(created_contacts)} nuovi contatti\")\n    return created_contacts\n\ndef seed_crm_proposals(clients, users):\n    \"\"\"Crea proposte per i clienti\"\"\"\n    print(\"📄 Creazione proposte...\")\n    \n    proposals_data = [\n        {\n            'title': 'Sviluppo Piattaforma E-commerce',\n            'description': 'Sviluppo completo piattaforma e-commerce con React e Node.js',\n            'value': 45000.00,\n            'status': 'accepted',\n            'valid_until': date.today() + timedelta(days=30)\n        },\n        {\n            'title': 'App Mobile Fintech',\n            'description': 'Sviluppo app mobile per servizi finanziari',\n            'value': 35000.00,\n            'status': 'negotiating',\n            'valid_until': date.today() + timedelta(days=15)\n        },\n        {\n            'title': 'Sistema Gestionale ERP',\n            'description': 'Implementazione sistema ERP personalizzato',\n            'value': 75000.00,\n            'status': 'sent',\n            'valid_until': date.today() + timedelta(days=45)\n        },\n        {\n            'title': 'Campagna Marketing Digitale',\n            'description': 'Strategia e implementazione campagna marketing',\n            'value': 15000.00,\n            'status': 'accepted',\n            'valid_until': date.today() + timedelta(days=60)\n        },\n        {\n            'title': 'Piattaforma Telemedicina',\n            'description': 'Sviluppo piattaforma per consultazioni mediche online',\n            'value': 55000.00,\n            'status': 'draft',\n            'valid_until': date.today() + timedelta(days=90)\n        }\n    ]\n    \n    created_proposals = []\n    for i, proposal_data in enumerate(proposals_data):\n        if i < len(clients):\n            proposal = Proposal(\n                client_id=clients[i].id,\n                created_by=random.choice(users).id,\n                **proposal_data\n            )\n            \n            # Set sent_date per proposte inviate\n            if proposal.status in ['sent', 'negotiating', 'accepted']:\n                proposal.sent_date = datetime.now() - timedelta(days=random.randint(1, 30))\n            \n            db.session.add(proposal)\n            created_proposals.append(proposal)\n    \n    db.session.commit()\n    print(f\"✅ Creati {len(created_proposals)} nuove proposte\")\n    return created_proposals\n\ndef seed_crm_contracts(clients):\n    \"\"\"Crea contratti per clienti\"\"\"\n    print(\"📋 Creazione contratti...\")\n    \n    contracts_data = [\n        {\n            'contract_number': 'CONTR-2024-001',\n            'title': 'Contratto Sviluppo E-commerce TechCorp',\n            'description': 'Contratto per sviluppo piattaforma e-commerce',\n            'contract_type': 'time_and_materials',\n            'hourly_rate': 85.00,\n            'budget_hours': 500.0,\n            'budget_amount': 42500.00,\n            'start_date': date(2024, 1, 15),\n            'end_date': date(2024, 6, 15),\n            'status': 'active'\n        },\n        {\n            'contract_number': 'CONTR-2024-002',\n            'title': 'Contratto Marketing DigitalAgency',\n            'description': 'Contratto per servizi di marketing digitale',\n            'contract_type': 'retainer',\n            'hourly_rate': 75.00,\n            'budget_hours': 200.0,\n            'budget_amount': 15000.00,\n            'start_date': date(2024, 2, 1),\n            'end_date': date(2024, 7, 31),\n            'status': 'active'\n        },\n        {\n            'contract_number': 'CONTR-2024-003',\n            'title': 'Contratto Consulenza HealthTech',\n            'description': 'Contratto per consulenza tecnologica',\n            'contract_type': 'fixed_price',\n            'hourly_rate': 95.00,\n            'budget_hours': 300.0,\n            'budget_amount': 28500.00,\n            'start_date': date(2024, 3, 1),\n            'end_date': date(2024, 8, 31),\n            'status': 'signed'\n        }\n    ]\n    \n    created_contracts = []\n    # Prendi solo i primi 3 clienti per i contratti\n    contract_clients = [c for c in clients if c.status == 'client'][:3]\n    \n    for i, contract_data in enumerate(contracts_data):\n        if i < len(contract_clients):\n            contract = Contract(\n                client_id=contract_clients[i].id,\n                **contract_data\n            )\n            db.session.add(contract)\n            created_contracts.append(contract)\n    \n    db.session.commit()\n    print(f\"✅ Creati {len(created_contracts)} nuovi contratti\")\n    return created_contracts\n\ndef link_projects_to_contracts(contracts):\n    \"\"\"Collega progetti esistenti ai contratti\"\"\"\n    print(\"🔗 Collegamento progetti a contratti...\")\n    \n    # Prendi progetti esistenti senza contratto\n    projects = Project.query.filter_by(contract_id=None).limit(3).all()\n    \n    linked_count = 0\n    for i, project in enumerate(projects):\n        if i < len(contracts):\n            project.contract_id = contracts[i].id\n            linked_count += 1\n    \n    db.session.commit()\n    print(f\"✅ Collegati {linked_count} progetti a contratti\")\n\ndef main():\n    \"\"\"Esegue il seed CRM completo\"\"\"\n    app = create_app()\n    \n    with app.app_context():\n        print(\"🚀 Avvio seed CRM...\")\n        \n        # Ottieni utenti esistenti\n        users = User.query.all()\n        if not users:\n            print(\"❌ Nessun utente trovato! Esegui prima il seed principale.\")\n            return\n        \n        # Seed CRM\n        clients = seed_crm_clients()\n        contacts = seed_crm_contacts(clients)\n        proposals = seed_crm_proposals(clients, users)\n        contracts = seed_crm_contracts(clients)\n        \n        # Collega progetti a contratti\n        link_projects_to_contracts(contracts)\n        \n        print(\"\\n🎉 Seed CRM completato con successo!\")\n        print(f\"   - {len(clients)} clienti totali\")\n        print(f\"   - {len(contacts)} contatti\")\n        print(f\"   - {len(proposals)} proposte\")\n        print(f\"   - {len(contracts)} contratti\")\n        print(\"   - Progetti collegati a contratti\")\n\nif __name__ == '__main__':\n    main()\n", "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nSeed specifico per dati CRM - Clients, Proposals, Contracts\nCollega contratti a progetti esistenti per testing\n\"\"\"\n\nimport sys\nimport os\nsys.path.append(os.path.dirname(os.path.abspath(__file__)))\n\nfrom app import create_app\nfrom extensions import db\nfrom models import Client, Contact, Proposal, Contract, Project, User\nfrom datetime import datetime, date, timedelta\nimport random\n\ndef seed_crm_clients():\n    \"\"\"Crea clienti CRM con diversi stati\"\"\"\n    print(\"🏢 Creazione clienti CRM...\")\n    \n    clients_data = [\n        {\n            'name': 'TechCorp Solutions',\n            'industry': 'Technology',\n            'description': 'Azienda leader nel settore tecnologico',\n            'website': 'https://techcorp.com',\n            'address': 'Via Milano 123, Milano',\n            'status': 'client'\n        },\n        {\n            'name': 'StartupInnovation',\n            'industry': 'Startup',\n            'description': 'Startup innovativa nel fintech',\n            'website': 'https://startupinnovation.com',\n            'address': 'Via Roma 456, Roma',\n            'status': 'prospect'\n        },\n        {\n            'name': 'MegaCorp Industries',\n            'industry': 'Manufacturing',\n            'description': 'Grande azienda manifatturiera',\n            'website': 'https://megacorp.com',\n            'address': 'Via Torino 789, Torino',\n            'status': 'lead'\n        },\n        {\n            'name': 'DigitalAgency Pro',\n            'industry': 'Marketing',\n            'description': 'Agenzia di marketing digitale',\n            'website': 'https://digitalagency.com',\n            'address': 'Via Napoli 321, Napoli',\n            'status': 'client'\n        },\n        {\n            'name': 'HealthTech Solutions',\n            'industry': 'Healthcare',\n            'description': 'Soluzioni tecnologiche per la sanità',\n            'website': 'https://healthtech.com',\n            'address': 'Via Bologna 654, Bologna',\n            'status': 'prospect'\n        }\n    ]\n    \n    created_clients = []\n    for client_data in clients_data:\n        # Verifica se esiste già\n        existing = Client.query.filter_by(name=client_data['name']).first()\n        if not existing:\n            client = Client(**client_data)\n            db.session.add(client)\n            created_clients.append(client)\n    \n    db.session.commit()\n    print(f\"✅ Creati {len(created_clients)} nuovi clienti\")\n    \n    # Ritorna tutti i clienti (esistenti + nuovi)\n    return Client.query.all()\n\ndef seed_crm_contacts(clients):\n    \"\"\"Crea contatti per i clienti\"\"\"\n    print(\"👥 Creazione contatti...\")\n    \n    contacts_data = [\n        {'first_name': 'Marco', 'last_name': 'Rossi', 'email': '<EMAIL>', 'phone': '+39 02 1234567', 'position': 'CTO'},\n        {'first_name': 'Laura', 'last_name': 'Bianchi', 'email': '<EMAIL>', 'phone': '+39 06 2345678', 'position': 'CEO'},\n        {'first_name': 'Giuseppe', 'last_name': 'Verdi', 'email': '<EMAIL>', 'phone': '+39 011 3456789', 'position': 'IT Director'},\n        {'first_name': 'Anna', 'last_name': 'Neri', 'email': '<EMAIL>', 'phone': '+39 081 4567890', 'position': 'Marketing Manager'},\n        {'first_name': 'Francesco', 'last_name': 'Blu', 'email': '<EMAIL>', 'phone': '+39 051 5678901', 'position': 'Product Manager'}\n    ]\n    \n    created_contacts = []\n    for i, contact_data in enumerate(contacts_data):\n        if i < len(clients):\n            # Verifica se esiste già\n            existing = Contact.query.filter_by(email=contact_data['email']).first()\n            if not existing:\n                contact = Contact(\n                    client_id=clients[i].id,\n                    **contact_data\n                )\n                db.session.add(contact)\n                created_contacts.append(contact)\n    \n    db.session.commit()\n    print(f\"✅ Creati {len(created_contacts)} nuovi contatti\")\n    return created_contacts\n\ndef seed_crm_proposals(clients, users):\n    \"\"\"Crea proposte per i clienti\"\"\"\n    print(\"📄 Creazione proposte...\")\n    \n    proposals_data = [\n        {\n            'title': 'Sviluppo Piattaforma E-commerce',\n            'description': 'Sviluppo completo piattaforma e-commerce con React e Node.js',\n            'value': 45000.00,\n            'status': 'accepted',\n            'expiry_date': date.today() + timedelta(days=30)\n        },\n        {\n            'title': 'App Mobile Fintech',\n            'description': 'Sviluppo app mobile per servizi finanziari',\n            'value': 35000.00,\n            'status': 'negotiating',\n            'valid_until': date.today() + timedelta(days=15)\n        },\n        {\n            'title': 'Sistema Gestionale ERP',\n            'description': 'Implementazione sistema ERP personalizzato',\n            'value': 75000.00,\n            'status': 'sent',\n            'valid_until': date.today() + timedelta(days=45)\n        },\n        {\n            'title': 'Campagna Marketing Digitale',\n            'description': 'Strategia e implementazione campagna marketing',\n            'value': 15000.00,\n            'status': 'accepted',\n            'valid_until': date.today() + timedelta(days=60)\n        },\n        {\n            'title': 'Piattaforma Telemedicina',\n            'description': 'Sviluppo piattaforma per consultazioni mediche online',\n            'value': 55000.00,\n            'status': 'draft',\n            'valid_until': date.today() + timedelta(days=90)\n        }\n    ]\n    \n    created_proposals = []\n    for i, proposal_data in enumerate(proposals_data):\n        if i < len(clients):\n            proposal = Proposal(\n                client_id=clients[i].id,\n                created_by=random.choice(users).id,\n                **proposal_data\n            )\n            \n            # Set sent_date per proposte inviate\n            if proposal.status in ['sent', 'negotiating', 'accepted']:\n                proposal.sent_date = datetime.now() - timedelta(days=random.randint(1, 30))\n            \n            db.session.add(proposal)\n            created_proposals.append(proposal)\n    \n    db.session.commit()\n    print(f\"✅ Creati {len(created_proposals)} nuove proposte\")\n    return created_proposals\n\ndef seed_crm_contracts(clients):\n    \"\"\"Crea contratti per clienti\"\"\"\n    print(\"📋 Creazione contratti...\")\n    \n    contracts_data = [\n        {\n            'contract_number': 'CONTR-2024-001',\n            'title': 'Contratto Sviluppo E-commerce TechCorp',\n            'description': 'Contratto per sviluppo piattaforma e-commerce',\n            'contract_type': 'time_and_materials',\n            'hourly_rate': 85.00,\n            'budget_hours': 500.0,\n            'budget_amount': 42500.00,\n            'start_date': date(2024, 1, 15),\n            'end_date': date(2024, 6, 15),\n            'status': 'active'\n        },\n        {\n            'contract_number': 'CONTR-2024-002',\n            'title': 'Contratto Marketing DigitalAgency',\n            'description': 'Contratto per servizi di marketing digitale',\n            'contract_type': 'retainer',\n            'hourly_rate': 75.00,\n            'budget_hours': 200.0,\n            'budget_amount': 15000.00,\n            'start_date': date(2024, 2, 1),\n            'end_date': date(2024, 7, 31),\n            'status': 'active'\n        },\n        {\n            'contract_number': 'CONTR-2024-003',\n            'title': 'Contratto Consulenza HealthTech',\n            'description': 'Contratto per consulenza tecnologica',\n            'contract_type': 'fixed_price',\n            'hourly_rate': 95.00,\n            'budget_hours': 300.0,\n            'budget_amount': 28500.00,\n            'start_date': date(2024, 3, 1),\n            'end_date': date(2024, 8, 31),\n            'status': 'signed'\n        }\n    ]\n    \n    created_contracts = []\n    # Prendi solo i primi 3 clienti per i contratti\n    contract_clients = [c for c in clients if c.status == 'client'][:3]\n    \n    for i, contract_data in enumerate(contracts_data):\n        if i < len(contract_clients):\n            contract = Contract(\n                client_id=contract_clients[i].id,\n                **contract_data\n            )\n            db.session.add(contract)\n            created_contracts.append(contract)\n    \n    db.session.commit()\n    print(f\"✅ Creati {len(created_contracts)} nuovi contratti\")\n    return created_contracts\n\ndef link_projects_to_contracts(contracts):\n    \"\"\"Collega progetti esistenti ai contratti\"\"\"\n    print(\"🔗 Collegamento progetti a contratti...\")\n    \n    # Prendi progetti esistenti senza contratto\n    projects = Project.query.filter_by(contract_id=None).limit(3).all()\n    \n    linked_count = 0\n    for i, project in enumerate(projects):\n        if i < len(contracts):\n            project.contract_id = contracts[i].id\n            linked_count += 1\n    \n    db.session.commit()\n    print(f\"✅ Collegati {linked_count} progetti a contratti\")\n\ndef main():\n    \"\"\"Esegue il seed CRM completo\"\"\"\n    app = create_app()\n    \n    with app.app_context():\n        print(\"🚀 Avvio seed CRM...\")\n        \n        # Ottieni utenti esistenti\n        users = User.query.all()\n        if not users:\n            print(\"❌ Nessun utente trovato! Esegui prima il seed principale.\")\n            return\n        \n        # Seed CRM\n        clients = seed_crm_clients()\n        contacts = seed_crm_contacts(clients)\n        proposals = seed_crm_proposals(clients, users)\n        contracts = seed_crm_contracts(clients)\n        \n        # Collega progetti a contratti\n        link_projects_to_contracts(contracts)\n        \n        print(\"\\n🎉 Seed CRM completato con successo!\")\n        print(f\"   - {len(clients)} clienti totali\")\n        print(f\"   - {len(contacts)} contatti\")\n        print(f\"   - {len(proposals)} proposte\")\n        print(f\"   - {len(contracts)} contratti\")\n        print(\"   - Progetti collegati a contratti\")\n\nif __name__ == '__main__':\n    main()\n"}