{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/dashboard.py"}, "originalCode": "\"\"\"\nAPI endpoints for dashboard data.\nProvides REST API for dashboard statistics, recent activities, and quick actions.\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user\nfrom sqlalchemy import func, desc, and_, or_\nfrom datetime import datetime, timedelta\n\nfrom extensions import db\nfrom models import (\n    Project, Task, Timesheet, News, Event, KPI, Client, Document,\n    FundingOpportunity, User, Notification, Department, UserSkill, Skill\n)\nfrom utils.api_utils import (\n    api_response, api_login_required, handle_api_error\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_DASHBOARD, PERMISSION_VIEW_ALL_PROJECTS,\n    PERMISSION_VIEW_REPORTS, PERMISSION_VIEW_CRM, user_has_permission\n)\nfrom extensions import csrf\n\n# Create blueprint\napi_dashboard = Blueprint('api_dashboard', __name__, url_prefix='/dashboard')\n\n@api_dashboard.route('/stats', methods=['GET'])\*************\n@api_login_required\ndef get_dashboard_stats():\n    \"\"\"\n    Get dashboard statistics and KPIs.\n\n    Returns:\n        JSON with dashboard statistics including:\n        - Active projects count\n        - Tasks statistics\n        - Recent activities count\n        - KPIs data\n    \"\"\"\n    try:\n        now = datetime.utcnow()\n        week_ahead = now + timedelta(days=7)\n\n        # Active projects count - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            active_projects_count = Project.query.filter_by(status='active').count()\n            total_projects_count = Project.query.count()\n        else:\n            # Only projects where user is a team member\n            active_projects_count = Project.query.filter_by(\n                status='active'\n            ).filter(\n                Project.team_members.any(id=current_user.id)\n            ).count()\n            total_projects_count = Project.query.filter(\n                Project.team_members.any(id=current_user.id)\n            ).count()\n\n        # Tasks statistics - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            total_tasks = Task.query.count()\n            pending_tasks = Task.query.filter(Task.status.in_(['todo', 'in-progress'])).count()\n            completed_tasks = Task.query.filter_by(status='done').count()\n            overdue_tasks = Task.query.filter(\n                and_(Task.due_date < now.date(), Task.status != 'done')\n            ).count()\n        else:\n            total_tasks = Task.query.filter_by(assignee_id=current_user.id).count()\n            pending_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.status.in_(['todo', 'in-progress'])\n                )\n            ).count()\n            completed_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.status == 'done'\n                )\n            ).count()\n            overdue_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.due_date < now.date(),\n                    Task.status != 'done'\n                )\n            ).count()\n\n        # Client count - visible only to those with CRM permission\n        if user_has_permission(current_user.role, PERMISSION_VIEW_CRM):\n            client_count = Client.query.count()\n        else:\n            client_count = 0\n\n        # Team statistics\n        total_users = User.query.filter_by(is_active=True).count()\n        total_departments = Department.query.filter_by(is_active=True).count()\n\n        # Recent activities count\n        recent_timesheets_count = Timesheet.query.filter(\n            and_(\n                Timesheet.user_id == current_user.id,\n                Timesheet.date >= (now - timedelta(days=7)).date()\n            )\n        ).count()\n\n        # Unread notifications count\n        unread_notifications = Notification.query.filter_by(\n            user_id=current_user.id,\n            is_read=False\n        ).count()\n\n        stats_data = {\n            'projects': {\n                'active': active_projects_count,\n                'total': total_projects_count\n            },\n            'tasks': {\n                'total': total_tasks,\n                'pending': pending_tasks,\n                'completed': completed_tasks,\n                'overdue': overdue_tasks\n            },\n            'team': {\n                'users': total_users,\n                'departments': total_departments,\n                'clients': client_count\n            },\n            'activities': {\n                'recent_timesheets': recent_timesheets_count,\n                'unread_notifications': unread_notifications\n            }\n        }\n\n        return api_response(\n            data=stats_data,\n            message=\"Dashboard statistics retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/recent-activities', methods=['GET'])\*************\n@api_login_required\ndef get_recent_activities():\n    \"\"\"\n    Get recent activities for the dashboard.\n\n    Query Parameters:\n        limit (int): Number of activities to return (default: 10, max: 50)\n\n    Returns:\n        JSON with recent activities including:\n        - Recent tasks\n        - Recent timesheet entries\n        - Recent projects\n        - Recent events\n    \"\"\"\n    try:\n        # Get limit parameter\n        limit = min(int(request.args.get('limit', 10)), 50)\n\n        now = datetime.utcnow()\n        week_ago = now - timedelta(days=7)\n\n        activities = []\n\n        # Recent tasks (assigned to user or all if has permission)\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            recent_tasks = Task.query.filter(\n                Task.updated_at >= week_ago\n            ).order_by(desc(Task.updated_at)).limit(limit).all()\n        else:\n            recent_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.updated_at >= week_ago\n                )\n            ).order_by(desc(Task.updated_at)).limit(limit).all()\n\n        for task in recent_tasks:\n            activities.append({\n                'type': 'task',\n                'id': task.id,\n                'title': task.name,\n                'description': f\"Task {task.status} in project {task.project.name}\",\n                'timestamp': task.updated_at.isoformat(),\n                'link': f\"/projects/{task.project_id}#task-{task.id}\",\n                'status': task.status,\n                'priority': task.priority\n            })\n\n        # Recent timesheet entries (user's own)\n        recent_timesheets = Timesheet.query.filter(\n            and_(\n                Timesheet.user_id == current_user.id,\n                Timesheet.created_at >= week_ago\n            )\n        ).order_by(desc(Timesheet.created_at)).limit(limit).all()\n\n        for timesheet in recent_timesheets:\n            activities.append({\n                'type': 'timesheet',\n                'id': timesheet.id,\n                'title': f\"Logged {timesheet.hours}h\",\n                'description': f\"Time logged for {timesheet.project.name}\",\n                'timestamp': timesheet.created_at.isoformat(),\n                'link': f\"/projects/{timesheet.project_id}\",\n                'hours': timesheet.hours,\n                'date': timesheet.date.isoformat()\n            })\n\n        # Recent events (user's or all if has permission)\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            recent_events = Event.query.filter(\n                Event.created_at >= week_ago\n            ).order_by(desc(Event.created_at)).limit(limit).all()\n        else:\n            recent_events = Event.query.filter(\n                and_(\n                    Event.created_by == current_user.id,\n                    Event.created_at >= week_ago\n                )\n            ).order_by(desc(Event.created_at)).limit(limit).all()\n\n        for event in recent_events:\n            activities.append({\n                'type': 'event',\n                'id': event.id,\n                'title': event.title,\n                'description': f\"Event scheduled for {event.start_time.strftime('%Y-%m-%d %H:%M')}\",\n                'timestamp': event.created_at.isoformat(),\n                'link': f\"/calendar#event-{event.id}\",\n                'start_time': event.start_time.isoformat(),\n                'event_type': event.event_type\n            })\n\n        # Sort all activities by timestamp (most recent first)\n        activities.sort(key=lambda x: x['timestamp'], reverse=True)\n\n        # Limit to requested number\n        activities = activities[:limit]\n\n        return api_response(\n            data={'activities': activities},\n            message=f\"Retrieved {len(activities)} recent activities\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/upcoming-tasks', methods=['GET'])\*************\n@api_login_required\ndef get_upcoming_tasks():\n    \"\"\"\n    Get upcoming tasks for the dashboard.\n\n    Query Parameters:\n        days (int): Number of days ahead to look (default: 7)\n        limit (int): Number of tasks to return (default: 10)\n\n    Returns:\n        JSON with upcoming tasks\n    \"\"\"\n    try:\n        # Get parameters\n        days = int(request.args.get('days', 7))\n        limit = min(int(request.args.get('limit', 10)), 50)\n\n        now = datetime.utcnow()\n        future_date = now + timedelta(days=days)\n\n        # Get upcoming tasks - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            upcoming_tasks = Task.query.filter(\n                and_(\n                    Task.due_date.between(now.date(), future_date.date()),\n                    Task.status != 'done'\n                )\n            ).order_by(Task.due_date, Task.priority.desc()).limit(limit).all()\n        else:\n            upcoming_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.due_date.between(now.date(), future_date.date()),\n                    Task.status != 'done'\n                )\n            ).order_by(Task.due_date, Task.priority.desc()).limit(limit).all()\n\n        tasks_data = []\n        for task in upcoming_tasks:\n            # Calculate days until due\n            days_until_due = (task.due_date - now.date()).days\n\n            tasks_data.append({\n                'id': task.id,\n                'name': task.name,\n                'description': task.description,\n                'project_id': task.project_id,\n                'project_name': task.project.name,\n                'assignee_id': task.assignee_id,\n                'assignee_name': task.assignee.full_name if task.assignee else None,\n                'status': task.status,\n                'priority': task.priority,\n                'due_date': task.due_date.isoformat(),\n                'days_until_due': days_until_due,\n                'estimated_hours': task.estimated_hours,\n                'is_overdue': days_until_due < 0,\n                'link': f\"/projects/{task.project_id}#task-{task.id}\"\n            })\n\n        return api_response(\n            data={'tasks': tasks_data},\n            message=f\"Retrieved {len(tasks_data)} upcoming tasks\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/kpis', methods=['GET'])\*************\n@api_login_required\ndef get_dashboard_kpis():\n    \"\"\"\n    Get KPIs for the dashboard.\n\n    Query Parameters:\n        category (str): Filter by KPI category\n        limit (int): Number of KPIs to return (default: 6)\n\n    Returns:\n        JSON with KPI data\n    \"\"\"\n    try:\n        # Get parameters\n        category = request.args.get('category')\n        limit = min(int(request.args.get('limit', 6)), 20)\n\n        # Build query\n        query = KPI.query\n\n        if category:\n            query = query.filter_by(category=category)\n\n        # Get KPIs - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):\n            kpis = query.order_by(KPI.category, KPI.name).limit(limit).all()\n        else:\n            # For non-admin users, show all available KPIs\n            kpis = query.order_by(KPI.category, KPI.name).limit(limit).all()\n\n        kpis_data = []\n        for kpi in kpis:\n            kpis_data.append({\n                'id': kpi.id,\n                'name': kpi.name,\n                'category': kpi.category,\n                'description': kpi.description,\n                'current_value': kpi.current_value,\n                'target_value': kpi.target_value,\n                'unit': kpi.unit,\n                'frequency': kpi.frequency,\n                'progress': kpi.progress,\n                'last_updated': kpi.updated_at.isoformat() if kpi.updated_at else None,\n                'performance_percentage': (\n                    (kpi.current_value / kpi.target_value * 100)\n                    if kpi.target_value and kpi.target_value > 0\n                    else 0\n                )\n            })\n\n        return api_response(\n            data={'kpis': kpis_data},\n            message=f\"Retrieved {len(kpis_data)} KPIs\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/charts/project-status', methods=['GET'])\*************\n@api_login_required\ndef get_project_status_chart():\n    \"\"\"\n    Get project status distribution for charts.\n\n    Returns:\n        JSON with project status data for charts\n    \"\"\"\n    try:\n        # Get project status distribution - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            project_status = db.session.query(\n                Project.status, func.count(Project.id)\n            ).group_by(Project.status).all()\n        else:\n            project_status = db.session.query(\n                Project.status, func.count(Project.id)\n            ).filter(\n                Project.team_members.any(id=current_user.id)\n            ).group_by(Project.status).all()\n\n        chart_data = {\n            'labels': [status for status, _ in project_status],\n            'data': [count for _, count in project_status],\n            'total': sum(count for _, count in project_status)\n        }\n\n        return api_response(\n            data={'chart': chart_data},\n            message=\"Project status chart data retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/charts/task-status', methods=['GET'])\*************\n@api_login_required\ndef get_task_status_chart():\n    \"\"\"\n    Get task status distribution for charts.\n\n    Returns:\n        JSON with task status data for charts\n    \"\"\"\n    try:\n        # Get task status distribution - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            task_status = db.session.query(\n                Task.status, func.count(Task.id)\n            ).group_by(Task.status).all()\n        else:\n            task_status = db.session.query(\n                Task.status, func.count(Task.id)\n            ).filter(Task.assignee_id == current_user.id).group_by(Task.status).all()\n\n        chart_data = {\n            'labels': [status for status, _ in task_status],\n            'data': [count for _, count in task_status],\n            'total': sum(count for _, count in task_status)\n        }\n\n        return api_response(\n            data={'chart': chart_data},\n            message=\"Task status chart data retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/quick-actions', methods=['GET'])\n@api_login_required\ndef get_quick_actions():\n    \"\"\"\n    Get available quick actions for the current user.\n\n    Returns:\n        JSON with available quick actions based on user permissions\n    \"\"\"\n    try:\n        actions = []\n\n        # Basic actions available to all users\n        actions.extend([\n            {\n                'id': 'create_task',\n                'title': 'Create Task',\n                'description': 'Create a new task',\n                'icon': 'plus-circle',\n                'url': '/tasks/create',\n                'category': 'tasks'\n            },\n            {\n                'id': 'log_time',\n                'title': 'Log Time',\n                'description': 'Log time for a project',\n                'icon': 'clock',\n                'url': '/timesheets/create',\n                'category': 'time'\n            },\n            {\n                'id': 'view_calendar',\n                'title': 'View Calendar',\n                'description': 'View upcoming events',\n                'icon': 'calendar',\n                'url': '/calendar',\n                'category': 'schedule'\n            }\n        ])\n\n        # Actions for users with project permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            actions.extend([\n                {\n                    'id': 'create_project',\n                    'title': 'Create Project',\n                    'description': 'Start a new project',\n                    'icon': 'folder-plus',\n                    'url': '/projects/create',\n                    'category': 'projects'\n                },\n                {\n                    'id': 'view_reports',\n                    'title': 'View Reports',\n                    'description': 'Access project reports',\n                    'icon': 'chart-bar',\n                    'url': '/reports',\n                    'category': 'reports'\n                }\n            ])\n\n        # Actions for users with CRM permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_CRM):\n            actions.extend([\n                {\n                    'id': 'create_client',\n                    'title': 'Add Client',\n                    'description': 'Add a new client',\n                    'icon': 'user-plus',\n                    'url': '/crm/clients/create',\n                    'category': 'crm'\n                },\n                {\n                    'id': 'view_opportunities',\n                    'title': 'View Opportunities',\n                    'description': 'Check funding opportunities',\n                    'icon': 'trending-up',\n                    'url': '/funding',\n                    'category': 'funding'\n                }\n            ])\n\n        return api_response(\n            data={'actions': actions},\n            message=f\"Retrieved {len(actions)} quick actions\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/news', methods=['GET'])\n@api_login_required\ndef get_dashboard_news():\n    \"\"\"\n    Get latest news for the dashboard.\n\n    Query Parameters:\n        limit (int): Number of news items to return (default: 5)\n\n    Returns:\n        JSON with latest published news\n    \"\"\"\n    try:\n        limit = min(int(request.args.get('limit', 5)), 20)\n\n        # Get latest published news\n        latest_news = News.query.filter_by(\n            is_published=True\n        ).order_by(desc(News.created_at)).limit(limit).all()\n\n        news_data = []\n        for news in latest_news:\n            news_data.append({\n                'id': news.id,\n                'title': news.title,\n                'content': news.content[:200] + '...' if len(news.content) > 200 else news.content,\n                'author_id': news.author_id,\n                'author_name': news.author.full_name if news.author else 'System',\n                'created_at': news.created_at.isoformat(),\n                'is_published': news.is_published,\n                'link': f\"/news/{news.id}\"\n            })\n\n        return api_response(\n            data={'news': news_data},\n            message=f\"Retrieved {len(news_data)} news items\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)", "modifiedCode": "\"\"\"\nAPI endpoints for dashboard data.\nProvides REST API for dashboard statistics, recent activities, and quick actions.\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user\nfrom sqlalchemy import func, desc, and_, or_\nfrom datetime import datetime, timedelta\n\nfrom extensions import db\nfrom models import (\n    Project, Task, TimesheetEntry, News, Event, KPI, Client, Document,\n    FundingOpportunity, User, Notification, Department, UserSkill, Skill\n)\nfrom utils.api_utils import (\n    api_response, api_login_required, handle_api_error\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_DASHBOARD, PERMISSION_VIEW_ALL_PROJECTS,\n    PERMISSION_VIEW_REPORTS, PERMISSION_VIEW_CRM, user_has_permission\n)\nfrom extensions import csrf\n\n# Create blueprint\napi_dashboard = Blueprint('api_dashboard', __name__, url_prefix='/dashboard')\n\n@api_dashboard.route('/stats', methods=['GET'])\*************\n@api_login_required\ndef get_dashboard_stats():\n    \"\"\"\n    Get dashboard statistics and KPIs.\n\n    Returns:\n        JSON with dashboard statistics including:\n        - Active projects count\n        - Tasks statistics\n        - Recent activities count\n        - KPIs data\n    \"\"\"\n    try:\n        now = datetime.utcnow()\n        week_ahead = now + timedelta(days=7)\n\n        # Active projects count - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            active_projects_count = Project.query.filter_by(status='active').count()\n            total_projects_count = Project.query.count()\n        else:\n            # Only projects where user is a team member\n            active_projects_count = Project.query.filter_by(\n                status='active'\n            ).filter(\n                Project.team_members.any(id=current_user.id)\n            ).count()\n            total_projects_count = Project.query.filter(\n                Project.team_members.any(id=current_user.id)\n            ).count()\n\n        # Tasks statistics - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            total_tasks = Task.query.count()\n            pending_tasks = Task.query.filter(Task.status.in_(['todo', 'in-progress'])).count()\n            completed_tasks = Task.query.filter_by(status='done').count()\n            overdue_tasks = Task.query.filter(\n                and_(Task.due_date < now.date(), Task.status != 'done')\n            ).count()\n        else:\n            total_tasks = Task.query.filter_by(assignee_id=current_user.id).count()\n            pending_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.status.in_(['todo', 'in-progress'])\n                )\n            ).count()\n            completed_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.status == 'done'\n                )\n            ).count()\n            overdue_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.due_date < now.date(),\n                    Task.status != 'done'\n                )\n            ).count()\n\n        # Client count - visible only to those with CRM permission\n        if user_has_permission(current_user.role, PERMISSION_VIEW_CRM):\n            client_count = Client.query.count()\n        else:\n            client_count = 0\n\n        # Team statistics\n        total_users = User.query.filter_by(is_active=True).count()\n        total_departments = Department.query.filter_by(is_active=True).count()\n\n        # Recent activities count\n        recent_timesheets_count = Timesheet.query.filter(\n            and_(\n                Timesheet.user_id == current_user.id,\n                Timesheet.date >= (now - timedelta(days=7)).date()\n            )\n        ).count()\n\n        # Unread notifications count\n        unread_notifications = Notification.query.filter_by(\n            user_id=current_user.id,\n            is_read=False\n        ).count()\n\n        stats_data = {\n            'projects': {\n                'active': active_projects_count,\n                'total': total_projects_count\n            },\n            'tasks': {\n                'total': total_tasks,\n                'pending': pending_tasks,\n                'completed': completed_tasks,\n                'overdue': overdue_tasks\n            },\n            'team': {\n                'users': total_users,\n                'departments': total_departments,\n                'clients': client_count\n            },\n            'activities': {\n                'recent_timesheets': recent_timesheets_count,\n                'unread_notifications': unread_notifications\n            }\n        }\n\n        return api_response(\n            data=stats_data,\n            message=\"Dashboard statistics retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/recent-activities', methods=['GET'])\*************\n@api_login_required\ndef get_recent_activities():\n    \"\"\"\n    Get recent activities for the dashboard.\n\n    Query Parameters:\n        limit (int): Number of activities to return (default: 10, max: 50)\n\n    Returns:\n        JSON with recent activities including:\n        - Recent tasks\n        - Recent timesheet entries\n        - Recent projects\n        - Recent events\n    \"\"\"\n    try:\n        # Get limit parameter\n        limit = min(int(request.args.get('limit', 10)), 50)\n\n        now = datetime.utcnow()\n        week_ago = now - timedelta(days=7)\n\n        activities = []\n\n        # Recent tasks (assigned to user or all if has permission)\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            recent_tasks = Task.query.filter(\n                Task.updated_at >= week_ago\n            ).order_by(desc(Task.updated_at)).limit(limit).all()\n        else:\n            recent_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.updated_at >= week_ago\n                )\n            ).order_by(desc(Task.updated_at)).limit(limit).all()\n\n        for task in recent_tasks:\n            activities.append({\n                'type': 'task',\n                'id': task.id,\n                'title': task.name,\n                'description': f\"Task {task.status} in project {task.project.name}\",\n                'timestamp': task.updated_at.isoformat(),\n                'link': f\"/projects/{task.project_id}#task-{task.id}\",\n                'status': task.status,\n                'priority': task.priority\n            })\n\n        # Recent timesheet entries (user's own)\n        recent_timesheets = Timesheet.query.filter(\n            and_(\n                Timesheet.user_id == current_user.id,\n                Timesheet.created_at >= week_ago\n            )\n        ).order_by(desc(Timesheet.created_at)).limit(limit).all()\n\n        for timesheet in recent_timesheets:\n            activities.append({\n                'type': 'timesheet',\n                'id': timesheet.id,\n                'title': f\"Logged {timesheet.hours}h\",\n                'description': f\"Time logged for {timesheet.project.name}\",\n                'timestamp': timesheet.created_at.isoformat(),\n                'link': f\"/projects/{timesheet.project_id}\",\n                'hours': timesheet.hours,\n                'date': timesheet.date.isoformat()\n            })\n\n        # Recent events (user's or all if has permission)\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            recent_events = Event.query.filter(\n                Event.created_at >= week_ago\n            ).order_by(desc(Event.created_at)).limit(limit).all()\n        else:\n            recent_events = Event.query.filter(\n                and_(\n                    Event.created_by == current_user.id,\n                    Event.created_at >= week_ago\n                )\n            ).order_by(desc(Event.created_at)).limit(limit).all()\n\n        for event in recent_events:\n            activities.append({\n                'type': 'event',\n                'id': event.id,\n                'title': event.title,\n                'description': f\"Event scheduled for {event.start_time.strftime('%Y-%m-%d %H:%M')}\",\n                'timestamp': event.created_at.isoformat(),\n                'link': f\"/calendar#event-{event.id}\",\n                'start_time': event.start_time.isoformat(),\n                'event_type': event.event_type\n            })\n\n        # Sort all activities by timestamp (most recent first)\n        activities.sort(key=lambda x: x['timestamp'], reverse=True)\n\n        # Limit to requested number\n        activities = activities[:limit]\n\n        return api_response(\n            data={'activities': activities},\n            message=f\"Retrieved {len(activities)} recent activities\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/upcoming-tasks', methods=['GET'])\*************\n@api_login_required\ndef get_upcoming_tasks():\n    \"\"\"\n    Get upcoming tasks for the dashboard.\n\n    Query Parameters:\n        days (int): Number of days ahead to look (default: 7)\n        limit (int): Number of tasks to return (default: 10)\n\n    Returns:\n        JSON with upcoming tasks\n    \"\"\"\n    try:\n        # Get parameters\n        days = int(request.args.get('days', 7))\n        limit = min(int(request.args.get('limit', 10)), 50)\n\n        now = datetime.utcnow()\n        future_date = now + timedelta(days=days)\n\n        # Get upcoming tasks - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            upcoming_tasks = Task.query.filter(\n                and_(\n                    Task.due_date.between(now.date(), future_date.date()),\n                    Task.status != 'done'\n                )\n            ).order_by(Task.due_date, Task.priority.desc()).limit(limit).all()\n        else:\n            upcoming_tasks = Task.query.filter(\n                and_(\n                    Task.assignee_id == current_user.id,\n                    Task.due_date.between(now.date(), future_date.date()),\n                    Task.status != 'done'\n                )\n            ).order_by(Task.due_date, Task.priority.desc()).limit(limit).all()\n\n        tasks_data = []\n        for task in upcoming_tasks:\n            # Calculate days until due\n            days_until_due = (task.due_date - now.date()).days\n\n            tasks_data.append({\n                'id': task.id,\n                'name': task.name,\n                'description': task.description,\n                'project_id': task.project_id,\n                'project_name': task.project.name,\n                'assignee_id': task.assignee_id,\n                'assignee_name': task.assignee.full_name if task.assignee else None,\n                'status': task.status,\n                'priority': task.priority,\n                'due_date': task.due_date.isoformat(),\n                'days_until_due': days_until_due,\n                'estimated_hours': task.estimated_hours,\n                'is_overdue': days_until_due < 0,\n                'link': f\"/projects/{task.project_id}#task-{task.id}\"\n            })\n\n        return api_response(\n            data={'tasks': tasks_data},\n            message=f\"Retrieved {len(tasks_data)} upcoming tasks\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/kpis', methods=['GET'])\*************\n@api_login_required\ndef get_dashboard_kpis():\n    \"\"\"\n    Get KPIs for the dashboard.\n\n    Query Parameters:\n        category (str): Filter by KPI category\n        limit (int): Number of KPIs to return (default: 6)\n\n    Returns:\n        JSON with KPI data\n    \"\"\"\n    try:\n        # Get parameters\n        category = request.args.get('category')\n        limit = min(int(request.args.get('limit', 6)), 20)\n\n        # Build query\n        query = KPI.query\n\n        if category:\n            query = query.filter_by(category=category)\n\n        # Get KPIs - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_REPORTS):\n            kpis = query.order_by(KPI.category, KPI.name).limit(limit).all()\n        else:\n            # For non-admin users, show all available KPIs\n            kpis = query.order_by(KPI.category, KPI.name).limit(limit).all()\n\n        kpis_data = []\n        for kpi in kpis:\n            kpis_data.append({\n                'id': kpi.id,\n                'name': kpi.name,\n                'category': kpi.category,\n                'description': kpi.description,\n                'current_value': kpi.current_value,\n                'target_value': kpi.target_value,\n                'unit': kpi.unit,\n                'frequency': kpi.frequency,\n                'progress': kpi.progress,\n                'last_updated': kpi.updated_at.isoformat() if kpi.updated_at else None,\n                'performance_percentage': (\n                    (kpi.current_value / kpi.target_value * 100)\n                    if kpi.target_value and kpi.target_value > 0\n                    else 0\n                )\n            })\n\n        return api_response(\n            data={'kpis': kpis_data},\n            message=f\"Retrieved {len(kpis_data)} KPIs\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/charts/project-status', methods=['GET'])\*************\n@api_login_required\ndef get_project_status_chart():\n    \"\"\"\n    Get project status distribution for charts.\n\n    Returns:\n        JSON with project status data for charts\n    \"\"\"\n    try:\n        # Get project status distribution - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            project_status = db.session.query(\n                Project.status, func.count(Project.id)\n            ).group_by(Project.status).all()\n        else:\n            project_status = db.session.query(\n                Project.status, func.count(Project.id)\n            ).filter(\n                Project.team_members.any(id=current_user.id)\n            ).group_by(Project.status).all()\n\n        chart_data = {\n            'labels': [status for status, _ in project_status],\n            'data': [count for _, count in project_status],\n            'total': sum(count for _, count in project_status)\n        }\n\n        return api_response(\n            data={'chart': chart_data},\n            message=\"Project status chart data retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/charts/task-status', methods=['GET'])\*************\n@api_login_required\ndef get_task_status_chart():\n    \"\"\"\n    Get task status distribution for charts.\n\n    Returns:\n        JSON with task status data for charts\n    \"\"\"\n    try:\n        # Get task status distribution - filtered by permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            task_status = db.session.query(\n                Task.status, func.count(Task.id)\n            ).group_by(Task.status).all()\n        else:\n            task_status = db.session.query(\n                Task.status, func.count(Task.id)\n            ).filter(Task.assignee_id == current_user.id).group_by(Task.status).all()\n\n        chart_data = {\n            'labels': [status for status, _ in task_status],\n            'data': [count for _, count in task_status],\n            'total': sum(count for _, count in task_status)\n        }\n\n        return api_response(\n            data={'chart': chart_data},\n            message=\"Task status chart data retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/quick-actions', methods=['GET'])\n@api_login_required\ndef get_quick_actions():\n    \"\"\"\n    Get available quick actions for the current user.\n\n    Returns:\n        JSON with available quick actions based on user permissions\n    \"\"\"\n    try:\n        actions = []\n\n        # Basic actions available to all users\n        actions.extend([\n            {\n                'id': 'create_task',\n                'title': 'Create Task',\n                'description': 'Create a new task',\n                'icon': 'plus-circle',\n                'url': '/tasks/create',\n                'category': 'tasks'\n            },\n            {\n                'id': 'log_time',\n                'title': 'Log Time',\n                'description': 'Log time for a project',\n                'icon': 'clock',\n                'url': '/timesheets/create',\n                'category': 'time'\n            },\n            {\n                'id': 'view_calendar',\n                'title': 'View Calendar',\n                'description': 'View upcoming events',\n                'icon': 'calendar',\n                'url': '/calendar',\n                'category': 'schedule'\n            }\n        ])\n\n        # Actions for users with project permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):\n            actions.extend([\n                {\n                    'id': 'create_project',\n                    'title': 'Create Project',\n                    'description': 'Start a new project',\n                    'icon': 'folder-plus',\n                    'url': '/projects/create',\n                    'category': 'projects'\n                },\n                {\n                    'id': 'view_reports',\n                    'title': 'View Reports',\n                    'description': 'Access project reports',\n                    'icon': 'chart-bar',\n                    'url': '/reports',\n                    'category': 'reports'\n                }\n            ])\n\n        # Actions for users with CRM permissions\n        if user_has_permission(current_user.role, PERMISSION_VIEW_CRM):\n            actions.extend([\n                {\n                    'id': 'create_client',\n                    'title': 'Add Client',\n                    'description': 'Add a new client',\n                    'icon': 'user-plus',\n                    'url': '/crm/clients/create',\n                    'category': 'crm'\n                },\n                {\n                    'id': 'view_opportunities',\n                    'title': 'View Opportunities',\n                    'description': 'Check funding opportunities',\n                    'icon': 'trending-up',\n                    'url': '/funding',\n                    'category': 'funding'\n                }\n            ])\n\n        return api_response(\n            data={'actions': actions},\n            message=f\"Retrieved {len(actions)} quick actions\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_dashboard.route('/news', methods=['GET'])\n@api_login_required\ndef get_dashboard_news():\n    \"\"\"\n    Get latest news for the dashboard.\n\n    Query Parameters:\n        limit (int): Number of news items to return (default: 5)\n\n    Returns:\n        JSON with latest published news\n    \"\"\"\n    try:\n        limit = min(int(request.args.get('limit', 5)), 20)\n\n        # Get latest published news\n        latest_news = News.query.filter_by(\n            is_published=True\n        ).order_by(desc(News.created_at)).limit(limit).all()\n\n        news_data = []\n        for news in latest_news:\n            news_data.append({\n                'id': news.id,\n                'title': news.title,\n                'content': news.content[:200] + '...' if len(news.content) > 200 else news.content,\n                'author_id': news.author_id,\n                'author_name': news.author.full_name if news.author else 'System',\n                'created_at': news.created_at.isoformat(),\n                'is_published': news.is_published,\n                'link': f\"/news/{news.id}\"\n            })\n\n        return api_response(\n            data={'news': news_data},\n            message=f\"Retrieved {len(news_data)} news items\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)"}