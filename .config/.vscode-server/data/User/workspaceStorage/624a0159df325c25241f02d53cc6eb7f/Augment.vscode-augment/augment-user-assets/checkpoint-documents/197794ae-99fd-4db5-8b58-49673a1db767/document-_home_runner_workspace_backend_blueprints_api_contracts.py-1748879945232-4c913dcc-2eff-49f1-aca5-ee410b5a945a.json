{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/contracts.py"}, "modifiedCode": "\"\"\"\nAPI Blueprint per la gestione dei contratti clienti.\nTask 3.1 + 4 - CRM/Billing Integration\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, or_, extract\nfrom datetime import datetime, date\n\nfrom models import Contract, Client, Project, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_contracts = Blueprint('api_contracts', __name__)\n\n\n@api_contracts.route('/', methods=['GET'])\n@login_required\ndef get_contracts():\n    \"\"\"Recupera lista contratti con filtri\"\"\"\n    try:\n        # Parametri filtro\n        client_id = request.args.get('client_id', type=int)\n        contract_type = request.args.get('type')  # fixed, hourly\n        status = request.args.get('status')  # active, completed, cancelled\n        start_date = request.args.get('start_date')  # YYYY-MM-DD\n        end_date = request.args.get('end_date')  # YYYY-MM-DD\n        search = request.args.get('search')  # Ricerca in titolo/numero\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = Contract.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_contracts'):\n            return api_response(False, 'Non hai i permessi per visualizzare i contratti', status_code=403)\n        \n        # Applica filtri\n        if client_id:\n            query = query.filter(Contract.client_id == client_id)\n            \n        if contract_type:\n            query = query.filter(Contract.contract_type == contract_type)\n            \n        if status:\n            query = query.filter(Contract.status == status)\n            \n        if start_date:\n            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()\n            query = query.filter(Contract.start_date >= start_date_obj)\n            \n        if end_date:\n            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()\n            query = query.filter(Contract.end_date <= end_date_obj)\n            \n        if search:\n            search_pattern = f\"%{search}%\"\n            query = query.filter(\n                or_(\n                    Contract.contract_number.ilike(search_pattern),\n                    Contract.title.ilike(search_pattern)\n                )\n            )\n        \n        # Ordina per data di creazione (più recenti prima)\n        query = query.order_by(Contract.created_at.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        contracts_data = []\n        for contract in paginated.items:\n            contracts_data.append({\n                'id': contract.id,\n                'client_id': contract.client_id,\n                'client': {\n                    'id': contract.client.id,\n                    'name': contract.client.name,\n                    'company': contract.client.company\n                } if contract.client else None,\n                'contract_number': contract.contract_number,\n                'title': contract.title,\n                'contract_type': contract.contract_type,\n                'status': contract.status,\n                'hourly_rate': contract.hourly_rate,\n                'budget_hours': contract.budget_hours,\n                'total_budget': contract.total_budget,\n                'start_date': contract.start_date.isoformat() if contract.start_date else None,\n                'end_date': contract.end_date.isoformat() if contract.end_date else None,\n                'projects_count': len(contract.projects) if contract.projects else 0,\n                'created_at': contract.created_at.isoformat(),\n                'updated_at': contract.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'contracts': contracts_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperati {len(contracts_data)} contratti\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_contracts.route('/', methods=['POST'])\*************\n@login_required\ndef create_contract():\n    \"\"\"Crea un nuovo contratto\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'manage_contracts'):\n            return api_response(False, 'Non hai i permessi per creare contratti', status_code=403)\n        \n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['client_id', 'title', 'contract_type']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        # Validazione tipo contratto\n        valid_types = ['fixed', 'hourly']\n        if data['contract_type'] not in valid_types:\n            return api_response(\n                False,\n                f'Tipo contratto non valido. Valori ammessi: {\", \".join(valid_types)}',\n                status_code=400\n            )\n        \n        # Verifica che il cliente esista\n        client = Client.query.get(data['client_id'])\n        if not client:\n            return api_response(\n                False,\n                'Cliente non trovato',\n                status_code=404\n            )\n        \n        # Parsing date\n        start_date = None\n        end_date = None\n        if 'start_date' in data and data['start_date']:\n            try:\n                start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()\n            except ValueError:\n                return api_response(\n                    False,\n                    'Formato start_date non valido. Utilizzare YYYY-MM-DD',\n                    status_code=400\n                )\n        \n        if 'end_date' in data and data['end_date']:\n            try:\n                end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()\n            except ValueError:\n                return api_response(\n                    False,\n                    'Formato end_date non valido. Utilizzare YYYY-MM-DD',\n                    status_code=400\n                )\n        \n        # Validazione logica date\n        if start_date and end_date and start_date > end_date:\n            return api_response(\n                False,\n                'La data di inizio non può essere successiva alla data di fine',\n                status_code=400\n            )\n        \n        # Genera numero contratto se non fornito\n        contract_number = data.get('contract_number')\n        if not contract_number:\n            # Genera numero automatico: YYYY-NNNN\n            year = datetime.now().year\n            last_contract = Contract.query.filter(\n                Contract.contract_number.like(f'{year}-%')\n            ).order_by(Contract.contract_number.desc()).first()\n            \n            if last_contract:\n                try:\n                    last_num = int(last_contract.contract_number.split('-')[1])\n                    next_num = last_num + 1\n                except:\n                    next_num = 1\n            else:\n                next_num = 1\n            \n            contract_number = f\"{year}-{next_num:04d}\"\n        \n        # Verifica unicità numero contratto\n        existing = Contract.query.filter(Contract.contract_number == contract_number).first()\n        if existing:\n            return api_response(\n                False,\n                f'Numero contratto {contract_number} già esistente',\n                status_code=400\n            )\n        \n        # Crea nuovo contratto\n        contract = Contract(\n            client_id=data['client_id'],\n            contract_number=contract_number,\n            title=data['title'],\n            description=data.get('description', ''),\n            contract_type=data['contract_type'],\n            status=data.get('status', 'active'),\n            hourly_rate=data.get('hourly_rate'),\n            budget_hours=data.get('budget_hours'),\n            total_budget=data.get('total_budget'),\n            start_date=start_date,\n            end_date=end_date\n        )\n        \n        db.session.add(contract)\n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': contract.id,\n                'contract_number': contract.contract_number,\n                'title': contract.title,\n                'contract_type': contract.contract_type,\n                'status': contract.status,\n                'client': {\n                    'id': contract.client.id,\n                    'name': contract.client.name,\n                    'company': contract.client.company\n                }\n            },\n            message='Contratto creato con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_contracts.route('/<int:contract_id>', methods=['GET'])\n@login_required\ndef get_contract(contract_id):\n    \"\"\"Recupera dettaglio singolo contratto\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_contracts'):\n            return api_response(False, 'Non hai i permessi per visualizzare i contratti', status_code=403)\n        \n        contract = Contract.query.get_or_404(contract_id)\n        \n        # Calcola statistiche progetti collegati\n        projects_stats = {\n            'total': len(contract.projects),\n            'active': len([p for p in contract.projects if p.status == 'active']),\n            'completed': len([p for p in contract.projects if p.status == 'completed'])\n        }\n        \n        return api_response(\n            data={\n                'id': contract.id,\n                'client_id': contract.client_id,\n                'client': {\n                    'id': contract.client.id,\n                    'name': contract.client.name,\n                    'company': contract.client.company,\n                    'email': contract.client.email,\n                    'phone': contract.client.phone\n                } if contract.client else None,\n                'contract_number': contract.contract_number,\n                'title': contract.title,\n                'description': contract.description,\n                'contract_type': contract.contract_type,\n                'status': contract.status,\n                'hourly_rate': contract.hourly_rate,\n                'budget_hours': contract.budget_hours,\n                'total_budget': contract.total_budget,\n                'start_date': contract.start_date.isoformat() if contract.start_date else None,\n                'end_date': contract.end_date.isoformat() if contract.end_date else None,\n                'projects': [{\n                    'id': p.id,\n                    'name': p.name,\n                    'status': p.status,\n                    'start_date': p.start_date.isoformat() if p.start_date else None,\n                    'end_date': p.end_date.isoformat() if p.end_date else None\n                } for p in contract.projects] if contract.projects else [],\n                'projects_stats': projects_stats,\n                'created_at': contract.created_at.isoformat(),\n                'updated_at': contract.updated_at.isoformat()\n            },\n            message=\"Dettaglio contratto recuperato con successo\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n"}