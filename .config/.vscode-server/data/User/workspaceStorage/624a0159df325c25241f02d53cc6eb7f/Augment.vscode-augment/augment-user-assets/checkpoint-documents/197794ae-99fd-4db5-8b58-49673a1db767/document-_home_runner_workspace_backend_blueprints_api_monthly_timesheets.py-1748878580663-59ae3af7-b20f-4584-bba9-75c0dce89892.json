{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/monthly_timesheets.py"}, "originalCode": "\"\"\"\nAPI Blueprint per la gestione delle approvazioni mensili dei timesheet.\nTask 3.1 - Timesheet Management System\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, extract, func\nfrom datetime import datetime, date\nfrom calendar import monthrange\n\nfrom models import MonthlyTimesheet, TimesheetEntry, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_monthly_timesheets = Blueprint('api_monthly_timesheets', __name__)\n\n\n@api_monthly_timesheets.route('/', methods=['GET'])\n@login_required\ndef get_monthly_timesheets():\n    \"\"\"Recupera lista monthly timesheet con filtri\"\"\"\n    try:\n        # Parametri filtro\n        user_id = request.args.get('user_id', type=int)\n        year = request.args.get('year', type=int)\n        month = request.args.get('month', type=int)\n        status = request.args.get('status')  # draft, submitted, approved, rejected\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = MonthlyTimesheet.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_timesheets'):\n            # L'utente può vedere solo i propri monthly timesheet\n            query = query.filter(MonthlyTimesheet.user_id == current_user.id)\n        \n        # Applica filtri\n        if user_id:\n            # Verifica permessi per vedere timesheet di altri utenti\n            if not user_has_permission(current_user.role, 'view_all_timesheets') and user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)\n            query = query.filter(MonthlyTimesheet.user_id == user_id)\n            \n        if year:\n            query = query.filter(MonthlyTimesheet.year == year)\n            \n        if month:\n            query = query.filter(MonthlyTimesheet.month == month)\n            \n        if status:\n            query = query.filter(MonthlyTimesheet.status == status)\n        \n        # Ordina per anno/mese (più recenti prima)\n        query = query.order_by(MonthlyTimesheet.year.desc(), MonthlyTimesheet.month.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        timesheets_data = []\n        for ts in paginated.items:\n            timesheets_data.append({\n                'id': ts.id,\n                'user_id': ts.user_id,\n                'user': {\n                    'id': ts.user.id,\n                    'first_name': ts.user.first_name,\n                    'last_name': ts.user.last_name,\n                    'full_name': ts.user.full_name\n                } if ts.user else None,\n                'year': ts.year,\n                'month': ts.month,\n                'status': ts.status,\n                'total_hours': ts.total_hours,\n                'billable_hours': ts.billable_hours,\n                'submission_date': ts.submission_date.isoformat() if ts.submission_date else None,\n                'approval_date': ts.approval_date.isoformat() if ts.approval_date else None,\n                'approved_by': ts.approved_by,\n                'approver': {\n                    'id': ts.approver.id,\n                    'first_name': ts.approver.first_name,\n                    'last_name': ts.approver.last_name,\n                    'full_name': ts.approver.full_name\n                } if ts.approver else None,\n                'rejection_reason': ts.rejection_reason,\n                'created_at': ts.created_at.isoformat(),\n                'updated_at': ts.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'timesheets': timesheets_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperati {len(timesheets_data)} monthly timesheet\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_monthly_timesheets.route('/<int:timesheet_id>', methods=['GET'])\n@login_required\ndef get_monthly_timesheet(timesheet_id):\n    \"\"\"Recupera dettaglio singolo monthly timesheet con entries\"\"\"\n    try:\n        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_timesheets'):\n            if monthly_timesheet.user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare questo timesheet', status_code=403)\n        \n        # Recupera entries del mese\n        entries = TimesheetEntry.query.filter(\n            and_(\n                TimesheetEntry.user_id == monthly_timesheet.user_id,\n                extract('year', TimesheetEntry.date) == monthly_timesheet.year,\n                extract('month', TimesheetEntry.date) == monthly_timesheet.month\n            )\n        ).order_by(TimesheetEntry.date.desc()).all()\n        \n        entries_data = []\n        for entry in entries:\n            entries_data.append({\n                'id': entry.id,\n                'project_id': entry.project_id,\n                'project': {\n                    'id': entry.project.id,\n                    'name': entry.project.name\n                } if entry.project else None,\n                'task_id': entry.task_id,\n                'task': {\n                    'id': entry.task.id,\n                    'title': entry.task.title\n                } if entry.task else None,\n                'date': entry.date.isoformat(),\n                'hours': entry.hours,\n                'description': entry.description,\n                'billable': entry.billable,\n                'billing_rate': entry.billing_rate,\n                'billing_status': entry.billing_status,\n                'status': entry.status\n            })\n        \n        return api_response(\n            data={\n                'id': monthly_timesheet.id,\n                'user_id': monthly_timesheet.user_id,\n                'user': {\n                    'id': monthly_timesheet.user.id,\n                    'first_name': monthly_timesheet.user.first_name,\n                    'last_name': monthly_timesheet.user.last_name,\n                    'full_name': monthly_timesheet.user.full_name\n                },\n                'year': monthly_timesheet.year,\n                'month': monthly_timesheet.month,\n                'status': monthly_timesheet.status,\n                'total_hours': monthly_timesheet.total_hours,\n                'billable_hours': monthly_timesheet.billable_hours,\n                'submission_date': monthly_timesheet.submission_date.isoformat() if monthly_timesheet.submission_date else None,\n                'approval_date': monthly_timesheet.approval_date.isoformat() if monthly_timesheet.approval_date else None,\n                'approved_by': monthly_timesheet.approved_by,\n                'approver': {\n                    'id': monthly_timesheet.approver.id,\n                    'first_name': monthly_timesheet.approver.first_name,\n                    'last_name': monthly_timesheet.approver.last_name,\n                    'full_name': monthly_timesheet.approver.full_name\n                } if monthly_timesheet.approver else None,\n                'rejection_reason': monthly_timesheet.rejection_reason,\n                'entries': entries_data,\n                'entries_count': len(entries_data),\n                'created_at': monthly_timesheet.created_at.isoformat(),\n                'updated_at': monthly_timesheet.updated_at.isoformat()\n            },\n            message=\"Dettaglio monthly timesheet recuperato con successo\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_monthly_timesheets.route('/generate', methods=['POST'])\*************\n@login_required\ndef generate_monthly_timesheet():\n    \"\"\"Genera/recupera monthly timesheet per un mese specifico\"\"\"\n    try:\n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['year', 'month']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        year = data['year']\n        month = data['month']\n        user_id = data.get('user_id', current_user.id)\n        \n        # Controllo permessi per generare timesheet di altri utenti\n        if user_id != current_user.id and not user_has_permission(current_user.role, 'manage_timesheets'):\n            return api_response(False, 'Non puoi generare timesheet per altri utenti', status_code=403)\n        \n        # Validazione mese/anno\n        if not (1 <= month <= 12):\n            return api_response(False, 'Mese non valido (1-12)', status_code=400)\n        \n        if year < 2020 or year > datetime.now().year + 1:\n            return api_response(False, 'Anno non valido', status_code=400)\n        \n        # Verifica se esiste già\n        existing = MonthlyTimesheet.query.filter(\n            and_(\n                MonthlyTimesheet.user_id == user_id,\n                MonthlyTimesheet.year == year,\n                MonthlyTimesheet.month == month\n            )\n        ).first()\n        \n        if existing:\n            return api_response(\n                data={\n                    'id': existing.id,\n                    'status': existing.status,\n                    'total_hours': existing.total_hours,\n                    'billable_hours': existing.billable_hours\n                },\n                message='Monthly timesheet già esistente'\n            )\n        \n        # Verifica che ci siano timesheet entries per il mese\n        entries_count = TimesheetEntry.query.filter(\n            and_(\n                TimesheetEntry.user_id == user_id,\n                extract('year', TimesheetEntry.date) == year,\n                extract('month', TimesheetEntry.date) == month\n            )\n        ).count()\n        \n        if entries_count == 0:\n            return api_response(\n                False,\n                f'Nessuna entry timesheet trovata per {month}/{year}',\n                status_code=400\n            )\n        \n        # Crea nuovo monthly timesheet\n        monthly_timesheet = MonthlyTimesheet(\n            user_id=user_id,\n            year=year,\n            month=month,\n            status='draft'\n        )\n        \n        db.session.add(monthly_timesheet)\n        db.session.commit()\n        \n        # Collega le entries esistenti al monthly timesheet\n        TimesheetEntry.query.filter(\n            and_(\n                TimesheetEntry.user_id == user_id,\n                extract('year', TimesheetEntry.date) == year,\n                extract('month', TimesheetEntry.date) == month\n            )\n        ).update({'monthly_timesheet_id': monthly_timesheet.id})\n        \n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': monthly_timesheet.id,\n                'year': monthly_timesheet.year,\n                'month': monthly_timesheet.month,\n                'status': monthly_timesheet.status,\n                'total_hours': monthly_timesheet.total_hours,\n                'billable_hours': monthly_timesheet.billable_hours,\n                'entries_count': entries_count\n            },\n            message='Monthly timesheet generato con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n", "modifiedCode": "\"\"\"\nAPI Blueprint per la gestione delle approvazioni mensili dei timesheet.\nTask 3.1 - Timesheet Management System\n\"\"\"\n\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import and_, extract, func\nfrom datetime import datetime, date\nfrom calendar import monthrange\n\nfrom models import MonthlyTimesheet, TimesheetEntry, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db, csrf\n\napi_monthly_timesheets = Blueprint('api_monthly_timesheets', __name__)\n\n\n@api_monthly_timesheets.route('/', methods=['GET'])\n@login_required\ndef get_monthly_timesheets():\n    \"\"\"Recupera lista monthly timesheet con filtri\"\"\"\n    try:\n        # Parametri filtro\n        user_id = request.args.get('user_id', type=int)\n        year = request.args.get('year', type=int)\n        month = request.args.get('month', type=int)\n        status = request.args.get('status')  # draft, submitted, approved, rejected\n        \n        # Paginazione\n        page = request.args.get('page', type=int, default=1)\n        per_page = request.args.get('per_page', type=int, default=50)\n        \n        # Query base\n        query = MonthlyTimesheet.query\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_timesheets'):\n            # L'utente può vedere solo i propri monthly timesheet\n            query = query.filter(MonthlyTimesheet.user_id == current_user.id)\n        \n        # Applica filtri\n        if user_id:\n            # Verifica permessi per vedere timesheet di altri utenti\n            if not user_has_permission(current_user.role, 'view_all_timesheets') and user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)\n            query = query.filter(MonthlyTimesheet.user_id == user_id)\n            \n        if year:\n            query = query.filter(MonthlyTimesheet.year == year)\n            \n        if month:\n            query = query.filter(MonthlyTimesheet.month == month)\n            \n        if status:\n            query = query.filter(MonthlyTimesheet.status == status)\n        \n        # Ordina per anno/mese (più recenti prima)\n        query = query.order_by(MonthlyTimesheet.year.desc(), MonthlyTimesheet.month.desc())\n        \n        # Applica paginazione\n        paginated = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        \n        # Prepara dati\n        timesheets_data = []\n        for ts in paginated.items:\n            timesheets_data.append({\n                'id': ts.id,\n                'user_id': ts.user_id,\n                'user': {\n                    'id': ts.user.id,\n                    'first_name': ts.user.first_name,\n                    'last_name': ts.user.last_name,\n                    'full_name': ts.user.full_name\n                } if ts.user else None,\n                'year': ts.year,\n                'month': ts.month,\n                'status': ts.status,\n                'total_hours': ts.total_hours,\n                'billable_hours': ts.billable_hours,\n                'submission_date': ts.submission_date.isoformat() if ts.submission_date else None,\n                'approval_date': ts.approval_date.isoformat() if ts.approval_date else None,\n                'approved_by': ts.approved_by,\n                'approver': {\n                    'id': ts.approver.id,\n                    'first_name': ts.approver.first_name,\n                    'last_name': ts.approver.last_name,\n                    'full_name': ts.approver.full_name\n                } if ts.approver else None,\n                'rejection_reason': ts.rejection_reason,\n                'created_at': ts.created_at.isoformat(),\n                'updated_at': ts.updated_at.isoformat()\n            })\n        \n        return api_response(\n            data={\n                'timesheets': timesheets_data,\n                'pagination': {\n                    'page': paginated.page,\n                    'pages': paginated.pages,\n                    'per_page': paginated.per_page,\n                    'total': paginated.total,\n                    'has_next': paginated.has_next,\n                    'has_prev': paginated.has_prev\n                }\n            },\n            message=f\"Recuperati {len(timesheets_data)} monthly timesheet\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_monthly_timesheets.route('/<int:timesheet_id>', methods=['GET'])\n@login_required\ndef get_monthly_timesheet(timesheet_id):\n    \"\"\"Recupera dettaglio singolo monthly timesheet con entries\"\"\"\n    try:\n        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)\n        \n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'view_all_timesheets'):\n            if monthly_timesheet.user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare questo timesheet', status_code=403)\n        \n        # Recupera entries del mese\n        entries = TimesheetEntry.query.filter(\n            and_(\n                TimesheetEntry.user_id == monthly_timesheet.user_id,\n                extract('year', TimesheetEntry.date) == monthly_timesheet.year,\n                extract('month', TimesheetEntry.date) == monthly_timesheet.month\n            )\n        ).order_by(TimesheetEntry.date.desc()).all()\n        \n        entries_data = []\n        for entry in entries:\n            entries_data.append({\n                'id': entry.id,\n                'project_id': entry.project_id,\n                'project': {\n                    'id': entry.project.id,\n                    'name': entry.project.name\n                } if entry.project else None,\n                'task_id': entry.task_id,\n                'task': {\n                    'id': entry.task.id,\n                    'title': entry.task.title\n                } if entry.task else None,\n                'date': entry.date.isoformat(),\n                'hours': entry.hours,\n                'description': entry.description,\n                'billable': entry.billable,\n                'billing_rate': entry.billing_rate,\n                'billing_status': entry.billing_status,\n                'status': entry.status\n            })\n        \n        return api_response(\n            data={\n                'id': monthly_timesheet.id,\n                'user_id': monthly_timesheet.user_id,\n                'user': {\n                    'id': monthly_timesheet.user.id,\n                    'first_name': monthly_timesheet.user.first_name,\n                    'last_name': monthly_timesheet.user.last_name,\n                    'full_name': monthly_timesheet.user.full_name\n                },\n                'year': monthly_timesheet.year,\n                'month': monthly_timesheet.month,\n                'status': monthly_timesheet.status,\n                'total_hours': monthly_timesheet.total_hours,\n                'billable_hours': monthly_timesheet.billable_hours,\n                'submission_date': monthly_timesheet.submission_date.isoformat() if monthly_timesheet.submission_date else None,\n                'approval_date': monthly_timesheet.approval_date.isoformat() if monthly_timesheet.approval_date else None,\n                'approved_by': monthly_timesheet.approved_by,\n                'approver': {\n                    'id': monthly_timesheet.approver.id,\n                    'first_name': monthly_timesheet.approver.first_name,\n                    'last_name': monthly_timesheet.approver.last_name,\n                    'full_name': monthly_timesheet.approver.full_name\n                } if monthly_timesheet.approver else None,\n                'rejection_reason': monthly_timesheet.rejection_reason,\n                'entries': entries_data,\n                'entries_count': len(entries_data),\n                'created_at': monthly_timesheet.created_at.isoformat(),\n                'updated_at': monthly_timesheet.updated_at.isoformat()\n            },\n            message=\"Dettaglio monthly timesheet recuperato con successo\"\n        )\n        \n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_monthly_timesheets.route('/generate', methods=['POST'])\*************\n@login_required\ndef generate_monthly_timesheet():\n    \"\"\"Genera/recupera monthly timesheet per un mese specifico\"\"\"\n    try:\n        data = request.get_json()\n        \n        # Validazione campi richiesti\n        required_fields = ['year', 'month']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n        \n        year = data['year']\n        month = data['month']\n        user_id = data.get('user_id', current_user.id)\n        \n        # Controllo permessi per generare timesheet di altri utenti\n        if user_id != current_user.id and not user_has_permission(current_user.role, 'manage_timesheets'):\n            return api_response(False, 'Non puoi generare timesheet per altri utenti', status_code=403)\n        \n        # Validazione mese/anno\n        if not (1 <= month <= 12):\n            return api_response(False, 'Mese non valido (1-12)', status_code=400)\n        \n        if year < 2020 or year > datetime.now().year + 1:\n            return api_response(False, 'Anno non valido', status_code=400)\n        \n        # Verifica se esiste già\n        existing = MonthlyTimesheet.query.filter(\n            and_(\n                MonthlyTimesheet.user_id == user_id,\n                MonthlyTimesheet.year == year,\n                MonthlyTimesheet.month == month\n            )\n        ).first()\n        \n        if existing:\n            return api_response(\n                data={\n                    'id': existing.id,\n                    'status': existing.status,\n                    'total_hours': existing.total_hours,\n                    'billable_hours': existing.billable_hours\n                },\n                message='Monthly timesheet già esistente'\n            )\n        \n        # Verifica che ci siano timesheet entries per il mese\n        entries_count = TimesheetEntry.query.filter(\n            and_(\n                TimesheetEntry.user_id == user_id,\n                extract('year', TimesheetEntry.date) == year,\n                extract('month', TimesheetEntry.date) == month\n            )\n        ).count()\n        \n        if entries_count == 0:\n            return api_response(\n                False,\n                f'Nessuna entry timesheet trovata per {month}/{year}',\n                status_code=400\n            )\n        \n        # Crea nuovo monthly timesheet\n        monthly_timesheet = MonthlyTimesheet(\n            user_id=user_id,\n            year=year,\n            month=month,\n            status='draft'\n        )\n        \n        db.session.add(monthly_timesheet)\n        db.session.commit()\n        \n        # Collega le entries esistenti al monthly timesheet\n        TimesheetEntry.query.filter(\n            and_(\n                TimesheetEntry.user_id == user_id,\n                extract('year', TimesheetEntry.date) == year,\n                extract('month', TimesheetEntry.date) == month\n            )\n        ).update({'monthly_timesheet_id': monthly_timesheet.id})\n        \n        db.session.commit()\n        \n        return api_response(\n            data={\n                'id': monthly_timesheet.id,\n                'year': monthly_timesheet.year,\n                'month': monthly_timesheet.month,\n                'status': monthly_timesheet.status,\n                'total_hours': monthly_timesheet.total_hours,\n                'billable_hours': monthly_timesheet.billable_hours,\n                'entries_count': entries_count\n            },\n            message='Monthly timesheet generato con successo'\n        )\n        \n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_monthly_timesheets.route('/<int:timesheet_id>/submit', methods=['PUT'])\*************\n@login_required\ndef submit_monthly_timesheet(timesheet_id):\n    \"\"\"Sottometti monthly timesheet per approvazione\"\"\"\n    try:\n        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)\n\n        # Solo il proprietario può sottomettere\n        if monthly_timesheet.user_id != current_user.id:\n            return api_response(False, 'Puoi sottomettere solo i tuoi timesheet', status_code=403)\n\n        # Verifica stato\n        if monthly_timesheet.status != 'draft':\n            return api_response(\n                False,\n                f'Il timesheet è già stato {monthly_timesheet.status}',\n                status_code=400\n            )\n\n        # Verifica che ci siano entries\n        if monthly_timesheet.total_hours == 0:\n            return api_response(\n                False,\n                'Non è possibile sottomettere un timesheet senza ore registrate',\n                status_code=400\n            )\n\n        # Sottometti\n        monthly_timesheet.status = 'submitted'\n        monthly_timesheet.submission_date = datetime.utcnow()\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': monthly_timesheet.id,\n                'status': monthly_timesheet.status,\n                'submission_date': monthly_timesheet.submission_date.isoformat(),\n                'total_hours': monthly_timesheet.total_hours\n            },\n            message='Timesheet sottomesso per approvazione'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_monthly_timesheets.route('/<int:timesheet_id>/approve', methods=['PUT'])\*************\n@login_required\ndef approve_monthly_timesheet(timesheet_id):\n    \"\"\"Approva monthly timesheet\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'approve_timesheets'):\n            return api_response(False, 'Non hai i permessi per approvare timesheet', status_code=403)\n\n        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)\n\n        # Verifica stato\n        if monthly_timesheet.status != 'submitted':\n            return api_response(\n                False,\n                f'Il timesheet deve essere in stato \"submitted\" per essere approvato (attuale: {monthly_timesheet.status})',\n                status_code=400\n            )\n\n        # Approva timesheet\n        monthly_timesheet.status = 'approved'\n        monthly_timesheet.approved_by = current_user.id\n        monthly_timesheet.approval_date = datetime.utcnow()\n        monthly_timesheet.rejection_reason = None\n\n        # Approva anche tutte le entries collegate\n        TimesheetEntry.query.filter(\n            TimesheetEntry.monthly_timesheet_id == monthly_timesheet.id\n        ).update({'status': 'approved'})\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': monthly_timesheet.id,\n                'status': monthly_timesheet.status,\n                'approved_by': monthly_timesheet.approved_by,\n                'approval_date': monthly_timesheet.approval_date.isoformat(),\n                'total_hours': monthly_timesheet.total_hours\n            },\n            message=f'Timesheet {monthly_timesheet.month}/{monthly_timesheet.year} approvato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_monthly_timesheets.route('/<int:timesheet_id>/reject', methods=['PUT'])\*************\n@login_required\ndef reject_monthly_timesheet(timesheet_id):\n    \"\"\"Rifiuta monthly timesheet\"\"\"\n    try:\n        # Controllo permessi\n        if not user_has_permission(current_user.role, 'approve_timesheets'):\n            return api_response(False, 'Non hai i permessi per rifiutare timesheet', status_code=403)\n\n        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)\n\n        # Verifica stato\n        if monthly_timesheet.status != 'submitted':\n            return api_response(\n                False,\n                f'Il timesheet deve essere in stato \"submitted\" per essere rifiutato (attuale: {monthly_timesheet.status})',\n                status_code=400\n            )\n\n        data = request.get_json() or {}\n        rejection_reason = data.get('reason', '')\n\n        if not rejection_reason:\n            return api_response(\n                False,\n                'Motivo del rifiuto richiesto',\n                status_code=400\n            )\n\n        # Rifiuta timesheet\n        monthly_timesheet.status = 'rejected'\n        monthly_timesheet.approved_by = current_user.id\n        monthly_timesheet.approval_date = datetime.utcnow()\n        monthly_timesheet.rejection_reason = rejection_reason\n\n        # Rimetti le entries in pending\n        TimesheetEntry.query.filter(\n            TimesheetEntry.monthly_timesheet_id == monthly_timesheet.id\n        ).update({'status': 'pending'})\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': monthly_timesheet.id,\n                'status': monthly_timesheet.status,\n                'approved_by': monthly_timesheet.approved_by,\n                'approval_date': monthly_timesheet.approval_date.isoformat(),\n                'rejection_reason': monthly_timesheet.rejection_reason\n            },\n            message=f'Timesheet {monthly_timesheet.month}/{monthly_timesheet.year} rifiutato'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_monthly_timesheets.route('/<int:timesheet_id>/reopen', methods=['PUT'])\*************\n@login_required\ndef reopen_monthly_timesheet(timesheet_id):\n    \"\"\"Riapre monthly timesheet rifiutato per modifiche\"\"\"\n    try:\n        monthly_timesheet = MonthlyTimesheet.query.get_or_404(timesheet_id)\n\n        # Solo il proprietario può riaprire\n        if monthly_timesheet.user_id != current_user.id:\n            return api_response(False, 'Puoi riaprire solo i tuoi timesheet', status_code=403)\n\n        # Solo timesheet rifiutati possono essere riaperti\n        if monthly_timesheet.status != 'rejected':\n            return api_response(\n                False,\n                'Solo timesheet rifiutati possono essere riaperti',\n                status_code=400\n            )\n\n        # Riapri\n        monthly_timesheet.status = 'draft'\n        monthly_timesheet.submission_date = None\n        monthly_timesheet.approval_date = None\n        monthly_timesheet.approved_by = None\n        monthly_timesheet.rejection_reason = None\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'id': monthly_timesheet.id,\n                'status': monthly_timesheet.status\n            },\n            message='Timesheet riaperto per modifiche'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n"}