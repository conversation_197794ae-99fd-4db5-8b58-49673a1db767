{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/SkillsMatrix.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <svg class=\"w-8 h-8 mr-3 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n          </svg>\n          Matrice Competenze\n        </h1>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Panoramica delle competenze del team con livelli di proficiency\n        </p>\n      </div>\n\n      <!-- Export Button -->\n      <div class=\"mt-4 sm:mt-0\">\n        <button @click=\"exportMatrix\"\n                :disabled=\"loading\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n          </svg>\n          Esporta CSV\n        </button>\n      </div>\n    </div>\n\n    <!-- Filters -->\n    <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Department Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Dipartimento</label>\n          <select v-model=\"filters.department_id\"\n                  @change=\"loadMatrix\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i dipartimenti</option>\n            <option v-for=\"dept in availableFilters.departments\" :key=\"dept.id\" :value=\"dept.id\">\n              {{ dept.name }}\n            </option>\n          </select>\n        </div>\n\n        <!-- Category Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Categoria</label>\n          <select v-model=\"filters.category\"\n                  @change=\"loadMatrix\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutte le categorie</option>\n            <option v-for=\"category in availableFilters.categories\" :key=\"category\" :value=\"category\">\n              {{ category }}\n            </option>\n          </select>\n        </div>\n\n        <!-- Min Level Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Livello Minimo</label>\n          <select v-model=\"filters.min_level\"\n                  @change=\"loadMatrix\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Qualsiasi</option>\n            <option value=\"1\">1 - Principiante</option>\n            <option value=\"2\">2 - Base</option>\n            <option value=\"3\">3 - Intermedio</option>\n            <option value=\"4\">4 - Avanzato</option>\n            <option value=\"5\">5 - Esperto</option>\n          </select>\n        </div>\n\n        <!-- Max Level Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Livello Massimo</label>\n          <select v-model=\"filters.max_level\"\n                  @change=\"loadMatrix\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Qualsiasi</option>\n            <option value=\"1\">1 - Principiante</option>\n            <option value=\"2\">2 - Base</option>\n            <option value=\"3\">3 - Intermedio</option>\n            <option value=\"4\">4 - Avanzato</option>\n            <option value=\"5\">5 - Esperto</option>\n          </select>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistics Cards -->\n    <div v-if=\"stats\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Dipendenti</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_users }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Competenze</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_skills }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Media Skills/Utente</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.avg_skills_per_user }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-orange-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Competenze Avanzate</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.skill_coverage.advanced }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center py-12\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-3 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento</h3>\n          <p class=\"mt-1 text-sm text-red-700 dark:text-red-300\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Skills Matrix Table -->\n    <div v-else-if=\"matrixData.length > 0\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden\">\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Table Header -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th scope=\"col\" class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Dipendente\n              </th>\n              <th v-for=\"skill in skillsSummary\"\n                  :key=\"skill.id\"\n                  scope=\"col\"\n                  class=\"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-24\">\n                <div class=\"flex flex-col items-center\">\n                  <span class=\"mb-1\">{{ skill.name }}</span>\n                  <span class=\"text-xs text-gray-400 dark:text-gray-500 normal-case\">{{ skill.category }}</span>\n                  <div class=\"mt-1 flex items-center space-x-1\">\n                    <span class=\"text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1 rounded\">\n                      {{ skill.total_users }}\n                    </span>\n                    <span class=\"text-xs text-gray-400\">avg: {{ skill.avg_level }}</span>\n                  </div>\n                </div>\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Table Body -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"user in matrixData\" :key=\"user.user_id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n              <!-- User Info (Sticky Column) -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"flex items-center\">\n                  <div class=\"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3\">\n                    <svg class=\"w-5 h-5 text-gray-500 dark:text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ user.full_name }}</div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">{{ user.position || 'Dipendente' }}</div>\n                    <div class=\"text-xs text-gray-400 dark:text-gray-500\">{{ user.department || 'N/A' }}</div>\n                  </div>\n                </div>\n              </td>\n\n              <!-- Skill Levels -->\n              <td v-for=\"skill in skillsSummary\"\n                  :key=\"`${user.user_id}-${skill.id}`\"\n                  class=\"px-3 py-4 text-center\">\n                <SkillCell\n                  :user-skill=\"getUserSkill(user, skill.id)\"\n                  :skill=\"skill\"\n                  @click=\"onSkillCellClick(user, skill)\"\n                />\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"!loading\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12\">\n      <div class=\"text-center\">\n        <svg class=\"mx-auto h-16 w-16 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\n        </svg>\n        <h3 class=\"mt-4 text-lg font-medium text-gray-900 dark:text-white\">Nessuna competenza trovata</h3>\n        <p class=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n          Prova a modificare i filtri o aggiungi competenze ai dipendenti\n        </p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport SkillCell from './components/SkillCell.vue'\nimport api from '@/utils/api'\n\n// Reactive state\nconst loading = ref(false)\nconst error = ref(null)\nconst matrixData = ref([])\nconst skillsSummary = ref([])\nconst stats = ref(null)\nconst availableFilters = ref({\n  departments: [],\n  categories: []\n})\n\n// Filters\nconst filters = ref({\n  department_id: '',\n  category: '',\n  min_level: '',\n  max_level: ''\n})\n\n// Methods\nconst loadMatrix = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const params = new URLSearchParams()\n\n    Object.entries(filters.value).forEach(([key, value]) => {\n      if (value) {\n        params.append(key, value)\n      }\n    })\n\n    const response = await api.get(`/api/personnel/skills-matrix?${params}`)\n\n    if (response.data.success) {\n      matrixData.value = response.data.data.matrix || []\n      skillsSummary.value = response.data.data.skills_summary || []\n      stats.value = response.data.data.stats || {}\n      availableFilters.value = response.data.data.filters || { departments: [], categories: [] }\n    } else {\n      throw new Error(response.data.message || 'Errore nel caricamento della matrice competenze')\n    }\n  } catch (err) {\n    console.error('Error loading skills matrix:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst getUserSkill = (user, skillId) => {\n  return user.skills.find(skill => skill.skill_id === skillId) || {\n    skill_id: skillId,\n    proficiency_level: 0,\n    years_experience: 0,\n    is_certified: false,\n    certification_name: null,\n    self_assessed: false,\n    manager_assessed: false\n  }\n}\n\nconst onSkillCellClick = (user, skill) => {\n  // TODO: Open skill detail modal or navigate to edit\n  console.log('Skill cell clicked:', user.full_name, skill.name)\n}\n\nconst exportMatrix = () => {\n  if (!matrixData.value.length || !skillsSummary.value.length) {\n    return\n  }\n\n  // Prepare CSV data\n  const headers = ['Dipendente', 'Dipartimento', 'Posizione', ...skillsSummary.value.map(s => s.name)]\n  const rows = matrixData.value.map(user => {\n    const skillLevels = skillsSummary.value.map(skill => {\n      const userSkill = getUserSkill(user, skill.id)\n      return userSkill.proficiency_level || 0\n    })\n\n    return [\n      user.full_name,\n      user.department || '',\n      user.position || '',\n      ...skillLevels\n    ]\n  })\n\n  // Create CSV content\n  const csvContent = [headers, ...rows]\n    .map(row => row.map(cell => `\"${cell}\"`).join(','))\n    .join('\\n')\n\n  // Download CSV\n  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })\n  const link = document.createElement('a')\n  const url = URL.createObjectURL(blob)\n  link.setAttribute('href', url)\n  link.setAttribute('download', `skills-matrix-${new Date().toISOString().split('T')[0]}.csv`)\n  link.style.visibility = 'hidden'\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n}\n\n// Lifecycle\nonMounted(() => {\n  loadMatrix()\n})\n</script>\n\n<style scoped>\n/* Sticky column styling */\n.sticky {\n  position: sticky;\n  background: inherit;\n}\n\n/* Table hover effects */\ntbody tr:hover .sticky {\n  background-color: rgb(249 250 251);\n}\n\n.dark tbody tr:hover .sticky {\n  background-color: rgb(55 65 81);\n}\n\n/* Responsive table */\n@media (max-width: 768px) {\n  .min-w-24 {\n    min-width: 80px;\n  }\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n      <div>\n        <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white flex items-center\">\n          <svg class=\"w-8 h-8 mr-3 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n          </svg>\n          Matrice Competenze\n        </h1>\n        <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n          Panoramica delle competenze del team con livelli di proficiency\n        </p>\n      </div>\n\n      <!-- Export Button -->\n      <div class=\"mt-4 sm:mt-0\">\n        <button @click=\"exportMatrix\"\n                :disabled=\"loading\"\n                class=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50\">\n          <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n          </svg>\n          Esporta CSV\n        </button>\n      </div>\n    </div>\n\n    <!-- Filters -->\n    <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <!-- Department Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Dipartimento</label>\n          <select v-model=\"filters.department_id\"\n                  @change=\"loadMatrix\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutti i dipartimenti</option>\n            <option v-for=\"dept in availableFilters.departments\" :key=\"dept.id\" :value=\"dept.id\">\n              {{ dept.name }}\n            </option>\n          </select>\n        </div>\n\n        <!-- Category Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Categoria</label>\n          <select v-model=\"filters.category\"\n                  @change=\"loadMatrix\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Tutte le categorie</option>\n            <option v-for=\"category in availableFilters.categories\" :key=\"category\" :value=\"category\">\n              {{ category }}\n            </option>\n          </select>\n        </div>\n\n        <!-- Min Level Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Livello Minimo</label>\n          <select v-model=\"filters.min_level\"\n                  @change=\"loadMatrix\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Qualsiasi</option>\n            <option value=\"1\">1 - Principiante</option>\n            <option value=\"2\">2 - Base</option>\n            <option value=\"3\">3 - Intermedio</option>\n            <option value=\"4\">4 - Avanzato</option>\n            <option value=\"5\">5 - Esperto</option>\n          </select>\n        </div>\n\n        <!-- Max Level Filter -->\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Livello Massimo</label>\n          <select v-model=\"filters.max_level\"\n                  @change=\"loadMatrix\"\n                  class=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent\">\n            <option value=\"\">Qualsiasi</option>\n            <option value=\"1\">1 - Principiante</option>\n            <option value=\"2\">2 - Base</option>\n            <option value=\"3\">3 - Intermedio</option>\n            <option value=\"4\">4 - Avanzato</option>\n            <option value=\"5\">5 - Esperto</option>\n          </select>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistics Cards -->\n    <div v-if=\"stats\" class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-blue-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Dipendenti</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_users }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-green-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Competenze</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.total_skills }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-purple-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Media Skills/Utente</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.avg_skills_per_user }}</p>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <svg class=\"w-8 h-8 text-orange-600\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fill-rule=\"evenodd\" d=\"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z\" clip-rule=\"evenodd\"></path>\n            </svg>\n          </div>\n          <div class=\"ml-4\">\n            <p class=\"text-sm font-medium text-gray-500 dark:text-gray-400\">Competenze Avanzate</p>\n            <p class=\"text-2xl font-bold text-gray-900 dark:text-white\">{{ stats.skill_coverage.advanced }}</p>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Loading State -->\n    <div v-if=\"loading\" class=\"flex justify-center items-center py-12\">\n      <div class=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n    </div>\n\n    <!-- Error State -->\n    <div v-else-if=\"error\" class=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n      <div class=\"flex\">\n        <svg class=\"w-5 h-5 text-red-400 mr-3 mt-0.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n          <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n        </svg>\n        <div>\n          <h3 class=\"text-sm font-medium text-red-800 dark:text-red-200\">Errore nel caricamento</h3>\n          <p class=\"mt-1 text-sm text-red-700 dark:text-red-300\">{{ error }}</p>\n        </div>\n      </div>\n    </div>\n\n    <!-- Skills Matrix Table -->\n    <div v-else-if=\"matrixData.length > 0\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden\">\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Table Header -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th scope=\"col\" class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Dipendente\n              </th>\n              <th v-for=\"skill in skillsSummary\"\n                  :key=\"skill.id\"\n                  scope=\"col\"\n                  class=\"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-24\">\n                <div class=\"flex flex-col items-center\">\n                  <span class=\"mb-1\">{{ skill.name }}</span>\n                  <span class=\"text-xs text-gray-400 dark:text-gray-500 normal-case\">{{ skill.category }}</span>\n                  <div class=\"mt-1 flex items-center space-x-1\">\n                    <span class=\"text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1 rounded\">\n                      {{ skill.total_users }}\n                    </span>\n                    <span class=\"text-xs text-gray-400\">avg: {{ skill.avg_level }}</span>\n                  </div>\n                </div>\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Table Body -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-for=\"user in matrixData\" :key=\"user.user_id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n              <!-- User Info (Sticky Column) -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"flex items-center\">\n                  <div class=\"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3\">\n                    <svg class=\"w-5 h-5 text-gray-500 dark:text-gray-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fill-rule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clip-rule=\"evenodd\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">{{ user.full_name }}</div>\n                    <div class=\"text-sm text-gray-500 dark:text-gray-400\">{{ user.position || 'Dipendente' }}</div>\n                    <div class=\"text-xs text-gray-400 dark:text-gray-500\">{{ user.department || 'N/A' }}</div>\n                  </div>\n                </div>\n              </td>\n\n              <!-- Skill Levels -->\n              <td v-for=\"skill in skillsSummary\"\n                  :key=\"`${user.user_id}-${skill.id}`\"\n                  class=\"px-3 py-4 text-center\">\n                <SkillCell\n                  :user-skill=\"getUserSkill(user, skill.id)\"\n                  :skill=\"skill\"\n                  @click=\"onSkillCellClick(user, skill)\"\n                />\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Empty State -->\n    <div v-else-if=\"!loading\" class=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12\">\n      <div class=\"text-center\">\n        <svg class=\"mx-auto h-16 w-16 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\n        </svg>\n        <h3 class=\"mt-4 text-lg font-medium text-gray-900 dark:text-white\">Nessuna competenza trovata</h3>\n        <p class=\"mt-2 text-sm text-gray-500 dark:text-gray-400\">\n          Prova a modificare i filtri o aggiungi competenze ai dipendenti\n        </p>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue'\nimport SkillCell from './components/SkillCell.vue'\nimport api from '@/utils/api'\n\n// Reactive state\nconst loading = ref(false)\nconst error = ref(null)\nconst matrixData = ref([])\nconst skillsSummary = ref([])\nconst stats = ref(null)\nconst availableFilters = ref({\n  departments: [],\n  categories: []\n})\n\n// Filters\nconst filters = ref({\n  department_id: '',\n  category: '',\n  min_level: '',\n  max_level: ''\n})\n\n// Methods\nconst loadMatrix = async () => {\n  loading.value = true\n  error.value = null\n\n  try {\n    const params = new URLSearchParams()\n\n    Object.entries(filters.value).forEach(([key, value]) => {\n      if (value) {\n        params.append(key, value)\n      }\n    })\n\n    const response = await api.get(`/api/personnel/skills-matrix?${params}`)\n\n    if (response.data.success) {\n      matrixData.value = response.data.data.matrix || []\n      skillsSummary.value = response.data.data.skills_summary || []\n      stats.value = response.data.data.stats || {}\n      availableFilters.value = response.data.data.filters || { departments: [], categories: [] }\n    } else {\n      throw new Error(response.data.message || 'Errore nel caricamento della matrice competenze')\n    }\n  } catch (err) {\n    console.error('Error loading skills matrix:', err)\n    error.value = err.message\n  } finally {\n    loading.value = false\n  }\n}\n\nconst getUserSkill = (user, skillId) => {\n  return user.skills.find(skill => skill.skill_id === skillId) || {\n    skill_id: skillId,\n    proficiency_level: 0,\n    years_experience: 0,\n    is_certified: false,\n    certification_name: null,\n    self_assessed: false,\n    manager_assessed: false\n  }\n}\n\nconst onSkillCellClick = (user, skill) => {\n  // TODO: Open skill detail modal or navigate to edit\n  console.log('Skill cell clicked:', user.full_name, skill.name)\n}\n\nconst exportMatrix = () => {\n  if (!matrixData.value.length || !skillsSummary.value.length) {\n    return\n  }\n\n  // Prepare CSV data\n  const headers = ['Dipendente', 'Dipartimento', 'Posizione', ...skillsSummary.value.map(s => s.name)]\n  const rows = matrixData.value.map(user => {\n    const skillLevels = skillsSummary.value.map(skill => {\n      const userSkill = getUserSkill(user, skill.id)\n      return userSkill.proficiency_level || 0\n    })\n\n    return [\n      user.full_name,\n      user.department || '',\n      user.position || '',\n      ...skillLevels\n    ]\n  })\n\n  // Create CSV content\n  const csvContent = [headers, ...rows]\n    .map(row => row.map(cell => `\"${cell}\"`).join(','))\n    .join('\\n')\n\n  // Download CSV\n  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })\n  const link = document.createElement('a')\n  const url = URL.createObjectURL(blob)\n  link.setAttribute('href', url)\n  link.setAttribute('download', `skills-matrix-${new Date().toISOString().split('T')[0]}.csv`)\n  link.style.visibility = 'hidden'\n  document.body.appendChild(link)\n  link.click()\n  document.body.removeChild(link)\n}\n\n// Lifecycle\nonMounted(() => {\n  loadMatrix()\n})\n</script>\n\n<style scoped>\n/* Sticky column styling */\n.sticky {\n  position: sticky;\n  background: inherit;\n}\n\n/* Table hover effects */\ntbody tr:hover .sticky {\n  background-color: rgb(249 250 251);\n}\n\n.dark tbody tr:hover .sticky {\n  background-color: rgb(55 65 81);\n}\n\n/* Responsive table */\n@media (max-width: 768px) {\n  .min-w-24 {\n    min-width: 80px;\n  }\n}\n</style>\n"}