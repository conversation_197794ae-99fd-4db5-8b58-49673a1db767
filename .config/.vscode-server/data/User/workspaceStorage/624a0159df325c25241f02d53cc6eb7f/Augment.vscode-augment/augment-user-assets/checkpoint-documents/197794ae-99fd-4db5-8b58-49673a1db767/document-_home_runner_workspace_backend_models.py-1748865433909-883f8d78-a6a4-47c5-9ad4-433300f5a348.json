{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models.py"}, "originalCode": "from datetime import datetime\nfrom extensions import db\nfrom flask_login import UserMixin\nfrom werkzeug.security import generate_password_hash, check_password_hash\nfrom utils.permissions import ROLE_EMPLOYEE\n\n# Association tables\n\nproject_team = db.Table('project_team',\n    db.Column('project_id', db.Integer, db.<PERSON>('project.id'), primary_key=True),\n    db.<PERSON>umn('user_id', db.Integer, db.Foreign<PERSON>ey('user.id'), primary_key=True),\n    db.Column('role', db.String(50)),\n    extend_existing=True\n)\n\n# User and Authentication models\nclass User(UserMixin, db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    username = db.Column(db.String(64), unique=True, nullable=False)\n    email = db.Column(db.String(120), unique=True, nullable=False)\n    password_hash = db.Column(db.String(256))\n    first_name = db.Column(db.String(64))\n    last_name = db.Column(db.String(64))\n    role = db.Column(db.String(50), default=ROLE_EMPLOYEE, nullable=False)\n    department = db.Column(db.String(64))  # DEPRECATED: use department_id instead\n    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)\n    position = db.Column(db.String(64))\n    hire_date = db.Column(db.Date)\n    phone = db.Column(db.String(20))\n    profile_image = db.Column(db.String(255))\n    bio = db.Column(db.Text)\n    is_active = db.Column(db.Boolean, default=True)\n    dark_mode = db.Column(db.Boolean, default=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    last_login = db.Column(db.DateTime)\n\n    # Password Reset Fields\n    reset_token = db.Column(db.String(100), nullable=True, unique=True)\n    reset_token_expiry = db.Column(db.DateTime, nullable=True)\n\n    # Relationships\n    timesheet_entries = db.relationship('Timesheet', backref='user', lazy='dynamic')\n    projects = db.relationship('Project', secondary=project_team, backref='team_members')\n    created_news = db.relationship('News', backref='author', lazy='dynamic')\n\n    def set_password(self, password):\n        self.password_hash = generate_password_hash(password)\n\n    def check_password(self, password):\n        return check_password_hash(self.password_hash, password)\n\n    def __repr__(self):\n        return f'<User {self.username}>'\n\n    @property\n    def full_name(self):\n        return f\"{self.first_name} {self.last_name}\"\n\n    def to_dict(self):\n        \"\"\"Convert user object to dictionary for JSON serialization\"\"\"\n        return {\n            'id': self.id,\n            'username': self.username,\n            'email': self.email,\n            'first_name': self.first_name,\n            'last_name': self.last_name,\n            'full_name': self.full_name,\n            'role': self.role,\n            'department': self.department,\n            'department_id': self.department_id,\n            'position': self.position,\n            'phone': self.phone,\n            'profile_image': self.profile_image,\n            'bio': self.bio,\n            'is_active': self.is_active,\n            'dark_mode': self.dark_mode,\n            'hire_date': self.hire_date.isoformat() if self.hire_date else None,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'last_login': self.last_login.isoformat() if self.last_login else None\n        }\n\n    def can_view_project(self, project):\n        \"\"\"Verifica se l'utente può visualizzare un progetto\"\"\"\n        from utils.permissions import user_has_permission, PERMISSION_VIEW_ALL_PROJECTS\n\n        # Admin e manager possono vedere tutti i progetti\n        if user_has_permission(self.role, PERMISSION_VIEW_ALL_PROJECTS):\n            return True\n\n        # Altrimenti, l'utente può vedere solo i progetti a cui è assegnato\n        return self in project.team_members\n\n    def has_permission(self, permission_name):\n        \"\"\"Verifica se l'utente ha un permesso specifico\"\"\"\n        from utils.permissions import user_has_permission\n        return user_has_permission(self.role, permission_name)\n\n# Skill Management models\nclass Skill(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(64), unique=True, nullable=False)\n    category = db.Column(db.String(64))\n    description = db.Column(db.Text)\n\n    # Relationships\n    user_skills = db.relationship('UserSkill', backref='skill', lazy='dynamic', cascade='all, delete-orphan')\n\n    def __repr__(self):\n        return f'<Skill {self.name}>'\n\n# HR Management models\nclass Department(db.Model):\n    \"\"\"Modello per la gestione dei dipartimenti aziendali con struttura gerarchica\"\"\"\n    __tablename__ = 'departments'\n\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(100), nullable=False, unique=True)\n    description = db.Column(db.Text)\n    manager_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)\n    parent_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)\n    budget = db.Column(db.Float, default=0.0)\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')\n    employees = db.relationship('User', foreign_keys='User.department_id', backref='department_obj', lazy='dynamic')\n    subdepartments = db.relationship('Department', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')\n\n    def __repr__(self):\n        return f'<Department {self.name}>'\n\n    @property\n    def employee_count(self):\n        \"\"\"Conta il numero di dipendenti nel dipartimento\"\"\"\n        return self.employees.filter_by(is_active=True).count()\n\n    @property\n    def full_path(self):\n        \"\"\"Restituisce il percorso completo del dipartimento (es: IT > Development > Frontend)\"\"\"\n        if self.parent:\n            return f\"{self.parent.full_path} > {self.name}\"\n        return self.name\n\nclass UserProfile(db.Model):\n    \"\"\"Profilo HR esteso per gli utenti\"\"\"\n    __tablename__ = 'user_profiles'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)\n    employee_id = db.Column(db.String(20), unique=True)\n    job_title = db.Column(db.String(100))\n    birth_date = db.Column(db.Date)\n    address = db.Column(db.Text)\n    emergency_contact_name = db.Column(db.String(100))\n    emergency_contact_phone = db.Column(db.String(20))\n    emergency_contact_relationship = db.Column(db.String(50))\n\n    # Informazioni lavorative\n    employment_type = db.Column(db.String(50), default='full_time')  # full_time, part_time, contractor, intern\n    work_location = db.Column(db.String(100))  # office, remote, hybrid\n    salary = db.Column(db.Float)  # Stipendio base\n    salary_currency = db.Column(db.String(3), default='EUR')\n\n    # Informazioni HR\n    probation_end_date = db.Column(db.Date)\n    contract_end_date = db.Column(db.Date)  # Per contratti a termine\n    notice_period_days = db.Column(db.Integer, default=30)\n\n    # Capacità lavorativa\n    weekly_hours = db.Column(db.Float, default=40.0)  # Ore settimanali standard\n    daily_hours = db.Column(db.Float, default=8.0)   # Ore giornaliere standard\n\n    # CV e documenti\n    current_cv_path = db.Column(db.String(255))  # Path del CV attuale\n    cv_last_updated = db.Column(db.DateTime)\n    cv_analysis_data = db.Column(db.Text)  # JSON con analisi AI del CV\n\n    # Metadati\n    profile_completion = db.Column(db.Float, default=0.0)  # Percentuale completamento profilo\n    notes = db.Column(db.Text)  # Note HR riservate\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref=db.backref('profile', uselist=False))\n\n    def __repr__(self):\n        return f'<UserProfile {self.user.username if self.user else self.id}>'\n\n    def calculate_completion(self):\n        \"\"\"Calcola la percentuale di completamento del profilo\"\"\"\n        # Campi UserProfile\n        profile_fields = [\n            self.employee_id, self.job_title, self.birth_date, self.address,\n            self.emergency_contact_name, self.emergency_contact_phone,\n            self.employment_type, self.work_location, self.current_cv_path\n        ]\n\n        # Campi User base (se disponibili)\n        user_fields = []\n        if self.user:\n            user_fields = [\n                self.user.first_name, self.user.last_name, self.user.phone,\n                self.user.position, self.user.bio\n            ]\n\n        # Combina tutti i campi\n        all_fields = profile_fields + user_fields\n        completed = sum(1 for field in all_fields if field)\n\n        # Calcola percentuale e arrotonda\n        if len(all_fields) > 0:\n            self.profile_completion = round((completed / len(all_fields)) * 100)\n        else:\n            self.profile_completion = 0.0\n\n        return self.profile_completion\n\nclass UserSkill(db.Model):\n    \"\"\"Modello per le competenze degli utenti con livelli di proficiency\"\"\"\n    __tablename__ = 'user_skills_detailed'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    skill_id = db.Column(db.Integer, db.ForeignKey('skill.id'), nullable=False)\n    proficiency_level = db.Column(db.Integer, default=1)  # 1-5 scale (1=Beginner, 5=Expert)\n    years_experience = db.Column(db.Float, default=0.0)\n    is_certified = db.Column(db.Boolean, default=False)\n    certification_name = db.Column(db.String(100))\n    certification_date = db.Column(db.Date)\n    certification_expiry = db.Column(db.Date)\n    self_assessed = db.Column(db.Boolean, default=True)  # Auto-valutazione vs valutazione manager\n    manager_assessed = db.Column(db.Boolean, default=False)\n    manager_assessment_date = db.Column(db.Date)\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref='detailed_skills')\n\n    # Constraints\n    __table_args__ = (\n        db.UniqueConstraint('user_id', 'skill_id', name='unique_user_skill'),\n    )\n\n    def __repr__(self):\n        return f'<UserSkill {self.user.username if self.user else self.user_id}:{self.skill.name if self.skill else self.skill_id} L{self.proficiency_level}>'\n\n    @property\n    def proficiency_label(self):\n        \"\"\"Restituisce l'etichetta del livello di competenza\"\"\"\n        levels = {\n            1: 'Principiante',\n            2: 'Base',\n            3: 'Intermedio',\n            4: 'Avanzato',\n            5: 'Esperto'\n        }\n        return levels.get(self.proficiency_level, 'Non definito')\n\n    @property\n    def is_certification_valid(self):\n        \"\"\"Verifica se la certificazione è ancora valida\"\"\"\n        if not self.is_certified or not self.certification_expiry:\n            return self.is_certified\n        from datetime import date\n        return date.today() <= self.certification_expiry\n\n# Project Management models\nclass Project(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    client_id = db.Column(db.Integer, db.ForeignKey('client.id'))\n    start_date = db.Column(db.Date)\n    end_date = db.Column(db.Date)\n    status = db.Column(db.String(20), default='planning')  # planning, active, completed, on-hold\n    budget = db.Column(db.Float)\n    expenses = db.Column(db.Float, default=0.0)\n    project_type = db.Column(db.String(50), default='service')  # service, license, consulting, product, rd, internal\n    is_billable = db.Column(db.Boolean, default=True)  # Progetto fatturabile?\n    client_daily_rate = db.Column(db.Float)  # Tariffa giornaliera al cliente\n    markup_percentage = db.Column(db.Float, default=0.0)  # Markup sui costi\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    tasks = db.relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')\n    timesheet_entries = db.relationship('Timesheet', backref='project', lazy='dynamic')\n    events = db.relationship('Event', backref='project', lazy='dynamic')\n    client = db.relationship('Client', backref='projects')\n\n    def __repr__(self):\n        return f'<Project {self.name}>'\n\n    @property\n    def remaining_budget(self):\n        return self.budget - self.expenses\n\nclass Task(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    assignee_id = db.Column(db.Integer, db.ForeignKey('user.id'))\n    status = db.Column(db.String(20), default='todo')  # todo, in-progress, review, done\n    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent\n    start_date = db.Column(db.Date)  # Data di inizio pianificata\n    due_date = db.Column(db.Date)    # Data di fine pianificata\n    estimated_hours = db.Column(db.Float)  # Ore stimate per completare il task\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    assignee = db.relationship('User', backref='assigned_tasks')\n\n    def __repr__(self):\n        return f'<Task {self.name}>'\n\n    @property\n    def actual_hours(self):\n        \"\"\"Calcola le ore effettive lavorate sul task dai timesheet\"\"\"\n        return sum(entry.hours for entry in self.timesheet_entries)\n\n    @property\n    def hours_variance(self):\n        \"\"\"Calcola la varianza tra ore stimate e ore effettive\"\"\"\n        if not self.estimated_hours or self.actual_hours == 0:\n            return None\n        return self.actual_hours - self.estimated_hours\n\n    @property\n    def hours_efficiency(self):\n        \"\"\"Calcola l'efficienza in percentuale (stimate/effettive * 100)\"\"\"\n        if not self.estimated_hours or self.actual_hours == 0:\n            return None\n        return (self.estimated_hours / self.actual_hours) * 100\n\n    @property\n    def duration_days(self):\n        \"\"\"Calcola la durata pianificata in giorni\"\"\"\n        if not self.start_date or not self.due_date:\n            return None\n        return (self.due_date - self.start_date).days + 1\n\n# Timesheet Management System - Task 3.1\nclass Timesheet(db.Model):\n    \"\"\"Timesheet giornaliero con workflow di approvazione\"\"\"\n    __tablename__ = 'timesheets'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    date = db.Column(db.Date, nullable=False)\n    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected\n    submission_date = db.Column(db.DateTime, nullable=True)\n    approval_date = db.Column(db.DateTime, nullable=True)\n    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)\n    rejection_reason = db.Column(db.Text, nullable=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', foreign_keys=[user_id], backref='timesheets')\n    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_timesheets')\n    entries = db.relationship('TimesheetEntry', backref='timesheet', lazy=True, cascade='all, delete-orphan')\n\n    def __repr__(self):\n        return f'<Timesheet {self.user_id} - {self.date}>'\n\n    @property\n    def total_hours(self):\n        \"\"\"Calcola il totale ore del timesheet\"\"\"\n        return sum(entry.hours for entry in self.entries)\n\n    @property\n    def billable_hours(self):\n        \"\"\"Calcola le ore fatturabili del timesheet\"\"\"\n        return sum(entry.hours for entry in self.entries if entry.billable)\n\n\nclass TimesheetEntry(db.Model):\n    \"\"\"Singola voce di timesheet\"\"\"\n    __tablename__ = 'timesheet_entries'\n\n    id = db.Column(db.Integer, primary_key=True)\n    timesheet_id = db.Column(db.Integer, db.ForeignKey('timesheets.id'), nullable=False)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    task_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=True)\n    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=True)\n    contract_id = db.Column(db.Integer, nullable=True)  # Future CRM integration\n    hours = db.Column(db.Float, nullable=False)\n    description = db.Column(db.Text)\n    entry_type = db.Column(db.String(20), default='work')  # work, vacation, leave, smartworking\n    billable = db.Column(db.Boolean, default=False)\n    billing_rate = db.Column(db.Float, nullable=True)\n    billing_status = db.Column(db.String(20), default='unbilled')  # unbilled, billed, non-billable\n    invoice_id = db.Column(db.Integer, nullable=True)  # Future billing integration\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='timesheet_entries')\n    task = db.relationship('Task', backref='timesheet_entries')\n    client = db.relationship('Client', backref='timesheet_entries')\n\n    def __repr__(self):\n        return f'<TimesheetEntry {self.timesheet_id} - {self.project_id}: {self.hours}h>'\n\n\nclass TimeOffRequest(db.Model):\n    \"\"\"Richieste di ferie, permessi e smartworking\"\"\"\n    __tablename__ = 'time_off_requests'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    request_type = db.Column(db.String(20), nullable=False)  # vacation, leave, smartworking\n    start_date = db.Column(db.Date, nullable=False)\n    end_date = db.Column(db.Date, nullable=False)\n    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected\n    notes = db.Column(db.Text)\n    submission_date = db.Column(db.DateTime, default=datetime.utcnow)\n    approval_date = db.Column(db.DateTime, nullable=True)\n    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)\n    rejection_reason = db.Column(db.Text, nullable=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', foreign_keys=[user_id], backref='time_off_requests')\n    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_time_off_requests')\n\n    def __repr__(self):\n        return f'<TimeOffRequest {self.user_id} - {self.request_type}: {self.start_date} to {self.end_date}>'\n\n    @property\n    def duration_days(self):\n        \"\"\"Calcola la durata in giorni della richiesta\"\"\"\n        return (self.end_date - self.start_date).days + 1\n\nclass Event(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))\n    start_time = db.Column(db.DateTime, nullable=False)\n    end_time = db.Column(db.DateTime, nullable=False)\n    location = db.Column(db.String(128))\n    event_type = db.Column(db.String(20))  # meeting, deadline, milestone\n    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    creator = db.relationship('User', backref='created_events')\n\n    def __repr__(self):\n        return f'<Event {self.title}>'\n\n# CRM models\nclass Client(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    industry = db.Column(db.String(64))\n    description = db.Column(db.Text)\n    website = db.Column(db.String(128))\n    address = db.Column(db.String(255))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships defined in Project model\n    contacts = db.relationship('Contact', backref='client', lazy='dynamic', cascade='all, delete-orphan')\n    proposals = db.relationship('Proposal', backref='client', lazy='dynamic')\n\n    def __repr__(self):\n        return f'<Client {self.name}>'\n\nclass Contact(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)\n    first_name = db.Column(db.String(64), nullable=False)\n    last_name = db.Column(db.String(64), nullable=False)\n    position = db.Column(db.String(64))\n    email = db.Column(db.String(120))\n    phone = db.Column(db.String(20))\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<Contact {self.first_name} {self.last_name}>'\n\n    @property\n    def full_name(self):\n        return f\"{self.first_name} {self.last_name}\"\n\nclass Proposal(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)\n    description = db.Column(db.Text)\n    value = db.Column(db.Float)\n    status = db.Column(db.String(20), default='draft')  # draft, sent, negotiating, accepted, rejected\n    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))\n    sent_date = db.Column(db.Date)\n    expiry_date = db.Column(db.Date)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    creator = db.relationship('User', backref='created_proposals')\n\n    def __repr__(self):\n        return f'<Proposal {self.title}>'\n\n# Product Catalog models\nclass Product(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    category = db.Column(db.String(64))\n    price = db.Column(db.Float)\n    status = db.Column(db.String(20), default='active')  # active, discontinued\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<Product {self.name}>'\n\nclass Service(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    category = db.Column(db.String(64))\n    hourly_rate = db.Column(db.Float)\n    status = db.Column(db.String(20), default='active')  # active, discontinued\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<Service {self.name}>'\n\n# Performance Management models\nclass KPI(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    category = db.Column(db.String(64))\n    target_value = db.Column(db.Float)\n    current_value = db.Column(db.Float, default=0.0)\n    unit = db.Column(db.String(20))\n    frequency = db.Column(db.String(20))  # daily, weekly, monthly, quarterly, annually\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<KPI {self.name}>'\n\n    @property\n    def progress(self):\n        if self.target_value and self.target_value > 0 and self.current_value is not None:\n            return (self.current_value / self.target_value) * 100\n        return 0\n\nclass BusinessProcess(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))\n    status = db.Column(db.String(20), default='active')  # draft, active, under_review, archived\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    owner = db.relationship('User', backref='owned_processes')\n    steps = db.relationship('ProcessStep', backref='process', lazy='dynamic', cascade='all, delete-orphan')\n\n    def __repr__(self):\n        return f'<BusinessProcess {self.name}>'\n\nclass ProcessStep(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    process_id = db.Column(db.Integer, db.ForeignKey('business_process.id'), nullable=False)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    order = db.Column(db.Integer, nullable=False)\n    responsible_role = db.Column(db.String(64))\n    estimated_time = db.Column(db.Float)  # in hours\n\n    def __repr__(self):\n        return f'<ProcessStep {self.name}>'\n\n# Communication models\nclass News(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    content = db.Column(db.Text, nullable=False)\n    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    image_url = db.Column(db.String(255))\n    is_published = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<News {self.title}>'\n\nclass Document(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    category = db.Column(db.String(64))\n    file_path = db.Column(db.String(255), nullable=False)\n    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    version = db.Column(db.String(20), default='1.0')\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    uploader = db.relationship('User', backref='uploaded_documents')\n\n    def __repr__(self):\n        return f'<Document {self.title}>'\n\nclass Regulation(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    content = db.Column(db.Text, nullable=False)\n    category = db.Column(db.String(64))\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<Regulation {self.title}>'\n\n# Funding and Grant models\nclass FundingOpportunity(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    source = db.Column(db.String(128))\n    amount = db.Column(db.Float)\n    application_deadline = db.Column(db.Date)\n    status = db.Column(db.String(20), default='open')  # open, applied, awarded, rejected, closed\n    requirements = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    applications = db.relationship('FundingApplication', backref='opportunity', lazy='dynamic')\n\n    def __repr__(self):\n        return f'<FundingOpportunity {self.title}>'\n\nclass FundingApplication(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    opportunity_id = db.Column(db.Integer, db.ForeignKey('funding_opportunity.id'), nullable=False)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))\n    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected\n    submission_date = db.Column(db.Date)\n    notes = db.Column(db.Text)\n    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='funding_applications')\n    creator = db.relationship('User', backref='created_applications')\n    expenses = db.relationship('FundingExpense', backref='application', lazy='dynamic')\n\n    def __repr__(self):\n        return f'<FundingApplication {self.id}>'\n\nclass FundingExpense(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    application_id = db.Column(db.Integer, db.ForeignKey('funding_application.id'), nullable=False)\n    description = db.Column(db.String(255), nullable=False)\n    amount = db.Column(db.Float, nullable=False)\n    date = db.Column(db.Date, nullable=False)\n    category = db.Column(db.String(64))\n    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected\n    receipt_path = db.Column(db.String(255))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<FundingExpense {self.description}>'\n\n# Startup Resources models\nclass StartupResource(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    resource_type = db.Column(db.String(64))  # incentive, tax_benefit, mentorship, funding, other\n    link = db.Column(db.String(255))\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<StartupResource {self.title}>'\n\n# Notification system\nclass Notification(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    title = db.Column(db.String(128), nullable=False)\n    message = db.Column(db.Text, nullable=False)\n    link = db.Column(db.String(255))\n    type = db.Column(db.String(50), default='info') # info, success, warning, danger\n    is_read = db.Column(db.Boolean, default=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref='notifications')\n\n    def __repr__(self):\n        return f'<Notification {self.id} for {self.user_id}>'\n\nclass AdminLog(db.Model):\n    \"\"\"Registra le azioni amministrative sugli utenti.\"\"\"\n    __tablename__ = 'admin_logs'\n\n    id = db.Column(db.Integer, primary_key=True)\n    admin_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    action = db.Column(db.String(255), nullable=False)\n    target_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)\n    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)\n\n    # Relazioni\n    admin = db.relationship('User', foreign_keys=[admin_id], backref='admin_logs')\n    target_user = db.relationship('User', foreign_keys=[target_user_id], backref='target_logs')\n\n    def __repr__(self):\n        return f'<AdminLog {self.id}: {self.action}>'\n\nclass ProjectResource(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    allocation_percentage = db.Column(db.Integer, default=100)\n    role = db.Column(db.String(50))\n    # Relationships opzionali\n    project = db.relationship('Project', backref='project_resources')\n    user = db.relationship('User', backref='resource_assignments')\n\nclass TaskDependency(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    task_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=False)\n    depends_on_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=False)\n    # Relationships opzionali\n    task = db.relationship('Task', foreign_keys=[task_id], backref='dependencies')\n    depends_on = db.relationship('Task', foreign_keys=[depends_on_id])\n\nclass ProjectKPI(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    kpi_id = db.Column(db.Integer, db.ForeignKey('kpi.id'), nullable=False)\n    target_value = db.Column(db.Float)\n    current_value = db.Column(db.Float)\n    # Relationships opzionali\n    project = db.relationship('Project', backref='kpis')\n    kpi = db.relationship('KPI', backref='project_links')\n\n    @property\n    def progress(self):\n        if self.target_value and self.target_value > 0 and self.current_value is not None:\n            return (self.current_value / self.target_value) * 100\n        return 0\n\n# Cost Management models\nclass PersonnelRate(db.Model):\n    \"\"\"Storico tariffe giornaliere del personale\"\"\"\n    __tablename__ = 'personnel_rates'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    daily_rate = db.Column(db.Float, nullable=False)  # Tariffa giornaliera\n    valid_from = db.Column(db.Date, nullable=False)  # Data inizio validità\n    valid_to = db.Column(db.Date, nullable=True)  # Data fine (NULL = attuale)\n    currency = db.Column(db.String(3), default='EUR')\n    notes = db.Column(db.String(255))  # Es: \"Aumento annuale\", \"Promozione\"\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref='rate_history')\n\n    def __repr__(self):\n        return f'<PersonnelRate {self.user_id}: €{self.daily_rate}/day>'\n\n    @property\n    def is_current(self):\n        \"\"\"Verifica se questa tariffa è attualmente valida\"\"\"\n        from datetime import date\n        today = date.today()\n        return (self.valid_from <= today and\n                (self.valid_to is None or self.valid_to >= today))\n\nclass ProjectExpense(db.Model):\n    \"\"\"Spese di progetto (non personale)\"\"\"\n    __tablename__ = 'project_expenses'\n\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # Chi ha sostenuto\n    category = db.Column(db.String(50), nullable=False)  # licenses, travel, meals, equipment, external, other\n    description = db.Column(db.String(255), nullable=False)\n    amount = db.Column(db.Float, nullable=False)\n    billing_type = db.Column(db.String(20), default='billable')  # billable, non-billable, reimbursable\n    date = db.Column(db.Date, nullable=False)\n    receipt_path = db.Column(db.String(255))  # Allegato ricevuta\n    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='expenses_detailed')\n    user = db.relationship('User', backref='submitted_expenses')\n\n    def __repr__(self):\n        return f'<ProjectExpense {self.project_id}: €{self.amount} ({self.category})>'\n\nclass ProjectKPITemplate(db.Model):\n    \"\"\"Template KPI di default per tipologie progetto (solo Admin)\"\"\"\n    __tablename__ = 'project_kpi_templates'\n\n    id = db.Column(db.Integer, primary_key=True)\n    project_type = db.Column(db.String(50), nullable=False)  # service, license, consulting, product, rd, internal\n    kpi_name = db.Column(db.String(100), nullable=False)  # margin_percentage, utilization_rate, cost_per_hour, cost_revenue_ratio\n    target_min = db.Column(db.Float)  # Soglia minima accettabile\n    target_max = db.Column(db.Float)  # Soglia ottimale\n    warning_threshold = db.Column(db.Float)  # Soglia di warning\n    unit = db.Column(db.String(10), default='%')  # %, €, ratio, giorni\n    description = db.Column(db.Text)\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<KPITemplate {self.project_type}.{self.kpi_name}>'\n\n    @property\n    def display_name(self):\n        \"\"\"Nome leggibile del KPI\"\"\"\n        names = {\n            'margin_percentage': 'Margine Netto',\n            'utilization_rate': 'Utilization Rate',\n            'cost_per_hour': 'Costo per Ora',\n            'cost_revenue_ratio': 'Rapporto C/R'\n        }\n        return names.get(self.kpi_name, self.kpi_name)\n\nclass ProjectKPITarget(db.Model):\n    \"\"\"KPI target personalizzati per singolo progetto (Admin + Project Owner)\"\"\"\n    __tablename__ = 'project_kpi_targets'\n\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    kpi_name = db.Column(db.String(100), nullable=False)\n    target_value = db.Column(db.Float)  # Valore target personalizzato\n    warning_threshold = db.Column(db.Float)  # Soglia warning personalizzata\n    custom_description = db.Column(db.Text)  # Descrizione personalizzata\n    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='kpi_targets')\n    creator = db.relationship('User', backref='created_kpi_targets')\n\n    def __repr__(self):\n        return f'<ProjectKPITarget {self.project_id}.{self.kpi_name}: {self.target_value}>'\n\n    @property\n    def display_name(self):\n        \"\"\"Nome leggibile del KPI\"\"\"\n        names = {\n            'margin_percentage': 'Margine Netto',\n            'utilization_rate': 'Utilization Rate',\n            'cost_per_hour': 'Costo per Ora',\n            'cost_revenue_ratio': 'Rapporto C/R'\n        }\n        return names.get(self.kpi_name, self.kpi_name)", "modifiedCode": "from datetime import datetime\nfrom extensions import db\nfrom flask_login import UserMixin\nfrom werkzeug.security import generate_password_hash, check_password_hash\nfrom utils.permissions import ROLE_EMPLOYEE\n\n# Association tables\n\nproject_team = db.Table('project_team',\n    db.Column('project_id', db.Integer, db.<PERSON>('project.id'), primary_key=True),\n    db.<PERSON>umn('user_id', db.Integer, db.Foreign<PERSON>ey('user.id'), primary_key=True),\n    db.Column('role', db.String(50)),\n    extend_existing=True\n)\n\n# User and Authentication models\nclass User(UserMixin, db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    username = db.Column(db.String(64), unique=True, nullable=False)\n    email = db.Column(db.String(120), unique=True, nullable=False)\n    password_hash = db.Column(db.String(256))\n    first_name = db.Column(db.String(64))\n    last_name = db.Column(db.String(64))\n    role = db.Column(db.String(50), default=ROLE_EMPLOYEE, nullable=False)\n    department = db.Column(db.String(64))  # DEPRECATED: use department_id instead\n    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)\n    position = db.Column(db.String(64))\n    hire_date = db.Column(db.Date)\n    phone = db.Column(db.String(20))\n    profile_image = db.Column(db.String(255))\n    bio = db.Column(db.Text)\n    is_active = db.Column(db.Boolean, default=True)\n    dark_mode = db.Column(db.Boolean, default=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    last_login = db.Column(db.DateTime)\n\n    # Password Reset Fields\n    reset_token = db.Column(db.String(100), nullable=True, unique=True)\n    reset_token_expiry = db.Column(db.DateTime, nullable=True)\n\n    # Relationships\n    timesheet_entries = db.relationship('Timesheet', backref='user', lazy='dynamic')\n    projects = db.relationship('Project', secondary=project_team, backref='team_members')\n    created_news = db.relationship('News', backref='author', lazy='dynamic')\n\n    def set_password(self, password):\n        self.password_hash = generate_password_hash(password)\n\n    def check_password(self, password):\n        return check_password_hash(self.password_hash, password)\n\n    def __repr__(self):\n        return f'<User {self.username}>'\n\n    @property\n    def full_name(self):\n        return f\"{self.first_name} {self.last_name}\"\n\n    def to_dict(self):\n        \"\"\"Convert user object to dictionary for JSON serialization\"\"\"\n        return {\n            'id': self.id,\n            'username': self.username,\n            'email': self.email,\n            'first_name': self.first_name,\n            'last_name': self.last_name,\n            'full_name': self.full_name,\n            'role': self.role,\n            'department': self.department,\n            'department_id': self.department_id,\n            'position': self.position,\n            'phone': self.phone,\n            'profile_image': self.profile_image,\n            'bio': self.bio,\n            'is_active': self.is_active,\n            'dark_mode': self.dark_mode,\n            'hire_date': self.hire_date.isoformat() if self.hire_date else None,\n            'created_at': self.created_at.isoformat() if self.created_at else None,\n            'last_login': self.last_login.isoformat() if self.last_login else None\n        }\n\n    def can_view_project(self, project):\n        \"\"\"Verifica se l'utente può visualizzare un progetto\"\"\"\n        from utils.permissions import user_has_permission, PERMISSION_VIEW_ALL_PROJECTS\n\n        # Admin e manager possono vedere tutti i progetti\n        if user_has_permission(self.role, PERMISSION_VIEW_ALL_PROJECTS):\n            return True\n\n        # Altrimenti, l'utente può vedere solo i progetti a cui è assegnato\n        return self in project.team_members\n\n    def has_permission(self, permission_name):\n        \"\"\"Verifica se l'utente ha un permesso specifico\"\"\"\n        from utils.permissions import user_has_permission\n        return user_has_permission(self.role, permission_name)\n\n# Skill Management models\nclass Skill(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(64), unique=True, nullable=False)\n    category = db.Column(db.String(64))\n    description = db.Column(db.Text)\n\n    # Relationships\n    user_skills = db.relationship('UserSkill', backref='skill', lazy='dynamic', cascade='all, delete-orphan')\n\n    def __repr__(self):\n        return f'<Skill {self.name}>'\n\n# HR Management models\nclass Department(db.Model):\n    \"\"\"Modello per la gestione dei dipartimenti aziendali con struttura gerarchica\"\"\"\n    __tablename__ = 'departments'\n\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(100), nullable=False, unique=True)\n    description = db.Column(db.Text)\n    manager_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)\n    parent_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)\n    budget = db.Column(db.Float, default=0.0)\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    manager = db.relationship('User', foreign_keys=[manager_id], backref='managed_departments')\n    employees = db.relationship('User', foreign_keys='User.department_id', backref='department_obj', lazy='dynamic')\n    subdepartments = db.relationship('Department', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')\n\n    def __repr__(self):\n        return f'<Department {self.name}>'\n\n    @property\n    def employee_count(self):\n        \"\"\"Conta il numero di dipendenti nel dipartimento\"\"\"\n        return self.employees.filter_by(is_active=True).count()\n\n    @property\n    def full_path(self):\n        \"\"\"Restituisce il percorso completo del dipartimento (es: IT > Development > Frontend)\"\"\"\n        if self.parent:\n            return f\"{self.parent.full_path} > {self.name}\"\n        return self.name\n\nclass UserProfile(db.Model):\n    \"\"\"Profilo HR esteso per gli utenti\"\"\"\n    __tablename__ = 'user_profiles'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)\n    employee_id = db.Column(db.String(20), unique=True)\n    job_title = db.Column(db.String(100))\n    birth_date = db.Column(db.Date)\n    address = db.Column(db.Text)\n    emergency_contact_name = db.Column(db.String(100))\n    emergency_contact_phone = db.Column(db.String(20))\n    emergency_contact_relationship = db.Column(db.String(50))\n\n    # Informazioni lavorative\n    employment_type = db.Column(db.String(50), default='full_time')  # full_time, part_time, contractor, intern\n    work_location = db.Column(db.String(100))  # office, remote, hybrid\n    salary = db.Column(db.Float)  # Stipendio base\n    salary_currency = db.Column(db.String(3), default='EUR')\n\n    # Informazioni HR\n    probation_end_date = db.Column(db.Date)\n    contract_end_date = db.Column(db.Date)  # Per contratti a termine\n    notice_period_days = db.Column(db.Integer, default=30)\n\n    # Capacità lavorativa\n    weekly_hours = db.Column(db.Float, default=40.0)  # Ore settimanali standard\n    daily_hours = db.Column(db.Float, default=8.0)   # Ore giornaliere standard\n\n    # CV e documenti\n    current_cv_path = db.Column(db.String(255))  # Path del CV attuale\n    cv_last_updated = db.Column(db.DateTime)\n    cv_analysis_data = db.Column(db.Text)  # JSON con analisi AI del CV\n\n    # Metadati\n    profile_completion = db.Column(db.Float, default=0.0)  # Percentuale completamento profilo\n    notes = db.Column(db.Text)  # Note HR riservate\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref=db.backref('profile', uselist=False))\n\n    def __repr__(self):\n        return f'<UserProfile {self.user.username if self.user else self.id}>'\n\n    def calculate_completion(self):\n        \"\"\"Calcola la percentuale di completamento del profilo\"\"\"\n        # Campi UserProfile\n        profile_fields = [\n            self.employee_id, self.job_title, self.birth_date, self.address,\n            self.emergency_contact_name, self.emergency_contact_phone,\n            self.employment_type, self.work_location, self.current_cv_path\n        ]\n\n        # Campi User base (se disponibili)\n        user_fields = []\n        if self.user:\n            user_fields = [\n                self.user.first_name, self.user.last_name, self.user.phone,\n                self.user.position, self.user.bio\n            ]\n\n        # Combina tutti i campi\n        all_fields = profile_fields + user_fields\n        completed = sum(1 for field in all_fields if field)\n\n        # Calcola percentuale e arrotonda\n        if len(all_fields) > 0:\n            self.profile_completion = round((completed / len(all_fields)) * 100)\n        else:\n            self.profile_completion = 0.0\n\n        return self.profile_completion\n\nclass UserSkill(db.Model):\n    \"\"\"Modello per le competenze degli utenti con livelli di proficiency\"\"\"\n    __tablename__ = 'user_skills_detailed'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    skill_id = db.Column(db.Integer, db.ForeignKey('skill.id'), nullable=False)\n    proficiency_level = db.Column(db.Integer, default=1)  # 1-5 scale (1=Beginner, 5=Expert)\n    years_experience = db.Column(db.Float, default=0.0)\n    is_certified = db.Column(db.Boolean, default=False)\n    certification_name = db.Column(db.String(100))\n    certification_date = db.Column(db.Date)\n    certification_expiry = db.Column(db.Date)\n    self_assessed = db.Column(db.Boolean, default=True)  # Auto-valutazione vs valutazione manager\n    manager_assessed = db.Column(db.Boolean, default=False)\n    manager_assessment_date = db.Column(db.Date)\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref='detailed_skills')\n\n    # Constraints\n    __table_args__ = (\n        db.UniqueConstraint('user_id', 'skill_id', name='unique_user_skill'),\n    )\n\n    def __repr__(self):\n        return f'<UserSkill {self.user.username if self.user else self.user_id}:{self.skill.name if self.skill else self.skill_id} L{self.proficiency_level}>'\n\n    @property\n    def proficiency_label(self):\n        \"\"\"Restituisce l'etichetta del livello di competenza\"\"\"\n        levels = {\n            1: 'Principiante',\n            2: 'Base',\n            3: 'Intermedio',\n            4: 'Avanzato',\n            5: 'Esperto'\n        }\n        return levels.get(self.proficiency_level, 'Non definito')\n\n    @property\n    def is_certification_valid(self):\n        \"\"\"Verifica se la certificazione è ancora valida\"\"\"\n        if not self.is_certified or not self.certification_expiry:\n            return self.is_certified\n        from datetime import date\n        return date.today() <= self.certification_expiry\n\n# Project Management models\nclass Project(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    client_id = db.Column(db.Integer, db.ForeignKey('client.id'))\n    start_date = db.Column(db.Date)\n    end_date = db.Column(db.Date)\n    status = db.Column(db.String(20), default='planning')  # planning, active, completed, on-hold\n    budget = db.Column(db.Float)\n    expenses = db.Column(db.Float, default=0.0)\n    project_type = db.Column(db.String(50), default='service')  # service, license, consulting, product, rd, internal\n    is_billable = db.Column(db.Boolean, default=True)  # Progetto fatturabile?\n    client_daily_rate = db.Column(db.Float)  # Tariffa giornaliera al cliente\n    markup_percentage = db.Column(db.Float, default=0.0)  # Markup sui costi\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    tasks = db.relationship('Task', backref='project', lazy='dynamic', cascade='all, delete-orphan')\n    timesheet_entries = db.relationship('Timesheet', backref='project', lazy='dynamic')\n    events = db.relationship('Event', backref='project', lazy='dynamic')\n    client = db.relationship('Client', backref='projects')\n\n    def __repr__(self):\n        return f'<Project {self.name}>'\n\n    @property\n    def remaining_budget(self):\n        return self.budget - self.expenses\n\nclass Task(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    assignee_id = db.Column(db.Integer, db.ForeignKey('user.id'))\n    status = db.Column(db.String(20), default='todo')  # todo, in-progress, review, done\n    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent\n    start_date = db.Column(db.Date)  # Data di inizio pianificata\n    due_date = db.Column(db.Date)    # Data di fine pianificata\n    estimated_hours = db.Column(db.Float)  # Ore stimate per completare il task\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    assignee = db.relationship('User', backref='assigned_tasks')\n\n    def __repr__(self):\n        return f'<Task {self.name}>'\n\n    @property\n    def actual_hours(self):\n        \"\"\"Calcola le ore effettive lavorate sul task dai timesheet\"\"\"\n        return sum(entry.hours for entry in self.timesheet_entries)\n\n    @property\n    def hours_variance(self):\n        \"\"\"Calcola la varianza tra ore stimate e ore effettive\"\"\"\n        if not self.estimated_hours or self.actual_hours == 0:\n            return None\n        return self.actual_hours - self.estimated_hours\n\n    @property\n    def hours_efficiency(self):\n        \"\"\"Calcola l'efficienza in percentuale (stimate/effettive * 100)\"\"\"\n        if not self.estimated_hours or self.actual_hours == 0:\n            return None\n        return (self.estimated_hours / self.actual_hours) * 100\n\n    @property\n    def duration_days(self):\n        \"\"\"Calcola la durata pianificata in giorni\"\"\"\n        if not self.start_date or not self.due_date:\n            return None\n        return (self.due_date - self.start_date).days + 1\n\nclass Timesheet(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    task_id = db.Column(db.Integer, db.ForeignKey('task.id'))\n    date = db.Column(db.Date, nullable=False)\n    hours = db.Column(db.Float, nullable=False)\n    description = db.Column(db.Text)\n    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    task = db.relationship('Task', backref='timesheet_entries')\n\n    def __repr__(self):\n        return f'<Timesheet {self.user_id} - {self.date}>'\n\nclass Event(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))\n    start_time = db.Column(db.DateTime, nullable=False)\n    end_time = db.Column(db.DateTime, nullable=False)\n    location = db.Column(db.String(128))\n    event_type = db.Column(db.String(20))  # meeting, deadline, milestone\n    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    creator = db.relationship('User', backref='created_events')\n\n    def __repr__(self):\n        return f'<Event {self.title}>'\n\n# CRM models\nclass Client(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    industry = db.Column(db.String(64))\n    description = db.Column(db.Text)\n    website = db.Column(db.String(128))\n    address = db.Column(db.String(255))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships defined in Project model\n    contacts = db.relationship('Contact', backref='client', lazy='dynamic', cascade='all, delete-orphan')\n    proposals = db.relationship('Proposal', backref='client', lazy='dynamic')\n\n    def __repr__(self):\n        return f'<Client {self.name}>'\n\nclass Contact(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)\n    first_name = db.Column(db.String(64), nullable=False)\n    last_name = db.Column(db.String(64), nullable=False)\n    position = db.Column(db.String(64))\n    email = db.Column(db.String(120))\n    phone = db.Column(db.String(20))\n    notes = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<Contact {self.first_name} {self.last_name}>'\n\n    @property\n    def full_name(self):\n        return f\"{self.first_name} {self.last_name}\"\n\nclass Proposal(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)\n    description = db.Column(db.Text)\n    value = db.Column(db.Float)\n    status = db.Column(db.String(20), default='draft')  # draft, sent, negotiating, accepted, rejected\n    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))\n    sent_date = db.Column(db.Date)\n    expiry_date = db.Column(db.Date)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    creator = db.relationship('User', backref='created_proposals')\n\n    def __repr__(self):\n        return f'<Proposal {self.title}>'\n\n# Product Catalog models\nclass Product(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    category = db.Column(db.String(64))\n    price = db.Column(db.Float)\n    status = db.Column(db.String(20), default='active')  # active, discontinued\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<Product {self.name}>'\n\nclass Service(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    category = db.Column(db.String(64))\n    hourly_rate = db.Column(db.Float)\n    status = db.Column(db.String(20), default='active')  # active, discontinued\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<Service {self.name}>'\n\n# Performance Management models\nclass KPI(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    category = db.Column(db.String(64))\n    target_value = db.Column(db.Float)\n    current_value = db.Column(db.Float, default=0.0)\n    unit = db.Column(db.String(20))\n    frequency = db.Column(db.String(20))  # daily, weekly, monthly, quarterly, annually\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<KPI {self.name}>'\n\n    @property\n    def progress(self):\n        if self.target_value and self.target_value > 0 and self.current_value is not None:\n            return (self.current_value / self.target_value) * 100\n        return 0\n\nclass BusinessProcess(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))\n    status = db.Column(db.String(20), default='active')  # draft, active, under_review, archived\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    owner = db.relationship('User', backref='owned_processes')\n    steps = db.relationship('ProcessStep', backref='process', lazy='dynamic', cascade='all, delete-orphan')\n\n    def __repr__(self):\n        return f'<BusinessProcess {self.name}>'\n\nclass ProcessStep(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    process_id = db.Column(db.Integer, db.ForeignKey('business_process.id'), nullable=False)\n    name = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    order = db.Column(db.Integer, nullable=False)\n    responsible_role = db.Column(db.String(64))\n    estimated_time = db.Column(db.Float)  # in hours\n\n    def __repr__(self):\n        return f'<ProcessStep {self.name}>'\n\n# Communication models\nclass News(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    content = db.Column(db.Text, nullable=False)\n    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    image_url = db.Column(db.String(255))\n    is_published = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<News {self.title}>'\n\nclass Document(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    category = db.Column(db.String(64))\n    file_path = db.Column(db.String(255), nullable=False)\n    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    version = db.Column(db.String(20), default='1.0')\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    uploader = db.relationship('User', backref='uploaded_documents')\n\n    def __repr__(self):\n        return f'<Document {self.title}>'\n\nclass Regulation(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    content = db.Column(db.Text, nullable=False)\n    category = db.Column(db.String(64))\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<Regulation {self.title}>'\n\n# Funding and Grant models\nclass FundingOpportunity(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    source = db.Column(db.String(128))\n    amount = db.Column(db.Float)\n    application_deadline = db.Column(db.Date)\n    status = db.Column(db.String(20), default='open')  # open, applied, awarded, rejected, closed\n    requirements = db.Column(db.Text)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    applications = db.relationship('FundingApplication', backref='opportunity', lazy='dynamic')\n\n    def __repr__(self):\n        return f'<FundingOpportunity {self.title}>'\n\nclass FundingApplication(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    opportunity_id = db.Column(db.Integer, db.ForeignKey('funding_opportunity.id'), nullable=False)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))\n    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected\n    submission_date = db.Column(db.Date)\n    notes = db.Column(db.Text)\n    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='funding_applications')\n    creator = db.relationship('User', backref='created_applications')\n    expenses = db.relationship('FundingExpense', backref='application', lazy='dynamic')\n\n    def __repr__(self):\n        return f'<FundingApplication {self.id}>'\n\nclass FundingExpense(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    application_id = db.Column(db.Integer, db.ForeignKey('funding_application.id'), nullable=False)\n    description = db.Column(db.String(255), nullable=False)\n    amount = db.Column(db.Float, nullable=False)\n    date = db.Column(db.Date, nullable=False)\n    category = db.Column(db.String(64))\n    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected\n    receipt_path = db.Column(db.String(255))\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<FundingExpense {self.description}>'\n\n# Startup Resources models\nclass StartupResource(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    title = db.Column(db.String(128), nullable=False)\n    description = db.Column(db.Text)\n    resource_type = db.Column(db.String(64))  # incentive, tax_benefit, mentorship, funding, other\n    link = db.Column(db.String(255))\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<StartupResource {self.title}>'\n\n# Notification system\nclass Notification(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    title = db.Column(db.String(128), nullable=False)\n    message = db.Column(db.Text, nullable=False)\n    link = db.Column(db.String(255))\n    type = db.Column(db.String(50), default='info') # info, success, warning, danger\n    is_read = db.Column(db.Boolean, default=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref='notifications')\n\n    def __repr__(self):\n        return f'<Notification {self.id} for {self.user_id}>'\n\nclass AdminLog(db.Model):\n    \"\"\"Registra le azioni amministrative sugli utenti.\"\"\"\n    __tablename__ = 'admin_logs'\n\n    id = db.Column(db.Integer, primary_key=True)\n    admin_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    action = db.Column(db.String(255), nullable=False)\n    target_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)\n    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)\n\n    # Relazioni\n    admin = db.relationship('User', foreign_keys=[admin_id], backref='admin_logs')\n    target_user = db.relationship('User', foreign_keys=[target_user_id], backref='target_logs')\n\n    def __repr__(self):\n        return f'<AdminLog {self.id}: {self.action}>'\n\nclass ProjectResource(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    allocation_percentage = db.Column(db.Integer, default=100)\n    role = db.Column(db.String(50))\n    # Relationships opzionali\n    project = db.relationship('Project', backref='project_resources')\n    user = db.relationship('User', backref='resource_assignments')\n\nclass TaskDependency(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    task_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=False)\n    depends_on_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=False)\n    # Relationships opzionali\n    task = db.relationship('Task', foreign_keys=[task_id], backref='dependencies')\n    depends_on = db.relationship('Task', foreign_keys=[depends_on_id])\n\nclass ProjectKPI(db.Model):\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    kpi_id = db.Column(db.Integer, db.ForeignKey('kpi.id'), nullable=False)\n    target_value = db.Column(db.Float)\n    current_value = db.Column(db.Float)\n    # Relationships opzionali\n    project = db.relationship('Project', backref='kpis')\n    kpi = db.relationship('KPI', backref='project_links')\n\n    @property\n    def progress(self):\n        if self.target_value and self.target_value > 0 and self.current_value is not None:\n            return (self.current_value / self.target_value) * 100\n        return 0\n\n# Cost Management models\nclass PersonnelRate(db.Model):\n    \"\"\"Storico tariffe giornaliere del personale\"\"\"\n    __tablename__ = 'personnel_rates'\n\n    id = db.Column(db.Integer, primary_key=True)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    daily_rate = db.Column(db.Float, nullable=False)  # Tariffa giornaliera\n    valid_from = db.Column(db.Date, nullable=False)  # Data inizio validità\n    valid_to = db.Column(db.Date, nullable=True)  # Data fine (NULL = attuale)\n    currency = db.Column(db.String(3), default='EUR')\n    notes = db.Column(db.String(255))  # Es: \"Aumento annuale\", \"Promozione\"\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    user = db.relationship('User', backref='rate_history')\n\n    def __repr__(self):\n        return f'<PersonnelRate {self.user_id}: €{self.daily_rate}/day>'\n\n    @property\n    def is_current(self):\n        \"\"\"Verifica se questa tariffa è attualmente valida\"\"\"\n        from datetime import date\n        today = date.today()\n        return (self.valid_from <= today and\n                (self.valid_to is None or self.valid_to >= today))\n\nclass ProjectExpense(db.Model):\n    \"\"\"Spese di progetto (non personale)\"\"\"\n    __tablename__ = 'project_expenses'\n\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # Chi ha sostenuto\n    category = db.Column(db.String(50), nullable=False)  # licenses, travel, meals, equipment, external, other\n    description = db.Column(db.String(255), nullable=False)\n    amount = db.Column(db.Float, nullable=False)\n    billing_type = db.Column(db.String(20), default='billable')  # billable, non-billable, reimbursable\n    date = db.Column(db.Date, nullable=False)\n    receipt_path = db.Column(db.String(255))  # Allegato ricevuta\n    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='expenses_detailed')\n    user = db.relationship('User', backref='submitted_expenses')\n\n    def __repr__(self):\n        return f'<ProjectExpense {self.project_id}: €{self.amount} ({self.category})>'\n\nclass ProjectKPITemplate(db.Model):\n    \"\"\"Template KPI di default per tipologie progetto (solo Admin)\"\"\"\n    __tablename__ = 'project_kpi_templates'\n\n    id = db.Column(db.Integer, primary_key=True)\n    project_type = db.Column(db.String(50), nullable=False)  # service, license, consulting, product, rd, internal\n    kpi_name = db.Column(db.String(100), nullable=False)  # margin_percentage, utilization_rate, cost_per_hour, cost_revenue_ratio\n    target_min = db.Column(db.Float)  # Soglia minima accettabile\n    target_max = db.Column(db.Float)  # Soglia ottimale\n    warning_threshold = db.Column(db.Float)  # Soglia di warning\n    unit = db.Column(db.String(10), default='%')  # %, €, ratio, giorni\n    description = db.Column(db.Text)\n    is_active = db.Column(db.Boolean, default=True)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    def __repr__(self):\n        return f'<KPITemplate {self.project_type}.{self.kpi_name}>'\n\n    @property\n    def display_name(self):\n        \"\"\"Nome leggibile del KPI\"\"\"\n        names = {\n            'margin_percentage': 'Margine Netto',\n            'utilization_rate': 'Utilization Rate',\n            'cost_per_hour': 'Costo per Ora',\n            'cost_revenue_ratio': 'Rapporto C/R'\n        }\n        return names.get(self.kpi_name, self.kpi_name)\n\nclass ProjectKPITarget(db.Model):\n    \"\"\"KPI target personalizzati per singolo progetto (Admin + Project Owner)\"\"\"\n    __tablename__ = 'project_kpi_targets'\n\n    id = db.Column(db.Integer, primary_key=True)\n    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)\n    kpi_name = db.Column(db.String(100), nullable=False)\n    target_value = db.Column(db.Float)  # Valore target personalizzato\n    warning_threshold = db.Column(db.Float)  # Soglia warning personalizzata\n    custom_description = db.Column(db.Text)  # Descrizione personalizzata\n    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)\n    created_at = db.Column(db.DateTime, default=datetime.utcnow)\n    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)\n\n    # Relationships\n    project = db.relationship('Project', backref='kpi_targets')\n    creator = db.relationship('User', backref='created_kpi_targets')\n\n    def __repr__(self):\n        return f'<ProjectKPITarget {self.project_id}.{self.kpi_name}: {self.target_value}>'\n\n    @property\n    def display_name(self):\n        \"\"\"Nome leggibile del KPI\"\"\"\n        names = {\n            'margin_percentage': 'Margine Netto',\n            'utilization_rate': 'Utilization Rate',\n            'cost_per_hour': 'Costo per Ora',\n            'cost_revenue_ratio': 'Rapporto C/R'\n        }\n        return names.get(self.kpi_name, self.kpi_name)"}