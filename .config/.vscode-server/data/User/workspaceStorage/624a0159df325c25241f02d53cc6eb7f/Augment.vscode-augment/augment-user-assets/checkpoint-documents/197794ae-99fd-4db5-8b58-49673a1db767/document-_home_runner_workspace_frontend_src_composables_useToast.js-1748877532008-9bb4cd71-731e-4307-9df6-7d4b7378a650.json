{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/composables/useToast.js"}, "originalCode": "import { ref, createApp } from 'vue'\nimport Toast from '@/components/ui/Toast.vue'\n\nconst toasts = ref([])\n\nexport function useToast() {\n  const showToast = (options) => {\n    const {\n      type = 'success',\n      title,\n      message = '',\n      duration = 3000,\n      closable = true\n    } = options\n\n    // Create a container for the toast\n    const container = document.createElement('div')\n    document.body.appendChild(container)\n\n    // Create Vue app instance for the toast\n    const app = createApp(Toast, {\n      type,\n      title,\n      message,\n      duration,\n      closable,\n      onClose: () => {\n        app.unmount()\n        document.body.removeChild(container)\n      }\n    })\n\n    app.mount(container)\n  }\n\n  const success = (title, message = '') => {\n    showToast({ type: 'success', title, message })\n  }\n\n  const error = (title, message = '') => {\n    showToast({ type: 'error', title, message })\n  }\n\n  const warning = (title, message = '') => {\n    showToast({ type: 'warning', title, message })\n  }\n\n  const info = (title, message = '') => {\n    showToast({ type: 'info', title, message })\n  }\n\n  return {\n    showToast,\n    success,\n    error,\n    warning,\n    info\n  }\n}\n", "modifiedCode": "import { ref, createApp } from 'vue'\nimport Toast from '@/components/ui/Toast.vue'\n\nconst toasts = ref([])\n\nexport function useToast() {\n  const showToast = (options) => {\n    const {\n      type = 'success',\n      title,\n      message = '',\n      duration = 3000,\n      closable = true\n    } = options\n\n    // Create a container for the toast\n    const container = document.createElement('div')\n    document.body.appendChild(container)\n\n    // Create Vue app instance for the toast\n    const app = createApp(Toast, {\n      type,\n      title,\n      message,\n      duration,\n      closable,\n      onClose: () => {\n        app.unmount()\n        document.body.removeChild(container)\n      }\n    })\n\n    app.mount(container)\n  }\n\n  const success = (title, message = '') => {\n    showToast({ type: 'success', title, message })\n  }\n\n  const error = (title, message = '') => {\n    showToast({ type: 'error', title, message })\n  }\n\n  const warning = (title, message = '') => {\n    showToast({ type: 'warning', title, message })\n  }\n\n  const info = (title, message = '') => {\n    showToast({ type: 'info', title, message })\n  }\n\n  return {\n    showToast,\n    success,\n    error,\n    warning,\n    info\n  }\n}\n"}