{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTimesheet.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <div class=\"flex items-center space-x-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Timesheet Dettaglio\n            </h3>\n            <!-- Navigazione mesi -->\n            <div class=\"flex items-center space-x-2\">\n              <button\n                @click=\"previousMonth\"\n                class=\"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n                </svg>\n              </button>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center\">\n                {{ currentMonth }}/{{ currentYear }}\n              </span>\n              <button\n                @click=\"nextMonth\"\n                class=\"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                </svg>\n              </button>\n            </div>\n\n            <!-- Filtro persona -->\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Vista:</span>\n              <select \n                v-model=\"selectedMember\"\n                @change=\"loadTimesheet\"\n                class=\"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n              >\n                <option value=\"\">Tutti i membri</option>\n                <option \n                  v-for=\"member in project?.team_members || []\" \n                  :key=\"member.id\" \n                  :value=\"member.id\"\n                >\n                  {{ member.first_name }} {{ member.last_name }}\n                </option>\n              </select>\n            </div>\n          </div>\n          <button\n            @click=\"showAddModal = true\"\n            class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Ore\n          </button>\n        </div>\n      </div>\n\n      <!-- Loading -->\n      <div v-if=\"timesheetLoading\" class=\"flex justify-center py-8\">\n        <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n\n      <!-- Error -->\n      <div v-if=\"error\" class=\"bg-red-50 border border-red-200 rounded-md p-4 m-6\">\n        <p class=\"text-red-600\">{{ error }}</p>\n      </div>\n\n      <!-- Vista mensile: Task per riga, Giorni per colonna -->\n      <div v-if=\"!timesheetLoading && timesheetData\" class=\"p-6\">\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead class=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th class=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700\">\n                  Task\n                </th>\n                <th \n                  v-for=\"day in daysInMonth\" \n                  :key=\"day\"\n                  class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]\"\n                  :class=\"{ 'bg-blue-50 dark:bg-blue-900': isToday(day) }\"\n                >\n                  {{ day }}\n                </th>\n                <th class=\"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700\">\n                  Tot\n                </th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              <tr v-for=\"task in timesheetData.tasks\" :key=\"task.id\">\n                <td class=\"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {{ task.name }}\n                  </div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {{ task.workers.length ? task.workers.join(', ') : 'Nessuno ha lavorato' }}\n                  </div>\n                </td>\n\n                <td\n                  v-for=\"day in daysInMonth\"\n                  :key=\"day\"\n                  class=\"px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\"\n                  :class=\"{ 'bg-blue-50 dark:bg-blue-900': isToday(day) }\"\n                  @click=\"editCell(task.id, day)\"\n                >\n                  <div v-if=\"task.daily_hours[day] > 0\" class=\"flex flex-col items-center\">\n                    <span class=\"text-xs font-medium text-primary-600 dark:text-primary-400\">\n                      {{ formatHours(task.daily_hours[day]) }}\n                    </span>\n                    <!-- Indicatore billing (se progetto ha contratto) -->\n                    <div v-if=\"project?.contract\" class=\"flex space-x-1 mt-1\">\n                      <div\n                        class=\"w-1.5 h-1.5 rounded-full\"\n                        :class=\"task.daily_billing && task.daily_billing[day] ? 'bg-green-500' : 'bg-gray-300'\"\n                        :title=\"task.daily_billing && task.daily_billing[day] ? 'Fatturabile' : 'Non fatturabile'\"\n                      ></div>\n                    </div>\n                  </div>\n                  <span v-else class=\"text-gray-300 dark:text-gray-600\">-</span>\n                </td>\n\n                <td class=\"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700\">\n                  <span class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {{ formatHours(task.total_hours) }}\n                  </span>\n                </td>\n              </tr>\n\n              <!-- Riga totali -->\n              <tr class=\"bg-gray-100 dark:bg-gray-600 font-medium\">\n                <td class=\"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600\">\n                  TOTALE GIORNALIERO\n                </td>\n                <td \n                  v-for=\"day in daysInMonth\" \n                  :key=\"day\"\n                  class=\"px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white\"\n                  :class=\"{ 'bg-blue-100 dark:bg-blue-800': isToday(day) }\"\n                >\n                  {{ formatHours(timesheetData.daily_totals[day] || 0) }}\n                </td>\n                <td class=\"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600\">\n                  {{ formatHours(timesheetData.grand_total) }}\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        <!-- Empty state -->\n        <div v-if=\"timesheetData.tasks.length === 0\" class=\"text-center py-8\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Nessun task trovato per questo progetto</p>\n        </div>\n\n        <!-- Legenda billing (solo se progetto ha contratto) -->\n        <div v-if=\"project?.contract && timesheetData.tasks.length > 0\" class=\"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md\">\n          <div class=\"flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-300\">\n            <span class=\"font-medium\">Legenda:</span>\n            <div class=\"flex items-center space-x-1\">\n              <div class=\"w-1.5 h-1.5 rounded-full bg-green-500\"></div>\n              <span>Fatturabile</span>\n            </div>\n            <div class=\"flex items-center space-x-1\">\n              <div class=\"w-1.5 h-1.5 rounded-full bg-gray-300\"></div>\n              <span>Non fatturabile</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi/Modifica Ore -->\n    <div v-if=\"showAddModal || showEditModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ showEditModal ? 'Modifica Ore' : 'Aggiungi Ore' }}\n          </h3>\n          \n          <form @submit.prevent=\"saveTimesheet\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Task</label>\n                <select \n                  v-model=\"formData.task_id\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona task</option>\n                  <option \n                    v-for=\"task in timesheetData?.tasks || []\" \n                    :key=\"task.id\" \n                    :value=\"task.id\"\n                  >\n                    {{ task.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Data</label>\n                <input \n                  v-model=\"formData.date\" \n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore</label>\n                <input \n                  v-model=\"formData.hours\" \n                  type=\"number\" \n                  step=\"0.25\"\n                  min=\"0\"\n                  max=\"24\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Descrizione</label>\n                <textarea\n                  v-model=\"formData.description\"\n                  rows=\"3\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                ></textarea>\n              </div>\n\n              <!-- Sezione Billing (solo se progetto ha contratto) -->\n              <div v-if=\"project?.contract\" class=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Informazioni Fatturazione</h4>\n\n                <div class=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Fatturabile\n                    </label>\n                    <select\n                      v-model=\"formData.billable\"\n                      class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                    >\n                      <option :value=\"true\">Sì - Fatturabile</option>\n                      <option :value=\"false\">No - Interno</option>\n                    </select>\n                  </div>\n\n                  <div v-if=\"formData.billable\">\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Tariffa (€/h)\n                    </label>\n                    <input\n                      v-model=\"formData.billing_rate\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      :placeholder=\"project.contract.hourly_rate\"\n                      class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                    >\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      Tariffa contrattuale: {{ formatCurrency(project.contract.hourly_rate) }}/h\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Salvataggio...' : (showEditModal ? 'Aggiorna' : 'Aggiungi') }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst authStore = useAuthStore()\n\n// State\nconst timesheetData = ref(null)\nconst timesheetLoading = ref(false)\nconst error = ref('')\nconst saving = ref(false)\n\n// Date navigation\nconst currentYear = ref(new Date().getFullYear())\nconst currentMonth = ref(new Date().getMonth() + 1)\nconst selectedMember = ref('')\n\n// Modal\nconst showAddModal = ref(false)\nconst showEditModal = ref(false)\nconst editingCell = ref(null)\n\n// Form data\nconst formData = ref({\n  task_id: '',\n  date: '',\n  hours: 0,\n  description: '',\n  billable: true,\n  billing_rate: null\n})\n\n// Computed\nconst daysInMonth = computed(() => {\n  if (!timesheetData.value) return []\n  return Array.from({ length: timesheetData.value.days_in_month }, (_, i) => i + 1)\n})\n\n// Methods\nconst loadTimesheet = async () => {\n  if (!props.project?.id) return\n  \n  timesheetLoading.value = true\n  error.value = ''\n  \n  try {\n    const params = new URLSearchParams({\n      year: currentYear.value.toString(),\n      month: currentMonth.value.toString()\n    })\n    \n    if (selectedMember.value) {\n      params.append('member_id', selectedMember.value.toString())\n    }\n\n    const response = await fetch(`/api/timesheets/project/${props.project.id}/monthly?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel caricamento del timesheet')\n    }\n\n    const result = await response.json()\n    timesheetData.value = result.data\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    timesheetLoading.value = false\n  }\n}\n\nconst saveTimesheet = async () => {\n  saving.value = true\n\n  try {\n    const data = {\n      ...formData.value,\n      project_id: props.project.id\n    }\n\n    // Check if we're editing an existing entry\n    const existingEntryId = await findExistingTimesheetEntry(\n      formData.value.task_id,\n      formData.value.date\n    )\n\n    let response\n    if (existingEntryId) {\n      // Update existing entry\n      response = await fetch(`/api/timesheets/${existingEntryId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': authStore.csrfToken\n        },\n        body: JSON.stringify(data)\n      })\n    } else {\n      // Create new entry\n      response = await fetch('/api/timesheets/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': authStore.csrfToken\n        },\n        body: JSON.stringify(data)\n      })\n    }\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio del timesheet')\n    }\n\n    await loadTimesheet()\n    closeModal()\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editCell = (taskId, day) => {\n  const task = timesheetData.value.tasks.find(t => t.id === taskId)\n  if (!task) return\n\n  editingCell.value = { taskId, day }\n\n  // Inizializza con tariffa contrattuale se disponibile\n  const defaultRate = props.project?.contract?.hourly_rate || null\n\n  formData.value = {\n    task_id: taskId,\n    date: `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`,\n    hours: task.daily_hours[day] || 0,\n    description: '',\n    billable: props.project?.contract ? true : false,\n    billing_rate: defaultRate\n  }\n\n  if (task.daily_hours[day] > 0) {\n    showEditModal.value = true\n  } else {\n    showAddModal.value = true\n  }\n}\n\nconst closeModal = () => {\n  showAddModal.value = false\n  showEditModal.value = false\n  editingCell.value = null\n  formData.value = {\n    task_id: '',\n    date: '',\n    hours: 0,\n    description: '',\n    billable: true,\n    billing_rate: null\n  }\n}\n\nconst previousMonth = () => {\n  if (currentMonth.value === 1) {\n    currentMonth.value = 12\n    currentYear.value--\n  } else {\n    currentMonth.value--\n  }\n  loadTimesheet()\n}\n\nconst nextMonth = () => {\n  if (currentMonth.value === 12) {\n    currentMonth.value = 1\n    currentYear.value++\n  } else {\n    currentMonth.value++\n  }\n  loadTimesheet()\n}\n\nconst isToday = (day) => {\n  const today = new Date()\n  return today.getFullYear() === currentYear.value &&\n         today.getMonth() + 1 === currentMonth.value &&\n         today.getDate() === day\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0'\n  return hours % 1 === 0 ? hours.toString() : hours.toFixed(2)\n}\n\nconst formatCurrency = (amount) => {\n  if (!amount) return '€0'\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n\n// Watchers\nwatch(() => props.project?.id, (newId) => {\n  if (newId) {\n    loadTimesheet()\n  }\n})\n\nwatch(selectedMember, () => {\n  loadTimesheet()\n})\n\n// Lifecycle\nonMounted(() => {\n  if (props.project?.id) {\n    loadTimesheet()\n  }\n})\n\n// Expose methods to parent\ndefineExpose({\n  refresh: loadTimesheet\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <div class=\"flex items-center space-x-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Timesheet Dettaglio\n            </h3>\n            <!-- Navigazione mesi -->\n            <div class=\"flex items-center space-x-2\">\n              <button\n                @click=\"previousMonth\"\n                class=\"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n                </svg>\n              </button>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center\">\n                {{ currentMonth }}/{{ currentYear }}\n              </span>\n              <button\n                @click=\"nextMonth\"\n                class=\"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700\"\n              >\n                <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n                </svg>\n              </button>\n            </div>\n\n            <!-- Filtro persona -->\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Vista:</span>\n              <select \n                v-model=\"selectedMember\"\n                @change=\"loadTimesheet\"\n                class=\"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n              >\n                <option value=\"\">Tutti i membri</option>\n                <option \n                  v-for=\"member in project?.team_members || []\" \n                  :key=\"member.id\" \n                  :value=\"member.id\"\n                >\n                  {{ member.first_name }} {{ member.last_name }}\n                </option>\n              </select>\n            </div>\n          </div>\n          <button\n            @click=\"showAddModal = true\"\n            class=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Ore\n          </button>\n        </div>\n      </div>\n\n      <!-- Loading -->\n      <div v-if=\"timesheetLoading\" class=\"flex justify-center py-8\">\n        <div class=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600\"></div>\n      </div>\n\n      <!-- Error -->\n      <div v-if=\"error\" class=\"bg-red-50 border border-red-200 rounded-md p-4 m-6\">\n        <p class=\"text-red-600\">{{ error }}</p>\n      </div>\n\n      <!-- Vista mensile: Task per riga, Giorni per colonna -->\n      <div v-if=\"!timesheetLoading && timesheetData\" class=\"p-6\">\n        <div class=\"overflow-x-auto\">\n          <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n            <thead class=\"bg-gray-50 dark:bg-gray-700\">\n              <tr>\n                <th class=\"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700\">\n                  Task\n                </th>\n                <th \n                  v-for=\"day in daysInMonth\" \n                  :key=\"day\"\n                  class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]\"\n                  :class=\"{ 'bg-blue-50 dark:bg-blue-900': isToday(day) }\"\n                >\n                  {{ day }}\n                </th>\n                <th class=\"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700\">\n                  Tot\n                </th>\n              </tr>\n            </thead>\n            <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n              <tr v-for=\"task in timesheetData.tasks\" :key=\"task.id\">\n                <td class=\"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700\">\n                  <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {{ task.name }}\n                  </div>\n                  <div class=\"text-xs text-gray-500 dark:text-gray-400\">\n                    {{ task.workers.length ? task.workers.join(', ') : 'Nessuno ha lavorato' }}\n                  </div>\n                </td>\n\n                <td\n                  v-for=\"day in daysInMonth\"\n                  :key=\"day\"\n                  class=\"px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\"\n                  :class=\"{ 'bg-blue-50 dark:bg-blue-900': isToday(day) }\"\n                  @click=\"editCell(task.id, day)\"\n                >\n                  <div v-if=\"task.daily_hours[day] > 0\" class=\"flex flex-col items-center\">\n                    <span class=\"text-xs font-medium text-primary-600 dark:text-primary-400\">\n                      {{ formatHours(task.daily_hours[day]) }}\n                    </span>\n                    <!-- Indicatore billing (se progetto ha contratto) -->\n                    <div v-if=\"project?.contract\" class=\"flex space-x-1 mt-1\">\n                      <div\n                        class=\"w-1.5 h-1.5 rounded-full\"\n                        :class=\"task.daily_billing && task.daily_billing[day] ? 'bg-green-500' : 'bg-gray-300'\"\n                        :title=\"task.daily_billing && task.daily_billing[day] ? 'Fatturabile' : 'Non fatturabile'\"\n                      ></div>\n                    </div>\n                  </div>\n                  <span v-else class=\"text-gray-300 dark:text-gray-600\">-</span>\n                </td>\n\n                <td class=\"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700\">\n                  <span class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {{ formatHours(task.total_hours) }}\n                  </span>\n                </td>\n              </tr>\n\n              <!-- Riga totali -->\n              <tr class=\"bg-gray-100 dark:bg-gray-600 font-medium\">\n                <td class=\"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600\">\n                  TOTALE GIORNALIERO\n                </td>\n                <td \n                  v-for=\"day in daysInMonth\" \n                  :key=\"day\"\n                  class=\"px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white\"\n                  :class=\"{ 'bg-blue-100 dark:bg-blue-800': isToday(day) }\"\n                >\n                  {{ formatHours(timesheetData.daily_totals[day] || 0) }}\n                </td>\n                <td class=\"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600\">\n                  {{ formatHours(timesheetData.grand_total) }}\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n\n        <!-- Empty state -->\n        <div v-if=\"timesheetData.tasks.length === 0\" class=\"text-center py-8\">\n          <p class=\"text-gray-500 dark:text-gray-400\">Nessun task trovato per questo progetto</p>\n        </div>\n\n        <!-- Legenda billing (solo se progetto ha contratto) -->\n        <div v-if=\"project?.contract && timesheetData.tasks.length > 0\" class=\"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md\">\n          <div class=\"flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-300\">\n            <span class=\"font-medium\">Legenda:</span>\n            <div class=\"flex items-center space-x-1\">\n              <div class=\"w-1.5 h-1.5 rounded-full bg-green-500\"></div>\n              <span>Fatturabile</span>\n            </div>\n            <div class=\"flex items-center space-x-1\">\n              <div class=\"w-1.5 h-1.5 rounded-full bg-gray-300\"></div>\n              <span>Non fatturabile</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi/Modifica Ore -->\n    <div v-if=\"showAddModal || showEditModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            {{ showEditModal ? 'Modifica Ore' : 'Aggiungi Ore' }}\n          </h3>\n          \n          <form @submit.prevent=\"saveTimesheet\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Task</label>\n                <select \n                  v-model=\"formData.task_id\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona task</option>\n                  <option \n                    v-for=\"task in timesheetData?.tasks || []\" \n                    :key=\"task.id\" \n                    :value=\"task.id\"\n                  >\n                    {{ task.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Data</label>\n                <input \n                  v-model=\"formData.date\" \n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore</label>\n                <input \n                  v-model=\"formData.hours\" \n                  type=\"number\" \n                  step=\"0.25\"\n                  min=\"0\"\n                  max=\"24\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Descrizione</label>\n                <textarea\n                  v-model=\"formData.description\"\n                  rows=\"3\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                ></textarea>\n              </div>\n\n              <!-- Sezione Billing (solo se progetto ha contratto) -->\n              <div v-if=\"project?.contract\" class=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Informazioni Fatturazione</h4>\n\n                <div class=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Fatturabile\n                    </label>\n                    <select\n                      v-model=\"formData.billable\"\n                      class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                    >\n                      <option :value=\"true\">Sì - Fatturabile</option>\n                      <option :value=\"false\">No - Interno</option>\n                    </select>\n                  </div>\n\n                  <div v-if=\"formData.billable\">\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Tariffa (€/h)\n                    </label>\n                    <input\n                      v-model=\"formData.billing_rate\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      :placeholder=\"project.contract.hourly_rate\"\n                      class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                    >\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      Tariffa contrattuale: {{ formatCurrency(project.contract.hourly_rate) }}/h\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button \n                type=\"button\" \n                @click=\"closeModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button \n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Salvataggio...' : (showEditModal ? 'Aggiorna' : 'Aggiungi') }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst props = defineProps({\n  project: {\n    type: Object,\n    default: null\n  },\n  loading: {\n    type: Boolean,\n    default: false\n  }\n})\n\nconst authStore = useAuthStore()\n\n// State\nconst timesheetData = ref(null)\nconst timesheetLoading = ref(false)\nconst error = ref('')\nconst saving = ref(false)\n\n// Date navigation\nconst currentYear = ref(new Date().getFullYear())\nconst currentMonth = ref(new Date().getMonth() + 1)\nconst selectedMember = ref('')\n\n// Modal\nconst showAddModal = ref(false)\nconst showEditModal = ref(false)\nconst editingCell = ref(null)\n\n// Form data\nconst formData = ref({\n  task_id: '',\n  date: '',\n  hours: 0,\n  description: '',\n  billable: true,\n  billing_rate: null\n})\n\n// Computed\nconst daysInMonth = computed(() => {\n  if (!timesheetData.value) return []\n  return Array.from({ length: timesheetData.value.days_in_month }, (_, i) => i + 1)\n})\n\n// Methods\nconst loadTimesheet = async () => {\n  if (!props.project?.id) return\n  \n  timesheetLoading.value = true\n  error.value = ''\n  \n  try {\n    const params = new URLSearchParams({\n      year: currentYear.value.toString(),\n      month: currentMonth.value.toString()\n    })\n    \n    if (selectedMember.value) {\n      params.append('member_id', selectedMember.value.toString())\n    }\n\n    const response = await fetch(`/api/timesheets/project/${props.project.id}/monthly?${params}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      throw new Error('Errore nel caricamento del timesheet')\n    }\n\n    const result = await response.json()\n    timesheetData.value = result.data\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    timesheetLoading.value = false\n  }\n}\n\nconst findExistingTimesheetEntry = async (taskId, date) => {\n  try {\n    // Get current user's timesheet entries for this project and date\n    const response = await fetch(`/api/timesheets/project/${props.project.id}?start_date=${date}&end_date=${date}&task_id=${taskId}`, {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (!response.ok) {\n      return null\n    }\n\n    const result = await response.json()\n    const entries = result.data || []\n\n    // Find entry for current user on this date and task\n    const existingEntry = entries.find(entry =>\n      entry.user_id === authStore.user.id &&\n      entry.task_id === parseInt(taskId) &&\n      entry.date === date\n    )\n\n    return existingEntry ? existingEntry.id : null\n  } catch (err) {\n    console.error('Error finding existing timesheet entry:', err)\n    return null\n  }\n}\n\nconst saveTimesheet = async () => {\n  saving.value = true\n\n  try {\n    const data = {\n      ...formData.value,\n      project_id: props.project.id\n    }\n\n    // Check if we're editing an existing entry\n    const existingEntryId = await findExistingTimesheetEntry(\n      formData.value.task_id,\n      formData.value.date\n    )\n\n    let response\n    if (existingEntryId) {\n      // Update existing entry\n      response = await fetch(`/api/timesheets/${existingEntryId}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': authStore.csrfToken\n        },\n        body: JSON.stringify(data)\n      })\n    } else {\n      // Create new entry\n      response = await fetch('/api/timesheets/', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          'X-CSRFToken': authStore.csrfToken\n        },\n        body: JSON.stringify(data)\n      })\n    }\n\n    if (!response.ok) {\n      throw new Error('Errore nel salvataggio del timesheet')\n    }\n\n    await loadTimesheet()\n    closeModal()\n  } catch (err) {\n    error.value = err.message\n  } finally {\n    saving.value = false\n  }\n}\n\nconst editCell = (taskId, day) => {\n  const task = timesheetData.value.tasks.find(t => t.id === taskId)\n  if (!task) return\n\n  editingCell.value = { taskId, day }\n\n  // Inizializza con tariffa contrattuale se disponibile\n  const defaultRate = props.project?.contract?.hourly_rate || null\n\n  formData.value = {\n    task_id: taskId,\n    date: `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`,\n    hours: task.daily_hours[day] || 0,\n    description: '',\n    billable: props.project?.contract ? true : false,\n    billing_rate: defaultRate\n  }\n\n  if (task.daily_hours[day] > 0) {\n    showEditModal.value = true\n  } else {\n    showAddModal.value = true\n  }\n}\n\nconst closeModal = () => {\n  showAddModal.value = false\n  showEditModal.value = false\n  editingCell.value = null\n  formData.value = {\n    task_id: '',\n    date: '',\n    hours: 0,\n    description: '',\n    billable: true,\n    billing_rate: null\n  }\n}\n\nconst previousMonth = () => {\n  if (currentMonth.value === 1) {\n    currentMonth.value = 12\n    currentYear.value--\n  } else {\n    currentMonth.value--\n  }\n  loadTimesheet()\n}\n\nconst nextMonth = () => {\n  if (currentMonth.value === 12) {\n    currentMonth.value = 1\n    currentYear.value++\n  } else {\n    currentMonth.value++\n  }\n  loadTimesheet()\n}\n\nconst isToday = (day) => {\n  const today = new Date()\n  return today.getFullYear() === currentYear.value &&\n         today.getMonth() + 1 === currentMonth.value &&\n         today.getDate() === day\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0'\n  return hours % 1 === 0 ? hours.toString() : hours.toFixed(2)\n}\n\nconst formatCurrency = (amount) => {\n  if (!amount) return '€0'\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount)\n}\n\n// Watchers\nwatch(() => props.project?.id, (newId) => {\n  if (newId) {\n    loadTimesheet()\n  }\n})\n\nwatch(selectedMember, () => {\n  loadTimesheet()\n})\n\n// Lifecycle\nonMounted(() => {\n  if (props.project?.id) {\n    loadTimesheet()\n  }\n})\n\n// Expose methods to parent\ndefineExpose({\n  refresh: loadTimesheet\n})\n</script>"}