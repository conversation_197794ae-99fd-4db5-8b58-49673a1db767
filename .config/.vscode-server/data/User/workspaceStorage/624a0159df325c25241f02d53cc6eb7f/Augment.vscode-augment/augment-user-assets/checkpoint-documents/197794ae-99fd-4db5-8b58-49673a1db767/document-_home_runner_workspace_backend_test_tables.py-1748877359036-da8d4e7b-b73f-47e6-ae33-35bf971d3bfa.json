{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/test_tables.py"}, "modifiedCode": "#!/usr/bin/env python3\n\"\"\"\nTest script per verificare che l'app non ricrei tabelle duplicate\n\"\"\"\n\nfrom app import create_app\nfrom extensions import db\nimport sqlalchemy as sa\n\ndef test_table_creation():\n    \"\"\"Testa che l'app non ricrei tabelle duplicate\"\"\"\n    print(\"🔍 Test creazione tabelle...\")\n    \n    app = create_app()\n    \n    with app.app_context():\n        print(\"✅ App avviata correttamente\")\n        \n        # Ispeziona tabelle esistenti\n        inspector = sa.inspect(db.engine)\n        all_tables = inspector.get_table_names()\n        \n        # Filtra tabelle timesheet e time_off\n        timesheet_tables = [t for t in all_tables if 'timesheet' in t]\n        timeoff_tables = [t for t in all_tables if 'time_off' in t]\n        \n        print(f\"\\n📋 Tabelle timesheet trovate: {sorted(timesheet_tables)}\")\n        print(f\"📋 Tabelle time_off trovate: {sorted(timeoff_tables)}\")\n        \n        # Verifica che non ci siano duplicati\n        expected_timesheet = ['timesheet']  # O timesheet_entries se rinominata\n        expected_timeoff = ['time_off_requests']\n        \n        print(f\"\\n✅ Tabelle timesheet attese: {expected_timesheet}\")\n        print(f\"✅ Tabelle time_off attese: {expected_timeoff}\")\n        \n        # Controlla duplicati\n        timesheet_duplicates = [t for t in timesheet_tables if t not in expected_timesheet and t != 'timesheet_entries']\n        timeoff_duplicates = [t for t in timeoff_tables if t not in expected_timeoff]\n        \n        if timesheet_duplicates:\n            print(f\"❌ DUPLICATI timesheet trovati: {timesheet_duplicates}\")\n        else:\n            print(\"✅ Nessun duplicato timesheet\")\n            \n        if timeoff_duplicates:\n            print(f\"❌ DUPLICATI time_off trovati: {timeoff_duplicates}\")\n        else:\n            print(\"✅ Nessun duplicato time_off\")\n        \n        # Verifica modelli\n        print(f\"\\n🔍 Verifica modelli...\")\n        from models import TimesheetEntry, TimeOffRequest, MonthlyTimesheet\n        \n        print(f\"TimesheetEntry.__tablename__: {getattr(TimesheetEntry, '__tablename__', 'DEFAULT')}\")\n        print(f\"TimeOffRequest.__tablename__: {getattr(TimeOffRequest, '__tablename__', 'DEFAULT')}\")\n        print(f\"MonthlyTimesheet.__tablename__: {getattr(MonthlyTimesheet, '__tablename__', 'DEFAULT')}\")\n        \n        return len(timesheet_duplicates) == 0 and len(timeoff_duplicates) == 0\n\nif __name__ == \"__main__\":\n    success = test_table_creation()\n    if success:\n        print(\"\\n🎉 TEST PASSATO: Nessuna tabella duplicata creata!\")\n    else:\n        print(\"\\n❌ TEST FALLITO: Trovate tabelle duplicate!\")\n    \n    exit(0 if success else 1)\n"}