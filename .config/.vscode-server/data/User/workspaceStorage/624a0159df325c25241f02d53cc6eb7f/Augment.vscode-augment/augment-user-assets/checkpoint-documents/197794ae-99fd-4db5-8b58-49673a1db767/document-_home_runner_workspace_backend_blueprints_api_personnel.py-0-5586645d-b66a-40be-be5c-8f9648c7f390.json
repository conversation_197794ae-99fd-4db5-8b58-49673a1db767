{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel.py"}, "originalCode": "\"\"\"\nAPI endpoints for personnel management.\nProvides REST API for users, departments, skills, and organization chart.\n\"\"\"\n\nimport os\nimport json\nfrom datetime import datetime\nfrom flask import Blueprint, request, jsonify, make_response, current_app, send_file\nfrom flask_login import current_user\nfrom sqlalchemy import or_, and_, func\nfrom sqlalchemy.orm import joinedload\nfrom werkzeug.utils import secure_filename\n\nfrom extensions import db\nfrom models import User, Department, Skill, UserSkill, UserProfile\nfrom utils.api_utils import (\n    api_response, get_pagination_params, api_permission_required,\n    handle_api_error, api_login_required\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA, PERMISSION_MANAGE_USERS\n)\nfrom utils.cv_parser import extract_text_from_cv, is_valid_cv_file, get_file_size_mb\nfrom services.ai import extract_skills_from_cv\n\n# Create blueprint\napi_personnel = Blueprint('api_personnel', __name__, url_prefix='/personnel')\n\n@api_personnel.route('', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_personnel():\n    \"\"\"\n    Get list of personnel (alias for /users endpoint).\n    This is a convenience endpoint that redirects to the users endpoint.\n    \"\"\"\n    return get_users()\n\n@api_personnel.route('/users', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_users():\n    \"\"\"\n    Get list of users with filtering, pagination, and search.\n\n    Query Parameters:\n    - page: Page number (default: 1)\n    - per_page: Items per page (default: 20)\n    - search: Search in name, username, email\n    - department_id: Filter by department\n    - role: Filter by role\n    - is_active: Filter by active status\n    - skills: Filter by skills (comma-separated skill IDs)\n    \"\"\"\n    try:\n        # Get pagination parameters\n        page, per_page = get_pagination_params()\n\n        # Build base query\n        query = User.query.options(\n            joinedload(User.department_obj),\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.profile)\n        )\n\n        # Apply filters\n        search = request.args.get('search', '').strip()\n        if search:\n            search_filter = or_(\n                User.first_name.ilike(f'%{search}%'),\n                User.last_name.ilike(f'%{search}%'),\n                User.username.ilike(f'%{search}%'),\n                User.email.ilike(f'%{search}%')\n            )\n            query = query.filter(search_filter)\n\n        # Department filter\n        department_id = request.args.get('department_id', type=int)\n        if department_id:\n            query = query.filter(User.department_id == department_id)\n\n        # Role filter\n        role = request.args.get('role')\n        if role:\n            query = query.filter(User.role == role)\n\n        # Active status filter\n        is_active = request.args.get('is_active')\n        if is_active is not None:\n            is_active_bool = is_active.lower() in ['true', '1', 'yes']\n            query = query.filter(User.is_active == is_active_bool)\n\n        # Skills filter\n        skills = request.args.get('skills')\n        if skills:\n            skill_ids = [int(id.strip()) for id in skills.split(',') if id.strip().isdigit()]\n            if skill_ids:\n                query = query.join(UserSkill).filter(UserSkill.skill_id.in_(skill_ids))\n\n        # Order by\n        order_by = request.args.get('order_by', 'last_name')\n        order_dir = request.args.get('order_dir', 'asc')\n\n        if hasattr(User, order_by):\n            order_column = getattr(User, order_by)\n            if order_dir.lower() == 'desc':\n                order_column = order_column.desc()\n            query = query.order_by(order_column)\n        else:\n            query = query.order_by(User.last_name.asc())\n\n        # Execute pagination\n        pagination = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        # Serialize users\n        users_data = []\n        for user in pagination.items:\n            user_data = {\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'first_name': user.first_name,\n                'last_name': user.last_name,\n                'full_name': user.full_name,\n                'role': user.role,\n                'department_id': user.department_id,\n                'department_name': user.department_obj.name if user.department_obj else None,\n                'position': user.position,\n                'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n                'phone': user.phone,\n                'profile_image': user.profile_image,\n                'is_active': user.is_active,\n                'last_login': user.last_login.isoformat() if user.last_login else None,\n                'skills': [\n                    {\n                        'id': us.skill.id,\n                        'name': us.skill.name,\n                        'category': us.skill.category,\n                        'proficiency_level': us.proficiency_level,\n                        'years_experience': us.years_experience\n                    }\n                    for us in user.detailed_skills\n                ] if user.detailed_skills else [],\n                'profile_completion': user.profile.profile_completion if user.profile else 0.0\n            }\n            users_data.append(user_data)\n\n        return api_response(\n            data={\n                'users': users_data,\n                'pagination': {\n                    'page': pagination.page,\n                    'pages': pagination.pages,\n                    'per_page': pagination.per_page,\n                    'total': pagination.total,\n                    'has_next': pagination.has_next,\n                    'has_prev': pagination.has_prev\n                }\n            },\n            message=f\"Retrieved {len(users_data)} users\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_user(user_id):\n    \"\"\"\n    Get detailed information about a specific user.\n    \"\"\"\n    try:\n        user = User.query.options(\n            joinedload(User.department_obj),\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.profile),\n            joinedload(User.projects)\n        ).get_or_404(user_id)\n\n        # Ricalcola il completamento del profilo se esiste\n        if user.profile:\n            user.profile.calculate_completion()\n            db.session.commit()\n\n        # Serialize user with full details\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'full_name': user.full_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'department': {\n                'id': user.department_obj.id,\n                'name': user.department_obj.name,\n                'description': user.department_obj.description\n            } if user.department_obj else None,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'profile_image': user.profile_image,\n            'bio': user.bio,\n            'is_active': user.is_active,\n            'dark_mode': user.dark_mode,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'last_login': user.last_login.isoformat() if user.last_login else None,\n            'skills': [\n                {\n                    'id': us.skill.id,\n                    'name': us.skill.name,\n                    'category': us.skill.category,\n                    'description': us.skill.description,\n                    'proficiency_level': us.proficiency_level,\n                    'years_experience': us.years_experience,\n                    'certified': us.is_certified,\n                    'last_used': us.certification_date.isoformat() if us.certification_date else None\n                }\n                for us in user.detailed_skills\n            ] if user.detailed_skills else [],\n            'projects': [\n                {\n                    'id': project.id,\n                    'name': project.name,\n                    'status': project.status,\n                    'role': 'team_member'  # Could be enhanced with actual role from project_team table\n                }\n                for project in user.projects\n            ] if user.projects else [],\n            'profile': {\n                'employee_id': user.profile.employee_id,\n                'job_title': user.profile.job_title,\n                'birth_date': user.profile.birth_date.isoformat() if user.profile.birth_date else None,\n                'address': user.profile.address,\n                'emergency_contact_name': user.profile.emergency_contact_name,\n                'emergency_contact_phone': user.profile.emergency_contact_phone,\n                'emergency_contact_relationship': user.profile.emergency_contact_relationship,\n                'employment_type': user.profile.employment_type,\n                'work_location': user.profile.work_location,\n                'weekly_hours': user.profile.weekly_hours,\n                'daily_hours': user.profile.daily_hours,\n                'current_cv_path': user.profile.current_cv_path,\n                'cv_last_updated': user.profile.cv_last_updated.isoformat() if user.profile.cv_last_updated else None,\n                'profile_completion': user.profile.profile_completion,\n                'notes': user.profile.notes if current_user.role in ['admin', 'human_resources'] else None,\n                'created_at': user.profile.created_at.isoformat() if user.profile.created_at else None,\n                'updated_at': user.profile.updated_at.isoformat() if user.profile.updated_at else None\n            } if user.profile else None\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=f\"Retrieved user {user.full_name}\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)\ndef update_user_profile(user_id):\n    \"\"\"\n    Update user profile information.\n    Allows updating both User and UserProfile fields.\n    \"\"\"\n    try:\n        # Check if user can edit this profile\n        user = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/HR\n        if user.id != current_user.id and not current_user.role in ['admin', 'human_resources']:\n            return api_response(\n                success=False,\n                message=\"Non hai il permesso di modificare questo profilo\",\n                status_code=403\n            )\n\n        data = request.get_json()\n        if not data:\n            return api_response(\n                success=False,\n                message=\"Nessun dato fornito\",\n                status_code=400\n            )\n\n        # Update User fields\n        user_fields = ['first_name', 'last_name', 'phone', 'bio', 'position']\n        for field in user_fields:\n            if field in data:\n                setattr(user, field, data[field])\n\n        # Get or create UserProfile\n        profile = user.profile\n        if not profile:\n            profile = UserProfile(user_id=user.id)\n            db.session.add(profile)\n\n        # Update UserProfile fields\n        profile_fields = [\n            'employee_id', 'job_title', 'birth_date', 'address',\n            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',\n            'employment_type', 'work_location', 'weekly_hours', 'daily_hours'\n        ]\n\n        for field in profile_fields:\n            if field in data:\n                if field == 'birth_date' and data[field]:\n                    # Handle date conversion\n                    from datetime import datetime\n                    profile.birth_date = datetime.strptime(data[field], '%Y-%m-%d').date()\n                else:\n                    setattr(profile, field, data[field])\n\n        # Recalculate profile completion\n        profile.calculate_completion()\n\n        # Save changes\n        db.session.commit()\n\n        # Return updated user data\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'full_name': user.full_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'department': {\n                'id': user.department_obj.id,\n                'name': user.department_obj.name,\n                'description': user.department_obj.description\n            } if user.department_obj else None,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'profile_image': user.profile_image,\n            'bio': user.bio,\n            'is_active': user.is_active,\n            'profile': {\n                'employee_id': profile.employee_id,\n                'job_title': profile.job_title,\n                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,\n                'address': profile.address,\n                'emergency_contact_name': profile.emergency_contact_name,\n                'emergency_contact_phone': profile.emergency_contact_phone,\n                'emergency_contact_relationship': profile.emergency_contact_relationship,\n                'employment_type': profile.employment_type,\n                'work_location': profile.work_location,\n                'weekly_hours': profile.weekly_hours,\n                'daily_hours': profile.daily_hours,\n                'profile_completion': profile.profile_completion\n            }\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=\"Profilo aggiornato con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments', methods=['GET'])\n@api_login_required\ndef get_departments():\n    \"\"\"\n    Get list of departments with organization chart data.\n    \"\"\"\n    try:\n        departments = Department.query.options(\n            joinedload(Department.manager)\n        ).all()\n\n        departments_data = []\n        for dept in departments:\n            dept_data = {\n                'id': dept.id,\n                'name': dept.name,\n                'description': dept.description,\n                'manager_id': dept.manager_id,\n                'manager': {\n                    'id': dept.manager.id,\n                    'full_name': dept.manager.full_name,\n                    'email': dept.manager.email\n                } if dept.manager else None,\n                'user_count': dept.employees.count() if dept.employees else 0,\n                'users': [\n                    {\n                        'id': user.id,\n                        'full_name': user.full_name,\n                        'position': user.position,\n                        'email': user.email,\n                        'is_active': user.is_active\n                    }\n                    for user in dept.employees\n                ] if dept.employees else [],\n                'created_at': dept.created_at.isoformat() if dept.created_at else None\n            }\n            departments_data.append(dept_data)\n\n        return api_response(\n            data={'departments': departments_data},\n            message=f\"Retrieved {len(departments_data)} departments\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/orgchart', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_orgchart():\n    \"\"\"\n    Get organization chart data with hierarchical structure.\n    \"\"\"\n    try:\n        # Build hierarchical department tree\n        def build_department_tree(parent_id=None):\n            tree = []\n            departments = Department.query.filter_by(\n                parent_id=parent_id,\n                is_active=True\n            ).order_by(Department.name).all()\n\n            for dept in departments:\n                # Get active employees for this department\n                employees = User.query.filter_by(\n                    department_id=dept.id,\n                    is_active=True\n                ).order_by(User.first_name, User.last_name).all()\n\n                # Build employee data\n                employees_data = []\n                for emp in employees:\n                    emp_data = {\n                        'id': emp.id,\n                        'full_name': emp.full_name,\n                        'first_name': emp.first_name,\n                        'last_name': emp.last_name,\n                        'email': emp.email,\n                        'position': emp.position,\n                        'role': emp.role,\n                        'hire_date': emp.hire_date.isoformat() if emp.hire_date else None,\n                        'profile_image': emp.profile_image,\n                        'is_manager': emp.id == dept.manager_id\n                    }\n                    employees_data.append(emp_data)\n\n                # Build department node\n                dept_data = {\n                    'id': dept.id,\n                    'name': dept.name,\n                    'description': dept.description,\n                    'manager_id': dept.manager_id,\n                    'manager': {\n                        'id': dept.manager.id,\n                        'full_name': dept.manager.full_name,\n                        'email': dept.manager.email,\n                        'position': dept.manager.position,\n                        'profile_image': dept.manager.profile_image\n                    } if dept.manager else None,\n                    'employees': employees_data,\n                    'employee_count': len(employees_data),\n                    'budget': dept.budget,\n                    'subdepartments': build_department_tree(dept.id)\n                }\n                tree.append(dept_data)\n\n            return tree\n\n        # Get root departments (no parent)\n        orgchart_data = build_department_tree()\n\n        # Calculate total statistics\n        total_employees = User.query.filter_by(is_active=True).count()\n        total_departments = Department.query.filter_by(is_active=True).count()\n        total_managers = Department.query.filter(\n            Department.manager_id.isnot(None),\n            Department.is_active == True\n        ).count()\n\n        stats = {\n            'total_employees': total_employees,\n            'total_departments': total_departments,\n            'total_managers': total_managers\n        }\n\n        return api_response(\n            data={\n                'orgchart': orgchart_data,\n                'stats': stats\n            },\n            message=f\"Retrieved organization chart with {total_departments} departments\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef create_department():\n    \"\"\"\n    Create a new department.\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        # Validate required fields\n        if not data.get('name'):\n            return api_response(\n                success=False,\n                message='Nome dipartimento richiesto',\n                status_code=400\n            )\n\n        # Check if department name already exists\n        existing_dept = Department.query.filter_by(name=data['name']).first()\n        if existing_dept:\n            return api_response(\n                success=False,\n                message='Un dipartimento con questo nome esiste già',\n                status_code=400\n            )\n\n        # Create new department\n        department = Department(\n            name=data['name'],\n            description=data.get('description', ''),\n            manager_id=data.get('manager_id'),\n            parent_id=data.get('parent_id'),\n            budget=data.get('budget', 0.0)\n        )\n\n        db.session.add(department)\n        db.session.commit()\n\n        # Return created department data\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'user_count': 0,\n            'created_at': department.created_at.isoformat()\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Dipartimento '{department.name}' creato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_department(dept_id):\n    \"\"\"\n    Get detailed information about a specific department.\n    \"\"\"\n    try:\n        department = Department.query.options(\n            joinedload(Department.manager)\n        ).get_or_404(dept_id)\n\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'manager': {\n                'id': department.manager.id,\n                'full_name': department.manager.full_name,\n                'email': department.manager.email,\n                'position': department.manager.position\n            } if department.manager else None,\n            'parent': {\n                'id': department.parent.id,\n                'name': department.parent.name\n            } if department.parent_id and hasattr(department, 'parent') and department.parent else None,\n            'employees': [\n                {\n                    'id': emp.id,\n                    'full_name': emp.full_name,\n                    'email': emp.email,\n                    'position': emp.position,\n                    'is_active': emp.is_active,\n                    'hire_date': emp.hire_date.isoformat() if emp.hire_date else None\n                }\n                for emp in department.employees if emp.is_active\n            ],\n            'subdepartments': [\n                {\n                    'id': sub.id,\n                    'name': sub.name,\n                    'employee_count': getattr(sub, 'employee_count', 0)\n                }\n                for sub in department.subdepartments if getattr(sub, 'is_active', True)\n            ],\n            'employee_count': len([emp for emp in department.employees if emp.is_active]),\n            'created_at': department.created_at.isoformat() if department.created_at else None,\n            'updated_at': department.updated_at.isoformat() if department.updated_at else None\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Retrieved department {department.name}\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef update_department(dept_id):\n    \"\"\"\n    Update an existing department.\n    \"\"\"\n    try:\n        department = Department.query.get_or_404(dept_id)\n        data = request.get_json()\n\n        # Validate name if provided\n        if 'name' in data and data['name']:\n            # Check if name already exists (excluding current department)\n            existing_dept = Department.query.filter(\n                Department.name == data['name'],\n                Department.id != dept_id\n            ).first()\n            if existing_dept:\n                return api_response(\n                    success=False,\n                    message='Un dipartimento con questo nome esiste già',\n                    status_code=400\n                )\n            department.name = data['name']\n\n        # Update other fields\n        if 'description' in data:\n            department.description = data['description']\n        if 'manager_id' in data:\n            department.manager_id = data['manager_id']\n        if 'parent_id' in data:\n            department.parent_id = data['parent_id']\n        if 'budget' in data:\n            department.budget = data['budget']\n\n        db.session.commit()\n\n        # Return updated department data\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'employee_count': len([emp for emp in department.employees if emp.is_active]),\n            'updated_at': department.updated_at.isoformat() if department.updated_at else None\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Dipartimento '{department.name}' aggiornato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['DELETE'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef delete_department(dept_id):\n    \"\"\"\n    Delete a department (soft delete by setting is_active=False).\n    \"\"\"\n    try:\n        department = Department.query.get_or_404(dept_id)\n\n        # Check if department has employees\n        active_employees = len([emp for emp in department.employees if emp.is_active])\n        if active_employees > 0:\n            return api_response(\n                success=False,\n                message='Impossibile eliminare un dipartimento con dipendenti assegnati',\n                status_code=400\n            )\n\n        # Check if department has subdepartments\n        active_subdepartments = len([sub for sub in department.subdepartments if getattr(sub, 'is_active', True)])\n        if active_subdepartments > 0:\n            return api_response(\n                success=False,\n                message='Impossibile eliminare un dipartimento con sotto-dipartimenti',\n                status_code=400\n            )\n\n        # Soft delete\n        department.is_active = False\n        db.session.commit()\n\n        return api_response(\n            message=f\"Dipartimento '{department.name}' eliminato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/skills', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_skills():\n    \"\"\"\n    Get list of skills with usage statistics.\n    \"\"\"\n    try:\n        # Get skills with user count\n        skills_query = db.session.query(\n            Skill,\n            func.count(UserSkill.user_id).label('user_count')\n        ).outerjoin(UserSkill).group_by(Skill.id)\n\n        category = request.args.get('category')\n        if category:\n            skills_query = skills_query.filter(Skill.category == category)\n\n        search = request.args.get('search', '').strip()\n        if search:\n            skills_query = skills_query.filter(\n                or_(\n                    Skill.name.ilike(f'%{search}%'),\n                    Skill.description.ilike(f'%{search}%')\n                )\n            )\n\n        skills_data = []\n        for skill, user_count in skills_query.all():\n            skill_data = {\n                'id': skill.id,\n                'name': skill.name,\n                'category': skill.category,\n                'description': skill.description,\n                'user_count': user_count,\n                'users': [\n                    {\n                        'id': us.user.id,\n                        'full_name': us.user.full_name,\n                        'proficiency_level': us.proficiency_level,\n                        'years_experience': us.years_experience\n                    }\n                    for us in skill.user_skills\n                ] if hasattr(skill, 'user_skills') else []\n            }\n            skills_data.append(skill_data)\n\n        # Get categories for filter\n        categories = db.session.query(Skill.category).distinct().all()\n        categories_list = [cat[0] for cat in categories if cat[0]]\n\n        return api_response(\n            data={\n                'skills': skills_data,\n                'categories': categories_list\n            },\n            message=f\"Retrieved {len(skills_data)} skills\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/skills-matrix', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_skills_matrix():\n    \"\"\"\n    Get skills matrix data - users vs skills with proficiency levels.\n    \"\"\"\n    try:\n        # Get filter parameters\n        department_id = request.args.get('department_id')\n        category = request.args.get('category')\n        skill_ids = request.args.get('skill_ids', '').split(',') if request.args.get('skill_ids') else []\n        min_level = request.args.get('min_level', type=int)\n        max_level = request.args.get('max_level', type=int)\n\n        # Build users query with filters\n        users_query = User.query.filter(User.is_active == True)\n\n        if department_id:\n            users_query = users_query.filter(User.department_id == department_id)\n\n        # Get users with their skills\n        users = users_query.options(\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.department_obj)\n        ).order_by(User.first_name, User.last_name).all()\n\n        # Build skills query with filters\n        skills_query = Skill.query\n\n        if category:\n            skills_query = skills_query.filter(Skill.category == category)\n\n        if skill_ids and skill_ids != ['']:\n            skills_query = skills_query.filter(Skill.id.in_(skill_ids))\n\n        skills = skills_query.order_by(Skill.category, Skill.name).all()\n\n        # Build matrix data\n        matrix_data = []\n\n        for user in users:\n            # Create user skills lookup\n            user_skills_dict = {\n                us.skill_id: {\n                    'proficiency_level': us.proficiency_level,\n                    'years_experience': us.years_experience,\n                    'is_certified': us.is_certified,\n                    'certification_name': us.certification_name,\n                    'self_assessed': us.self_assessed,\n                    'manager_assessed': us.manager_assessed\n                }\n                for us in user.detailed_skills\n            }\n\n            # Build skills data for this user\n            user_skills = []\n            for skill in skills:\n                skill_data = user_skills_dict.get(skill.id, {\n                    'proficiency_level': 0,\n                    'years_experience': 0,\n                    'is_certified': False,\n                    'certification_name': None,\n                    'self_assessed': False,\n                    'manager_assessed': False\n                })\n\n                # Apply level filters\n                if min_level and skill_data['proficiency_level'] < min_level:\n                    continue\n                if max_level and skill_data['proficiency_level'] > max_level:\n                    continue\n\n                user_skills.append({\n                    'skill_id': skill.id,\n                    'skill_name': skill.name,\n                    'skill_category': skill.category,\n                    **skill_data\n                })\n\n            # Add user to matrix if they have skills matching filters\n            if not min_level and not max_level or user_skills:\n                matrix_data.append({\n                    'user_id': user.id,\n                    'full_name': user.full_name,\n                    'first_name': user.first_name,\n                    'last_name': user.last_name,\n                    'email': user.email,\n                    'position': user.position,\n                    'department': user.department_obj.name if user.department_obj else None,\n                    'department_id': user.department_id,\n                    'profile_image': user.profile_image,\n                    'skills': user_skills\n                })\n\n        # Build skills summary\n        skills_summary = []\n        for skill in skills:\n            # Count users by proficiency level\n            level_counts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}\n            total_users = 0\n            avg_level = 0\n\n            for user_data in matrix_data:\n                for user_skill in user_data['skills']:\n                    if user_skill['skill_id'] == skill.id and user_skill['proficiency_level'] > 0:\n                        level_counts[user_skill['proficiency_level']] += 1\n                        total_users += 1\n                        avg_level += user_skill['proficiency_level']\n\n            avg_level = round(avg_level / total_users, 1) if total_users > 0 else 0\n\n            skills_summary.append({\n                'id': skill.id,\n                'name': skill.name,\n                'category': skill.category,\n                'description': skill.description,\n                'total_users': total_users,\n                'avg_level': avg_level,\n                'level_distribution': level_counts\n            })\n\n        # Calculate statistics\n        stats = {\n            'total_users': len(matrix_data),\n            'total_skills': len(skills),\n            'total_skill_assignments': sum(len(user['skills']) for user in matrix_data),\n            'avg_skills_per_user': round(\n                sum(len([s for s in user['skills'] if s['proficiency_level'] > 0]) for user in matrix_data) / len(matrix_data), 1\n            ) if matrix_data else 0,\n            'skill_coverage': {\n                'beginner': sum(1 for skill in skills_summary if skill['avg_level'] >= 1 and skill['avg_level'] < 2),\n                'intermediate': sum(1 for skill in skills_summary if skill['avg_level'] >= 2 and skill['avg_level'] < 4),\n                'advanced': sum(1 for skill in skills_summary if skill['avg_level'] >= 4),\n                'no_coverage': sum(1 for skill in skills_summary if skill['total_users'] == 0)\n            }\n        }\n\n        # Get available departments and categories for filters\n        departments = Department.query.filter(Department.is_active == True).order_by(Department.name).all()\n        categories = db.session.query(Skill.category).distinct().filter(Skill.category.isnot(None)).all()\n\n        return api_response(\n            data={\n                'matrix': matrix_data,\n                'skills_summary': skills_summary,\n                'stats': stats,\n                'filters': {\n                    'departments': [{'id': d.id, 'name': d.name} for d in departments],\n                    'categories': [cat[0] for cat in categories if cat[0]]\n                }\n            },\n            message=f\"Retrieved skills matrix for {len(matrix_data)} users and {len(skills)} skills\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/skills', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef create_skill():\n    \"\"\"\n    Create a new skill.\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        # Validate required fields\n        if not data.get('name'):\n            return api_response(\n                success=False,\n                message='Nome competenza richiesto',\n                status_code=400\n            )\n\n        # Check if skill name already exists\n        existing_skill = Skill.query.filter_by(name=data['name']).first()\n        if existing_skill:\n            return api_response(\n                success=False,\n                message='Una competenza con questo nome esiste già',\n                status_code=400\n            )\n\n        # Create new skill\n        skill = Skill(\n            name=data['name'],\n            category=data.get('category', ''),\n            description=data.get('description', '')\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        # Return created skill data\n        skill_data = {\n            'id': skill.id,\n            'name': skill.name,\n            'category': skill.category,\n            'description': skill.description,\n            'user_count': 0\n        }\n\n        return api_response(\n            data={'skill': skill_data},\n            message=f\"Competenza '{skill.name}' creata con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/skills/<int:skill_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef update_skill(skill_id):\n    \"\"\"\n    Update an existing skill.\n    \"\"\"\n    try:\n        skill = Skill.query.get_or_404(skill_id)\n        data = request.get_json()\n\n        # Validate name if provided\n        if 'name' in data and data['name']:\n            # Check if name already exists (excluding current skill)\n            existing_skill = Skill.query.filter(\n                Skill.name == data['name'],\n                Skill.id != skill_id\n            ).first()\n            if existing_skill:\n                return api_response(\n                    success=False,\n                    message='Una competenza con questo nome esiste già',\n                    status_code=400\n                )\n            skill.name = data['name']\n\n        # Update other fields\n        if 'category' in data:\n            skill.category = data['category']\n        if 'description' in data:\n            skill.description = data['description']\n\n        db.session.commit()\n\n        # Get user count for response\n        user_count = UserSkill.query.filter_by(skill_id=skill.id).count()\n\n        skill_data = {\n            'id': skill.id,\n            'name': skill.name,\n            'category': skill.category,\n            'description': skill.description,\n            'user_count': user_count\n        }\n\n        return api_response(\n            data={'skill': skill_data},\n            message=f\"Competenza '{skill.name}' aggiornata con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/skills/<int:skill_id>', methods=['DELETE'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef delete_skill(skill_id):\n    \"\"\"\n    Delete a skill and all associated user skills.\n    \"\"\"\n    try:\n        skill = Skill.query.get_or_404(skill_id)\n\n        # Check if skill is assigned to users\n        user_skills_count = UserSkill.query.filter_by(skill_id=skill.id).count()\n        if user_skills_count > 0:\n            return api_response(\n                success=False,\n                message=f'Impossibile eliminare la competenza. È assegnata a {user_skills_count} dipendenti.',\n                status_code=400\n            )\n\n        skill_name = skill.name\n        db.session.delete(skill)\n        db.session.commit()\n\n        return api_response(\n            message=f\"Competenza '{skill_name}' eliminata con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n# ADMIN ENDPOINTS\n\n@api_personnel.route('/admin/users', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef create_user():\n    \"\"\"\n    Create a new user with full profile data.\n    \"\"\"\n    try:\n        from datetime import datetime\n        data = request.get_json()\n\n        # Validate required fields\n        required_fields = ['username', 'email', 'first_name', 'last_name']\n        for field in required_fields:\n            if not data.get(field):\n                return api_response(\n                    success=False,\n                    message=f'Campo {field} richiesto',\n                    status_code=400\n                )\n\n        # Check if username or email already exists\n        existing_user = User.query.filter(\n            or_(User.username == data['username'], User.email == data['email'])\n        ).first()\n        if existing_user:\n            return api_response(\n                success=False,\n                message='Username o email già esistenti',\n                status_code=400\n            )\n\n        # Create new user\n        user = User(\n            username=data['username'],\n            email=data['email'],\n            first_name=data['first_name'],\n            last_name=data['last_name'],\n            role=data.get('role', 'employee'),\n            department_id=data.get('department_id'),\n            position=data.get('position'),\n            hire_date=datetime.strptime(data['hire_date'], '%Y-%m-%d').date() if data.get('hire_date') else None,\n            phone=data.get('phone'),\n            is_active=data.get('is_active', True)\n        )\n\n        # Set password if provided\n        if data.get('password'):\n            user.set_password(data['password'])\n        else:\n            # Generate temporary password\n            import secrets\n            temp_password = secrets.token_urlsafe(12)\n            user.set_password(temp_password)\n\n        db.session.add(user)\n        db.session.flush()  # Get user ID\n\n        # Create profile if profile data provided\n        profile_fields = [\n            'employee_id', 'job_title', 'birth_date', 'address', 'employment_type',\n            'work_location', 'salary', 'salary_currency', 'probation_end_date',\n            'contract_end_date', 'notice_period_days', 'weekly_hours', 'daily_hours',\n            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship'\n        ]\n\n        if any(field in data for field in profile_fields):\n            profile = UserProfile(\n                user_id=user.id,\n                employee_id=data.get('employee_id'),\n                job_title=data.get('job_title'),\n                birth_date=datetime.strptime(data['birth_date'], '%Y-%m-%d').date() if data.get('birth_date') else None,\n                address=data.get('address'),\n                employment_type=data.get('employment_type', 'full_time'),\n                work_location=data.get('work_location'),\n                salary=data.get('salary'),\n                salary_currency=data.get('salary_currency', 'EUR'),\n                probation_end_date=datetime.strptime(data['probation_end_date'], '%Y-%m-%d').date() if data.get('probation_end_date') else None,\n                contract_end_date=datetime.strptime(data['contract_end_date'], '%Y-%m-%d').date() if data.get('contract_end_date') else None,\n                notice_period_days=data.get('notice_period_days', 30),\n                weekly_hours=data.get('weekly_hours', 40.0),\n                daily_hours=data.get('daily_hours', 8.0),\n                emergency_contact_name=data.get('emergency_contact_name'),\n                emergency_contact_phone=data.get('emergency_contact_phone'),\n                emergency_contact_relationship=data.get('emergency_contact_relationship')\n            )\n\n            db.session.add(profile)\n\n        db.session.commit()\n\n        # Return created user data\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'full_name': user.full_name,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'is_active': user.is_active,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'updated_at': profile.updated_at.isoformat() if profile.updated_at else None,\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=f\"Utente '{user.full_name}' creato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/admin/users/<int:user_id>', methods=['DELETE'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef delete_user(user_id):\n    \"\"\"\n    Delete a user (soft delete by setting is_active=False).\n    \"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n\n        # Prevent self-deletion\n        if user.id == current_user.id:\n            return api_response(\n                success=False,\n                message='Non puoi eliminare il tuo stesso account',\n                status_code=400\n            )\n\n        # Soft delete\n        user.is_active = False\n        db.session.commit()\n\n        return api_response(\n            message=f\"Utente '{user.full_name}' disattivato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/admin/users/<int:user_id>/reset-password', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef reset_user_password(user_id):\n    \"\"\"\n    Reset user password to a temporary one.\n    \"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n\n        # Generate temporary password\n        import secrets\n        temp_password = secrets.token_urlsafe(12)\n        user.set_password(temp_password)\n\n        db.session.commit()\n\n        return api_response(\n            data={'temporary_password': temp_password},\n            message=f\"Password di '{user.full_name}' resettata con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/admin/analytics', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_admin_analytics():\n    \"\"\"\n    Get comprehensive analytics for personnel administration.\n    \"\"\"\n    try:\n        from datetime import datetime, timedelta\n        from sqlalchemy import extract\n\n        # Basic statistics\n        total_users = User.query.filter_by(is_active=True).count()\n        total_departments = Department.query.filter_by(is_active=True).count()\n\n        # Users by role\n        users_by_role = db.session.query(\n            User.role, func.count(User.id)\n        ).filter_by(is_active=True).group_by(User.role).all()\n\n        # Users by department\n        users_by_dept = db.session.query(\n            Department.name, func.count(User.id)\n        ).join(User, Department.id == User.department_id)\\\n         .filter(User.is_active == True, Department.is_active == True)\\\n         .group_by(Department.name).all()\n\n        # Contract expiring soon (next 90 days)\n        today = datetime.now().date()\n        expiring_soon = today + timedelta(days=90)\n\n        expiring_contracts = db.session.query(User, UserProfile)\\\n            .join(UserProfile, User.id == UserProfile.user_id)\\\n            .filter(\n                User.is_active == True,\n                UserProfile.contract_end_date.isnot(None),\n                UserProfile.contract_end_date <= expiring_soon,\n                UserProfile.contract_end_date >= today\n            ).all()\n\n        # Probation ending soon (next 30 days)\n        probation_ending = today + timedelta(days=30)\n\n        ending_probation = db.session.query(User, UserProfile)\\\n            .join(UserProfile, User.id == UserProfile.user_id)\\\n            .filter(\n                User.is_active == True,\n                UserProfile.probation_end_date.isnot(None),\n                UserProfile.probation_end_date <= probation_ending,\n                UserProfile.probation_end_date >= today\n            ).all()\n\n        # Employment types distribution\n        employment_types = db.session.query(\n            UserProfile.employment_type, func.count(UserProfile.id)\n        ).join(User, UserProfile.user_id == User.id)\\\n         .filter(User.is_active == True)\\\n         .group_by(UserProfile.employment_type).all()\n\n        # Average salary by department (if salary data exists)\n        avg_salary_by_dept = db.session.query(\n            Department.name, func.avg(UserProfile.salary)\n        ).join(User, Department.id == User.department_id)\\\n         .join(UserProfile, User.id == UserProfile.user_id)\\\n         .filter(\n             User.is_active == True,\n             Department.is_active == True,\n             UserProfile.salary.isnot(None)\n         ).group_by(Department.name).all()\n\n        # Recent hires (last 90 days)\n        recent_hire_date = today - timedelta(days=90)\n        recent_hires = User.query.filter(\n            User.is_active == True,\n            User.hire_date >= recent_hire_date\n        ).count()\n\n        analytics_data = {\n            'overview': {\n                'total_users': total_users,\n                'total_departments': total_departments,\n                'recent_hires': recent_hires\n            },\n            'users_by_role': [{'role': role, 'count': count} for role, count in users_by_role],\n            'users_by_department': [{'department': dept, 'count': count} for dept, count in users_by_dept],\n            'employment_types': [{'type': emp_type or 'Non specificato', 'count': count} for emp_type, count in employment_types],\n            'avg_salary_by_department': [{'department': dept, 'avg_salary': float(avg_sal) if avg_sal else 0} for dept, avg_sal in avg_salary_by_dept],\n            'alerts': {\n                'expiring_contracts': [\n                    {\n                        'user_id': user.id,\n                        'full_name': user.full_name,\n                        'contract_end_date': profile.contract_end_date.isoformat(),\n                        'days_remaining': (profile.contract_end_date - today).days\n                    }\n                    for user, profile in expiring_contracts\n                ],\n                'ending_probation': [\n                    {\n                        'user_id': user.id,\n                        'full_name': user.full_name,\n                        'probation_end_date': profile.probation_end_date.isoformat(),\n                        'days_remaining': (profile.probation_end_date - today).days\n                    }\n                    for user, profile in ending_probation\n                ]\n            }\n        }\n\n        return api_response(\n            data=analytics_data,\n            message=\"Analytics data retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/export', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef export_personnel_data():\n    \"\"\"\n    Export personnel data to CSV format.\n    \"\"\"\n    try:\n        import csv\n        import io\n        from datetime import datetime\n\n        # Get all active users with their profiles\n        users_query = db.session.query(User, UserProfile, Department)\\\n            .outerjoin(UserProfile, User.id == UserProfile.user_id)\\\n            .outerjoin(Department, User.department_id == Department.id)\\\n            .filter(User.is_active == True)\\\n            .order_by(User.last_name, User.first_name)\n\n        users_data = users_query.all()\n\n        # Create CSV in memory\n        output = io.StringIO()\n        writer = csv.writer(output)\n\n        # Write header\n        headers = [\n            'ID', 'Nome', 'Cognome', 'Email', 'Username', 'Telefono',\n            'Ruolo', 'Dipartimento', 'Posizione', 'Data Assunzione',\n            'Tipo Contratto', 'Modalità Lavoro', 'Ore Settimanali',\n            'Stipendio', 'Valuta', 'Fine Periodo Prova', 'Scadenza Contratto',\n            'Giorni Preavviso', 'Contatto Emergenza', 'Tel. Emergenza', 'Relazione Emergenza',\n            'Indirizzo', 'Data Nascita', 'Creato il'\n        ]\n        writer.writerow(headers)\n\n        # Write data rows\n        for user, profile, department in users_data:\n            row = [\n                user.id,\n                user.first_name,\n                user.last_name,\n                user.email,\n                user.username,\n                user.phone or '',\n                user.role,\n                department.name if department else '',\n                user.position or '',\n                user.hire_date.isoformat() if user.hire_date else '',\n                profile.employment_type if profile else '',\n                profile.work_location if profile else '',\n                profile.weekly_hours if profile else '',\n                profile.salary if profile else '',\n                profile.salary_currency if profile else '',\n                profile.probation_end_date.isoformat() if profile and profile.probation_end_date else '',\n                profile.contract_end_date.isoformat() if profile and profile.contract_end_date else '',\n                profile.notice_period_days if profile else '',\n                profile.emergency_contact_name if profile else '',\n                profile.emergency_contact_phone if profile else '',\n                profile.emergency_contact_relationship if profile else '',\n                profile.address if profile else '',\n                profile.birth_date.isoformat() if profile and profile.birth_date else '',\n                user.created_at.isoformat() if user.created_at else ''\n            ]\n            writer.writerow(row)\n\n        # Prepare response\n        output.seek(0)\n        csv_data = output.getvalue()\n        output.close()\n\n        # Create response with CSV data\n        response = make_response(csv_data)\n        response.headers['Content-Type'] = 'text/csv'\n        response.headers['Content-Disposition'] = f'attachment; filename=personnel-export-{datetime.now().strftime(\"%Y%m%d\")}.csv'\n\n        return response\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/export/contacts', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef export_contacts_data():\n    \"\"\"\n    Export only contact information to CSV format.\n    \"\"\"\n    try:\n        import csv\n        import io\n        from datetime import datetime\n\n        # Get all active users with basic contact info\n        users = User.query.filter_by(is_active=True)\\\n            .order_by(User.last_name, User.first_name).all()\n\n        # Create CSV in memory\n        output = io.StringIO()\n        writer = csv.writer(output)\n\n        # Write header\n        headers = ['Nome', 'Cognome', 'Email', 'Telefono', 'Dipartimento', 'Posizione']\n        writer.writerow(headers)\n\n        # Write data\n        for user in users:\n            row = [\n                user.first_name or '',\n                user.last_name or '',\n                user.email or '',\n                user.phone or '',\n                user.department_obj.name if user.department_obj else '',\n                user.position or ''\n            ]\n            writer.writerow(row)\n\n        # Prepare response\n        output.seek(0)\n        csv_data = output.getvalue()\n        output.close()\n\n        # Create response with CSV data\n        response = make_response(csv_data)\n        response.headers['Content-Type'] = 'text/csv'\n        response.headers['Content-Disposition'] = f'attachment; filename=contacts-export-{datetime.now().strftime(\"%Y%m%d\")}.csv'\n\n        return response\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/import', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef import_personnel_data():\n    \"\"\"\n    Import personnel data from CSV file.\n    \"\"\"\n    try:\n        import csv\n        import io\n        from datetime import datetime\n\n        # Check if file was uploaded\n        if 'file' not in request.files:\n            return api_response(\n                success=False,\n                message='Nessun file caricato',\n                status_code=400\n            )\n\n        file = request.files['file']\n        if file.filename == '':\n            return api_response(\n                success=False,\n                message='Nessun file selezionato',\n                status_code=400\n            )\n\n        if not file.filename.lower().endswith('.csv'):\n            return api_response(\n                success=False,\n                message='Solo file CSV sono supportati',\n                status_code=400\n            )\n\n        # Read CSV content\n        stream = io.StringIO(file.stream.read().decode(\"UTF8\"), newline=None)\n        csv_input = csv.DictReader(stream)\n\n        imported_count = 0\n        total_count = 0\n        errors = []\n\n        for row_num, row in enumerate(csv_input, start=2):  # Start from 2 (header is row 1)\n            total_count += 1\n\n            try:\n                # Validate required fields\n                if not row.get('email') or not row.get('first_name') or not row.get('last_name'):\n                    errors.append(f\"Riga {row_num}: Email, nome e cognome sono obbligatori\")\n                    continue\n\n                # Check if user already exists\n                existing_user = User.query.filter_by(email=row['email']).first()\n                if existing_user:\n                    errors.append(f\"Riga {row_num}: Email {row['email']} già esistente\")\n                    continue\n\n                # Create new user\n                user = User(\n                    username=row.get('email'),  # Use email as username if not provided\n                    email=row['email'],\n                    first_name=row['first_name'],\n                    last_name=row['last_name'],\n                    phone=row.get('phone', ''),\n                    role=row.get('role', 'employee'),\n                    is_active=row.get('is_active', 'true').lower() == 'true',\n                    position=row.get('position', '')\n                )\n\n                # Set department if provided\n                if row.get('department_id'):\n                    try:\n                        dept_id = int(row['department_id'])\n                        dept = Department.query.get(dept_id)\n                        if dept:\n                            user.department_id = dept_id\n                        else:\n                            errors.append(f\"Riga {row_num}: Dipartimento ID {dept_id} non trovato\")\n                    except ValueError:\n                        errors.append(f\"Riga {row_num}: ID dipartimento non valido\")\n\n                # Set password (temporary)\n                user.set_password('TempPassword123!')\n\n                db.session.add(user)\n                db.session.flush()  # Get user ID\n\n                # Create profile if additional data is provided\n                if any(row.get(field) for field in ['hire_date', 'employment_type', 'salary']):\n                    profile = UserProfile(user_id=user.id)\n\n                    # Set hire date\n                    if row.get('hire_date'):\n                        try:\n                            profile.hire_date = datetime.strptime(row['hire_date'], '%Y-%m-%d').date()\n                        except ValueError:\n                            errors.append(f\"Riga {row_num}: Formato data assunzione non valido (usa YYYY-MM-DD)\")\n\n                    # Set employment type\n                    if row.get('employment_type'):\n                        profile.employment_type = row['employment_type']\n\n                    # Set salary\n                    if row.get('salary'):\n                        try:\n                            profile.salary = float(row['salary'])\n                        except ValueError:\n                            errors.append(f\"Riga {row_num}: Stipendio non valido\")\n\n                    db.session.add(profile)\n\n                imported_count += 1\n\n            except Exception as e:\n                errors.append(f\"Riga {row_num}: Errore durante l'importazione - {str(e)}\")\n                continue\n\n        # Commit all changes\n        db.session.commit()\n\n        return api_response(\n            data={\n                'imported': imported_count,\n                'total': total_count,\n                'errors': errors[:10]  # Limit to first 10 errors\n            },\n            message=f\"Import completato: {imported_count}/{total_count} dipendenti importati\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/verify', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef verify_data_integrity():\n    \"\"\"\n    Verify data integrity and return issues found.\n    \"\"\"\n    try:\n        issues = []\n\n        # Check for users without email\n        users_no_email = User.query.filter(\n            or_(User.email == None, User.email == '')\n        ).filter_by(is_active=True).count()\n\n        if users_no_email > 0:\n            issues.append(f\"{users_no_email} dipendenti attivi senza email\")\n\n        # Check for users without names\n        users_no_name = User.query.filter(\n            or_(\n                and_(User.first_name == None, User.last_name == None),\n                and_(User.first_name == '', User.last_name == '')\n            )\n        ).filter_by(is_active=True).count()\n\n        if users_no_name > 0:\n            issues.append(f\"{users_no_name} dipendenti attivi senza nome\")\n\n        # Check for users without department\n        users_no_dept = User.query.filter_by(\n            department_id=None,\n            is_active=True\n        ).count()\n\n        if users_no_dept > 0:\n            issues.append(f\"{users_no_dept} dipendenti attivi senza dipartimento\")\n\n        # Check for departments without manager\n        depts_no_manager = Department.query.filter_by(\n            manager_id=None,\n            is_active=True\n        ).count()\n\n        if depts_no_manager > 0:\n            issues.append(f\"{depts_no_manager} dipartimenti attivi senza manager\")\n\n        # Check for orphaned user profiles\n        orphaned_profiles = db.session.query(UserProfile).outerjoin(User).filter(\n            User.id == None\n        ).count()\n\n        if orphaned_profiles > 0:\n            issues.append(f\"{orphaned_profiles} profili utente orfani\")\n\n        # Check for duplicate emails\n        duplicate_emails = db.session.query(User.email, func.count(User.id))\\\n            .filter(User.email != None, User.email != '')\\\n            .group_by(User.email)\\\n            .having(func.count(User.id) > 1).count()\n\n        if duplicate_emails > 0:\n            issues.append(f\"{duplicate_emails} email duplicate\")\n\n        return api_response(\n            data={'issues': issues},\n            message=f\"Verifica completata: {len(issues)} problemi trovati\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n# ============================================================================\n# CV AND DOCUMENTS MANAGEMENT API ENDPOINTS\n# ============================================================================\n\n@api_personnel.route('/users/<int:user_id>/cv/upload', methods=['POST'])\n@api_login_required\ndef upload_cv(user_id):\n    \"\"\"\n    Upload CV for a user with AI analysis.\n    \"\"\"\n    try:\n        user_to_edit = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/manager/HR\n        if (user_to_edit.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di modificare il CV di questo utente',\n                status_code=403\n            )\n\n        # Check if file was uploaded\n        if 'cv_file' not in request.files:\n            return api_response(\n                success=False,\n                message='Nessun file CV caricato',\n                status_code=400\n            )\n\n        file = request.files['cv_file']\n        if file.filename == '':\n            return api_response(\n                success=False,\n                message='Nessun file selezionato',\n                status_code=400\n            )\n\n        # Validate file\n        if not is_valid_cv_file(file.filename):\n            return api_response(\n                success=False,\n                message='Formato file non supportato. Usa PDF, DOCX, DOC o TXT',\n                status_code=400\n            )\n\n        # Check file size\n        if get_file_size_mb(file) > 10:\n            return api_response(\n                success=False,\n                message='File troppo grande. Dimensione massima: 10MB',\n                status_code=400\n            )\n\n        # Create upload directory if it doesn't exist\n        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'cv')\n        os.makedirs(upload_dir, exist_ok=True)\n\n        # Generate secure filename\n        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n        filename = f\"cv_{user_to_edit.id}_{timestamp}_{secure_filename(file.filename)}\"\n        file_path = os.path.join('cv', filename)\n        full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)\n\n        # Save file\n        file.save(full_path)\n\n        # Get or create profile\n        profile = user_to_edit.profile\n        if not profile:\n            profile = UserProfile(user_id=user_to_edit.id)\n            db.session.add(profile)\n            db.session.flush()\n\n        # Remove old CV if exists\n        if profile.current_cv_path:\n            old_cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], profile.current_cv_path)\n            if os.path.exists(old_cv_path):\n                try:\n                    os.remove(old_cv_path)\n                except OSError:\n                    pass  # Ignore if file can't be deleted\n\n        # Update profile\n        profile.current_cv_path = file_path\n        profile.cv_last_updated = datetime.utcnow()\n\n        # AI Analysis if requested\n        analyze_skills = request.form.get('analyze_skills', 'true').lower() == 'true'\n        analysis_result = None\n\n        if analyze_skills:\n            cv_text = extract_text_from_cv(full_path)\n            if cv_text:\n                try:\n                    analysis_result = extract_skills_from_cv(cv_text)\n                    if 'error' not in analysis_result:\n                        profile.cv_analysis_data = json.dumps(analysis_result)\n\n                        # Auto-add skills if requested\n                        auto_add_skills = request.form.get('auto_add_skills', 'false').lower() == 'true'\n                        if auto_add_skills and 'skills' in analysis_result:\n                            added_skills = []\n                            for skill_data in analysis_result['skills'][:10]:  # Limit to first 10\n                                skill_name = skill_data.get('name', '').strip()\n                                if not skill_name:\n                                    continue\n\n                                # Find or create skill\n                                skill = Skill.query.filter_by(name=skill_name).first()\n                                if not skill:\n                                    skill = Skill(\n                                        name=skill_name,\n                                        category=skill_data.get('category', 'Generale')\n                                    )\n                                    db.session.add(skill)\n                                    db.session.flush()\n\n                                # Check if user already has this skill\n                                existing_user_skill = UserSkill.query.filter_by(\n                                    user_id=user_to_edit.id,\n                                    skill_id=skill.id\n                                ).first()\n\n                                if not existing_user_skill:\n                                    # Add skill to user\n                                    user_skill = UserSkill(\n                                        user_id=user_to_edit.id,\n                                        skill_id=skill.id,\n                                        proficiency_level=skill_data.get('level', 3),\n                                        years_experience=skill_data.get('years_experience', 0),\n                                        notes=f\"Estratto automaticamente dal CV - {skill_data.get('context', '')}\"\n                                    )\n                                    db.session.add(user_skill)\n                                    added_skills.append(skill_name)\n\n                            analysis_result['added_skills'] = added_skills\n\n                except Exception as ai_error:\n                    # Don't fail the upload if AI analysis fails\n                    analysis_result = {'error': f'Errore analisi AI: {str(ai_error)}'}\n\n        # Recalculate profile completion\n        profile.calculate_completion()\n        db.session.commit()\n\n        # Return complete user data with updated profile info\n        user_data = {\n            'id': user_to_edit.id,\n            'username': user_to_edit.username,\n            'email': user_to_edit.email,\n            'first_name': user_to_edit.first_name,\n            'last_name': user_to_edit.last_name,\n            'full_name': user_to_edit.full_name,\n            'role': user_to_edit.role,\n            'is_active': user_to_edit.is_active,\n            'department_id': user_to_edit.department_id,\n            'hire_date': user_to_edit.hire_date.isoformat() if user_to_edit.hire_date else None,\n            'phone': user_to_edit.phone,\n            'last_login': user_to_edit.last_login.isoformat() if user_to_edit.last_login else None,\n            'created_at': user_to_edit.created_at.isoformat() if user_to_edit.created_at else None,\n            'updated_at': profile.updated_at.isoformat() if profile.updated_at else None,\n            \n            'profile': {\n                'employee_id': profile.employee_id,\n                'job_title': profile.job_title,\n                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,\n                'address': profile.address,\n                'emergency_contact_name': profile.emergency_contact_name,\n                'emergency_contact_phone': profile.emergency_contact_phone,\n                'emergency_contact_relationship': profile.emergency_contact_relationship,\n                'employment_type': profile.employment_type,\n                'work_location': profile.work_location,\n                'weekly_hours': profile.weekly_hours,\n                'daily_hours': profile.daily_hours,\n                'current_cv_path': profile.current_cv_path,\n                'cv_last_updated': profile.cv_last_updated.isoformat() if profile.cv_last_updated else None,\n                'cv_analysis_data': profile.cv_analysis_data,\n                'profile_completion': profile.profile_completion,\n                'notes': profile.notes if current_user.role in ['admin', 'human_resources'] else None,\n                'created_at': profile.created_at.isoformat() if profile.created_at else None,\n                'updated_at': profile.updated_at.isoformat() if profile.updated_at else None\n            }\n        }\n\n        return api_response(\n            data=user_data,\n            message='CV caricato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>/cv/download', methods=['GET'])\n@api_login_required\ndef download_cv(user_id):\n    \"\"\"\n    Download CV file for a user.\n    \"\"\"\n    try:\n        user_to_view = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or view personnel data permission\n        if (user_to_view.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di scaricare questo CV',\n                status_code=403\n            )\n\n        if not user_to_view.profile or not user_to_view.profile.current_cv_path:\n            return api_response(\n                success=False,\n                message='Nessun CV disponibile per questo utente',\n                status_code=404\n            )\n\n        cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], user_to_view.profile.current_cv_path)\n\n        if not os.path.exists(cv_path):\n            return api_response(\n                success=False,\n                message='File CV non trovato sul server',\n                status_code=404\n            )\n\n        # Get original filename from path\n        original_filename = os.path.basename(user_to_view.profile.current_cv_path)\n        # Clean filename for download\n        download_filename = f\"CV_{user_to_view.full_name}_{original_filename.split('_')[-1]}\"\n\n        return send_file(\n            cv_path,\n            as_attachment=True,\n            download_name=download_filename\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>/cv', methods=['DELETE'])\n@api_login_required\ndef delete_cv(user_id):\n    \"\"\"\n    Delete CV file for a user.\n    \"\"\"\n    try:\n        user_to_edit = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/manager/HR\n        if (user_to_edit.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di cancellare il CV di questo utente',\n                status_code=403\n            )\n\n        if not user_to_edit.profile or not user_to_edit.profile.current_cv_path:\n            return api_response(\n                success=False,\n                message='Nessun CV da cancellare',\n                status_code=404\n            )\n\n        # Remove file from filesystem\n        cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], user_to_edit.profile.current_cv_path)\n        if os.path.exists(cv_path):\n            try:\n                os.remove(cv_path)\n            except OSError:\n                pass  # Ignore if file can't be deleted\n\n        # Clear CV data from profile\n        profile = user_to_edit.profile\n        profile.current_cv_path = None\n        profile.cv_last_updated = None\n        profile.cv_analysis_data = None\n\n        # Recalculate profile completion\n        profile.calculate_completion()\n        db.session.commit()\n\n        return api_response(\n            data={'profile_completion': profile.profile_completion},\n            message='CV cancellato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>/cv/analysis', methods=['GET'])\n@api_login_required\ndef get_cv_analysis(user_id):\n    \"\"\"\n    Get CV analysis data for a user.\n    \"\"\"\n    try:\n        user_to_view = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or view personnel data permission\n        if (user_to_view.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di visualizzare l\\'analisi CV di questo utente',\n                status_code=403\n            )\n\n        if not user_to_view.profile or not user_to_view.profile.cv_analysis_data:\n            return api_response(\n                success=False,\n                message='Nessuna analisi CV disponibile per questo utente',\n                status_code=404\n            )\n\n        try:\n            analysis_data = json.loads(user_to_view.profile.cv_analysis_data)\n        except json.JSONDecodeError:\n            return api_response(\n                success=False,\n                message='Dati di analisi CV corrotti',\n                status_code=500\n            )\n\n        return api_response(\n            data={\n                'analysis': analysis_data,\n                'cv_last_updated': user_to_view.profile.cv_last_updated.isoformat() if user_to_view.profile.cv_last_updated else None\n            },\n            message='Analisi CV recuperata con successo'\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>/skills/from-cv', methods=['POST'])\n@api_login_required\ndef add_skills_from_cv_analysis(user_id):\n    \"\"\"\n    Add skills to user profile from CV analysis data.\n    \"\"\"\n    try:\n        user_to_edit = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/manager/HR\n        if (user_to_edit.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di modificare le competenze di questo utente',\n                status_code=403\n            )\n\n        if not user_to_edit.profile or not user_to_edit.profile.cv_analysis_data:\n            return api_response(\n                success=False,\n                message='Nessuna analisi CV disponibile per questo utente',\n                status_code=404\n            )\n\n        try:\n            analysis_data = json.loads(user_to_edit.profile.cv_analysis_data)\n        except json.JSONDecodeError:\n            return api_response(\n                success=False,\n                message='Dati di analisi CV corrotti',\n                status_code=500\n            )\n\n        if 'skills' not in analysis_data:\n            return api_response(\n                success=False,\n                message='Nessuna competenza trovata nell\\'analisi CV',\n                status_code=404\n            )\n\n        # Get selected skills from request\n        data = request.get_json()\n        selected_skills = data.get('selected_skills', [])\n\n        if not selected_skills:\n            return api_response(\n                success=False,\n                message='Nessuna competenza selezionata',\n                status_code=400\n            )\n\n        added_skills = []\n        skipped_skills = []\n\n        for skill_index in selected_skills:\n            if skill_index >= len(analysis_data['skills']):\n                continue\n\n            skill_data = analysis_data['skills'][skill_index]\n            skill_name = skill_data.get('name', '').strip()\n\n            if not skill_name:\n                continue\n\n            # Find or create skill\n            skill = Skill.query.filter_by(name=skill_name).first()\n            if not skill:\n                skill = Skill(\n                    name=skill_name,\n                    category=skill_data.get('category', 'Generale'),\n                    description=skill_data.get('description', '')\n                )\n                db.session.add(skill)\n                db.session.flush()\n\n            # Check if user already has this skill\n            existing_user_skill = UserSkill.query.filter_by(\n                user_id=user_to_edit.id,\n                skill_id=skill.id\n            ).first()\n\n            if existing_user_skill:\n                skipped_skills.append(skill_name)\n                continue\n\n            # Add skill to user\n            user_skill = UserSkill(\n                user_id=user_to_edit.id,\n                skill_id=skill.id,\n                proficiency_level=skill_data.get('level', 3),\n                years_experience=skill_data.get('years_experience', 0),\n                notes=f\"Estratto dal CV - {skill_data.get('context', '')}\"\n            )\n            db.session.add(user_skill)\n            added_skills.append(skill_name)\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'added_skills': added_skills,\n                'skipped_skills': skipped_skills,\n                'total_added': len(added_skills),\n                'total_skipped': len(skipped_skills)\n            },\n            message=f'Aggiunte {len(added_skills)} competenze al profilo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)", "modifiedCode": "\"\"\"\nAPI endpoints for personnel management.\nProvides REST API for users, departments, skills, and organization chart.\n\"\"\"\n\nimport os\nimport json\nfrom datetime import datetime\nfrom flask import Blueprint, request, jsonify, make_response, current_app, send_file\nfrom flask_login import current_user\nfrom sqlalchemy import or_, and_, func\nfrom sqlalchemy.orm import joinedload\nfrom werkzeug.utils import secure_filename\n\nfrom extensions import db\nfrom models import User, Department, Skill, UserSkill, UserProfile\nfrom utils.api_utils import (\n    api_response, get_pagination_params, api_permission_required,\n    handle_api_error, api_login_required\n)\nfrom utils.permissions import (\n    PERMISSION_VIEW_PERSONNEL_DATA, PERMISSION_EDIT_PERSONNEL_DATA, PERMISSION_MANAGE_USERS\n)\nfrom utils.cv_parser import extract_text_from_cv, is_valid_cv_file, get_file_size_mb\nfrom services.ai import extract_skills_from_cv\n\n# Create blueprint\napi_personnel = Blueprint('api_personnel', __name__, url_prefix='/personnel')\n\n@api_personnel.route('', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_personnel():\n    \"\"\"\n    Get list of personnel (alias for /users endpoint).\n    This is a convenience endpoint that redirects to the users endpoint.\n    \"\"\"\n    return get_users()\n\n@api_personnel.route('/users', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_users():\n    \"\"\"\n    Get list of users with filtering, pagination, and search.\n\n    Query Parameters:\n    - page: Page number (default: 1)\n    - per_page: Items per page (default: 20)\n    - search: Search in name, username, email\n    - department_id: Filter by department\n    - role: Filter by role\n    - is_active: Filter by active status\n    - skills: Filter by skills (comma-separated skill IDs)\n    \"\"\"\n    try:\n        # Get pagination parameters\n        page, per_page = get_pagination_params()\n\n        # Build base query\n        query = User.query.options(\n            joinedload(User.department_obj),\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.profile)\n        )\n\n        # Apply filters\n        search = request.args.get('search', '').strip()\n        if search:\n            search_filter = or_(\n                User.first_name.ilike(f'%{search}%'),\n                User.last_name.ilike(f'%{search}%'),\n                User.username.ilike(f'%{search}%'),\n                User.email.ilike(f'%{search}%')\n            )\n            query = query.filter(search_filter)\n\n        # Department filter\n        department_id = request.args.get('department_id', type=int)\n        if department_id:\n            query = query.filter(User.department_id == department_id)\n\n        # Role filter\n        role = request.args.get('role')\n        if role:\n            query = query.filter(User.role == role)\n\n        # Active status filter\n        is_active = request.args.get('is_active')\n        if is_active is not None:\n            is_active_bool = is_active.lower() in ['true', '1', 'yes']\n            query = query.filter(User.is_active == is_active_bool)\n\n        # Skills filter\n        skills = request.args.get('skills')\n        if skills:\n            skill_ids = [int(id.strip()) for id in skills.split(',') if id.strip().isdigit()]\n            if skill_ids:\n                query = query.join(UserSkill).filter(UserSkill.skill_id.in_(skill_ids))\n\n        # Order by\n        order_by = request.args.get('order_by', 'last_name')\n        order_dir = request.args.get('order_dir', 'asc')\n\n        if hasattr(User, order_by):\n            order_column = getattr(User, order_by)\n            if order_dir.lower() == 'desc':\n                order_column = order_column.desc()\n            query = query.order_by(order_column)\n        else:\n            query = query.order_by(User.last_name.asc())\n\n        # Execute pagination\n        pagination = query.paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        # Serialize users\n        users_data = []\n        for user in pagination.items:\n            user_data = {\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'first_name': user.first_name,\n                'last_name': user.last_name,\n                'full_name': user.full_name,\n                'role': user.role,\n                'department_id': user.department_id,\n                'department_name': user.department_obj.name if user.department_obj else None,\n                'position': user.position,\n                'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n                'phone': user.phone,\n                'profile_image': user.profile_image,\n                'is_active': user.is_active,\n                'last_login': user.last_login.isoformat() if user.last_login else None,\n                'skills': [\n                    {\n                        'id': us.skill.id,\n                        'name': us.skill.name,\n                        'category': us.skill.category,\n                        'proficiency_level': us.proficiency_level,\n                        'years_experience': us.years_experience\n                    }\n                    for us in user.detailed_skills\n                ] if user.detailed_skills else [],\n                'profile_completion': user.profile.profile_completion if user.profile else 0.0\n            }\n            users_data.append(user_data)\n\n        return api_response(\n            data={\n                'users': users_data,\n                'pagination': {\n                    'page': pagination.page,\n                    'pages': pagination.pages,\n                    'per_page': pagination.per_page,\n                    'total': pagination.total,\n                    'has_next': pagination.has_next,\n                    'has_prev': pagination.has_prev\n                }\n            },\n            message=f\"Retrieved {len(users_data)} users\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_user(user_id):\n    \"\"\"\n    Get detailed information about a specific user.\n    \"\"\"\n    try:\n        user = User.query.options(\n            joinedload(User.department_obj),\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.profile),\n            joinedload(User.projects)\n        ).get_or_404(user_id)\n\n        # Ricalcola il completamento del profilo se esiste\n        if user.profile:\n            user.profile.calculate_completion()\n            db.session.commit()\n\n        # Serialize user with full details\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'full_name': user.full_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'department': {\n                'id': user.department_obj.id,\n                'name': user.department_obj.name,\n                'description': user.department_obj.description\n            } if user.department_obj else None,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'profile_image': user.profile_image,\n            'bio': user.bio,\n            'is_active': user.is_active,\n            'dark_mode': user.dark_mode,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'last_login': user.last_login.isoformat() if user.last_login else None,\n            'skills': [\n                {\n                    'id': us.skill.id,\n                    'name': us.skill.name,\n                    'category': us.skill.category,\n                    'description': us.skill.description,\n                    'proficiency_level': us.proficiency_level,\n                    'years_experience': us.years_experience,\n                    'certified': us.is_certified,\n                    'last_used': us.certification_date.isoformat() if us.certification_date else None\n                }\n                for us in user.detailed_skills\n            ] if user.detailed_skills else [],\n            'projects': [\n                {\n                    'id': project.id,\n                    'name': project.name,\n                    'status': project.status,\n                    'role': 'team_member'  # Could be enhanced with actual role from project_team table\n                }\n                for project in user.projects\n            ] if user.projects else [],\n            'profile': {\n                'employee_id': user.profile.employee_id,\n                'job_title': user.profile.job_title,\n                'birth_date': user.profile.birth_date.isoformat() if user.profile.birth_date else None,\n                'address': user.profile.address,\n                'emergency_contact_name': user.profile.emergency_contact_name,\n                'emergency_contact_phone': user.profile.emergency_contact_phone,\n                'emergency_contact_relationship': user.profile.emergency_contact_relationship,\n                'employment_type': user.profile.employment_type,\n                'work_location': user.profile.work_location,\n                'weekly_hours': user.profile.weekly_hours,\n                'daily_hours': user.profile.daily_hours,\n                'current_cv_path': user.profile.current_cv_path,\n                'cv_last_updated': user.profile.cv_last_updated.isoformat() if user.profile.cv_last_updated else None,\n                'profile_completion': user.profile.profile_completion,\n                'notes': user.profile.notes if current_user.role in ['admin', 'human_resources'] else None,\n                'created_at': user.profile.created_at.isoformat() if user.profile.created_at else None,\n                'updated_at': user.profile.updated_at.isoformat() if user.profile.updated_at else None\n            } if user.profile else None\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=f\"Retrieved user {user.full_name}\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_EDIT_PERSONNEL_DATA)\ndef update_user_profile(user_id):\n    \"\"\"\n    Update user profile information.\n    Allows updating both User and UserProfile fields.\n    \"\"\"\n    try:\n        # Check if user can edit this profile\n        user = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/HR\n        if user.id != current_user.id and not current_user.role in ['admin', 'human_resources']:\n            return api_response(\n                success=False,\n                message=\"Non hai il permesso di modificare questo profilo\",\n                status_code=403\n            )\n\n        data = request.get_json()\n        if not data:\n            return api_response(\n                success=False,\n                message=\"Nessun dato fornito\",\n                status_code=400\n            )\n\n        # Update User fields\n        user_fields = ['first_name', 'last_name', 'phone', 'bio', 'position']\n        for field in user_fields:\n            if field in data:\n                setattr(user, field, data[field])\n\n        # Get or create UserProfile\n        profile = user.profile\n        if not profile:\n            profile = UserProfile(user_id=user.id)\n            db.session.add(profile)\n\n        # Update UserProfile fields\n        profile_fields = [\n            'employee_id', 'job_title', 'birth_date', 'address',\n            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',\n            'employment_type', 'work_location', 'weekly_hours', 'daily_hours'\n        ]\n\n        for field in profile_fields:\n            if field in data:\n                if field == 'birth_date' and data[field]:\n                    # Handle date conversion\n                    from datetime import datetime\n                    profile.birth_date = datetime.strptime(data[field], '%Y-%m-%d').date()\n                else:\n                    setattr(profile, field, data[field])\n\n        # Recalculate profile completion\n        profile.calculate_completion()\n\n        # Save changes\n        db.session.commit()\n\n        # Return updated user data\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'full_name': user.full_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'department': {\n                'id': user.department_obj.id,\n                'name': user.department_obj.name,\n                'description': user.department_obj.description\n            } if user.department_obj else None,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'profile_image': user.profile_image,\n            'bio': user.bio,\n            'is_active': user.is_active,\n            'profile': {\n                'employee_id': profile.employee_id,\n                'job_title': profile.job_title,\n                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,\n                'address': profile.address,\n                'emergency_contact_name': profile.emergency_contact_name,\n                'emergency_contact_phone': profile.emergency_contact_phone,\n                'emergency_contact_relationship': profile.emergency_contact_relationship,\n                'employment_type': profile.employment_type,\n                'work_location': profile.work_location,\n                'weekly_hours': profile.weekly_hours,\n                'daily_hours': profile.daily_hours,\n                'profile_completion': profile.profile_completion\n            }\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=\"Profilo aggiornato con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments', methods=['GET'])\n@api_login_required\ndef get_departments():\n    \"\"\"\n    Get list of departments with organization chart data.\n    \"\"\"\n    try:\n        departments = Department.query.options(\n            joinedload(Department.manager)\n        ).all()\n\n        departments_data = []\n        for dept in departments:\n            dept_data = {\n                'id': dept.id,\n                'name': dept.name,\n                'description': dept.description,\n                'manager_id': dept.manager_id,\n                'manager': {\n                    'id': dept.manager.id,\n                    'full_name': dept.manager.full_name,\n                    'email': dept.manager.email\n                } if dept.manager else None,\n                'user_count': dept.employees.count() if dept.employees else 0,\n                'users': [\n                    {\n                        'id': user.id,\n                        'full_name': user.full_name,\n                        'position': user.position,\n                        'email': user.email,\n                        'is_active': user.is_active\n                    }\n                    for user in dept.employees\n                ] if dept.employees else [],\n                'created_at': dept.created_at.isoformat() if dept.created_at else None\n            }\n            departments_data.append(dept_data)\n\n        return api_response(\n            data={'departments': departments_data},\n            message=f\"Retrieved {len(departments_data)} departments\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/orgchart', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_orgchart():\n    \"\"\"\n    Get organization chart data with hierarchical structure.\n    \"\"\"\n    try:\n        # Build hierarchical department tree\n        def build_department_tree(parent_id=None):\n            tree = []\n            departments = Department.query.filter_by(\n                parent_id=parent_id,\n                is_active=True\n            ).order_by(Department.name).all()\n\n            for dept in departments:\n                # Get active employees for this department\n                employees = User.query.filter_by(\n                    department_id=dept.id,\n                    is_active=True\n                ).order_by(User.first_name, User.last_name).all()\n\n                # Build employee data\n                employees_data = []\n                for emp in employees:\n                    emp_data = {\n                        'id': emp.id,\n                        'full_name': emp.full_name,\n                        'first_name': emp.first_name,\n                        'last_name': emp.last_name,\n                        'email': emp.email,\n                        'position': emp.position,\n                        'role': emp.role,\n                        'hire_date': emp.hire_date.isoformat() if emp.hire_date else None,\n                        'profile_image': emp.profile_image,\n                        'is_manager': emp.id == dept.manager_id\n                    }\n                    employees_data.append(emp_data)\n\n                # Build department node\n                dept_data = {\n                    'id': dept.id,\n                    'name': dept.name,\n                    'description': dept.description,\n                    'manager_id': dept.manager_id,\n                    'manager': {\n                        'id': dept.manager.id,\n                        'full_name': dept.manager.full_name,\n                        'email': dept.manager.email,\n                        'position': dept.manager.position,\n                        'profile_image': dept.manager.profile_image\n                    } if dept.manager else None,\n                    'employees': employees_data,\n                    'employee_count': len(employees_data),\n                    'budget': dept.budget,\n                    'subdepartments': build_department_tree(dept.id)\n                }\n                tree.append(dept_data)\n\n            return tree\n\n        # Get root departments (no parent)\n        orgchart_data = build_department_tree()\n\n        # Calculate total statistics\n        total_employees = User.query.filter_by(is_active=True).count()\n        total_departments = Department.query.filter_by(is_active=True).count()\n        total_managers = Department.query.filter(\n            Department.manager_id.isnot(None),\n            Department.is_active == True\n        ).count()\n\n        stats = {\n            'total_employees': total_employees,\n            'total_departments': total_departments,\n            'total_managers': total_managers\n        }\n\n        return api_response(\n            data={\n                'orgchart': orgchart_data,\n                'stats': stats\n            },\n            message=f\"Retrieved organization chart with {total_departments} departments\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef create_department():\n    \"\"\"\n    Create a new department.\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        # Validate required fields\n        if not data.get('name'):\n            return api_response(\n                success=False,\n                message='Nome dipartimento richiesto',\n                status_code=400\n            )\n\n        # Check if department name already exists\n        existing_dept = Department.query.filter_by(name=data['name']).first()\n        if existing_dept:\n            return api_response(\n                success=False,\n                message='Un dipartimento con questo nome esiste già',\n                status_code=400\n            )\n\n        # Create new department\n        department = Department(\n            name=data['name'],\n            description=data.get('description', ''),\n            manager_id=data.get('manager_id'),\n            parent_id=data.get('parent_id'),\n            budget=data.get('budget', 0.0)\n        )\n\n        db.session.add(department)\n        db.session.commit()\n\n        # Return created department data\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'user_count': 0,\n            'created_at': department.created_at.isoformat()\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Dipartimento '{department.name}' creato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_department(dept_id):\n    \"\"\"\n    Get detailed information about a specific department.\n    \"\"\"\n    try:\n        department = Department.query.options(\n            joinedload(Department.manager)\n        ).get_or_404(dept_id)\n\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'manager': {\n                'id': department.manager.id,\n                'full_name': department.manager.full_name,\n                'email': department.manager.email,\n                'position': department.manager.position\n            } if department.manager else None,\n            'parent': {\n                'id': department.parent.id,\n                'name': department.parent.name\n            } if department.parent_id and hasattr(department, 'parent') and department.parent else None,\n            'employees': [\n                {\n                    'id': emp.id,\n                    'full_name': emp.full_name,\n                    'email': emp.email,\n                    'position': emp.position,\n                    'is_active': emp.is_active,\n                    'hire_date': emp.hire_date.isoformat() if emp.hire_date else None\n                }\n                for emp in department.employees if emp.is_active\n            ],\n            'subdepartments': [\n                {\n                    'id': sub.id,\n                    'name': sub.name,\n                    'employee_count': getattr(sub, 'employee_count', 0)\n                }\n                for sub in department.subdepartments if getattr(sub, 'is_active', True)\n            ],\n            'employee_count': len([emp for emp in department.employees if emp.is_active]),\n            'created_at': department.created_at.isoformat() if department.created_at else None,\n            'updated_at': department.updated_at.isoformat() if department.updated_at else None\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Retrieved department {department.name}\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef update_department(dept_id):\n    \"\"\"\n    Update an existing department.\n    \"\"\"\n    try:\n        department = Department.query.get_or_404(dept_id)\n        data = request.get_json()\n\n        # Validate name if provided\n        if 'name' in data and data['name']:\n            # Check if name already exists (excluding current department)\n            existing_dept = Department.query.filter(\n                Department.name == data['name'],\n                Department.id != dept_id\n            ).first()\n            if existing_dept:\n                return api_response(\n                    success=False,\n                    message='Un dipartimento con questo nome esiste già',\n                    status_code=400\n                )\n            department.name = data['name']\n\n        # Update other fields\n        if 'description' in data:\n            department.description = data['description']\n        if 'manager_id' in data:\n            department.manager_id = data['manager_id']\n        if 'parent_id' in data:\n            department.parent_id = data['parent_id']\n        if 'budget' in data:\n            department.budget = data['budget']\n\n        db.session.commit()\n\n        # Return updated department data\n        dept_data = {\n            'id': department.id,\n            'name': department.name,\n            'description': department.description,\n            'manager_id': department.manager_id,\n            'parent_id': department.parent_id,\n            'budget': department.budget,\n            'employee_count': len([emp for emp in department.employees if emp.is_active]),\n            'updated_at': department.updated_at.isoformat() if department.updated_at else None\n        }\n\n        return api_response(\n            data={'department': dept_data},\n            message=f\"Dipartimento '{department.name}' aggiornato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/departments/<int:dept_id>', methods=['DELETE'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef delete_department(dept_id):\n    \"\"\"\n    Delete a department (soft delete by setting is_active=False).\n    \"\"\"\n    try:\n        department = Department.query.get_or_404(dept_id)\n\n        # Check if department has employees\n        active_employees = len([emp for emp in department.employees if emp.is_active])\n        if active_employees > 0:\n            return api_response(\n                success=False,\n                message='Impossibile eliminare un dipartimento con dipendenti assegnati',\n                status_code=400\n            )\n\n        # Check if department has subdepartments\n        active_subdepartments = len([sub for sub in department.subdepartments if getattr(sub, 'is_active', True)])\n        if active_subdepartments > 0:\n            return api_response(\n                success=False,\n                message='Impossibile eliminare un dipartimento con sotto-dipartimenti',\n                status_code=400\n            )\n\n        # Soft delete\n        department.is_active = False\n        db.session.commit()\n\n        return api_response(\n            message=f\"Dipartimento '{department.name}' eliminato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/skills', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_skills():\n    \"\"\"\n    Get list of skills with usage statistics.\n    \"\"\"\n    try:\n        # Get skills with user count\n        skills_query = db.session.query(\n            Skill,\n            func.count(UserSkill.user_id).label('user_count')\n        ).outerjoin(UserSkill).group_by(Skill.id)\n\n        category = request.args.get('category')\n        if category:\n            skills_query = skills_query.filter(Skill.category == category)\n\n        search = request.args.get('search', '').strip()\n        if search:\n            skills_query = skills_query.filter(\n                or_(\n                    Skill.name.ilike(f'%{search}%'),\n                    Skill.description.ilike(f'%{search}%')\n                )\n            )\n\n        skills_data = []\n        for skill, user_count in skills_query.all():\n            skill_data = {\n                'id': skill.id,\n                'name': skill.name,\n                'category': skill.category,\n                'description': skill.description,\n                'user_count': user_count,\n                'users': [\n                    {\n                        'id': us.user.id,\n                        'full_name': us.user.full_name,\n                        'proficiency_level': us.proficiency_level,\n                        'years_experience': us.years_experience\n                    }\n                    for us in skill.user_skills\n                ] if hasattr(skill, 'user_skills') else []\n            }\n            skills_data.append(skill_data)\n\n        # Get categories for filter\n        categories = db.session.query(Skill.category).distinct().all()\n        categories_list = [cat[0] for cat in categories if cat[0]]\n\n        return api_response(\n            data={\n                'skills': skills_data,\n                'categories': categories_list\n            },\n            message=f\"Retrieved {len(skills_data)} skills\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/skills-matrix', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_skills_matrix():\n    \"\"\"\n    Get skills matrix data - users vs skills with proficiency levels.\n    \"\"\"\n    try:\n        # Get filter parameters\n        department_id = request.args.get('department_id')\n        category = request.args.get('category')\n        skill_ids = request.args.get('skill_ids', '').split(',') if request.args.get('skill_ids') else []\n        min_level = request.args.get('min_level', type=int)\n        max_level = request.args.get('max_level', type=int)\n\n        # Build users query with filters\n        users_query = User.query.filter(User.is_active == True)\n\n        if department_id:\n            users_query = users_query.filter(User.department_id == department_id)\n\n        # Get users with their skills\n        users = users_query.options(\n            joinedload(User.detailed_skills).joinedload(UserSkill.skill),\n            joinedload(User.department_obj)\n        ).order_by(User.first_name, User.last_name).all()\n\n        # Build skills query with filters\n        skills_query = Skill.query\n\n        if category:\n            skills_query = skills_query.filter(Skill.category == category)\n\n        if skill_ids and skill_ids != ['']:\n            skills_query = skills_query.filter(Skill.id.in_(skill_ids))\n\n        skills = skills_query.order_by(Skill.category, Skill.name).all()\n\n        # Build matrix data\n        matrix_data = []\n\n        for user in users:\n            # Create user skills lookup\n            user_skills_dict = {\n                us.skill_id: {\n                    'proficiency_level': us.proficiency_level,\n                    'years_experience': us.years_experience,\n                    'is_certified': us.is_certified,\n                    'certification_name': us.certification_name,\n                    'self_assessed': us.self_assessed,\n                    'manager_assessed': us.manager_assessed\n                }\n                for us in user.detailed_skills\n            }\n\n            # Build skills data for this user\n            user_skills = []\n            for skill in skills:\n                skill_data = user_skills_dict.get(skill.id, {\n                    'proficiency_level': 0,\n                    'years_experience': 0,\n                    'is_certified': False,\n                    'certification_name': None,\n                    'self_assessed': False,\n                    'manager_assessed': False\n                })\n\n                # Apply level filters\n                if min_level and skill_data['proficiency_level'] < min_level:\n                    continue\n                if max_level and skill_data['proficiency_level'] > max_level:\n                    continue\n\n                user_skills.append({\n                    'skill_id': skill.id,\n                    'skill_name': skill.name,\n                    'skill_category': skill.category,\n                    **skill_data\n                })\n\n            # Add user to matrix if they have skills matching filters\n            if not min_level and not max_level or user_skills:\n                matrix_data.append({\n                    'user_id': user.id,\n                    'full_name': user.full_name,\n                    'first_name': user.first_name,\n                    'last_name': user.last_name,\n                    'email': user.email,\n                    'position': user.position,\n                    'department': user.department_obj.name if user.department_obj else None,\n                    'department_id': user.department_id,\n                    'profile_image': user.profile_image,\n                    'skills': user_skills\n                })\n\n        # Build skills summary\n        skills_summary = []\n        for skill in skills:\n            # Count users by proficiency level\n            level_counts = {1: 0, 2: 0, 3: 0, 4: 0, 5: 0}\n            total_users = 0\n            avg_level = 0\n\n            for user_data in matrix_data:\n                for user_skill in user_data['skills']:\n                    if user_skill['skill_id'] == skill.id and user_skill['proficiency_level'] > 0:\n                        level_counts[user_skill['proficiency_level']] += 1\n                        total_users += 1\n                        avg_level += user_skill['proficiency_level']\n\n            avg_level = round(avg_level / total_users, 1) if total_users > 0 else 0\n\n            skills_summary.append({\n                'id': skill.id,\n                'name': skill.name,\n                'category': skill.category,\n                'description': skill.description,\n                'total_users': total_users,\n                'avg_level': avg_level,\n                'level_distribution': level_counts\n            })\n\n        # Calculate statistics\n        stats = {\n            'total_users': len(matrix_data),\n            'total_skills': len(skills),\n            'total_skill_assignments': sum(len(user['skills']) for user in matrix_data),\n            'avg_skills_per_user': round(\n                sum(len([s for s in user['skills'] if s['proficiency_level'] > 0]) for user in matrix_data) / len(matrix_data), 1\n            ) if matrix_data else 0,\n            'skill_coverage': {\n                'beginner': sum(1 for skill in skills_summary if skill['avg_level'] >= 1 and skill['avg_level'] < 2),\n                'intermediate': sum(1 for skill in skills_summary if skill['avg_level'] >= 2 and skill['avg_level'] < 4),\n                'advanced': sum(1 for skill in skills_summary if skill['avg_level'] >= 4),\n                'no_coverage': sum(1 for skill in skills_summary if skill['total_users'] == 0)\n            }\n        }\n\n        # Get available departments and categories for filters\n        departments = Department.query.filter(Department.is_active == True).order_by(Department.name).all()\n        categories = db.session.query(Skill.category).distinct().filter(Skill.category.isnot(None)).all()\n\n        return api_response(\n            data={\n                'matrix': matrix_data,\n                'skills_summary': skills_summary,\n                'stats': stats,\n                'filters': {\n                    'departments': [{'id': d.id, 'name': d.name} for d in departments],\n                    'categories': [cat[0] for cat in categories if cat[0]]\n                }\n            },\n            message=f\"Retrieved skills matrix for {len(matrix_data)} users and {len(skills)} skills\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/skills', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef create_skill():\n    \"\"\"\n    Create a new skill.\n    \"\"\"\n    try:\n        data = request.get_json()\n\n        # Validate required fields\n        if not data.get('name'):\n            return api_response(\n                success=False,\n                message='Nome competenza richiesto',\n                status_code=400\n            )\n\n        # Check if skill name already exists\n        existing_skill = Skill.query.filter_by(name=data['name']).first()\n        if existing_skill:\n            return api_response(\n                success=False,\n                message='Una competenza con questo nome esiste già',\n                status_code=400\n            )\n\n        # Create new skill\n        skill = Skill(\n            name=data['name'],\n            category=data.get('category', ''),\n            description=data.get('description', '')\n        )\n\n        db.session.add(skill)\n        db.session.commit()\n\n        # Return created skill data\n        skill_data = {\n            'id': skill.id,\n            'name': skill.name,\n            'category': skill.category,\n            'description': skill.description,\n            'user_count': 0\n        }\n\n        return api_response(\n            data={'skill': skill_data},\n            message=f\"Competenza '{skill.name}' creata con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/skills/<int:skill_id>', methods=['PUT'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef update_skill(skill_id):\n    \"\"\"\n    Update an existing skill.\n    \"\"\"\n    try:\n        skill = Skill.query.get_or_404(skill_id)\n        data = request.get_json()\n\n        # Validate name if provided\n        if 'name' in data and data['name']:\n            # Check if name already exists (excluding current skill)\n            existing_skill = Skill.query.filter(\n                Skill.name == data['name'],\n                Skill.id != skill_id\n            ).first()\n            if existing_skill:\n                return api_response(\n                    success=False,\n                    message='Una competenza con questo nome esiste già',\n                    status_code=400\n                )\n            skill.name = data['name']\n\n        # Update other fields\n        if 'category' in data:\n            skill.category = data['category']\n        if 'description' in data:\n            skill.description = data['description']\n\n        db.session.commit()\n\n        # Get user count for response\n        user_count = UserSkill.query.filter_by(skill_id=skill.id).count()\n\n        skill_data = {\n            'id': skill.id,\n            'name': skill.name,\n            'category': skill.category,\n            'description': skill.description,\n            'user_count': user_count\n        }\n\n        return api_response(\n            data={'skill': skill_data},\n            message=f\"Competenza '{skill.name}' aggiornata con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/skills/<int:skill_id>', methods=['DELETE'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef delete_skill(skill_id):\n    \"\"\"\n    Delete a skill and all associated user skills.\n    \"\"\"\n    try:\n        skill = Skill.query.get_or_404(skill_id)\n\n        # Check if skill is assigned to users\n        user_skills_count = UserSkill.query.filter_by(skill_id=skill.id).count()\n        if user_skills_count > 0:\n            return api_response(\n                success=False,\n                message=f'Impossibile eliminare la competenza. È assegnata a {user_skills_count} dipendenti.',\n                status_code=400\n            )\n\n        skill_name = skill.name\n        db.session.delete(skill)\n        db.session.commit()\n\n        return api_response(\n            message=f\"Competenza '{skill_name}' eliminata con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n# ADMIN ENDPOINTS\n\n@api_personnel.route('/admin/users', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef create_user():\n    \"\"\"\n    Create a new user with full profile data.\n    \"\"\"\n    try:\n        from datetime import datetime\n        data = request.get_json()\n\n        # Validate required fields\n        required_fields = ['username', 'email', 'first_name', 'last_name']\n        for field in required_fields:\n            if not data.get(field):\n                return api_response(\n                    success=False,\n                    message=f'Campo {field} richiesto',\n                    status_code=400\n                )\n\n        # Check if username or email already exists\n        existing_user = User.query.filter(\n            or_(User.username == data['username'], User.email == data['email'])\n        ).first()\n        if existing_user:\n            return api_response(\n                success=False,\n                message='Username o email già esistenti',\n                status_code=400\n            )\n\n        # Create new user\n        user = User(\n            username=data['username'],\n            email=data['email'],\n            first_name=data['first_name'],\n            last_name=data['last_name'],\n            role=data.get('role', 'employee'),\n            department_id=data.get('department_id'),\n            position=data.get('position'),\n            hire_date=datetime.strptime(data['hire_date'], '%Y-%m-%d').date() if data.get('hire_date') else None,\n            phone=data.get('phone'),\n            is_active=data.get('is_active', True)\n        )\n\n        # Set password if provided\n        if data.get('password'):\n            user.set_password(data['password'])\n        else:\n            # Generate temporary password\n            import secrets\n            temp_password = secrets.token_urlsafe(12)\n            user.set_password(temp_password)\n\n        db.session.add(user)\n        db.session.flush()  # Get user ID\n\n        # Create profile if profile data provided\n        profile_fields = [\n            'employee_id', 'job_title', 'birth_date', 'address', 'employment_type',\n            'work_location', 'salary', 'salary_currency', 'probation_end_date',\n            'contract_end_date', 'notice_period_days', 'weekly_hours', 'daily_hours',\n            'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship'\n        ]\n\n        if any(field in data for field in profile_fields):\n            profile = UserProfile(\n                user_id=user.id,\n                employee_id=data.get('employee_id'),\n                job_title=data.get('job_title'),\n                birth_date=datetime.strptime(data['birth_date'], '%Y-%m-%d').date() if data.get('birth_date') else None,\n                address=data.get('address'),\n                employment_type=data.get('employment_type', 'full_time'),\n                work_location=data.get('work_location'),\n                salary=data.get('salary'),\n                salary_currency=data.get('salary_currency', 'EUR'),\n                probation_end_date=datetime.strptime(data['probation_end_date'], '%Y-%m-%d').date() if data.get('probation_end_date') else None,\n                contract_end_date=datetime.strptime(data['contract_end_date'], '%Y-%m-%d').date() if data.get('contract_end_date') else None,\n                notice_period_days=data.get('notice_period_days', 30),\n                weekly_hours=data.get('weekly_hours', 40.0),\n                daily_hours=data.get('daily_hours', 8.0),\n                emergency_contact_name=data.get('emergency_contact_name'),\n                emergency_contact_phone=data.get('emergency_contact_phone'),\n                emergency_contact_relationship=data.get('emergency_contact_relationship')\n            )\n\n            db.session.add(profile)\n\n        db.session.commit()\n\n        # Return created user data\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'full_name': user.full_name,\n            'first_name': user.first_name,\n            'last_name': user.last_name,\n            'role': user.role,\n            'department_id': user.department_id,\n            'position': user.position,\n            'hire_date': user.hire_date.isoformat() if user.hire_date else None,\n            'phone': user.phone,\n            'is_active': user.is_active,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'updated_at': profile.updated_at.isoformat() if profile.updated_at else None,\n        }\n\n        return api_response(\n            data={'user': user_data},\n            message=f\"Utente '{user.full_name}' creato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/admin/users/<int:user_id>', methods=['DELETE'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef delete_user(user_id):\n    \"\"\"\n    Delete a user (soft delete by setting is_active=False).\n    \"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n\n        # Prevent self-deletion\n        if user.id == current_user.id:\n            return api_response(\n                success=False,\n                message='Non puoi eliminare il tuo stesso account',\n                status_code=400\n            )\n\n        # Soft delete\n        user.is_active = False\n        db.session.commit()\n\n        return api_response(\n            message=f\"Utente '{user.full_name}' disattivato con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/admin/users/<int:user_id>/reset-password', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef reset_user_password(user_id):\n    \"\"\"\n    Reset user password to a temporary one.\n    \"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n\n        # Generate temporary password\n        import secrets\n        temp_password = secrets.token_urlsafe(12)\n        user.set_password(temp_password)\n\n        db.session.commit()\n\n        return api_response(\n            data={'temporary_password': temp_password},\n            message=f\"Password di '{user.full_name}' resettata con successo\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/admin/analytics', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef get_admin_analytics():\n    \"\"\"\n    Get comprehensive analytics for personnel administration.\n    \"\"\"\n    try:\n        from datetime import datetime, timedelta\n        from sqlalchemy import extract\n\n        # Basic statistics\n        total_users = User.query.filter_by(is_active=True).count()\n        total_departments = Department.query.filter_by(is_active=True).count()\n\n        # Users by role\n        users_by_role = db.session.query(\n            User.role, func.count(User.id)\n        ).filter_by(is_active=True).group_by(User.role).all()\n\n        # Users by department\n        users_by_dept = db.session.query(\n            Department.name, func.count(User.id)\n        ).join(User, Department.id == User.department_id)\\\n         .filter(User.is_active == True, Department.is_active == True)\\\n         .group_by(Department.name).all()\n\n        # Contract expiring soon (next 90 days)\n        today = datetime.now().date()\n        expiring_soon = today + timedelta(days=90)\n\n        expiring_contracts = db.session.query(User, UserProfile)\\\n            .join(UserProfile, User.id == UserProfile.user_id)\\\n            .filter(\n                User.is_active == True,\n                UserProfile.contract_end_date.isnot(None),\n                UserProfile.contract_end_date <= expiring_soon,\n                UserProfile.contract_end_date >= today\n            ).all()\n\n        # Probation ending soon (next 30 days)\n        probation_ending = today + timedelta(days=30)\n\n        ending_probation = db.session.query(User, UserProfile)\\\n            .join(UserProfile, User.id == UserProfile.user_id)\\\n            .filter(\n                User.is_active == True,\n                UserProfile.probation_end_date.isnot(None),\n                UserProfile.probation_end_date <= probation_ending,\n                UserProfile.probation_end_date >= today\n            ).all()\n\n        # Employment types distribution\n        employment_types = db.session.query(\n            UserProfile.employment_type, func.count(UserProfile.id)\n        ).join(User, UserProfile.user_id == User.id)\\\n         .filter(User.is_active == True)\\\n         .group_by(UserProfile.employment_type).all()\n\n        # Average salary by department (if salary data exists)\n        avg_salary_by_dept = db.session.query(\n            Department.name, func.avg(UserProfile.salary)\n        ).join(User, Department.id == User.department_id)\\\n         .join(UserProfile, User.id == UserProfile.user_id)\\\n         .filter(\n             User.is_active == True,\n             Department.is_active == True,\n             UserProfile.salary.isnot(None)\n         ).group_by(Department.name).all()\n\n        # Recent hires (last 90 days)\n        recent_hire_date = today - timedelta(days=90)\n        recent_hires = User.query.filter(\n            User.is_active == True,\n            User.hire_date >= recent_hire_date\n        ).count()\n\n        analytics_data = {\n            'overview': {\n                'total_users': total_users,\n                'total_departments': total_departments,\n                'recent_hires': recent_hires\n            },\n            'users_by_role': [{'role': role, 'count': count} for role, count in users_by_role],\n            'users_by_department': [{'department': dept, 'count': count} for dept, count in users_by_dept],\n            'employment_types': [{'type': emp_type or 'Non specificato', 'count': count} for emp_type, count in employment_types],\n            'avg_salary_by_department': [{'department': dept, 'avg_salary': float(avg_sal) if avg_sal else 0} for dept, avg_sal in avg_salary_by_dept],\n            'alerts': {\n                'expiring_contracts': [\n                    {\n                        'user_id': user.id,\n                        'full_name': user.full_name,\n                        'contract_end_date': profile.contract_end_date.isoformat(),\n                        'days_remaining': (profile.contract_end_date - today).days\n                    }\n                    for user, profile in expiring_contracts\n                ],\n                'ending_probation': [\n                    {\n                        'user_id': user.id,\n                        'full_name': user.full_name,\n                        'probation_end_date': profile.probation_end_date.isoformat(),\n                        'days_remaining': (profile.probation_end_date - today).days\n                    }\n                    for user, profile in ending_probation\n                ]\n            }\n        }\n\n        return api_response(\n            data=analytics_data,\n            message=\"Analytics data retrieved successfully\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/export', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef export_personnel_data():\n    \"\"\"\n    Export personnel data to CSV format.\n    \"\"\"\n    try:\n        import csv\n        import io\n        from datetime import datetime\n\n        # Get all active users with their profiles\n        users_query = db.session.query(User, UserProfile, Department)\\\n            .outerjoin(UserProfile, User.id == UserProfile.user_id)\\\n            .outerjoin(Department, User.department_id == Department.id)\\\n            .filter(User.is_active == True)\\\n            .order_by(User.last_name, User.first_name)\n\n        users_data = users_query.all()\n\n        # Create CSV in memory\n        output = io.StringIO()\n        writer = csv.writer(output)\n\n        # Write header\n        headers = [\n            'ID', 'Nome', 'Cognome', 'Email', 'Username', 'Telefono',\n            'Ruolo', 'Dipartimento', 'Posizione', 'Data Assunzione',\n            'Tipo Contratto', 'Modalità Lavoro', 'Ore Settimanali',\n            'Stipendio', 'Valuta', 'Fine Periodo Prova', 'Scadenza Contratto',\n            'Giorni Preavviso', 'Contatto Emergenza', 'Tel. Emergenza', 'Relazione Emergenza',\n            'Indirizzo', 'Data Nascita', 'Creato il'\n        ]\n        writer.writerow(headers)\n\n        # Write data rows\n        for user, profile, department in users_data:\n            row = [\n                user.id,\n                user.first_name,\n                user.last_name,\n                user.email,\n                user.username,\n                user.phone or '',\n                user.role,\n                department.name if department else '',\n                user.position or '',\n                user.hire_date.isoformat() if user.hire_date else '',\n                profile.employment_type if profile else '',\n                profile.work_location if profile else '',\n                profile.weekly_hours if profile else '',\n                profile.salary if profile else '',\n                profile.salary_currency if profile else '',\n                profile.probation_end_date.isoformat() if profile and profile.probation_end_date else '',\n                profile.contract_end_date.isoformat() if profile and profile.contract_end_date else '',\n                profile.notice_period_days if profile else '',\n                profile.emergency_contact_name if profile else '',\n                profile.emergency_contact_phone if profile else '',\n                profile.emergency_contact_relationship if profile else '',\n                profile.address if profile else '',\n                profile.birth_date.isoformat() if profile and profile.birth_date else '',\n                user.created_at.isoformat() if user.created_at else ''\n            ]\n            writer.writerow(row)\n\n        # Prepare response\n        output.seek(0)\n        csv_data = output.getvalue()\n        output.close()\n\n        # Create response with CSV data\n        response = make_response(csv_data)\n        response.headers['Content-Type'] = 'text/csv'\n        response.headers['Content-Disposition'] = f'attachment; filename=personnel-export-{datetime.now().strftime(\"%Y%m%d\")}.csv'\n\n        return response\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/export/contacts', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef export_contacts_data():\n    \"\"\"\n    Export only contact information to CSV format.\n    \"\"\"\n    try:\n        import csv\n        import io\n        from datetime import datetime\n\n        # Get all active users with basic contact info\n        users = User.query.filter_by(is_active=True)\\\n            .order_by(User.last_name, User.first_name).all()\n\n        # Create CSV in memory\n        output = io.StringIO()\n        writer = csv.writer(output)\n\n        # Write header\n        headers = ['Nome', 'Cognome', 'Email', 'Telefono', 'Dipartimento', 'Posizione']\n        writer.writerow(headers)\n\n        # Write data\n        for user in users:\n            row = [\n                user.first_name or '',\n                user.last_name or '',\n                user.email or '',\n                user.phone or '',\n                user.department_obj.name if user.department_obj else '',\n                user.position or ''\n            ]\n            writer.writerow(row)\n\n        # Prepare response\n        output.seek(0)\n        csv_data = output.getvalue()\n        output.close()\n\n        # Create response with CSV data\n        response = make_response(csv_data)\n        response.headers['Content-Type'] = 'text/csv'\n        response.headers['Content-Disposition'] = f'attachment; filename=contacts-export-{datetime.now().strftime(\"%Y%m%d\")}.csv'\n\n        return response\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/import', methods=['POST'])\n@api_login_required\n@api_permission_required(PERMISSION_MANAGE_USERS)\ndef import_personnel_data():\n    \"\"\"\n    Import personnel data from CSV file.\n    \"\"\"\n    try:\n        import csv\n        import io\n        from datetime import datetime\n\n        # Check if file was uploaded\n        if 'file' not in request.files:\n            return api_response(\n                success=False,\n                message='Nessun file caricato',\n                status_code=400\n            )\n\n        file = request.files['file']\n        if file.filename == '':\n            return api_response(\n                success=False,\n                message='Nessun file selezionato',\n                status_code=400\n            )\n\n        if not file.filename.lower().endswith('.csv'):\n            return api_response(\n                success=False,\n                message='Solo file CSV sono supportati',\n                status_code=400\n            )\n\n        # Read CSV content\n        stream = io.StringIO(file.stream.read().decode(\"UTF8\"), newline=None)\n        csv_input = csv.DictReader(stream)\n\n        imported_count = 0\n        total_count = 0\n        errors = []\n\n        for row_num, row in enumerate(csv_input, start=2):  # Start from 2 (header is row 1)\n            total_count += 1\n\n            try:\n                # Validate required fields\n                if not row.get('email') or not row.get('first_name') or not row.get('last_name'):\n                    errors.append(f\"Riga {row_num}: Email, nome e cognome sono obbligatori\")\n                    continue\n\n                # Check if user already exists\n                existing_user = User.query.filter_by(email=row['email']).first()\n                if existing_user:\n                    errors.append(f\"Riga {row_num}: Email {row['email']} già esistente\")\n                    continue\n\n                # Create new user\n                user = User(\n                    username=row.get('email'),  # Use email as username if not provided\n                    email=row['email'],\n                    first_name=row['first_name'],\n                    last_name=row['last_name'],\n                    phone=row.get('phone', ''),\n                    role=row.get('role', 'employee'),\n                    is_active=row.get('is_active', 'true').lower() == 'true',\n                    position=row.get('position', '')\n                )\n\n                # Set department if provided\n                if row.get('department_id'):\n                    try:\n                        dept_id = int(row['department_id'])\n                        dept = Department.query.get(dept_id)\n                        if dept:\n                            user.department_id = dept_id\n                        else:\n                            errors.append(f\"Riga {row_num}: Dipartimento ID {dept_id} non trovato\")\n                    except ValueError:\n                        errors.append(f\"Riga {row_num}: ID dipartimento non valido\")\n\n                # Set password (temporary)\n                user.set_password('TempPassword123!')\n\n                db.session.add(user)\n                db.session.flush()  # Get user ID\n\n                # Create profile if additional data is provided\n                if any(row.get(field) for field in ['hire_date', 'employment_type', 'salary']):\n                    profile = UserProfile(user_id=user.id)\n\n                    # Set hire date\n                    if row.get('hire_date'):\n                        try:\n                            profile.hire_date = datetime.strptime(row['hire_date'], '%Y-%m-%d').date()\n                        except ValueError:\n                            errors.append(f\"Riga {row_num}: Formato data assunzione non valido (usa YYYY-MM-DD)\")\n\n                    # Set employment type\n                    if row.get('employment_type'):\n                        profile.employment_type = row['employment_type']\n\n                    # Set salary\n                    if row.get('salary'):\n                        try:\n                            profile.salary = float(row['salary'])\n                        except ValueError:\n                            errors.append(f\"Riga {row_num}: Stipendio non valido\")\n\n                    db.session.add(profile)\n\n                imported_count += 1\n\n            except Exception as e:\n                errors.append(f\"Riga {row_num}: Errore durante l'importazione - {str(e)}\")\n                continue\n\n        # Commit all changes\n        db.session.commit()\n\n        return api_response(\n            data={\n                'imported': imported_count,\n                'total': total_count,\n                'errors': errors[:10]  # Limit to first 10 errors\n            },\n            message=f\"Import completato: {imported_count}/{total_count} dipendenti importati\"\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/verify', methods=['GET'])\n@api_login_required\n@api_permission_required(PERMISSION_VIEW_PERSONNEL_DATA)\ndef verify_data_integrity():\n    \"\"\"\n    Verify data integrity and return issues found.\n    \"\"\"\n    try:\n        issues = []\n\n        # Check for users without email\n        users_no_email = User.query.filter(\n            or_(User.email == None, User.email == '')\n        ).filter_by(is_active=True).count()\n\n        if users_no_email > 0:\n            issues.append(f\"{users_no_email} dipendenti attivi senza email\")\n\n        # Check for users without names\n        users_no_name = User.query.filter(\n            or_(\n                and_(User.first_name == None, User.last_name == None),\n                and_(User.first_name == '', User.last_name == '')\n            )\n        ).filter_by(is_active=True).count()\n\n        if users_no_name > 0:\n            issues.append(f\"{users_no_name} dipendenti attivi senza nome\")\n\n        # Check for users without department\n        users_no_dept = User.query.filter_by(\n            department_id=None,\n            is_active=True\n        ).count()\n\n        if users_no_dept > 0:\n            issues.append(f\"{users_no_dept} dipendenti attivi senza dipartimento\")\n\n        # Check for departments without manager\n        depts_no_manager = Department.query.filter_by(\n            manager_id=None,\n            is_active=True\n        ).count()\n\n        if depts_no_manager > 0:\n            issues.append(f\"{depts_no_manager} dipartimenti attivi senza manager\")\n\n        # Check for orphaned user profiles\n        orphaned_profiles = db.session.query(UserProfile).outerjoin(User).filter(\n            User.id == None\n        ).count()\n\n        if orphaned_profiles > 0:\n            issues.append(f\"{orphaned_profiles} profili utente orfani\")\n\n        # Check for duplicate emails\n        duplicate_emails = db.session.query(User.email, func.count(User.id))\\\n            .filter(User.email != None, User.email != '')\\\n            .group_by(User.email)\\\n            .having(func.count(User.id) > 1).count()\n\n        if duplicate_emails > 0:\n            issues.append(f\"{duplicate_emails} email duplicate\")\n\n        return api_response(\n            data={'issues': issues},\n            message=f\"Verifica completata: {len(issues)} problemi trovati\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n# ============================================================================\n# CV AND DOCUMENTS MANAGEMENT API ENDPOINTS\n# ============================================================================\n\n@api_personnel.route('/users/<int:user_id>/cv/upload', methods=['POST'])\n@api_login_required\ndef upload_cv(user_id):\n    \"\"\"\n    Upload CV for a user with AI analysis.\n    \"\"\"\n    try:\n        user_to_edit = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/manager/HR\n        if (user_to_edit.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di modificare il CV di questo utente',\n                status_code=403\n            )\n\n        # Check if file was uploaded\n        if 'cv_file' not in request.files:\n            return api_response(\n                success=False,\n                message='Nessun file CV caricato',\n                status_code=400\n            )\n\n        file = request.files['cv_file']\n        if file.filename == '':\n            return api_response(\n                success=False,\n                message='Nessun file selezionato',\n                status_code=400\n            )\n\n        # Validate file\n        if not is_valid_cv_file(file.filename):\n            return api_response(\n                success=False,\n                message='Formato file non supportato. Usa PDF, DOCX, DOC o TXT',\n                status_code=400\n            )\n\n        # Check file size\n        if get_file_size_mb(file) > 10:\n            return api_response(\n                success=False,\n                message='File troppo grande. Dimensione massima: 10MB',\n                status_code=400\n            )\n\n        # Create upload directory if it doesn't exist\n        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'cv')\n        os.makedirs(upload_dir, exist_ok=True)\n\n        # Generate secure filename\n        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n        filename = f\"cv_{user_to_edit.id}_{timestamp}_{secure_filename(file.filename)}\"\n        file_path = os.path.join('cv', filename)\n        full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)\n\n        # Save file\n        file.save(full_path)\n\n        # Get or create profile\n        profile = user_to_edit.profile\n        if not profile:\n            profile = UserProfile(user_id=user_to_edit.id)\n            db.session.add(profile)\n            db.session.flush()\n\n        # Remove old CV if exists\n        if profile.current_cv_path:\n            old_cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], profile.current_cv_path)\n            if os.path.exists(old_cv_path):\n                try:\n                    os.remove(old_cv_path)\n                except OSError:\n                    pass  # Ignore if file can't be deleted\n\n        # Update profile\n        profile.current_cv_path = file_path\n        profile.cv_last_updated = datetime.utcnow()\n\n        # AI Analysis if requested\n        analyze_skills = request.form.get('analyze_skills', 'true').lower() == 'true'\n        analysis_result = None\n\n        if analyze_skills:\n            cv_text = extract_text_from_cv(full_path)\n            if cv_text:\n                try:\n                    analysis_result = extract_skills_from_cv(cv_text)\n                    if 'error' not in analysis_result:\n                        profile.cv_analysis_data = json.dumps(analysis_result)\n\n                        # Auto-add skills if requested\n                        auto_add_skills = request.form.get('auto_add_skills', 'false').lower() == 'true'\n                        if auto_add_skills and 'skills' in analysis_result:\n                            added_skills = []\n                            for skill_data in analysis_result['skills'][:10]:  # Limit to first 10\n                                skill_name = skill_data.get('name', '').strip()\n                                if not skill_name:\n                                    continue\n\n                                # Find or create skill\n                                skill = Skill.query.filter_by(name=skill_name).first()\n                                if not skill:\n                                    skill = Skill(\n                                        name=skill_name,\n                                        category=skill_data.get('category', 'Generale')\n                                    )\n                                    db.session.add(skill)\n                                    db.session.flush()\n\n                                # Check if user already has this skill\n                                existing_user_skill = UserSkill.query.filter_by(\n                                    user_id=user_to_edit.id,\n                                    skill_id=skill.id\n                                ).first()\n\n                                if not existing_user_skill:\n                                    # Add skill to user\n                                    user_skill = UserSkill(\n                                        user_id=user_to_edit.id,\n                                        skill_id=skill.id,\n                                        proficiency_level=skill_data.get('level', 3),\n                                        years_experience=skill_data.get('years_experience', 0),\n                                        notes=f\"Estratto automaticamente dal CV - {skill_data.get('context', '')}\"\n                                    )\n                                    db.session.add(user_skill)\n                                    added_skills.append(skill_name)\n\n                            analysis_result['added_skills'] = added_skills\n\n                except Exception as ai_error:\n                    # Don't fail the upload if AI analysis fails\n                    analysis_result = {'error': f'Errore analisi AI: {str(ai_error)}'}\n\n        # Recalculate profile completion\n        profile.calculate_completion()\n        db.session.commit()\n\n        # Return complete user data with updated profile info\n        user_data = {\n            'id': user_to_edit.id,\n            'username': user_to_edit.username,\n            'email': user_to_edit.email,\n            'first_name': user_to_edit.first_name,\n            'last_name': user_to_edit.last_name,\n            'full_name': user_to_edit.full_name,\n            'role': user_to_edit.role,\n            'is_active': user_to_edit.is_active,\n            'department_id': user_to_edit.department_id,\n            'hire_date': user_to_edit.hire_date.isoformat() if user_to_edit.hire_date else None,\n            'phone': user_to_edit.phone,\n            'last_login': user_to_edit.last_login.isoformat() if user_to_edit.last_login else None,\n            'created_at': user_to_edit.created_at.isoformat() if user_to_edit.created_at else None,\n            'updated_at': profile.updated_at.isoformat() if profile.updated_at else None,\n            \n            'profile': {\n                'employee_id': profile.employee_id,\n                'job_title': profile.job_title,\n                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,\n                'address': profile.address,\n                'emergency_contact_name': profile.emergency_contact_name,\n                'emergency_contact_phone': profile.emergency_contact_phone,\n                'emergency_contact_relationship': profile.emergency_contact_relationship,\n                'employment_type': profile.employment_type,\n                'work_location': profile.work_location,\n                'weekly_hours': profile.weekly_hours,\n                'daily_hours': profile.daily_hours,\n                'current_cv_path': profile.current_cv_path,\n                'cv_last_updated': profile.cv_last_updated.isoformat() if profile.cv_last_updated else None,\n                'cv_analysis_data': profile.cv_analysis_data,\n                'profile_completion': profile.profile_completion,\n                'notes': profile.notes if current_user.role in ['admin', 'human_resources'] else None,\n                'created_at': profile.created_at.isoformat() if profile.created_at else None,\n                'updated_at': profile.updated_at.isoformat() if profile.updated_at else None\n            }\n        }\n\n        return api_response(\n            data=user_data,\n            message='CV caricato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>/cv/download', methods=['GET'])\n@api_login_required\ndef download_cv(user_id):\n    \"\"\"\n    Download CV file for a user.\n    \"\"\"\n    try:\n        user_to_view = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or view personnel data permission\n        if (user_to_view.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di scaricare questo CV',\n                status_code=403\n            )\n\n        if not user_to_view.profile or not user_to_view.profile.current_cv_path:\n            return api_response(\n                success=False,\n                message='Nessun CV disponibile per questo utente',\n                status_code=404\n            )\n\n        cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], user_to_view.profile.current_cv_path)\n\n        if not os.path.exists(cv_path):\n            return api_response(\n                success=False,\n                message='File CV non trovato sul server',\n                status_code=404\n            )\n\n        # Get original filename from path\n        original_filename = os.path.basename(user_to_view.profile.current_cv_path)\n        # Clean filename for download\n        download_filename = f\"CV_{user_to_view.full_name}_{original_filename.split('_')[-1]}\"\n\n        return send_file(\n            cv_path,\n            as_attachment=True,\n            download_name=download_filename\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>/cv', methods=['DELETE'])\n@api_login_required\ndef delete_cv(user_id):\n    \"\"\"\n    Delete CV file for a user.\n    \"\"\"\n    try:\n        user_to_edit = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/manager/HR\n        if (user_to_edit.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di cancellare il CV di questo utente',\n                status_code=403\n            )\n\n        if not user_to_edit.profile or not user_to_edit.profile.current_cv_path:\n            return api_response(\n                success=False,\n                message='Nessun CV da cancellare',\n                status_code=404\n            )\n\n        # Remove file from filesystem\n        cv_path = os.path.join(current_app.config['UPLOAD_FOLDER'], user_to_edit.profile.current_cv_path)\n        if os.path.exists(cv_path):\n            try:\n                os.remove(cv_path)\n            except OSError:\n                pass  # Ignore if file can't be deleted\n\n        # Clear CV data from profile\n        profile = user_to_edit.profile\n        profile.current_cv_path = None\n        profile.cv_last_updated = None\n        profile.cv_analysis_data = None\n\n        # Recalculate profile completion\n        profile.calculate_completion()\n        db.session.commit()\n\n        return api_response(\n            data={'profile_completion': profile.profile_completion},\n            message='CV cancellato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>/cv/analysis', methods=['GET'])\n@api_login_required\ndef get_cv_analysis(user_id):\n    \"\"\"\n    Get CV analysis data for a user.\n    \"\"\"\n    try:\n        user_to_view = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or view personnel data permission\n        if (user_to_view.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di visualizzare l\\'analisi CV di questo utente',\n                status_code=403\n            )\n\n        if not user_to_view.profile or not user_to_view.profile.cv_analysis_data:\n            return api_response(\n                success=False,\n                message='Nessuna analisi CV disponibile per questo utente',\n                status_code=404\n            )\n\n        try:\n            analysis_data = json.loads(user_to_view.profile.cv_analysis_data)\n        except json.JSONDecodeError:\n            return api_response(\n                success=False,\n                message='Dati di analisi CV corrotti',\n                status_code=500\n            )\n\n        return api_response(\n            data={\n                'analysis': analysis_data,\n                'cv_last_updated': user_to_view.profile.cv_last_updated.isoformat() if user_to_view.profile.cv_last_updated else None\n            },\n            message='Analisi CV recuperata con successo'\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_personnel.route('/users/<int:user_id>/skills/from-cv', methods=['POST'])\n@api_login_required\ndef add_skills_from_cv_analysis(user_id):\n    \"\"\"\n    Add skills to user profile from CV analysis data.\n    \"\"\"\n    try:\n        user_to_edit = User.query.get_or_404(user_id)\n\n        # Permission check: own profile or admin/manager/HR\n        if (user_to_edit.id != current_user.id and\n            not current_user.role in ['admin', 'manager', 'human_resources']):\n            return api_response(\n                success=False,\n                message='Non hai il permesso di modificare le competenze di questo utente',\n                status_code=403\n            )\n\n        if not user_to_edit.profile or not user_to_edit.profile.cv_analysis_data:\n            return api_response(\n                success=False,\n                message='Nessuna analisi CV disponibile per questo utente',\n                status_code=404\n            )\n\n        try:\n            analysis_data = json.loads(user_to_edit.profile.cv_analysis_data)\n        except json.JSONDecodeError:\n            return api_response(\n                success=False,\n                message='Dati di analisi CV corrotti',\n                status_code=500\n            )\n\n        if 'skills' not in analysis_data:\n            return api_response(\n                success=False,\n                message='Nessuna competenza trovata nell\\'analisi CV',\n                status_code=404\n            )\n\n        # Get selected skills from request\n        data = request.get_json()\n        selected_skills = data.get('selected_skills', [])\n\n        if not selected_skills:\n            return api_response(\n                success=False,\n                message='Nessuna competenza selezionata',\n                status_code=400\n            )\n\n        added_skills = []\n        skipped_skills = []\n\n        for skill_index in selected_skills:\n            if skill_index >= len(analysis_data['skills']):\n                continue\n\n            skill_data = analysis_data['skills'][skill_index]\n            skill_name = skill_data.get('name', '').strip()\n\n            if not skill_name:\n                continue\n\n            # Find or create skill\n            skill = Skill.query.filter_by(name=skill_name).first()\n            if not skill:\n                skill = Skill(\n                    name=skill_name,\n                    category=skill_data.get('category', 'Generale'),\n                    description=skill_data.get('description', '')\n                )\n                db.session.add(skill)\n                db.session.flush()\n\n            # Check if user already has this skill\n            existing_user_skill = UserSkill.query.filter_by(\n                user_id=user_to_edit.id,\n                skill_id=skill.id\n            ).first()\n\n            if existing_user_skill:\n                skipped_skills.append(skill_name)\n                continue\n\n            # Add skill to user\n            user_skill = UserSkill(\n                user_id=user_to_edit.id,\n                skill_id=skill.id,\n                proficiency_level=skill_data.get('level', 3),\n                years_experience=skill_data.get('years_experience', 0),\n                notes=f\"Estratto dal CV - {skill_data.get('context', '')}\"\n            )\n            db.session.add(user_skill)\n            added_skills.append(skill_name)\n\n        db.session.commit()\n\n        return api_response(\n            data={\n                'added_skills': added_skills,\n                'skipped_skills': skipped_skills,\n                'total_added': len(added_skills),\n                'total_skipped': len(skipped_skills)\n            },\n            message=f'Aggiunte {len(added_skills)} competenze al profilo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)"}