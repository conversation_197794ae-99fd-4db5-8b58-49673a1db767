{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/ui/Toast.vue"}, "modifiedCode": "<template>\n  <Teleport to=\"body\">\n    <div v-if=\"visible\" \n         :class=\"[\n           'fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform',\n           typeClasses[type] || typeClasses.success,\n           visible ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'\n         ]\"\n         class=\"max-w-sm\">\n      <div class=\"flex items-center\">\n        <div class=\"flex-shrink-0 mr-3\">\n          <component :is=\"iconComponent\" class=\"w-5 h-5\" />\n        </div>\n        <div class=\"flex-1\">\n          <p class=\"text-sm font-medium\">{{ title }}</p>\n          <p v-if=\"message\" class=\"text-sm opacity-90\">{{ message }}</p>\n        </div>\n        <button v-if=\"closable\" \n                @click=\"close\"\n                class=\"flex-shrink-0 ml-3 opacity-70 hover:opacity-100 transition-opacity\">\n          <svg class=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fill-rule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clip-rule=\"evenodd\"></path>\n          </svg>\n        </button>\n      </div>\n    </div>\n  </Teleport>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\n\nconst props = defineProps({\n  type: {\n    type: String,\n    default: 'success',\n    validator: value => ['success', 'error', 'warning', 'info'].includes(value)\n  },\n  title: {\n    type: String,\n    required: true\n  },\n  message: {\n    type: String,\n    default: ''\n  },\n  duration: {\n    type: Number,\n    default: 3000\n  },\n  closable: {\n    type: Boolean,\n    default: true\n  }\n})\n\nconst emit = defineEmits(['close'])\n\nconst visible = ref(false)\nlet timeoutId = null\n\nconst typeClasses = {\n  success: 'bg-green-500 text-white',\n  error: 'bg-red-500 text-white',\n  warning: 'bg-yellow-500 text-black',\n  info: 'bg-blue-500 text-white'\n}\n\nconst iconComponent = computed(() => {\n  const icons = {\n    success: 'CheckCircleIcon',\n    error: 'XCircleIcon', \n    warning: 'ExclamationTriangleIcon',\n    info: 'InformationCircleIcon'\n  }\n  return icons[props.type] || icons.success\n})\n\nconst CheckCircleIcon = {\n  template: `\n    <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n      <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\"></path>\n    </svg>\n  `\n}\n\nconst XCircleIcon = {\n  template: `\n    <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n      <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clip-rule=\"evenodd\"></path>\n    </svg>\n  `\n}\n\nconst ExclamationTriangleIcon = {\n  template: `\n    <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n      <path fill-rule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\"></path>\n    </svg>\n  `\n}\n\nconst InformationCircleIcon = {\n  template: `\n    <svg fill=\"currentColor\" viewBox=\"0 0 20 20\">\n      <path fill-rule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clip-rule=\"evenodd\"></path>\n    </svg>\n  `\n}\n\nconst show = () => {\n  visible.value = true\n  \n  if (props.duration > 0) {\n    timeoutId = setTimeout(() => {\n      close()\n    }, props.duration)\n  }\n}\n\nconst close = () => {\n  visible.value = false\n  if (timeoutId) {\n    clearTimeout(timeoutId)\n    timeoutId = null\n  }\n  \n  setTimeout(() => {\n    emit('close')\n  }, 300) // Wait for animation to complete\n}\n\nonMounted(() => {\n  // Show toast after mount\n  setTimeout(show, 10)\n})\n\nonUnmounted(() => {\n  if (timeoutId) {\n    clearTimeout(timeoutId)\n  }\n})\n\n// Expose methods for parent components\ndefineExpose({\n  show,\n  close\n})\n</script>\n"}