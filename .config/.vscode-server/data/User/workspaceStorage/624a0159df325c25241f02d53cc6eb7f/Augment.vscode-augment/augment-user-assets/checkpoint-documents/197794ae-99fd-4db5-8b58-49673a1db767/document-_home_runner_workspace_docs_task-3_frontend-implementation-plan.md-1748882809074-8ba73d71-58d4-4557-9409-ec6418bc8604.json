{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task-3/frontend-implementation-plan.md"}, "modifiedCode": "# 🎯 **PIANO IMPLEMENTAZIONE FRONTEND TIMESHEET + CRM**\n\n## 📋 **OVERVIEW ARCHITETTURA**\n\n### **🎯 OBIETTIVI:**\n- Menu Timesheet separato con workflow approvazioni centralizzato\n- Bulk approval con AI anomaly detection\n- Sistema notifiche in-app (navbar)\n- Gerarchia manager per approvazioni\n- Integrazione CRM completa\n\n---\n\n## 🚀 **FASE 1: STRUTTURA BASE TIMESHEET**\n\n### **1.1 Nuove Rotte Router**\n\n**File:** `frontend/src/router/index.js`\n\n```javascript\n// Timesheet routes - Menu separato\n{\n  path: '/app/timesheet',\n  meta: { requiresAuth: true },\n  children: [\n    {\n      path: '',\n      name: 'timesheet-dashboard',\n      component: () => import('@/views/timesheet/TimesheetDashboard.vue'),\n      meta: { requiredPermission: 'view_timesheet' }\n    },\n    {\n      path: 'entries',\n      name: 'timesheet-entries',\n      component: () => import('@/views/timesheet/TimesheetEntries.vue'),\n      meta: { requiredPermission: 'manage_timesheet' }\n    },\n    {\n      path: 'approvals',\n      name: 'timesheet-approvals',\n      component: () => import('@/views/timesheet/TimesheetApprovals.vue'),\n      meta: { requiredPermission: 'approve_timesheets' }\n    },\n    {\n      path: 'time-off',\n      name: 'time-off-requests',\n      component: () => import('@/views/timesheet/TimeOffRequests.vue'),\n      meta: { requiredPermission: 'manage_time_off' }\n    },\n    {\n      path: 'reports',\n      name: 'timesheet-reports',\n      component: () => import('@/views/timesheet/TimesheetReports.vue'),\n      meta: { requiredPermission: 'view_timesheet_reports' }\n    }\n  ]\n}\n```\n\n### **1.2 Aggiornamento Sidebar Navigation**\n\n**File:** `frontend/src/components/layout/SidebarNavigation.vue`\n\n```javascript\n// Aggiungere menu Timesheet separato\n{\n  name: 'Timesheet',\n  icon: 'clock',\n  children: [\n    { \n      name: 'Dashboard', \n      path: '/app/timesheet', \n      icon: 'chart-bar',\n      permission: 'view_timesheet'\n    },\n    { \n      name: 'Registra Ore', \n      path: '/app/timesheet/entries', \n      icon: 'plus-circle',\n      permission: 'manage_timesheet'\n    },\n    { \n      name: 'Approvazioni', \n      path: '/app/timesheet/approvals', \n      icon: 'check-circle',\n      permission: 'approve_timesheets'\n    },\n    { \n      name: 'Ferie/Permessi', \n      path: '/app/timesheet/time-off', \n      icon: 'calendar',\n      permission: 'manage_time_off'\n    },\n    { \n      name: 'Report', \n      path: '/app/timesheet/reports', \n      icon: 'document-report',\n      permission: 'view_timesheet_reports'\n    }\n  ]\n}\n```\n\n---\n\n## 🎯 **FASE 2: COMPONENTI TIMESHEET CORE**\n\n### **2.1 Timesheet Dashboard**\n\n**File:** `frontend/src/views/timesheet/TimesheetDashboard.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-dashboard\">\n    <!-- Header -->\n    <div class=\"mb-6\">\n      <h1 class=\"text-2xl font-bold text-gray-900\">Dashboard Timesheet</h1>\n      <p class=\"text-gray-600\">Panoramica ore e approvazioni</p>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard \n        title=\"Ore Questa Settimana\" \n        :value=\"stats.weeklyHours\" \n        color=\"blue\"\n        icon=\"clock\"\n      />\n      <StatCard \n        title=\"Ore Questo Mese\" \n        :value=\"stats.monthlyHours\" \n        color=\"green\"\n        icon=\"calendar\"\n      />\n      <StatCard \n        title=\"Da Approvare\" \n        :value=\"stats.pendingApprovals\" \n        color=\"yellow\"\n        icon=\"exclamation\"\n        v-if=\"canApprove\"\n      />\n      <StatCard \n        title=\"Efficienza\" \n        :value=\"stats.efficiency + '%'\" \n        color=\"purple\"\n        icon=\"trending-up\"\n      />\n    </div>\n\n    <!-- Quick Actions -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6\">\n      <!-- Registrazione Rapida -->\n      <QuickTimeEntry @saved=\"refreshStats\" />\n      \n      <!-- Calendario Ore -->\n      <TimesheetCalendar :month=\"currentMonth\" />\n    </div>\n\n    <!-- Recent Activity & Notifications -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <RecentTimesheetActivity />\n      <PendingApprovals v-if=\"canApprove\" />\n    </div>\n  </div>\n</template>\n```\n\n### **2.2 Timesheet Entries**\n\n**File:** `frontend/src/views/timesheet/TimesheetEntries.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-entries\">\n    <!-- Header con filtri -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <h1 class=\"text-2xl font-bold\">Registrazione Ore</h1>\n        <button @click=\"showQuickEntry = true\" \n                class=\"btn-primary\">\n          <PlusIcon class=\"w-4 h-4 mr-2\" />\n          Registra Ore\n        </button>\n      </div>\n      \n      <!-- Filtri -->\n      <div class=\"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <DateRangePicker v-model=\"dateRange\" />\n        <ProjectSelector v-model=\"selectedProject\" />\n        <StatusFilter v-model=\"selectedStatus\" />\n        <SearchInput v-model=\"searchQuery\" placeholder=\"Cerca...\" />\n      </div>\n    </div>\n\n    <!-- Lista Entries -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <TimesheetEntriesTable \n        :entries=\"filteredEntries\"\n        :loading=\"loading\"\n        @edit=\"editEntry\"\n        @delete=\"deleteEntry\"\n        @bulk-action=\"handleBulkAction\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <TimesheetEntryModal \n      v-if=\"showQuickEntry || editingEntry\"\n      :entry=\"editingEntry\"\n      @close=\"closeModal\"\n      @saved=\"handleEntrySaved\"\n    />\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 3: SISTEMA APPROVAZIONI CON AI**\n\n### **3.1 Timesheet Approvals - Vista Manager**\n\n**File:** `frontend/src/views/timesheet/TimesheetApprovals.vue`\n\n```javascript\n<template>\n  <div class=\"timesheet-approvals\">\n    <!-- Header con AI Analysis -->\n    <div class=\"bg-white shadow rounded-lg p-6 mb-6\">\n      <div class=\"flex items-center justify-between\">\n        <div>\n          <h1 class=\"text-2xl font-bold\">Approvazioni Timesheet</h1>\n          <p class=\"text-gray-600\">Gestisci approvazioni con assistenza AI</p>\n        </div>\n        <div class=\"flex items-center space-x-3\">\n          <!-- AI Anomaly Detection -->\n          <button @click=\"runAnomalyDetection\" \n                  :disabled=\"analyzingAnomalies\"\n                  class=\"btn-purple\">\n            <BeakerIcon class=\"w-4 h-4 mr-2\" />\n            {{ analyzingAnomalies ? 'Analizzando...' : 'Rileva Anomalie' }}\n          </button>\n          \n          <!-- Bulk Actions -->\n          <div class=\"relative\" v-if=\"selectedTimesheets.length\">\n            <button @click=\"showBulkMenu = !showBulkMenu\" \n                    class=\"btn-secondary\">\n              Azioni Multiple ({{ selectedTimesheets.length }})\n            </button>\n            <BulkActionsMenu \n              v-if=\"showBulkMenu\"\n              @approve-all=\"bulkApprove\"\n              @reject-all=\"bulkReject\"\n              @close=\"showBulkMenu = false\"\n            />\n          </div>\n        </div>\n      </div>\n\n      <!-- AI Anomalies Alert -->\n      <div v-if=\"anomalies.length\" \n           class=\"mt-4 bg-red-50 border border-red-200 rounded-lg p-4\">\n        <div class=\"flex items-start\">\n          <ExclamationTriangleIcon class=\"w-5 h-5 text-red-400 mt-0.5\" />\n          <div class=\"ml-3\">\n            <h3 class=\"text-sm font-medium text-red-800\">\n              Anomalie Rilevate ({{ anomalies.length }})\n            </h3>\n            <div class=\"mt-2 space-y-1\">\n              <div v-for=\"anomaly in anomalies\" :key=\"anomaly.id\"\n                   class=\"text-sm text-red-700\">\n                • {{ anomaly.user_name }}: {{ anomaly.description }}\n                <button @click=\"viewAnomalyDetails(anomaly)\"\n                        class=\"ml-2 text-red-600 underline\">\n                  Dettagli\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Stats Cards -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6\">\n      <StatCard title=\"Da Approvare\" :value=\"stats.pending\" color=\"yellow\" />\n      <StatCard title=\"Approvati\" :value=\"stats.approved\" color=\"green\" />\n      <StatCard title=\"Con Anomalie\" :value=\"anomalies.length\" color=\"red\" />\n      <StatCard title=\"Ore Totali\" :value=\"stats.totalHours\" color=\"blue\" />\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white shadow rounded-lg p-4 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-5 gap-4\">\n        <MonthSelector v-model=\"selectedMonth\" />\n        <StatusFilter v-model=\"selectedStatus\" />\n        <TeamMemberFilter v-model=\"selectedMember\" :members=\"teamMembers\" />\n        <AnomalyFilter v-model=\"showOnlyAnomalies\" />\n        <SearchInput v-model=\"searchQuery\" />\n      </div>\n    </div>\n\n    <!-- Lista Timesheet -->\n    <div class=\"bg-white shadow rounded-lg\">\n      <TimesheetApprovalsTable \n        :timesheets=\"filteredTimesheets\"\n        :loading=\"loading\"\n        :selected=\"selectedTimesheets\"\n        :anomalies=\"anomalies\"\n        @select=\"handleSelection\"\n        @view-details=\"viewDetails\"\n        @approve=\"approveTimesheet\"\n        @reject=\"rejectTimesheet\"\n      />\n    </div>\n\n    <!-- Modals -->\n    <TimesheetDetailModal \n      v-if=\"showDetailModal\"\n      :timesheet=\"selectedTimesheet\"\n      :anomaly=\"getAnomalyForTimesheet(selectedTimesheet)\"\n      @close=\"showDetailModal = false\"\n      @approve=\"approveTimesheet\"\n      @reject=\"rejectTimesheet\"\n    />\n\n    <AnomalyDetailModal\n      v-if=\"showAnomalyModal\"\n      :anomaly=\"selectedAnomaly\"\n      @close=\"showAnomalyModal = false\"\n    />\n  </div>\n</template>\n```\n\n### **3.2 AI Anomaly Detection Service**\n\n**File:** `frontend/src/services/aiAnomalyService.js`\n\n```javascript\nexport const aiAnomalyService = {\n  // Rileva anomalie nei timesheet\n  detectAnomalies: async (filters = {}) => {\n    const response = await apiClient.post('/ai-timesheet/detect-anomalies', {\n      month: filters.month,\n      year: filters.year,\n      team_members: filters.teamMembers,\n      analysis_types: [\n        'overtime_patterns',\n        'weekend_work',\n        'unusual_hours',\n        'project_switching',\n        'productivity_drops',\n        'duplicate_entries'\n      ]\n    })\n    return response.data\n  },\n\n  // Analizza pattern specifico utente\n  analyzeUserPattern: async (userId, timeRange) => {\n    const response = await apiClient.post(`/ai-timesheet/analyze-user/${userId}`, {\n      start_date: timeRange.start,\n      end_date: timeRange.end,\n      include_recommendations: true\n    })\n    return response.data\n  },\n\n  // Suggerimenti approvazione\n  getApprovalSuggestions: async (timesheetId) => {\n    const response = await apiClient.get(`/ai-timesheet/approval-suggestions/${timesheetId}`)\n    return response.data\n  }\n}\n```\n\n---\n\n## 🎯 **FASE 4: SISTEMA NOTIFICHE IN-APP**\n\n### **4.1 Notification Service**\n\n**File:** `frontend/src/services/notificationService.js`\n\n```javascript\nexport const notificationService = {\n  // Ottieni notifiche utente\n  getUserNotifications: async (filters = {}) => {\n    const response = await apiClient.get('/notifications/', {\n      params: {\n        unread_only: filters.unreadOnly || false,\n        type: filters.type,\n        limit: filters.limit || 50\n      }\n    })\n    return response.data\n  },\n\n  // Marca come letta\n  markAsRead: async (notificationId) => {\n    const response = await apiClient.put(`/notifications/${notificationId}/read`)\n    return response.data\n  },\n\n  // Marca tutte come lette\n  markAllAsRead: async () => {\n    const response = await apiClient.put('/notifications/mark-all-read')\n    return response.data\n  },\n\n  // Elimina notifica\n  deleteNotification: async (notificationId) => {\n    const response = await apiClient.delete(`/notifications/${notificationId}`)\n    return response.data\n  }\n}\n```\n\n### **4.2 Notification Store**\n\n**File:** `frontend/src/stores/notifications.js`\n\n```javascript\nimport { defineStore } from 'pinia'\nimport { notificationService } from '@/services/notificationService'\n\nexport const useNotificationStore = defineStore('notifications', {\n  state: () => ({\n    notifications: [],\n    unreadCount: 0,\n    loading: false,\n    polling: null\n  }),\n\n  getters: {\n    unreadNotifications: (state) => \n      state.notifications.filter(n => !n.read),\n    \n    timesheetNotifications: (state) =>\n      state.notifications.filter(n => n.type.includes('timesheet')),\n    \n    approvalNotifications: (state) =>\n      state.notifications.filter(n => n.type.includes('approval'))\n  },\n\n  actions: {\n    async fetchNotifications() {\n      this.loading = true\n      try {\n        const result = await notificationService.getUserNotifications()\n        this.notifications = result.data.notifications\n        this.unreadCount = result.data.unread_count\n      } catch (error) {\n        console.error('Error fetching notifications:', error)\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async markAsRead(notificationId) {\n      try {\n        await notificationService.markAsRead(notificationId)\n        const notification = this.notifications.find(n => n.id === notificationId)\n        if (notification) {\n          notification.read = true\n          this.unreadCount = Math.max(0, this.unreadCount - 1)\n        }\n      } catch (error) {\n        console.error('Error marking notification as read:', error)\n      }\n    },\n\n    async markAllAsRead() {\n      try {\n        await notificationService.markAllAsRead()\n        this.notifications.forEach(n => n.read = true)\n        this.unreadCount = 0\n      } catch (error) {\n        console.error('Error marking all notifications as read:', error)\n      }\n    },\n\n    startPolling() {\n      this.polling = setInterval(() => {\n        this.fetchNotifications()\n      }, 30000) // Poll ogni 30 secondi\n    },\n\n    stopPolling() {\n      if (this.polling) {\n        clearInterval(this.polling)\n        this.polling = null\n      }\n    }\n  }\n})\n```\n\n### **4.3 Notification Bell Component**\n\n**File:** `frontend/src/components/layout/HeaderNotifications.vue`\n\n```javascript\n<template>\n  <div class=\"relative\">\n    <!-- Notification Bell -->\n    <button @click=\"showDropdown = !showDropdown\"\n            class=\"relative p-2 text-gray-400 hover:text-gray-600 focus:outline-none\">\n      <BellIcon class=\"w-6 h-6\" />\n      \n      <!-- Badge -->\n      <span v-if=\"unreadCount > 0\"\n            class=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n        {{ unreadCount > 99 ? '99+' : unreadCount }}\n      </span>\n    </button>\n\n    <!-- Dropdown -->\n    <div v-if=\"showDropdown\" \n         class=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50\">\n      <!-- Header -->\n      <div class=\"px-4 py-3 border-b border-gray-200\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-sm font-medium text-gray-900\">Notifiche</h3>\n          <button v-if=\"unreadCount > 0\" \n                  @click=\"markAllAsRead\"\n                  class=\"text-xs text-blue-600 hover:text-blue-800\">\n            Segna tutte come lette\n          </button>\n        </div>\n      </div>\n\n      <!-- Lista Notifiche -->\n      <div class=\"max-h-96 overflow-y-auto\">\n        <div v-if=\"notifications.length === 0\" \n             class=\"px-4 py-8 text-center text-gray-500\">\n          Nessuna notifica\n        </div>\n        \n        <div v-for=\"notification in notifications.slice(0, 10)\" \n             :key=\"notification.id\"\n             class=\"px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer\"\n             :class=\"{ 'bg-blue-50': !notification.read }\"\n             @click=\"handleNotificationClick(notification)\">\n          \n          <div class=\"flex items-start space-x-3\">\n            <!-- Icon -->\n            <div class=\"flex-shrink-0\">\n              <component :is=\"getNotificationIcon(notification.type)\"\n                         class=\"w-5 h-5\"\n                         :class=\"getNotificationIconColor(notification.type)\" />\n            </div>\n            \n            <!-- Content -->\n            <div class=\"flex-1 min-w-0\">\n              <p class=\"text-sm font-medium text-gray-900\">\n                {{ notification.title }}\n              </p>\n              <p class=\"text-sm text-gray-500 truncate\">\n                {{ notification.message }}\n              </p>\n              <p class=\"text-xs text-gray-400 mt-1\">\n                {{ formatRelativeTime(notification.created_at) }}\n              </p>\n            </div>\n            \n            <!-- Unread indicator -->\n            <div v-if=\"!notification.read\" \n                 class=\"flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full\">\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Footer -->\n      <div class=\"px-4 py-3 border-t border-gray-200\">\n        <router-link to=\"/app/notifications\" \n                     class=\"text-sm text-blue-600 hover:text-blue-800\">\n          Vedi tutte le notifiche\n        </router-link>\n      </div>\n    </div>\n  </div>\n</template>\n```\n\n---\n\n## 🎯 **FASE 5: GERARCHIA MANAGER**\n\n### **5.1 Manager Hierarchy Service**\n\n**File:** `frontend/src/services/managerHierarchyService.js`\n\n```javascript\nexport const managerHierarchyService = {\n  // Ottieni team members sotto il manager\n  getTeamMembers: async (managerId = null) => {\n    const response = await apiClient.get('/personnel/team-hierarchy', {\n      params: { manager_id: managerId }\n    })\n    return response.data\n  },\n\n  // Ottieni timesheet da approvare per il manager\n  getPendingApprovalsForManager: async (filters = {}) => {\n    const response = await apiClient.get('/monthly-timesheets/pending-approvals', {\n      params: {\n        month: filters.month,\n        year: filters.year,\n        team_member_id: filters.teamMemberId\n      }\n    })\n    return response.data\n  },\n\n  // Verifica se può approvare timesheet\n  canApproveTimesheet: async (timesheetId) => {\n    const response = await apiClient.get(`/monthly-timesheets/${timesheetId}/can-approve`)\n    return response.data\n  }\n}\n```\n\n---\n\n## 📊 **STRUTTURA FILE COMPLETA**\n\n```\nfrontend/src/\n├── views/timesheet/\n│   ├── TimesheetDashboard.vue\n│   ├── TimesheetEntries.vue\n│   ├── TimesheetApprovals.vue\n│   ├── TimeOffRequests.vue\n│   ├── TimesheetReports.vue\n│   └── components/\n│       ├── QuickTimeEntry.vue\n│       ├── TimesheetCalendar.vue\n│       ├── TimesheetEntriesTable.vue\n│       ├── TimesheetApprovalsTable.vue\n│       ├── TimesheetDetailModal.vue\n│       ├── AnomalyDetailModal.vue\n│       ├── BulkActionsMenu.vue\n│       └── filters/\n│           ├── DateRangePicker.vue\n│           ├── ProjectSelector.vue\n│           ├── StatusFilter.vue\n│           ├── TeamMemberFilter.vue\n│           └── AnomalyFilter.vue\n├── services/\n│   ├── timesheetApi.js\n│   ├── aiAnomalyService.js\n│   ├── notificationService.js\n│   └── managerHierarchyService.js\n├── stores/\n│   ├── timesheet.js\n│   ├── notifications.js\n│   └── managerHierarchy.js\n└── composables/\n    ├── useTimesheetApprovals.js\n    ├── useAnomalyDetection.js\n    └── useNotifications.js\n```\n\n---\n\n## ⏱️ **TIMELINE IMPLEMENTAZIONE**\n\n### **Settimana 1:**\n- ✅ Struttura router e sidebar\n- ✅ TimesheetDashboard base\n- ✅ TimesheetEntries CRUD\n\n### **Settimana 2:**\n- ✅ Sistema approvazioni\n- ✅ AI anomaly detection\n- ✅ Bulk operations\n\n### **Settimana 3:**\n- ✅ Sistema notifiche\n- ✅ Gerarchia manager\n- ✅ Testing e refinement\n\n**Procediamo con l'implementazione?**\n"}