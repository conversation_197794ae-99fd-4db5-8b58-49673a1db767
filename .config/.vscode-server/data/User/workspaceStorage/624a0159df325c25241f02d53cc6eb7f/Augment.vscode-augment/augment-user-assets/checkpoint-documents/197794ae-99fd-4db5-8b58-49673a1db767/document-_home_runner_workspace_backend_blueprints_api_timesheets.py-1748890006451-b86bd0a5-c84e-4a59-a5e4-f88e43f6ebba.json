{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/timesheets.py"}, "originalCode": "\"\"\"\nAPI Blueprint per la gestione dei timesheet.\n\"\"\"\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import extract, func\nfrom datetime import datetime, date\nfrom calendar import monthrange\n\nfrom models import TimesheetEntry, Task, Project, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db\nfrom extensions import csrf\n\napi_timesheets = Blueprint('api_timesheets', __name__)\n\n@api_timesheets.route('/', methods=['GET'])\*************\n@login_required\ndef get_timesheets():\n    \"\"\"Recupera i timesheet con filtri generali.\"\"\"\n    try:\n        # Parametri query\n        start_date = request.args.get('start_date')\n        end_date = request.args.get('end_date')\n        user_id = request.args.get('user_id', type=int)\n        project_id = request.args.get('project_id', type=int)\n        task_id = request.args.get('task_id', type=int)\n        limit = request.args.get('limit', type=int, default=50)\n        offset = request.args.get('offset', type=int, default=0)\n\n        # Query base\n        query = TimesheetEntry.query\n\n        # Verifica permessi\n        if not user_has_permission(current_user.role, 'view_all_timesheets'):\n            # L'utente può vedere solo i propri timesheet\n            query = query.filter(TimesheetEntry.user_id == current_user.id)\n\n        # Applica filtri\n        if start_date:\n            query = query.filter(TimesheetEntry.date >= datetime.strptime(start_date, '%Y-%m-%d').date())\n        if end_date:\n            query = query.filter(TimesheetEntry.date <= datetime.strptime(end_date, '%Y-%m-%d').date())\n        if user_id:\n            # Verifica permessi per vedere timesheet di altri utenti\n            if not user_has_permission(current_user.role, 'view_all_timesheets') and user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)\n            query = query.filter(TimesheetEntry.user_id == user_id)\n        if project_id:\n            query = query.filter(TimesheetEntry.project_id == project_id)\n        if task_id:\n            query = query.filter(TimesheetEntry.task_id == task_id)\n\n        # Conta totale\n        total = query.count()\n\n        # Applica paginazione e ordina per data\n        timesheets = query.order_by(TimesheetEntry.date.desc()).offset(offset).limit(limit).all()\n\n        # Prepara dati\n        timesheets_data = []\n        for ts in timesheets:\n            timesheets_data.append({\n                'id': ts.id,\n                'user_id': ts.user_id,\n                'project_id': ts.project_id,\n                'task_id': ts.task_id,\n                'date': ts.date.isoformat(),\n                'hours': ts.hours,\n                'description': ts.description,\n                'status': ts.status,\n                'user_name': f\"{ts.user.first_name} {ts.user.last_name}\" if ts.user else None,\n                'project_name': ts.project.name if ts.project else None,\n                'task_name': ts.task.name if ts.task else None,\n                'created_at': ts.created_at.isoformat() if ts.created_at else None\n            })\n\n        return api_response(\n            data=timesheets_data,\n            message=f\"Recuperati {len(timesheets_data)} timesheet\",\n            meta={\n                'total': total,\n                'limit': limit,\n                'offset': offset,\n                'has_more': offset + limit < total\n            }\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_timesheets.route('/project/<int:project_id>/monthly', methods=['GET'])\*************\n@login_required\ndef get_project_monthly_timesheet(project_id):\n    \"\"\"Recupera i timesheet mensili per un progetto con layout tabellare.\"\"\"\n    try:\n        # Parametri query\n        year = int(request.args.get('year', datetime.now().year))\n        month = int(request.args.get('month', datetime.now().month))\n        member_id = request.args.get('member_id', type=int)\n\n        # Verifica permessi progetto\n        project = Project.query.get_or_404(project_id)\n        if not user_has_permission(current_user.role, 'view_all_projects'):\n            # Verifica se l'utente è membro del team\n            if not any(member.id == current_user.id for member in project.team_members):\n                return api_response(False, 'Accesso negato al progetto', status_code=403)\n\n        # Calcola giorni del mese\n        days_in_month = monthrange(year, month)[1]\n\n        # Query timesheet del progetto per il mese\n        query = TimesheetEntry.query.filter(\n            TimesheetEntry.project_id == project_id,\n            extract('year', TimesheetEntry.date) == year,\n            extract('month', TimesheetEntry.date) == month\n        )\n\n        # Filtra per membro se specificato\n        if member_id:\n            query = query.filter(TimesheetEntry.user_id == member_id)\n\n        timesheets = query.all()\n\n        # Query task del progetto\n        tasks = Task.query.filter(Task.project_id == project_id).all()\n\n        # Organizza dati per task e giorni\n        task_daily_data = {}\n        task_daily_billing = {}\n        task_totals = {}\n\n        for task in tasks:\n            task_daily_data[task.id] = {day: 0 for day in range(1, days_in_month + 1)}\n            task_daily_billing[task.id] = {day: False for day in range(1, days_in_month + 1)}\n            task_totals[task.id] = 0\n\n        # Popola dati timesheet\n        for ts in timesheets:\n            day = ts.date.day\n            task_id = ts.task_id\n            if task_id and task_id in task_daily_data:\n                task_daily_data[task_id][day] += ts.hours\n                task_totals[task_id] += ts.hours\n                # Se almeno una entry del giorno è fatturabile, segna il giorno come fatturabile\n                if ts.billable:\n                    task_daily_billing[task_id][day] = True\n\n        # Prepara dati task con timesheet\n        tasks_data = []\n        for task in tasks:\n            # Trova lavoratori per questo task nel mese\n            workers = db.session.query(User).join(TimesheetEntry).filter(\n                TimesheetEntry.task_id == task.id,\n                TimesheetEntry.project_id == project_id,\n                extract('year', TimesheetEntry.date) == year,\n                extract('month', TimesheetEntry.date) == month\n            ).distinct().all()\n\n            tasks_data.append({\n                'id': task.id,\n                'name': task.name,\n                'description': task.description,\n                'daily_hours': task_daily_data[task.id],\n                'daily_billing': task_daily_billing[task.id],\n                'total_hours': task_totals[task.id],\n                'workers': [f\"{w.first_name} {w.last_name}\" for w in workers]\n            })\n\n        # Calcola totali giornalieri\n        daily_totals = {day: 0 for day in range(1, days_in_month + 1)}\n        for task_id, daily_data in task_daily_data.items():\n            for day, hours in daily_data.items():\n                daily_totals[day] += hours\n\n        return api_response(\n            data={\n                'year': year,\n                'month': month,\n                'days_in_month': days_in_month,\n                'tasks': tasks_data,\n                'daily_totals': daily_totals,\n                'grand_total': sum(daily_totals.values()),\n                'project': {\n                    'id': project.id,\n                    'name': project.name\n                }\n            },\n            message=\"Timesheet mensile recuperato con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_timesheets.route('/project/<int:project_id>', methods=['GET'])\*************\n@login_required\ndef get_project_timesheets(project_id):\n    \"\"\"Recupera i timesheet per un progetto con filtri.\"\"\"\n    try:\n        # Parametri query\n        start_date = request.args.get('start_date')\n        end_date = request.args.get('end_date')\n        task_id = request.args.get('task_id', type=int)\n        user_id = request.args.get('user_id', type=int)\n\n        # Verifica permessi progetto\n        project = Project.query.get_or_404(project_id)\n        if not user_has_permission(current_user.role, 'view_all_projects'):\n            if not any(member.id == current_user.id for member in project.team_members):\n                return api_response(False, 'Accesso negato al progetto', status_code=403)\n\n        # Query base\n        query = TimesheetEntry.query.filter(TimesheetEntry.project_id == project_id)\n\n        # Applica filtri\n        if start_date:\n            query = query.filter(TimesheetEntry.date >= datetime.strptime(start_date, '%Y-%m-%d').date())\n        if end_date:\n            query = query.filter(TimesheetEntry.date <= datetime.strptime(end_date, '%Y-%m-%d').date())\n        if task_id:\n            query = query.filter(TimesheetEntry.task_id == task_id)\n        if user_id:\n            query = query.filter(TimesheetEntry.user_id == user_id)\n\n        # Ordina per data\n        timesheets = query.order_by(TimesheetEntry.date.desc()).all()\n\n        # Prepara dati\n        timesheets_data = []\n        for ts in timesheets:\n            timesheets_data.append({\n                'id': ts.id,\n                'user_id': ts.user_id,\n                'task_id': ts.task_id,\n                'date': ts.date.isoformat(),\n                'hours': ts.hours,\n                'description': ts.description,\n                'status': ts.status,\n                'user': {\n                    'id': ts.user.id,\n                    'first_name': ts.user.first_name,\n                    'last_name': ts.user.last_name\n                } if ts.user else None,\n                'task': {\n                    'id': ts.task.id,\n                    'name': ts.task.name\n                } if ts.task else None\n            })\n\n        return api_response(\n            data=timesheets_data,\n            message=\"Timesheet recuperati con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_timesheets.route('/', methods=['POST'])\*************\n@login_required\ndef create_timesheet():\n    \"\"\"Crea un nuovo timesheet.\"\"\"\n    try:\n        data = request.get_json()\n\n        # Validazione campi richiesti\n        required_fields = ['project_id', 'date', 'hours']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n\n        # Verifica permessi progetto\n        project = Project.query.get_or_404(data['project_id'])\n        if not user_has_permission(current_user.role, 'manage_timesheets'):\n            # Verifica se può inserire timesheet per se stesso\n            user_id = data.get('user_id', current_user.id)\n            if user_id != current_user.id:\n                return api_response(False, 'Non puoi inserire timesheet per altri utenti', status_code=403)\n\n        # Crea timesheet\n        timesheet = TimesheetEntry(\n            user_id=data.get('user_id', current_user.id),\n            project_id=data['project_id'],\n            task_id=data.get('task_id'),\n            date=datetime.strptime(data['date'], '%Y-%m-%d').date(),\n            hours=float(data['hours']),\n            description=data.get('description', ''),\n            status=data.get('status', 'pending')\n        )\n\n        db.session.add(timesheet)\n        db.session.commit()\n\n        return api_response(\n            data={'id': timesheet.id},\n            message='Timesheet creato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timesheets.route('/<int:timesheet_id>', methods=['PUT'])\*************\n@login_required\ndef update_timesheet(timesheet_id):\n    \"\"\"Aggiorna un timesheet esistente.\"\"\"\n    try:\n        timesheet = TimesheetEntry.query.get_or_404(timesheet_id)\n\n        # Verifica permessi\n        if not user_has_permission(current_user.role, 'manage_timesheets'):\n            if timesheet.user_id != current_user.id:\n                return api_response(False, 'Non puoi modificare timesheet di altri utenti', status_code=403)\n\n        data = request.get_json()\n\n        # Aggiorna campi\n        if 'date' in data:\n            timesheet.date = datetime.strptime(data['date'], '%Y-%m-%d').date()\n        if 'hours' in data:\n            timesheet.hours = float(data['hours'])\n        if 'description' in data:\n            timesheet.description = data['description']\n        if 'status' in data:\n            timesheet.status = data['status']\n        if 'task_id' in data:\n            timesheet.task_id = data['task_id']\n\n        db.session.commit()\n\n        return api_response(\n            data={},\n            message='Timesheet aggiornato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timesheets.route('/<int:timesheet_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_timesheet(timesheet_id):\n    \"\"\"Elimina un timesheet.\"\"\"\n    try:\n        timesheet = TimesheetEntry.query.get_or_404(timesheet_id)\n\n        # Verifica permessi\n        if not user_has_permission(current_user.role, 'manage_timesheets'):\n            if timesheet.user_id != current_user.id:\n                return api_response(False, 'Non puoi eliminare timesheet di altri utenti', status_code=403)\n\n        db.session.delete(timesheet)\n        db.session.commit()\n\n        return api_response(\n            data={},\n            message='Timesheet eliminato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)", "modifiedCode": "\"\"\"\nAPI Blueprint per la gestione dei timesheet.\n\"\"\"\nfrom flask import Blueprint, request, jsonify\nfrom flask_login import current_user, login_required\nfrom sqlalchemy import extract, func\nfrom datetime import datetime, date\nfrom calendar import monthrange\n\nfrom models import TimesheetEntry, Task, Project, User\nfrom utils.api_utils import api_response, handle_api_error\nfrom utils.permissions import user_has_permission\nfrom extensions import db\nfrom extensions import csrf\n\napi_timesheets = Blueprint('api_timesheets', __name__)\n\n@api_timesheets.route('/', methods=['GET'])\*************\n@login_required\ndef get_timesheets():\n    \"\"\"Recupera i timesheet con filtri generali.\"\"\"\n    try:\n        # Parametri query\n        start_date = request.args.get('start_date')\n        end_date = request.args.get('end_date')\n        user_id = request.args.get('user_id', type=int)\n        project_id = request.args.get('project_id', type=int)\n        task_id = request.args.get('task_id', type=int)\n        limit = request.args.get('limit', type=int, default=50)\n        offset = request.args.get('offset', type=int, default=0)\n\n        # Query base\n        query = TimesheetEntry.query\n\n        # Verifica permessi\n        if not user_has_permission(current_user.role, 'view_all_timesheets'):\n            # L'utente può vedere solo i propri timesheet\n            query = query.filter(TimesheetEntry.user_id == current_user.id)\n\n        # Applica filtri\n        if start_date:\n            query = query.filter(TimesheetEntry.date >= datetime.strptime(start_date, '%Y-%m-%d').date())\n        if end_date:\n            query = query.filter(TimesheetEntry.date <= datetime.strptime(end_date, '%Y-%m-%d').date())\n        if user_id:\n            # Verifica permessi per vedere timesheet di altri utenti\n            if not user_has_permission(current_user.role, 'view_all_timesheets') and user_id != current_user.id:\n                return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)\n            query = query.filter(TimesheetEntry.user_id == user_id)\n        if project_id:\n            query = query.filter(TimesheetEntry.project_id == project_id)\n        if task_id:\n            query = query.filter(TimesheetEntry.task_id == task_id)\n\n        # Conta totale\n        total = query.count()\n\n        # Applica paginazione e ordina per data\n        timesheets = query.order_by(TimesheetEntry.date.desc()).offset(offset).limit(limit).all()\n\n        # Prepara dati\n        timesheets_data = []\n        for ts in timesheets:\n            timesheets_data.append({\n                'id': ts.id,\n                'user_id': ts.user_id,\n                'project_id': ts.project_id,\n                'task_id': ts.task_id,\n                'date': ts.date.isoformat(),\n                'hours': ts.hours,\n                'description': ts.description,\n                'status': ts.status,\n                'user_name': f\"{ts.user.first_name} {ts.user.last_name}\" if ts.user else None,\n                'project_name': ts.project.name if ts.project else None,\n                'task_name': ts.task.name if ts.task else None,\n                'created_at': ts.created_at.isoformat() if ts.created_at else None\n            })\n\n        return api_response(\n            data=timesheets_data,\n            message=f\"Recuperati {len(timesheets_data)} timesheet\",\n            meta={\n                'total': total,\n                'limit': limit,\n                'offset': offset,\n                'has_more': offset + limit < total\n            }\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n@api_timesheets.route('/project/<int:project_id>/monthly', methods=['GET'])\*************\n@login_required\ndef get_project_monthly_timesheet(project_id):\n    \"\"\"Recupera i timesheet mensili per un progetto con layout tabellare.\"\"\"\n    try:\n        # Parametri query\n        year = int(request.args.get('year', datetime.now().year))\n        month = int(request.args.get('month', datetime.now().month))\n        member_id = request.args.get('member_id', type=int)\n\n        # Verifica permessi progetto\n        project = Project.query.get_or_404(project_id)\n        if not user_has_permission(current_user.role, 'view_all_projects'):\n            # Verifica se l'utente è membro del team\n            if not any(member.id == current_user.id for member in project.team_members):\n                return api_response(False, 'Accesso negato al progetto', status_code=403)\n\n        # Calcola giorni del mese\n        days_in_month = monthrange(year, month)[1]\n\n        # Query timesheet del progetto per il mese\n        query = TimesheetEntry.query.filter(\n            TimesheetEntry.project_id == project_id,\n            extract('year', TimesheetEntry.date) == year,\n            extract('month', TimesheetEntry.date) == month\n        )\n\n        # Filtra per membro se specificato\n        if member_id:\n            query = query.filter(TimesheetEntry.user_id == member_id)\n\n        timesheets = query.all()\n\n        # Query task del progetto\n        tasks = Task.query.filter(Task.project_id == project_id).all()\n\n        # Organizza dati per task e giorni\n        task_daily_data = {}\n        task_daily_billing = {}\n        task_totals = {}\n\n        for task in tasks:\n            task_daily_data[task.id] = {day: 0 for day in range(1, days_in_month + 1)}\n            task_daily_billing[task.id] = {day: False for day in range(1, days_in_month + 1)}\n            task_totals[task.id] = 0\n\n        # Popola dati timesheet\n        for ts in timesheets:\n            day = ts.date.day\n            task_id = ts.task_id\n            if task_id and task_id in task_daily_data:\n                task_daily_data[task_id][day] += ts.hours\n                task_totals[task_id] += ts.hours\n                # Se almeno una entry del giorno è fatturabile, segna il giorno come fatturabile\n                if ts.billable:\n                    task_daily_billing[task_id][day] = True\n\n        # Prepara dati task con timesheet\n        tasks_data = []\n        for task in tasks:\n            # Trova lavoratori per questo task nel mese\n            workers = db.session.query(User).join(TimesheetEntry).filter(\n                TimesheetEntry.task_id == task.id,\n                TimesheetEntry.project_id == project_id,\n                extract('year', TimesheetEntry.date) == year,\n                extract('month', TimesheetEntry.date) == month\n            ).distinct().all()\n\n            tasks_data.append({\n                'id': task.id,\n                'name': task.name,\n                'description': task.description,\n                'daily_hours': task_daily_data[task.id],\n                'daily_billing': task_daily_billing[task.id],\n                'total_hours': task_totals[task.id],\n                'workers': [f\"{w.first_name} {w.last_name}\" for w in workers]\n            })\n\n        # Calcola totali giornalieri\n        daily_totals = {day: 0 for day in range(1, days_in_month + 1)}\n        for task_id, daily_data in task_daily_data.items():\n            for day, hours in daily_data.items():\n                daily_totals[day] += hours\n\n        return api_response(\n            data={\n                'year': year,\n                'month': month,\n                'days_in_month': days_in_month,\n                'tasks': tasks_data,\n                'daily_totals': daily_totals,\n                'grand_total': sum(daily_totals.values()),\n                'project': {\n                    'id': project.id,\n                    'name': project.name\n                }\n            },\n            message=\"Timesheet mensile recuperato con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_timesheets.route('/project/<int:project_id>', methods=['GET'])\*************\n@login_required\ndef get_project_timesheets(project_id):\n    \"\"\"Recupera i timesheet per un progetto con filtri.\"\"\"\n    try:\n        # Parametri query\n        start_date = request.args.get('start_date')\n        end_date = request.args.get('end_date')\n        task_id = request.args.get('task_id', type=int)\n        user_id = request.args.get('user_id', type=int)\n\n        # Verifica permessi progetto\n        project = Project.query.get_or_404(project_id)\n        if not user_has_permission(current_user.role, 'view_all_projects'):\n            if not any(member.id == current_user.id for member in project.team_members):\n                return api_response(False, 'Accesso negato al progetto', status_code=403)\n\n        # Query base\n        query = TimesheetEntry.query.filter(TimesheetEntry.project_id == project_id)\n\n        # Applica filtri\n        if start_date:\n            query = query.filter(TimesheetEntry.date >= datetime.strptime(start_date, '%Y-%m-%d').date())\n        if end_date:\n            query = query.filter(TimesheetEntry.date <= datetime.strptime(end_date, '%Y-%m-%d').date())\n        if task_id:\n            query = query.filter(TimesheetEntry.task_id == task_id)\n        if user_id:\n            query = query.filter(TimesheetEntry.user_id == user_id)\n\n        # Ordina per data\n        timesheets = query.order_by(TimesheetEntry.date.desc()).all()\n\n        # Prepara dati\n        timesheets_data = []\n        for ts in timesheets:\n            timesheets_data.append({\n                'id': ts.id,\n                'user_id': ts.user_id,\n                'task_id': ts.task_id,\n                'date': ts.date.isoformat(),\n                'hours': ts.hours,\n                'description': ts.description,\n                'status': ts.status,\n                'user': {\n                    'id': ts.user.id,\n                    'first_name': ts.user.first_name,\n                    'last_name': ts.user.last_name\n                } if ts.user else None,\n                'task': {\n                    'id': ts.task.id,\n                    'name': ts.task.name\n                } if ts.task else None\n            })\n\n        return api_response(\n            data=timesheets_data,\n            message=\"Timesheet recuperati con successo\"\n        )\n\n    except Exception as e:\n        return handle_api_error(e)\n\n\n@api_timesheets.route('/', methods=['POST'])\*************\n@login_required\ndef create_timesheet():\n    \"\"\"Crea un nuovo timesheet.\"\"\"\n    try:\n        data = request.get_json()\n\n        # Validazione campi richiesti\n        required_fields = ['project_id', 'date', 'hours']\n        for field in required_fields:\n            if field not in data:\n                return api_response(\n                    False,\n                    f'Campo {field} richiesto',\n                    status_code=400\n                )\n\n        # Verifica permessi progetto\n        project = Project.query.get_or_404(data['project_id'])\n        if not user_has_permission(current_user.role, 'manage_timesheets'):\n            # Verifica se può inserire timesheet per se stesso\n            user_id = data.get('user_id', current_user.id)\n            if user_id != current_user.id:\n                return api_response(False, 'Non puoi inserire timesheet per altri utenti', status_code=403)\n\n        # Crea timesheet\n        timesheet = TimesheetEntry(\n            user_id=data.get('user_id', current_user.id),\n            project_id=data['project_id'],\n            task_id=data.get('task_id'),\n            date=datetime.strptime(data['date'], '%Y-%m-%d').date(),\n            hours=float(data['hours']),\n            description=data.get('description', ''),\n            status=data.get('status', 'pending'),\n            billable=data.get('billable', False),\n            billing_rate=float(data['billing_rate']) if data.get('billing_rate') else None\n        )\n\n        db.session.add(timesheet)\n        db.session.commit()\n\n        return api_response(\n            data={'id': timesheet.id},\n            message='Timesheet creato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timesheets.route('/<int:timesheet_id>', methods=['PUT'])\*************\n@login_required\ndef update_timesheet(timesheet_id):\n    \"\"\"Aggiorna un timesheet esistente.\"\"\"\n    try:\n        timesheet = TimesheetEntry.query.get_or_404(timesheet_id)\n\n        # Verifica permessi\n        if not user_has_permission(current_user.role, 'manage_timesheets'):\n            if timesheet.user_id != current_user.id:\n                return api_response(False, 'Non puoi modificare timesheet di altri utenti', status_code=403)\n\n        data = request.get_json()\n\n        # Aggiorna campi\n        if 'date' in data:\n            timesheet.date = datetime.strptime(data['date'], '%Y-%m-%d').date()\n        if 'hours' in data:\n            timesheet.hours = float(data['hours'])\n        if 'description' in data:\n            timesheet.description = data['description']\n        if 'status' in data:\n            timesheet.status = data['status']\n        if 'task_id' in data:\n            timesheet.task_id = data['task_id']\n\n        db.session.commit()\n\n        return api_response(\n            data={},\n            message='Timesheet aggiornato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)\n\n\n@api_timesheets.route('/<int:timesheet_id>', methods=['DELETE'])\*************\n@login_required\ndef delete_timesheet(timesheet_id):\n    \"\"\"Elimina un timesheet.\"\"\"\n    try:\n        timesheet = TimesheetEntry.query.get_or_404(timesheet_id)\n\n        # Verifica permessi\n        if not user_has_permission(current_user.role, 'manage_timesheets'):\n            if timesheet.user_id != current_user.id:\n                return api_response(False, 'Non puoi eliminare timesheet di altri utenti', status_code=403)\n\n        db.session.delete(timesheet)\n        db.session.commit()\n\n        return api_response(\n            data={},\n            message='Timesheet eliminato con successo'\n        )\n\n    except Exception as e:\n        db.session.rollback()\n        return handle_api_error(e)"}