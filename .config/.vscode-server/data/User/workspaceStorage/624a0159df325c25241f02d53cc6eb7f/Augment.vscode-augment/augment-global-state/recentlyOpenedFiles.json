[["/home/<USER>/workspace/utils/image_utils.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "utils/image_utils.py"}}], ["/home/<USER>/workspace/specs/task_16_vue_refactoring.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/task_16_vue_refactoring.txt"}}], ["/home/<USER>/workspace/app.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "app.py"}}], ["/home/<USER>/workspace/main.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "main.py"}}], ["/home/<USER>/workspace/backend/blueprints/api/kpis.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/kpis.py"}}], ["/home/<USER>/workspace/backend/cookies.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/cookies.txt"}}], ["/home/<USER>/workspace/cookies.txt", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "cookies.txt"}}], ["/home/<USER>/workspace/frontend/package.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/package.json"}}], ["/home/<USER>/workspace/frontend/src/main.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/main.js"}}], ["/home/<USER>/workspace/frontend/package-lock.json", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/package-lock.json"}}], ["/home/<USER>/workspace/frontend/.eslintrc.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/.eslintrc.js"}}], ["/home/<USER>/workspace/backend/config.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/config.py"}}], ["/home/<USER>/workspace/backend/static/dist/index.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/static/dist/index.html"}}], ["/home/<USER>/workspace/backend/static/dist/assets/app.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/static/dist/assets/app.js"}}], ["/home/<USER>/workspace/frontend/vite.config.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/vite.config.js"}}], ["/home/<USER>/workspace/frontend/src/components/layout/HeaderSearch.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/HeaderSearch.vue"}}], ["/home/<USER>/workspace/frontend/src/components/layout/HeaderQuickActions.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/HeaderQuickActions.vue"}}], ["/home/<USER>/workspace/frontend/src/components/layout/AppHeader.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/components/layout/AppHeader.vue"}}], ["/home/<USER>/workspace/frontend/tailwind.config.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/tailwind.config.js"}}], ["/home/<USER>/workspace/backend/templates/vue_app.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/templates/vue_app.html"}}], ["/home/<USER>/workspace/frontend/src/views/projects/components/ProjectOverview.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectOverview.vue"}}], ["/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTeam.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTeam.vue"}}], ["/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTimesheet.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTimesheet.vue"}}], ["/home/<USER>/workspace/frontend/src/views/projects/components/ProjectHeader.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectHeader.vue"}}], ["/home/<USER>/workspace/frontend/src/views/projects/Projects.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/Projects.vue"}}], ["/home/<USER>/workspace/backend/legacy/templates/projects/kpi_config.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/legacy/templates/projects/kpi_config.html"}}], ["/home/<USER>/workspace/frontend/src/views/projects/components/ProjectKPI.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectKPI.vue"}}], ["/home/<USER>/workspace/frontend/src/views/projects/components/ProjectTasks.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/projects/components/ProjectTasks.vue"}}], ["/home/<USER>/workspace/backend/legacy/admin.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/legacy/admin.py"}}], ["/home/<USER>/workspace/backend/legacy/templates/products/catalog.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/legacy/templates/products/catalog.html"}}], ["/home/<USER>/workspace/backend/legacy/admin.py.bak", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/legacy/admin.py.bak"}}], ["/home/<USER>/workspace/backend/legacy/dashboard.py.bak", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/legacy/dashboard.py.bak"}}], ["/home/<USER>/workspace/backend/legacy/templates/components/toast.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/legacy/templates/components/toast.html"}}], ["/home/<USER>/workspace/backend/legacy/projects.py.bak", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/legacy/projects.py.bak"}}], ["/home/<USER>/workspace/specs/prd.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/prd.md"}}], ["/home/<USER>/workspace/specs/personnel_migration_plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/personnel_migration_plan.md"}}], ["/home/<USER>/workspace/docs/testing_implementation_plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing_implementation_plan.md"}}], ["/home/<USER>/workspace/backend/utils/create_admin.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/create_admin.py"}}], ["/home/<USER>/workspace/docs/testing_strategy.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/testing_strategy.md"}}], ["/home/<USER>/workspace/backend/blueprints/api/base.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/base.py"}}], ["/home/<USER>/workspace/backend/blueprints/api/time_off_requests.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/time_off_requests.py"}}], ["/home/<USER>/workspace/backend/db_update_timesheet_migration.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db_update_timesheet_migration.py"}}], ["/home/<USER>/workspace/specs/vue_professional_migration.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "specs/vue_professional_migration.md"}}], ["/home/<USER>/workspace/frontend/src/views/personnel/PersonnelAdmin.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/PersonnelAdmin.vue"}}], ["/home/<USER>/workspace/backend/ai_services.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/ai_services.py"}}], ["/home/<USER>/workspace/docs/task/2.4/ai/resource/allocation.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task/2.4/ai/resource/allocation.md"}}], ["/home/<USER>/workspace/docs/timesheet-management-implementation-plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/timesheet-management-implementation-plan.md"}}], ["/home/<USER>/workspace/frontend/src/views/personnel/components/CVTab.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/personnel/components/CVTab.vue"}}], ["/home/<USER>/workspace/backend/static/dist/assets/PersonnelProfile.js", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/static/dist/assets/PersonnelProfile.js"}}], ["/home/<USER>/workspace/backend/static/dist/assets/index.css", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/static/dist/assets/index.css"}}], ["/home/<USER>/workspace/backend/utils/cv_parser.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/utils/cv_parser.py"}}], ["/home/<USER>/workspace/backend/blueprints/api/personnel.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/personnel.py"}}], ["/home/<USER>/workspace/backend/blueprints/api/auth.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/auth.py"}}], ["/home/<USER>/workspace/.gitignore", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": ".giti<PERSON>re"}}], ["/home/<USER>/workspace/backend/app.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/app.py"}}], ["/home/<USER>/workspace/backend/db_update.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/db_update.py"}}], ["/home/<USER>/workspace/backend/blueprints/api/projects.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/projects.py"}}], ["/home/<USER>/workspace/docs/timesheet-database-api-plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/timesheet-database-api-plan.md"}}], ["/home/<USER>/workspace/docs/task-3/log.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task-3/log.md"}}], ["/home/<USER>/workspace/docs/task-3/timesheet-database-api-plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task-3/timesheet-database-api-plan.md"}}], ["/home/<USER>/workspace/docs/task-3/timesheet-management-implementation-plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task-3/timesheet-management-implementation-plan.md"}}], ["/home/<USER>/workspace/docs/task-3/frontend-implementation-plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/task-3/frontend-implementation-plan.md"}}], ["/home/<USER>/workspace/backend/models.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/models.py"}}], ["/home/<USER>/workspace/backend/blueprints/api/dashboard.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "backend/blueprints/api/dashboard.py"}}]]