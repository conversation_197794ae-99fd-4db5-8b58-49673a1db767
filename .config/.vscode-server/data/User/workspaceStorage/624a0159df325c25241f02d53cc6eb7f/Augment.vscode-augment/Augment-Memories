# Implementation Planning and Priorities
- User prefers detailed roadmaps/scalettas before starting development work on migration tasks
- User expects comprehensive, production-ready solutions that address all mentioned issues, not partial fixes
- When proposing new architectures or migrations, ensure all existing features, utilities, and test structures are properly migrated
- User prefers isofunctional Vue.js migrations that maintain exact functional parity with legacy systems
- User expects resource allocations to be connected to actual task assignments rather than being standalone percentage allocations
- User prefers collaborative design approach before implementation, wants documentation in /docs directory, and expects thorough codebase analysis including existing timesheet views before making changes.
- User strongly prefers thorough analysis of existing code (models, views, relationships) and collaborative planning before any implementation work begins.
- User prefers detailed explanations of planned changes before implementation to avoid potential damage to the codebase.
- User expects me to proactively search the codebase for existing components (like Vue components in personnel profile pages) rather than needing explicit guidance to find them.
- User plans to implement CRM integration with customers, proposals, offers, approved orders, project linking, billing, and payments - wants to consider how this will work with current project structure and timesheet entries.
- User prefers that the /tasks folder should not be modified or touched during development work.
- User expects comprehensive testing with pytest for backend and frontend tests to be implemented alongside new features.
- User wants unused/dead files to be cleaned up rather than left in the codebase

# UI/UX Design and Branding
- User prefers consolidated UI interfaces with tab-based pages rather than separate dedicated pages
- User prefers icons in all tab headers and URL hash fragments to maintain tab state on page reload
- User strongly prefers to maintain SPA functionality with non-reloading page navigation and static sidebar
- Future task will implement dynamic branding management, so Tailwind CSS should use CSS variables for customization
- Dark mode toggle should be implemented in layout components and managed at component level
- User prefers no emojis in the UI and wants them replaced with proper icons
- User prefers single-column vertical layouts for profile pages to optimize space utilization
- User prefers department lists with pagination, proper grid sizing, action buttons, and prominent creation buttons

# Vue.js Development and Organization
- User prefers proper Vue.js Single File Components (.vue) with build system over inline template approach
- User prefers organizing Vue.js components within view directories rather than in separate global components directories
- The base.html template was removed during Vue.js refactoring; spa.html should be used universally
- User prefers blueprints to contain only API endpoints when using Vue.js SPA architecture
- Dashboard links should be consistent without /app prefix, and legacy admin page should be replicated in Vue.js migration
- Legacy code/files are not being used in the current Vue.js migration and should not be considered for fixes or imports
- PersonnelProfile.vue should have a tab system (Projects, Tasks, Skills, Timesheet, CV), progress bar for profile completion
- ProjectVue component previously had resource allocation functionality that was lost during checkpoint restoration and needs to be recovered.

# Task and Project Management
- Different project types should have different KPI expectations and calculations
- Projects should have billable/non-billable flag, and personnel costs should be valued with validity periods
- For personnel cost calculations, use daily rates instead of hourly rates as the base unit
- User prefers KPI thresholds to be configurable per project, with AI-suggested or default values
- For timesheet views, user prefers days as columns and hours in cells, plus an aggregated view with persons as rows and months as columns
- User prefers hours to be rounded/formatted in KPI displays rather than showing decimal precision
- Timesheet system should track hours on project activities or time-off (vacation/leave) without requiring daily containers or separate endpoints - workers log hours directly on activities.
- The current Timesheet model represents individual time entries logged against specific tasks, not daily timesheet containers.
- User questioned the need for DailyTimesheet concept, suggesting they may prefer a different timesheet architecture approach.
- User prefers internal non-billable projects, wants billing rates defined in resource allocation relationships, manual invoicing with entry selection, and confirms project types already support both fixed budget and hourly billing models.
- User prefers extending existing timesheet model (Option A) over task_003.txt approach, wants CRM pipeline integration with specific billing rates in ProjectResource relationships.
- User prefers MonthlyTimesheet naming over MonthlyTimesheetApproval and wants to integrate billing models (tasks linked to projects and client contracts) when designing the timesheet system to prepare for Task 4 CRM integration.
- User prefers renaming TimeSheet model to TimeSheetEntry and wants detailed backend API modification planning before implementation.

# Database and Technical Management
- When seeding database, preserve existing users instead of clearing them completely
- For database schema changes, use db_update file instead of creating migrations
- The Flask application should be started with 'python main.py' instead of 'python app.py'
- Always keep Swagger JSON documentation updated for every API endpoint
- User wants tests to be written, verified and updated when implementing new features
- User expects consistent database table naming without duplicates and prefers clean schema design - noticed issues with time_off_request/time_off_requests and timesheet/timesheets/entries/entry duplications.

# HR Management
- Task 7 HR Management implementation plan: Phase 1 (Profile Management, Skills CRUD), Phase 2 (Department Management, Organization Chart), Phase 3 (Resource Allocation), Phase 4 (Security & Compliance)
- Personnel module has 4 incomplete placeholder views (Organigramma, Competenze, Dipartimenti, Amministrazione) that need full implementation
- User wants to integrate existing contractual data fields from the UserProfile model into the PersonnelAdmin.vue implementation
- User prefers to keep 'Directory' as the sidebar navigation label for the personnel module instead of 'Team'
- Profile completion percentages should be rounded rather than showing decimals
- User prefers orgchart components to have multiple visualization modes with comprehensive filters and interactive controls

# AI Integration
- User prefers to integrate AI functionality using OpenAI API key stored in environment variables (Replit secrets)
- OpenAI API key is correctly configured in environment variables and working for AI services
- AI analysis operations require longer timeouts than 10 seconds due to processing time requirements.

# Security and Access Control
- Billing rates and financial data in ProjectResource and timesheet entries should only be visible to admin and manager roles in the UI, not to regular employees.
- Managers must be able to view the billing rates of people assigned to their projects.

# Time-Off Requests
- User wants separate manager/HR views for team time-off requests with notifications and KPI tracking.