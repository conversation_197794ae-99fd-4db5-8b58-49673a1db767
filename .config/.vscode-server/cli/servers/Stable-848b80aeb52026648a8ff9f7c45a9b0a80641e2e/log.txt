*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[10:03:32] 




[10:03:32] Extension host agent started.
[10:03:32] [<unknown>][4efaea64][ManagementConnection] New connection established.
[10:03:32] [<unknown>][570b7058][ExtensionHostConnection] New connection established.
[10:03:32] [<unknown>][570b7058][ExtensionHostConnection] <1314> Launched Extension Host Process.
[10:03:33] ComputeTargetPlatform: linux-x64
[10:03:37] ComputeTargetPlatform: linux-x64
[10:03:44] [File Watcher] Unexpected error: inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250528T213443/exthost1/GitHub.copilot-chat' failed: No such file or directory (EUNKNOWN) (path: /home/<USER>/workspace)
[10:03:44] [File Watcher (universal)] inotify_add_watch on '/home/<USER>/workspace/.config/.vscode-server/data/logs/20250528T213443/exthost1/GitHub.copilot-chat' failed: No such file or directory
