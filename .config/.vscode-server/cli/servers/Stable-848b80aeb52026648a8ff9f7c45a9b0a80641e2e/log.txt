*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[10:50:22] 




[10:50:23] Extension host agent started.
[10:50:23] [<unknown>][b6645fc4][ExtensionHostConnection] New connection established.
[10:50:23] [<unknown>][2237c3d9][ManagementConnection] New connection established.
[10:50:23] [<unknown>][b6645fc4][ExtensionHostConnection] <451> Launched Extension Host Process.
[10:50:24] ComputeTargetPlatform: linux-x64
[10:50:26] ComputeTargetPlatform: linux-x64
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[10:50:32] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
New EH opened, aborting shutdown
[10:55:23] New EH opened, aborting shutdown
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[11:04:02] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[11:04:48] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[11:34:20] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:3000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[11:46:02] Error: connect ECONNREFUSED 127.0.0.1:3000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 3000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[12:00:55] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[12:00:55] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[15:53:23] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[15:53:23] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[15:57:12] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
rejected promise not handled within 1 second: Error: connect ECONNREFUSED 127.0.0.1:5000
stack trace: Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16)
[15:57:12] Error: connect ECONNREFUSED 127.0.0.1:5000
    at TCPConnectWrap.afterConnect [as oncomplete] (node:net:1611:16) {
  errno: -111,
  code: 'ECONNREFUSED',
  syscall: 'connect',
  address: '127.0.0.1',
  port: 5000
}
[16:17:32] Getting Manifest... shd101wyy.markdown-preview-enhanced
[16:17:32] Installing extension: shd101wyy.markdown-preview-enhanced {
  installPreReleaseVersion: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[16:17:35] Extension signature verification result for shd101wyy.markdown-preview-enhanced: Success. Internal Code: 0. Executed: true. Duration: 1809ms.
[16:17:36] Extracted extension to file:///home/<USER>/.vscode-server/extensions/shd101wyy.markdown-preview-enhanced-0.8.18: shd101wyy.markdown-preview-enhanced
[16:17:36] Renamed to /home/<USER>/.vscode-server/extensions/shd101wyy.markdown-preview-enhanced-0.8.18
[16:17:36] Extension installed successfully: shd101wyy.markdown-preview-enhanced file:///home/<USER>/.vscode-server/extensions/extensions.json
[19:02:55] [<unknown>][2237c3d9][ManagementConnection] The client has reconnected.
[19:03:27] [<unknown>][b6645fc4][ExtensionHostConnection] The client has reconnected.
[19:10:44] [<unknown>][2237c3d9][ManagementConnection] The client has reconnected.
[19:10:53] [<unknown>][b6645fc4][ExtensionHostConnection] The client has reconnected.
[19:18:05] [<unknown>][2237c3d9][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[19:18:06] [<unknown>][b6645fc4][ExtensionHostConnection] <451> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[19:18:06] Last EH closed, waiting before shutting down
[19:18:23] [<unknown>][5279b8d1][ManagementConnection] New connection established.
[19:18:23] [<unknown>][fa8593cb][ExtensionHostConnection] New connection established.
[19:18:23] [<unknown>][fa8593cb][ExtensionHostConnection] <5383> Launched Extension Host Process.
[19:18:28] Getting Manifest... augment.vscode-augment
[19:18:28] Getting Manifest... stagewise.stagewise-vscode-extension
[19:18:29] Installing extension: augment.vscode-augment {
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[19:18:29] Installing extension: stagewise.stagewise-vscode-extension {
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[19:18:30] Extension signature verification result for stagewise.stagewise-vscode-extension: Success. Internal Code: 0. Executed: true. Duration: 1167ms.
[19:18:30] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 1074ms.
[19:18:30] Extracted extension to file:///home/<USER>/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.4.0: stagewise.stagewise-vscode-extension
[19:18:30] Renamed to /home/<USER>/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.4.0
[19:18:30] Marked extension as removed stagewise.stagewise-vscode-extension-0.2.2
[19:18:30] Extension installed successfully: stagewise.stagewise-vscode-extension file:///home/<USER>/.vscode-server/extensions/extensions.json
[19:18:31] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.470.1: augment.vscode-augment
[19:18:31] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.470.1
[19:18:31] Marked extension as removed augment.vscode-augment-0.467.1
[19:18:31] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[19:23:06] New EH opened, aborting shutdown
