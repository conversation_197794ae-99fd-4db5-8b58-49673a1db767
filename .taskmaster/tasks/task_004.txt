# Task ID: 4
# Title: CRM Implementation
# Status: pending
# Dependencies: 1
# Priority: medium
# Description: Develop a Customer Relationship Management module with client database, contact management, and commercial proposal tracking, including contract management and integration with billing system. Implement a seamless workflow from sales to billing, ensuring data consistency and accuracy throughout the entire process.
# Details:
Implement a complete CRM system with the following components:

1. Data models:
```python
# models/crm.py
class Client(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50))  # e.g., SME, Startup, Enterprise
    industry = db.Column(db.String(50))
    address = db.Column(db.Text)
    vat_number = db.Column(db.String(20))
    website = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    # Relationships
    contacts = db.relationship('Contact', backref='client', lazy=True)
    projects = db.relationship('Project', backref='client', lazy=True)
    proposals = db.relationship('Proposal', backref='client', lazy=True)
    contracts = db.relationship('Contract', backref='client', lazy=True)
    invoices = db.relationship('Invoice', backref='client', lazy=True)

class Contact(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    role = db.Column(db.String(50))
    is_primary = db.Column(db.Boolean, default=False)
    notes = db.Column(db.Text)

class Proposal(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    value = db.Column(db.Float)
    status = db.Column(db.String(20), default='draft')  # draft, sent, accepted, rejected
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    sent_at = db.Column(db.DateTime)
    valid_until = db.Column(db.Date)
    owner_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    # Document storage
    document_path = db.Column(db.String(255))
    # Relationships
    contracts = db.relationship('Contract', backref='proposal', lazy=True)

class Contract(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    proposal_id = db.Column(db.Integer, db.ForeignKey('proposal.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    value = db.Column(db.Float)
    status = db.Column(db.String(20), default='draft')  # draft, active, completed, terminated
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    signed_at = db.Column(db.DateTime)
    document_path = db.Column(db.String(255))
    # Relationships
    projects = db.relationship('Project', backref='contract', lazy=True)
    invoices = db.relationship('Invoice', backref='contract', lazy=True)
```

2. Implement client management interface with Vue 3 components:
   - Client list with filtering and search using Vue's reactive system
   - Client detail view with all related information as nested components
   - Client creation and editing forms using Vue's Composition API

3. Create contact management system with Vue components:
   - Contact list per client with sortable tables
   - Contact details and communication history
   - Primary contact designation with reactive updates

4. Develop proposal management with Vue components:
   - Proposal creation wizard with multi-step form
   - Status tracking and notifications using Pinia store
   - Document generation and storage
   - Conversion tracking (proposal to contract to project) with cross-module state management

5. Implement contract management with Vue components:
   - Contract creation from accepted proposals
   - Contract lifecycle management
   - Explicit linking between contracts and projects to ensure traceability
   - Document generation and storage

6. Develop billing integration with Vue components:
   - Complete quotation-to-project-to-billing workflow
   - Invoice generation based on contracts and timesheet entries
   - Billing cycle management with automated invoice generation
   - Payment tracking and reporting
   - Deep integration with timesheet system for accurate billable hours tracking

7. Implement activity tracking with Vue components:
   - Communication log with real-time updates
   - Follow-up reminders integrated with calendar system
   - Client interaction history with timeline visualization

8. Create a seamless workflow system:
   - End-to-end process from sales to billing with consistent data flow
   - Automated state transitions between sales, project management, and billing
   - Comprehensive dashboard for tracking the entire client lifecycle
   - Validation mechanisms to ensure data consistency across all stages

# Test Strategy:
1. Unit tests for Client, Contact, Proposal, Contract, and Invoice models
2. Vue component tests for client, proposal, contract, and billing workflows using Vue Test Utils
3. End-to-end testing with Cypress for CRM interfaces
4. Document generation testing
5. Search and filtering functionality tests
6. Data validation and integrity tests
7. Integration tests for the complete Customer → Proposal → Contract → Project → Tasks → Timesheet → Billing pipeline
8. Validation of timesheet-to-billing conversion accuracy
9. Contract-to-project conversion tests
10. End-to-end workflow testing from quotation to billing
11. Data consistency tests across the entire sales-to-billing process
12. Stress testing of the timesheet integration for large datasets
13. Billing accuracy tests with various timesheet scenarios

# Subtasks:
## 1. Modellazione dati & API [pending]
### Dependencies: None
### Description: Progettazione ed estensione dei modelli dati per Cliente, Proposta, Contratto, Progetto/Servizio, Fattura e Incasso. Implementazione degli endpoint API CRUD e delle relazioni tra le entità (quotation → contract → project/service → invoice → payment). Definizione delle regole di business per la creazione, modifica e collegamento tra i vari oggetti. Migrazioni e test di integrazione dei modelli.
### Details:


## 2. Frontend & Workflow CRM [pending]
### Dependencies: None
### Description: Sviluppo o estensione dei componenti Vue per la gestione di clienti, proposte, contratti, progetti/servizi, fatture e incassi. Implementazione di workflow visuali per la conversione di una proposta in contratto e progetto/servizio, e per la generazione e gestione delle fatture. UI/UX per pipeline commerciale, stato avanzamento, e gestione documentale.
### Details:


## 3. Integrazione Timesheet & Billing [pending]
### Dependencies: None
### Description: Collegamento tra timesheet, contratti, progetti/servizi e fatture. Implementazione della logica per la generazione automatica delle fatture a partire dalle ore lavorate e dalle tariffe contrattuali. Sincronizzazione dei dati tra CRM e timesheet, gestione dello stato di fatturazione (emessa, pagata, scaduta, parziale) e supporto per la fatturazione multipla su uno stesso contratto/progetto.
### Details:


## 4. Tracciamento Incassi & Reportistica [pending]
### Dependencies: None
### Description: Gestione dei pagamenti e degli incassi, reminder automatici per fatture scadute, riconciliazione degli incassi con le fatture. Sviluppo di dashboard e report per pipeline commerciale, fatturato, incassi, clienti insolventi, performance clienti e andamento dei progetti/servizi. Questo subtask si concentra su reportistica e dashboard operative per incassi, fatture, pipeline e performance clienti. La progettazione e realizzazione dei KPI trasversali e delle dashboard globali è demandata al task 9, con cui verranno condivisi dati e API.
### Details:


