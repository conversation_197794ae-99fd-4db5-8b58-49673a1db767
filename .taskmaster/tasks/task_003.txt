# Task ID: 3
# Title: Timesheet Management System
# Status: in-progress
# Dependencies: 2
# Priority: high
# Description: Implement a timesheet system for tracking and approving work hours, with reporting capabilities and integration with the project management module. The system will be the main feature for employees, easily accessible from the main menu and dashboard, with comprehensive approval workflows and request management. The system will also integrate with the future CRM and billing systems to support client invoicing workflows.
# Details:
Create a complete timesheet system:

1. Data models:
```python
# models/timesheet.py
class Timesheet(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('user.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected
    submission_date = db.Column(db.DateTime, nullable=True)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('user.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    # Relationships
    entries = db.relationship('TimesheetEntry', backref='timesheet', lazy=True)

class TimesheetEntry(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    timesheet_id = db.Column(db.Integer, db.ForeignKey('timesheet.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    task_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=True)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('contract.id'), nullable=True)
    hours = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    entry_type = db.Column(db.String(20), default='work')  # work, vacation, leave, smartworking
    billable = db.Column(db.Boolean, default=False)
    billing_rate = db.Column(db.Float, nullable=True)
    billing_status = db.Column(db.String(20), default='unbilled')  # unbilled, billed, non-billable
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=True)

class TimeOffRequest(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    request_type = db.Column(db.String(20), nullable=False)  # vacation, leave, smartworking
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    notes = db.Column(db.Text)
    submission_date = db.Column(db.DateTime, default=datetime.utcnow)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
```

2. Implement timesheet entry interface with Vue 3 components:
   - Weekly/daily view options using Vue Router for navigation
   - Project/task selection with reactive data binding
   - Client and contract selection for billable work
   - Hour logging with validation using Vue form handling
   - Billable/non-billable toggle with rate selection
   - Submission workflow with Pinia state management
   - Prominent placement in main menu and dashboard for easy access
   - Visual differentiation for work hours, vacation, leave, and smartworking entries

3. Create approval workflow with Vue components:
   - Manager notification of pending timesheets and time-off requests
   - Approval/rejection interface for both timesheets and requests
   - Comments and feedback system
   - Status tracking and visualization
   - Email notifications for status changes
   - Billing verification step for client-billable entries

4. Reporting features with Vue components:
   - Hours by project/task visualizations
   - Hours by client/contract visualizations
   - User productivity reports using Vue-compatible charting libraries
   - Export to CSV/Excel functionality
   - Billable vs non-billable time tracking
   - Unbilled hours reports for finance department
   - Time-off and smartworking analytics for HR

5. Integration with Project Management:
   - Task progress updates based on logged hours
   - Resource utilization tracking
   - Shared Pinia store for cross-module communication
   - Calendar integration for time-off visualization

6. Integration with CRM and Billing:
   - Client and contract data synchronization
   - Billable hours tracking with appropriate rates
   - Invoice generation data preparation
   - Billing status tracking (unbilled, billed, non-billable)
   - API endpoints for CRM to query billable hours
   - Workflow for marking hours as billed when invoiced

7. Notification System:
   - Real-time notifications for managers about pending approvals
   - HR notifications for time-off requests
   - Finance notifications for billable hours ready for invoicing
   - Email alerts for critical status changes
   - Dashboard widgets showing pending items

# Test Strategy:
1. Unit tests for Timesheet, TimesheetEntry, and TimeOffRequest models
2. Vue component tests using Vue Test Utils for timesheet submission and approval flows
3. End-to-end testing with Cypress for timesheet entry interface and request workflows
4. Report generation testing
5. Validation testing for business rules (max hours per day, vacation policy compliance, etc.)
6. Performance testing with bulk timesheet processing
7. Notification system testing
8. Mobile responsiveness testing for dashboard and main menu integration
9. Integration tests for CRM and billing system data exchange
10. Billing workflow testing with mock invoice generation
11. Contract rate application testing
12. Client-specific reporting accuracy tests

# Subtasks:
## 1. Database & API [pending]
### Dependencies: None
### Description: Estensione modelli, migrazioni, implementazione e test degli endpoint core (timesheet, time-off, notification).
### Details:


## 2. Frontend & UX [pending]
### Dependencies: None
### Description: Sviluppo/estensione componenti Vue, dashboard, viste manager/HR, workflow di approvazione, notifiche in-app.
### Details:


## 3. Permessi, Notifiche & Integrazione [pending]
### Dependencies: None
### Description: Gestione permessi, sistema notifiche, collegamento con CRM/billing, logica di fatturazione e legame con contratti/clienti.
### Details:


## 4. Testing, Validazione & Documentazione [pending]
### Dependencies: None
### Description: Test unitari, end-to-end, validazione permessi/business rules, aggiornamento documentazione tecnica e manuali utente.
### Details:


