# Task ID: 6
# Title: Funding and Grants Management
# Status: pending
# Dependencies: 2, 4
# Priority: medium
# Description: Implement a system for tracking funding opportunities, managing grant applications, and handling expense reporting for funded projects.
# Details:
Create a comprehensive funding management system:

1. Data models:
```python
# models/funding.py
class FundingOpportunity(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    source = db.Column(db.String(100))  # e.g., EU, National, Regional
    funding_type = db.Column(db.String(50))  # e.g., Grant, Loan, Investment
    max_funding = db.Column(db.Float)
    eligibility_criteria = db.Column(db.Text)
    opening_date = db.Column(db.Date)
    closing_date = db.Column(db.Date)
    website = db.Column(db.String(255))
    contact_info = db.Column(db.Text)
    status = db.Column(db.String(20), default='open')  # open, closed, upcoming
    created_by = db.Column(db.<PERSON>te<PERSON>, db.<PERSON>ey('user.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    # Relationships
    applications = db.relationship('FundingApplication', backref='opportunity', lazy=True)

class FundingApplication(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    opportunity_id = db.Column(db.Integer, db.ForeignKey('funding_opportunity.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'))
    title = db.Column(db.String(100), nullable=False)
    requested_amount = db.Column(db.Float)
    approved_amount = db.Column(db.Float)
    submission_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected
    approval_date = db.Column(db.Date)
    rejection_reason = db.Column(db.Text)
    notes = db.Column(db.Text)
    responsible_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # Relationships
    expenses = db.relationship('FundingExpense', backref='application', lazy=True)
    documents = db.relationship('FundingDocument', backref='application', lazy=True)

class FundingExpense(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    application_id = db.Column(db.Integer, db.ForeignKey('funding_application.id'), nullable=False)
    description = db.Column(db.String(255), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    date = db.Column(db.Date, nullable=False)
    category = db.Column(db.String(50))  # e.g., Personnel, Equipment, Travel
    supplier = db.Column(db.String(100))
    invoice_number = db.Column(db.String(50))
    payment_method = db.Column(db.String(50))
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    notes = db.Column(db.Text)
    receipt_path = db.Column(db.String(255))
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class FundingDocument(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    application_id = db.Column(db.Integer, db.ForeignKey('funding_application.id'), nullable=False)
    title = db.Column(db.String(100), nullable=False)
    document_type = db.Column(db.String(50))  # e.g., Application, Report, Invoice
    file_path = db.Column(db.String(255), nullable=False)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    version = db.Column(db.String(20), default='1.0')
```

2. Implement funding opportunity tracking with Vue 3 components:
   - Opportunity database with search and filtering using Vue's reactive system
   - Eligibility assessment tools as interactive components
   - Deadline notifications and reminders using Pinia store
   - Subscription to relevant categories with user preferences

3. Create application management workflow with Vue components:
   - Application creation wizard with multi-step form and route guards
   - Document preparation checklists as reusable components
   - Status tracking dashboard with reactive updates
   - Approval process management with state transitions

4. Develop expense tracking and reporting with Vue components:
   - Expense entry with receipt upload using Vue 3 file handling
   - Budget vs. actual tracking with reactive calculations
   - Expense categorization and validation with form validation
   - Report generation for funding bodies with exportable formats

5. Implement document management for funding with Vue components:
   - Template library for common documents
   - Version control for application documents using Pinia store
   - Approval workflows for submissions with state management

# Test Strategy:
1. Unit tests for all funding-related models
2. Vue component tests for application workflows using Vue Test Utils
3. Document generation and validation tests
4. Expense tracking and reporting tests
5. Notification system tests
6. Budget calculation and validation tests
