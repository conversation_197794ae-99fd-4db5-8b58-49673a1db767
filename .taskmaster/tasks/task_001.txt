# Task ID: 1
# Title: Complete Authentication System
# Status: done
# Dependencies: None
# Priority: high
# Description: Enhance the existing authentication system with password reset functionality, role-based authorization controls, and admin dashboard for user management.
# Details:
Building on the existing Flask-Login implementation, add:
1. Password reset flow with email verification
2. Role-based access control (RBAC) with roles: <PERSON><PERSON>, Manager, Employee, Sales
3. Admin dashboard for user management
4. Session management with timeout
5. Authorization middleware for route protection

Implementation using Flask blueprints:
```python
# models/user.py
class User(db.Model):
    # Existing fields
    role = db.Column(db.String(20), nullable=False, default='employee')
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    # Add password reset fields
    reset_token = db.Column(db.String(100), nullable=True)
    reset_token_expiry = db.Column(db.DateTime, nullable=True)

# blueprints/auth/routes.py
@auth_bp.route('/reset-password', methods=['GET', 'POST'])
def reset_password():
    # Implementation for password reset
    pass

# utils/decorators.py
def role_required(role):
    def wrapper(fn):
        @wraps(fn)
        def decorated_view(*args, **kwargs):
            if not current_user.is_authenticated or current_user.role != role:
                abort(403)
            return fn(*args, **kwargs)
        return decorated_view
    return wrapper
```

# Test Strategy:
1. Unit tests for User model with new fields
2. Integration tests for password reset flow
3. Authorization tests for each role type
4. Security testing for password policies
5. UI testing for admin dashboard
6. End-to-end testing of complete authentication flows
7. Integration tests for data filtering across all blueprints based on user roles

# Subtasks:
## 1. Implement Password Reset Flow [done]
### Dependencies: None
### Description: Create a secure password reset system with email verification
### Details:
Design and implement a password reset flow including: forgotten password form, secure token generation, email delivery system integration, token verification page, and password reset form with validation. Ensure tokens expire after a reasonable time period and can only be used once.
<info added on 2025-05-18T20:11:59.511Z>
Design and implement a password reset flow including: forgotten password form, secure token generation, email delivery system integration, token verification page, and password reset form with validation. Ensure tokens expire after a reasonable time period and can only be used once.

Implementation Plan:

1. Database Model Updates:
   - Add reset_token (String, nullable, unique) to User model
   - Add reset_token_expiry (DateTime, nullable) to User model
   - Tokens will be invalidated after use by setting both fields to None

2. Routes and Logic (in blueprints/auth/routes.py):
   - /forgot-password (GET/POST):
     * GET: Display forgot_password.html template
     * POST: Process email, generate token, send reset email
   - /reset-password/<token> (GET/POST):
     * GET: Verify token validity and display reset form
     * POST: Process new password, update user record, invalidate token

3. Utility Functions:
   - generate_reset_token(): Create cryptographically secure token
   - send_password_reset_email(user, token): Build reset URL and send email
   - verify_reset_token(token): Validate token existence and expiration

4. HTML Templates:
   - forgot_password.html: Email input form
   - reset_password_email.html: Email template with reset link
   - reset_password_form.html: New password and confirmation form

5. Form Classes (using Flask-WTF):
   - ForgotPasswordForm: Email field with validation
   - ResetPasswordForm: Password and confirmation fields with validation

6. Configuration:
   - Set PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS = 3600 (1 hour)
   - Add email service configuration parameters

7. Testing:
   - Unit tests for token generation and verification
   - Integration tests for the complete password reset flow
   - Test cases for valid/invalid inputs and edge cases
</info added on 2025-05-18T20:11:59.511Z>
<info added on 2025-05-18T20:42:48.211Z>
The email delivery for password reset is currently simulated through logging. The actual integration with a real email service will be handled in subtask 1.8. For now, when the reset password function is triggered, the system will generate the reset token and log the reset URL that would normally be sent via email. This allows for testing the password reset flow without requiring an actual email service configuration. Developers should check the application logs to retrieve the reset URLs during development and testing phases.
</info added on 2025-05-18T20:42:48.211Z>

## 2. Develop Role-Based Access Control (RBAC) System [done]
### Dependencies: None
### Description: Design and implement a comprehensive RBAC system
### Details:
Create a flexible role system with hierarchical permissions, define core roles (admin, user, etc.), implement role assignment functionality, and develop permission checking utilities. Include database schema updates to support roles and permissions.

## 3. Build Admin Dashboard for User Management [done]
### Dependencies: 1.2
### Description: Create an interface for administrators to manage users and roles
### Details:
Develop a secure admin dashboard with user listing, search and filtering capabilities, role assignment interface, account status management (activate/deactivate), and audit logging for administrative actions.

## 4. Implement Session Management with Timeout [done]
### Dependencies: None
### Description: Create a robust session handling system with security features
### Details:
Implement secure session creation, storage, and validation. Add configurable session timeout, automatic logout after inactivity, and session regeneration on privilege changes. Include 'remember me' functionality with secure persistent cookies.

## 5. Develop Authorization Middleware [done]
### Dependencies: 1.2, 1.4
### Description: Create middleware to enforce access control across the application
### Details:
Build middleware to validate user authentication status, check role-based permissions for requested resources, handle unauthorized access attempts, and integrate with the session management system. Include logging for security events.

## 6. Create Integration Tests [done]
### Dependencies: 1.1, 1.2, 1.3, 1.4, 1.5
### Description: Develop comprehensive tests for all authentication components
### Details:
Write integration tests covering all authentication flows including login, logout, password reset, role-based access, session management, and admin functionality. Include both positive and negative test cases to ensure security constraints are enforced.

## 7. Perform Security Validation and Audit [done]
### Dependencies: 1.6
### Description: Conduct thorough security review of the authentication system
### Details:
Perform security validation including: penetration testing, code review for security vulnerabilities, validation against OWASP top 10, checking for common authentication weaknesses, and documenting security measures implemented. Create a security report with findings and recommendations.

## 8. Implement Real Email Service for Password Reset [done]
### Dependencies: 1.1
### Description: Integrate a real email service (e.g., Flask-Mail with SendGrid/Mailgun or SMTP) to send password reset emails. Update email utility functions.
### Details:


## 9. Adattare i dati della dashboard ai permessi utente [done]
### Dependencies: 1.2
### Description: Modificare la logica della route /dashboard per recuperare e visualizzare i dati (es. progetti, task, KPI, clienti) in base al ruolo e ai permessi specifici dell'utente corrente. Passare la funzione user_has_permission al template per la visibilità condizionale dei widget.
### Details:


## 10. Implementare filtri RBAC nel blueprint reporting [done]
### Dependencies: 1.2, 1.5, 1.9
### Description: Applicare filtri basati sui ruoli per i dati di documentazione, spese e report finanziari nel blueprint reporting.
### Details:
Implementare filtri di sicurezza per garantire che:
- Admin e Manager possano visualizzare tutti i dati
- Altri ruoli vedano solo i propri dati o quelli relativi ai progetti a cui sono assegnati
- Aggiungere controlli di autorizzazione nelle query del database
- Implementare messaggi di errore appropriati per tentativi di accesso non autorizzato

## 11. Implementare filtri RBAC nel blueprint personnel [done]
### Dependencies: 1.2, 1.5, 1.9
### Description: Limitare l'accesso ai dati del personale in base ai ruoli e permessi utente.
### Details:
Modificare il blueprint personnel per:
- Limitare l'indice degli utenti in base ai permessi (admin/manager/HR vedono tutto, altri solo il proprio dipartimento o se stessi)
- Filtrare i dropdown di dipartimento e skill in base ai permessi dell'utente
- Implementare redirect e messaggi di errore per tentativi di accesso non autorizzato
- Garantire che tutte le query rispettino i permessi dell'utente corrente

## 12. Implementare filtri RBAC nel blueprint projects [done]
### Dependencies: 1.2, 1.5, 1.9
### Description: Applicare controlli di accesso basati sui ruoli per progetti, timesheet, eventi di calendario e dati correlati.
### Details:
Modificare il blueprint projects per:
- Filtrare l'accesso ai progetti in base al ruolo dell'utente e alle assegnazioni
- Limitare la visibilità dei timesheet in base ai permessi
- Filtrare gli eventi di calendario in base al ruolo e alle autorizzazioni
- Adattare tutte le query e i dropdown di selezione per rispettare i permessi
- Implementare gestione degli errori per tentativi di accesso non autorizzato

## 13. Testare il filtraggio dati RBAC in tutti i blueprint [done]
### Dependencies: 1.9, 1.10, 1.11, 1.12
### Description: Creare test di integrazione per verificare il corretto funzionamento del filtraggio dati basato sui ruoli in tutte le aree dell'applicazione.
### Details:
Sviluppare test che verifichino:
- Il corretto filtraggio dei dati in base ai ruoli utente in tutti i blueprint (dashboard, reporting, personnel, projects)
- La gestione appropriata dei tentativi di accesso non autorizzato
- La coerenza del sistema di filtraggio tra le diverse aree dell'applicazione
- Edge case e scenari di sicurezza critici

