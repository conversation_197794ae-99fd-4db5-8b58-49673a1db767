"""
API RESTful per la gestione delle dipendenze tra task.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc
from models import TaskDependency, Task, Project, User
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_MANAGE_PROJECT_TASKS,
    user_has_permission
)
from extensions import db

# Crea il blueprint per le API delle dipendenze tra task
api_task_dependencies = Blueprint('api_task_dependencies', __name__, url_prefix='/task-dependencies')

@api_task_dependencies.route('/', methods=['GET'])
@login_required
def get_task_dependencies():
    """
    Ottiene la lista delle dipendenze tra task con supporto per filtri e paginazione.
    ---
    tags:
      - task-dependencies
    parameters:
      - $ref: '#/components/parameters/pageParam'
      - $ref: '#/components/parameters/perPageParam'
      - name: task_id
        in: query
        description: Filtra per ID del task
        schema:
          type: integer
      - name: depends_on_id
        in: query
        description: Filtra per ID del task dipendente
        schema:
          type: integer
      - name: project_id
        in: query
        description: Filtra per ID del progetto
        schema:
          type: integer
    responses:
      200:
        description: Lista di dipendenze tra task
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    dependencies:
                      type: array
                      items:
                        $ref: '#/components/schemas/TaskDependency'
                pagination:
                  $ref: '#/components/schemas/Pagination'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        # Ottieni parametri di paginazione
        page, per_page = get_pagination_params()

        # Inizia la query
        query = TaskDependency.query

        # Applica filtri
        task_id = request.args.get('task_id', type=int)
        if task_id:
            query = query.filter(TaskDependency.task_id == task_id)

        depends_on_id = request.args.get('depends_on_id', type=int)
        if depends_on_id:
            query = query.filter(TaskDependency.depends_on_id == depends_on_id)

        # Modifichiamo l'approccio per evitare ambiguità nelle colonne
        # Creiamo una subquery per ottenere i task_id che appartengono al progetto specificato
        project_id = request.args.get('project_id', type=int)
        if project_id:
            # Ottieni i task_id che appartengono al progetto specificato
            task_ids_subquery = db.session.query(Task.id).filter(Task.project_id == project_id).subquery()
            # Filtra le dipendenze per i task_id ottenuti
            query = query.filter(TaskDependency.task_id.in_(task_ids_subquery))

        # Filtra per permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Se l'utente non ha il permesso di vedere tutti i progetti,
            # mostra solo le dipendenze dei task dei progetti a cui è assegnato

            # Ottieni i progetti a cui l'utente è assegnato
            user_projects_subquery = db.session.query(Project.id).join(
                Project.team_members
            ).filter(User.id == current_user.id).subquery()

            # Ottieni i task_id che appartengono ai progetti dell'utente
            user_tasks_subquery = db.session.query(Task.id).filter(
                Task.project_id.in_(user_projects_subquery)
            ).subquery()

            # Filtra le dipendenze per i task_id ottenuti
            query = query.filter(TaskDependency.task_id.in_(user_tasks_subquery))

        # Applica ordinamento
        query = query.order_by(desc(TaskDependency.id))

        # Esegui query con paginazione
        pagination = query.paginate(page=page, per_page=per_page)

        # Prepara i dati delle dipendenze
        dependencies_data = []
        for dependency in pagination.items:
            dependencies_data.append({
                'id': dependency.id,
                'task_id': dependency.task_id,
                'depends_on_id': dependency.depends_on_id,
                'task_name': dependency.task.name if dependency.task else None,
                'depends_on_name': dependency.depends_on.name if dependency.depends_on else None
            })

        # Restituisci risposta
        return api_response(
            data={'dependencies': dependencies_data},
            pagination=format_pagination(pagination)
        )
    except Exception as e:
        current_app.logger.error(f"Error in get_task_dependencies: {str(e)}")
        return handle_api_error(e)

@api_task_dependencies.route('/<int:dependency_id>', methods=['GET'])
@login_required
def get_task_dependency(dependency_id):
    """
    Ottiene i dettagli di una dipendenza specifica.
    ---
    tags:
      - task-dependencies
    parameters:
      - name: dependency_id
        in: path
        required: true
        description: ID della dipendenza
        schema:
          type: integer
    responses:
      200:
        description: Dettagli della dipendenza
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    dependency:
                      $ref: '#/components/schemas/TaskDependency'
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        dependency = TaskDependency.query.get_or_404(dependency_id)

        # Verifica permessi
        task = Task.query.get(dependency.task_id)
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Verifica se l'utente è assegnato al progetto del task
            is_in_project = current_user in task.project.team_members if task and task.project else False

            if not is_in_project:
                return api_response(
                    message="Non hai i permessi per visualizzare questa dipendenza",
                    status_code=403
                )

        # Prepara i dati della dipendenza
        dependency_data = {
            'id': dependency.id,
            'task_id': dependency.task_id,
            'depends_on_id': dependency.depends_on_id,
            'task_name': dependency.task.name if dependency.task else None,
            'depends_on_name': dependency.depends_on.name if dependency.depends_on else None
        }

        return api_response(data={'dependency': dependency_data})
    except Exception as e:
        current_app.logger.error(f"Error in get_task_dependency: {str(e)}")
        return handle_api_error(e)

@api_task_dependencies.route('/', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def create_task_dependency():
    """
    Crea una nuova dipendenza tra task.
    ---
    tags:
      - task-dependencies
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - task_id
              - depends_on_id
            properties:
              task_id:
                type: integer
                description: ID del task che dipende
              depends_on_id:
                type: integer
                description: ID del task da cui dipende
    responses:
      201:
        description: Dipendenza creata con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    dependency:
                      $ref: '#/components/schemas/TaskDependency'
                    message:
                      type: string
                      example: Dipendenza creata con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: ID task e ID task dipendente obbligatori
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json

        # Validazione dei dati
        if not data or not data.get('task_id') or not data.get('depends_on_id'):
            return api_response(
                message="ID task e ID task dipendente obbligatori",
                status_code=400
            )

        # Verifica che i task esistano
        task = Task.query.get(data.get('task_id'))
        depends_on = Task.query.get(data.get('depends_on_id'))

        if not task:
            return api_response(
                message="Task non trovato",
                status_code=404
            )

        if not depends_on:
            return api_response(
                message="Task dipendente non trovato",
                status_code=404
            )

        # Verifica che i task appartengano allo stesso progetto
        if task.project_id != depends_on.project_id:
            return api_response(
                message="I task devono appartenere allo stesso progetto",
                status_code=400
            )

        # Verifica che non si stia creando una dipendenza circolare
        # (il task non può dipendere da se stesso)
        if task.id == depends_on.id:
            return api_response(
                message="Un task non può dipendere da se stesso",
                status_code=400
            )

        # Verifica che la dipendenza non esista già
        existing_dependency = TaskDependency.query.filter_by(
            task_id=data.get('task_id'),
            depends_on_id=data.get('depends_on_id')
        ).first()

        if existing_dependency:
            return api_response(
                message="Questa dipendenza esiste già",
                status_code=400
            )

        # Crea la nuova dipendenza
        new_dependency = TaskDependency(
            task_id=data.get('task_id'),
            depends_on_id=data.get('depends_on_id')
        )

        # Aggiungi la dipendenza al database
        db.session.add(new_dependency)
        db.session.commit()

        # Prepara i dati della dipendenza per la risposta
        dependency_data = {
            'id': new_dependency.id,
            'task_id': new_dependency.task_id,
            'depends_on_id': new_dependency.depends_on_id,
            'task_name': task.name,
            'depends_on_name': depends_on.name
        }

        return api_response(
            data={'dependency': dependency_data},
            message="Dipendenza creata con successo",
            status_code=201
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in create_task_dependency: {str(e)}")
        return handle_api_error(e)

@api_task_dependencies.route('/<int:dependency_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def delete_task_dependency(dependency_id):
    """
    Elimina una dipendenza tra task.
    ---
    tags:
      - task-dependencies
    parameters:
      - name: dependency_id
        in: path
        required: true
        description: ID della dipendenza da eliminare
        schema:
          type: integer
    responses:
      200:
        description: Dipendenza eliminata con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Dipendenza eliminata con successo
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni la dipendenza dal database
        dependency = TaskDependency.query.get_or_404(dependency_id)

        # Ottieni i task coinvolti per il messaggio di risposta
        task = Task.query.get(dependency.task_id)
        depends_on = Task.query.get(dependency.depends_on_id)

        task_name = task.name if task else "Task sconosciuto"
        depends_on_name = depends_on.name if depends_on else "Task sconosciuto"

        # Elimina la dipendenza
        db.session.delete(dependency)
        db.session.commit()

        return api_response(
            message=f"Dipendenza eliminata con successo: '{task_name}' non dipende più da '{depends_on_name}'"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in delete_task_dependency: {str(e)}")
        return handle_api_error(e)