"""
API RESTful per la gestione dei task.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc
from models import Task, Project, User
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_MANAGE_PROJECT_TASKS,
    user_has_permission
)
from extensions import db

# Crea il blueprint per le API dei task
api_tasks = Blueprint('api_tasks', __name__, url_prefix='/tasks')

@api_tasks.route('/', methods=['GET'])
@login_required
def get_tasks():
    """
    Ottiene la lista dei task con supporto per filtri e paginazione.
    ---
    tags:
      - tasks
    parameters:
      - $ref: '#/components/parameters/pageParam'
      - $ref: '#/components/parameters/perPageParam'
      - name: project_id
        in: query
        description: Filtra per ID progetto
        schema:
          type: integer
      - name: status
        in: query
        description: Filtra per stato del task
        schema:
          type: string
          enum: [todo, in-progress, review, done]
      - name: priority
        in: query
        description: Filtra per priorità del task
        schema:
          type: string
          enum: [low, medium, high, urgent]
      - name: assignee_id
        in: query
        description: Filtra per ID dell'assegnatario
        schema:
          type: integer
      - name: search
        in: query
        description: Cerca nei nomi e nelle descrizioni dei task
        schema:
          type: string
    responses:
      200:
        description: Lista di task
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    tasks:
                      type: array
                      items:
                        $ref: '#/components/schemas/Task'
                pagination:
                  $ref: '#/components/schemas/Pagination'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        # Ottieni parametri di paginazione
        page, per_page = get_pagination_params()

        # Inizia la query
        query = Task.query

        # Applica filtri
        project_id = request.args.get('project_id', type=int)
        if project_id:
            query = query.filter(Task.project_id == project_id)

        status = request.args.get('status')
        if status:
            query = query.filter(Task.status == status)

        priority = request.args.get('priority')
        if priority:
            query = query.filter(Task.priority == priority)

        assignee_id = request.args.get('assignee_id', type=int)
        if assignee_id:
            query = query.filter(Task.assignee_id == assignee_id)

        search = request.args.get('search')
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (Task.name.ilike(search_term)) |
                (Task.description.ilike(search_term))
            )

        # Filtra per permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Se l'utente non ha il permesso di vedere tutti i progetti,
            # mostra solo i task assegnati a lui o dei progetti a cui è assegnato
            query = query.filter(
                (Task.assignee_id == current_user.id) |
                (Task.project_id.in_(
                    db.session.query(Project.id).join(
                        Project.team_members
                    ).filter(User.id == current_user.id)
                ))
            )

        # Applica ordinamento
        query = query.order_by(desc(Task.updated_at))

        # Esegui query con paginazione
        pagination = query.paginate(page=page, per_page=per_page)

        # Prepara i dati dei task
        tasks_data = []
        for task in pagination.items:
            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'description': task.description,
                'project_id': task.project_id,
                'assignee_id': task.assignee_id,
                'status': task.status,
                'priority': task.priority,
                'due_date': task.due_date.isoformat() if task.due_date else None,
                'created_at': task.created_at.isoformat(),
                'updated_at': task.updated_at.isoformat()
            })

        # Restituisci risposta
        return api_response(
            data={'tasks': tasks_data},
            pagination=format_pagination(pagination)
        )
    except Exception as e:
        current_app.logger.error(f"Error in get_tasks: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/<int:task_id>', methods=['GET'])
@login_required
def get_task(task_id):
    """
    Ottiene i dettagli di un task specifico.
    ---
    tags:
      - tasks
    parameters:
      - name: task_id
        in: path
        required: true
        description: ID del task
        schema:
          type: integer
    responses:
      200:
        description: Dettagli del task
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    task:
                      $ref: '#/components/schemas/Task'
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        task = Task.query.get_or_404(task_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Verifica se l'utente è assegnato al task o al progetto
            is_assignee = task.assignee_id == current_user.id
            is_in_project = current_user in task.project.team_members if task.project else False

            if not (is_assignee or is_in_project):
                return api_response(
                    message="Non hai i permessi per visualizzare questo task",
                    status_code=403
                )

        # Prepara i dati del task
        task_data = {
            'id': task.id,
            'name': task.name,
            'description': task.description,
            'project_id': task.project_id,
            'assignee_id': task.assignee_id,
            'status': task.status,
            'priority': task.priority,
            'due_date': task.due_date.isoformat() if task.due_date else None,
            'created_at': task.created_at.isoformat(),
            'updated_at': task.updated_at.isoformat()
        }

        return api_response(data={'task': task_data})
    except Exception as e:
        current_app.logger.error(f"Error in get_task: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def create_task():
    """
    Crea un nuovo task.
    ---
    tags:
      - tasks
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - name
              - project_id
            properties:
              name:
                type: string
                description: Nome del task
              description:
                type: string
                description: Descrizione del task
              project_id:
                type: integer
                description: ID del progetto associato
              assignee_id:
                type: integer
                description: ID dell'utente assegnato al task
              status:
                type: string
                enum: [todo, in-progress, review, done]
                default: todo
                description: Stato del task
              priority:
                type: string
                enum: [low, medium, high, urgent]
                default: medium
                description: Priorità del task
              due_date:
                type: string
                format: date
                description: Data di scadenza (YYYY-MM-DD)
    responses:
      201:
        description: Task creato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    task:
                      $ref: '#/components/schemas/Task'
                    message:
                      type: string
                      example: Task creato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Nome del task e ID progetto obbligatori
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json

        # Validazione dei dati
        if not data or not data.get('name') or not data.get('project_id'):
            return api_response(
                message="Nome del task e ID progetto obbligatori",
                status_code=400
            )

        # Verifica che il progetto esista
        project = Project.query.get(data.get('project_id'))
        if not project:
            return api_response(
                message="Progetto non trovato",
                status_code=404
            )

        # Crea il nuovo task
        new_task = Task(
            name=data.get('name'),
            description=data.get('description', ''),
            project_id=data.get('project_id'),
            assignee_id=data.get('assignee_id'),
            status=data.get('status', 'todo'),
            priority=data.get('priority', 'medium'),
            due_date=data.get('due_date')
        )

        # Aggiungi il task al database
        db.session.add(new_task)
        db.session.commit()

        # Prepara i dati del task per la risposta
        task_data = {
            'id': new_task.id,
            'name': new_task.name,
            'description': new_task.description,
            'project_id': new_task.project_id,
            'assignee_id': new_task.assignee_id,
            'status': new_task.status,
            'priority': new_task.priority,
            'due_date': new_task.due_date.isoformat() if new_task.due_date else None,
            'created_at': new_task.created_at.isoformat(),
            'updated_at': new_task.updated_at.isoformat()
        }

        return api_response(
            data={'task': task_data},
            message="Task creato con successo",
            status_code=201
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in create_task: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/<int:task_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def update_task(task_id):
    """
    Aggiorna un task esistente.
    ---
    tags:
      - tasks
    parameters:
      - name: task_id
        in: path
        required: true
        description: ID del task da aggiornare
        schema:
          type: integer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
                description: Nome del task
              description:
                type: string
                description: Descrizione del task
              project_id:
                type: integer
                description: ID del progetto associato
              assignee_id:
                type: integer
                description: ID dell'utente assegnato al task
              status:
                type: string
                enum: [todo, in-progress, review, done]
                description: Stato del task
              priority:
                type: string
                enum: [low, medium, high, urgent]
                description: Priorità del task
              due_date:
                type: string
                format: date
                description: Data di scadenza (YYYY-MM-DD)
    responses:
      200:
        description: Task aggiornato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    task:
                      $ref: '#/components/schemas/Task'
                    message:
                      type: string
                      example: Task aggiornato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Dati non validi
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni il task dal database
        task = Task.query.get_or_404(task_id)

        # Ottieni i dati dalla richiesta
        data = request.json
        if not data:
            return api_response(
                message="Nessun dato fornito per l'aggiornamento",
                status_code=400
            )

        # Aggiorna i campi del task
        if 'name' in data:
            task.name = data['name']
        if 'description' in data:
            task.description = data['description']
        if 'project_id' in data:
            # Verifica che il progetto esista
            project = Project.query.get(data['project_id'])
            if not project:
                return api_response(
                    message="Progetto non trovato",
                    status_code=404
                )
            task.project_id = data['project_id']
        if 'assignee_id' in data:
            # Verifica che l'utente esista se specificato
            if data['assignee_id']:
                user = User.query.get(data['assignee_id'])
                if not user:
                    return api_response(
                        message="Utente non trovato",
                        status_code=404
                    )
            task.assignee_id = data['assignee_id']
        if 'status' in data:
            task.status = data['status']
        if 'priority' in data:
            task.priority = data['priority']
        if 'due_date' in data:
            task.due_date = data['due_date']

        # Commit delle modifiche
        db.session.commit()

        # Prepara i dati del task per la risposta
        task_data = {
            'id': task.id,
            'name': task.name,
            'description': task.description,
            'project_id': task.project_id,
            'assignee_id': task.assignee_id,
            'status': task.status,
            'priority': task.priority,
            'due_date': task.due_date.isoformat() if task.due_date else None,
            'created_at': task.created_at.isoformat(),
            'updated_at': task.updated_at.isoformat()
        }

        return api_response(
            data={'task': task_data},
            message="Task aggiornato con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in update_task: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/status', methods=['PATCH'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def bulk_update_task_status():
    """
    Aggiorna lo stato di più task contemporaneamente.
    ---
    tags:
      - tasks
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - tasks
            properties:
              tasks:
                type: array
                items:
                  type: object
                  required:
                    - id
                    - status
                  properties:
                    id:
                      type: integer
                      description: ID del task
                    status:
                      type: string
                      enum: [todo, in-progress, review, done]
                      description: Nuovo stato del task
    responses:
      200:
        description: Stato dei task aggiornato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    results:
                      type: array
                      items:
                        type: object
                        properties:
                          task_id:
                            type: integer
                            example: 1
                          success:
                            type: boolean
                            example: true
                          message:
                            type: string
                            example: Stato aggiornato con successo
                    message:
                      type: string
                      example: Stato dei task aggiornato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Dati non validi
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json

        # Validazione dei dati
        if not data or 'tasks' not in data or not isinstance(data['tasks'], list):
            return api_response(
                message="Dati non validi: è richiesta una lista di task",
                status_code=400
            )

        # Risultati delle operazioni
        results = []

        # Aggiorna lo stato dei task
        for task_data in data['tasks']:
            # Validazione dei dati del task
            if 'id' not in task_data or 'status' not in task_data:
                results.append({
                    'task_id': task_data.get('id', 'unknown'),
                    'success': False,
                    'message': "ID task e stato obbligatori"
                })
                continue

            # Verifica che lo stato sia valido
            if task_data['status'] not in ['todo', 'in-progress', 'review', 'done']:
                results.append({
                    'task_id': task_data['id'],
                    'success': False,
                    'message': f"Stato '{task_data['status']}' non valido"
                })
                continue

            try:
                # Ottieni il task dal database
                task = Task.query.get(task_data['id'])
                if not task:
                    results.append({
                        'task_id': task_data['id'],
                        'success': False,
                        'message': "Task non trovato"
                    })
                    continue

                # Aggiorna lo stato del task
                old_status = task.status
                task.status = task_data['status']

                results.append({
                    'task_id': task.id,
                    'success': True,
                    'old_status': old_status,
                    'new_status': task.status,
                    'message': f"Stato aggiornato da '{old_status}' a '{task.status}'"
                })
            except Exception as e:
                results.append({
                    'task_id': task_data['id'],
                    'success': False,
                    'message': f"Errore durante l'aggiornamento: {str(e)}"
                })

        # Commit delle modifiche
        db.session.commit()

        # Conta i successi e i fallimenti
        success_count = sum(1 for result in results if result['success'])
        failure_count = len(results) - success_count

        return api_response(
            data={'results': results},
            message=f"Aggiornamento completato: {success_count} task aggiornati, {failure_count} falliti"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in bulk_update_task_status: {str(e)}")
        return handle_api_error(e)

@api_tasks.route('/<int:task_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_MANAGE_PROJECT_TASKS)
def delete_task(task_id):
    """
    Elimina un task esistente.
    ---
    tags:
      - tasks
    parameters:
      - name: task_id
        in: path
        required: true
        description: ID del task da eliminare
        schema:
          type: integer
    responses:
      200:
        description: Task eliminato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: Task eliminato con successo
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni il task dal database
        task = Task.query.get_or_404(task_id)

        # Salva il nome del task per il messaggio di risposta
        task_name = task.name

        # Elimina il task
        db.session.delete(task)
        db.session.commit()

        return api_response(
            message=f"Task '{task_name}' eliminato con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in delete_task: {str(e)}")
        return handle_api_error(e)