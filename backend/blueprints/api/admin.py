"""
API Blueprint per la gestione amministrativa degli utenti.
"""
from flask import Blueprint, request, jsonify
from flask_login import current_user
from werkzeug.security import generate_password_hash

from extensions import db
from models import User, ProjectKPITemplate
from utils.api_utils import api_response, api_login_required, handle_api_error
from utils.permissions import user_has_permission
from extensions import csrf

api_admin = Blueprint('api_admin', __name__)

@api_admin.route('/users', methods=['GET'])
@csrf.exempt
@api_login_required
def get_users():
    """Recupera tutti gli utenti del sistema."""
    if not user_has_permission(current_user.role, 'admin'):
        return api_response(False, 'Permessi insufficienti', status_code=403)
    
    try:
        users = User.query.all()
        users_data = []
        
        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'role': user.role,
                'is_active': user.is_active,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'position': user.position
            })
        
        return api_response(
            data={'users': users_data},
            message="Utenti recuperati con successo"
        )
    
    except Exception as e:
        return handle_api_error(e)

@api_admin.route('/users/<int:user_id>/reset-password', methods=['POST'])
@csrf.exempt
@api_login_required
def reset_user_password(user_id):
    """Reset password di un utente."""
    if not user_has_permission(current_user.role, 'admin'):
        return api_response(False, 'Permessi insufficienti', status_code=403)
    
    try:
        data = request.get_json()
        new_password = data.get('password')
        
        if not new_password or len(new_password) < 6:
            return api_response(
                data={},
                message='Password deve essere di almeno 6 caratteri',
                status_code=400
            )
        
        user = User.query.get(user_id)
        if not user:
            return api_response(
                data={},
                message='Utente non trovato',
                status_code=404
            )
        
        user.password_hash = generate_password_hash(new_password)
        db.session.commit()
        
        return api_response(
            data={},
            message=f'Password resettata per {user.username}'
        )
    
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


# KPI Templates endpoints
@api_admin.route('/kpi-templates', methods=['GET'])
@csrf.exempt
@api_login_required
def get_kpi_templates():
    """Recupera tutti i template KPI."""
    if not user_has_permission(current_user.role, 'admin'):
        return api_response(False, 'Permessi insufficienti', status_code=403)
    
    try:
        templates = ProjectKPITemplate.query.all()
        templates_data = []
        
        for template in templates:
            templates_data.append({
                'id': template.id,
                'project_type': template.project_type,
                'kpi_name': template.kpi_name,
                'target_min': template.target_min,
                'target_max': template.target_max,
                'warning_threshold': template.warning_threshold,
                'unit': template.unit,
                'description': template.description,
                'is_active': template.is_active,
                'created_at': template.created_at.isoformat() if template.created_at else None,
                'updated_at': template.updated_at.isoformat() if template.updated_at else None,
                'display_name': template.display_name
            })
        
        return api_response(
            data=templates_data,
            message="Template KPI recuperati con successo"
        )
    
    except Exception as e:
        return handle_api_error(e)


@api_admin.route('/kpi-templates', methods=['POST'])
@csrf.exempt
@api_login_required
def create_kpi_template():
    """Crea un nuovo template KPI."""
    if not user_has_permission(current_user.role, 'admin'):
        return api_response(False, 'Permessi insufficienti', status_code=403)
    
    try:
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['project_type', 'kpi_name']
        for field in required_fields:
            if not data.get(field):
                return api_response(
                    data={},
                    message=f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Verifica che non esista già lo stesso template
        existing = ProjectKPITemplate.query.filter_by(
            project_type=data['project_type'],
            kpi_name=data['kpi_name']
        ).first()
        
        if existing:
            return api_response(
                data={},
                message='Template già esistente per questa combinazione progetto/KPI',
                status_code=400
            )
        
        template = ProjectKPITemplate(
            project_type=data['project_type'],
            kpi_name=data['kpi_name'],
            target_min=data.get('target_min'),
            target_max=data.get('target_max'),
            warning_threshold=data.get('warning_threshold'),
            unit=data.get('unit', '%'),
            description=data.get('description'),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(template)
        db.session.commit()
        
        return api_response(
            data={'id': template.id},
            message='Template KPI creato con successo'
        )
    
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_admin.route('/kpi-templates/<int:template_id>', methods=['PUT'])
@csrf.exempt
@api_login_required
def update_kpi_template(template_id):
    """Aggiorna un template KPI."""
    if not user_has_permission(current_user.role, 'admin'):
        return api_response(False, 'Permessi insufficienti', status_code=403)
    
    try:
        template = ProjectKPITemplate.query.get(template_id)
        if not template:
            return api_response(
                data={},
                message='Template non trovato',
                status_code=404
            )
        
        data = request.get_json()
        
        # Aggiorna i campi forniti
        if 'project_type' in data:
            template.project_type = data['project_type']
        if 'kpi_name' in data:
            template.kpi_name = data['kpi_name']
        if 'target_min' in data:
            template.target_min = data['target_min']
        if 'target_max' in data:
            template.target_max = data['target_max']
        if 'warning_threshold' in data:
            template.warning_threshold = data['warning_threshold']
        if 'unit' in data:
            template.unit = data['unit']
        if 'description' in data:
            template.description = data['description']
        if 'is_active' in data:
            template.is_active = data['is_active']
        
        db.session.commit()
        
        return api_response(
            data={},
            message='Template KPI aggiornato con successo'
        )
    
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_admin.route('/kpi-templates/<int:template_id>/toggle', methods=['PUT'])
@csrf.exempt
@api_login_required
def toggle_kpi_template(template_id):
    """Toggle stato attivo/inattivo di un template KPI."""
    if not user_has_permission(current_user.role, 'admin'):
        return api_response(False, 'Permessi insufficienti', status_code=403)
    
    try:
        template = ProjectKPITemplate.query.get(template_id)
        if not template:
            return api_response(
                data={},
                message='Template non trovato',
                status_code=404
            )
        
        template.is_active = not template.is_active
        db.session.commit()
        
        return api_response(
            data={'is_active': template.is_active},
            message=f'Template {"attivato" if template.is_active else "disattivato"} con successo'
        )
    
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_admin.route('/kpi-templates/<int:template_id>', methods=['DELETE'])
@csrf.exempt
@api_login_required
def delete_kpi_template(template_id):
    """Elimina un template KPI."""
    if not user_has_permission(current_user.role, 'admin'):
        return api_response(False, 'Permessi insufficienti', status_code=403)
    
    try:
        template = ProjectKPITemplate.query.get(template_id)
        if not template:
            return api_response(
                data={},
                message='Template non trovato',
                status_code=404
            )
        
        db.session.delete(template)
        db.session.commit()
        
        return api_response(
            data={},
            message='Template KPI eliminato con successo'
        )
    
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_admin.route('/kpi-templates/reset', methods=['POST'])
@csrf.exempt
@api_login_required
def reset_kpi_templates():
    """Reset template KPI ai valori di default."""
    if not user_has_permission(current_user.role, 'admin'):
        return api_response(False, 'Permessi insufficienti', status_code=403)
    
    try:
        # Elimina tutti i template esistenti
        ProjectKPITemplate.query.delete()
        
        # Crea i template di default
        default_templates = [
            {
                'project_type': 'consulting',
                'kpi_name': 'margin_percentage',
                'target_min': 15.0,
                'target_max': 25.0,
                'warning_threshold': 10.0,
                'unit': '%',
                'description': 'Margine netto target per progetti di consulenza'
            },
            {
                'project_type': 'consulting',
                'kpi_name': 'utilization_rate',
                'target_min': 70.0,
                'target_max': 85.0,
                'warning_threshold': 60.0,
                'unit': '%',
                'description': 'Tasso di utilizzo risorse per progetti di consulenza'
            },
            {
                'project_type': 'service',
                'kpi_name': 'margin_percentage',
                'target_min': 20.0,
                'target_max': 30.0,
                'warning_threshold': 15.0,
                'unit': '%',
                'description': 'Margine netto target per progetti di servizio'
            },
            {
                'project_type': 'product',
                'kpi_name': 'cost_revenue_ratio',
                'target_min': 0.6,
                'target_max': 0.8,
                'warning_threshold': 0.9,
                'unit': 'ratio',
                'description': 'Rapporto costi/ricavi per sviluppo prodotti'
            }
        ]
        
        for template_data in default_templates:
            template = ProjectKPITemplate(**template_data)
            db.session.add(template)
        
        db.session.commit()
        
        return api_response(
            data={},
            message='Template KPI ripristinati ai valori di default'
        )
    
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)