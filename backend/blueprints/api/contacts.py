"""
API Blueprint per la gestione dei contatti clienti.
Task 4 - CRM Integration
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import and_, or_

from models import Contact, Client, User
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db, csrf

api_contacts = Blueprint('api_contacts', __name__)


@api_contacts.route('/', methods=['GET'])
@login_required
def get_contacts():
    """Recupera lista contatti con filtri"""
    try:
        # Parametri filtro
        client_id = request.args.get('client_id', type=int)
        search = request.args.get('search')  # Ricerca in nome/email
        
        # Paginazione
        page = request.args.get('page', type=int, default=1)
        per_page = request.args.get('per_page', type=int, default=50)
        
        # Query base
        query = Contact.query
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_clients'):
            return api_response(False, 'Non hai i permessi per visualizzare i contatti', status_code=403)
        
        # Applica filtri
        if client_id:
            query = query.filter(Contact.client_id == client_id)
            
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(
                or_(
                    Contact.first_name.ilike(search_pattern),
                    Contact.last_name.ilike(search_pattern),
                    Contact.email.ilike(search_pattern),
                    Contact.position.ilike(search_pattern)
                )
            )
        
        # Ordina per cognome, nome
        query = query.order_by(Contact.last_name, Contact.first_name)
        
        # Applica paginazione
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Prepara dati
        contacts_data = []
        for contact in paginated.items:
            contacts_data.append({
                'id': contact.id,
                'client_id': contact.client_id,
                'client': {
                    'id': contact.client.id,
                    'name': contact.client.name,
                    'company': getattr(contact.client, 'company', None)
                } if contact.client else None,
                'first_name': contact.first_name,
                'last_name': contact.last_name,
                'full_name': contact.full_name,
                'position': contact.position,
                'email': contact.email,
                'phone': contact.phone,
                'notes': contact.notes,
                'created_at': contact.created_at.isoformat(),
                'updated_at': contact.updated_at.isoformat()
            })
        
        return api_response(
            data={
                'contacts': contacts_data,
                'pagination': {
                    'page': paginated.page,
                    'pages': paginated.pages,
                    'per_page': paginated.per_page,
                    'total': paginated.total,
                    'has_next': paginated.has_next,
                    'has_prev': paginated.has_prev
                }
            },
            message=f"Recuperati {len(contacts_data)} contatti"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_contacts.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_contact():
    """Crea un nuovo contatto"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_clients'):
            return api_response(False, 'Non hai i permessi per creare contatti', status_code=403)
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['client_id', 'first_name', 'last_name']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Verifica che il cliente esista
        client = Client.query.get(data['client_id'])
        if not client:
            return api_response(
                False,
                'Cliente non trovato',
                status_code=404
            )
        
        # Verifica unicità email se fornita
        if data.get('email'):
            existing_contact = Contact.query.filter(Contact.email == data['email']).first()
            if existing_contact:
                return api_response(
                    False,
                    f'Esiste già un contatto con email {data["email"]}',
                    status_code=400
                )
        
        # Crea nuovo contatto
        contact = Contact(
            client_id=data['client_id'],
            first_name=data['first_name'],
            last_name=data['last_name'],
            position=data.get('position'),
            email=data.get('email'),
            phone=data.get('phone'),
            notes=data.get('notes', '')
        )
        
        db.session.add(contact)
        db.session.commit()
        
        return api_response(
            data={
                'id': contact.id,
                'client_id': contact.client_id,
                'client': {
                    'id': contact.client.id,
                    'name': contact.client.name
                },
                'first_name': contact.first_name,
                'last_name': contact.last_name,
                'full_name': contact.full_name,
                'position': contact.position,
                'email': contact.email,
                'phone': contact.phone,
                'notes': contact.notes,
                'created_at': contact.created_at.isoformat()
            },
            message='Contatto creato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_contacts.route('/<int:contact_id>', methods=['GET'])
@login_required
def get_contact(contact_id):
    """Recupera dettaglio singolo contatto"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_clients'):
            return api_response(False, 'Non hai i permessi per visualizzare i contatti', status_code=403)
        
        contact = Contact.query.get_or_404(contact_id)
        
        return api_response(
            data={
                'id': contact.id,
                'client_id': contact.client_id,
                'client': {
                    'id': contact.client.id,
                    'name': contact.client.name,
                    'company': getattr(contact.client, 'company', None),
                    'industry': contact.client.industry,
                    'website': contact.client.website
                } if contact.client else None,
                'first_name': contact.first_name,
                'last_name': contact.last_name,
                'full_name': contact.full_name,
                'position': contact.position,
                'email': contact.email,
                'phone': contact.phone,
                'notes': contact.notes,
                'created_at': contact.created_at.isoformat(),
                'updated_at': contact.updated_at.isoformat()
            },
            message="Dettaglio contatto recuperato con successo"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_contacts.route('/<int:contact_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_contact(contact_id):
    """Aggiorna un contatto esistente"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_clients'):
            return api_response(False, 'Non hai i permessi per modificare contatti', status_code=403)
        
        contact = Contact.query.get_or_404(contact_id)
        data = request.get_json()
        
        # Aggiorna campi se forniti
        if 'first_name' in data:
            contact.first_name = data['first_name']
        
        if 'last_name' in data:
            contact.last_name = data['last_name']
        
        if 'position' in data:
            contact.position = data['position']
        
        if 'email' in data:
            # Verifica unicità email se cambiata
            if data['email'] != contact.email:
                existing_contact = Contact.query.filter(
                    and_(Contact.email == data['email'], Contact.id != contact.id)
                ).first()
                if existing_contact:
                    return api_response(
                        False,
                        f'Esiste già un contatto con email {data["email"]}',
                        status_code=400
                    )
            contact.email = data['email']
        
        if 'phone' in data:
            contact.phone = data['phone']
        
        if 'notes' in data:
            contact.notes = data['notes']
        
        db.session.commit()
        
        return api_response(
            data={
                'id': contact.id,
                'first_name': contact.first_name,
                'last_name': contact.last_name,
                'full_name': contact.full_name,
                'position': contact.position,
                'email': contact.email,
                'phone': contact.phone,
                'updated_at': contact.updated_at.isoformat()
            },
            message='Contatto aggiornato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_contacts.route('/<int:contact_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_contact(contact_id):
    """Elimina un contatto"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_clients'):
            return api_response(False, 'Non hai i permessi per eliminare contatti', status_code=403)
        
        contact = Contact.query.get_or_404(contact_id)
        
        contact_name = contact.full_name
        db.session.delete(contact)
        db.session.commit()
        
        return api_response(
            message=f'Contatto {contact_name} eliminato con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)
