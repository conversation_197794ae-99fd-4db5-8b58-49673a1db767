"""
API RESTful per la gestione dei KPI generali.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc
from models import KPI, User
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_EDIT_PROJECT,
    user_has_permission
)
from extensions import db

# Crea il blueprint per le API dei KPI
api_kpis = Blueprint('api_kpis', __name__, url_prefix='/kpis')

@api_kpis.route('/', methods=['GET'])
@login_required
def get_kpis():
    """
    Ottiene la lista dei KPI con supporto per filtri e paginazione.
    ---
    tags:
      - kpis
    parameters:
      - $ref: '#/components/parameters/pageParam'
      - $ref: '#/components/parameters/perPageParam'
      - name: category
        in: query
        description: Filtra per categoria KPI
        schema:
          type: string
      - name: frequency
        in: query
        description: Filtra per frequenza KPI
        schema:
          type: string
          enum: ["daily", "weekly", "monthly", "quarterly", "annually"]
      - name: search
        in: query
        description: Cerca nei nomi e descrizioni dei KPI
        schema:
          type: string
    responses:
      200:
        description: Lista di KPI
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    kpis:
                      type: array
                      items:
                        $ref: '#/components/schemas/KPI'
                pagination:
                  $ref: '#/components/schemas/Pagination'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        # Ottieni parametri di paginazione
        page, per_page = get_pagination_params()

        # Inizia la query
        query = KPI.query

        # Applica filtri
        category = request.args.get('category')
        if category:
            query = query.filter(KPI.category.ilike(f'%{category}%'))

        frequency = request.args.get('frequency')
        if frequency:
            query = query.filter(KPI.frequency == frequency)

        search = request.args.get('search')
        if search:
            query = query.filter(
                db.or_(
                    KPI.name.ilike(f'%{search}%'),
                    KPI.description.ilike(f'%{search}%')
                )
            )

        # Applica ordinamento
        query = query.order_by(KPI.category, KPI.name)

        # Esegui query con paginazione
        pagination = query.paginate(page=page, per_page=per_page)
        kpis = pagination.items

        # Prepara i dati dei KPI per la risposta
        kpis_data = []
        for kpi in kpis:
            kpi_data = {
                'id': kpi.id,
                'name': kpi.name,
                'description': kpi.description,
                'category': kpi.category,
                'target_value': kpi.target_value,
                'current_value': kpi.current_value,
                'unit': kpi.unit,
                'frequency': kpi.frequency,
                'progress': kpi.progress,
                'created_at': kpi.created_at.isoformat() if kpi.created_at else None,
                'updated_at': kpi.updated_at.isoformat() if kpi.updated_at else None
            }
            kpis_data.append(kpi_data)

        return api_response(
            data={
                'kpis': kpis_data,
                'pagination': format_pagination(pagination)
            }
        )

    except Exception as e:
        current_app.logger.error(f"Error getting KPIs: {str(e)}")
        return handle_api_error(e)

@api_kpis.route('/<int:kpi_id>', methods=['GET'])
@login_required
def get_kpi(kpi_id):
    """
    Ottiene i dettagli di un KPI specifico.
    ---
    tags:
      - kpis
    parameters:
      - name: kpi_id
        in: path
        required: true
        description: ID del KPI
        schema:
          type: integer
    responses:
      200:
        description: Dettagli del KPI
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    kpi:
                      $ref: '#/components/schemas/KPI'
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        kpi = KPI.query.get_or_404(kpi_id)

        # Prepara i dati del KPI per la risposta
        kpi_data = {
            'id': kpi.id,
            'name': kpi.name,
            'description': kpi.description,
            'category': kpi.category,
            'target_value': kpi.target_value,
            'current_value': kpi.current_value,
            'unit': kpi.unit,
            'frequency': kpi.frequency,
            'progress': kpi.progress,
            'created_at': kpi.created_at.isoformat() if kpi.created_at else None,
            'updated_at': kpi.updated_at.isoformat() if kpi.updated_at else None
        }

        return api_response(data={'kpi': kpi_data})

    except Exception as e:
        current_app.logger.error(f"Error getting KPI {kpi_id}: {str(e)}")
        return handle_api_error(e)

@api_kpis.route('/', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def create_kpi():
    """
    Crea un nuovo KPI.
    ---
    tags:
      - kpis
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - name
            properties:
              name:
                type: string
                description: Nome del KPI
              description:
                type: string
                description: Descrizione del KPI
              category:
                type: string
                description: Categoria del KPI
              target_value:
                type: number
                description: Valore target del KPI
              current_value:
                type: number
                description: Valore attuale del KPI
                default: 0
              unit:
                type: string
                description: Unità di misura
              frequency:
                type: string
                enum: ["daily", "weekly", "monthly", "quarterly", "annually"]
                description: Frequenza di misurazione
    responses:
      201:
        description: KPI creato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    kpi:
                      $ref: '#/components/schemas/KPI'
                message:
                  type: string
                  example: "KPI creato con successo"
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: "Nome del KPI obbligatorio"
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        data = request.get_json()

        if not data:
            return api_response(
                message="Dati JSON richiesti",
                status_code=400
            )

        # Validazione campi obbligatori
        name = data.get('name', '').strip()
        if not name:
            return api_response(
                message="Nome del KPI obbligatorio",
                status_code=400
            )

        # Verifica che non esista già un KPI con lo stesso nome
        existing_kpi = KPI.query.filter_by(name=name).first()
        if existing_kpi:
            return api_response(
                message="Esiste già un KPI con questo nome",
                status_code=400
            )

        # Crea il nuovo KPI
        new_kpi = KPI(
            name=name,
            description=data.get('description', ''),
            category=data.get('category', ''),
            target_value=data.get('target_value'),
            current_value=data.get('current_value', 0.0),
            unit=data.get('unit', ''),
            frequency=data.get('frequency', 'monthly')
        )

        # Aggiungi il KPI al database
        db.session.add(new_kpi)
        db.session.commit()

        # Prepara i dati del KPI per la risposta
        kpi_data = {
            'id': new_kpi.id,
            'name': new_kpi.name,
            'description': new_kpi.description,
            'category': new_kpi.category,
            'target_value': new_kpi.target_value,
            'current_value': new_kpi.current_value,
            'unit': new_kpi.unit,
            'frequency': new_kpi.frequency,
            'progress': new_kpi.progress
        }

        return api_response(
            data={'kpi': kpi_data},
            message="KPI creato con successo",
            status_code=201
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error creating KPI: {str(e)}")
        return handle_api_error(e)

@api_kpis.route('/<int:kpi_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def update_kpi(kpi_id):
    """
    Aggiorna un KPI esistente.
    ---
    tags:
      - kpis
    parameters:
      - name: kpi_id
        in: path
        required: true
        description: ID del KPI da aggiornare
        schema:
          type: integer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
                description: Nome del KPI
              description:
                type: string
                description: Descrizione del KPI
              category:
                type: string
                description: Categoria del KPI
              target_value:
                type: number
                description: Valore target del KPI
              current_value:
                type: number
                description: Valore attuale del KPI
              unit:
                type: string
                description: Unità di misura
              frequency:
                type: string
                enum: ["daily", "weekly", "monthly", "quarterly", "annually"]
                description: Frequenza di misurazione
    responses:
      200:
        description: KPI aggiornato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    kpi:
                      $ref: '#/components/schemas/KPI'
                message:
                  type: string
                  example: "KPI aggiornato con successo"
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        kpi = KPI.query.get_or_404(kpi_id)
        data = request.get_json()

        if not data:
            return api_response(
                message="Dati JSON richiesti",
                status_code=400
            )

        # Aggiorna i campi se forniti
        if 'name' in data:
            name = data['name'].strip()
            if not name:
                return api_response(
                    message="Nome del KPI non può essere vuoto",
                    status_code=400
                )

            # Verifica che non esista già un altro KPI con lo stesso nome
            existing_kpi = KPI.query.filter(KPI.name == name, KPI.id != kpi_id).first()
            if existing_kpi:
                return api_response(
                    message="Esiste già un altro KPI con questo nome",
                    status_code=400
                )

            kpi.name = name

        if 'description' in data:
            kpi.description = data['description']

        if 'category' in data:
            kpi.category = data['category']

        if 'target_value' in data:
            kpi.target_value = data['target_value']

        if 'current_value' in data:
            kpi.current_value = data['current_value']

        if 'unit' in data:
            kpi.unit = data['unit']

        if 'frequency' in data:
            kpi.frequency = data['frequency']

        # Commit delle modifiche
        db.session.commit()

        # Prepara i dati del KPI per la risposta
        kpi_data = {
            'id': kpi.id,
            'name': kpi.name,
            'description': kpi.description,
            'category': kpi.category,
            'target_value': kpi.target_value,
            'current_value': kpi.current_value,
            'unit': kpi.unit,
            'frequency': kpi.frequency,
            'progress': kpi.progress
        }

        return api_response(
            data={'kpi': kpi_data},
            message="KPI aggiornato con successo"
        )

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating KPI {kpi_id}: {str(e)}")
        return handle_api_error(e)

@api_kpis.route('/<int:kpi_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def delete_kpi(kpi_id):
    """
    Elimina un KPI.
    ---
    tags:
      - kpis
    parameters:
      - name: kpi_id
        in: path
        required: true
        description: ID del KPI da eliminare
        schema:
          type: integer
    responses:
      200:
        description: KPI eliminato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: "KPI eliminato con successo"
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
      400:
        description: KPI in uso, impossibile eliminare
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: "Impossibile eliminare KPI: è utilizzato in progetti"
    """
    try:
        kpi = KPI.query.get_or_404(kpi_id)

        # Verifica se il KPI è utilizzato in progetti
        if kpi.project_links:
            return api_response(
                message="Impossibile eliminare KPI: è utilizzato in progetti",
                status_code=400
            )

        # Elimina il KPI
        db.session.delete(kpi)
        db.session.commit()

        return api_response(message="KPI eliminato con successo")

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error deleting KPI {kpi_id}: {str(e)}")
        return handle_api_error(e)
