"""
API Blueprint per la gestione delle fatture e fatturazione automatica.
Task 3.1 + 4 - CRM/Billing Integration
"""

from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import and_, or_, extract, func
from datetime import datetime, date, timedelta
from calendar import monthrange

from models import Invoice, InvoiceLine, Contract, Client, Project, TimesheetEntry, User
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db, csrf

api_invoices = Blueprint('api_invoices', __name__)


@api_invoices.route('/', methods=['GET'])
@login_required
def get_invoices():
    """Recupera lista fatture con filtri"""
    try:
        # Parametri filtro
        client_id = request.args.get('client_id', type=int)
        status = request.args.get('status')  # draft, sent, paid, overdue
        start_date = request.args.get('start_date')  # YYYY-MM-DD
        end_date = request.args.get('end_date')  # YYYY-MM-DD
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        search = request.args.get('search')  # Ricerca in numero fattura
        
        # Paginazione
        page = request.args.get('page', type=int, default=1)
        per_page = request.args.get('per_page', type=int, default=50)
        
        # Query base
        query = Invoice.query
        
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_invoices'):
            return api_response(False, 'Non hai i permessi per visualizzare le fatture', status_code=403)
        
        # Applica filtri
        if client_id:
            query = query.filter(Invoice.client_id == client_id)
            
        if status:
            query = query.filter(Invoice.status == status)
            
        if start_date:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(Invoice.issue_date >= start_date_obj)
            
        if end_date:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(Invoice.issue_date <= end_date_obj)
            
        if year:
            query = query.filter(extract('year', Invoice.issue_date) == year)
            
        if month:
            query = query.filter(extract('month', Invoice.issue_date) == month)
            
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(Invoice.invoice_number.ilike(search_pattern))
        
        # Ordina per data emissione (più recenti prima)
        query = query.order_by(Invoice.issue_date.desc())
        
        # Applica paginazione
        paginated = query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # Prepara dati
        invoices_data = []
        for invoice in paginated.items:
            invoices_data.append({
                'id': invoice.id,
                'client_id': invoice.client_id,
                'client': {
                    'id': invoice.client.id,
                    'name': invoice.client.name,
                    'company': invoice.client.company
                } if invoice.client else None,
                'invoice_number': invoice.invoice_number,
                'status': invoice.status,
                'billing_period_start': invoice.billing_period_start.isoformat() if invoice.billing_period_start else None,
                'billing_period_end': invoice.billing_period_end.isoformat() if invoice.billing_period_end else None,
                'issue_date': invoice.issue_date.isoformat() if invoice.issue_date else None,
                'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
                'subtotal': invoice.subtotal,
                'tax_amount': invoice.tax_amount,
                'total_amount': invoice.total_amount,
                'lines_count': len(invoice.lines) if invoice.lines else 0,
                'created_at': invoice.created_at.isoformat(),
                'updated_at': invoice.updated_at.isoformat()
            })
        
        return api_response(
            data={
                'invoices': invoices_data,
                'pagination': {
                    'page': paginated.page,
                    'pages': paginated.pages,
                    'per_page': paginated.per_page,
                    'total': paginated.total,
                    'has_next': paginated.has_next,
                    'has_prev': paginated.has_prev
                }
            },
            message=f"Recuperate {len(invoices_data)} fatture"
        )
        
    except Exception as e:
        return handle_api_error(e)


@api_invoices.route('/generate', methods=['POST'])
@csrf.exempt
@login_required
def generate_invoice():
    """Genera fattura automatica da timesheet entries per periodo"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_invoices'):
            return api_response(False, 'Non hai i permessi per generare fatture', status_code=403)
        
        data = request.get_json()
        
        # Validazione campi richiesti
        required_fields = ['client_id', 'billing_period_start', 'billing_period_end']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )
        
        # Verifica che il cliente esista
        client = Client.query.get(data['client_id'])
        if not client:
            return api_response(
                False,
                'Cliente non trovato',
                status_code=404
            )
        
        # Parsing date periodo fatturazione
        try:
            billing_start = datetime.strptime(data['billing_period_start'], '%Y-%m-%d').date()
            billing_end = datetime.strptime(data['billing_period_end'], '%Y-%m-%d').date()
        except ValueError:
            return api_response(
                False,
                'Formato date non valido. Utilizzare YYYY-MM-DD',
                status_code=400
            )
        
        # Validazione logica date
        if billing_start > billing_end:
            return api_response(
                False,
                'La data di inizio periodo non può essere successiva alla data di fine',
                status_code=400
            )
        
        # Trova timesheet entries fatturabili per il cliente nel periodo
        timesheet_entries = TimesheetEntry.query.join(Project).join(Contract).filter(
            and_(
                Contract.client_id == data['client_id'],
                TimesheetEntry.date >= billing_start,
                TimesheetEntry.date <= billing_end,
                TimesheetEntry.billable == True,
                TimesheetEntry.billing_status == 'unbilled'
            )
        ).all()
        
        if not timesheet_entries:
            return api_response(
                False,
                f'Nessuna timesheet entry fatturabile trovata per il cliente nel periodo {billing_start} - {billing_end}',
                status_code=400
            )
        
        # Genera numero fattura
        year = datetime.now().year
        last_invoice = Invoice.query.filter(
            Invoice.invoice_number.like(f'INV-{year}-%')
        ).order_by(Invoice.invoice_number.desc()).first()
        
        if last_invoice:
            try:
                last_num = int(last_invoice.invoice_number.split('-')[2])
                next_num = last_num + 1
            except:
                next_num = 1
        else:
            next_num = 1
        
        invoice_number = f"INV-{year}-{next_num:04d}"
        
        # Crea fattura
        invoice = Invoice(
            client_id=data['client_id'],
            invoice_number=invoice_number,
            status='draft',
            billing_period_start=billing_start,
            billing_period_end=billing_end,
            issue_date=date.today(),
            due_date=date.today() + timedelta(days=30),  # Default 30 giorni
            subtotal=0,
            tax_rate=data.get('tax_rate', 22.0),  # Default IVA 22%
            tax_amount=0,
            total_amount=0
        )
        
        db.session.add(invoice)
        db.session.flush()  # Per ottenere l'ID
        
        # Raggruppa entries per progetto/contratto
        project_groups = {}
        for entry in timesheet_entries:
            project_id = entry.project_id
            if project_id not in project_groups:
                project_groups[project_id] = {
                    'project': entry.project,
                    'contract': entry.project.contract,
                    'entries': [],
                    'total_hours': 0,
                    'total_amount': 0
                }
            
            project_groups[project_id]['entries'].append(entry)
            project_groups[project_id]['total_hours'] += entry.hours
            
            # Calcola importo: usa billing_rate dell'entry o hourly_rate del contratto
            rate = entry.billing_rate or (entry.project.contract.hourly_rate if entry.project.contract else 0)
            amount = entry.hours * rate
            project_groups[project_id]['total_amount'] += amount
        
        # Crea righe fattura
        total_subtotal = 0
        for project_id, group in project_groups.items():
            invoice_line = InvoiceLine(
                invoice_id=invoice.id,
                project_id=project_id,
                contract_id=group['contract'].id if group['contract'] else None,
                description=f"Servizi professionali - {group['project'].name}",
                total_hours=group['total_hours'],
                hourly_rate=group['contract'].hourly_rate if group['contract'] else 0,
                total_amount=group['total_amount']
            )
            
            db.session.add(invoice_line)
            total_subtotal += group['total_amount']
            
            # Marca entries come fatturate
            for entry in group['entries']:
                entry.billing_status = 'billed'
                entry.invoice_line_id = invoice_line.id
        
        # Aggiorna totali fattura
        invoice.subtotal = total_subtotal
        invoice.tax_amount = total_subtotal * (invoice.tax_rate / 100)
        invoice.total_amount = invoice.subtotal + invoice.tax_amount
        
        db.session.commit()
        
        return api_response(
            data={
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'client': {
                    'id': invoice.client.id,
                    'name': invoice.client.name,
                    'company': invoice.client.company
                },
                'billing_period_start': invoice.billing_period_start.isoformat(),
                'billing_period_end': invoice.billing_period_end.isoformat(),
                'subtotal': invoice.subtotal,
                'tax_amount': invoice.tax_amount,
                'total_amount': invoice.total_amount,
                'lines_count': len(invoice.lines),
                'timesheet_entries_count': len(timesheet_entries)
            },
            message=f'Fattura {invoice_number} generata con successo'
        )
        
    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_invoices.route('/<int:invoice_id>', methods=['GET'])
@login_required
def get_invoice(invoice_id):
    """Recupera dettaglio singola fattura"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'view_all_invoices'):
            return api_response(False, 'Non hai i permessi per visualizzare le fatture', status_code=403)

        invoice = Invoice.query.get_or_404(invoice_id)

        # Prepara righe fattura
        lines_data = []
        for line in invoice.lines:
            lines_data.append({
                'id': line.id,
                'project_id': line.project_id,
                'project': {
                    'id': line.project.id,
                    'name': line.project.name
                } if line.project else None,
                'contract_id': line.contract_id,
                'contract': {
                    'id': line.contract.id,
                    'contract_number': line.contract.contract_number,
                    'title': line.contract.title
                } if line.contract else None,
                'description': line.description,
                'total_hours': line.total_hours,
                'hourly_rate': line.hourly_rate,
                'total_amount': line.total_amount
            })

        return api_response(
            data={
                'id': invoice.id,
                'client_id': invoice.client_id,
                'client': {
                    'id': invoice.client.id,
                    'name': invoice.client.name,
                    'company': invoice.client.company,
                    'email': invoice.client.email,
                    'phone': invoice.client.phone,
                    'address': invoice.client.address
                } if invoice.client else None,
                'invoice_number': invoice.invoice_number,
                'status': invoice.status,
                'billing_period_start': invoice.billing_period_start.isoformat() if invoice.billing_period_start else None,
                'billing_period_end': invoice.billing_period_end.isoformat() if invoice.billing_period_end else None,
                'issue_date': invoice.issue_date.isoformat() if invoice.issue_date else None,
                'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
                'subtotal': invoice.subtotal,
                'tax_rate': invoice.tax_rate,
                'tax_amount': invoice.tax_amount,
                'total_amount': invoice.total_amount,
                'notes': invoice.notes,
                'lines': lines_data,
                'created_at': invoice.created_at.isoformat(),
                'updated_at': invoice.updated_at.isoformat()
            },
            message="Dettaglio fattura recuperato con successo"
        )

    except Exception as e:
        return handle_api_error(e)


@api_invoices.route('/<int:invoice_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_invoice(invoice_id):
    """Aggiorna una fattura esistente"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_invoices'):
            return api_response(False, 'Non hai i permessi per modificare fatture', status_code=403)

        invoice = Invoice.query.get_or_404(invoice_id)
        data = request.get_json()

        # Non permettere modifiche a fatture già inviate/pagate
        if invoice.status in ['sent', 'paid'] and 'status' not in data:
            return api_response(
                False,
                'Non è possibile modificare fatture già inviate o pagate',
                status_code=400
            )

        # Aggiorna campi se forniti
        if 'status' in data:
            valid_statuses = ['draft', 'sent', 'paid', 'overdue']
            if data['status'] not in valid_statuses:
                return api_response(
                    False,
                    f'Status non valido. Valori ammessi: {", ".join(valid_statuses)}',
                    status_code=400
                )
            invoice.status = data['status']

        if 'issue_date' in data and data['issue_date']:
            try:
                invoice.issue_date = datetime.strptime(data['issue_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato issue_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )

        if 'due_date' in data and data['due_date']:
            try:
                invoice.due_date = datetime.strptime(data['due_date'], '%Y-%m-%d').date()
            except ValueError:
                return api_response(
                    False,
                    'Formato due_date non valido. Utilizzare YYYY-MM-DD',
                    status_code=400
                )

        if 'tax_rate' in data:
            invoice.tax_rate = data['tax_rate']
            # Ricalcola tasse
            invoice.tax_amount = invoice.subtotal * (invoice.tax_rate / 100)
            invoice.total_amount = invoice.subtotal + invoice.tax_amount

        if 'notes' in data:
            invoice.notes = data['notes']

        db.session.commit()

        return api_response(
            data={
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'status': invoice.status,
                'total_amount': invoice.total_amount,
                'updated_at': invoice.updated_at.isoformat()
            },
            message='Fattura aggiornata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_invoices.route('/<int:invoice_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_invoice(invoice_id):
    """Elimina una fattura"""
    try:
        # Controllo permessi
        if not user_has_permission(current_user.role, 'manage_invoices'):
            return api_response(False, 'Non hai i permessi per eliminare fatture', status_code=403)

        invoice = Invoice.query.get_or_404(invoice_id)

        # Non permettere eliminazione di fatture già inviate/pagate
        if invoice.status in ['sent', 'paid']:
            return api_response(
                False,
                'Non è possibile eliminare fatture già inviate o pagate',
                status_code=400
            )

        # Rimetti timesheet entries come non fatturate
        for line in invoice.lines:
            TimesheetEntry.query.filter(
                TimesheetEntry.invoice_line_id == line.id
            ).update({
                'billing_status': 'unbilled',
                'invoice_line_id': None
            })

        # Elimina righe fattura
        for line in invoice.lines:
            db.session.delete(line)

        # Elimina fattura
        invoice_number = invoice.invoice_number
        db.session.delete(invoice)
        db.session.commit()

        return api_response(
            message=f'Fattura {invoice_number} eliminata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)
