"""
API RESTful per la gestione dei KPI di progetto.
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from sqlalchemy import desc
from models import ProjectKPI, Project, KPI, User
from utils.api_utils import (
    api_response, handle_api_error, get_pagination_params,
    format_pagination, api_permission_required
)
from utils.permissions import (
    PERMISSION_VIEW_ALL_PROJECTS, PERMISSION_EDIT_PROJECT,
    user_has_permission
)
from extensions import db

# Crea il blueprint per le API dei KPI di progetto
api_project_kpis = Blueprint('api_project_kpis', __name__, url_prefix='/project-kpis')

@api_project_kpis.route('/', methods=['GET'])
@login_required
def get_project_kpis():
    """
    Ottiene la lista dei KPI di progetto con supporto per filtri e paginazione.
    ---
    tags:
      - project-kpis
    parameters:
      - $ref: '#/components/parameters/pageParam'
      - $ref: '#/components/parameters/perPageParam'
      - name: project_id
        in: query
        description: Filtra per ID progetto
        schema:
          type: integer
      - name: kpi_id
        in: query
        description: Filtra per ID KPI
        schema:
          type: integer
    responses:
      200:
        description: Lista di KPI di progetto
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    project_kpis:
                      type: array
                      items:
                        $ref: '#/components/schemas/ProjectKPI'
                pagination:
                  $ref: '#/components/schemas/Pagination'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        # Ottieni parametri di paginazione
        page, per_page = get_pagination_params()

        # Inizia la query
        query = ProjectKPI.query

        # Applica filtri
        project_id = request.args.get('project_id', type=int)
        if project_id:
            query = query.filter(ProjectKPI.project_id == project_id)

        kpi_id = request.args.get('kpi_id', type=int)
        if kpi_id:
            query = query.filter(ProjectKPI.kpi_id == kpi_id)

        # Filtra per permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Se l'utente non ha il permesso di vedere tutti i progetti,
            # mostra solo i KPI dei progetti a cui è assegnato
            query = query.filter(
                ProjectKPI.project_id.in_(
                    db.session.query(Project.id).join(
                        Project.team_members
                    ).filter(User.id == current_user.id)
                )
            )

        # Applica ordinamento
        query = query.order_by(desc(ProjectKPI.project_id))

        # Esegui query con paginazione
        pagination = query.paginate(page=page, per_page=per_page)

        # Prepara i dati dei KPI di progetto
        project_kpis_data = []
        for project_kpi in pagination.items:
            project_kpis_data.append({
                'id': project_kpi.id,
                'project_id': project_kpi.project_id,
                'kpi_id': project_kpi.kpi_id,
                'target_value': project_kpi.target_value,
                'current_value': project_kpi.current_value,
                'project_name': project_kpi.project.name if project_kpi.project else None,
                'kpi_name': project_kpi.kpi.name if project_kpi.kpi else None,
                'kpi_unit': project_kpi.kpi.unit if project_kpi.kpi else None,
                'progress': round((project_kpi.current_value / project_kpi.target_value) * 100, 2) if project_kpi.target_value and project_kpi.target_value > 0 else 0
            })

        # Restituisci risposta
        return api_response(
            data={'project_kpis': project_kpis_data},
            pagination=format_pagination(pagination)
        )
    except Exception as e:
        current_app.logger.error(f"Error in get_project_kpis: {str(e)}")
        return handle_api_error(e)

@api_project_kpis.route('/<int:project_kpi_id>', methods=['GET'])
@login_required
def get_project_kpi(project_kpi_id):
    """
    Ottiene i dettagli di un KPI di progetto specifico.
    ---
    tags:
      - project-kpis
    parameters:
      - name: project_kpi_id
        in: path
        required: true
        description: ID del KPI di progetto
        schema:
          type: integer
    responses:
      200:
        description: Dettagli del KPI di progetto
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    project_kpi:
                      $ref: '#/components/schemas/ProjectKPI'
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
    """
    try:
        project_kpi = ProjectKPI.query.get_or_404(project_kpi_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, PERMISSION_VIEW_ALL_PROJECTS):
            # Verifica se l'utente è assegnato al progetto
            is_in_project = current_user in project_kpi.project.team_members if project_kpi.project else False

            if not is_in_project:
                return api_response(
                    message="Non hai i permessi per visualizzare questo KPI di progetto",
                    status_code=403
                )

        # Prepara i dati del KPI di progetto
        project_kpi_data = {
            'id': project_kpi.id,
            'project_id': project_kpi.project_id,
            'kpi_id': project_kpi.kpi_id,
            'target_value': project_kpi.target_value,
            'current_value': project_kpi.current_value,
            'project_name': project_kpi.project.name if project_kpi.project else None,
            'kpi_name': project_kpi.kpi.name if project_kpi.kpi else None,
            'kpi_description': project_kpi.kpi.description if project_kpi.kpi else None,
            'kpi_unit': project_kpi.kpi.unit if project_kpi.kpi else None,
            'progress': round((project_kpi.current_value / project_kpi.target_value) * 100, 2) if project_kpi.target_value and project_kpi.target_value > 0 else 0
        }

        return api_response(data={'project_kpi': project_kpi_data})
    except Exception as e:
        current_app.logger.error(f"Error in get_project_kpi: {str(e)}")
        return handle_api_error(e)

@api_project_kpis.route('/', methods=['POST'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def create_project_kpi():
    """
    Crea un nuovo KPI di progetto.
    ---
    tags:
      - project-kpis
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - project_id
              - kpi_id
            properties:
              project_id:
                type: integer
                description: ID del progetto
              kpi_id:
                type: integer
                description: ID del KPI
              target_value:
                type: number
                description: Valore target del KPI
              current_value:
                type: number
                description: Valore attuale del KPI
                default: 0
    responses:
      201:
        description: KPI di progetto creato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    project_kpi:
                      $ref: '#/components/schemas/ProjectKPI'
                    message:
                      type: string
                      example: KPI di progetto creato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: ID progetto e ID KPI obbligatori
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni i dati dalla richiesta
        data = request.json

        # Validazione dei dati
        if not data or not data.get('project_id') or not data.get('kpi_id'):
            return api_response(
                message="ID progetto e ID KPI obbligatori",
                status_code=400
            )

        # Verifica che il progetto esista
        project = Project.query.get(data.get('project_id'))
        if not project:
            return api_response(
                message="Progetto non trovato",
                status_code=404
            )

        # Verifica che il KPI esista
        kpi = KPI.query.get(data.get('kpi_id'))
        if not kpi:
            return api_response(
                message="KPI non trovato",
                status_code=404
            )

        # Verifica che il KPI non sia già associato al progetto
        existing_project_kpi = ProjectKPI.query.filter_by(
            project_id=data.get('project_id'),
            kpi_id=data.get('kpi_id')
        ).first()

        if existing_project_kpi:
            return api_response(
                message="Questo KPI è già associato al progetto",
                status_code=400
            )

        # Crea il nuovo KPI di progetto
        new_project_kpi = ProjectKPI(
            project_id=data.get('project_id'),
            kpi_id=data.get('kpi_id'),
            target_value=data.get('target_value', 0),
            current_value=data.get('current_value', 0)
        )

        # Aggiungi il KPI di progetto al database
        db.session.add(new_project_kpi)
        db.session.commit()

        # Prepara i dati del KPI di progetto per la risposta
        project_kpi_data = {
            'id': new_project_kpi.id,
            'project_id': new_project_kpi.project_id,
            'kpi_id': new_project_kpi.kpi_id,
            'target_value': new_project_kpi.target_value,
            'current_value': new_project_kpi.current_value,
            'project_name': project.name,
            'kpi_name': kpi.name,
            'kpi_unit': kpi.unit,
            'progress': round((new_project_kpi.current_value / new_project_kpi.target_value) * 100, 2) if new_project_kpi.target_value and new_project_kpi.target_value > 0 else 0
        }

        return api_response(
            data={'project_kpi': project_kpi_data},
            message="KPI di progetto creato con successo",
            status_code=201
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in create_project_kpi: {str(e)}")
        return handle_api_error(e)

@api_project_kpis.route('/<int:project_kpi_id>', methods=['PUT'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def update_project_kpi(project_kpi_id):
    """
    Aggiorna un KPI di progetto esistente.
    ---
    tags:
      - project-kpis
    parameters:
      - name: project_kpi_id
        in: path
        required: true
        description: ID del KPI di progetto da aggiornare
        schema:
          type: integer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              target_value:
                type: number
                description: Valore target del KPI
              current_value:
                type: number
                description: Valore attuale del KPI
    responses:
      200:
        description: KPI di progetto aggiornato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                data:
                  type: object
                  properties:
                    project_kpi:
                      $ref: '#/components/schemas/ProjectKPI'
                    message:
                      type: string
                      example: KPI di progetto aggiornato con successo
      400:
        description: Dati non validi
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: false
                message:
                  type: string
                  example: Dati non validi
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni il KPI di progetto dal database
        project_kpi = ProjectKPI.query.get_or_404(project_kpi_id)

        # Ottieni i dati dalla richiesta
        data = request.json
        if not data:
            return api_response(
                message="Nessun dato fornito per l'aggiornamento",
                status_code=400
            )

        # Aggiorna i campi del KPI di progetto
        if 'target_value' in data:
            project_kpi.target_value = data['target_value']
        if 'current_value' in data:
            project_kpi.current_value = data['current_value']

        # Commit delle modifiche
        db.session.commit()

        # Prepara i dati del KPI di progetto per la risposta
        project_kpi_data = {
            'id': project_kpi.id,
            'project_id': project_kpi.project_id,
            'kpi_id': project_kpi.kpi_id,
            'target_value': project_kpi.target_value,
            'current_value': project_kpi.current_value,
            'project_name': project_kpi.project.name if project_kpi.project else None,
            'kpi_name': project_kpi.kpi.name if project_kpi.kpi else None,
            'kpi_unit': project_kpi.kpi.unit if project_kpi.kpi else None,
            'progress': round((project_kpi.current_value / project_kpi.target_value) * 100, 2) if project_kpi.target_value and project_kpi.target_value > 0 else 0
        }

        return api_response(
            data={'project_kpi': project_kpi_data},
            message="KPI di progetto aggiornato con successo"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in update_project_kpi: {str(e)}")
        return handle_api_error(e)

@api_project_kpis.route('/<int:project_kpi_id>', methods=['DELETE'])
@login_required
@api_permission_required(PERMISSION_EDIT_PROJECT)
def delete_project_kpi(project_kpi_id):
    """
    Elimina un KPI di progetto.
    ---
    tags:
      - project-kpis
    parameters:
      - name: project_kpi_id
        in: path
        required: true
        description: ID del KPI di progetto da eliminare
        schema:
          type: integer
    responses:
      200:
        description: KPI di progetto eliminato con successo
        content:
          application/json:
            schema:
              type: object
              properties:
                success:
                  type: boolean
                  example: true
                message:
                  type: string
                  example: KPI di progetto eliminato con successo
      404:
        $ref: '#/components/responses/NotFound'
      401:
        $ref: '#/components/responses/Unauthorized'
      403:
        $ref: '#/components/responses/Forbidden'
    """
    try:
        # Ottieni il KPI di progetto dal database
        project_kpi = ProjectKPI.query.get_or_404(project_kpi_id)

        # Salva i dati del KPI di progetto per il messaggio di risposta
        project_name = project_kpi.project.name if project_kpi.project else "Progetto sconosciuto"
        kpi_name = project_kpi.kpi.name if project_kpi.kpi else "KPI sconosciuto"

        # Elimina il KPI di progetto
        db.session.delete(project_kpi)
        db.session.commit()

        return api_response(
            message=f"KPI di progetto eliminato con successo: '{kpi_name}' dal progetto '{project_name}'"
        )
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error in delete_project_kpi: {str(e)}")
        return handle_api_error(e)