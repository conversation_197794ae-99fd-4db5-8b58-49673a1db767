#!/usr/bin/env python3
"""
Test script per verificare che l'app non ricrei tabelle duplicate
"""

from app import create_app
from extensions import db
import sqlalchemy as sa

def test_table_creation():
    """Testa che l'app non ricrei tabelle duplicate"""
    print("🔍 Test creazione tabelle...")
    
    app = create_app()
    
    with app.app_context():
        print("✅ App avviata correttamente")
        
        # Ispeziona tabelle esistenti
        inspector = sa.inspect(db.engine)
        all_tables = inspector.get_table_names()
        
        # Filtra tabelle timesheet e time_off
        timesheet_tables = [t for t in all_tables if 'timesheet' in t]
        timeoff_tables = [t for t in all_tables if 'time_off' in t]
        
        print(f"\n📋 Tabelle timesheet trovate: {sorted(timesheet_tables)}")
        print(f"📋 Tabelle time_off trovate: {sorted(timeoff_tables)}")
        
        # Verifica che non ci siano duplicati
        expected_timesheet = ['timesheet']  # O timesheet_entries se rinominata
        expected_timeoff = ['time_off_requests']
        
        print(f"\n✅ Tabelle timesheet attese: {expected_timesheet}")
        print(f"✅ Tabelle time_off attese: {expected_timeoff}")
        
        # Controlla duplicati
        timesheet_duplicates = [t for t in timesheet_tables if t not in expected_timesheet and t != 'timesheet_entries']
        timeoff_duplicates = [t for t in timeoff_tables if t not in expected_timeoff]
        
        if timesheet_duplicates:
            print(f"❌ DUPLICATI timesheet trovati: {timesheet_duplicates}")
        else:
            print("✅ Nessun duplicato timesheet")
            
        if timeoff_duplicates:
            print(f"❌ DUPLICATI time_off trovati: {timeoff_duplicates}")
        else:
            print("✅ Nessun duplicato time_off")
        
        # Verifica modelli
        print(f"\n🔍 Verifica modelli...")
        from models import TimesheetEntry, TimeOffRequest, MonthlyTimesheet
        
        print(f"TimesheetEntry.__tablename__: {getattr(TimesheetEntry, '__tablename__', 'DEFAULT')}")
        print(f"TimeOffRequest.__tablename__: {getattr(TimeOffRequest, '__tablename__', 'DEFAULT')}")
        print(f"MonthlyTimesheet.__tablename__: {getattr(MonthlyTimesheet, '__tablename__', 'DEFAULT')}")
        
        return len(timesheet_duplicates) == 0 and len(timeoff_duplicates) == 0

if __name__ == "__main__":
    success = test_table_creation()
    if success:
        print("\n🎉 TEST PASSATO: Nessuna tabella duplicata creata!")
    else:
        print("\n❌ TEST FALLITO: Trovate tabelle duplicate!")
    
    exit(0 if success else 1)
