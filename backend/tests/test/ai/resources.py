"""
Test per AI Resource Allocation API
"""
import pytest
import json
from unittest.mock import patch, MagicMock
from app import create_app
from models import User, Project, ProjectResource
from extensions import db

@pytest.fixture
def app():
    """Create test app"""
    app = create_app()
    app.config['TESTING'] = True
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///:memory:'
    
    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

@pytest.fixture
def auth_user(app):
    """Create authenticated user"""
    with app.app_context():
        user = User(
            username='testuser',
            email='<EMAIL>',
            first_name='Test',
            last_name='User',
            role='admin'
        )
        user.set_password('password')
        db.session.add(user)
        db.session.commit()
        return user

@pytest.fixture
def test_project(app, auth_user):
    """Create test project"""
    with app.app_context():
        project = Project(
            name='Test Project',
            description='Test project for AI resource allocation',
            project_type='service',
            budget=10000.0
        )
        db.session.add(project)
        db.session.commit()
        return project

class TestAIResourceAllocation:
    """Test AI Resource Allocation functionality"""
    
    def test_analyze_allocation_endpoint(self, client, auth_user, test_project):
        """Test AI allocation analysis endpoint"""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(auth_user.id)
            sess['_fresh'] = True
        
        # Mock AI service response
        mock_ai_response = {
            "recommended_allocations": [
                {
                    "user_id": auth_user.id,
                    "user_name": "Test User",
                    "role": "Developer",
                    "allocation": 80
                }
            ],
            "optimization_insights": [
                "Team has good skill coverage for this project type",
                "Consider adding a senior developer for mentoring"
            ],
            "efficiency_score": 85,
            "potential_conflicts": [],
            "cost_analysis": {
                "estimated_cost": 8000,
                "budget_utilization": 80
            }
        }
        
        with patch('ai_services.analyze_resource_allocation', return_value=mock_ai_response):
            response = client.post(
                f'/api/ai-resources/analyze-allocation/{test_project.id}',
                json={
                    "include_suggestions": True,
                    "analysis_depth": "detailed"
                },
                headers={'Content-Type': 'application/json'}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'analysis' in data['data']
        assert data['data']['project_id'] == test_project.id
    
    def test_predict_conflicts_endpoint(self, client, auth_user):
        """Test AI conflict prediction endpoint"""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(auth_user.id)
            sess['_fresh'] = True
        
        # Mock AI service response
        mock_prediction = {
            "conflicts": [
                {
                    "type": "overallocation",
                    "user_id": auth_user.id,
                    "user_name": "Test User",
                    "current_allocation": 120,
                    "severity": "high"
                }
            ],
            "risk_level": "medium",
            "recommendations": [
                "Reduce allocation for Test User to 100%",
                "Consider hiring additional resources"
            ],
            "timeline_impact": "Potential 2-week delay if not addressed"
        }
        
        test_data = {
            "allocations": [
                {
                    "user_id": auth_user.id,
                    "allocation_percentage": 120,
                    "project_id": 1
                }
            ],
            "timeline": {
                "start_date": "2024-01-01",
                "end_date": "2024-03-01"
            }
        }
        
        with patch('ai_services.predict_resource_conflicts', return_value=mock_prediction):
            response = client.post(
                '/api/ai-resources/predict-conflicts',
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'prediction' in data['data']
        assert data['data']['prediction']['risk_level'] == 'medium'
    
    def test_optimize_team_endpoint(self, client, auth_user, test_project):
        """Test AI team optimization endpoint"""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(auth_user.id)
            sess['_fresh'] = True
        
        # Mock AI service response
        mock_optimization = {
            "optimal_team": [
                {
                    "user_id": auth_user.id,
                    "user_name": "Test User",
                    "role": "Lead Developer",
                    "allocation_percentage": 80,
                    "skill_match_score": 95
                }
            ],
            "skill_coverage": 85,
            "team_synergy_score": 78,
            "alternative_options": [],
            "training_needs": [
                "Advanced project management training for Test User"
            ]
        }
        
        with patch('ai_services.optimize_team_composition', return_value=mock_optimization):
            response = client.post(
                f'/api/ai-resources/optimize-team/{test_project.id}',
                json={
                    "candidate_users": [auth_user.id],
                    "optimization_criteria": ["skill_match", "experience"],
                    "team_size_limit": 5
                },
                headers={'Content-Type': 'application/json'}
            )
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        assert 'optimization' in data['data']
        assert data['data']['optimization']['skill_coverage'] == 85
    
    def test_unauthorized_access(self, client, test_project):
        """Test unauthorized access to AI endpoints"""
        response = client.post(f'/api/ai-resources/analyze-allocation/{test_project.id}')
        assert response.status_code == 401
        
        response = client.post('/api/ai-resources/predict-conflicts')
        assert response.status_code == 401
        
        response = client.post(f'/api/ai-resources/optimize-team/{test_project.id}')
        assert response.status_code == 401
    
    def test_invalid_project_id(self, client, auth_user):
        """Test with invalid project ID"""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(auth_user.id)
            sess['_fresh'] = True
        
        response = client.post('/api/ai-resources/analyze-allocation/99999')
        assert response.status_code == 404
    
    def test_missing_required_data(self, client, auth_user):
        """Test with missing required data"""
        with client.session_transaction() as sess:
            sess['_user_id'] = str(auth_user.id)
            sess['_fresh'] = True
        
        # Test predict conflicts without required data
        response = client.post(
            '/api/ai-resources/predict-conflicts',
            json={},
            headers={'Content-Type': 'application/json'}
        )
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert 'allocazioni e timeline obbligatori' in data['message']

class TestAIServiceIntegration:
    """Test AI service integration"""
    
    @patch('ai_services.openai_client')
    def test_ai_service_mock_response(self, mock_openai):
        """Test AI service with mocked OpenAI response"""
        from backend.services.ai import analyze_resource_allocation
        
        # Mock OpenAI response
        mock_response = MagicMock()
        mock_response.choices[0].message.content = json.dumps({
            "recommended_allocations": [],
            "optimization_insights": ["Test insight"],
            "efficiency_score": 75,
            "potential_conflicts": [],
            "cost_analysis": {}
        })
        mock_openai.chat.completions.create.return_value = mock_response
        
        # Test data
        project_data = {
            "name": "Test Project",
            "description": "Test description",
            "project_type": "service"
        }
        available_resources = [
            {
                "user_id": 1,
                "name": "Test User",
                "skills": ["Python", "JavaScript"]
            }
        ]
        
        result = analyze_resource_allocation(project_data, available_resources)
        
        assert "efficiency_score" in result
        assert result["efficiency_score"] == 75
        assert "optimization_insights" in result
        assert len(result["optimization_insights"]) > 0

if __name__ == '__main__':
    pytest.main([__file__])
