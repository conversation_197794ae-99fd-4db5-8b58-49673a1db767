"""
Utility per l'analisi e parsing di CV
"""
import os
import PyPDF2
from docx import Document
import logging

logger = logging.getLogger(__name__)

def extract_text_from_pdf(file_path):
    """
    Estrae testo da file PDF
    """
    try:
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text.strip()
    except Exception as e:
        logger.error(f"Errore lettura PDF {file_path}: {str(e)}")
        return ""

def extract_text_from_docx(file_path):
    """
    Estrae testo da file DOCX
    """
    try:
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text.strip()
    except Exception as e:
        logger.error(f"Errore lettura DOCX {file_path}: {str(e)}")
        return ""

def extract_text_from_txt(file_path):
    """
    Estrae testo da file TXT
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read().strip()
    except UnicodeDecodeError:
        # Prova con encoding diverso
        try:
            with open(file_path, 'r', encoding='latin-1') as file:
                return file.read().strip()
        except Exception as e:
            logger.error(f"Errore lettura TXT {file_path}: {str(e)}")
            return ""
    except Exception as e:
        logger.error(f"Errore lettura TXT {file_path}: {str(e)}")
        return ""

def extract_text_from_cv(file_path):
    """
    Estrae testo da CV basandosi sull'estensione del file
    """
    if not os.path.exists(file_path):
        logger.error(f"File non trovato: {file_path}")
        return ""
    
    file_extension = os.path.splitext(file_path)[1].lower()
    
    if file_extension == '.pdf':
        return extract_text_from_pdf(file_path)
    elif file_extension in ['.docx', '.doc']:
        return extract_text_from_docx(file_path)
    elif file_extension == '.txt':
        return extract_text_from_txt(file_path)
    else:
        logger.error(f"Formato file non supportato: {file_extension}")
        return ""

def is_valid_cv_file(filename):
    """
    Verifica se il file ha un'estensione valida per CV
    """
    if not filename:
        return False
    
    valid_extensions = ['.pdf', '.docx', '.doc', '.txt']
    file_extension = os.path.splitext(filename)[1].lower()
    return file_extension in valid_extensions

def get_file_size_mb(file_input):
    """
    Restituisce la dimensione del file in MB.
    Accetta sia un file path (stringa) che un oggetto file-like (es. FileStorage di Flask).
    """
    try:
        # Caso: oggetto file-like (FileStorage)
        if hasattr(file_input, 'seek') and hasattr(file_input, 'tell'):
            current_pos = file_input.tell()
            file_input.seek(0, 2)  # Vai alla fine
            size_bytes = file_input.tell()
            file_input.seek(current_pos)  # Torna alla posizione originale
            return size_bytes / (1024 * 1024)
        # Caso: path stringa
        size_bytes = os.path.getsize(file_input)
        return size_bytes / (1024 * 1024)
    except Exception as e:
        logger.error(f"Errore calcolo dimensione file: {str(e)}")
        return 0
