from flask import current_app, render_template, url_for

# In futuro, potremmo integrare Flask-Mail qui
# from flask_mail import Message
# from app import mail # Assumendo che mail sia inizializzato nell'app factory

def send_password_reset_email(user_email, user_name, token):
    """
    Prepara e "invia" (attualmente logga) l'email per il reset della password.
    """
    reset_url = url_for('auth.reset_password_with_token', token=token, _external=True)
    
    # Per ora, logghiamo. In futuro, useremo un vero servizio email.
    email_subject = "Richiesta di Reset Password"
    email_body_text = f"""Ciao {user_name},

Per resettare la tua password, visita il seguente link:
{reset_url}

Se non hai richiesto tu il reset della password, ignora semplicemente questa email.

Il link scadrà tra {current_app.config.get('PASSWORD_RESET_TOKEN_EXPIRATION_SECONDS', 3600) // 3600} ora/e.
"""
    # Potremmo anche renderizzare un template HTML per l'email:
    # email_body_html = render_template('auth/emails/reset_password_email.html', user_name=user_name, reset_url=reset_url)

    current_app.logger.info(f"---- EMAIL SIMULATA ----")
    current_app.logger.info(f"A: {user_email}")
    current_app.logger.info(f"Oggetto: {email_subject}")
    current_app.logger.info(f"Corpo:\n{email_body_text}")
    current_app.logger.info(f"---- FINE EMAIL SIMULATA ----")

    # Esempio futuro con Flask-Mail:
    # msg = Message(email_subject,
    #               sender=current_app.config['MAIL_DEFAULT_SENDER'],
    #               recipients=[user_email])
    # msg.body = email_body_text
    # msg.html = email_body_html # se si usa un template HTML
    # try:
    #     mail.send(msg)
    #     current_app.logger.info(f"Email di reset password inviata a {user_email}")
    # except Exception as e:
    #     current_app.logger.error(f"Errore durante l'invio dell'email di reset a {user_email}: {str(e)}")
    #     return False
    return True 