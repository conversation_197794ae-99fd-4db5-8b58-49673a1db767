from datetime import datetime
import json

def format_datetime(value, format='%d/%m/%Y %H:%M'):
    """Formatta un oggetto datetime in una stringa più leggibile."""
    if value is None:
        return ""
    return value.strftime(format)

def current_year():
    """Restituisce l'anno corrente."""
    return datetime.utcnow().year

def from_json(value):
    """Converte una stringa JSON in un oggetto Python."""
    if not value:
        return {}
    try:
        return json.loads(value)
    except (json.JSONDecodeError, TypeError):
        return {}

def register_filters(app):
    """Registra tutti i filtri personalizzati nell'app Flask."""
    app.jinja_env.filters['format_datetime'] = format_datetime
    app.jinja_env.filters['current_year'] = current_year
    app.jinja_env.filters['from_json'] = from_json 