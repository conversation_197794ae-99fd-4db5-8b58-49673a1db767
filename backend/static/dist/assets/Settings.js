import{r as d,A as _,c as S,j as e,s as j,v as a,x as g,t as b,R as y,H as C,C as u,o as T}from"./vendor.js";import{u as P,d as U}from"./app.js";const V={class:"max-w-4xl mx-auto space-y-6"},z={class:"bg-white shadow rounded-lg"},E={class:"p-6 space-y-6"},M=["disabled"],N={class:"bg-white shadow rounded-lg"},A={class:"p-6 space-y-6"},R={class:"space-y-3"},D={class:"flex items-center"},F={class:"flex items-center"},I={class:"flex items-center"},q={class:"flex items-center"},B={class:"bg-white shadow rounded-lg"},O={class:"p-6 space-y-6"},X={class:"space-y-4"},G={class:"flex items-center justify-between"},J={class:"flex items-center justify-between"},L={class:"flex items-center justify-between"},H={class:"flex items-center justify-between"},Q={class:"flex justify-end space-x-3"},K=["disabled"],ee={__name:"Settings",setup(W){const f=P();U();const m=d(!1),p=d(!1),r=d({current_password:"",new_password:"",confirm_password:""}),o=d({theme:"light",language:"it",sidebar_collapsed:!1}),i=d({email:!0,projects:!0,tasks:!0,system:!1}),w=async()=>{var l,s,t,v;try{const c=await fetch("/api/auth/settings",{headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken}});if(c.ok){const n=(await c.json()).data.settings;o.value={theme:n.theme||"light",language:n.language||"it",sidebar_collapsed:n.sidebar_collapsed||!1},i.value={email:((l=n.notifications)==null?void 0:l.email)!==!1,projects:((s=n.notifications)==null?void 0:s.projects)!==!1,tasks:((t=n.notifications)==null?void 0:t.tasks)!==!1,system:((v=n.notifications)==null?void 0:v.system)||!1}}}catch(c){console.error("Error loading settings:",c)}},x=async()=>{m.value=!0;try{if(!(await fetch("/api/auth/settings",{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken},body:JSON.stringify({preferences:o.value,notifications:i.value})})).ok)throw new Error("Errore nel salvataggio delle impostazioni");alert("Impostazioni salvate con successo!")}catch(l){console.error("Error saving settings:",l),alert("Errore nel salvataggio delle impostazioni")}finally{m.value=!1}},h=async()=>{if(r.value.new_password!==r.value.confirm_password){alert("Le password non coincidono");return}p.value=!0;try{if(!(await fetch("/api/auth/change-password",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":f.csrfToken},body:JSON.stringify({current_password:r.value.current_password,new_password:r.value.new_password})})).ok)throw new Error("Errore nel cambio password");alert("Password cambiata con successo!"),r.value={current_password:"",new_password:"",confirm_password:""}}catch(l){console.error("Error changing password:",l),alert("Errore nel cambio password")}finally{p.value=!1}},k=()=>{o.value={theme:"light",language:"it",sidebar_collapsed:!1},i.value={email:!0,projects:!0,tasks:!0,system:!1}};return _(()=>{w()}),(l,s)=>(T(),S("div",V,[e("div",z,[s[16]||(s[16]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h2",{class:"text-lg font-semibold text-gray-900"},"Impostazioni Account"),e("p",{class:"mt-1 text-sm text-gray-600"},"Gestisci le impostazioni del tuo account")],-1)),e("div",E,[e("div",null,[s[15]||(s[15]=e("h3",{class:"text-md font-medium text-gray-900 mb-4"},"Cambia Password",-1)),e("form",{onSubmit:j(h,["prevent"]),class:"space-y-4"},[e("div",null,[s[12]||(s[12]=e("label",{for:"current_password",class:"block text-sm font-medium text-gray-700"},"Password Attuale",-1)),a(e("input",{id:"current_password","onUpdate:modelValue":s[0]||(s[0]=t=>r.value.current_password=t),type:"password",required:"",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[g,r.value.current_password]])]),e("div",null,[s[13]||(s[13]=e("label",{for:"new_password",class:"block text-sm font-medium text-gray-700"},"Nuova Password",-1)),a(e("input",{id:"new_password","onUpdate:modelValue":s[1]||(s[1]=t=>r.value.new_password=t),type:"password",required:"",minlength:"8",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[g,r.value.new_password]])]),e("div",null,[s[14]||(s[14]=e("label",{for:"confirm_password",class:"block text-sm font-medium text-gray-700"},"Conferma Password",-1)),a(e("input",{id:"confirm_password","onUpdate:modelValue":s[2]||(s[2]=t=>r.value.confirm_password=t),type:"password",required:"",class:"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[g,r.value.confirm_password]])]),e("button",{type:"submit",disabled:p.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},b(p.value?"Aggiornamento...":"Cambia Password"),9,M)],32)])])]),e("div",N,[s[25]||(s[25]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h2",{class:"text-lg font-semibold text-gray-900"},"Preferenze Interfaccia"),e("p",{class:"mt-1 text-sm text-gray-600"},"Personalizza l'aspetto dell'applicazione")],-1)),e("div",A,[e("div",null,[s[20]||(s[20]=e("h3",{class:"text-md font-medium text-gray-900 mb-4"},"Tema",-1)),e("div",R,[e("div",D,[a(e("input",{id:"theme_light","onUpdate:modelValue":s[3]||(s[3]=t=>o.value.theme=t),type:"radio",value:"light",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"},null,512),[[y,o.value.theme]]),s[17]||(s[17]=e("label",{for:"theme_light",class:"ml-2 block text-sm text-gray-900"}," 🌞 Modalità Chiara ",-1))]),e("div",F,[a(e("input",{id:"theme_dark","onUpdate:modelValue":s[4]||(s[4]=t=>o.value.theme=t),type:"radio",value:"dark",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"},null,512),[[y,o.value.theme]]),s[18]||(s[18]=e("label",{for:"theme_dark",class:"ml-2 block text-sm text-gray-900"}," 🌙 Modalità Scura ",-1))]),e("div",I,[a(e("input",{id:"theme_auto","onUpdate:modelValue":s[5]||(s[5]=t=>o.value.theme=t),type:"radio",value:"auto",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"},null,512),[[y,o.value.theme]]),s[19]||(s[19]=e("label",{for:"theme_auto",class:"ml-2 block text-sm text-gray-900"}," 🔄 Automatico (segue il sistema) ",-1))])])]),e("div",null,[s[22]||(s[22]=e("h3",{class:"text-md font-medium text-gray-900 mb-4"},"Lingua",-1)),a(e("select",{"onUpdate:modelValue":s[6]||(s[6]=t=>o.value.language=t),class:"block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"},s[21]||(s[21]=[e("option",{value:"it"},"🇮🇹 Italiano",-1),e("option",{value:"en"},"🇺🇸 English",-1),e("option",{value:"fr"},"🇫🇷 Français",-1),e("option",{value:"de"},"🇩🇪 Deutsch",-1)]),512),[[C,o.value.language]])]),e("div",null,[s[24]||(s[24]=e("h3",{class:"text-md font-medium text-gray-900 mb-4"},"Sidebar",-1)),e("div",q,[a(e("input",{id:"sidebar_collapsed","onUpdate:modelValue":s[7]||(s[7]=t=>o.value.sidebar_collapsed=t),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[u,o.value.sidebar_collapsed]]),s[23]||(s[23]=e("label",{for:"sidebar_collapsed",class:"ml-2 block text-sm text-gray-900"}," Mantieni sidebar collassata di default ",-1))])])])]),e("div",B,[s[30]||(s[30]=e("div",{class:"px-6 py-4 border-b border-gray-200"},[e("h2",{class:"text-lg font-semibold text-gray-900"},"Notifiche"),e("p",{class:"mt-1 text-sm text-gray-600"},"Gestisci le tue preferenze di notifica")],-1)),e("div",O,[e("div",X,[e("div",G,[s[26]||(s[26]=e("div",null,[e("h4",{class:"text-sm font-medium text-gray-900"},"Notifiche Email"),e("p",{class:"text-sm text-gray-500"},"Ricevi notifiche via email")],-1)),a(e("input",{"onUpdate:modelValue":s[8]||(s[8]=t=>i.value.email=t),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[u,i.value.email]])]),e("div",J,[s[27]||(s[27]=e("div",null,[e("h4",{class:"text-sm font-medium text-gray-900"},"Notifiche Progetti"),e("p",{class:"text-sm text-gray-500"},"Aggiornamenti sui progetti a cui partecipi")],-1)),a(e("input",{"onUpdate:modelValue":s[9]||(s[9]=t=>i.value.projects=t),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[u,i.value.projects]])]),e("div",L,[s[28]||(s[28]=e("div",null,[e("h4",{class:"text-sm font-medium text-gray-900"},"Notifiche Task"),e("p",{class:"text-sm text-gray-500"},"Quando ti vengono assegnati nuovi task")],-1)),a(e("input",{"onUpdate:modelValue":s[10]||(s[10]=t=>i.value.tasks=t),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[u,i.value.tasks]])]),e("div",H,[s[29]||(s[29]=e("div",null,[e("h4",{class:"text-sm font-medium text-gray-900"},"Notifiche Sistema"),e("p",{class:"text-sm text-gray-500"},"Aggiornamenti e manutenzioni del sistema")],-1)),a(e("input",{"onUpdate:modelValue":s[11]||(s[11]=t=>i.value.system=t),type:"checkbox",class:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"},null,512),[[u,i.value.system]])])])])]),e("div",Q,[e("button",{onClick:k,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"}," Ripristina Default "),e("button",{onClick:x,disabled:m.value,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},b(m.value?"Salvataggio...":"Salva Impostazioni"),9,K)])]))}};export{ee as default};
