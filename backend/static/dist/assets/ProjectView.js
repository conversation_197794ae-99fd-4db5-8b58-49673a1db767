import{c as s,o as t,j as e,t as o,n as F,g as I,m as J,a as oe,i as xe,b as fe,F as H,k as O,h as de,M as ve,f as B,z as ee,r as P,w as Z,A as se,v as E,G,H as ae,x as X,s as te,N as be,p as le,u as he,O as ke,l as we}from"./vendor.js";import{_ as ue,u as ne,a as _e,b as ye}from"./app.js";const $e={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},je={key:0,class:"animate-pulse"},Ce={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Me={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Se={class:"text-2xl font-bold text-gray-900"},Pe={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},Ae={key:0},ze={key:1},De={key:2},Ie={key:3},Ve={class:"mt-4 sm:mt-0 flex space-x-3"},Ee={key:2,class:"text-center py-8"},Be={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(b){const A=p=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[p]||"bg-gray-100 text-gray-800",_=p=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[p]||p,T=p=>p?new Date(p).toLocaleDateString("it-IT"):"",$=p=>p?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(p):"";return(p,f)=>{const y=fe("router-link");return t(),s("div",$e,[b.loading?(t(),s("div",je,f[1]||(f[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):b.project?(t(),s("div",Ce,[e("div",Me,[e("div",Te,[e("h1",Se,o(b.project.name),1),e("span",{class:F(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",A(b.project.status)])},o(_(b.project.status)),3)]),e("div",Pe,[b.project.client?(t(),s("span",Ae,[f[2]||(f[2]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),J(" Cliente: "+o(b.project.client.name),1)])):I("",!0),b.project.start_date?(t(),s("span",ze,[f[3]||(f[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),J(" Inizio: "+o(T(b.project.start_date)),1)])):I("",!0),b.project.end_date?(t(),s("span",De,[f[4]||(f[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),J(" Fine: "+o(T(b.project.end_date)),1)])):I("",!0),b.project.budget?(t(),s("span",Ie,[f[5]||(f[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),J(" Budget: "+o($(b.project.budget)),1)])):I("",!0)])]),e("div",Ve,[oe(y,{to:`/app/projects/${b.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:xe(()=>f[6]||(f[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),J(" Modifica ")])),_:1,__:[6]},8,["to"]),e("button",{onClick:f[0]||(f[0]=x=>p.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},f[7]||(f[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),J(" Elimina ")]))])])):(t(),s("div",Ee,f[8]||(f[8]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Ue=ue(Be,[["__scopeId","data-v-6f1b5cc9"]]),Re={class:"tab-navigation"},He={class:"border-b border-gray-200"},Oe={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Fe=["onClick","aria-current"],Le={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},Ke={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:b=>b.every(A=>typeof A=="object"&&A.id&&A.label)}},emits:["update:modelValue"],setup(b,{emit:A}){const _=b,T=A,$=y=>_.modelValue===y,p=y=>{T("update:modelValue",y)},f=y=>{const x={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return x[y]||x["chart-bar"]};return(y,x)=>(t(),s("div",Re,[e("div",He,[e("nav",Oe,[(t(!0),s(H,null,O(b.tabs,n=>(t(),s("button",{key:n.id,onClick:j=>p(n.id),class:F(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",$(n.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":$(n.id)?"page":void 0},[n.icon?(t(),de(ve(f(n.icon)),{key:0,class:"w-4 h-4"})):I("",!0),e("span",null,o(n.label),1),n.count!==void 0?(t(),s("span",Le,o(n.count),1)):I("",!0)],10,Fe))),128))])])]))}},qe=ue(Ke,[["__scopeId","data-v-c205976e"]]),Ne={class:"project-overview"},Xe={key:0,class:"animate-pulse space-y-4"},Je={key:1,class:"space-y-6"},Ge={class:"bg-white shadow rounded-lg p-6"},We={key:0,class:"text-gray-600"},Ye={key:1,class:"text-gray-400 italic"},Qe={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Ze={class:"bg-white shadow rounded-lg p-6"},et={class:"flex items-center"},tt={class:"ml-5 w-0 flex-1"},st={class:"text-lg font-medium text-gray-900"},rt={class:"bg-white shadow rounded-lg p-6"},ot={class:"flex items-center"},at={class:"ml-5 w-0 flex-1"},nt={class:"text-lg font-medium text-gray-900"},it={class:"bg-white shadow rounded-lg p-6"},lt={class:"flex items-center"},dt={class:"ml-5 w-0 flex-1"},ut={class:"text-lg font-medium text-gray-900"},ct={class:"bg-white shadow rounded-lg p-6"},gt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white shadow rounded-lg p-6"},yt={class:"w-full bg-gray-200 rounded-full h-2.5"},xt={class:"text-sm text-gray-500 mt-2"},ft={class:"bg-white shadow rounded-lg p-6"},bt={class:"space-y-4"},ht={class:"flex justify-between items-center"},kt={class:"text-sm font-medium"},wt={class:"flex justify-between items-center"},_t={class:"text-sm font-medium"},$t={class:"w-full bg-gray-200 rounded-full h-3"},jt={class:"flex justify-between items-center text-sm"},Ct={class:"bg-white shadow rounded-lg p-6"},Mt={class:"space-y-3"},Tt={class:"flex-shrink-0"},St=["src","alt"],Pt={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},At={class:"text-xs font-medium text-gray-600"},zt={class:"flex-1"},Dt={class:"text-sm font-medium text-gray-900"},It={class:"text-xs text-gray-500"},Vt={class:"text-right"},Et={class:"text-xs text-gray-500"},Bt={key:0,class:"text-center py-4"},Ut={class:"bg-white shadow rounded-lg p-6"},Rt={class:"space-y-3"},Ht={class:"flex-shrink-0"},Ot={class:"flex-1"},Ft={class:"text-sm text-gray-900"},Lt={class:"flex items-center space-x-2 mt-1"},Kt={class:"text-xs text-gray-500"},qt={class:"text-xs text-gray-500"},Nt={key:0,class:"text-center py-4"},Xt={key:2,class:"text-center py-8"},Jt={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(b){const A=b,_=B(()=>{if(!A.project||!A.project.task_count)return 0;const v=A.project.completed_tasks||0,a=A.project.task_count||1;return Math.round(v/a*100)}),T=B(()=>{var v;return((v=A.project)==null?void 0:v.team_members)||[]}),$=B(()=>{var M,k,C;if((M=A.project)!=null&&M.expenses)return A.project.expenses;const v=((k=A.project)==null?void 0:k.total_hours)||0,a=(C=A.project)!=null&&C.client_daily_rate?A.project.client_daily_rate/8:50;return v*a}),p=B(()=>{var a;return(((a=A.project)==null?void 0:a.budget)||0)-$.value}),f=B(()=>{var a;const v=((a=A.project)==null?void 0:a.budget)||1;return Math.min(Math.round($.value/v*100),100)}),y=B(()=>{const v=f.value;return v>=90?"bg-red-600":v>=75?"bg-yellow-600":"bg-green-600"}),x=B(()=>{var a;const v=p.value;return v<0?"text-red-600":v<(((a=A.project)==null?void 0:a.budget)||0)*.1?"text-yellow-600":"text-green-600"}),n=B(()=>{var v;return(v=A.project)!=null&&v.tasks?[...A.project.tasks].sort((a,M)=>new Date(M.updated_at)-new Date(a.updated_at)).slice(0,5).map(a=>{var M;return{id:a.id,description:`Task "${a.name}" ${j(a.status)}`,created_at:a.updated_at,user_name:((M=a.assignee)==null?void 0:M.full_name)||"Non assegnato",type:U(a.status)}}):[]}),j=v=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[v]||v,U=v=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[v]||"task_updated",V=v=>v?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(v):"Non specificato",z=v=>v?new Date(v).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",h=v=>v?v.split(" ").map(a=>a.charAt(0).toUpperCase()).slice(0,2).join(""):"??",L=v=>{const a={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return a[v]||a.default};return(v,a)=>(t(),s("div",Ne,[b.loading?(t(),s("div",Xe,a[0]||(a[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):b.project?(t(),s("div",Je,[e("div",Ge,[a[1]||(a[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),b.project.description?(t(),s("p",We,o(b.project.description),1)):(t(),s("p",Ye,"Nessuna descrizione disponibile"))]),e("div",Qe,[e("div",Ze,[e("div",et,[a[3]||(a[3]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",tt,[e("dl",null,[a[2]||(a[2]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",st,o(b.project.task_count||0),1)])])])]),e("div",rt,[e("div",ot,[a[5]||(a[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",at,[e("dl",null,[a[4]||(a[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",nt,o(b.project.completed_tasks||0),1)])])])]),e("div",it,[e("div",lt,[a[7]||(a[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",dt,[e("dl",null,[a[6]||(a[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",ut,o(b.project.team_count||0),1)])])])]),e("div",ct,[e("div",gt,[a[9]||(a[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",mt,[e("dl",null,[a[8]||(a[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",pt,o(V(b.project.budget)),1)])])])])]),e("div",vt,[a[10]||(a[10]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",yt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:ee({width:`${_.value}%`})},null,4)]),e("p",xt,o(_.value)+"% completato",1)]),e("div",ft,[a[15]||(a[15]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",bt,[e("div",ht,[a[11]||(a[11]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",kt,o(V(b.project.budget)),1)]),a[14]||(a[14]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",wt,[a[12]||(a[12]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",_t,o(V($.value)),1)]),e("div",$t,[e("div",{class:F(["h-3 rounded-full transition-all duration-300",y.value]),style:ee({width:f.value+"%"})},null,6)]),e("div",jt,[a[13]||(a[13]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:F(["font-medium",x.value])},o(V(p.value)),3)])])]),e("div",Ct,[a[17]||(a[17]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Mt,[(t(!0),s(H,null,O(T.value,M=>(t(),s("div",{key:M.id,class:"flex items-center space-x-3"},[e("div",Tt,[M.profile_image?(t(),s("img",{key:0,src:M.profile_image,alt:M.full_name,class:"w-8 h-8 rounded-full"},null,8,St)):(t(),s("div",Pt,[e("span",At,o(h(M.full_name)),1)]))]),e("div",zt,[e("p",Dt,o(M.full_name),1),e("p",It,o(M.role||"Team Member"),1)]),e("div",Vt,[e("p",Et,o(M.hours_worked||0)+"h",1)])]))),128)),T.value.length===0?(t(),s("div",Bt,a[16]||(a[16]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):I("",!0)])]),e("div",Ut,[a[20]||(a[20]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Rt,[(t(!0),s(H,null,O(n.value,M=>(t(),s("div",{key:M.id,class:"flex items-start space-x-3"},[e("div",Ht,[e("div",{class:F(["w-2 h-2 rounded-full mt-2",L(M.type)])},null,2)]),e("div",Ot,[e("p",Ft,o(M.description),1),e("div",Lt,[e("p",Kt,o(z(M.created_at)),1),a[18]||(a[18]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",qt,o(M.user_name),1)])])]))),128)),n.value.length===0?(t(),s("div",Nt,a[19]||(a[19]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):I("",!0)])])])):(t(),s("div",Xt,a[21]||(a[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))]))}},me=ue(Jt,[["__scopeId","data-v-16274846"]]),Gt={class:"space-y-6"},Wt={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Yt={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qt={class:"flex items-center justify-between"},Zt={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},es=["value"],ts={class:"mt-4 flex items-center justify-between"},ss={class:"flex items-center space-x-4"},rs={class:"text-sm text-gray-500 dark:text-gray-400"},os={key:0,class:"flex justify-center py-8"},as={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},ns={class:"text-red-600"},is={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ls={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ds={class:"col-span-4"},us={class:"text-sm font-medium text-gray-900 dark:text-white"},cs={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},gs={class:"col-span-2"},ms={key:0,class:"flex items-center"},ps={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},vs={class:"ml-2"},ys={class:"text-sm font-medium text-gray-900 dark:text-white"},xs={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},fs={class:"col-span-1"},bs={class:"col-span-1"},hs={class:"col-span-2"},ks={key:0,class:"text-sm text-gray-900 dark:text-white"},ws={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},_s={class:"col-span-1"},$s={class:"text-sm text-gray-900 dark:text-white"},js={key:0,class:"text-gray-500"},Cs={class:"col-span-1"},Ms={class:"flex items-center space-x-2"},Ts=["onClick"],Ss={key:0,class:"px-6 py-12 text-center"},Ps={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},As={class:"flex items-center justify-between mb-4"},zs={class:"font-medium text-gray-900 dark:text-white"},Ds={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},Is={class:"space-y-3"},Vs=["onClick"],Es={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},Bs={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Us={class:"flex items-center justify-between"},Rs={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Hs={class:"mt-3"},Os={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Fs={class:"grid grid-cols-1 gap-4"},Ls={class:"grid grid-cols-2 gap-4"},Ks=["value"],qs={class:"grid grid-cols-2 gap-4"},Ns={class:"flex justify-end space-x-3 mt-6"},Xs=["disabled"],Js={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(b,{expose:A}){const _=b,T=ne(),{hasPermission:$}=_e(),p=P([]),f=P(!1),y=P(""),x=P("list"),n=P(!1),j=P({status:"",priority:"",assignee_id:"",search:""}),U=P(!1),V=P(!1),z=P(null),h=P({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),L=B(()=>$.value("manage_project_tasks")),v=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],a=async()=>{var c,d;if((c=_.project)!=null&&c.id){f.value=!0,y.value="";try{const W=new URLSearchParams({project_id:_.project.id,...j.value}),re=await fetch(`/api/tasks?${W}`,{headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken}});if(!re.ok)throw new Error("Errore nel caricamento dei task");const w=await re.json();p.value=((d=w.data)==null?void 0:d.tasks)||w.tasks||[]}catch(W){y.value=W.message}finally{f.value=!1}}},M=async()=>{n.value=!0;try{const c=V.value?`/api/tasks/${z.value.id}`:"/api/tasks",d=V.value?"PUT":"POST",W={...h.value,project_id:_.project.id};if(!(await fetch(c,{method:d,headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken},body:JSON.stringify(W)})).ok)throw new Error("Errore nel salvataggio del task");await a(),C()}catch(c){y.value=c.message}finally{n.value=!1}},k=c=>{z.value=c,h.value={name:c.name,description:c.description||"",status:c.status,priority:c.priority,assignee_id:c.assignee_id||"",due_date:c.due_date?c.due_date.split("T")[0]:"",estimated_hours:c.estimated_hours},V.value=!0},C=()=>{U.value=!1,V.value=!1,z.value=null,h.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},S=c=>p.value.filter(d=>d.status===c),N=c=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[c]||"bg-gray-100 text-gray-800",K=c=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[c]||c,D=c=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[c]||"bg-gray-100 text-gray-800",i=c=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[c]||c,g=(c,d)=>`${(c==null?void 0:c.charAt(0))||""}${(d==null?void 0:d.charAt(0))||""}`.toUpperCase(),l=c=>new Date(c).toLocaleDateString("it-IT");let r;const R=()=>{clearTimeout(r),r=setTimeout(()=>{a()},300)};return Z(()=>{var c;return(c=_.project)==null?void 0:c.id},c=>{c&&a()}),se(()=>{var c;(c=_.project)!=null&&c.id&&a()}),A({refresh:a}),(c,d)=>{var W,re;return t(),s("div",Gt,[e("div",Wt,[e("div",Yt,[e("div",Qt,[d[16]||(d[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),L.value?(t(),s("button",{key:0,onClick:d[0]||(d[0]=w=>U.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},d[15]||(d[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Nuovo Task ")]))):I("",!0)]),e("div",Zt,[e("div",null,[d[18]||(d[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":d[1]||(d[1]=w=>j.value.status=w),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},d[17]||(d[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[G,j.value.status]])]),e("div",null,[d[20]||(d[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":d[2]||(d[2]=w=>j.value.priority=w),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},d[19]||(d[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[G,j.value.priority]])]),e("div",null,[d[22]||(d[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":d[3]||(d[3]=w=>j.value.assignee_id=w),onChange:a,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[d[21]||(d[21]=e("option",{value:""},"Tutti",-1)),(t(!0),s(H,null,O(((W=b.project)==null?void 0:W.team_members)||[],w=>(t(),s("option",{key:w.id,value:w.id},o(w.first_name)+" "+o(w.last_name),9,es))),128))],544),[[G,j.value.assignee_id]])]),e("div",null,[d[23]||(d[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),E(e("input",{"onUpdate:modelValue":d[4]||(d[4]=w=>j.value.search=w),onInput:R,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[X,j.value.search]])])]),e("div",ts,[e("div",ss,[d[24]||(d[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:d[5]||(d[5]=w=>x.value="list"),class:F([x.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:d[6]||(d[6]=w=>x.value="kanban"),class:F([x.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",rs,o(p.value.length)+" task trovati ",1)])])]),f.value?(t(),s("div",os,d[25]||(d[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):I("",!0),y.value?(t(),s("div",as,[e("p",ns,o(y.value),1)])):I("",!0),!f.value&&x.value==="list"?(t(),s("div",is,[e("div",ls,[d[27]||(d[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(t(!0),s(H,null,O(p.value,w=>(t(),s("div",{key:w.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ds,[e("div",us,o(w.name),1),w.description?(t(),s("div",cs,o(w.description),1)):I("",!0)]),e("div",gs,[w.assignee?(t(),s("div",ms,[e("div",ps,o(g(w.assignee.first_name,w.assignee.last_name)),1),e("div",vs,[e("div",ys,o(w.assignee.first_name)+" "+o(w.assignee.last_name),1)])])):(t(),s("span",xs,"Non assegnato"))]),e("div",fs,[e("span",{class:F([N(w.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(K(w.status)),3)]),e("div",bs,[e("span",{class:F([D(w.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(i(w.priority)),3)]),e("div",hs,[w.due_date?(t(),s("div",ks,o(l(w.due_date)),1)):(t(),s("span",ws,"-"))]),e("div",_s,[e("div",$s,[J(o(w.actual_hours||0)+"h ",1),w.estimated_hours?(t(),s("span",js,"/ "+o(w.estimated_hours)+"h",1)):I("",!0)])]),e("div",Cs,[e("div",Ms,[e("button",{onClick:u=>k(w),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Ts)])])]))),128)),p.value.length===0?(t(),s("div",Ss,d[26]||(d[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):I("",!0)])])):I("",!0),!f.value&&x.value==="kanban"?(t(),s("div",Ps,[(t(),s(H,null,O(v,w=>e("div",{key:w.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",As,[e("h4",zs,o(w.label),1),e("span",Ds,o(S(w.value).length),1)]),e("div",Is,[(t(!0),s(H,null,O(S(w.value),u=>(t(),s("div",{key:u.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:m=>k(u)},[e("div",Es,o(u.name),1),u.description?(t(),s("div",Bs,o(u.description),1)):I("",!0),e("div",Us,[e("span",{class:F([D(u.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},o(i(u.priority)),3),u.assignee?(t(),s("div",Rs,o(g(u.assignee.first_name,u.assignee.last_name)),1)):I("",!0)])],8,Vs))),128))])])),64))])):I("",!0),U.value||V.value?(t(),s("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:C},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:d[14]||(d[14]=te(()=>{},["stop"]))},[e("div",Hs,[e("h3",Os,o(V.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:te(M,["prevent"])},[e("div",Fs,[e("div",null,[d[28]||(d[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),E(e("input",{"onUpdate:modelValue":d[7]||(d[7]=w=>h.value.name=w),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.name]])]),e("div",null,[d[29]||(d[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":d[8]||(d[8]=w=>h.value.description=w),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.description]])]),e("div",Ls,[e("div",null,[d[31]||(d[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":d[9]||(d[9]=w=>h.value.status=w),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},d[30]||(d[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[G,h.value.status]])]),e("div",null,[d[33]||(d[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":d[10]||(d[10]=w=>h.value.priority=w),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},d[32]||(d[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[G,h.value.priority]])])]),e("div",null,[d[35]||(d[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":d[11]||(d[11]=w=>h.value.assignee_id=w),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[d[34]||(d[34]=e("option",{value:""},"Non assegnato",-1)),(t(!0),s(H,null,O(((re=b.project)==null?void 0:re.team_members)||[],w=>(t(),s("option",{key:w.id,value:w.id},o(w.first_name)+" "+o(w.last_name),9,Ks))),128))],512),[[G,h.value.assignee_id]])]),e("div",qs,[e("div",null,[d[36]||(d[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),E(e("input",{"onUpdate:modelValue":d[12]||(d[12]=w=>h.value.due_date=w),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.due_date]])]),e("div",null,[d[37]||(d[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),E(e("input",{"onUpdate:modelValue":d[13]||(d[13]=w=>h.value.estimated_hours=w),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.estimated_hours]])])])]),e("div",Ns,[e("button",{type:"button",onClick:C,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:n.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(n.value?"Salvataggio...":V.value?"Aggiorna":"Crea"),9,Xs)])],32)])])])):I("",!0)])}}},Gs={class:"space-y-6"},Ws={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Ys={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Qs={class:"flex items-center justify-between"},Zs={class:"p-6 border-b border-gray-200 dark:border-gray-700"},er={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},tr={class:"text-center"},sr={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},rr={class:"text-center"},or={class:"text-2xl font-bold text-green-600"},ar={class:"text-center"},nr={class:"text-2xl font-bold text-blue-600"},ir={class:"text-center"},lr={class:"text-2xl font-bold text-purple-600"},dr={class:"p-6"},ur={class:"space-y-4"},cr={class:"flex items-center justify-between"},gr={class:"flex items-center space-x-4"},mr={class:"flex-shrink-0"},pr=["src","alt"],vr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},yr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},xr={class:"flex-1"},fr={class:"flex items-center space-x-2"},br={class:"text-lg font-medium text-gray-900 dark:text-white"},hr={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},kr={class:"text-sm text-gray-600 dark:text-gray-400"},wr={class:"text-xs text-gray-500 dark:text-gray-500"},_r={class:"flex items-center space-x-4"},$r={class:"text-right"},jr={class:"text-sm font-medium text-gray-900 dark:text-white"},Cr={class:"text-right"},Mr={class:"text-sm font-medium text-gray-900 dark:text-white"},Tr={class:"text-right"},Sr={class:"text-sm font-medium text-gray-900 dark:text-white"},Pr={class:"flex items-center space-x-2"},Ar=["onClick"],zr=["onClick"],Dr={class:"mt-4"},Ir={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},Vr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Er={key:0,class:"text-center py-8"},Br={class:"mt-6"},Ur={class:"mt-3"},Rr={class:"space-y-4"},Hr=["value"],Or={class:"flex justify-end space-x-3 mt-6"},Fr=["disabled"],Lr={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(b,{expose:A,emit:_}){const T=b,$=ne(),p=P(!1),f=P([]),y=P(!1),x=P({user_id:"",role:""}),n=B(()=>{var i;return((i=T.project)==null?void 0:i.team_members)||[]}),j=B(()=>n.value.reduce((i,g)=>i+(g.hours_worked||0),0)),U=B(()=>n.value.length===0?0:Math.round(j.value/n.value.length)),V=B(()=>n.value.filter(i=>(i.hours_worked||0)>0).length),z=i=>i?i.split(" ").map(g=>g.charAt(0).toUpperCase()).slice(0,2).join(""):"??",h=i=>{var l;return(((l=T.project)==null?void 0:l.tasks)||[]).filter(r=>r.assignee_id===i).length},L=i=>{var l;return(((l=T.project)==null?void 0:l.tasks)||[]).filter(r=>r.assignee_id===i&&r.status==="done").length},v=i=>{const g=h(i),l=L(i);return g===0?0:Math.round(l/g*100)},a=i=>{const g=v(i);return g>=80?"bg-green-600":g>=60?"bg-yellow-600":g>=40?"bg-orange-600":"bg-red-600"},M=i=>!i||i===0?"0.00":parseFloat(i).toFixed(2),k=async()=>{var i;try{const g=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(g.ok){const l=await g.json(),r=n.value.map(R=>R.id);f.value=(i=l.data)!=null&&i.users?l.data.users.filter(R=>!r.includes(R.id)):[]}}catch(g){console.error("Errore nel caricamento utenti:",g),f.value=[]}},C=async()=>{y.value=!0;try{const i=await fetch(`/api/projects/${T.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify(x.value)});if(i.ok)D("refresh"),K();else{const g=await i.json();alert(g.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{y.value=!1}},S=i=>{console.log("Edit member:",i)},N=async i=>{if(confirm(`Rimuovere ${i.full_name} dal progetto?`))try{const g=await fetch(`/api/projects/${T.project.id}/team/${i.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(g.ok)D("refresh");else{const l=await g.json();alert(l.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},K=()=>{p.value=!1,x.value={user_id:"",role:""}},D=_;return se(()=>{k()}),Z(()=>p.value,i=>{i&&k()}),Z(()=>{var i;return(i=T.project)==null?void 0:i.team_members},()=>{p.value&&k()}),A({refresh:k}),(i,g)=>(t(),s("div",Gs,[e("div",Ws,[e("div",Ys,[e("div",Qs,[g[6]||(g[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:g[0]||(g[0]=l=>p.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},g[5]||(g[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Aggiungi Membro ")]))])]),e("div",Zs,[e("div",er,[e("div",tr,[e("div",sr,o(n.value.length),1),g[7]||(g[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",rr,[e("div",or,o(j.value)+"h",1),g[8]||(g[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",ar,[e("div",nr,o(U.value)+"h",1),g[9]||(g[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",ir,[e("div",lr,o(V.value),1),g[10]||(g[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",dr,[e("div",ur,[(t(!0),s(H,null,O(n.value,l=>{var r,R;return t(),s("div",{key:l.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",cr,[e("div",gr,[e("div",mr,[l.profile_image?(t(),s("img",{key:0,src:l.profile_image,alt:l.full_name,class:"w-12 h-12 rounded-full"},null,8,pr)):(t(),s("div",vr,[e("span",yr,o(z(l.full_name)),1)]))]),e("div",xr,[e("div",fr,[e("h4",br,o(l.full_name),1),l.id===((r=b.project)==null?void 0:r.manager_id)?(t(),s("span",hr," Project Manager ")):I("",!0)]),e("p",kr,o(l.role||"Team Member"),1),e("p",wr,o(l.email),1)])]),e("div",_r,[e("div",$r,[e("div",jr,o(M(l.hours_worked||0))+"h",1),g[11]||(g[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",Cr,[e("div",Mr,o(h(l.id)),1),g[12]||(g[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Tr,[e("div",Sr,o(L(l.id)),1),g[13]||(g[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Pr,[e("button",{onClick:c=>S(l),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},g[14]||(g[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Ar),l.id!==((R=b.project)==null?void 0:R.manager_id)?(t(),s("button",{key:0,onClick:c=>N(l),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},g[15]||(g[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,zr)):I("",!0)])])]),e("div",Dr,[e("div",Ir,[g[16]||(g[16]=e("span",null,"Produttività",-1)),e("span",null,o(v(l.id))+"%",1)]),e("div",Vr,[e("div",{class:F(["h-2 rounded-full transition-all duration-300",a(l.id)]),style:ee({width:v(l.id)+"%"})},null,6)])])])}),128)),n.value.length===0?(t(),s("div",Er,[g[18]||(g[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),g[19]||(g[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),g[20]||(g[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Br,[e("button",{onClick:g[1]||(g[1]=l=>p.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},g[17]||(g[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Aggiungi primo membro ")]))])])):I("",!0)])])]),p.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:K},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:g[4]||(g[4]=te(()=>{},["stop"]))},[e("div",Ur,[g[25]||(g[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:te(C,["prevent"])},[e("div",Rr,[e("div",null,[g[22]||(g[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),E(e("select",{"onUpdate:modelValue":g[2]||(g[2]=l=>x.value.user_id=l),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[g[21]||(g[21]=e("option",{value:""},"Seleziona utente",-1)),(t(!0),s(H,null,O(f.value,l=>(t(),s("option",{key:l.id,value:l.id},o(l.full_name)+" ("+o(l.email)+") ",9,Hr))),128))],512),[[G,x.value.user_id]])]),e("div",null,[g[24]||(g[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),E(e("select",{"onUpdate:modelValue":g[3]||(g[3]=l=>x.value.role=l),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},g[23]||(g[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[G,x.value.role]])])]),e("div",Or,[e("button",{type:"button",onClick:K,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(y.value?"Aggiungendo...":"Aggiungi"),9,Fr)])],32)])])])):I("",!0)]))}};function Kr(b,A){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(b,A){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function qr(b,A){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const Nr={class:"fixed inset-0 z-50 overflow-y-auto"},Xr={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},Jr={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},Gr={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Wr={class:"mb-4"},Yr={class:"text-lg font-medium text-gray-900 dark:text-white"},Qr={class:"space-y-4"},Zr={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},eo=["disabled"],to={key:0},so={key:1},ro={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(b,{emit:A}){const _=b,T=A,$=P(!1),p=be({description:"",amount:0,category:"",billing_type:"billable",status:"pending",date:new Date().toISOString().split("T")[0],notes:"",receipt_file:null}),f=async()=>{$.value=!0;try{const x=_.expense?`/api/expenses/${_.expense.id}`:`/api/projects/${_.projectId}/expenses`,n=_.expense?"PUT":"POST";(await fetch(x,{method:n,headers:{"Content-Type":"application/json"},body:JSON.stringify(p)})).ok?T("saved"):console.error("Error saving expense")}catch(x){console.error("Error saving expense:",x)}finally{$.value=!1}},y=x=>{const n=x.target.files[0];if(n){if(n.size>5*1024*1024){alert("Il file è troppo grande. Dimensione massima: 5MB"),x.target.value="";return}p.receipt_file=n}};return se(()=>{_.expense&&Object.assign(p,{description:_.expense.description,amount:_.expense.amount,category:_.expense.category,billing_type:_.expense.billing_type||"billable",status:_.expense.status||"pending",date:_.expense.date.split("T")[0],notes:_.expense.notes||""})}),(x,n)=>(t(),s("div",Nr,[e("div",Xr,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:n[0]||(n[0]=j=>x.$emit("close"))}),e("div",Jr,[e("form",{onSubmit:te(f,["prevent"])},[e("div",Gr,[e("div",Wr,[e("h3",Yr,o(b.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",Qr,[e("div",null,[n[9]||(n[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),E(e("input",{"onUpdate:modelValue":n[1]||(n[1]=j=>p.description=j),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[X,p.description]])]),e("div",null,[n[10]||(n[10]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),E(e("input",{"onUpdate:modelValue":n[2]||(n[2]=j=>p.amount=j),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[X,p.amount,void 0,{number:!0}]])]),e("div",null,[n[12]||(n[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),E(e("select",{"onUpdate:modelValue":n[3]||(n[3]=j=>p.category=j),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[11]||(n[11]=[ae('<option value="">Seleziona categoria</option><option value="licenses">📄 Licenze</option><option value="travel">✈️ Viaggi</option><option value="meals">🍽️ Pasti</option><option value="equipment">🖥️ Attrezzature</option><option value="external">🏢 Servizi Esterni</option><option value="other">📦 Altro</option>',7)]),512),[[G,p.category]])]),e("div",null,[n[14]||(n[14]=e("label",{for:"billing_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Fatturazione ",-1)),E(e("select",{"onUpdate:modelValue":n[4]||(n[4]=j=>p.billing_type=j),id:"billing_type",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[13]||(n[13]=[e("option",{value:"billable"},"💰 Fatturabile al Cliente",-1),e("option",{value:"non-billable"},"🏢 Assorbimento Interno",-1),e("option",{value:"reimbursable"},"💳 Rimborsabile",-1)]),512),[[G,p.billing_type]])]),e("div",null,[n[16]||(n[16]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),E(e("select",{"onUpdate:modelValue":n[5]||(n[5]=j=>p.status=j),id:"status",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[15]||(n[15]=[e("option",{value:"pending"},"⏳ In Attesa di Approvazione",-1),e("option",{value:"approved"},"✅ Approvata",-1),e("option",{value:"rejected"},"❌ Rifiutata",-1)]),512),[[G,p.status]])]),e("div",null,[n[17]||(n[17]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),E(e("input",{"onUpdate:modelValue":n[6]||(n[6]=j=>p.date=j),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,p.date]])]),e("div",null,[n[18]||(n[18]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),E(e("textarea",{"onUpdate:modelValue":n[7]||(n[7]=j=>p.notes=j),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[X,p.notes]])]),e("div",null,[n[19]||(n[19]=e("label",{for:"receipt",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ricevuta/Scontrino ",-1)),e("input",{type:"file",id:"receipt",accept:"image/*,.pdf",onChange:y,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),n[20]||(n[20]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Carica immagine o PDF della ricevuta (max 5MB) ",-1))])])]),e("div",Zr,[e("button",{type:"submit",disabled:$.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[$.value?(t(),s("span",to,"Salvando...")):(t(),s("span",so,o(b.expense?"Aggiorna":"Salva"),1))],8,eo),e("button",{type:"button",onClick:n[8]||(n[8]=j=>x.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},oo={class:"project-expenses"},ao={class:"space-y-6"},no={class:"flex justify-between items-center"},io={key:0,class:"text-center py-8"},lo={key:1,class:"text-center py-12"},uo={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},co={class:"divide-y divide-gray-200 dark:divide-gray-700"},go={class:"flex items-center justify-between"},mo={class:"flex-1"},po={class:"flex items-center"},vo={class:"flex-shrink-0"},yo={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},xo={class:"ml-4 flex-1"},fo={class:"flex items-center justify-between"},bo={class:"text-sm font-medium text-gray-900 dark:text-white"},ho={class:"ml-2 flex-shrink-0"},ko={class:"text-sm font-medium text-gray-900 dark:text-white"},wo={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},_o={class:"capitalize"},$o={key:0,class:"mx-2"},jo={key:1},Co={key:0,class:"flex items-center space-x-2"},Mo=["onClick"],To=["onClick"],So={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Po={class:"flex justify-between items-center"},Ao={class:"text-lg font-bold text-gray-900 dark:text-white"},zo={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(b){const A=b;ye();const _=ne(),T=P(!1),$=P([]),p=P(!1),f=P(null),y=B(()=>_.hasPermission("manage_expenses")),x=B(()=>$.value.reduce((v,a)=>v+a.amount,0)),n=async()=>{var v;if((v=A.project)!=null&&v.id){T.value=!0;try{const a=await fetch(`/api/projects/${A.project.id}/expenses`);a.ok&&($.value=await a.json())}catch(a){console.error("Error loading expenses:",a)}finally{T.value=!1}}},j=v=>{f.value=v,p.value=!0},U=async v=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${v}`,{method:"DELETE"})).ok&&($.value=$.value.filter(M=>M.id!==v))}catch(a){console.error("Error deleting expense:",a)}},V=()=>{p.value=!1,f.value=null},z=()=>{V(),n()},h=v=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(v),L=v=>new Date(v).toLocaleDateString("it-IT");return se(()=>{n()}),(v,a)=>(t(),s("div",oo,[e("div",ao,[e("div",no,[a[2]||(a[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),y.value?(t(),s("button",{key:0,onClick:a[0]||(a[0]=M=>p.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[oe(le(qr),{class:"w-4 h-4 mr-2"}),a[1]||(a[1]=J(" Aggiungi Spesa "))])):I("",!0)]),T.value?(t(),s("div",io,a[3]||(a[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):$.value.length===0?(t(),s("div",lo,[oe(le(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),a[4]||(a[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),a[5]||(a[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(t(),s("div",uo,[e("ul",co,[(t(!0),s(H,null,O($.value,M=>(t(),s("li",{key:M.id,class:"px-6 py-4"},[e("div",go,[e("div",mo,[e("div",po,[e("div",vo,[e("div",yo,[oe(le(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",xo,[e("div",fo,[e("p",bo,o(M.description),1),e("div",ho,[e("p",ko," €"+o(h(M.amount)),1)])]),e("div",wo,[oe(le(Kr),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),J(" "+o(L(M.date))+" ",1),a[6]||(a[6]=e("span",{class:"mx-2"},"•",-1)),e("span",_o,o(M.category),1),M.user?(t(),s("span",$o,"•")):I("",!0),M.user?(t(),s("span",jo,o(M.user.name),1)):I("",!0)])])])]),y.value?(t(),s("div",Co,[e("button",{onClick:k=>j(M),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,Mo),e("button",{onClick:k=>U(M.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,To)])):I("",!0)])]))),128))])])),$.value.length>0?(t(),s("div",So,[e("div",Po,[a[7]||(a[7]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",Ao,"€"+o(h(x.value)),1)])])):I("",!0)]),p.value?(t(),de(ro,{key:0,"project-id":v.projectId,expense:f.value,onClose:V,onSaved:z},null,8,["project-id","expense"])):I("",!0)]))}},Do={class:"project-kpi"},Io={key:0,class:"animate-pulse space-y-4"},Vo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Eo={key:1,class:"space-y-6"},Bo={class:"bg-white shadow rounded-lg p-6"},Uo={class:"flex items-center justify-between"},Ro=["disabled"],Ho={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Oo={class:"bg-white shadow rounded-lg p-6"},Fo={class:"flex items-center"},Lo={class:"ml-5 w-0 flex-1"},Ko={class:"text-lg font-medium text-gray-900"},qo={class:"text-xs text-gray-500"},No={class:"bg-white shadow rounded-lg p-6"},Xo={class:"flex items-center"},Jo={class:"ml-5 w-0 flex-1"},Go={class:"text-lg font-medium text-gray-900"},Wo={class:"bg-white shadow rounded-lg p-6"},Yo={class:"flex items-center"},Qo={class:"ml-5 w-0 flex-1"},Zo={class:"text-lg font-medium text-gray-900"},ea={class:"text-xs text-gray-500"},ta={class:"bg-white shadow rounded-lg p-6"},sa={class:"flex items-center"},ra={class:"ml-5 w-0 flex-1"},oa={class:"text-lg font-medium text-gray-900"},aa={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},na={class:"bg-white shadow rounded-lg p-6"},ia={class:"space-y-4"},la={class:"flex justify-between text-sm"},da={class:"font-medium"},ua={class:"w-full bg-gray-200 rounded-full h-3"},ca={class:"flex justify-between text-sm"},ga={class:"text-gray-600"},ma={class:"font-medium"},pa={class:"bg-white shadow rounded-lg p-6"},va={class:"space-y-4"},ya={class:"flex justify-between text-sm"},xa={class:"font-medium"},fa={class:"w-full bg-gray-200 rounded-full h-3"},ba={class:"flex justify-between text-sm"},ha={class:"text-gray-600"},ka={class:"font-medium"},wa={class:"bg-white shadow rounded-lg p-6"},_a={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},$a={class:"text-center p-4 border rounded-lg"},ja={class:"text-xs text-gray-500"},Ca={class:"text-center p-4 border rounded-lg"},Ma={class:"text-xs text-gray-500"},Ta={class:"text-center p-4 border rounded-lg"},Sa={class:"text-xs text-gray-500"},Pa={key:2,class:"text-center py-8"},Aa={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},za={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},Da={class:"mt-3"},Ia={class:"mt-6 space-y-6"},Va={class:"bg-gray-50 p-4 rounded-lg"},Ea={class:"font-medium text-gray-900"},Ba={class:"text-sm text-gray-600"},Ua={class:"space-y-6"},Ra={class:"flex items-center justify-between mb-4"},Ha={class:"font-medium text-gray-900"},Oa={class:"text-sm text-gray-600"},Fa={class:"flex items-center space-x-2"},La={class:"text-xs text-gray-500"},Ka=["onClick"],qa={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Na=["onUpdate:modelValue","onInput"],Xa=["onUpdate:modelValue","onInput"],Ja=["onUpdate:modelValue","onInput"],Ga={class:"mt-4"},Wa=["onUpdate:modelValue","onInput"],Ya={class:"mt-4 flex justify-end"},Qa=["onClick","disabled"],Za={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},en={key:1,class:"text-sm text-green-600"},tn={class:"mt-6 pt-4 border-t flex justify-between"},sn={class:"flex space-x-3"},rn=["disabled"],on={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(b,{emit:A}){const _=b,T=A,$=P(!1),p=P(!1),f=P(null),y=P({}),x=P({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),n=P({budget:80,time:85,margin:15}),j=B(()=>{var u;return!((u=_.project)!=null&&u.budget)||x.value.totalCosts===0?0:Math.round(x.value.totalCosts/_.project.budget*100)}),U=B(()=>{var u;return!((u=_.project)!=null&&u.estimated_hours)||x.value.totalHours===0?0:Math.round(x.value.totalHours/_.project.estimated_hours*100)}),V=B(()=>{const u=x.value.costVariance;return u>0?"text-red-600":u<0?"text-green-600":"text-gray-600"}),z=B(()=>{const u=x.value.marginPercentage;return u>=n.value.margin?"text-green-600":u>=n.value.margin*.7?"text-yellow-600":"text-red-600"}),h=B(()=>{const u=x.value.marginPercentage;return u>=n.value.margin?"Ottimo":u>=n.value.margin*.7?"Accettabile":"Critico"}),L=B(()=>{const u=j.value;return u>=n.value.budget?"text-red-600":u>=n.value.budget*.8?"text-yellow-600":"text-green-600"}),v=B(()=>{const u=U.value;return u>=n.value.time?"text-red-600":u>=n.value.time*.8?"text-yellow-600":"text-green-600"}),a=B(()=>{const u=x.value.marginPercentage;return u>=n.value.margin?"text-green-600":u>=n.value.margin*.7?"text-yellow-600":"text-red-600"}),M=u=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(u||0),k=u=>!u||u===0?"0h":`${parseFloat(u).toFixed(2)}h`,C=u=>`${(u||0).toFixed(1)}%`,S=async()=>{var u;(u=_.project)!=null&&u.id&&N()},N=()=>{const u=_.project;u&&(x.value={totalHours:u.total_hours||0,workDays:Math.ceil((u.total_hours||0)/8),totalCosts:(u.total_hours||0)*50,costVariance:(u.total_hours||0)*50-(u.budget||0),potentialRevenue:u.budget||0,actualRevenue:u.invoiced_amount||0,marginPercentage:u.budget?(u.budget-(u.total_hours||0)*50)/u.budget*100:0})},K=async()=>{$.value=!0;try{await S(),T("refresh")}catch(u){console.error("Error refreshing KPIs:",u)}finally{$.value=!1}},D=B(()=>{var m;const u=((m=_.project)==null?void 0:m.project_type)||"service";return i(u)}),i=u=>{const m={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return m[u]||m.service},g=u=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[u]||"Sconosciuto",l=()=>{D.value.forEach(m=>{y.value[m.name]||(y.value[m.name]={target_min:m.target_min,target_max:m.target_max,warning_threshold:m.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),p.value=!0},r=()=>{p.value=!1},R=u=>{y.value[u]&&(y.value[u].isDirty=!0,y.value[u].isSaved=!1)},c=u=>{const m=D.value.find(Y=>Y.name===u);m&&y.value[u]&&(y.value[u].target_min=m.target_min,y.value[u].target_max=m.target_max,y.value[u].warning_threshold=m.warning_threshold,y.value[u].custom_description="",y.value[u].isDirty=!0,y.value[u].isSaved=!1)},d=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&D.value.forEach(u=>{c(u.name)})},W=async u=>{var m;if(y.value[u]){f.value=u;try{const Y=y.value[u];await new Promise(ie=>setTimeout(ie,1e3)),console.log("Saving KPI config:",{project_id:(m=_.project)==null?void 0:m.id,kpi_name:u,target_min:Y.target_min,target_max:Y.target_max,warning_threshold:Y.warning_threshold,custom_description:Y.custom_description}),y.value[u].isDirty=!1,y.value[u].isSaved=!0,setTimeout(()=>{y.value[u]&&(y.value[u].isSaved=!1)},3e3)}catch(Y){console.error("Error saving KPI config:",Y),alert("Errore nel salvataggio della configurazione KPI")}finally{f.value=null}}},re=async()=>{const u=D.value.filter(m=>{var Y;return(Y=y.value[m.name])==null?void 0:Y.isDirty});for(const m of u)await W(m.name)},w=B(()=>D.value.some(u=>{var m;return(m=y.value[u.name])==null?void 0:m.isDirty}));return Z(()=>_.project,u=>{u&&S()},{immediate:!0}),se(()=>{_.project&&S()}),(u,m)=>{var Y,ie;return t(),s("div",Do,[b.loading?(t(),s("div",Io,[e("div",Vo,[(t(),s(H,null,O(4,q=>e("div",{key:q,class:"bg-gray-200 rounded-lg h-24"})),64))]),m[0]||(m[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):b.project?(t(),s("div",Eo,[e("div",Bo,[e("div",Uo,[m[3]||(m[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:K,disabled:$.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(t(),s("svg",{class:F(["w-4 h-4 mr-2",{"animate-spin":$.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},m[1]||(m[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),m[2]||(m[2]=J(" Aggiorna "))],8,Ro)])]),e("div",Ho,[e("div",Oo,[e("div",Fo,[m[5]||(m[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",Lo,[e("dl",null,[m[4]||(m[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",Ko,o(k(x.value.totalHours)),1),e("dd",qo,o(x.value.workDays)+" giorni lavorati",1)])])])]),e("div",No,[e("div",Xo,[m[7]||(m[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",Jo,[e("dl",null,[m[6]||(m[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",Go,o(M(x.value.totalCosts)),1),e("dd",{class:F(["text-xs",V.value])},o(M(x.value.costVariance))+" vs budget",3)])])])]),e("div",Wo,[e("div",Yo,[m[9]||(m[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",Qo,[e("dl",null,[m[8]||(m[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",Zo,o(M(x.value.potentialRevenue)),1),e("dd",ea,o(M(x.value.actualRevenue))+" fatturati",1)])])])]),e("div",ta,[e("div",sa,[m[11]||(m[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",ra,[e("dl",null,[m[10]||(m[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",oa,o(C(x.value.marginPercentage)),1),e("dd",{class:F(["text-xs",z.value])},o(h.value),3)])])])])]),e("div",aa,[e("div",na,[m[13]||(m[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",ia,[e("div",la,[m[12]||(m[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",da,o(M(b.project.budget||0)),1)]),e("div",ua,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:ee({width:j.value+"%"})},null,4)]),e("div",ca,[e("span",ga,"Utilizzato: "+o(M(x.value.totalCosts)),1),e("span",ma,o(j.value)+"%",1)])])]),e("div",pa,[m[15]||(m[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",va,[e("div",ya,[m[14]||(m[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",xa,o(k(b.project.estimated_hours||0)),1)]),e("div",fa,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:ee({width:U.value+"%"})},null,4)]),e("div",ba,[e("span",ha,"Lavorate: "+o(k(x.value.totalHours)),1),e("span",ka,o(U.value)+"%",1)])])])]),e("div",wa,[e("div",{class:"flex items-center justify-between mb-4"},[m[17]||(m[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:l,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},m[16]||(m[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),J(" Configura KPI ")]))]),e("div",_a,[e("div",$a,[e("div",{class:F(["text-2xl font-bold",L.value])},o(j.value)+"% ",3),m[18]||(m[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",ja,"Soglia: "+o(n.value.budget)+"%",1)]),e("div",Ca,[e("div",{class:F(["text-2xl font-bold",v.value])},o(U.value)+"% ",3),m[19]||(m[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",Ma,"Soglia: "+o(n.value.time)+"%",1)]),e("div",Ta,[e("div",{class:F(["text-2xl font-bold",a.value])},o(C(x.value.marginPercentage)),3),m[20]||(m[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Sa,"Soglia: "+o(n.value.margin)+"%",1)])])])])):(t(),s("div",Pa,m[21]||(m[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),p.value?(t(),s("div",Aa,[e("div",za,[e("div",Da,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[m[23]||(m[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:r,class:"text-gray-400 hover:text-gray-600"},m[22]||(m[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ia,[e("div",Va,[e("h4",Ea,o((Y=b.project)==null?void 0:Y.name),1),e("p",Ba,"Tipo: "+o(g((ie=b.project)==null?void 0:ie.project_type)),1)]),e("div",Ua,[(t(!0),s(H,null,O(D.value,q=>{var ce,ge;return t(),s("div",{key:q.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Ra,[e("div",null,[e("h5",Ha,o(q.display_name),1),e("p",Oa,o(q.description),1)]),e("div",Fa,[e("span",La,o(q.unit),1),e("button",{onClick:Q=>c(q.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,Ka)])]),e("div",qa,[e("div",null,[m[24]||(m[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>y.value[q.name].target_min=Q,onInput:Q=>R(q.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Na),[[X,y.value[q.name].target_min]])]),e("div",null,[m[25]||(m[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>y.value[q.name].target_max=Q,onInput:Q=>R(q.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Xa),[[X,y.value[q.name].target_max]])]),e("div",null,[m[26]||(m[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>y.value[q.name].warning_threshold=Q,onInput:Q=>R(q.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,Ja),[[X,y.value[q.name].warning_threshold]])])]),e("div",Ga,[m[27]||(m[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),E(e("textarea",{"onUpdate:modelValue":Q=>y.value[q.name].custom_description=Q,onInput:Q=>R(q.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,Wa),[[X,y.value[q.name].custom_description]])]),e("div",Ya,[(ce=y.value[q.name])!=null&&ce.isDirty?(t(),s("button",{key:0,onClick:Q=>W(q.name),disabled:f.value===q.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[f.value===q.name?(t(),s("svg",Za,m[28]||(m[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):I("",!0),J(" "+o(f.value===q.name?"Salvataggio...":"Salva KPI"),1)],8,Qa)):(ge=y.value[q.name])!=null&&ge.isSaved?(t(),s("span",en,"✓ Salvato")):I("",!0)])])}),128))])]),e("div",tn,[e("button",{onClick:d,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",sn,[e("button",{onClick:r,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:re,disabled:!w.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,rn)])])])])])):I("",!0)])}}},an={class:"space-y-6"},nn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},ln={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},dn={class:"flex items-center justify-between"},un={class:"flex items-center space-x-4"},cn={class:"flex items-center space-x-2"},gn={key:0,class:"p-6"},mn={class:"overflow-x-auto"},pn={class:"min-w-[1000px]"},vn={class:"flex mb-4"},yn={class:"flex-1 flex"},xn={class:"space-y-1"},fn={class:"w-80 flex-shrink-0 px-4 py-3"},bn={class:"flex items-center space-x-2"},hn={class:"flex-1 min-w-0"},kn={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},wn={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},_n={key:0},$n={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},jn={class:"flex-1 relative h-12 flex items-center"},Cn=["title"],Mn={class:"truncate"},Tn={class:"ml-2"},Sn={key:1,class:"text-center py-12"},Pn={key:2,class:"flex justify-center py-12"},An={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(b,{expose:A}){const _=b,T=P("weeks"),$=P(new Date),p=P([]),f=B(()=>{var k;return((k=_.project)==null?void 0:k.tasks)||[]}),y=B(()=>f.value.filter(k=>k.start_date&&k.due_date).map(k=>{const C=n(k);return{...k,timeline:C}})),x=()=>{const k=new Date($.value),C=[],S=12;for(let N=0;N<S;N++){const K=new Date(k);T.value==="weeks"?K.setDate(k.getDate()+N*7):T.value==="months"&&K.setMonth(k.getMonth()+N),C.push(K)}p.value=C},n=k=>{if(!p.value.length)return null;const C=new Date(k.start_date),S=new Date(k.due_date),N=p.value[0],D=p.value[p.value.length-1]-N,i=C-N,g=S-C,l=Math.max(0,i/D*100),r=Math.min(100-l,g/D*100);return{leftPercent:l,widthPercent:Math.max(5,r)}},j=k=>T.value==="weeks"?`${k.getDate()}/${k.getMonth()+1}`:T.value==="months"?k.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",U=k=>{const C=new Date,S=new Date(k);if(T.value==="weeks"){const N=new Date(S),K=new Date(S);return K.setDate(K.getDate()+6),C>=N&&C<=K}else if(T.value==="months")return S.getMonth()===C.getMonth()&&S.getFullYear()===C.getFullYear();return!1},V=()=>{const k=new Date;if(T.value==="weeks"){const C=new Date(k);C.setDate(k.getDate()-k.getDay()),$.value=C}else{const C=new Date(k.getFullYear(),k.getMonth(),1);$.value=C}x()},z=k=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[k]||"bg-gray-400",h=k=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[k]||"bg-gray-500",L=k=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[k]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",v=k=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[k]||"Non specificata",a=k=>({todo:0,"in-progress":50,review:75,done:100})[k.status]||0,M=k=>k?new Date(k).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return Z(()=>_.project,()=>{x()},{immediate:!0}),se(()=>{V()}),A({refresh:x}),(k,C)=>(t(),s("div",an,[e("div",nn,[e("div",ln,[e("div",dn,[C[3]||(C[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",un,[e("div",cn,[C[2]||(C[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":C[0]||(C[0]=S=>T.value=S),onChange:x,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},C[1]||(C[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[G,T.value]])]),e("button",{onClick:V,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!b.loading&&y.value.length>0?(t(),s("div",gn,[e("div",mn,[e("div",pn,[e("div",vn,[C[4]||(C[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",yn,[(t(!0),s(H,null,O(p.value,(S,N)=>(t(),s("div",{key:N,class:F(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":U(S)}])},o(j(S)),3))),128))])]),e("div",xn,[(t(!0),s(H,null,O(y.value,S=>(t(),s("div",{key:S.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",fn,[e("div",bn,[e("div",{class:F(["w-3 h-3 rounded-full",z(S.status)])},null,2),e("div",hn,[e("p",kn,o(S.name),1),e("div",wn,[S.assignee?(t(),s("span",_n,o(S.assignee.full_name),1)):I("",!0),e("span",{class:F(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",L(S.priority)])},o(v(S.priority)),3)]),e("div",$n,o(M(S.start_date))+" - "+o(M(S.due_date)),1)])])]),e("div",jn,[S.timeline?(t(),s("div",{key:0,class:F(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",h(S.status)]),style:ee({left:S.timeline.leftPercent+"%",width:S.timeline.widthPercent+"%",minWidth:"60px"}),title:`${S.name} - ${a(S)}% completato`},[e("span",Mn,o(S.name.length>15?S.name.substring(0,15)+"...":S.name),1),e("span",Tn,o(a(S))+"%",1)],14,Cn)):I("",!0),S.timeline&&a(S)>0&&a(S)<100?(t(),s("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:ee({left:S.timeline.leftPercent+"%",width:S.timeline.widthPercent*a(S)/100+"%",minWidth:"2px"})},null,4)):I("",!0),(t(!0),s(H,null,O(p.value,(N,K)=>(t(),s("div",{key:K,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:ee({left:K/p.value.length*100+"%"})},null,4))),128))])]))),128))])])]),C[5]||(C[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):b.loading?I("",!0):(t(),s("div",Sn,C[6]||(C[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),b.loading?(t(),s("div",Pn,C[7]||(C[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):I("",!0)])]))}},zn={class:"space-y-6"},Dn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},In={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Vn={class:"flex items-center justify-between"},En={class:"flex items-center space-x-4"},Bn={class:"flex items-center space-x-2"},Un={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},Rn={class:"flex items-center space-x-2"},Hn=["value"],On={key:0,class:"flex justify-center py-8"},Fn={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},Ln={class:"text-red-600"},Kn={key:2,class:"p-6"},qn={class:"overflow-x-auto"},Nn={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Xn={class:"bg-gray-50 dark:bg-gray-700"},Jn={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Gn={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},Wn={class:"text-sm font-medium text-gray-900 dark:text-white"},Yn={class:"text-xs text-gray-500 dark:text-gray-400"},Qn=["onClick"],Zn={key:0,class:"text-xs font-medium text-primary-600 dark:text-primary-400"},ei={key:1,class:"text-gray-300 dark:text-gray-600"},ti={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},si={class:"text-sm font-medium text-gray-900 dark:text-white"},ri={class:"bg-gray-100 dark:bg-gray-600 font-medium"},oi={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},ai={key:0,class:"text-center py-8"},ni={class:"mt-3"},ii={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},li={class:"grid grid-cols-1 gap-4"},di=["value"],ui={class:"flex justify-end space-x-3 mt-6"},ci=["disabled"],gi={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(b,{expose:A}){const _=b,T=ne(),$=P(null),p=P(!1),f=P(""),y=P(!1),x=P(new Date().getFullYear()),n=P(new Date().getMonth()+1),j=P(""),U=P(!1),V=P(!1),z=P(null),h=P({task_id:"",date:"",hours:0,description:""}),L=B(()=>$.value?Array.from({length:$.value.days_in_month},(D,i)=>i+1):[]),v=async()=>{var D;if((D=_.project)!=null&&D.id){p.value=!0,f.value="";try{const i=new URLSearchParams({year:x.value.toString(),month:n.value.toString()});j.value&&i.append("member_id",j.value.toString());const g=await fetch(`/api/timesheets/project/${_.project.id}/monthly?${i}`,{headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken}});if(!g.ok)throw new Error("Errore nel caricamento del timesheet");const l=await g.json();$.value=l.data}catch(i){f.value=i.message}finally{p.value=!1}}},a=async()=>{y.value=!0;try{const D={...h.value,project_id:_.project.id};if(!(await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken},body:JSON.stringify(D)})).ok)throw new Error("Errore nel salvataggio del timesheet");await v(),k()}catch(D){f.value=D.message}finally{y.value=!1}},M=(D,i)=>{const g=$.value.tasks.find(l=>l.id===D);g&&(z.value={taskId:D,day:i},h.value={task_id:D,date:`${x.value}-${String(n.value).padStart(2,"0")}-${String(i).padStart(2,"0")}`,hours:g.daily_hours[i]||0,description:""},g.daily_hours[i]>0?V.value=!0:U.value=!0)},k=()=>{U.value=!1,V.value=!1,z.value=null,h.value={task_id:"",date:"",hours:0,description:""}},C=()=>{n.value===1?(n.value=12,x.value--):n.value--,v()},S=()=>{n.value===12?(n.value=1,x.value++):n.value++,v()},N=D=>{const i=new Date;return i.getFullYear()===x.value&&i.getMonth()+1===n.value&&i.getDate()===D},K=D=>!D||D===0?"0":D%1===0?D.toString():D.toFixed(2);return Z(()=>{var D;return(D=_.project)==null?void 0:D.id},D=>{D&&v()}),Z(j,()=>{v()}),se(()=>{var D;(D=_.project)!=null&&D.id&&v()}),A({refresh:v}),(D,i)=>{var g,l;return t(),s("div",zn,[e("div",Dn,[e("div",In,[e("div",Vn,[e("div",En,[i[11]||(i[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Bn,[e("button",{onClick:C,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},i[7]||(i[7]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",Un,o(n.value)+"/"+o(x.value),1),e("button",{onClick:S,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},i[8]||(i[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",Rn,[i[10]||(i[10]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":i[0]||(i[0]=r=>j.value=r),onChange:v,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[9]||(i[9]=e("option",{value:""},"Tutti i membri",-1)),(t(!0),s(H,null,O(((g=b.project)==null?void 0:g.team_members)||[],r=>(t(),s("option",{key:r.id,value:r.id},o(r.first_name)+" "+o(r.last_name),9,Hn))),128))],544),[[G,j.value]])])]),e("button",{onClick:i[1]||(i[1]=r=>U.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[12]||(i[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Aggiungi Ore ")]))])]),p.value?(t(),s("div",On,i[13]||(i[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):I("",!0),f.value?(t(),s("div",Fn,[e("p",Ln,o(f.value),1)])):I("",!0),!p.value&&$.value?(t(),s("div",Kn,[e("div",qn,[e("table",Nn,[e("thead",Xn,[e("tr",null,[i[14]||(i[14]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(t(!0),s(H,null,O(L.value,r=>(t(),s("th",{key:r,class:F(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":N(r)}])},o(r),3))),128)),i[15]||(i[15]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",Jn,[(t(!0),s(H,null,O($.value.tasks,r=>(t(),s("tr",{key:r.id},[e("td",Gn,[e("div",Wn,o(r.name),1),e("div",Yn,o(r.workers.length?r.workers.join(", "):"Nessuno ha lavorato"),1)]),(t(!0),s(H,null,O(L.value,R=>(t(),s("td",{key:R,class:F(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":N(R)}]),onClick:c=>M(r.id,R)},[r.daily_hours[R]>0?(t(),s("span",Zn,o(K(r.daily_hours[R])),1)):(t(),s("span",ei,"-"))],10,Qn))),128)),e("td",ti,[e("span",si,o(K(r.total_hours)),1)])]))),128)),e("tr",ri,[i[16]||(i[16]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(t(!0),s(H,null,O(L.value,r=>(t(),s("td",{key:r,class:F(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":N(r)}])},o(K($.value.daily_totals[r]||0)),3))),128)),e("td",oi,o(K($.value.grand_total)),1)])])])]),$.value.tasks.length===0?(t(),s("div",ai,i[17]||(i[17]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):I("",!0)])):I("",!0)]),U.value||V.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:k},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[6]||(i[6]=te(()=>{},["stop"]))},[e("div",ni,[e("h3",ii,o(V.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:te(a,["prevent"])},[e("div",li,[e("div",null,[i[19]||(i[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),E(e("select",{"onUpdate:modelValue":i[2]||(i[2]=r=>h.value.task_id=r),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[18]||(i[18]=e("option",{value:""},"Seleziona task",-1)),(t(!0),s(H,null,O(((l=$.value)==null?void 0:l.tasks)||[],r=>(t(),s("option",{key:r.id,value:r.id},o(r.name),9,di))),128))],512),[[G,h.value.task_id]])]),e("div",null,[i[20]||(i[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),E(e("input",{"onUpdate:modelValue":i[3]||(i[3]=r=>h.value.date=r),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.date]])]),e("div",null,[i[21]||(i[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),E(e("input",{"onUpdate:modelValue":i[4]||(i[4]=r=>h.value.hours=r),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.hours]])]),e("div",null,[i[22]||(i[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":i[5]||(i[5]=r=>h.value.description=r),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.description]])])]),e("div",ui,[e("button",{type:"button",onClick:k,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(y.value?"Salvataggio...":V.value?"Aggiorna":"Aggiungi"),9,ci)])],32)])])])):I("",!0)])}}},mi={class:"space-y-6"},pi={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},vi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},yi={class:"flex items-center justify-between"},xi={class:"flex items-center space-x-3"},fi=["disabled"],bi={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},hi={key:1,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},ki={key:0,class:"px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700"},wi={class:"flex items-start space-x-3"},_i={class:"flex-1"},$i={class:"text-sm font-medium text-purple-900 dark:text-purple-100"},ji={class:"mt-2 space-y-2"},Ci={key:0,class:"mt-3"},Mi={class:"space-y-2"},Ti={class:"flex items-center space-x-3"},Si={class:"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center"},Pi={class:"text-xs font-medium text-purple-600 dark:text-purple-300"},Ai={class:"text-sm font-medium text-gray-900 dark:text-white"},zi={class:"text-xs text-gray-500 dark:text-gray-400"},Di=["onClick"],Ii={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Vi={key:0,class:"p-6"},Ei={class:"animate-pulse space-y-4"},Bi={key:1,class:"p-6 text-center"},Ui={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},Ri={class:"flex items-center justify-between"},Hi={class:"flex items-center space-x-4"},Oi={class:"w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center"},Fi={class:"text-sm font-medium text-primary-600 dark:text-primary-300"},Li={class:"text-sm font-medium text-gray-900 dark:text-white"},Ki={class:"text-sm text-gray-500 dark:text-gray-400"},qi={class:"flex items-center space-x-4"},Ni={class:"text-right"},Xi={class:"text-sm font-medium text-gray-900 dark:text-white"},Ji={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Gi={class:"flex items-center space-x-2"},Wi=["onClick"],Yi=["onClick"],Qi={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Zi={class:"p-6"},el={class:"space-y-4"},tl={class:"w-32 text-sm text-gray-600 dark:text-gray-400"},sl={class:"flex-1 mx-4"},rl={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3"},ol={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},al={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},nl={class:"mt-3"},il={class:"space-y-4"},ll=["value"],dl={class:"flex justify-end space-x-3 mt-6"},ul=["disabled"],cl={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},gl={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},ml={class:"mt-3"},pl={class:"space-y-4"},vl={class:"flex justify-end space-x-3 mt-6"},yl=["disabled"],xl={__name:"ProjectResourceAllocation",props:{project:{type:Object,required:!0}},setup(b){const A=b,_=ne(),T=P(!0),$=P(!1),p=P(!1),f=P([]),y=P([]),x=P([]),n=P(null),j=P(!1),U=P(!1),V=P({user_id:"",role:"",allocation_percentage:100}),z=P({id:null,role:"",allocation_percentage:100}),h=B(()=>{var l;return(l=A.project)==null?void 0:l.id}),L=async()=>{var l;if(h.value){T.value=!0;try{const r=await fetch(`/api/resources?project_id=${h.value}`,{headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}});if(!r.ok)throw new Error("Errore nel caricamento allocazioni");const R=await r.json();f.value=((l=R.data)==null?void 0:l.resources)||[],await a()}catch(r){console.error("Error loading allocations:",r)}finally{T.value=!1}}},v=async()=>{var l;try{const r=await fetch("/api/personnel",{headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}});if(!r.ok)throw new Error("Errore nel caricamento utenti");const R=await r.json();y.value=((l=R.data)==null?void 0:l.users)||[]}catch(r){console.error("Error loading users:",r)}},a=async()=>{x.value=f.value.map(l=>({user_id:l.user_id,user_name:l.user_name,total_allocation:l.allocation_percentage+Math.floor(Math.random()*30)}))},M=async()=>{var l;if(h.value){p.value=!0;try{const r=await fetch(`/api/ai-resources/analyze-allocation/${h.value}`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({include_suggestions:!0,analysis_depth:"detailed"})});if(!r.ok)throw new Error("Errore nell'analisi AI");const R=await r.json();n.value=((l=R.data)==null?void 0:l.analysis)||null}catch(r){console.error("Error in AI analysis:",r),alert("Errore nell'analisi AI: "+r.message)}finally{p.value=!1}}},k=async()=>{$.value=!0;try{if(!(await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({project_id:h.value,...V.value})})).ok)throw new Error("Errore nell'aggiunta risorsa");await L(),j.value=!1,V.value={user_id:"",role:"",allocation_percentage:100}}catch(l){console.error("Error adding resource:",l),alert("Errore nell'aggiunta risorsa: "+l.message)}finally{$.value=!1}},C=l=>{z.value={id:l.id,role:l.role,allocation_percentage:l.allocation_percentage},U.value=!0},S=async()=>{$.value=!0;try{if(!(await fetch(`/api/resources/${z.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({role:z.value.role,allocation_percentage:z.value.allocation_percentage})})).ok)throw new Error("Errore nell'aggiornamento allocazione");await L(),U.value=!1,z.value={id:null,role:"",allocation_percentage:100}}catch(l){console.error("Error updating allocation:",l),alert("Errore nell'aggiornamento: "+l.message)}finally{$.value=!1}},N=async l=>{if(confirm("Sei sicuro di voler rimuovere questa allocazione?"))try{if(!(await fetch(`/api/resources/${l.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}})).ok)throw new Error("Errore nella rimozione");await L()}catch(r){console.error("Error removing allocation:",r),alert("Errore nella rimozione: "+r.message)}},K=async l=>{try{await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({project_id:h.value,user_id:l.user_id,role:l.role,allocation_percentage:l.allocation})}),await L()}catch(r){console.error("Error applying AI recommendation:",r)}},D=l=>l>=80?"bg-red-500":l>=60?"bg-yellow-500":"bg-green-500",i=l=>l>100?"bg-red-500":l>=90?"bg-yellow-500":"bg-green-500",g=l=>l>100?"text-red-600 dark:text-red-400":l>=90?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400";return Z(()=>A.project,l=>{l&&L()},{immediate:!0}),se(()=>{v()}),(l,r)=>{var R;return t(),s("div",mi,[e("div",pi,[e("div",vi,[e("div",yi,[r[12]||(r[12]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazione Risorse "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci l'allocazione delle risorse con assistenza AI ")],-1)),e("div",xi,[e("button",{onClick:M,disabled:p.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"},[p.value?(t(),s("svg",hi,r[10]||(r[10]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(t(),s("svg",bi,r[9]||(r[9]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},null,-1)]))),J(" "+o(p.value?"Analizzando...":"Analisi AI"),1)],8,fi),e("button",{onClick:r[0]||(r[0]=c=>j.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},r[11]||(r[11]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),J(" Aggiungi Risorsa ")]))])])]),n.value?(t(),s("div",ki,[e("div",wi,[r[15]||(r[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])],-1)),e("div",_i,[e("h4",$i," Insights AI - Efficienza: "+o(n.value.efficiency_score)+"% ",1),e("div",ji,[(t(!0),s(H,null,O(n.value.optimization_insights,c=>(t(),s("div",{key:c,class:"text-sm text-purple-700 dark:text-purple-300"}," • "+o(c),1))),128))]),(R=n.value.recommended_allocations)!=null&&R.length?(t(),s("div",Ci,[r[13]||(r[13]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"}," Raccomandazioni AI: ",-1)),e("div",Mi,[(t(!0),s(H,null,O(n.value.recommended_allocations,c=>{var d;return t(),s("div",{key:c.user_id,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3"},[e("div",Ti,[e("div",Si,[e("span",Pi,o((d=c.user_name)==null?void 0:d.charAt(0)),1)]),e("div",null,[e("p",Ai,o(c.user_name),1),e("p",zi,o(c.role)+" - "+o(c.allocation)+"%",1)])]),e("button",{onClick:W=>K(c),class:"text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700"}," Applica ",8,Di)])}),128))])])):I("",!0)]),e("button",{onClick:r[1]||(r[1]=c=>n.value=null),class:"flex-shrink-0 text-purple-400 hover:text-purple-600"},r[14]||(r[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])):I("",!0)]),e("div",Ii,[r[20]||(r[20]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazioni Attuali ")],-1)),T.value?(t(),s("div",Vi,[e("div",Ei,[(t(),s(H,null,O(3,c=>e("div",{key:c,class:"flex items-center space-x-4"},r[16]||(r[16]=[ae('<div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div><div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div></div><div class="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>',3)]))),64))])])):f.value.length?(t(),s("div",Ui,[(t(!0),s(H,null,O(f.value,c=>{var d;return t(),s("div",{key:c.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",Ri,[e("div",Hi,[e("div",Oi,[e("span",Fi,o((d=c.user_name)==null?void 0:d.charAt(0)),1)]),e("div",null,[e("h4",Li,o(c.user_name),1),e("p",Ki,o(c.role||"Team Member"),1)])]),e("div",qi,[e("div",Ni,[e("div",Xi,o(c.allocation_percentage)+"% ",1),e("div",Ji,[e("div",{class:F(["h-2 rounded-full",D(c.allocation_percentage)]),style:ee({width:c.allocation_percentage+"%"})},null,6)])]),e("div",Gi,[e("button",{onClick:W=>C(c),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},r[18]||(r[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Wi),e("button",{onClick:W=>N(c),class:"text-red-400 hover:text-red-600"},r[19]||(r[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Yi)])])])])}),128))])):(t(),s("div",Bi,r[17]||(r[17]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna risorsa allocata",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti. ",-1)])))]),e("div",Qi,[r[21]||(r[21]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Utilizzo Risorse ")],-1)),e("div",Zi,[e("div",el,[(t(!0),s(H,null,O(x.value,c=>(t(),s("div",{key:c.user_id,class:"flex items-center"},[e("div",tl,o(c.user_name),1),e("div",sl,[e("div",rl,[e("div",{class:F(["h-3 rounded-full transition-all duration-300",i(c.total_allocation)]),style:ee({width:Math.min(c.total_allocation,100)+"%"})},null,6)])]),e("div",{class:F(["w-16 text-sm text-right font-medium",g(c.total_allocation)])},o(c.total_allocation)+"% ",3)]))),128))])])]),j.value?(t(),s("div",ol,[e("div",al,[e("div",nl,[r[26]||(r[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Risorsa ",-1)),e("form",{onSubmit:te(k,["prevent"])},[e("div",il,[e("div",null,[r[23]||(r[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Utente ",-1)),E(e("select",{"onUpdate:modelValue":r[2]||(r[2]=c=>V.value.user_id=c),required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[r[22]||(r[22]=e("option",{value:""},"Seleziona utente...",-1)),(t(!0),s(H,null,O(y.value,c=>(t(),s("option",{key:c.id,value:c.id},o(c.full_name)+" ("+o(c.role)+") ",9,ll))),128))],512),[[G,V.value.user_id]])]),e("div",null,[r[24]||(r[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":r[3]||(r[3]=c=>V.value.role=c),type:"text",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[X,V.value.role]])]),e("div",null,[r[25]||(r[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":r[4]||(r[4]=c=>V.value.allocation_percentage=c),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,V.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",dl,[e("button",{type:"button",onClick:r[5]||(r[5]=c=>j.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:$.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},o($.value?"Salvando...":"Aggiungi"),9,ul)])],32)])])])):I("",!0),U.value?(t(),s("div",cl,[e("div",gl,[e("div",ml,[r[29]||(r[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Modifica Allocazione ",-1)),e("form",{onSubmit:te(S,["prevent"])},[e("div",pl,[e("div",null,[r[27]||(r[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":r[6]||(r[6]=c=>z.value.role=c),type:"text",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[X,z.value.role]])]),e("div",null,[r[28]||(r[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":r[7]||(r[7]=c=>z.value.allocation_percentage=c),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,z.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",vl,[e("button",{type:"button",onClick:r[8]||(r[8]=c=>U.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:$.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},o($.value?"Salvando...":"Aggiorna"),9,yl)])],32)])])])):I("",!0)])}}},fl={class:"project-view"},bl={class:"tab-content"},hl={__name:"ProjectView",setup(b){const A=ye(),_=ne(),T=he(),$=we(),p=P(!0),f=P("overview"),y=B(()=>A.currentProject),x=B(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"resources",label:"Allocazione Risorse",icon:"user-group"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(h=>!!(["overview","tasks","gantt","team","timesheet","resources"].includes(h.id)||h.id==="kpi"&&_.hasPermission("view_reports")||h.id==="expenses"&&_.hasPermission("manage_expenses")))),n=B(()=>({overview:me,tasks:Js,team:Lr,resources:xl,expenses:zo,kpi:on,gantt:An,timesheet:gi})[f.value]||me),j=async()=>{p.value=!0;try{const z=T.params.id;await A.fetchProject(z)}catch(z){console.error("Error loading project:",z)}finally{p.value=!1}},U=()=>{$.push(`/projects/${T.params.id}/edit`)},V=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await A.deleteProject(T.params.id),$.push("/projects")}catch(z){console.error("Error deleting project:",z)}};return Z(()=>T.params.id,(z,h)=>{z&&z!==h&&j()}),Z(()=>T.hash,z=>{if(z){const h=z.replace("#","");x.value.find(L=>L.id===h)&&f.value!==h&&(f.value=h)}},{immediate:!0}),Z(f,z=>{const h=`#${z}`;T.hash!==h&&$.replace({...T,hash:h})}),se(()=>{if(T.hash){const z=T.hash.replace("#","");x.value.find(h=>h.id===z)&&(f.value=z)}j()}),(z,h)=>(t(),s("div",fl,[oe(Ue,{project:y.value,loading:p.value,onEdit:U,onDelete:V},null,8,["project","loading"]),oe(qe,{modelValue:f.value,"onUpdate:modelValue":h[0]||(h[0]=L=>f.value=L),tabs:x.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",bl,[(t(),de(ke,null,[(t(),de(ve(n.value),{project:y.value,loading:p.value},null,8,["project","loading"]))],1024))])]))}},_l=ue(hl,[["__scopeId","data-v-de1f32e3"]]);export{_l as default};
