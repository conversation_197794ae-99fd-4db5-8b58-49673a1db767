import{c as r,o as s,j as e,t as o,n as H,g as C,m as W,a as oe,i as ve,b as ye,F as L,k as K,h as de,M as xe,f as U,z as te,r as S,w as ee,A as re,v as E,G as Y,H as ae,x as X,s as se,N as be,p as ie,u as he,O as ke,l as we}from"./vendor.js";import{_ as ue,u as ne,a as _e,b as fe}from"./app.js";const $e={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},je={key:0,class:"animate-pulse"},Ce={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Me={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Se={class:"text-2xl font-bold text-gray-900"},Pe={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},ze={key:0},Ae={key:1},De={key:2},Ie={key:3},Ve={class:"mt-4 sm:mt-0 flex space-x-3"},Ee={key:2,class:"text-center py-8"},Be={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(p){const z=v=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[v]||"bg-gray-100 text-gray-800",w=v=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[v]||v,T=v=>v?new Date(v).toLocaleDateString("it-IT"):"",$=v=>v?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(v):"";return(v,k)=>{const f=ye("router-link");return s(),r("div",$e,[p.loading?(s(),r("div",je,k[1]||(k[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):p.project?(s(),r("div",Ce,[e("div",Me,[e("div",Te,[e("h1",Se,o(p.project.name),1),e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",z(p.project.status)])},o(w(p.project.status)),3)]),e("div",Pe,[p.project.client?(s(),r("span",ze,[k[2]||(k[2]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),W(" Cliente: "+o(p.project.client.name),1)])):C("",!0),p.project.start_date?(s(),r("span",Ae,[k[3]||(k[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),W(" Inizio: "+o(T(p.project.start_date)),1)])):C("",!0),p.project.end_date?(s(),r("span",De,[k[4]||(k[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),W(" Fine: "+o(T(p.project.end_date)),1)])):C("",!0),p.project.budget?(s(),r("span",Ie,[k[5]||(k[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),W(" Budget: "+o($(p.project.budget)),1)])):C("",!0)])]),e("div",Ve,[oe(f,{to:`/app/projects/${p.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:ve(()=>k[6]||(k[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),W(" Modifica ")])),_:1,__:[6]},8,["to"]),e("button",{onClick:k[0]||(k[0]=b=>v.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},k[7]||(k[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),W(" Elimina ")]))])])):(s(),r("div",Ee,k[8]||(k[8]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Ue=ue(Be,[["__scopeId","data-v-6f1b5cc9"]]),Re={class:"tab-navigation"},He={class:"border-b border-gray-200"},Oe={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Fe=["onClick","aria-current"],Le={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},Ke={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:p=>p.every(z=>typeof z=="object"&&z.id&&z.label)}},emits:["update:modelValue"],setup(p,{emit:z}){const w=p,T=z,$=f=>w.modelValue===f,v=f=>{T("update:modelValue",f)},k=f=>{const b={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return b[f]||b["chart-bar"]};return(f,b)=>(s(),r("div",Re,[e("div",He,[e("nav",Oe,[(s(!0),r(L,null,K(p.tabs,n=>(s(),r("button",{key:n.id,onClick:j=>v(n.id),class:H(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",$(n.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":$(n.id)?"page":void 0},[n.icon?(s(),de(xe(k(n.icon)),{key:0,class:"w-4 h-4"})):C("",!0),e("span",null,o(n.label),1),n.count!==void 0?(s(),r("span",Le,o(n.count),1)):C("",!0)],10,Fe))),128))])])]))}},qe=ue(Ke,[["__scopeId","data-v-c205976e"]]),Ne={class:"project-overview"},Xe={key:0,class:"animate-pulse space-y-4"},Ge={key:1,class:"space-y-6"},Je={class:"bg-white shadow rounded-lg p-6"},We={key:0,class:"text-gray-600"},Ye={key:1,class:"text-gray-400 italic"},Qe={key:0,class:"bg-white shadow rounded-lg p-6"},Ze={class:"flex items-center justify-between mb-4"},et={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},tt={class:"text-sm font-medium text-gray-900"},st={class:"text-sm font-medium text-gray-900"},rt={class:"text-sm font-medium text-gray-900"},ot={key:0,class:"mt-4 pt-4 border-t border-gray-200"},at={class:"grid grid-cols-2 gap-4"},nt={key:0},lt={class:"text-sm font-medium text-gray-900"},it={key:1},dt={class:"text-sm font-medium text-gray-900"},ut={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},ct={class:"bg-white shadow rounded-lg p-6"},gt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white shadow rounded-lg p-6"},yt={class:"flex items-center"},xt={class:"ml-5 w-0 flex-1"},ft={class:"text-lg font-medium text-gray-900"},bt={class:"bg-white shadow rounded-lg p-6"},ht={class:"flex items-center"},kt={class:"ml-5 w-0 flex-1"},wt={class:"text-lg font-medium text-gray-900"},_t={class:"bg-white shadow rounded-lg p-6"},$t={class:"flex items-center"},jt={class:"ml-5 w-0 flex-1"},Ct={class:"text-lg font-medium text-gray-900"},Mt={class:"bg-white shadow rounded-lg p-6"},Tt={class:"w-full bg-gray-200 rounded-full h-2.5"},St={class:"text-sm text-gray-500 mt-2"},Pt={class:"bg-white shadow rounded-lg p-6"},zt={class:"space-y-4"},At={class:"flex justify-between items-center"},Dt={class:"text-sm font-medium"},It={class:"flex justify-between items-center"},Vt={class:"text-sm font-medium"},Et={class:"w-full bg-gray-200 rounded-full h-3"},Bt={class:"flex justify-between items-center text-sm"},Ut={class:"bg-white shadow rounded-lg p-6"},Rt={class:"space-y-3"},Ht={class:"flex-shrink-0"},Ot=["src","alt"],Ft={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},Lt={class:"text-xs font-medium text-gray-600"},Kt={class:"flex-1"},qt={class:"text-sm font-medium text-gray-900"},Nt={class:"text-xs text-gray-500"},Xt={class:"text-right"},Gt={class:"text-xs text-gray-500"},Jt={key:0,class:"text-center py-4"},Wt={class:"bg-white shadow rounded-lg p-6"},Yt={class:"space-y-3"},Qt={class:"flex-shrink-0"},Zt={class:"flex-1"},es={class:"text-sm text-gray-900"},ts={class:"flex items-center space-x-2 mt-1"},ss={class:"text-xs text-gray-500"},rs={class:"text-xs text-gray-500"},os={key:0,class:"text-center py-4"},as={key:2,class:"text-center py-8"},ns={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(p){const z=p,w=U(()=>{if(!z.project||!z.project.task_count)return 0;const d=z.project.completed_tasks||0,a=z.project.task_count||1;return Math.round(d/a*100)}),T=U(()=>{var d;return((d=z.project)==null?void 0:d.team_members)||[]}),$=U(()=>{var _,D,R;if((_=z.project)!=null&&_.expenses)return z.project.expenses;const d=((D=z.project)==null?void 0:D.total_hours)||0,a=(R=z.project)!=null&&R.client_daily_rate?z.project.client_daily_rate/8:50;return d*a}),v=U(()=>{var a;return(((a=z.project)==null?void 0:a.budget)||0)-$.value}),k=U(()=>{var a;const d=((a=z.project)==null?void 0:a.budget)||1;return Math.min(Math.round($.value/d*100),100)}),f=U(()=>{const d=k.value;return d>=90?"bg-red-600":d>=75?"bg-yellow-600":"bg-green-600"}),b=U(()=>{var a;const d=v.value;return d<0?"text-red-600":d<(((a=z.project)==null?void 0:a.budget)||0)*.1?"text-yellow-600":"text-green-600"}),n=U(()=>{var d;return(d=z.project)!=null&&d.tasks?[...z.project.tasks].sort((a,_)=>new Date(_.updated_at)-new Date(a.updated_at)).slice(0,5).map(a=>{var _;return{id:a.id,description:`Task "${a.name}" ${j(a.status)}`,created_at:a.updated_at,user_name:((_=a.assignee)==null?void 0:_.full_name)||"Non assegnato",type:O(a.status)}}):[]}),j=d=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[d]||d,O=d=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[d]||"task_updated",I=d=>d?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(d):"Non specificato",A=d=>d?new Date(d).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",h=d=>d?d.split(" ").map(a=>a.charAt(0).toUpperCase()).slice(0,2).join(""):"??",q=d=>{const a={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return a[d]||a.default},V=d=>({fixed_price:"Prezzo Fisso",time_and_materials:"Tempo e Materiali",retainer:"Retainer",milestone:"Milestone"})[d]||d,P=d=>({draft:"Bozza",sent:"Inviato",signed:"Firmato",active:"Attivo",completed:"Completato",cancelled:"Annullato"})[d]||d,B=d=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",signed:"bg-green-100 text-green-800",active:"bg-green-100 text-green-800",completed:"bg-purple-100 text-purple-800",cancelled:"bg-red-100 text-red-800"})[d]||"bg-gray-100 text-gray-800";return(d,a)=>{const _=ye("router-link");return s(),r("div",Ne,[p.loading?(s(),r("div",Xe,a[0]||(a[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):p.project?(s(),r("div",Ge,[e("div",Je,[a[1]||(a[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),p.project.description?(s(),r("p",We,o(p.project.description),1)):(s(),r("p",Ye,"Nessuna descrizione disponibile"))]),p.project.contract?(s(),r("div",Qe,[e("div",Ze,[a[3]||(a[3]=e("h3",{class:"text-lg font-medium text-gray-900"},"Contratto Collegato",-1)),oe(_,{to:`/app/crm/contracts/${p.project.contract.id}`,class:"text-blue-600 hover:text-blue-800 text-sm font-medium"},{default:ve(()=>a[2]||(a[2]=[W(" Gestisci Contratto → ")])),_:1,__:[2]},8,["to"])]),e("div",et,[e("div",null,[a[4]||(a[4]=e("dt",{class:"text-sm text-gray-500"},"Numero Contratto",-1)),e("dd",tt,o(p.project.contract.contract_number),1)]),e("div",null,[a[5]||(a[5]=e("dt",{class:"text-sm text-gray-500"},"Tipo",-1)),e("dd",st,o(V(p.project.contract.contract_type)),1)]),e("div",null,[a[6]||(a[6]=e("dt",{class:"text-sm text-gray-500"},"Tariffa Oraria",-1)),e("dd",rt,o(I(p.project.contract.hourly_rate))+"/h",1)]),e("div",null,[a[7]||(a[7]=e("dt",{class:"text-sm text-gray-500"},"Stato",-1)),e("dd",null,[e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",B(p.project.contract.status)])},o(P(p.project.contract.status)),3)])])]),p.project.contract.budget_hours||p.project.contract.budget_amount?(s(),r("div",ot,[e("div",at,[p.project.contract.budget_hours?(s(),r("div",nt,[a[8]||(a[8]=e("dt",{class:"text-sm text-gray-500"},"Budget Ore",-1)),e("dd",lt,o(p.project.contract.budget_hours)+"h",1)])):C("",!0),p.project.contract.budget_amount?(s(),r("div",it,[a[9]||(a[9]=e("dt",{class:"text-sm text-gray-500"},"Budget Importo",-1)),e("dd",dt,o(I(p.project.contract.budget_amount)),1)])):C("",!0)])])):C("",!0)])):C("",!0),e("div",ut,[e("div",ct,[e("div",gt,[a[11]||(a[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",mt,[e("dl",null,[a[10]||(a[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",pt,o(p.project.task_count||0),1)])])])]),e("div",vt,[e("div",yt,[a[13]||(a[13]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",xt,[e("dl",null,[a[12]||(a[12]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",ft,o(p.project.completed_tasks||0),1)])])])]),e("div",bt,[e("div",ht,[a[15]||(a[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",kt,[e("dl",null,[a[14]||(a[14]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",wt,o(p.project.team_count||0),1)])])])]),e("div",_t,[e("div",$t,[a[17]||(a[17]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",jt,[e("dl",null,[a[16]||(a[16]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",Ct,o(I(p.project.budget)),1)])])])])]),e("div",Mt,[a[18]||(a[18]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",Tt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:te({width:`${w.value}%`})},null,4)]),e("p",St,o(w.value)+"% completato",1)]),e("div",Pt,[a[23]||(a[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",zt,[e("div",At,[a[19]||(a[19]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",Dt,o(I(p.project.budget)),1)]),a[22]||(a[22]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",It,[a[20]||(a[20]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",Vt,o(I($.value)),1)]),e("div",Et,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",f.value]),style:te({width:k.value+"%"})},null,6)]),e("div",Bt,[a[21]||(a[21]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:H(["font-medium",b.value])},o(I(v.value)),3)])])]),e("div",Ut,[a[25]||(a[25]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Rt,[(s(!0),r(L,null,K(T.value,D=>(s(),r("div",{key:D.id,class:"flex items-center space-x-3"},[e("div",Ht,[D.profile_image?(s(),r("img",{key:0,src:D.profile_image,alt:D.full_name,class:"w-8 h-8 rounded-full"},null,8,Ot)):(s(),r("div",Ft,[e("span",Lt,o(h(D.full_name)),1)]))]),e("div",Kt,[e("p",qt,o(D.full_name),1),e("p",Nt,o(D.role||"Team Member"),1)]),e("div",Xt,[e("p",Gt,o(D.hours_worked||0)+"h",1)])]))),128)),T.value.length===0?(s(),r("div",Jt,a[24]||(a[24]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):C("",!0)])]),e("div",Wt,[a[28]||(a[28]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Yt,[(s(!0),r(L,null,K(n.value,D=>(s(),r("div",{key:D.id,class:"flex items-start space-x-3"},[e("div",Qt,[e("div",{class:H(["w-2 h-2 rounded-full mt-2",q(D.type)])},null,2)]),e("div",Zt,[e("p",es,o(D.description),1),e("div",ts,[e("p",ss,o(A(D.created_at)),1),a[26]||(a[26]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",rs,o(D.user_name),1)])])]))),128)),n.value.length===0?(s(),r("div",os,a[27]||(a[27]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):C("",!0)])])])):(s(),r("div",as,a[29]||(a[29]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},me=ue(ns,[["__scopeId","data-v-e2382cbb"]]),ls={class:"space-y-6"},is={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},ds={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},us={class:"flex items-center justify-between"},cs={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},gs=["value"],ms={class:"mt-4 flex items-center justify-between"},ps={class:"flex items-center space-x-4"},vs={class:"text-sm text-gray-500 dark:text-gray-400"},ys={key:0,class:"flex justify-center py-8"},xs={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},fs={class:"text-red-600"},bs={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},hs={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ks={class:"col-span-4"},ws={class:"text-sm font-medium text-gray-900 dark:text-white"},_s={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},$s={class:"col-span-2"},js={key:0,class:"flex items-center"},Cs={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},Ms={class:"ml-2"},Ts={class:"text-sm font-medium text-gray-900 dark:text-white"},Ss={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Ps={class:"col-span-1"},zs={class:"col-span-1"},As={class:"col-span-2"},Ds={key:0,class:"text-sm text-gray-900 dark:text-white"},Is={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Vs={class:"col-span-1"},Es={class:"text-sm text-gray-900 dark:text-white"},Bs={key:0,class:"text-gray-500"},Us={class:"col-span-1"},Rs={class:"flex items-center space-x-2"},Hs=["onClick"],Os={key:0,class:"px-6 py-12 text-center"},Fs={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ls={class:"flex items-center justify-between mb-4"},Ks={class:"font-medium text-gray-900 dark:text-white"},qs={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},Ns={class:"space-y-3"},Xs=["onClick"],Gs={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},Js={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Ws={class:"flex items-center justify-between"},Ys={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Qs={class:"mt-3"},Zs={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},er={class:"grid grid-cols-1 gap-4"},tr={class:"grid grid-cols-2 gap-4"},sr=["value"],rr={class:"grid grid-cols-2 gap-4"},or={class:"flex justify-end space-x-3 mt-6"},ar=["disabled"],nr={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(p,{expose:z}){const w=p,T=ne(),{hasPermission:$}=_e(),v=S([]),k=S(!1),f=S(""),b=S("list"),n=S(!1),j=S({status:"",priority:"",assignee_id:"",search:""}),O=S(!1),I=S(!1),A=S(null),h=S({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),q=U(()=>$.value("manage_project_tasks")),V=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],P=async()=>{var c,i;if((c=w.project)!=null&&c.id){k.value=!0,f.value="";try{const J=new URLSearchParams({project_id:w.project.id,...j.value}),M=await fetch(`/api/tasks?${J}`,{headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken}});if(!M.ok)throw new Error("Errore nel caricamento dei task");const x=await M.json();v.value=((i=x.data)==null?void 0:i.tasks)||x.tasks||[]}catch(J){f.value=J.message}finally{k.value=!1}}},B=async()=>{n.value=!0;try{const c=I.value?`/api/tasks/${A.value.id}`:"/api/tasks",i=I.value?"PUT":"POST",J={...h.value,project_id:w.project.id};if(!(await fetch(c,{method:i,headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken},body:JSON.stringify(J)})).ok)throw new Error("Errore nel salvataggio del task");await P(),a()}catch(c){f.value=c.message}finally{n.value=!1}},d=c=>{A.value=c,h.value={name:c.name,description:c.description||"",status:c.status,priority:c.priority,assignee_id:c.assignee_id||"",due_date:c.due_date?c.due_date.split("T")[0]:"",estimated_hours:c.estimated_hours},I.value=!0},a=()=>{O.value=!1,I.value=!1,A.value=null,h.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},_=c=>v.value.filter(i=>i.status===c),D=c=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[c]||"bg-gray-100 text-gray-800",R=c=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[c]||c,G=c=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[c]||"bg-gray-100 text-gray-800",y=c=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[c]||c,t=(c,i)=>`${(c==null?void 0:c.charAt(0))||""}${(i==null?void 0:i.charAt(0))||""}`.toUpperCase(),u=c=>new Date(c).toLocaleDateString("it-IT");let l;const F=()=>{clearTimeout(l),l=setTimeout(()=>{P()},300)};return ee(()=>{var c;return(c=w.project)==null?void 0:c.id},c=>{c&&P()}),re(()=>{var c;(c=w.project)!=null&&c.id&&P()}),z({refresh:P}),(c,i)=>{var J,M;return s(),r("div",ls,[e("div",is,[e("div",ds,[e("div",us,[i[16]||(i[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),q.value?(s(),r("button",{key:0,onClick:i[0]||(i[0]=x=>O.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[15]||(i[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),W(" Nuovo Task ")]))):C("",!0)]),e("div",cs,[e("div",null,[i[18]||(i[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":i[1]||(i[1]=x=>j.value.status=x),onChange:P,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[17]||(i[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[Y,j.value.status]])]),e("div",null,[i[20]||(i[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":i[2]||(i[2]=x=>j.value.priority=x),onChange:P,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[19]||(i[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[Y,j.value.priority]])]),e("div",null,[i[22]||(i[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":i[3]||(i[3]=x=>j.value.assignee_id=x),onChange:P,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[21]||(i[21]=e("option",{value:""},"Tutti",-1)),(s(!0),r(L,null,K(((J=p.project)==null?void 0:J.team_members)||[],x=>(s(),r("option",{key:x.id,value:x.id},o(x.first_name)+" "+o(x.last_name),9,gs))),128))],544),[[Y,j.value.assignee_id]])]),e("div",null,[i[23]||(i[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),E(e("input",{"onUpdate:modelValue":i[4]||(i[4]=x=>j.value.search=x),onInput:F,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[X,j.value.search]])])]),e("div",ms,[e("div",ps,[i[24]||(i[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:i[5]||(i[5]=x=>b.value="list"),class:H([b.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:i[6]||(i[6]=x=>b.value="kanban"),class:H([b.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",vs,o(v.value.length)+" task trovati ",1)])])]),k.value?(s(),r("div",ys,i[25]||(i[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):C("",!0),f.value?(s(),r("div",xs,[e("p",fs,o(f.value),1)])):C("",!0),!k.value&&b.value==="list"?(s(),r("div",bs,[e("div",hs,[i[27]||(i[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(s(!0),r(L,null,K(v.value,x=>(s(),r("div",{key:x.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ks,[e("div",ws,o(x.name),1),x.description?(s(),r("div",_s,o(x.description),1)):C("",!0)]),e("div",$s,[x.assignee?(s(),r("div",js,[e("div",Cs,o(t(x.assignee.first_name,x.assignee.last_name)),1),e("div",Ms,[e("div",Ts,o(x.assignee.first_name)+" "+o(x.assignee.last_name),1)])])):(s(),r("span",Ss,"Non assegnato"))]),e("div",Ps,[e("span",{class:H([D(x.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(R(x.status)),3)]),e("div",zs,[e("span",{class:H([G(x.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(y(x.priority)),3)]),e("div",As,[x.due_date?(s(),r("div",Ds,o(u(x.due_date)),1)):(s(),r("span",Is,"-"))]),e("div",Vs,[e("div",Es,[W(o(x.actual_hours||0)+"h ",1),x.estimated_hours?(s(),r("span",Bs,"/ "+o(x.estimated_hours)+"h",1)):C("",!0)])]),e("div",Us,[e("div",Rs,[e("button",{onClick:g=>d(x),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Hs)])])]))),128)),v.value.length===0?(s(),r("div",Os,i[26]||(i[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):C("",!0)])])):C("",!0),!k.value&&b.value==="kanban"?(s(),r("div",Fs,[(s(),r(L,null,K(V,x=>e("div",{key:x.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Ls,[e("h4",Ks,o(x.label),1),e("span",qs,o(_(x.value).length),1)]),e("div",Ns,[(s(!0),r(L,null,K(_(x.value),g=>(s(),r("div",{key:g.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:m=>d(g)},[e("div",Gs,o(g.name),1),g.description?(s(),r("div",Js,o(g.description),1)):C("",!0),e("div",Ws,[e("span",{class:H([G(g.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},o(y(g.priority)),3),g.assignee?(s(),r("div",Ys,o(t(g.assignee.first_name,g.assignee.last_name)),1)):C("",!0)])],8,Xs))),128))])])),64))])):C("",!0),O.value||I.value?(s(),r("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:a},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[14]||(i[14]=se(()=>{},["stop"]))},[e("div",Qs,[e("h3",Zs,o(I.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:se(B,["prevent"])},[e("div",er,[e("div",null,[i[28]||(i[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),E(e("input",{"onUpdate:modelValue":i[7]||(i[7]=x=>h.value.name=x),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.name]])]),e("div",null,[i[29]||(i[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":i[8]||(i[8]=x=>h.value.description=x),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.description]])]),e("div",tr,[e("div",null,[i[31]||(i[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":i[9]||(i[9]=x=>h.value.status=x),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[30]||(i[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[Y,h.value.status]])]),e("div",null,[i[33]||(i[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":i[10]||(i[10]=x=>h.value.priority=x),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},i[32]||(i[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[Y,h.value.priority]])])]),e("div",null,[i[35]||(i[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":i[11]||(i[11]=x=>h.value.assignee_id=x),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[34]||(i[34]=e("option",{value:""},"Non assegnato",-1)),(s(!0),r(L,null,K(((M=p.project)==null?void 0:M.team_members)||[],x=>(s(),r("option",{key:x.id,value:x.id},o(x.first_name)+" "+o(x.last_name),9,sr))),128))],512),[[Y,h.value.assignee_id]])]),e("div",rr,[e("div",null,[i[36]||(i[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),E(e("input",{"onUpdate:modelValue":i[12]||(i[12]=x=>h.value.due_date=x),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.due_date]])]),e("div",null,[i[37]||(i[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),E(e("input",{"onUpdate:modelValue":i[13]||(i[13]=x=>h.value.estimated_hours=x),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.estimated_hours]])])])]),e("div",or,[e("button",{type:"button",onClick:a,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:n.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(n.value?"Salvataggio...":I.value?"Aggiorna":"Crea"),9,ar)])],32)])])])):C("",!0)])}}},lr={class:"space-y-6"},ir={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},dr={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ur={class:"flex items-center justify-between"},cr={class:"p-6 border-b border-gray-200 dark:border-gray-700"},gr={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},mr={class:"text-center"},pr={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},vr={class:"text-center"},yr={class:"text-2xl font-bold text-green-600"},xr={class:"text-center"},fr={class:"text-2xl font-bold text-blue-600"},br={class:"text-center"},hr={class:"text-2xl font-bold text-purple-600"},kr={class:"p-6"},wr={class:"space-y-4"},_r={class:"flex items-center justify-between"},$r={class:"flex items-center space-x-4"},jr={class:"flex-shrink-0"},Cr=["src","alt"],Mr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Tr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},Sr={class:"flex-1"},Pr={class:"flex items-center space-x-2"},zr={class:"text-lg font-medium text-gray-900 dark:text-white"},Ar={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},Dr={class:"text-sm text-gray-600 dark:text-gray-400"},Ir={class:"text-xs text-gray-500 dark:text-gray-500"},Vr={class:"flex items-center space-x-4"},Er={class:"text-right"},Br={class:"text-sm font-medium text-gray-900 dark:text-white"},Ur={class:"text-right"},Rr={class:"text-sm font-medium text-gray-900 dark:text-white"},Hr={class:"text-right"},Or={class:"text-sm font-medium text-gray-900 dark:text-white"},Fr={class:"flex items-center space-x-2"},Lr=["onClick"],Kr=["onClick"],qr={class:"mt-4"},Nr={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},Xr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Gr={key:0,class:"text-center py-8"},Jr={class:"mt-6"},Wr={class:"mt-3"},Yr={class:"space-y-4"},Qr=["value"],Zr={class:"flex justify-end space-x-3 mt-6"},eo=["disabled"],to={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(p,{expose:z,emit:w}){const T=p,$=ne(),v=S(!1),k=S([]),f=S(!1),b=S({user_id:"",role:""}),n=U(()=>{var y;return((y=T.project)==null?void 0:y.team_members)||[]}),j=U(()=>n.value.reduce((y,t)=>y+(t.hours_worked||0),0)),O=U(()=>n.value.length===0?0:Math.round(j.value/n.value.length)),I=U(()=>n.value.filter(y=>(y.hours_worked||0)>0).length),A=y=>y?y.split(" ").map(t=>t.charAt(0).toUpperCase()).slice(0,2).join(""):"??",h=y=>{var u;return(((u=T.project)==null?void 0:u.tasks)||[]).filter(l=>l.assignee_id===y).length},q=y=>{var u;return(((u=T.project)==null?void 0:u.tasks)||[]).filter(l=>l.assignee_id===y&&l.status==="done").length},V=y=>{const t=h(y),u=q(y);return t===0?0:Math.round(u/t*100)},P=y=>{const t=V(y);return t>=80?"bg-green-600":t>=60?"bg-yellow-600":t>=40?"bg-orange-600":"bg-red-600"},B=y=>!y||y===0?"0.00":parseFloat(y).toFixed(2),d=async()=>{var y;try{const t=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(t.ok){const u=await t.json(),l=n.value.map(F=>F.id);k.value=(y=u.data)!=null&&y.users?u.data.users.filter(F=>!l.includes(F.id)):[]}}catch(t){console.error("Errore nel caricamento utenti:",t),k.value=[]}},a=async()=>{f.value=!0;try{const y=await fetch(`/api/projects/${T.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify(b.value)});if(y.ok)G("refresh"),R();else{const t=await y.json();alert(t.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{f.value=!1}},_=y=>{console.log("Edit member:",y)},D=async y=>{if(confirm(`Rimuovere ${y.full_name} dal progetto?`))try{const t=await fetch(`/api/projects/${T.project.id}/team/${y.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(t.ok)G("refresh");else{const u=await t.json();alert(u.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},R=()=>{v.value=!1,b.value={user_id:"",role:""}},G=w;return re(()=>{d()}),ee(()=>v.value,y=>{y&&d()}),ee(()=>{var y;return(y=T.project)==null?void 0:y.team_members},()=>{v.value&&d()}),z({refresh:d}),(y,t)=>(s(),r("div",lr,[e("div",ir,[e("div",dr,[e("div",ur,[t[6]||(t[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:t[0]||(t[0]=u=>v.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},t[5]||(t[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),W(" Aggiungi Membro ")]))])]),e("div",cr,[e("div",gr,[e("div",mr,[e("div",pr,o(n.value.length),1),t[7]||(t[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",vr,[e("div",yr,o(j.value)+"h",1),t[8]||(t[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",xr,[e("div",fr,o(O.value)+"h",1),t[9]||(t[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",br,[e("div",hr,o(I.value),1),t[10]||(t[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",kr,[e("div",wr,[(s(!0),r(L,null,K(n.value,u=>{var l,F;return s(),r("div",{key:u.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",_r,[e("div",$r,[e("div",jr,[u.profile_image?(s(),r("img",{key:0,src:u.profile_image,alt:u.full_name,class:"w-12 h-12 rounded-full"},null,8,Cr)):(s(),r("div",Mr,[e("span",Tr,o(A(u.full_name)),1)]))]),e("div",Sr,[e("div",Pr,[e("h4",zr,o(u.full_name),1),u.id===((l=p.project)==null?void 0:l.manager_id)?(s(),r("span",Ar," Project Manager ")):C("",!0)]),e("p",Dr,o(u.role||"Team Member"),1),e("p",Ir,o(u.email),1)])]),e("div",Vr,[e("div",Er,[e("div",Br,o(B(u.hours_worked||0))+"h",1),t[11]||(t[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",Ur,[e("div",Rr,o(h(u.id)),1),t[12]||(t[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Hr,[e("div",Or,o(q(u.id)),1),t[13]||(t[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Fr,[e("button",{onClick:c=>_(u),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},t[14]||(t[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Lr),u.id!==((F=p.project)==null?void 0:F.manager_id)?(s(),r("button",{key:0,onClick:c=>D(u),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},t[15]||(t[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Kr)):C("",!0)])])]),e("div",qr,[e("div",Nr,[t[16]||(t[16]=e("span",null,"Produttività",-1)),e("span",null,o(V(u.id))+"%",1)]),e("div",Xr,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",P(u.id)]),style:te({width:V(u.id)+"%"})},null,6)])])])}),128)),n.value.length===0?(s(),r("div",Gr,[t[18]||(t[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),t[19]||(t[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),t[20]||(t[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Jr,[e("button",{onClick:t[1]||(t[1]=u=>v.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},t[17]||(t[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),W(" Aggiungi primo membro ")]))])])):C("",!0)])])]),v.value?(s(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:R},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[4]||(t[4]=se(()=>{},["stop"]))},[e("div",Wr,[t[25]||(t[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:se(a,["prevent"])},[e("div",Yr,[e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),E(e("select",{"onUpdate:modelValue":t[2]||(t[2]=u=>b.value.user_id=u),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[21]||(t[21]=e("option",{value:""},"Seleziona utente",-1)),(s(!0),r(L,null,K(k.value,u=>(s(),r("option",{key:u.id,value:u.id},o(u.full_name)+" ("+o(u.email)+") ",9,Qr))),128))],512),[[Y,b.value.user_id]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),E(e("select",{"onUpdate:modelValue":t[3]||(t[3]=u=>b.value.role=u),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[23]||(t[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[Y,b.value.role]])])]),e("div",Zr,[e("button",{type:"button",onClick:R,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:f.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(f.value?"Aggiungendo...":"Aggiungi"),9,eo)])],32)])])])):C("",!0)]))}};function so(p,z){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(p,z){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function ro(p,z){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const oo={class:"fixed inset-0 z-50 overflow-y-auto"},ao={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},no={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},lo={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},io={class:"mb-4"},uo={class:"text-lg font-medium text-gray-900 dark:text-white"},co={class:"space-y-4"},go={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},mo=["disabled"],po={key:0},vo={key:1},yo={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(p,{emit:z}){const w=p,T=z,$=S(!1),v=be({description:"",amount:0,category:"",billing_type:"billable",status:"pending",date:new Date().toISOString().split("T")[0],notes:"",receipt_file:null}),k=async()=>{$.value=!0;try{const b=w.expense?`/api/expenses/${w.expense.id}`:`/api/projects/${w.projectId}/expenses`,n=w.expense?"PUT":"POST";(await fetch(b,{method:n,headers:{"Content-Type":"application/json"},body:JSON.stringify(v)})).ok?T("saved"):console.error("Error saving expense")}catch(b){console.error("Error saving expense:",b)}finally{$.value=!1}},f=b=>{const n=b.target.files[0];if(n){if(n.size>5*1024*1024){alert("Il file è troppo grande. Dimensione massima: 5MB"),b.target.value="";return}v.receipt_file=n}};return re(()=>{w.expense&&Object.assign(v,{description:w.expense.description,amount:w.expense.amount,category:w.expense.category,billing_type:w.expense.billing_type||"billable",status:w.expense.status||"pending",date:w.expense.date.split("T")[0],notes:w.expense.notes||""})}),(b,n)=>(s(),r("div",oo,[e("div",ao,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:n[0]||(n[0]=j=>b.$emit("close"))}),e("div",no,[e("form",{onSubmit:se(k,["prevent"])},[e("div",lo,[e("div",io,[e("h3",uo,o(p.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",co,[e("div",null,[n[9]||(n[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),E(e("input",{"onUpdate:modelValue":n[1]||(n[1]=j=>v.description=j),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[X,v.description]])]),e("div",null,[n[10]||(n[10]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),E(e("input",{"onUpdate:modelValue":n[2]||(n[2]=j=>v.amount=j),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[X,v.amount,void 0,{number:!0}]])]),e("div",null,[n[12]||(n[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),E(e("select",{"onUpdate:modelValue":n[3]||(n[3]=j=>v.category=j),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[11]||(n[11]=[ae('<option value="">Seleziona categoria</option><option value="licenses">📄 Licenze</option><option value="travel">✈️ Viaggi</option><option value="meals">🍽️ Pasti</option><option value="equipment">🖥️ Attrezzature</option><option value="external">🏢 Servizi Esterni</option><option value="other">📦 Altro</option>',7)]),512),[[Y,v.category]])]),e("div",null,[n[14]||(n[14]=e("label",{for:"billing_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Fatturazione ",-1)),E(e("select",{"onUpdate:modelValue":n[4]||(n[4]=j=>v.billing_type=j),id:"billing_type",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[13]||(n[13]=[e("option",{value:"billable"},"💰 Fatturabile al Cliente",-1),e("option",{value:"non-billable"},"🏢 Assorbimento Interno",-1),e("option",{value:"reimbursable"},"💳 Rimborsabile",-1)]),512),[[Y,v.billing_type]])]),e("div",null,[n[16]||(n[16]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),E(e("select",{"onUpdate:modelValue":n[5]||(n[5]=j=>v.status=j),id:"status",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[15]||(n[15]=[e("option",{value:"pending"},"⏳ In Attesa di Approvazione",-1),e("option",{value:"approved"},"✅ Approvata",-1),e("option",{value:"rejected"},"❌ Rifiutata",-1)]),512),[[Y,v.status]])]),e("div",null,[n[17]||(n[17]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),E(e("input",{"onUpdate:modelValue":n[6]||(n[6]=j=>v.date=j),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,v.date]])]),e("div",null,[n[18]||(n[18]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),E(e("textarea",{"onUpdate:modelValue":n[7]||(n[7]=j=>v.notes=j),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[X,v.notes]])]),e("div",null,[n[19]||(n[19]=e("label",{for:"receipt",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ricevuta/Scontrino ",-1)),e("input",{type:"file",id:"receipt",accept:"image/*,.pdf",onChange:f,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),n[20]||(n[20]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Carica immagine o PDF della ricevuta (max 5MB) ",-1))])])]),e("div",go,[e("button",{type:"submit",disabled:$.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[$.value?(s(),r("span",po,"Salvando...")):(s(),r("span",vo,o(p.expense?"Aggiorna":"Salva"),1))],8,mo),e("button",{type:"button",onClick:n[8]||(n[8]=j=>b.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},xo={class:"project-expenses"},fo={class:"space-y-6"},bo={class:"flex justify-between items-center"},ho={key:0,class:"text-center py-8"},ko={key:1,class:"text-center py-12"},wo={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},_o={class:"divide-y divide-gray-200 dark:divide-gray-700"},$o={class:"flex items-center justify-between"},jo={class:"flex-1"},Co={class:"flex items-center"},Mo={class:"flex-shrink-0"},To={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},So={class:"ml-4 flex-1"},Po={class:"flex items-center justify-between"},zo={class:"text-sm font-medium text-gray-900 dark:text-white"},Ao={class:"ml-2 flex-shrink-0"},Do={class:"text-sm font-medium text-gray-900 dark:text-white"},Io={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},Vo={class:"capitalize"},Eo={key:0,class:"mx-2"},Bo={key:1},Uo={key:0,class:"flex items-center space-x-2"},Ro=["onClick"],Ho=["onClick"],Oo={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Fo={class:"flex justify-between items-center"},Lo={class:"text-lg font-bold text-gray-900 dark:text-white"},Ko={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(p){const z=p;fe();const w=ne(),T=S(!1),$=S([]),v=S(!1),k=S(null),f=U(()=>w.hasPermission("manage_expenses")),b=U(()=>$.value.reduce((V,P)=>V+P.amount,0)),n=async()=>{var V;if((V=z.project)!=null&&V.id){T.value=!0;try{const P=await fetch(`/api/projects/${z.project.id}/expenses`);P.ok&&($.value=await P.json())}catch(P){console.error("Error loading expenses:",P)}finally{T.value=!1}}},j=V=>{k.value=V,v.value=!0},O=async V=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${V}`,{method:"DELETE"})).ok&&($.value=$.value.filter(B=>B.id!==V))}catch(P){console.error("Error deleting expense:",P)}},I=()=>{v.value=!1,k.value=null},A=()=>{I(),n()},h=V=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(V),q=V=>new Date(V).toLocaleDateString("it-IT");return re(()=>{n()}),(V,P)=>(s(),r("div",xo,[e("div",fo,[e("div",bo,[P[2]||(P[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),f.value?(s(),r("button",{key:0,onClick:P[0]||(P[0]=B=>v.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[oe(ie(ro),{class:"w-4 h-4 mr-2"}),P[1]||(P[1]=W(" Aggiungi Spesa "))])):C("",!0)]),T.value?(s(),r("div",ho,P[3]||(P[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):$.value.length===0?(s(),r("div",ko,[oe(ie(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),P[4]||(P[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),P[5]||(P[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(s(),r("div",wo,[e("ul",_o,[(s(!0),r(L,null,K($.value,B=>(s(),r("li",{key:B.id,class:"px-6 py-4"},[e("div",$o,[e("div",jo,[e("div",Co,[e("div",Mo,[e("div",To,[oe(ie(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",So,[e("div",Po,[e("p",zo,o(B.description),1),e("div",Ao,[e("p",Do," €"+o(h(B.amount)),1)])]),e("div",Io,[oe(ie(so),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),W(" "+o(q(B.date))+" ",1),P[6]||(P[6]=e("span",{class:"mx-2"},"•",-1)),e("span",Vo,o(B.category),1),B.user?(s(),r("span",Eo,"•")):C("",!0),B.user?(s(),r("span",Bo,o(B.user.name),1)):C("",!0)])])])]),f.value?(s(),r("div",Uo,[e("button",{onClick:d=>j(B),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,Ro),e("button",{onClick:d=>O(B.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,Ho)])):C("",!0)])]))),128))])])),$.value.length>0?(s(),r("div",Oo,[e("div",Fo,[P[7]||(P[7]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",Lo,"€"+o(h(b.value)),1)])])):C("",!0)]),v.value?(s(),de(yo,{key:0,"project-id":V.projectId,expense:k.value,onClose:I,onSaved:A},null,8,["project-id","expense"])):C("",!0)]))}},qo={class:"project-kpi"},No={key:0,class:"animate-pulse space-y-4"},Xo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Go={key:1,class:"space-y-6"},Jo={class:"bg-white shadow rounded-lg p-6"},Wo={class:"flex items-center justify-between"},Yo=["disabled"],Qo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Zo={class:"bg-white shadow rounded-lg p-6"},ea={class:"flex items-center"},ta={class:"ml-5 w-0 flex-1"},sa={class:"text-lg font-medium text-gray-900"},ra={class:"text-xs text-gray-500"},oa={class:"bg-white shadow rounded-lg p-6"},aa={class:"flex items-center"},na={class:"ml-5 w-0 flex-1"},la={class:"text-lg font-medium text-gray-900"},ia={class:"bg-white shadow rounded-lg p-6"},da={class:"flex items-center"},ua={class:"ml-5 w-0 flex-1"},ca={class:"text-lg font-medium text-gray-900"},ga={class:"text-xs text-gray-500"},ma={class:"bg-white shadow rounded-lg p-6"},pa={class:"flex items-center"},va={class:"ml-5 w-0 flex-1"},ya={class:"text-lg font-medium text-gray-900"},xa={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},fa={class:"bg-white shadow rounded-lg p-6"},ba={class:"space-y-4"},ha={class:"flex justify-between text-sm"},ka={class:"font-medium"},wa={class:"w-full bg-gray-200 rounded-full h-3"},_a={class:"flex justify-between text-sm"},$a={class:"text-gray-600"},ja={class:"font-medium"},Ca={class:"bg-white shadow rounded-lg p-6"},Ma={class:"space-y-4"},Ta={class:"flex justify-between text-sm"},Sa={class:"font-medium"},Pa={class:"w-full bg-gray-200 rounded-full h-3"},za={class:"flex justify-between text-sm"},Aa={class:"text-gray-600"},Da={class:"font-medium"},Ia={class:"bg-white shadow rounded-lg p-6"},Va={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ea={class:"text-center p-4 border rounded-lg"},Ba={class:"text-xs text-gray-500"},Ua={class:"text-center p-4 border rounded-lg"},Ra={class:"text-xs text-gray-500"},Ha={class:"text-center p-4 border rounded-lg"},Oa={class:"text-xs text-gray-500"},Fa={key:2,class:"text-center py-8"},La={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ka={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},qa={class:"mt-3"},Na={class:"mt-6 space-y-6"},Xa={class:"bg-gray-50 p-4 rounded-lg"},Ga={class:"font-medium text-gray-900"},Ja={class:"text-sm text-gray-600"},Wa={class:"space-y-6"},Ya={class:"flex items-center justify-between mb-4"},Qa={class:"font-medium text-gray-900"},Za={class:"text-sm text-gray-600"},en={class:"flex items-center space-x-2"},tn={class:"text-xs text-gray-500"},sn=["onClick"],rn={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},on=["onUpdate:modelValue","onInput"],an=["onUpdate:modelValue","onInput"],nn=["onUpdate:modelValue","onInput"],ln={class:"mt-4"},dn=["onUpdate:modelValue","onInput"],un={class:"mt-4 flex justify-end"},cn=["onClick","disabled"],gn={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},mn={key:1,class:"text-sm text-green-600"},pn={class:"mt-6 pt-4 border-t flex justify-between"},vn={class:"flex space-x-3"},yn=["disabled"],xn={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(p,{emit:z}){const w=p,T=z,$=S(!1),v=S(!1),k=S(null),f=S({}),b=S({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),n=S({budget:80,time:85,margin:15}),j=U(()=>{var g;return!((g=w.project)!=null&&g.budget)||b.value.totalCosts===0?0:Math.round(b.value.totalCosts/w.project.budget*100)}),O=U(()=>{var g;return!((g=w.project)!=null&&g.estimated_hours)||b.value.totalHours===0?0:Math.round(b.value.totalHours/w.project.estimated_hours*100)}),I=U(()=>{const g=b.value.costVariance;return g>0?"text-red-600":g<0?"text-green-600":"text-gray-600"}),A=U(()=>{const g=b.value.marginPercentage;return g>=n.value.margin?"text-green-600":g>=n.value.margin*.7?"text-yellow-600":"text-red-600"}),h=U(()=>{const g=b.value.marginPercentage;return g>=n.value.margin?"Ottimo":g>=n.value.margin*.7?"Accettabile":"Critico"}),q=U(()=>{const g=j.value;return g>=n.value.budget?"text-red-600":g>=n.value.budget*.8?"text-yellow-600":"text-green-600"}),V=U(()=>{const g=O.value;return g>=n.value.time?"text-red-600":g>=n.value.time*.8?"text-yellow-600":"text-green-600"}),P=U(()=>{const g=b.value.marginPercentage;return g>=n.value.margin?"text-green-600":g>=n.value.margin*.7?"text-yellow-600":"text-red-600"}),B=g=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(g||0),d=g=>!g||g===0?"0h":`${parseFloat(g).toFixed(2)}h`,a=g=>`${(g||0).toFixed(1)}%`,_=async()=>{var g;(g=w.project)!=null&&g.id&&D()},D=()=>{const g=w.project;g&&(b.value={totalHours:g.total_hours||0,workDays:Math.ceil((g.total_hours||0)/8),totalCosts:(g.total_hours||0)*50,costVariance:(g.total_hours||0)*50-(g.budget||0),potentialRevenue:g.budget||0,actualRevenue:g.invoiced_amount||0,marginPercentage:g.budget?(g.budget-(g.total_hours||0)*50)/g.budget*100:0})},R=async()=>{$.value=!0;try{await _(),T("refresh")}catch(g){console.error("Error refreshing KPIs:",g)}finally{$.value=!1}},G=U(()=>{var m;const g=((m=w.project)==null?void 0:m.project_type)||"service";return y(g)}),y=g=>{const m={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return m[g]||m.service},t=g=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[g]||"Sconosciuto",u=()=>{G.value.forEach(m=>{f.value[m.name]||(f.value[m.name]={target_min:m.target_min,target_max:m.target_max,warning_threshold:m.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),v.value=!0},l=()=>{v.value=!1},F=g=>{f.value[g]&&(f.value[g].isDirty=!0,f.value[g].isSaved=!1)},c=g=>{const m=G.value.find(Q=>Q.name===g);m&&f.value[g]&&(f.value[g].target_min=m.target_min,f.value[g].target_max=m.target_max,f.value[g].warning_threshold=m.warning_threshold,f.value[g].custom_description="",f.value[g].isDirty=!0,f.value[g].isSaved=!1)},i=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&G.value.forEach(g=>{c(g.name)})},J=async g=>{var m;if(f.value[g]){k.value=g;try{const Q=f.value[g];await new Promise(le=>setTimeout(le,1e3)),console.log("Saving KPI config:",{project_id:(m=w.project)==null?void 0:m.id,kpi_name:g,target_min:Q.target_min,target_max:Q.target_max,warning_threshold:Q.warning_threshold,custom_description:Q.custom_description}),f.value[g].isDirty=!1,f.value[g].isSaved=!0,setTimeout(()=>{f.value[g]&&(f.value[g].isSaved=!1)},3e3)}catch(Q){console.error("Error saving KPI config:",Q),alert("Errore nel salvataggio della configurazione KPI")}finally{k.value=null}}},M=async()=>{const g=G.value.filter(m=>{var Q;return(Q=f.value[m.name])==null?void 0:Q.isDirty});for(const m of g)await J(m.name)},x=U(()=>G.value.some(g=>{var m;return(m=f.value[g.name])==null?void 0:m.isDirty}));return ee(()=>w.project,g=>{g&&_()},{immediate:!0}),re(()=>{w.project&&_()}),(g,m)=>{var Q,le;return s(),r("div",qo,[p.loading?(s(),r("div",No,[e("div",Xo,[(s(),r(L,null,K(4,N=>e("div",{key:N,class:"bg-gray-200 rounded-lg h-24"})),64))]),m[0]||(m[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):p.project?(s(),r("div",Go,[e("div",Jo,[e("div",Wo,[m[3]||(m[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:R,disabled:$.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),r("svg",{class:H(["w-4 h-4 mr-2",{"animate-spin":$.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},m[1]||(m[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),m[2]||(m[2]=W(" Aggiorna "))],8,Yo)])]),e("div",Qo,[e("div",Zo,[e("div",ea,[m[5]||(m[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ta,[e("dl",null,[m[4]||(m[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",sa,o(d(b.value.totalHours)),1),e("dd",ra,o(b.value.workDays)+" giorni lavorati",1)])])])]),e("div",oa,[e("div",aa,[m[7]||(m[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",na,[e("dl",null,[m[6]||(m[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",la,o(B(b.value.totalCosts)),1),e("dd",{class:H(["text-xs",I.value])},o(B(b.value.costVariance))+" vs budget",3)])])])]),e("div",ia,[e("div",da,[m[9]||(m[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",ua,[e("dl",null,[m[8]||(m[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",ca,o(B(b.value.potentialRevenue)),1),e("dd",ga,o(B(b.value.actualRevenue))+" fatturati",1)])])])]),e("div",ma,[e("div",pa,[m[11]||(m[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",va,[e("dl",null,[m[10]||(m[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",ya,o(a(b.value.marginPercentage)),1),e("dd",{class:H(["text-xs",A.value])},o(h.value),3)])])])])]),e("div",xa,[e("div",fa,[m[13]||(m[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",ba,[e("div",ha,[m[12]||(m[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ka,o(B(p.project.budget||0)),1)]),e("div",wa,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:te({width:j.value+"%"})},null,4)]),e("div",_a,[e("span",$a,"Utilizzato: "+o(B(b.value.totalCosts)),1),e("span",ja,o(j.value)+"%",1)])])]),e("div",Ca,[m[15]||(m[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",Ma,[e("div",Ta,[m[14]||(m[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",Sa,o(d(p.project.estimated_hours||0)),1)]),e("div",Pa,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:te({width:O.value+"%"})},null,4)]),e("div",za,[e("span",Aa,"Lavorate: "+o(d(b.value.totalHours)),1),e("span",Da,o(O.value)+"%",1)])])])]),e("div",Ia,[e("div",{class:"flex items-center justify-between mb-4"},[m[17]||(m[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:u,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},m[16]||(m[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),W(" Configura KPI ")]))]),e("div",Va,[e("div",Ea,[e("div",{class:H(["text-2xl font-bold",q.value])},o(j.value)+"% ",3),m[18]||(m[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",Ba,"Soglia: "+o(n.value.budget)+"%",1)]),e("div",Ua,[e("div",{class:H(["text-2xl font-bold",V.value])},o(O.value)+"% ",3),m[19]||(m[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",Ra,"Soglia: "+o(n.value.time)+"%",1)]),e("div",Ha,[e("div",{class:H(["text-2xl font-bold",P.value])},o(a(b.value.marginPercentage)),3),m[20]||(m[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Oa,"Soglia: "+o(n.value.margin)+"%",1)])])])])):(s(),r("div",Fa,m[21]||(m[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),v.value?(s(),r("div",La,[e("div",Ka,[e("div",qa,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[m[23]||(m[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:l,class:"text-gray-400 hover:text-gray-600"},m[22]||(m[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Na,[e("div",Xa,[e("h4",Ga,o((Q=p.project)==null?void 0:Q.name),1),e("p",Ja,"Tipo: "+o(t((le=p.project)==null?void 0:le.project_type)),1)]),e("div",Wa,[(s(!0),r(L,null,K(G.value,N=>{var ce,ge;return s(),r("div",{key:N.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Ya,[e("div",null,[e("h5",Qa,o(N.display_name),1),e("p",Za,o(N.description),1)]),e("div",en,[e("span",tn,o(N.unit),1),e("button",{onClick:Z=>c(N.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,sn)])]),e("div",rn,[e("div",null,[m[24]||(m[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Z=>f.value[N.name].target_min=Z,onInput:Z=>F(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,on),[[X,f.value[N.name].target_min]])]),e("div",null,[m[25]||(m[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Z=>f.value[N.name].target_max=Z,onInput:Z=>F(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,an),[[X,f.value[N.name].target_max]])]),e("div",null,[m[26]||(m[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Z=>f.value[N.name].warning_threshold=Z,onInput:Z=>F(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,nn),[[X,f.value[N.name].warning_threshold]])])]),e("div",ln,[m[27]||(m[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),E(e("textarea",{"onUpdate:modelValue":Z=>f.value[N.name].custom_description=Z,onInput:Z=>F(N.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,dn),[[X,f.value[N.name].custom_description]])]),e("div",un,[(ce=f.value[N.name])!=null&&ce.isDirty?(s(),r("button",{key:0,onClick:Z=>J(N.name),disabled:k.value===N.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[k.value===N.name?(s(),r("svg",gn,m[28]||(m[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):C("",!0),W(" "+o(k.value===N.name?"Salvataggio...":"Salva KPI"),1)],8,cn)):(ge=f.value[N.name])!=null&&ge.isSaved?(s(),r("span",mn,"✓ Salvato")):C("",!0)])])}),128))])]),e("div",pn,[e("button",{onClick:i,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",vn,[e("button",{onClick:l,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:M,disabled:!x.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,yn)])])])])])):C("",!0)])}}},fn={class:"space-y-6"},bn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},hn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},kn={class:"flex items-center justify-between"},wn={class:"flex items-center space-x-4"},_n={class:"flex items-center space-x-2"},$n={key:0,class:"p-6"},jn={class:"overflow-x-auto"},Cn={class:"min-w-[1000px]"},Mn={class:"flex mb-4"},Tn={class:"flex-1 flex"},Sn={class:"space-y-1"},Pn={class:"w-80 flex-shrink-0 px-4 py-3"},zn={class:"flex items-center space-x-2"},An={class:"flex-1 min-w-0"},Dn={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},In={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},Vn={key:0},En={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Bn={class:"flex-1 relative h-12 flex items-center"},Un=["title"],Rn={class:"truncate"},Hn={class:"ml-2"},On={key:1,class:"text-center py-12"},Fn={key:2,class:"flex justify-center py-12"},Ln={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(p,{expose:z}){const w=p,T=S("weeks"),$=S(new Date),v=S([]),k=U(()=>{var d;return((d=w.project)==null?void 0:d.tasks)||[]}),f=U(()=>k.value.filter(d=>d.start_date&&d.due_date).map(d=>{const a=n(d);return{...d,timeline:a}})),b=()=>{const d=new Date($.value),a=[],_=12;for(let D=0;D<_;D++){const R=new Date(d);T.value==="weeks"?R.setDate(d.getDate()+D*7):T.value==="months"&&R.setMonth(d.getMonth()+D),a.push(R)}v.value=a},n=d=>{if(!v.value.length)return null;const a=new Date(d.start_date),_=new Date(d.due_date),D=v.value[0],G=v.value[v.value.length-1]-D,y=a-D,t=_-a,u=Math.max(0,y/G*100),l=Math.min(100-u,t/G*100);return{leftPercent:u,widthPercent:Math.max(5,l)}},j=d=>T.value==="weeks"?`${d.getDate()}/${d.getMonth()+1}`:T.value==="months"?d.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",O=d=>{const a=new Date,_=new Date(d);if(T.value==="weeks"){const D=new Date(_),R=new Date(_);return R.setDate(R.getDate()+6),a>=D&&a<=R}else if(T.value==="months")return _.getMonth()===a.getMonth()&&_.getFullYear()===a.getFullYear();return!1},I=()=>{const d=new Date;if(T.value==="weeks"){const a=new Date(d);a.setDate(d.getDate()-d.getDay()),$.value=a}else{const a=new Date(d.getFullYear(),d.getMonth(),1);$.value=a}b()},A=d=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[d]||"bg-gray-400",h=d=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[d]||"bg-gray-500",q=d=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[d]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",V=d=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[d]||"Non specificata",P=d=>({todo:0,"in-progress":50,review:75,done:100})[d.status]||0,B=d=>d?new Date(d).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return ee(()=>w.project,()=>{b()},{immediate:!0}),re(()=>{I()}),z({refresh:b}),(d,a)=>(s(),r("div",fn,[e("div",bn,[e("div",hn,[e("div",kn,[a[3]||(a[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",wn,[e("div",_n,[a[2]||(a[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":a[0]||(a[0]=_=>T.value=_),onChange:b,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},a[1]||(a[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[Y,T.value]])]),e("button",{onClick:I,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!p.loading&&f.value.length>0?(s(),r("div",$n,[e("div",jn,[e("div",Cn,[e("div",Mn,[a[4]||(a[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",Tn,[(s(!0),r(L,null,K(v.value,(_,D)=>(s(),r("div",{key:D,class:H(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":O(_)}])},o(j(_)),3))),128))])]),e("div",Sn,[(s(!0),r(L,null,K(f.value,_=>(s(),r("div",{key:_.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",Pn,[e("div",zn,[e("div",{class:H(["w-3 h-3 rounded-full",A(_.status)])},null,2),e("div",An,[e("p",Dn,o(_.name),1),e("div",In,[_.assignee?(s(),r("span",Vn,o(_.assignee.full_name),1)):C("",!0),e("span",{class:H(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",q(_.priority)])},o(V(_.priority)),3)]),e("div",En,o(B(_.start_date))+" - "+o(B(_.due_date)),1)])])]),e("div",Bn,[_.timeline?(s(),r("div",{key:0,class:H(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",h(_.status)]),style:te({left:_.timeline.leftPercent+"%",width:_.timeline.widthPercent+"%",minWidth:"60px"}),title:`${_.name} - ${P(_)}% completato`},[e("span",Rn,o(_.name.length>15?_.name.substring(0,15)+"...":_.name),1),e("span",Hn,o(P(_))+"%",1)],14,Un)):C("",!0),_.timeline&&P(_)>0&&P(_)<100?(s(),r("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:te({left:_.timeline.leftPercent+"%",width:_.timeline.widthPercent*P(_)/100+"%",minWidth:"2px"})},null,4)):C("",!0),(s(!0),r(L,null,K(v.value,(D,R)=>(s(),r("div",{key:R,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:te({left:R/v.value.length*100+"%"})},null,4))),128))])]))),128))])])]),a[5]||(a[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):p.loading?C("",!0):(s(),r("div",On,a[6]||(a[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),p.loading?(s(),r("div",Fn,a[7]||(a[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):C("",!0)])]))}},Kn={class:"space-y-6"},qn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Nn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Xn={class:"flex items-center justify-between"},Gn={class:"flex items-center space-x-4"},Jn={class:"flex items-center space-x-2"},Wn={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},Yn={class:"flex items-center space-x-2"},Qn=["value"],Zn={key:0,class:"flex justify-center py-8"},el={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},tl={class:"text-red-600"},sl={key:2,class:"p-6"},rl={class:"overflow-x-auto"},ol={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},al={class:"bg-gray-50 dark:bg-gray-700"},nl={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ll={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},il={class:"text-sm font-medium text-gray-900 dark:text-white"},dl={class:"text-xs text-gray-500 dark:text-gray-400"},ul=["onClick"],cl={key:0,class:"flex flex-col items-center"},gl={class:"text-xs font-medium text-primary-600 dark:text-primary-400"},ml={key:0,class:"flex space-x-1 mt-1"},pl=["title"],vl={key:1,class:"text-gray-300 dark:text-gray-600"},yl={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},xl={class:"text-sm font-medium text-gray-900 dark:text-white"},fl={class:"bg-gray-100 dark:bg-gray-600 font-medium"},bl={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},hl={key:0,class:"text-center py-8"},kl={key:1,class:"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},wl={class:"mt-3"},_l={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},$l={class:"grid grid-cols-1 gap-4"},jl=["value"],Cl={class:"border-t border-gray-200 dark:border-gray-600 pt-4"},Ml={class:"text-xs text-red-500 mb-2"},Tl={key:0,class:"mt-2"},Sl={class:"grid grid-cols-2 gap-4"},Pl={key:0},zl=["placeholder"],Al={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Dl={class:"flex justify-end space-x-3 mt-6"},Il=["disabled"],Vl={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(p,{expose:z}){const w=p,T=ne(),$=S(null),v=S(!1),k=S(""),f=S(!1),b=S(new Date().getFullYear()),n=S(new Date().getMonth()+1),j=S(""),O=S(!1),I=S(!1),A=S(null),h=S({task_id:"",date:"",hours:0,description:"",billable:!0,billing_rate:null}),q=U(()=>$.value?Array.from({length:$.value.days_in_month},(y,t)=>t+1):[]),V=async()=>{var y;if((y=w.project)!=null&&y.id){v.value=!0,k.value="";try{const t=new URLSearchParams({year:b.value.toString(),month:n.value.toString()});j.value&&t.append("member_id",j.value.toString());const u=await fetch(`/api/timesheets/project/${w.project.id}/monthly?${t}`,{headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken}});if(!u.ok)throw new Error("Errore nel caricamento del timesheet");const l=await u.json();$.value=l.data}catch(t){k.value=t.message}finally{v.value=!1}}},P=async()=>{f.value=!0;try{const y={...h.value,project_id:w.project.id};if(!(await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":T.csrfToken},body:JSON.stringify(y)})).ok)throw new Error("Errore nel salvataggio del timesheet");await V(),d()}catch(y){k.value=y.message}finally{f.value=!1}},B=(y,t)=>{var F,c,i;const u=$.value.tasks.find(J=>J.id===y);if(!u)return;A.value={taskId:y,day:t};const l=((c=(F=w.project)==null?void 0:F.contract)==null?void 0:c.hourly_rate)||null;h.value={task_id:y,date:`${b.value}-${String(n.value).padStart(2,"0")}-${String(t).padStart(2,"0")}`,hours:u.daily_hours[t]||0,description:"",billable:!!((i=w.project)!=null&&i.contract),billing_rate:l},u.daily_hours[t]>0?I.value=!0:O.value=!0},d=()=>{O.value=!1,I.value=!1,A.value=null,h.value={task_id:"",date:"",hours:0,description:"",billable:!0,billing_rate:null}},a=()=>{n.value===1?(n.value=12,b.value--):n.value--,V()},_=()=>{n.value===12?(n.value=1,b.value++):n.value++,V()},D=y=>{const t=new Date;return t.getFullYear()===b.value&&t.getMonth()+1===n.value&&t.getDate()===y},R=y=>!y||y===0?"0":y%1===0?y.toString():y.toFixed(2),G=y=>y?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(y):"€0";return ee(()=>{var y;return(y=w.project)==null?void 0:y.id},y=>{y&&V()}),ee(j,()=>{V()}),re(()=>{var y;(y=w.project)!=null&&y.id&&V()}),z({refresh:V}),(y,t)=>{var u,l,F,c,i,J;return s(),r("div",Kn,[e("div",qn,[e("div",Nn,[e("div",Xn,[e("div",Gn,[t[13]||(t[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Jn,[e("button",{onClick:a,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},t[9]||(t[9]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",Wn,o(n.value)+"/"+o(b.value),1),e("button",{onClick:_,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},t[10]||(t[10]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",Yn,[t[12]||(t[12]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":t[0]||(t[0]=M=>j.value=M),onChange:V,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[11]||(t[11]=e("option",{value:""},"Tutti i membri",-1)),(s(!0),r(L,null,K(((u=p.project)==null?void 0:u.team_members)||[],M=>(s(),r("option",{key:M.id,value:M.id},o(M.first_name)+" "+o(M.last_name),9,Qn))),128))],544),[[Y,j.value]])])]),e("button",{onClick:t[1]||(t[1]=M=>O.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},t[14]||(t[14]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),W(" Aggiungi Ore ")]))])]),v.value?(s(),r("div",Zn,t[15]||(t[15]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):C("",!0),k.value?(s(),r("div",el,[e("p",tl,o(k.value),1)])):C("",!0),!v.value&&$.value?(s(),r("div",sl,[e("div",rl,[e("table",ol,[e("thead",al,[e("tr",null,[t[16]||(t[16]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(s(!0),r(L,null,K(q.value,M=>(s(),r("th",{key:M,class:H(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":D(M)}])},o(M),3))),128)),t[17]||(t[17]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",nl,[(s(!0),r(L,null,K($.value.tasks,M=>(s(),r("tr",{key:M.id},[e("td",ll,[e("div",il,o(M.name),1),e("div",dl,o(M.workers.length?M.workers.join(", "):"Nessuno ha lavorato"),1)]),(s(!0),r(L,null,K(q.value,x=>{var g;return s(),r("td",{key:x,class:H(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":D(x)}]),onClick:m=>B(M.id,x)},[M.daily_hours[x]>0?(s(),r("div",cl,[e("span",gl,o(R(M.daily_hours[x])),1),(g=p.project)!=null&&g.contract?(s(),r("div",ml,[e("div",{class:H(["w-1.5 h-1.5 rounded-full",M.daily_billing&&M.daily_billing[x]?"bg-green-500":"bg-gray-300"]),title:M.daily_billing&&M.daily_billing[x]?"Fatturabile":"Non fatturabile"},null,10,pl)])):C("",!0)])):(s(),r("span",vl,"-"))],10,ul)}),128)),e("td",yl,[e("span",xl,o(R(M.total_hours)),1)])]))),128)),e("tr",fl,[t[18]||(t[18]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(s(!0),r(L,null,K(q.value,M=>(s(),r("td",{key:M,class:H(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":D(M)}])},o(R($.value.daily_totals[M]||0)),3))),128)),e("td",bl,o(R($.value.grand_total)),1)])])])]),$.value.tasks.length===0?(s(),r("div",hl,t[19]||(t[19]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):C("",!0),(l=p.project)!=null&&l.contract&&$.value.tasks.length>0?(s(),r("div",kl,t[20]||(t[20]=[ae('<div class="flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-300"><span class="font-medium">Legenda:</span><div class="flex items-center space-x-1"><div class="w-1.5 h-1.5 rounded-full bg-green-500"></div><span>Fatturabile</span></div><div class="flex items-center space-x-1"><div class="w-1.5 h-1.5 rounded-full bg-gray-300"></div><span>Non fatturabile</span></div></div>',1)]))):C("",!0)])):C("",!0)]),O.value||I.value?(s(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:d},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[8]||(t[8]=se(()=>{},["stop"]))},[e("div",wl,[e("h3",_l,o(I.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:se(P,["prevent"])},[e("div",$l,[e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),E(e("select",{"onUpdate:modelValue":t[2]||(t[2]=M=>h.value.task_id=M),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[21]||(t[21]=e("option",{value:""},"Seleziona task",-1)),(s(!0),r(L,null,K(((F=$.value)==null?void 0:F.tasks)||[],M=>(s(),r("option",{key:M.id,value:M.id},o(M.name),9,jl))),128))],512),[[Y,h.value.task_id]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),E(e("input",{"onUpdate:modelValue":t[3]||(t[3]=M=>h.value.date=M),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.date]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),E(e("input",{"onUpdate:modelValue":t[4]||(t[4]=M=>h.value.hours=M),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.hours]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":t[5]||(t[5]=M=>h.value.description=M),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.description]])]),e("div",Cl,[e("div",Ml," DEBUG: project?.contract = "+o(!!((c=p.project)!=null&&c.contract))+" | contract_id = "+o((i=p.project)==null?void 0:i.contract_id),1),(J=p.project)!=null&&J.contract?(s(),r("div",Tl,[t[29]||(t[29]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Informazioni Fatturazione",-1)),e("div",Sl,[e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fatturabile ",-1)),E(e("select",{"onUpdate:modelValue":t[6]||(t[6]=M=>h.value.billable=M),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[26]||(t[26]=[e("option",{value:!0},"Sì - Fatturabile",-1),e("option",{value:!1},"No - Interno",-1)]),512),[[Y,h.value.billable]])]),h.value.billable?(s(),r("div",Pl,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tariffa (€/h) ",-1)),E(e("input",{"onUpdate:modelValue":t[7]||(t[7]=M=>h.value.billing_rate=M),type:"number",step:"0.01",min:"0",placeholder:p.project.contract.hourly_rate,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,8,zl),[[X,h.value.billing_rate]]),e("p",Al," Tariffa contrattuale: "+o(G(p.project.contract.hourly_rate))+"/h ",1)])):C("",!0)])])):C("",!0)])]),e("div",Dl,[e("button",{type:"button",onClick:d,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:f.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(f.value?"Salvataggio...":I.value?"Aggiorna":"Aggiungi"),9,Il)])],32)])])])):C("",!0)])}}},El={class:"space-y-6"},Bl={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Ul={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Rl={class:"flex items-center justify-between"},Hl={class:"flex items-center space-x-3"},Ol=["disabled"],Fl={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Ll={key:1,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},Kl={key:0,class:"px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700"},ql={class:"flex items-start space-x-3"},Nl={class:"flex-1"},Xl={class:"text-sm font-medium text-purple-900 dark:text-purple-100"},Gl={class:"mt-2 space-y-2"},Jl={key:0,class:"mt-3"},Wl={class:"space-y-2"},Yl={class:"flex items-center space-x-3"},Ql={class:"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center"},Zl={class:"text-xs font-medium text-purple-600 dark:text-purple-300"},ei={class:"text-sm font-medium text-gray-900 dark:text-white"},ti={class:"text-xs text-gray-500 dark:text-gray-400"},si=["onClick"],ri={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},oi={key:0,class:"p-6"},ai={class:"animate-pulse space-y-4"},ni={key:1,class:"p-6 text-center"},li={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},ii={class:"flex items-center justify-between"},di={class:"flex items-center space-x-4"},ui={class:"w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center"},ci={class:"text-sm font-medium text-primary-600 dark:text-primary-300"},gi={class:"text-sm font-medium text-gray-900 dark:text-white"},mi={class:"text-sm text-gray-500 dark:text-gray-400"},pi={class:"flex items-center space-x-4"},vi={class:"text-right"},yi={class:"text-sm font-medium text-gray-900 dark:text-white"},xi={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2"},fi={class:"flex items-center space-x-2"},bi=["onClick"],hi=["onClick"],ki={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},wi={class:"p-6"},_i={class:"space-y-4"},$i={class:"w-32 text-sm text-gray-600 dark:text-gray-400"},ji={class:"flex-1 mx-4"},Ci={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3"},Mi={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ti={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Si={class:"mt-3"},Pi={class:"space-y-4"},zi=["value"],Ai={class:"flex justify-end space-x-3 mt-6"},Di=["disabled"],Ii={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Vi={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Ei={class:"mt-3"},Bi={class:"space-y-4"},Ui={class:"flex justify-end space-x-3 mt-6"},Ri=["disabled"],Hi={__name:"ProjectResourceAllocation",props:{project:{type:Object,required:!0}},setup(p){const z=p,w=ne(),T=S(!0),$=S(!1),v=S(!1),k=S([]),f=S([]),b=S([]),n=S(null),j=S(!1),O=S(!1),I=S({user_id:"",role:"",allocation_percentage:100}),A=S({id:null,role:"",allocation_percentage:100}),h=U(()=>{var u;return(u=z.project)==null?void 0:u.id}),q=async()=>{var u;if(h.value){T.value=!0;try{const l=await fetch(`/api/resources?project_id=${h.value}`,{headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken}});if(!l.ok)throw new Error("Errore nel caricamento allocazioni");const F=await l.json();k.value=((u=F.data)==null?void 0:u.resources)||[],await P()}catch(l){console.error("Error loading allocations:",l)}finally{T.value=!1}}},V=async()=>{var u;try{const l=await fetch("/api/personnel",{headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken}});if(!l.ok)throw new Error("Errore nel caricamento utenti");const F=await l.json();f.value=((u=F.data)==null?void 0:u.users)||[]}catch(l){console.error("Error loading users:",l)}},P=async()=>{b.value=k.value.map(u=>({user_id:u.user_id,user_name:u.user_name,total_allocation:u.allocation_percentage+Math.floor(Math.random()*30)}))},B=async()=>{var u;if(h.value){v.value=!0;try{const l=await fetch(`/api/ai-resources/analyze-allocation/${h.value}`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({include_suggestions:!0,analysis_depth:"detailed"})});if(!l.ok)throw new Error("Errore nell'analisi AI");const F=await l.json();n.value=((u=F.data)==null?void 0:u.analysis)||null}catch(l){console.error("Error in AI analysis:",l),alert("Errore nell'analisi AI: "+l.message)}finally{v.value=!1}}},d=async()=>{$.value=!0;try{if(!(await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({project_id:h.value,...I.value})})).ok)throw new Error("Errore nell'aggiunta risorsa");await q(),j.value=!1,I.value={user_id:"",role:"",allocation_percentage:100}}catch(u){console.error("Error adding resource:",u),alert("Errore nell'aggiunta risorsa: "+u.message)}finally{$.value=!1}},a=u=>{A.value={id:u.id,role:u.role,allocation_percentage:u.allocation_percentage},O.value=!0},_=async()=>{$.value=!0;try{if(!(await fetch(`/api/resources/${A.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({role:A.value.role,allocation_percentage:A.value.allocation_percentage})})).ok)throw new Error("Errore nell'aggiornamento allocazione");await q(),O.value=!1,A.value={id:null,role:"",allocation_percentage:100}}catch(u){console.error("Error updating allocation:",u),alert("Errore nell'aggiornamento: "+u.message)}finally{$.value=!1}},D=async u=>{if(confirm("Sei sicuro di voler rimuovere questa allocazione?"))try{if(!(await fetch(`/api/resources/${u.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken}})).ok)throw new Error("Errore nella rimozione");await q()}catch(l){console.error("Error removing allocation:",l),alert("Errore nella rimozione: "+l.message)}},R=async u=>{try{await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({project_id:h.value,user_id:u.user_id,role:u.role,allocation_percentage:u.allocation})}),await q()}catch(l){console.error("Error applying AI recommendation:",l)}},G=u=>u>=80?"bg-red-500":u>=60?"bg-yellow-500":"bg-green-500",y=u=>u>100?"bg-red-500":u>=90?"bg-yellow-500":"bg-green-500",t=u=>u>100?"text-red-600 dark:text-red-400":u>=90?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400";return ee(()=>z.project,u=>{u&&q()},{immediate:!0}),re(()=>{V()}),(u,l)=>{var F;return s(),r("div",El,[e("div",Bl,[e("div",Ul,[e("div",Rl,[l[12]||(l[12]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazione Risorse "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci l'allocazione delle risorse con assistenza AI ")],-1)),e("div",Hl,[e("button",{onClick:B,disabled:v.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"},[v.value?(s(),r("svg",Ll,l[10]||(l[10]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(s(),r("svg",Fl,l[9]||(l[9]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},null,-1)]))),W(" "+o(v.value?"Analizzando...":"Analisi AI"),1)],8,Ol),e("button",{onClick:l[0]||(l[0]=c=>j.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},l[11]||(l[11]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),W(" Aggiungi Risorsa ")]))])])]),n.value?(s(),r("div",Kl,[e("div",ql,[l[15]||(l[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])],-1)),e("div",Nl,[e("h4",Xl," Insights AI - Efficienza: "+o(n.value.efficiency_score)+"% ",1),e("div",Gl,[(s(!0),r(L,null,K(n.value.optimization_insights,c=>(s(),r("div",{key:c,class:"text-sm text-purple-700 dark:text-purple-300"}," • "+o(c),1))),128))]),(F=n.value.recommended_allocations)!=null&&F.length?(s(),r("div",Jl,[l[13]||(l[13]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"}," Raccomandazioni AI: ",-1)),e("div",Wl,[(s(!0),r(L,null,K(n.value.recommended_allocations,c=>{var i;return s(),r("div",{key:c.user_id,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3"},[e("div",Yl,[e("div",Ql,[e("span",Zl,o((i=c.user_name)==null?void 0:i.charAt(0)),1)]),e("div",null,[e("p",ei,o(c.user_name),1),e("p",ti,o(c.role)+" - "+o(c.allocation)+"%",1)])]),e("button",{onClick:J=>R(c),class:"text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700"}," Applica ",8,si)])}),128))])])):C("",!0)]),e("button",{onClick:l[1]||(l[1]=c=>n.value=null),class:"flex-shrink-0 text-purple-400 hover:text-purple-600"},l[14]||(l[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])):C("",!0)]),e("div",ri,[l[20]||(l[20]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazioni Attuali ")],-1)),T.value?(s(),r("div",oi,[e("div",ai,[(s(),r(L,null,K(3,c=>e("div",{key:c,class:"flex items-center space-x-4"},l[16]||(l[16]=[ae('<div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div><div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div></div><div class="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>',3)]))),64))])])):k.value.length?(s(),r("div",li,[(s(!0),r(L,null,K(k.value,c=>{var i;return s(),r("div",{key:c.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ii,[e("div",di,[e("div",ui,[e("span",ci,o((i=c.user_name)==null?void 0:i.charAt(0)),1)]),e("div",null,[e("h4",gi,o(c.user_name),1),e("p",mi,o(c.role||"Team Member"),1)])]),e("div",pi,[e("div",vi,[e("div",yi,o(c.allocation_percentage)+"% ",1),e("div",xi,[e("div",{class:H(["h-2 rounded-full",G(c.allocation_percentage)]),style:te({width:c.allocation_percentage+"%"})},null,6)])]),e("div",fi,[e("button",{onClick:J=>a(c),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},l[18]||(l[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,bi),e("button",{onClick:J=>D(c),class:"text-red-400 hover:text-red-600"},l[19]||(l[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,hi)])])])])}),128))])):(s(),r("div",ni,l[17]||(l[17]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna risorsa allocata",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti. ",-1)])))]),e("div",ki,[l[21]||(l[21]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Utilizzo Risorse ")],-1)),e("div",wi,[e("div",_i,[(s(!0),r(L,null,K(b.value,c=>(s(),r("div",{key:c.user_id,class:"flex items-center"},[e("div",$i,o(c.user_name),1),e("div",ji,[e("div",Ci,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",y(c.total_allocation)]),style:te({width:Math.min(c.total_allocation,100)+"%"})},null,6)])]),e("div",{class:H(["w-16 text-sm text-right font-medium",t(c.total_allocation)])},o(c.total_allocation)+"% ",3)]))),128))])])]),j.value?(s(),r("div",Mi,[e("div",Ti,[e("div",Si,[l[26]||(l[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Risorsa ",-1)),e("form",{onSubmit:se(d,["prevent"])},[e("div",Pi,[e("div",null,[l[23]||(l[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Utente ",-1)),E(e("select",{"onUpdate:modelValue":l[2]||(l[2]=c=>I.value.user_id=c),required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[l[22]||(l[22]=e("option",{value:""},"Seleziona utente...",-1)),(s(!0),r(L,null,K(f.value,c=>(s(),r("option",{key:c.id,value:c.id},o(c.full_name)+" ("+o(c.role)+") ",9,zi))),128))],512),[[Y,I.value.user_id]])]),e("div",null,[l[24]||(l[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":l[3]||(l[3]=c=>I.value.role=c),type:"text",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[X,I.value.role]])]),e("div",null,[l[25]||(l[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":l[4]||(l[4]=c=>I.value.allocation_percentage=c),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,I.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",Ai,[e("button",{type:"button",onClick:l[5]||(l[5]=c=>j.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:$.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},o($.value?"Salvando...":"Aggiungi"),9,Di)])],32)])])])):C("",!0),O.value?(s(),r("div",Ii,[e("div",Vi,[e("div",Ei,[l[29]||(l[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Modifica Allocazione ",-1)),e("form",{onSubmit:se(_,["prevent"])},[e("div",Bi,[e("div",null,[l[27]||(l[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":l[6]||(l[6]=c=>A.value.role=c),type:"text",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[X,A.value.role]])]),e("div",null,[l[28]||(l[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":l[7]||(l[7]=c=>A.value.allocation_percentage=c),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,A.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",Ui,[e("button",{type:"button",onClick:l[8]||(l[8]=c=>O.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:$.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},o($.value?"Salvando...":"Aggiorna"),9,Ri)])],32)])])])):C("",!0)])}}},Oi={class:"project-view"},Fi={class:"tab-content"},Li={__name:"ProjectView",setup(p){const z=fe(),w=ne(),T=he(),$=we(),v=S(!0),k=S("overview"),f=U(()=>z.currentProject),b=U(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"resources",label:"Allocazione Risorse",icon:"user-group"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(h=>!!(["overview","tasks","gantt","team","timesheet","resources"].includes(h.id)||h.id==="kpi"&&w.hasPermission("view_reports")||h.id==="expenses"&&w.hasPermission("manage_expenses")))),n=U(()=>({overview:me,tasks:nr,team:to,resources:Hi,expenses:Ko,kpi:xn,gantt:Ln,timesheet:Vl})[k.value]||me),j=async()=>{v.value=!0;try{const A=T.params.id;await z.fetchProject(A)}catch(A){console.error("Error loading project:",A)}finally{v.value=!1}},O=()=>{$.push(`/projects/${T.params.id}/edit`)},I=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await z.deleteProject(T.params.id),$.push("/projects")}catch(A){console.error("Error deleting project:",A)}};return ee(()=>T.params.id,(A,h)=>{A&&A!==h&&j()}),ee(()=>T.hash,A=>{if(A){const h=A.replace("#","");b.value.find(q=>q.id===h)&&k.value!==h&&(k.value=h)}},{immediate:!0}),ee(k,A=>{const h=`#${A}`;T.hash!==h&&$.replace({...T,hash:h})}),re(()=>{if(T.hash){const A=T.hash.replace("#","");b.value.find(h=>h.id===A)&&(k.value=A)}j()}),(A,h)=>(s(),r("div",Oi,[oe(Ue,{project:f.value,loading:v.value,onEdit:O,onDelete:I},null,8,["project","loading"]),oe(qe,{modelValue:k.value,"onUpdate:modelValue":h[0]||(h[0]=q=>k.value=q),tabs:b.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",Fi,[(s(),de(ke,null,[(s(),de(xe(n.value),{project:f.value,loading:v.value},null,8,["project","loading"]))],1024))])]))}},Ni=ue(Li,[["__scopeId","data-v-de1f32e3"]]);export{Ni as default};
