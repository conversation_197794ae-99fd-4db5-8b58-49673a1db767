import{c as s,o as t,j as e,t as o,n as F,g as S,m as G,a as oe,i as ve,b as xe,F as L,k as K,h as de,M as ye,f as U,z as ee,r as M,w as Z,A as se,v as E,G as J,H as ae,x as X,s as te,N as be,p as le,u as he,O as ke,l as we}from"./vendor.js";import{_ as ue,u as ne,a as _e,b as fe}from"./app.js";const $e={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},je={key:0,class:"animate-pulse"},Ce={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Me={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Se={class:"text-2xl font-bold text-gray-900"},Pe={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},Ae={key:0},ze={key:1},De={key:2},Ie={key:3},Ve={class:"mt-4 sm:mt-0 flex space-x-3"},Ee={key:2,class:"text-center py-8"},Be={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(x){const P=v=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[v]||"bg-gray-100 text-gray-800",_=v=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[v]||v,C=v=>v?new Date(v).toLocaleDateString("it-IT"):"",$=v=>v?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(v):"";return(v,b)=>{const y=xe("router-link");return t(),s("div",$e,[x.loading?(t(),s("div",je,b[1]||(b[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):x.project?(t(),s("div",Ce,[e("div",Me,[e("div",Te,[e("h1",Se,o(x.project.name),1),e("span",{class:F(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",P(x.project.status)])},o(_(x.project.status)),3)]),e("div",Pe,[x.project.client?(t(),s("span",Ae,[b[2]||(b[2]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),G(" Cliente: "+o(x.project.client.name),1)])):S("",!0),x.project.start_date?(t(),s("span",ze,[b[3]||(b[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),G(" Inizio: "+o(C(x.project.start_date)),1)])):S("",!0),x.project.end_date?(t(),s("span",De,[b[4]||(b[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),G(" Fine: "+o(C(x.project.end_date)),1)])):S("",!0),x.project.budget?(t(),s("span",Ie,[b[5]||(b[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),G(" Budget: "+o($(x.project.budget)),1)])):S("",!0)])]),e("div",Ve,[oe(y,{to:`/app/projects/${x.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:ve(()=>b[6]||(b[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),G(" Modifica ")])),_:1,__:[6]},8,["to"]),e("button",{onClick:b[0]||(b[0]=f=>v.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},b[7]||(b[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),G(" Elimina ")]))])])):(t(),s("div",Ee,b[8]||(b[8]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Ue=ue(Be,[["__scopeId","data-v-6f1b5cc9"]]),Re={class:"tab-navigation"},He={class:"border-b border-gray-200"},Oe={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Fe=["onClick","aria-current"],Le={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},Ke={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:x=>x.every(P=>typeof P=="object"&&P.id&&P.label)}},emits:["update:modelValue"],setup(x,{emit:P}){const _=x,C=P,$=y=>_.modelValue===y,v=y=>{C("update:modelValue",y)},b=y=>{const f={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return f[y]||f["chart-bar"]};return(y,f)=>(t(),s("div",Re,[e("div",He,[e("nav",Oe,[(t(!0),s(L,null,K(x.tabs,n=>(t(),s("button",{key:n.id,onClick:j=>v(n.id),class:F(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",$(n.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":$(n.id)?"page":void 0},[n.icon?(t(),de(ye(b(n.icon)),{key:0,class:"w-4 h-4"})):S("",!0),e("span",null,o(n.label),1),n.count!==void 0?(t(),s("span",Le,o(n.count),1)):S("",!0)],10,Fe))),128))])])]))}},qe=ue(Ke,[["__scopeId","data-v-c205976e"]]),Ne={class:"project-overview"},Xe={key:0,class:"animate-pulse space-y-4"},Ge={key:1,class:"space-y-6"},Je={class:"bg-white shadow rounded-lg p-6"},We={key:0,class:"text-gray-600"},Ye={key:1,class:"text-gray-400 italic"},Qe={key:0,class:"bg-white shadow rounded-lg p-6"},Ze={class:"flex items-center justify-between mb-4"},et={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},tt={class:"text-sm font-medium text-gray-900"},st={class:"text-sm font-medium text-gray-900"},rt={class:"text-sm font-medium text-gray-900"},ot={key:0,class:"mt-4 pt-4 border-t border-gray-200"},at={class:"grid grid-cols-2 gap-4"},nt={key:0},it={class:"text-sm font-medium text-gray-900"},lt={key:1},dt={class:"text-sm font-medium text-gray-900"},ut={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},ct={class:"bg-white shadow rounded-lg p-6"},gt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white shadow rounded-lg p-6"},xt={class:"flex items-center"},yt={class:"ml-5 w-0 flex-1"},ft={class:"text-lg font-medium text-gray-900"},bt={class:"bg-white shadow rounded-lg p-6"},ht={class:"flex items-center"},kt={class:"ml-5 w-0 flex-1"},wt={class:"text-lg font-medium text-gray-900"},_t={class:"bg-white shadow rounded-lg p-6"},$t={class:"flex items-center"},jt={class:"ml-5 w-0 flex-1"},Ct={class:"text-lg font-medium text-gray-900"},Mt={class:"bg-white shadow rounded-lg p-6"},Tt={class:"w-full bg-gray-200 rounded-full h-2.5"},St={class:"text-sm text-gray-500 mt-2"},Pt={class:"bg-white shadow rounded-lg p-6"},At={class:"space-y-4"},zt={class:"flex justify-between items-center"},Dt={class:"text-sm font-medium"},It={class:"flex justify-between items-center"},Vt={class:"text-sm font-medium"},Et={class:"w-full bg-gray-200 rounded-full h-3"},Bt={class:"flex justify-between items-center text-sm"},Ut={class:"bg-white shadow rounded-lg p-6"},Rt={class:"space-y-3"},Ht={class:"flex-shrink-0"},Ot=["src","alt"],Ft={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},Lt={class:"text-xs font-medium text-gray-600"},Kt={class:"flex-1"},qt={class:"text-sm font-medium text-gray-900"},Nt={class:"text-xs text-gray-500"},Xt={class:"text-right"},Gt={class:"text-xs text-gray-500"},Jt={key:0,class:"text-center py-4"},Wt={class:"bg-white shadow rounded-lg p-6"},Yt={class:"space-y-3"},Qt={class:"flex-shrink-0"},Zt={class:"flex-1"},es={class:"text-sm text-gray-900"},ts={class:"flex items-center space-x-2 mt-1"},ss={class:"text-xs text-gray-500"},rs={class:"text-xs text-gray-500"},os={key:0,class:"text-center py-4"},as={key:2,class:"text-center py-8"},ns={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x){const P=x,_=U(()=>{if(!P.project||!P.project.task_count)return 0;const l=P.project.completed_tasks||0,r=P.project.task_count||1;return Math.round(l/r*100)}),C=U(()=>{var l;return((l=P.project)==null?void 0:l.team_members)||[]}),$=U(()=>{var w,D,R;if((w=P.project)!=null&&w.expenses)return P.project.expenses;const l=((D=P.project)==null?void 0:D.total_hours)||0,r=(R=P.project)!=null&&R.client_daily_rate?P.project.client_daily_rate/8:50;return l*r}),v=U(()=>{var r;return(((r=P.project)==null?void 0:r.budget)||0)-$.value}),b=U(()=>{var r;const l=((r=P.project)==null?void 0:r.budget)||1;return Math.min(Math.round($.value/l*100),100)}),y=U(()=>{const l=b.value;return l>=90?"bg-red-600":l>=75?"bg-yellow-600":"bg-green-600"}),f=U(()=>{var r;const l=v.value;return l<0?"text-red-600":l<(((r=P.project)==null?void 0:r.budget)||0)*.1?"text-yellow-600":"text-green-600"}),n=U(()=>{var l;return(l=P.project)!=null&&l.tasks?[...P.project.tasks].sort((r,w)=>new Date(w.updated_at)-new Date(r.updated_at)).slice(0,5).map(r=>{var w;return{id:r.id,description:`Task "${r.name}" ${j(r.status)}`,created_at:r.updated_at,user_name:((w=r.assignee)==null?void 0:w.full_name)||"Non assegnato",type:H(r.status)}}):[]}),j=l=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[l]||l,H=l=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[l]||"task_updated",I=l=>l?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(l):"Non specificato",A=l=>l?new Date(l).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",h=l=>l?l.split(" ").map(r=>r.charAt(0).toUpperCase()).slice(0,2).join(""):"??",q=l=>{const r={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return r[l]||r.default},V=l=>({fixed_price:"Prezzo Fisso",time_and_materials:"Tempo e Materiali",retainer:"Retainer",milestone:"Milestone"})[l]||l,T=l=>({draft:"Bozza",sent:"Inviato",signed:"Firmato",active:"Attivo",completed:"Completato",cancelled:"Annullato"})[l]||l,B=l=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",signed:"bg-green-100 text-green-800",active:"bg-green-100 text-green-800",completed:"bg-purple-100 text-purple-800",cancelled:"bg-red-100 text-red-800"})[l]||"bg-gray-100 text-gray-800";return(l,r)=>{const w=xe("router-link");return t(),s("div",Ne,[x.loading?(t(),s("div",Xe,r[0]||(r[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):x.project?(t(),s("div",Ge,[e("div",Je,[r[1]||(r[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),x.project.description?(t(),s("p",We,o(x.project.description),1)):(t(),s("p",Ye,"Nessuna descrizione disponibile"))]),x.project.contract?(t(),s("div",Qe,[e("div",Ze,[r[3]||(r[3]=e("h3",{class:"text-lg font-medium text-gray-900"},"Contratto Collegato",-1)),oe(w,{to:`/app/crm/contracts/${x.project.contract.id}`,class:"text-blue-600 hover:text-blue-800 text-sm font-medium"},{default:ve(()=>r[2]||(r[2]=[G(" Gestisci Contratto → ")])),_:1,__:[2]},8,["to"])]),e("div",et,[e("div",null,[r[4]||(r[4]=e("dt",{class:"text-sm text-gray-500"},"Numero Contratto",-1)),e("dd",tt,o(x.project.contract.contract_number),1)]),e("div",null,[r[5]||(r[5]=e("dt",{class:"text-sm text-gray-500"},"Tipo",-1)),e("dd",st,o(V(x.project.contract.contract_type)),1)]),e("div",null,[r[6]||(r[6]=e("dt",{class:"text-sm text-gray-500"},"Tariffa Oraria",-1)),e("dd",rt,o(I(x.project.contract.hourly_rate))+"/h",1)]),e("div",null,[r[7]||(r[7]=e("dt",{class:"text-sm text-gray-500"},"Stato",-1)),e("dd",null,[e("span",{class:F(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",B(x.project.contract.status)])},o(T(x.project.contract.status)),3)])])]),x.project.contract.budget_hours||x.project.contract.budget_amount?(t(),s("div",ot,[e("div",at,[x.project.contract.budget_hours?(t(),s("div",nt,[r[8]||(r[8]=e("dt",{class:"text-sm text-gray-500"},"Budget Ore",-1)),e("dd",it,o(x.project.contract.budget_hours)+"h",1)])):S("",!0),x.project.contract.budget_amount?(t(),s("div",lt,[r[9]||(r[9]=e("dt",{class:"text-sm text-gray-500"},"Budget Importo",-1)),e("dd",dt,o(I(x.project.contract.budget_amount)),1)])):S("",!0)])])):S("",!0)])):S("",!0),e("div",ut,[e("div",ct,[e("div",gt,[r[11]||(r[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",mt,[e("dl",null,[r[10]||(r[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",pt,o(x.project.task_count||0),1)])])])]),e("div",vt,[e("div",xt,[r[13]||(r[13]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",yt,[e("dl",null,[r[12]||(r[12]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",ft,o(x.project.completed_tasks||0),1)])])])]),e("div",bt,[e("div",ht,[r[15]||(r[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",kt,[e("dl",null,[r[14]||(r[14]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",wt,o(x.project.team_count||0),1)])])])]),e("div",_t,[e("div",$t,[r[17]||(r[17]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",jt,[e("dl",null,[r[16]||(r[16]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",Ct,o(I(x.project.budget)),1)])])])])]),e("div",Mt,[r[18]||(r[18]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",Tt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:ee({width:`${_.value}%`})},null,4)]),e("p",St,o(_.value)+"% completato",1)]),e("div",Pt,[r[23]||(r[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",At,[e("div",zt,[r[19]||(r[19]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",Dt,o(I(x.project.budget)),1)]),r[22]||(r[22]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",It,[r[20]||(r[20]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",Vt,o(I($.value)),1)]),e("div",Et,[e("div",{class:F(["h-3 rounded-full transition-all duration-300",y.value]),style:ee({width:b.value+"%"})},null,6)]),e("div",Bt,[r[21]||(r[21]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:F(["font-medium",f.value])},o(I(v.value)),3)])])]),e("div",Ut,[r[25]||(r[25]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Rt,[(t(!0),s(L,null,K(C.value,D=>(t(),s("div",{key:D.id,class:"flex items-center space-x-3"},[e("div",Ht,[D.profile_image?(t(),s("img",{key:0,src:D.profile_image,alt:D.full_name,class:"w-8 h-8 rounded-full"},null,8,Ot)):(t(),s("div",Ft,[e("span",Lt,o(h(D.full_name)),1)]))]),e("div",Kt,[e("p",qt,o(D.full_name),1),e("p",Nt,o(D.role||"Team Member"),1)]),e("div",Xt,[e("p",Gt,o(D.hours_worked||0)+"h",1)])]))),128)),C.value.length===0?(t(),s("div",Jt,r[24]||(r[24]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):S("",!0)])]),e("div",Wt,[r[28]||(r[28]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Yt,[(t(!0),s(L,null,K(n.value,D=>(t(),s("div",{key:D.id,class:"flex items-start space-x-3"},[e("div",Qt,[e("div",{class:F(["w-2 h-2 rounded-full mt-2",q(D.type)])},null,2)]),e("div",Zt,[e("p",es,o(D.description),1),e("div",ts,[e("p",ss,o(A(D.created_at)),1),r[26]||(r[26]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",rs,o(D.user_name),1)])])]))),128)),n.value.length===0?(t(),s("div",os,r[27]||(r[27]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):S("",!0)])])])):(t(),s("div",as,r[29]||(r[29]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},me=ue(ns,[["__scopeId","data-v-e2382cbb"]]),is={class:"space-y-6"},ls={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},ds={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},us={class:"flex items-center justify-between"},cs={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},gs=["value"],ms={class:"mt-4 flex items-center justify-between"},ps={class:"flex items-center space-x-4"},vs={class:"text-sm text-gray-500 dark:text-gray-400"},xs={key:0,class:"flex justify-center py-8"},ys={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},fs={class:"text-red-600"},bs={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},hs={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ks={class:"col-span-4"},ws={class:"text-sm font-medium text-gray-900 dark:text-white"},_s={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},$s={class:"col-span-2"},js={key:0,class:"flex items-center"},Cs={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},Ms={class:"ml-2"},Ts={class:"text-sm font-medium text-gray-900 dark:text-white"},Ss={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Ps={class:"col-span-1"},As={class:"col-span-1"},zs={class:"col-span-2"},Ds={key:0,class:"text-sm text-gray-900 dark:text-white"},Is={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Vs={class:"col-span-1"},Es={class:"text-sm text-gray-900 dark:text-white"},Bs={key:0,class:"text-gray-500"},Us={class:"col-span-1"},Rs={class:"flex items-center space-x-2"},Hs=["onClick"],Os={key:0,class:"px-6 py-12 text-center"},Fs={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ls={class:"flex items-center justify-between mb-4"},Ks={class:"font-medium text-gray-900 dark:text-white"},qs={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},Ns={class:"space-y-3"},Xs=["onClick"],Gs={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},Js={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Ws={class:"flex items-center justify-between"},Ys={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Qs={class:"mt-3"},Zs={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},er={class:"grid grid-cols-1 gap-4"},tr={class:"grid grid-cols-2 gap-4"},sr=["value"],rr={class:"grid grid-cols-2 gap-4"},or={class:"flex justify-end space-x-3 mt-6"},ar=["disabled"],nr={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const _=x,C=ne(),{hasPermission:$}=_e(),v=M([]),b=M(!1),y=M(""),f=M("list"),n=M(!1),j=M({status:"",priority:"",assignee_id:"",search:""}),H=M(!1),I=M(!1),A=M(null),h=M({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),q=U(()=>$.value("manage_project_tasks")),V=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],T=async()=>{var g,u;if((g=_.project)!=null&&g.id){b.value=!0,y.value="";try{const W=new URLSearchParams({project_id:_.project.id,...j.value}),re=await fetch(`/api/tasks?${W}`,{headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken}});if(!re.ok)throw new Error("Errore nel caricamento dei task");const k=await re.json();v.value=((u=k.data)==null?void 0:u.tasks)||k.tasks||[]}catch(W){y.value=W.message}finally{b.value=!1}}},B=async()=>{n.value=!0;try{const g=I.value?`/api/tasks/${A.value.id}`:"/api/tasks",u=I.value?"PUT":"POST",W={...h.value,project_id:_.project.id};if(!(await fetch(g,{method:u,headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken},body:JSON.stringify(W)})).ok)throw new Error("Errore nel salvataggio del task");await T(),r()}catch(g){y.value=g.message}finally{n.value=!1}},l=g=>{A.value=g,h.value={name:g.name,description:g.description||"",status:g.status,priority:g.priority,assignee_id:g.assignee_id||"",due_date:g.due_date?g.due_date.split("T")[0]:"",estimated_hours:g.estimated_hours},I.value=!0},r=()=>{H.value=!1,I.value=!1,A.value=null,h.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},w=g=>v.value.filter(u=>u.status===g),D=g=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[g]||"bg-gray-100 text-gray-800",R=g=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[g]||g,z=g=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[g]||"bg-gray-100 text-gray-800",i=g=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[g]||g,m=(g,u)=>`${(g==null?void 0:g.charAt(0))||""}${(u==null?void 0:u.charAt(0))||""}`.toUpperCase(),d=g=>new Date(g).toLocaleDateString("it-IT");let a;const O=()=>{clearTimeout(a),a=setTimeout(()=>{T()},300)};return Z(()=>{var g;return(g=_.project)==null?void 0:g.id},g=>{g&&T()}),se(()=>{var g;(g=_.project)!=null&&g.id&&T()}),P({refresh:T}),(g,u)=>{var W,re;return t(),s("div",is,[e("div",ls,[e("div",ds,[e("div",us,[u[16]||(u[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),q.value?(t(),s("button",{key:0,onClick:u[0]||(u[0]=k=>H.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},u[15]||(u[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),G(" Nuovo Task ")]))):S("",!0)]),e("div",cs,[e("div",null,[u[18]||(u[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":u[1]||(u[1]=k=>j.value.status=k),onChange:T,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},u[17]||(u[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[J,j.value.status]])]),e("div",null,[u[20]||(u[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":u[2]||(u[2]=k=>j.value.priority=k),onChange:T,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},u[19]||(u[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[J,j.value.priority]])]),e("div",null,[u[22]||(u[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":u[3]||(u[3]=k=>j.value.assignee_id=k),onChange:T,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[u[21]||(u[21]=e("option",{value:""},"Tutti",-1)),(t(!0),s(L,null,K(((W=x.project)==null?void 0:W.team_members)||[],k=>(t(),s("option",{key:k.id,value:k.id},o(k.first_name)+" "+o(k.last_name),9,gs))),128))],544),[[J,j.value.assignee_id]])]),e("div",null,[u[23]||(u[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),E(e("input",{"onUpdate:modelValue":u[4]||(u[4]=k=>j.value.search=k),onInput:O,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[X,j.value.search]])])]),e("div",ms,[e("div",ps,[u[24]||(u[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:u[5]||(u[5]=k=>f.value="list"),class:F([f.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:u[6]||(u[6]=k=>f.value="kanban"),class:F([f.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",vs,o(v.value.length)+" task trovati ",1)])])]),b.value?(t(),s("div",xs,u[25]||(u[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):S("",!0),y.value?(t(),s("div",ys,[e("p",fs,o(y.value),1)])):S("",!0),!b.value&&f.value==="list"?(t(),s("div",bs,[e("div",hs,[u[27]||(u[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(t(!0),s(L,null,K(v.value,k=>(t(),s("div",{key:k.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ks,[e("div",ws,o(k.name),1),k.description?(t(),s("div",_s,o(k.description),1)):S("",!0)]),e("div",$s,[k.assignee?(t(),s("div",js,[e("div",Cs,o(m(k.assignee.first_name,k.assignee.last_name)),1),e("div",Ms,[e("div",Ts,o(k.assignee.first_name)+" "+o(k.assignee.last_name),1)])])):(t(),s("span",Ss,"Non assegnato"))]),e("div",Ps,[e("span",{class:F([D(k.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(R(k.status)),3)]),e("div",As,[e("span",{class:F([z(k.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(i(k.priority)),3)]),e("div",zs,[k.due_date?(t(),s("div",Ds,o(d(k.due_date)),1)):(t(),s("span",Is,"-"))]),e("div",Vs,[e("div",Es,[G(o(k.actual_hours||0)+"h ",1),k.estimated_hours?(t(),s("span",Bs,"/ "+o(k.estimated_hours)+"h",1)):S("",!0)])]),e("div",Us,[e("div",Rs,[e("button",{onClick:c=>l(k),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Hs)])])]))),128)),v.value.length===0?(t(),s("div",Os,u[26]||(u[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):S("",!0)])])):S("",!0),!b.value&&f.value==="kanban"?(t(),s("div",Fs,[(t(),s(L,null,K(V,k=>e("div",{key:k.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Ls,[e("h4",Ks,o(k.label),1),e("span",qs,o(w(k.value).length),1)]),e("div",Ns,[(t(!0),s(L,null,K(w(k.value),c=>(t(),s("div",{key:c.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:p=>l(c)},[e("div",Gs,o(c.name),1),c.description?(t(),s("div",Js,o(c.description),1)):S("",!0),e("div",Ws,[e("span",{class:F([z(c.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},o(i(c.priority)),3),c.assignee?(t(),s("div",Ys,o(m(c.assignee.first_name,c.assignee.last_name)),1)):S("",!0)])],8,Xs))),128))])])),64))])):S("",!0),H.value||I.value?(t(),s("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:r},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:u[14]||(u[14]=te(()=>{},["stop"]))},[e("div",Qs,[e("h3",Zs,o(I.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:te(B,["prevent"])},[e("div",er,[e("div",null,[u[28]||(u[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),E(e("input",{"onUpdate:modelValue":u[7]||(u[7]=k=>h.value.name=k),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.name]])]),e("div",null,[u[29]||(u[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":u[8]||(u[8]=k=>h.value.description=k),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.description]])]),e("div",tr,[e("div",null,[u[31]||(u[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":u[9]||(u[9]=k=>h.value.status=k),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},u[30]||(u[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[J,h.value.status]])]),e("div",null,[u[33]||(u[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":u[10]||(u[10]=k=>h.value.priority=k),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},u[32]||(u[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[J,h.value.priority]])])]),e("div",null,[u[35]||(u[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":u[11]||(u[11]=k=>h.value.assignee_id=k),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[u[34]||(u[34]=e("option",{value:""},"Non assegnato",-1)),(t(!0),s(L,null,K(((re=x.project)==null?void 0:re.team_members)||[],k=>(t(),s("option",{key:k.id,value:k.id},o(k.first_name)+" "+o(k.last_name),9,sr))),128))],512),[[J,h.value.assignee_id]])]),e("div",rr,[e("div",null,[u[36]||(u[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),E(e("input",{"onUpdate:modelValue":u[12]||(u[12]=k=>h.value.due_date=k),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.due_date]])]),e("div",null,[u[37]||(u[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),E(e("input",{"onUpdate:modelValue":u[13]||(u[13]=k=>h.value.estimated_hours=k),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.estimated_hours]])])])]),e("div",or,[e("button",{type:"button",onClick:r,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:n.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(n.value?"Salvataggio...":I.value?"Aggiorna":"Crea"),9,ar)])],32)])])])):S("",!0)])}}},ir={class:"space-y-6"},lr={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},dr={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ur={class:"flex items-center justify-between"},cr={class:"p-6 border-b border-gray-200 dark:border-gray-700"},gr={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},mr={class:"text-center"},pr={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},vr={class:"text-center"},xr={class:"text-2xl font-bold text-green-600"},yr={class:"text-center"},fr={class:"text-2xl font-bold text-blue-600"},br={class:"text-center"},hr={class:"text-2xl font-bold text-purple-600"},kr={class:"p-6"},wr={class:"space-y-4"},_r={class:"flex items-center justify-between"},$r={class:"flex items-center space-x-4"},jr={class:"flex-shrink-0"},Cr=["src","alt"],Mr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Tr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},Sr={class:"flex-1"},Pr={class:"flex items-center space-x-2"},Ar={class:"text-lg font-medium text-gray-900 dark:text-white"},zr={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},Dr={class:"text-sm text-gray-600 dark:text-gray-400"},Ir={class:"text-xs text-gray-500 dark:text-gray-500"},Vr={class:"flex items-center space-x-4"},Er={class:"text-right"},Br={class:"text-sm font-medium text-gray-900 dark:text-white"},Ur={class:"text-right"},Rr={class:"text-sm font-medium text-gray-900 dark:text-white"},Hr={class:"text-right"},Or={class:"text-sm font-medium text-gray-900 dark:text-white"},Fr={class:"flex items-center space-x-2"},Lr=["onClick"],Kr=["onClick"],qr={class:"mt-4"},Nr={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},Xr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Gr={key:0,class:"text-center py-8"},Jr={class:"mt-6"},Wr={class:"mt-3"},Yr={class:"space-y-4"},Qr=["value"],Zr={class:"flex justify-end space-x-3 mt-6"},eo=["disabled"],to={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(x,{expose:P,emit:_}){const C=x,$=ne(),v=M(!1),b=M([]),y=M(!1),f=M({user_id:"",role:""}),n=U(()=>{var i;return((i=C.project)==null?void 0:i.team_members)||[]}),j=U(()=>n.value.reduce((i,m)=>i+(m.hours_worked||0),0)),H=U(()=>n.value.length===0?0:Math.round(j.value/n.value.length)),I=U(()=>n.value.filter(i=>(i.hours_worked||0)>0).length),A=i=>i?i.split(" ").map(m=>m.charAt(0).toUpperCase()).slice(0,2).join(""):"??",h=i=>{var d;return(((d=C.project)==null?void 0:d.tasks)||[]).filter(a=>a.assignee_id===i).length},q=i=>{var d;return(((d=C.project)==null?void 0:d.tasks)||[]).filter(a=>a.assignee_id===i&&a.status==="done").length},V=i=>{const m=h(i),d=q(i);return m===0?0:Math.round(d/m*100)},T=i=>{const m=V(i);return m>=80?"bg-green-600":m>=60?"bg-yellow-600":m>=40?"bg-orange-600":"bg-red-600"},B=i=>!i||i===0?"0.00":parseFloat(i).toFixed(2),l=async()=>{var i;try{const m=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(m.ok){const d=await m.json(),a=n.value.map(O=>O.id);b.value=(i=d.data)!=null&&i.users?d.data.users.filter(O=>!a.includes(O.id)):[]}}catch(m){console.error("Errore nel caricamento utenti:",m),b.value=[]}},r=async()=>{y.value=!0;try{const i=await fetch(`/api/projects/${C.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify(f.value)});if(i.ok)z("refresh"),R();else{const m=await i.json();alert(m.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{y.value=!1}},w=i=>{console.log("Edit member:",i)},D=async i=>{if(confirm(`Rimuovere ${i.full_name} dal progetto?`))try{const m=await fetch(`/api/projects/${C.project.id}/team/${i.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(m.ok)z("refresh");else{const d=await m.json();alert(d.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},R=()=>{v.value=!1,f.value={user_id:"",role:""}},z=_;return se(()=>{l()}),Z(()=>v.value,i=>{i&&l()}),Z(()=>{var i;return(i=C.project)==null?void 0:i.team_members},()=>{v.value&&l()}),P({refresh:l}),(i,m)=>(t(),s("div",ir,[e("div",lr,[e("div",dr,[e("div",ur,[m[6]||(m[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:m[0]||(m[0]=d=>v.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},m[5]||(m[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),G(" Aggiungi Membro ")]))])]),e("div",cr,[e("div",gr,[e("div",mr,[e("div",pr,o(n.value.length),1),m[7]||(m[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",vr,[e("div",xr,o(j.value)+"h",1),m[8]||(m[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",yr,[e("div",fr,o(H.value)+"h",1),m[9]||(m[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",br,[e("div",hr,o(I.value),1),m[10]||(m[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",kr,[e("div",wr,[(t(!0),s(L,null,K(n.value,d=>{var a,O;return t(),s("div",{key:d.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",_r,[e("div",$r,[e("div",jr,[d.profile_image?(t(),s("img",{key:0,src:d.profile_image,alt:d.full_name,class:"w-12 h-12 rounded-full"},null,8,Cr)):(t(),s("div",Mr,[e("span",Tr,o(A(d.full_name)),1)]))]),e("div",Sr,[e("div",Pr,[e("h4",Ar,o(d.full_name),1),d.id===((a=x.project)==null?void 0:a.manager_id)?(t(),s("span",zr," Project Manager ")):S("",!0)]),e("p",Dr,o(d.role||"Team Member"),1),e("p",Ir,o(d.email),1)])]),e("div",Vr,[e("div",Er,[e("div",Br,o(B(d.hours_worked||0))+"h",1),m[11]||(m[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",Ur,[e("div",Rr,o(h(d.id)),1),m[12]||(m[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Hr,[e("div",Or,o(q(d.id)),1),m[13]||(m[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Fr,[e("button",{onClick:g=>w(d),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},m[14]||(m[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Lr),d.id!==((O=x.project)==null?void 0:O.manager_id)?(t(),s("button",{key:0,onClick:g=>D(d),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},m[15]||(m[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Kr)):S("",!0)])])]),e("div",qr,[e("div",Nr,[m[16]||(m[16]=e("span",null,"Produttività",-1)),e("span",null,o(V(d.id))+"%",1)]),e("div",Xr,[e("div",{class:F(["h-2 rounded-full transition-all duration-300",T(d.id)]),style:ee({width:V(d.id)+"%"})},null,6)])])])}),128)),n.value.length===0?(t(),s("div",Gr,[m[18]||(m[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),m[19]||(m[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),m[20]||(m[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Jr,[e("button",{onClick:m[1]||(m[1]=d=>v.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},m[17]||(m[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),G(" Aggiungi primo membro ")]))])])):S("",!0)])])]),v.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:R},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:m[4]||(m[4]=te(()=>{},["stop"]))},[e("div",Wr,[m[25]||(m[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:te(r,["prevent"])},[e("div",Yr,[e("div",null,[m[22]||(m[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),E(e("select",{"onUpdate:modelValue":m[2]||(m[2]=d=>f.value.user_id=d),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[m[21]||(m[21]=e("option",{value:""},"Seleziona utente",-1)),(t(!0),s(L,null,K(b.value,d=>(t(),s("option",{key:d.id,value:d.id},o(d.full_name)+" ("+o(d.email)+") ",9,Qr))),128))],512),[[J,f.value.user_id]])]),e("div",null,[m[24]||(m[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),E(e("select",{"onUpdate:modelValue":m[3]||(m[3]=d=>f.value.role=d),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},m[23]||(m[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[J,f.value.role]])])]),e("div",Zr,[e("button",{type:"button",onClick:R,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(y.value?"Aggiungendo...":"Aggiungi"),9,eo)])],32)])])])):S("",!0)]))}};function so(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function ro(x,P){return t(),s("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const oo={class:"fixed inset-0 z-50 overflow-y-auto"},ao={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},no={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},io={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},lo={class:"mb-4"},uo={class:"text-lg font-medium text-gray-900 dark:text-white"},co={class:"space-y-4"},go={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},mo=["disabled"],po={key:0},vo={key:1},xo={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(x,{emit:P}){const _=x,C=P,$=M(!1),v=be({description:"",amount:0,category:"",billing_type:"billable",status:"pending",date:new Date().toISOString().split("T")[0],notes:"",receipt_file:null}),b=async()=>{$.value=!0;try{const f=_.expense?`/api/expenses/${_.expense.id}`:`/api/projects/${_.projectId}/expenses`,n=_.expense?"PUT":"POST";(await fetch(f,{method:n,headers:{"Content-Type":"application/json"},body:JSON.stringify(v)})).ok?C("saved"):console.error("Error saving expense")}catch(f){console.error("Error saving expense:",f)}finally{$.value=!1}},y=f=>{const n=f.target.files[0];if(n){if(n.size>5*1024*1024){alert("Il file è troppo grande. Dimensione massima: 5MB"),f.target.value="";return}v.receipt_file=n}};return se(()=>{_.expense&&Object.assign(v,{description:_.expense.description,amount:_.expense.amount,category:_.expense.category,billing_type:_.expense.billing_type||"billable",status:_.expense.status||"pending",date:_.expense.date.split("T")[0],notes:_.expense.notes||""})}),(f,n)=>(t(),s("div",oo,[e("div",ao,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:n[0]||(n[0]=j=>f.$emit("close"))}),e("div",no,[e("form",{onSubmit:te(b,["prevent"])},[e("div",io,[e("div",lo,[e("h3",uo,o(x.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",co,[e("div",null,[n[9]||(n[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),E(e("input",{"onUpdate:modelValue":n[1]||(n[1]=j=>v.description=j),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[X,v.description]])]),e("div",null,[n[10]||(n[10]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),E(e("input",{"onUpdate:modelValue":n[2]||(n[2]=j=>v.amount=j),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[X,v.amount,void 0,{number:!0}]])]),e("div",null,[n[12]||(n[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),E(e("select",{"onUpdate:modelValue":n[3]||(n[3]=j=>v.category=j),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[11]||(n[11]=[ae('<option value="">Seleziona categoria</option><option value="licenses">📄 Licenze</option><option value="travel">✈️ Viaggi</option><option value="meals">🍽️ Pasti</option><option value="equipment">🖥️ Attrezzature</option><option value="external">🏢 Servizi Esterni</option><option value="other">📦 Altro</option>',7)]),512),[[J,v.category]])]),e("div",null,[n[14]||(n[14]=e("label",{for:"billing_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Fatturazione ",-1)),E(e("select",{"onUpdate:modelValue":n[4]||(n[4]=j=>v.billing_type=j),id:"billing_type",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[13]||(n[13]=[e("option",{value:"billable"},"💰 Fatturabile al Cliente",-1),e("option",{value:"non-billable"},"🏢 Assorbimento Interno",-1),e("option",{value:"reimbursable"},"💳 Rimborsabile",-1)]),512),[[J,v.billing_type]])]),e("div",null,[n[16]||(n[16]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),E(e("select",{"onUpdate:modelValue":n[5]||(n[5]=j=>v.status=j),id:"status",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},n[15]||(n[15]=[e("option",{value:"pending"},"⏳ In Attesa di Approvazione",-1),e("option",{value:"approved"},"✅ Approvata",-1),e("option",{value:"rejected"},"❌ Rifiutata",-1)]),512),[[J,v.status]])]),e("div",null,[n[17]||(n[17]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),E(e("input",{"onUpdate:modelValue":n[6]||(n[6]=j=>v.date=j),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,v.date]])]),e("div",null,[n[18]||(n[18]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),E(e("textarea",{"onUpdate:modelValue":n[7]||(n[7]=j=>v.notes=j),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[X,v.notes]])]),e("div",null,[n[19]||(n[19]=e("label",{for:"receipt",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ricevuta/Scontrino ",-1)),e("input",{type:"file",id:"receipt",accept:"image/*,.pdf",onChange:y,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),n[20]||(n[20]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Carica immagine o PDF della ricevuta (max 5MB) ",-1))])])]),e("div",go,[e("button",{type:"submit",disabled:$.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[$.value?(t(),s("span",po,"Salvando...")):(t(),s("span",vo,o(x.expense?"Aggiorna":"Salva"),1))],8,mo),e("button",{type:"button",onClick:n[8]||(n[8]=j=>f.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},yo={class:"project-expenses"},fo={class:"space-y-6"},bo={class:"flex justify-between items-center"},ho={key:0,class:"text-center py-8"},ko={key:1,class:"text-center py-12"},wo={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},_o={class:"divide-y divide-gray-200 dark:divide-gray-700"},$o={class:"flex items-center justify-between"},jo={class:"flex-1"},Co={class:"flex items-center"},Mo={class:"flex-shrink-0"},To={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},So={class:"ml-4 flex-1"},Po={class:"flex items-center justify-between"},Ao={class:"text-sm font-medium text-gray-900 dark:text-white"},zo={class:"ml-2 flex-shrink-0"},Do={class:"text-sm font-medium text-gray-900 dark:text-white"},Io={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},Vo={class:"capitalize"},Eo={key:0,class:"mx-2"},Bo={key:1},Uo={key:0,class:"flex items-center space-x-2"},Ro=["onClick"],Ho=["onClick"],Oo={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Fo={class:"flex justify-between items-center"},Lo={class:"text-lg font-bold text-gray-900 dark:text-white"},Ko={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(x){const P=x;fe();const _=ne(),C=M(!1),$=M([]),v=M(!1),b=M(null),y=U(()=>_.hasPermission("manage_expenses")),f=U(()=>$.value.reduce((V,T)=>V+T.amount,0)),n=async()=>{var V;if((V=P.project)!=null&&V.id){C.value=!0;try{const T=await fetch(`/api/projects/${P.project.id}/expenses`);T.ok&&($.value=await T.json())}catch(T){console.error("Error loading expenses:",T)}finally{C.value=!1}}},j=V=>{b.value=V,v.value=!0},H=async V=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${V}`,{method:"DELETE"})).ok&&($.value=$.value.filter(B=>B.id!==V))}catch(T){console.error("Error deleting expense:",T)}},I=()=>{v.value=!1,b.value=null},A=()=>{I(),n()},h=V=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(V),q=V=>new Date(V).toLocaleDateString("it-IT");return se(()=>{n()}),(V,T)=>(t(),s("div",yo,[e("div",fo,[e("div",bo,[T[2]||(T[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),y.value?(t(),s("button",{key:0,onClick:T[0]||(T[0]=B=>v.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[oe(le(ro),{class:"w-4 h-4 mr-2"}),T[1]||(T[1]=G(" Aggiungi Spesa "))])):S("",!0)]),C.value?(t(),s("div",ho,T[3]||(T[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):$.value.length===0?(t(),s("div",ko,[oe(le(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),T[4]||(T[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),T[5]||(T[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(t(),s("div",wo,[e("ul",_o,[(t(!0),s(L,null,K($.value,B=>(t(),s("li",{key:B.id,class:"px-6 py-4"},[e("div",$o,[e("div",jo,[e("div",Co,[e("div",Mo,[e("div",To,[oe(le(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",So,[e("div",Po,[e("p",Ao,o(B.description),1),e("div",zo,[e("p",Do," €"+o(h(B.amount)),1)])]),e("div",Io,[oe(le(so),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),G(" "+o(q(B.date))+" ",1),T[6]||(T[6]=e("span",{class:"mx-2"},"•",-1)),e("span",Vo,o(B.category),1),B.user?(t(),s("span",Eo,"•")):S("",!0),B.user?(t(),s("span",Bo,o(B.user.name),1)):S("",!0)])])])]),y.value?(t(),s("div",Uo,[e("button",{onClick:l=>j(B),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,Ro),e("button",{onClick:l=>H(B.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,Ho)])):S("",!0)])]))),128))])])),$.value.length>0?(t(),s("div",Oo,[e("div",Fo,[T[7]||(T[7]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",Lo,"€"+o(h(f.value)),1)])])):S("",!0)]),v.value?(t(),de(xo,{key:0,"project-id":V.projectId,expense:b.value,onClose:I,onSaved:A},null,8,["project-id","expense"])):S("",!0)]))}},qo={class:"project-kpi"},No={key:0,class:"animate-pulse space-y-4"},Xo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Go={key:1,class:"space-y-6"},Jo={class:"bg-white shadow rounded-lg p-6"},Wo={class:"flex items-center justify-between"},Yo=["disabled"],Qo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Zo={class:"bg-white shadow rounded-lg p-6"},ea={class:"flex items-center"},ta={class:"ml-5 w-0 flex-1"},sa={class:"text-lg font-medium text-gray-900"},ra={class:"text-xs text-gray-500"},oa={class:"bg-white shadow rounded-lg p-6"},aa={class:"flex items-center"},na={class:"ml-5 w-0 flex-1"},ia={class:"text-lg font-medium text-gray-900"},la={class:"bg-white shadow rounded-lg p-6"},da={class:"flex items-center"},ua={class:"ml-5 w-0 flex-1"},ca={class:"text-lg font-medium text-gray-900"},ga={class:"text-xs text-gray-500"},ma={class:"bg-white shadow rounded-lg p-6"},pa={class:"flex items-center"},va={class:"ml-5 w-0 flex-1"},xa={class:"text-lg font-medium text-gray-900"},ya={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},fa={class:"bg-white shadow rounded-lg p-6"},ba={class:"space-y-4"},ha={class:"flex justify-between text-sm"},ka={class:"font-medium"},wa={class:"w-full bg-gray-200 rounded-full h-3"},_a={class:"flex justify-between text-sm"},$a={class:"text-gray-600"},ja={class:"font-medium"},Ca={class:"bg-white shadow rounded-lg p-6"},Ma={class:"space-y-4"},Ta={class:"flex justify-between text-sm"},Sa={class:"font-medium"},Pa={class:"w-full bg-gray-200 rounded-full h-3"},Aa={class:"flex justify-between text-sm"},za={class:"text-gray-600"},Da={class:"font-medium"},Ia={class:"bg-white shadow rounded-lg p-6"},Va={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ea={class:"text-center p-4 border rounded-lg"},Ba={class:"text-xs text-gray-500"},Ua={class:"text-center p-4 border rounded-lg"},Ra={class:"text-xs text-gray-500"},Ha={class:"text-center p-4 border rounded-lg"},Oa={class:"text-xs text-gray-500"},Fa={key:2,class:"text-center py-8"},La={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ka={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},qa={class:"mt-3"},Na={class:"mt-6 space-y-6"},Xa={class:"bg-gray-50 p-4 rounded-lg"},Ga={class:"font-medium text-gray-900"},Ja={class:"text-sm text-gray-600"},Wa={class:"space-y-6"},Ya={class:"flex items-center justify-between mb-4"},Qa={class:"font-medium text-gray-900"},Za={class:"text-sm text-gray-600"},en={class:"flex items-center space-x-2"},tn={class:"text-xs text-gray-500"},sn=["onClick"],rn={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},on=["onUpdate:modelValue","onInput"],an=["onUpdate:modelValue","onInput"],nn=["onUpdate:modelValue","onInput"],ln={class:"mt-4"},dn=["onUpdate:modelValue","onInput"],un={class:"mt-4 flex justify-end"},cn=["onClick","disabled"],gn={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},mn={key:1,class:"text-sm text-green-600"},pn={class:"mt-6 pt-4 border-t flex justify-between"},vn={class:"flex space-x-3"},xn=["disabled"],yn={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(x,{emit:P}){const _=x,C=P,$=M(!1),v=M(!1),b=M(null),y=M({}),f=M({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),n=M({budget:80,time:85,margin:15}),j=U(()=>{var c;return!((c=_.project)!=null&&c.budget)||f.value.totalCosts===0?0:Math.round(f.value.totalCosts/_.project.budget*100)}),H=U(()=>{var c;return!((c=_.project)!=null&&c.estimated_hours)||f.value.totalHours===0?0:Math.round(f.value.totalHours/_.project.estimated_hours*100)}),I=U(()=>{const c=f.value.costVariance;return c>0?"text-red-600":c<0?"text-green-600":"text-gray-600"}),A=U(()=>{const c=f.value.marginPercentage;return c>=n.value.margin?"text-green-600":c>=n.value.margin*.7?"text-yellow-600":"text-red-600"}),h=U(()=>{const c=f.value.marginPercentage;return c>=n.value.margin?"Ottimo":c>=n.value.margin*.7?"Accettabile":"Critico"}),q=U(()=>{const c=j.value;return c>=n.value.budget?"text-red-600":c>=n.value.budget*.8?"text-yellow-600":"text-green-600"}),V=U(()=>{const c=H.value;return c>=n.value.time?"text-red-600":c>=n.value.time*.8?"text-yellow-600":"text-green-600"}),T=U(()=>{const c=f.value.marginPercentage;return c>=n.value.margin?"text-green-600":c>=n.value.margin*.7?"text-yellow-600":"text-red-600"}),B=c=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(c||0),l=c=>!c||c===0?"0h":`${parseFloat(c).toFixed(2)}h`,r=c=>`${(c||0).toFixed(1)}%`,w=async()=>{var c;(c=_.project)!=null&&c.id&&D()},D=()=>{const c=_.project;c&&(f.value={totalHours:c.total_hours||0,workDays:Math.ceil((c.total_hours||0)/8),totalCosts:(c.total_hours||0)*50,costVariance:(c.total_hours||0)*50-(c.budget||0),potentialRevenue:c.budget||0,actualRevenue:c.invoiced_amount||0,marginPercentage:c.budget?(c.budget-(c.total_hours||0)*50)/c.budget*100:0})},R=async()=>{$.value=!0;try{await w(),C("refresh")}catch(c){console.error("Error refreshing KPIs:",c)}finally{$.value=!1}},z=U(()=>{var p;const c=((p=_.project)==null?void 0:p.project_type)||"service";return i(c)}),i=c=>{const p={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return p[c]||p.service},m=c=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[c]||"Sconosciuto",d=()=>{z.value.forEach(p=>{y.value[p.name]||(y.value[p.name]={target_min:p.target_min,target_max:p.target_max,warning_threshold:p.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),v.value=!0},a=()=>{v.value=!1},O=c=>{y.value[c]&&(y.value[c].isDirty=!0,y.value[c].isSaved=!1)},g=c=>{const p=z.value.find(Y=>Y.name===c);p&&y.value[c]&&(y.value[c].target_min=p.target_min,y.value[c].target_max=p.target_max,y.value[c].warning_threshold=p.warning_threshold,y.value[c].custom_description="",y.value[c].isDirty=!0,y.value[c].isSaved=!1)},u=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&z.value.forEach(c=>{g(c.name)})},W=async c=>{var p;if(y.value[c]){b.value=c;try{const Y=y.value[c];await new Promise(ie=>setTimeout(ie,1e3)),console.log("Saving KPI config:",{project_id:(p=_.project)==null?void 0:p.id,kpi_name:c,target_min:Y.target_min,target_max:Y.target_max,warning_threshold:Y.warning_threshold,custom_description:Y.custom_description}),y.value[c].isDirty=!1,y.value[c].isSaved=!0,setTimeout(()=>{y.value[c]&&(y.value[c].isSaved=!1)},3e3)}catch(Y){console.error("Error saving KPI config:",Y),alert("Errore nel salvataggio della configurazione KPI")}finally{b.value=null}}},re=async()=>{const c=z.value.filter(p=>{var Y;return(Y=y.value[p.name])==null?void 0:Y.isDirty});for(const p of c)await W(p.name)},k=U(()=>z.value.some(c=>{var p;return(p=y.value[c.name])==null?void 0:p.isDirty}));return Z(()=>_.project,c=>{c&&w()},{immediate:!0}),se(()=>{_.project&&w()}),(c,p)=>{var Y,ie;return t(),s("div",qo,[x.loading?(t(),s("div",No,[e("div",Xo,[(t(),s(L,null,K(4,N=>e("div",{key:N,class:"bg-gray-200 rounded-lg h-24"})),64))]),p[0]||(p[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):x.project?(t(),s("div",Go,[e("div",Jo,[e("div",Wo,[p[3]||(p[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:R,disabled:$.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(t(),s("svg",{class:F(["w-4 h-4 mr-2",{"animate-spin":$.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},p[1]||(p[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),p[2]||(p[2]=G(" Aggiorna "))],8,Yo)])]),e("div",Qo,[e("div",Zo,[e("div",ea,[p[5]||(p[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ta,[e("dl",null,[p[4]||(p[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",sa,o(l(f.value.totalHours)),1),e("dd",ra,o(f.value.workDays)+" giorni lavorati",1)])])])]),e("div",oa,[e("div",aa,[p[7]||(p[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",na,[e("dl",null,[p[6]||(p[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",ia,o(B(f.value.totalCosts)),1),e("dd",{class:F(["text-xs",I.value])},o(B(f.value.costVariance))+" vs budget",3)])])])]),e("div",la,[e("div",da,[p[9]||(p[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",ua,[e("dl",null,[p[8]||(p[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",ca,o(B(f.value.potentialRevenue)),1),e("dd",ga,o(B(f.value.actualRevenue))+" fatturati",1)])])])]),e("div",ma,[e("div",pa,[p[11]||(p[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",va,[e("dl",null,[p[10]||(p[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",xa,o(r(f.value.marginPercentage)),1),e("dd",{class:F(["text-xs",A.value])},o(h.value),3)])])])])]),e("div",ya,[e("div",fa,[p[13]||(p[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",ba,[e("div",ha,[p[12]||(p[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ka,o(B(x.project.budget||0)),1)]),e("div",wa,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:ee({width:j.value+"%"})},null,4)]),e("div",_a,[e("span",$a,"Utilizzato: "+o(B(f.value.totalCosts)),1),e("span",ja,o(j.value)+"%",1)])])]),e("div",Ca,[p[15]||(p[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",Ma,[e("div",Ta,[p[14]||(p[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",Sa,o(l(x.project.estimated_hours||0)),1)]),e("div",Pa,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:ee({width:H.value+"%"})},null,4)]),e("div",Aa,[e("span",za,"Lavorate: "+o(l(f.value.totalHours)),1),e("span",Da,o(H.value)+"%",1)])])])]),e("div",Ia,[e("div",{class:"flex items-center justify-between mb-4"},[p[17]||(p[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:d,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},p[16]||(p[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),G(" Configura KPI ")]))]),e("div",Va,[e("div",Ea,[e("div",{class:F(["text-2xl font-bold",q.value])},o(j.value)+"% ",3),p[18]||(p[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",Ba,"Soglia: "+o(n.value.budget)+"%",1)]),e("div",Ua,[e("div",{class:F(["text-2xl font-bold",V.value])},o(H.value)+"% ",3),p[19]||(p[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",Ra,"Soglia: "+o(n.value.time)+"%",1)]),e("div",Ha,[e("div",{class:F(["text-2xl font-bold",T.value])},o(r(f.value.marginPercentage)),3),p[20]||(p[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Oa,"Soglia: "+o(n.value.margin)+"%",1)])])])])):(t(),s("div",Fa,p[21]||(p[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),v.value?(t(),s("div",La,[e("div",Ka,[e("div",qa,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[p[23]||(p[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:a,class:"text-gray-400 hover:text-gray-600"},p[22]||(p[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Na,[e("div",Xa,[e("h4",Ga,o((Y=x.project)==null?void 0:Y.name),1),e("p",Ja,"Tipo: "+o(m((ie=x.project)==null?void 0:ie.project_type)),1)]),e("div",Wa,[(t(!0),s(L,null,K(z.value,N=>{var ce,ge;return t(),s("div",{key:N.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Ya,[e("div",null,[e("h5",Qa,o(N.display_name),1),e("p",Za,o(N.description),1)]),e("div",en,[e("span",tn,o(N.unit),1),e("button",{onClick:Q=>g(N.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,sn)])]),e("div",rn,[e("div",null,[p[24]||(p[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>y.value[N.name].target_min=Q,onInput:Q=>O(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,on),[[X,y.value[N.name].target_min]])]),e("div",null,[p[25]||(p[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>y.value[N.name].target_max=Q,onInput:Q=>O(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,an),[[X,y.value[N.name].target_max]])]),e("div",null,[p[26]||(p[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Q=>y.value[N.name].warning_threshold=Q,onInput:Q=>O(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,nn),[[X,y.value[N.name].warning_threshold]])])]),e("div",ln,[p[27]||(p[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),E(e("textarea",{"onUpdate:modelValue":Q=>y.value[N.name].custom_description=Q,onInput:Q=>O(N.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,dn),[[X,y.value[N.name].custom_description]])]),e("div",un,[(ce=y.value[N.name])!=null&&ce.isDirty?(t(),s("button",{key:0,onClick:Q=>W(N.name),disabled:b.value===N.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[b.value===N.name?(t(),s("svg",gn,p[28]||(p[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):S("",!0),G(" "+o(b.value===N.name?"Salvataggio...":"Salva KPI"),1)],8,cn)):(ge=y.value[N.name])!=null&&ge.isSaved?(t(),s("span",mn,"✓ Salvato")):S("",!0)])])}),128))])]),e("div",pn,[e("button",{onClick:u,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",vn,[e("button",{onClick:a,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:re,disabled:!k.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,xn)])])])])])):S("",!0)])}}},fn={class:"space-y-6"},bn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},hn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},kn={class:"flex items-center justify-between"},wn={class:"flex items-center space-x-4"},_n={class:"flex items-center space-x-2"},$n={key:0,class:"p-6"},jn={class:"overflow-x-auto"},Cn={class:"min-w-[1000px]"},Mn={class:"flex mb-4"},Tn={class:"flex-1 flex"},Sn={class:"space-y-1"},Pn={class:"w-80 flex-shrink-0 px-4 py-3"},An={class:"flex items-center space-x-2"},zn={class:"flex-1 min-w-0"},Dn={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},In={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},Vn={key:0},En={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Bn={class:"flex-1 relative h-12 flex items-center"},Un=["title"],Rn={class:"truncate"},Hn={class:"ml-2"},On={key:1,class:"text-center py-12"},Fn={key:2,class:"flex justify-center py-12"},Ln={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const _=x,C=M("weeks"),$=M(new Date),v=M([]),b=U(()=>{var l;return((l=_.project)==null?void 0:l.tasks)||[]}),y=U(()=>b.value.filter(l=>l.start_date&&l.due_date).map(l=>{const r=n(l);return{...l,timeline:r}})),f=()=>{const l=new Date($.value),r=[],w=12;for(let D=0;D<w;D++){const R=new Date(l);C.value==="weeks"?R.setDate(l.getDate()+D*7):C.value==="months"&&R.setMonth(l.getMonth()+D),r.push(R)}v.value=r},n=l=>{if(!v.value.length)return null;const r=new Date(l.start_date),w=new Date(l.due_date),D=v.value[0],z=v.value[v.value.length-1]-D,i=r-D,m=w-r,d=Math.max(0,i/z*100),a=Math.min(100-d,m/z*100);return{leftPercent:d,widthPercent:Math.max(5,a)}},j=l=>C.value==="weeks"?`${l.getDate()}/${l.getMonth()+1}`:C.value==="months"?l.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",H=l=>{const r=new Date,w=new Date(l);if(C.value==="weeks"){const D=new Date(w),R=new Date(w);return R.setDate(R.getDate()+6),r>=D&&r<=R}else if(C.value==="months")return w.getMonth()===r.getMonth()&&w.getFullYear()===r.getFullYear();return!1},I=()=>{const l=new Date;if(C.value==="weeks"){const r=new Date(l);r.setDate(l.getDate()-l.getDay()),$.value=r}else{const r=new Date(l.getFullYear(),l.getMonth(),1);$.value=r}f()},A=l=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[l]||"bg-gray-400",h=l=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[l]||"bg-gray-500",q=l=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[l]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",V=l=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[l]||"Non specificata",T=l=>({todo:0,"in-progress":50,review:75,done:100})[l.status]||0,B=l=>l?new Date(l).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return Z(()=>_.project,()=>{f()},{immediate:!0}),se(()=>{I()}),P({refresh:f}),(l,r)=>(t(),s("div",fn,[e("div",bn,[e("div",hn,[e("div",kn,[r[3]||(r[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",wn,[e("div",_n,[r[2]||(r[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":r[0]||(r[0]=w=>C.value=w),onChange:f,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},r[1]||(r[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[J,C.value]])]),e("button",{onClick:I,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!x.loading&&y.value.length>0?(t(),s("div",$n,[e("div",jn,[e("div",Cn,[e("div",Mn,[r[4]||(r[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",Tn,[(t(!0),s(L,null,K(v.value,(w,D)=>(t(),s("div",{key:D,class:F(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":H(w)}])},o(j(w)),3))),128))])]),e("div",Sn,[(t(!0),s(L,null,K(y.value,w=>(t(),s("div",{key:w.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",Pn,[e("div",An,[e("div",{class:F(["w-3 h-3 rounded-full",A(w.status)])},null,2),e("div",zn,[e("p",Dn,o(w.name),1),e("div",In,[w.assignee?(t(),s("span",Vn,o(w.assignee.full_name),1)):S("",!0),e("span",{class:F(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",q(w.priority)])},o(V(w.priority)),3)]),e("div",En,o(B(w.start_date))+" - "+o(B(w.due_date)),1)])])]),e("div",Bn,[w.timeline?(t(),s("div",{key:0,class:F(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",h(w.status)]),style:ee({left:w.timeline.leftPercent+"%",width:w.timeline.widthPercent+"%",minWidth:"60px"}),title:`${w.name} - ${T(w)}% completato`},[e("span",Rn,o(w.name.length>15?w.name.substring(0,15)+"...":w.name),1),e("span",Hn,o(T(w))+"%",1)],14,Un)):S("",!0),w.timeline&&T(w)>0&&T(w)<100?(t(),s("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:ee({left:w.timeline.leftPercent+"%",width:w.timeline.widthPercent*T(w)/100+"%",minWidth:"2px"})},null,4)):S("",!0),(t(!0),s(L,null,K(v.value,(D,R)=>(t(),s("div",{key:R,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:ee({left:R/v.value.length*100+"%"})},null,4))),128))])]))),128))])])]),r[5]||(r[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):x.loading?S("",!0):(t(),s("div",On,r[6]||(r[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),x.loading?(t(),s("div",Fn,r[7]||(r[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):S("",!0)])]))}},Kn={class:"space-y-6"},qn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Nn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Xn={class:"flex items-center justify-between"},Gn={class:"flex items-center space-x-4"},Jn={class:"flex items-center space-x-2"},Wn={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},Yn={class:"flex items-center space-x-2"},Qn=["value"],Zn={key:0,class:"flex justify-center py-8"},ei={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},ti={class:"text-red-600"},si={key:2,class:"p-6"},ri={class:"overflow-x-auto"},oi={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ai={class:"bg-gray-50 dark:bg-gray-700"},ni={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ii={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},li={class:"text-sm font-medium text-gray-900 dark:text-white"},di={class:"text-xs text-gray-500 dark:text-gray-400"},ui=["onClick"],ci={key:0,class:"text-xs font-medium text-primary-600 dark:text-primary-400"},gi={key:1,class:"text-gray-300 dark:text-gray-600"},mi={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},pi={class:"text-sm font-medium text-gray-900 dark:text-white"},vi={class:"bg-gray-100 dark:bg-gray-600 font-medium"},xi={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},yi={key:0,class:"text-center py-8"},fi={class:"mt-3"},bi={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},hi={class:"grid grid-cols-1 gap-4"},ki=["value"],wi={class:"flex justify-end space-x-3 mt-6"},_i=["disabled"],$i={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(x,{expose:P}){const _=x,C=ne(),$=M(null),v=M(!1),b=M(""),y=M(!1),f=M(new Date().getFullYear()),n=M(new Date().getMonth()+1),j=M(""),H=M(!1),I=M(!1),A=M(null),h=M({task_id:"",date:"",hours:0,description:""}),q=U(()=>$.value?Array.from({length:$.value.days_in_month},(z,i)=>i+1):[]),V=async()=>{var z;if((z=_.project)!=null&&z.id){v.value=!0,b.value="";try{const i=new URLSearchParams({year:f.value.toString(),month:n.value.toString()});j.value&&i.append("member_id",j.value.toString());const m=await fetch(`/api/timesheets/project/${_.project.id}/monthly?${i}`,{headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken}});if(!m.ok)throw new Error("Errore nel caricamento del timesheet");const d=await m.json();$.value=d.data}catch(i){b.value=i.message}finally{v.value=!1}}},T=async()=>{y.value=!0;try{const z={...h.value,project_id:_.project.id};if(!(await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":C.csrfToken},body:JSON.stringify(z)})).ok)throw new Error("Errore nel salvataggio del timesheet");await V(),l()}catch(z){b.value=z.message}finally{y.value=!1}},B=(z,i)=>{const m=$.value.tasks.find(d=>d.id===z);m&&(A.value={taskId:z,day:i},h.value={task_id:z,date:`${f.value}-${String(n.value).padStart(2,"0")}-${String(i).padStart(2,"0")}`,hours:m.daily_hours[i]||0,description:""},m.daily_hours[i]>0?I.value=!0:H.value=!0)},l=()=>{H.value=!1,I.value=!1,A.value=null,h.value={task_id:"",date:"",hours:0,description:""}},r=()=>{n.value===1?(n.value=12,f.value--):n.value--,V()},w=()=>{n.value===12?(n.value=1,f.value++):n.value++,V()},D=z=>{const i=new Date;return i.getFullYear()===f.value&&i.getMonth()+1===n.value&&i.getDate()===z},R=z=>!z||z===0?"0":z%1===0?z.toString():z.toFixed(2);return Z(()=>{var z;return(z=_.project)==null?void 0:z.id},z=>{z&&V()}),Z(j,()=>{V()}),se(()=>{var z;(z=_.project)!=null&&z.id&&V()}),P({refresh:V}),(z,i)=>{var m,d;return t(),s("div",Kn,[e("div",qn,[e("div",Nn,[e("div",Xn,[e("div",Gn,[i[11]||(i[11]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Jn,[e("button",{onClick:r,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},i[7]||(i[7]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",Wn,o(n.value)+"/"+o(f.value),1),e("button",{onClick:w,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},i[8]||(i[8]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",Yn,[i[10]||(i[10]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":i[0]||(i[0]=a=>j.value=a),onChange:V,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[9]||(i[9]=e("option",{value:""},"Tutti i membri",-1)),(t(!0),s(L,null,K(((m=x.project)==null?void 0:m.team_members)||[],a=>(t(),s("option",{key:a.id,value:a.id},o(a.first_name)+" "+o(a.last_name),9,Qn))),128))],544),[[J,j.value]])])]),e("button",{onClick:i[1]||(i[1]=a=>H.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[12]||(i[12]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),G(" Aggiungi Ore ")]))])]),v.value?(t(),s("div",Zn,i[13]||(i[13]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):S("",!0),b.value?(t(),s("div",ei,[e("p",ti,o(b.value),1)])):S("",!0),!v.value&&$.value?(t(),s("div",si,[e("div",ri,[e("table",oi,[e("thead",ai,[e("tr",null,[i[14]||(i[14]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(t(!0),s(L,null,K(q.value,a=>(t(),s("th",{key:a,class:F(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":D(a)}])},o(a),3))),128)),i[15]||(i[15]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",ni,[(t(!0),s(L,null,K($.value.tasks,a=>(t(),s("tr",{key:a.id},[e("td",ii,[e("div",li,o(a.name),1),e("div",di,o(a.workers.length?a.workers.join(", "):"Nessuno ha lavorato"),1)]),(t(!0),s(L,null,K(q.value,O=>(t(),s("td",{key:O,class:F(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":D(O)}]),onClick:g=>B(a.id,O)},[a.daily_hours[O]>0?(t(),s("span",ci,o(R(a.daily_hours[O])),1)):(t(),s("span",gi,"-"))],10,ui))),128)),e("td",mi,[e("span",pi,o(R(a.total_hours)),1)])]))),128)),e("tr",vi,[i[16]||(i[16]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(t(!0),s(L,null,K(q.value,a=>(t(),s("td",{key:a,class:F(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":D(a)}])},o(R($.value.daily_totals[a]||0)),3))),128)),e("td",xi,o(R($.value.grand_total)),1)])])])]),$.value.tasks.length===0?(t(),s("div",yi,i[17]||(i[17]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):S("",!0)])):S("",!0)]),H.value||I.value?(t(),s("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:l},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:i[6]||(i[6]=te(()=>{},["stop"]))},[e("div",fi,[e("h3",bi,o(I.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:te(T,["prevent"])},[e("div",hi,[e("div",null,[i[19]||(i[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),E(e("select",{"onUpdate:modelValue":i[2]||(i[2]=a=>h.value.task_id=a),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[i[18]||(i[18]=e("option",{value:""},"Seleziona task",-1)),(t(!0),s(L,null,K(((d=$.value)==null?void 0:d.tasks)||[],a=>(t(),s("option",{key:a.id,value:a.id},o(a.name),9,ki))),128))],512),[[J,h.value.task_id]])]),e("div",null,[i[20]||(i[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),E(e("input",{"onUpdate:modelValue":i[3]||(i[3]=a=>h.value.date=a),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.date]])]),e("div",null,[i[21]||(i[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),E(e("input",{"onUpdate:modelValue":i[4]||(i[4]=a=>h.value.hours=a),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.hours]])]),e("div",null,[i[22]||(i[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":i[5]||(i[5]=a=>h.value.description=a),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[X,h.value.description]])])]),e("div",wi,[e("button",{type:"button",onClick:l,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:y.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(y.value?"Salvataggio...":I.value?"Aggiorna":"Aggiungi"),9,_i)])],32)])])])):S("",!0)])}}},ji={class:"space-y-6"},Ci={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Mi={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Ti={class:"flex items-center justify-between"},Si={class:"flex items-center space-x-3"},Pi=["disabled"],Ai={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},zi={key:1,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},Di={key:0,class:"px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700"},Ii={class:"flex items-start space-x-3"},Vi={class:"flex-1"},Ei={class:"text-sm font-medium text-purple-900 dark:text-purple-100"},Bi={class:"mt-2 space-y-2"},Ui={key:0,class:"mt-3"},Ri={class:"space-y-2"},Hi={class:"flex items-center space-x-3"},Oi={class:"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center"},Fi={class:"text-xs font-medium text-purple-600 dark:text-purple-300"},Li={class:"text-sm font-medium text-gray-900 dark:text-white"},Ki={class:"text-xs text-gray-500 dark:text-gray-400"},qi=["onClick"],Ni={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Xi={key:0,class:"p-6"},Gi={class:"animate-pulse space-y-4"},Ji={key:1,class:"p-6 text-center"},Wi={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},Yi={class:"flex items-center justify-between"},Qi={class:"flex items-center space-x-4"},Zi={class:"w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center"},el={class:"text-sm font-medium text-primary-600 dark:text-primary-300"},tl={class:"text-sm font-medium text-gray-900 dark:text-white"},sl={class:"text-sm text-gray-500 dark:text-gray-400"},rl={class:"flex items-center space-x-4"},ol={class:"text-right"},al={class:"text-sm font-medium text-gray-900 dark:text-white"},nl={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2"},il={class:"flex items-center space-x-2"},ll=["onClick"],dl=["onClick"],ul={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},cl={class:"p-6"},gl={class:"space-y-4"},ml={class:"w-32 text-sm text-gray-600 dark:text-gray-400"},pl={class:"flex-1 mx-4"},vl={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3"},xl={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},yl={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},fl={class:"mt-3"},bl={class:"space-y-4"},hl=["value"],kl={class:"flex justify-end space-x-3 mt-6"},wl=["disabled"],_l={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},$l={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},jl={class:"mt-3"},Cl={class:"space-y-4"},Ml={class:"flex justify-end space-x-3 mt-6"},Tl=["disabled"],Sl={__name:"ProjectResourceAllocation",props:{project:{type:Object,required:!0}},setup(x){const P=x,_=ne(),C=M(!0),$=M(!1),v=M(!1),b=M([]),y=M([]),f=M([]),n=M(null),j=M(!1),H=M(!1),I=M({user_id:"",role:"",allocation_percentage:100}),A=M({id:null,role:"",allocation_percentage:100}),h=U(()=>{var d;return(d=P.project)==null?void 0:d.id}),q=async()=>{var d;if(h.value){C.value=!0;try{const a=await fetch(`/api/resources?project_id=${h.value}`,{headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}});if(!a.ok)throw new Error("Errore nel caricamento allocazioni");const O=await a.json();b.value=((d=O.data)==null?void 0:d.resources)||[],await T()}catch(a){console.error("Error loading allocations:",a)}finally{C.value=!1}}},V=async()=>{var d;try{const a=await fetch("/api/personnel",{headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}});if(!a.ok)throw new Error("Errore nel caricamento utenti");const O=await a.json();y.value=((d=O.data)==null?void 0:d.users)||[]}catch(a){console.error("Error loading users:",a)}},T=async()=>{f.value=b.value.map(d=>({user_id:d.user_id,user_name:d.user_name,total_allocation:d.allocation_percentage+Math.floor(Math.random()*30)}))},B=async()=>{var d;if(h.value){v.value=!0;try{const a=await fetch(`/api/ai-resources/analyze-allocation/${h.value}`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({include_suggestions:!0,analysis_depth:"detailed"})});if(!a.ok)throw new Error("Errore nell'analisi AI");const O=await a.json();n.value=((d=O.data)==null?void 0:d.analysis)||null}catch(a){console.error("Error in AI analysis:",a),alert("Errore nell'analisi AI: "+a.message)}finally{v.value=!1}}},l=async()=>{$.value=!0;try{if(!(await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({project_id:h.value,...I.value})})).ok)throw new Error("Errore nell'aggiunta risorsa");await q(),j.value=!1,I.value={user_id:"",role:"",allocation_percentage:100}}catch(d){console.error("Error adding resource:",d),alert("Errore nell'aggiunta risorsa: "+d.message)}finally{$.value=!1}},r=d=>{A.value={id:d.id,role:d.role,allocation_percentage:d.allocation_percentage},H.value=!0},w=async()=>{$.value=!0;try{if(!(await fetch(`/api/resources/${A.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({role:A.value.role,allocation_percentage:A.value.allocation_percentage})})).ok)throw new Error("Errore nell'aggiornamento allocazione");await q(),H.value=!1,A.value={id:null,role:"",allocation_percentage:100}}catch(d){console.error("Error updating allocation:",d),alert("Errore nell'aggiornamento: "+d.message)}finally{$.value=!1}},D=async d=>{if(confirm("Sei sicuro di voler rimuovere questa allocazione?"))try{if(!(await fetch(`/api/resources/${d.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken}})).ok)throw new Error("Errore nella rimozione");await q()}catch(a){console.error("Error removing allocation:",a),alert("Errore nella rimozione: "+a.message)}},R=async d=>{try{await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":_.csrfToken},body:JSON.stringify({project_id:h.value,user_id:d.user_id,role:d.role,allocation_percentage:d.allocation})}),await q()}catch(a){console.error("Error applying AI recommendation:",a)}},z=d=>d>=80?"bg-red-500":d>=60?"bg-yellow-500":"bg-green-500",i=d=>d>100?"bg-red-500":d>=90?"bg-yellow-500":"bg-green-500",m=d=>d>100?"text-red-600 dark:text-red-400":d>=90?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400";return Z(()=>P.project,d=>{d&&q()},{immediate:!0}),se(()=>{V()}),(d,a)=>{var O;return t(),s("div",ji,[e("div",Ci,[e("div",Mi,[e("div",Ti,[a[12]||(a[12]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazione Risorse "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci l'allocazione delle risorse con assistenza AI ")],-1)),e("div",Si,[e("button",{onClick:B,disabled:v.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"},[v.value?(t(),s("svg",zi,a[10]||(a[10]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(t(),s("svg",Ai,a[9]||(a[9]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},null,-1)]))),G(" "+o(v.value?"Analizzando...":"Analisi AI"),1)],8,Pi),e("button",{onClick:a[0]||(a[0]=g=>j.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},a[11]||(a[11]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),G(" Aggiungi Risorsa ")]))])])]),n.value?(t(),s("div",Di,[e("div",Ii,[a[15]||(a[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])],-1)),e("div",Vi,[e("h4",Ei," Insights AI - Efficienza: "+o(n.value.efficiency_score)+"% ",1),e("div",Bi,[(t(!0),s(L,null,K(n.value.optimization_insights,g=>(t(),s("div",{key:g,class:"text-sm text-purple-700 dark:text-purple-300"}," • "+o(g),1))),128))]),(O=n.value.recommended_allocations)!=null&&O.length?(t(),s("div",Ui,[a[13]||(a[13]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"}," Raccomandazioni AI: ",-1)),e("div",Ri,[(t(!0),s(L,null,K(n.value.recommended_allocations,g=>{var u;return t(),s("div",{key:g.user_id,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3"},[e("div",Hi,[e("div",Oi,[e("span",Fi,o((u=g.user_name)==null?void 0:u.charAt(0)),1)]),e("div",null,[e("p",Li,o(g.user_name),1),e("p",Ki,o(g.role)+" - "+o(g.allocation)+"%",1)])]),e("button",{onClick:W=>R(g),class:"text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700"}," Applica ",8,qi)])}),128))])])):S("",!0)]),e("button",{onClick:a[1]||(a[1]=g=>n.value=null),class:"flex-shrink-0 text-purple-400 hover:text-purple-600"},a[14]||(a[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])):S("",!0)]),e("div",Ni,[a[20]||(a[20]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazioni Attuali ")],-1)),C.value?(t(),s("div",Xi,[e("div",Gi,[(t(),s(L,null,K(3,g=>e("div",{key:g,class:"flex items-center space-x-4"},a[16]||(a[16]=[ae('<div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div><div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div></div><div class="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>',3)]))),64))])])):b.value.length?(t(),s("div",Wi,[(t(!0),s(L,null,K(b.value,g=>{var u;return t(),s("div",{key:g.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",Yi,[e("div",Qi,[e("div",Zi,[e("span",el,o((u=g.user_name)==null?void 0:u.charAt(0)),1)]),e("div",null,[e("h4",tl,o(g.user_name),1),e("p",sl,o(g.role||"Team Member"),1)])]),e("div",rl,[e("div",ol,[e("div",al,o(g.allocation_percentage)+"% ",1),e("div",nl,[e("div",{class:F(["h-2 rounded-full",z(g.allocation_percentage)]),style:ee({width:g.allocation_percentage+"%"})},null,6)])]),e("div",il,[e("button",{onClick:W=>r(g),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},a[18]||(a[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,ll),e("button",{onClick:W=>D(g),class:"text-red-400 hover:text-red-600"},a[19]||(a[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,dl)])])])])}),128))])):(t(),s("div",Ji,a[17]||(a[17]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna risorsa allocata",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti. ",-1)])))]),e("div",ul,[a[21]||(a[21]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Utilizzo Risorse ")],-1)),e("div",cl,[e("div",gl,[(t(!0),s(L,null,K(f.value,g=>(t(),s("div",{key:g.user_id,class:"flex items-center"},[e("div",ml,o(g.user_name),1),e("div",pl,[e("div",vl,[e("div",{class:F(["h-3 rounded-full transition-all duration-300",i(g.total_allocation)]),style:ee({width:Math.min(g.total_allocation,100)+"%"})},null,6)])]),e("div",{class:F(["w-16 text-sm text-right font-medium",m(g.total_allocation)])},o(g.total_allocation)+"% ",3)]))),128))])])]),j.value?(t(),s("div",xl,[e("div",yl,[e("div",fl,[a[26]||(a[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Risorsa ",-1)),e("form",{onSubmit:te(l,["prevent"])},[e("div",bl,[e("div",null,[a[23]||(a[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Utente ",-1)),E(e("select",{"onUpdate:modelValue":a[2]||(a[2]=g=>I.value.user_id=g),required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[a[22]||(a[22]=e("option",{value:""},"Seleziona utente...",-1)),(t(!0),s(L,null,K(y.value,g=>(t(),s("option",{key:g.id,value:g.id},o(g.full_name)+" ("+o(g.role)+") ",9,hl))),128))],512),[[J,I.value.user_id]])]),e("div",null,[a[24]||(a[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":a[3]||(a[3]=g=>I.value.role=g),type:"text",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[X,I.value.role]])]),e("div",null,[a[25]||(a[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":a[4]||(a[4]=g=>I.value.allocation_percentage=g),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,I.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",kl,[e("button",{type:"button",onClick:a[5]||(a[5]=g=>j.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:$.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},o($.value?"Salvando...":"Aggiungi"),9,wl)])],32)])])])):S("",!0),H.value?(t(),s("div",_l,[e("div",$l,[e("div",jl,[a[29]||(a[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Modifica Allocazione ",-1)),e("form",{onSubmit:te(w,["prevent"])},[e("div",Cl,[e("div",null,[a[27]||(a[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":a[6]||(a[6]=g=>A.value.role=g),type:"text",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[X,A.value.role]])]),e("div",null,[a[28]||(a[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":a[7]||(a[7]=g=>A.value.allocation_percentage=g),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[X,A.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",Ml,[e("button",{type:"button",onClick:a[8]||(a[8]=g=>H.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:$.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},o($.value?"Salvando...":"Aggiorna"),9,Tl)])],32)])])])):S("",!0)])}}},Pl={class:"project-view"},Al={class:"tab-content"},zl={__name:"ProjectView",setup(x){const P=fe(),_=ne(),C=he(),$=we(),v=M(!0),b=M("overview"),y=U(()=>P.currentProject),f=U(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"resources",label:"Allocazione Risorse",icon:"user-group"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(h=>!!(["overview","tasks","gantt","team","timesheet","resources"].includes(h.id)||h.id==="kpi"&&_.hasPermission("view_reports")||h.id==="expenses"&&_.hasPermission("manage_expenses")))),n=U(()=>({overview:me,tasks:nr,team:to,resources:Sl,expenses:Ko,kpi:yn,gantt:Ln,timesheet:$i})[b.value]||me),j=async()=>{v.value=!0;try{const A=C.params.id;await P.fetchProject(A)}catch(A){console.error("Error loading project:",A)}finally{v.value=!1}},H=()=>{$.push(`/projects/${C.params.id}/edit`)},I=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await P.deleteProject(C.params.id),$.push("/projects")}catch(A){console.error("Error deleting project:",A)}};return Z(()=>C.params.id,(A,h)=>{A&&A!==h&&j()}),Z(()=>C.hash,A=>{if(A){const h=A.replace("#","");f.value.find(q=>q.id===h)&&b.value!==h&&(b.value=h)}},{immediate:!0}),Z(b,A=>{const h=`#${A}`;C.hash!==h&&$.replace({...C,hash:h})}),se(()=>{if(C.hash){const A=C.hash.replace("#","");f.value.find(h=>h.id===A)&&(b.value=A)}j()}),(A,h)=>(t(),s("div",Pl,[oe(Ue,{project:y.value,loading:v.value,onEdit:H,onDelete:I},null,8,["project","loading"]),oe(qe,{modelValue:b.value,"onUpdate:modelValue":h[0]||(h[0]=q=>b.value=q),tabs:f.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",Al,[(t(),de(ke,null,[(t(),de(ye(n.value),{project:y.value,loading:v.value},null,8,["project","loading"]))],1024))])]))}},Vl=ue(zl,[["__scopeId","data-v-de1f32e3"]]);export{Vl as default};
