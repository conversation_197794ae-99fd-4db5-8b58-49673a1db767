import{c as r,o as s,j as e,t as a,n as H,g as M,m as Y,a as oe,i as ve,b as ye,F as L,k as K,h as ce,M as xe,f as U,z as te,r as T,w as ee,A as re,v as E,G as Q,H as ae,x as G,s as se,N as be,p as ue,u as he,O as ke,l as we}from"./vendor.js";import{_ as ge,u as le,a as _e,b as fe}from"./app.js";const $e={class:"project-header bg-white shadow-sm rounded-lg p-6 mb-6"},je={key:0,class:"animate-pulse"},Ce={key:1,class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},Me={class:"flex-1"},Te={class:"flex items-center space-x-3 mb-2"},Se={class:"text-2xl font-bold text-gray-900"},Pe={class:"flex flex-wrap items-center gap-4 text-sm text-gray-500"},ze={key:0},Ae={key:1},De={key:2},Ie={key:3},Ve={class:"mt-4 sm:mt-0 flex space-x-3"},Ee={key:2,class:"text-center py-8"},Be={__name:"ProjectHeader",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["edit","delete"],setup(p){const z=v=>({planning:"bg-yellow-100 text-yellow-800",active:"bg-green-100 text-green-800",on_hold:"bg-orange-100 text-orange-800",completed:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800"})[v]||"bg-gray-100 text-gray-800",w=v=>({planning:"Pianificazione",active:"Attivo",on_hold:"In Pausa",completed:"Completato",cancelled:"Annullato"})[v]||v,j=v=>v?new Date(v).toLocaleDateString("it-IT"):"",$=v=>v?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(v):"";return(v,h)=>{const x=ye("router-link");return s(),r("div",$e,[p.loading?(s(),r("div",je,h[1]||(h[1]=[e("div",{class:"h-8 bg-gray-200 rounded w-1/3 mb-2"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1)]))):p.project?(s(),r("div",Ce,[e("div",Me,[e("div",Te,[e("h1",Se,a(p.project.name),1),e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",z(p.project.status)])},a(w(p.project.status)),3)]),e("div",Pe,[p.project.client?(s(),r("span",ze,[h[2]||(h[2]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),Y(" Cliente: "+a(p.project.client.name),1)])):M("",!0),p.project.start_date?(s(),r("span",Ae,[h[3]||(h[3]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),Y(" Inizio: "+a(j(p.project.start_date)),1)])):M("",!0),p.project.end_date?(s(),r("span",De,[h[4]||(h[4]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),Y(" Fine: "+a(j(p.project.end_date)),1)])):M("",!0),p.project.budget?(s(),r("span",Ie,[h[5]||(h[5]=e("svg",{class:"w-4 h-4 inline mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})],-1)),Y(" Budget: "+a($(p.project.budget)),1)])):M("",!0)])]),e("div",Ve,[oe(x,{to:`/app/projects/${p.project.id}/edit`,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},{default:ve(()=>h[6]||(h[6]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1),Y(" Modifica ")])),_:1,__:[6]},8,["to"]),e("button",{onClick:h[0]||(h[0]=f=>v.$emit("delete")),class:"inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"},h[7]||(h[7]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),Y(" Elimina ")]))])])):(s(),r("div",Ee,h[8]||(h[8]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},Ue=ge(Be,[["__scopeId","data-v-6f1b5cc9"]]),Re={class:"tab-navigation"},He={class:"border-b border-gray-200"},Fe={class:"-mb-px flex space-x-8","aria-label":"Tabs"},Oe=["onClick","aria-current"],Le={key:1,class:"ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600"},Ke={__name:"TabNavigation",props:{modelValue:{type:String,required:!0},tabs:{type:Array,required:!0,validator:p=>p.every(z=>typeof z=="object"&&z.id&&z.label)}},emits:["update:modelValue"],setup(p,{emit:z}){const w=p,j=z,$=x=>w.modelValue===x,v=x=>{j("update:modelValue",x)},h=x=>{const f={"chart-bar":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>`},"clipboard-list":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>`},users:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" />
      </svg>`},folder:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
      </svg>`},"trending-up":{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>`},calendar:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>`},clock:{template:`<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>`}};return f[x]||f["chart-bar"]};return(x,f)=>(s(),r("div",Re,[e("div",He,[e("nav",Fe,[(s(!0),r(L,null,K(p.tabs,l=>(s(),r("button",{key:l.id,onClick:C=>v(l.id),class:H(["whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2",$(l.id)?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"]),"aria-current":$(l.id)?"page":void 0},[l.icon?(s(),ce(xe(h(l.icon)),{key:0,class:"w-4 h-4"})):M("",!0),e("span",null,a(l.label),1),l.count!==void 0?(s(),r("span",Le,a(l.count),1)):M("",!0)],10,Oe))),128))])])]))}},qe=ge(Ke,[["__scopeId","data-v-c205976e"]]),Ne={class:"project-overview"},Xe={key:0,class:"animate-pulse space-y-4"},Je={key:1,class:"space-y-6"},Ge={class:"bg-white shadow rounded-lg p-6"},We={key:0,class:"text-gray-600"},Ye={key:1,class:"text-gray-400 italic"},Qe={key:0,class:"bg-white shadow rounded-lg p-6"},Ze={class:"flex items-center justify-between mb-4"},et={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},tt={class:"text-sm font-medium text-gray-900"},st={class:"text-sm font-medium text-gray-900"},rt={class:"text-sm font-medium text-gray-900"},ot={key:0,class:"mt-4 pt-4 border-t border-gray-200"},at={class:"grid grid-cols-2 gap-4"},nt={key:0},it={class:"text-sm font-medium text-gray-900"},lt={key:1},dt={class:"text-sm font-medium text-gray-900"},ut={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},ct={class:"bg-white shadow rounded-lg p-6"},gt={class:"flex items-center"},mt={class:"ml-5 w-0 flex-1"},pt={class:"text-lg font-medium text-gray-900"},vt={class:"bg-white shadow rounded-lg p-6"},yt={class:"flex items-center"},xt={class:"ml-5 w-0 flex-1"},ft={class:"text-lg font-medium text-gray-900"},bt={class:"bg-white shadow rounded-lg p-6"},ht={class:"flex items-center"},kt={class:"ml-5 w-0 flex-1"},wt={class:"text-lg font-medium text-gray-900"},_t={class:"bg-white shadow rounded-lg p-6"},$t={class:"flex items-center"},jt={class:"ml-5 w-0 flex-1"},Ct={class:"text-lg font-medium text-gray-900"},Mt={class:"bg-white shadow rounded-lg p-6"},Tt={class:"w-full bg-gray-200 rounded-full h-2.5"},St={class:"text-sm text-gray-500 mt-2"},Pt={class:"bg-white shadow rounded-lg p-6"},zt={class:"space-y-4"},At={class:"flex justify-between items-center"},Dt={class:"text-sm font-medium"},It={class:"flex justify-between items-center"},Vt={class:"text-sm font-medium"},Et={class:"w-full bg-gray-200 rounded-full h-3"},Bt={class:"flex justify-between items-center text-sm"},Ut={class:"bg-white shadow rounded-lg p-6"},Rt={class:"space-y-3"},Ht={class:"flex-shrink-0"},Ft=["src","alt"],Ot={key:1,class:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"},Lt={class:"text-xs font-medium text-gray-600"},Kt={class:"flex-1"},qt={class:"text-sm font-medium text-gray-900"},Nt={class:"text-xs text-gray-500"},Xt={class:"text-right"},Jt={class:"text-xs text-gray-500"},Gt={key:0,class:"text-center py-4"},Wt={class:"bg-white shadow rounded-lg p-6"},Yt={class:"space-y-3"},Qt={class:"flex-shrink-0"},Zt={class:"flex-1"},es={class:"text-sm text-gray-900"},ts={class:"flex items-center space-x-2 mt-1"},ss={class:"text-xs text-gray-500"},rs={class:"text-xs text-gray-500"},os={key:0,class:"text-center py-4"},as={key:2,class:"text-center py-8"},ns={__name:"ProjectOverview",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(p){const z=p,w=U(()=>{if(!z.project||!z.project.task_count)return 0;const c=z.project.completed_tasks||0,o=z.project.task_count||1;return Math.round(c/o*100)}),j=U(()=>{var c;return((c=z.project)==null?void 0:c.team_members)||[]}),$=U(()=>{var _,I,O;if((_=z.project)!=null&&_.expenses)return z.project.expenses;const c=((I=z.project)==null?void 0:I.total_hours)||0,o=(O=z.project)!=null&&O.client_daily_rate?z.project.client_daily_rate/8:50;return c*o}),v=U(()=>{var o;return(((o=z.project)==null?void 0:o.budget)||0)-$.value}),h=U(()=>{var o;const c=((o=z.project)==null?void 0:o.budget)||1;return Math.min(Math.round($.value/c*100),100)}),x=U(()=>{const c=h.value;return c>=90?"bg-red-600":c>=75?"bg-yellow-600":"bg-green-600"}),f=U(()=>{var o;const c=v.value;return c<0?"text-red-600":c<(((o=z.project)==null?void 0:o.budget)||0)*.1?"text-yellow-600":"text-green-600"}),l=U(()=>{var c;return(c=z.project)!=null&&c.tasks?[...z.project.tasks].sort((o,_)=>new Date(_.updated_at)-new Date(o.updated_at)).slice(0,5).map(o=>{var _;return{id:o.id,description:`Task "${o.name}" ${C(o.status)}`,created_at:o.updated_at,user_name:((_=o.assignee)==null?void 0:_.full_name)||"Non assegnato",type:F(o.status)}}):[]}),C=c=>({todo:"creato","in-progress":"in corso",review:"in revisione",done:"completato"})[c]||c,F=c=>({todo:"task_created","in-progress":"task_updated",review:"task_updated",done:"task_completed"})[c]||"task_updated",D=c=>c?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(c):"Non specificato",A=c=>c?new Date(c).toLocaleDateString("it-IT",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}):"",y=c=>c?c.split(" ").map(o=>o.charAt(0).toUpperCase()).slice(0,2).join(""):"??",q=c=>{const o={task_created:"bg-blue-600",task_completed:"bg-green-600",task_updated:"bg-yellow-600",comment_added:"bg-purple-600",file_uploaded:"bg-indigo-600",member_added:"bg-pink-600",default:"bg-gray-600"};return o[c]||o.default},V=c=>({fixed_price:"Prezzo Fisso",time_and_materials:"Tempo e Materiali",retainer:"Retainer",milestone:"Milestone"})[c]||c,S=c=>({draft:"Bozza",sent:"Inviato",signed:"Firmato",active:"Attivo",completed:"Completato",cancelled:"Annullato"})[c]||c,B=c=>({draft:"bg-gray-100 text-gray-800",sent:"bg-blue-100 text-blue-800",signed:"bg-green-100 text-green-800",active:"bg-green-100 text-green-800",completed:"bg-purple-100 text-purple-800",cancelled:"bg-red-100 text-red-800"})[c]||"bg-gray-100 text-gray-800";return(c,o)=>{const _=ye("router-link");return s(),r("div",Ne,[p.loading?(s(),r("div",Xe,o[0]||(o[0]=[e("div",{class:"h-4 bg-gray-200 rounded w-3/4"},null,-1),e("div",{class:"h-4 bg-gray-200 rounded w-1/2"},null,-1),e("div",{class:"h-32 bg-gray-200 rounded"},null,-1)]))):p.project?(s(),r("div",Je,[e("div",Ge,[o[1]||(o[1]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Descrizione Progetto",-1)),p.project.description?(s(),r("p",We,a(p.project.description),1)):(s(),r("p",Ye,"Nessuna descrizione disponibile"))]),p.project.contract?(s(),r("div",Qe,[e("div",Ze,[o[3]||(o[3]=e("h3",{class:"text-lg font-medium text-gray-900"},"Contratto Collegato",-1)),oe(_,{to:`/app/crm/contracts/${p.project.contract.id}`,class:"text-blue-600 hover:text-blue-800 text-sm font-medium"},{default:ve(()=>o[2]||(o[2]=[Y(" Gestisci Contratto → ")])),_:1,__:[2]},8,["to"])]),e("div",et,[e("div",null,[o[4]||(o[4]=e("dt",{class:"text-sm text-gray-500"},"Numero Contratto",-1)),e("dd",tt,a(p.project.contract.contract_number),1)]),e("div",null,[o[5]||(o[5]=e("dt",{class:"text-sm text-gray-500"},"Tipo",-1)),e("dd",st,a(V(p.project.contract.contract_type)),1)]),e("div",null,[o[6]||(o[6]=e("dt",{class:"text-sm text-gray-500"},"Tariffa Oraria",-1)),e("dd",rt,a(D(p.project.contract.hourly_rate))+"/h",1)]),e("div",null,[o[7]||(o[7]=e("dt",{class:"text-sm text-gray-500"},"Stato",-1)),e("dd",null,[e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",B(p.project.contract.status)])},a(S(p.project.contract.status)),3)])])]),p.project.contract.budget_hours||p.project.contract.budget_amount?(s(),r("div",ot,[e("div",at,[p.project.contract.budget_hours?(s(),r("div",nt,[o[8]||(o[8]=e("dt",{class:"text-sm text-gray-500"},"Budget Ore",-1)),e("dd",it,a(p.project.contract.budget_hours)+"h",1)])):M("",!0),p.project.contract.budget_amount?(s(),r("div",lt,[o[9]||(o[9]=e("dt",{class:"text-sm text-gray-500"},"Budget Importo",-1)),e("dd",dt,a(D(p.project.contract.budget_amount)),1)])):M("",!0)])])):M("",!0)])):M("",!0),e("div",ut,[e("div",ct,[e("div",gt,[o[11]||(o[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"})])],-1)),e("div",mt,[e("dl",null,[o[10]||(o[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Totali",-1)),e("dd",pt,a(p.project.task_count||0),1)])])])]),e("div",vt,[e("div",yt,[o[13]||(o[13]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",xt,[e("dl",null,[o[12]||(o[12]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Task Completati",-1)),e("dd",ft,a(p.project.completed_tasks||0),1)])])])]),e("div",bt,[e("div",ht,[o[15]||(o[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})])],-1)),e("div",kt,[e("dl",null,[o[14]||(o[14]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Membri Team",-1)),e("dd",wt,a(p.project.team_count||0),1)])])])]),e("div",_t,[e("div",$t,[o[17]||(o[17]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",jt,[e("dl",null,[o[16]||(o[16]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Budget",-1)),e("dd",Ct,a(D(p.project.budget)),1)])])])])]),e("div",Mt,[o[18]||(o[18]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Progresso Progetto",-1)),e("div",Tt,[e("div",{class:"bg-blue-600 h-2.5 rounded-full transition-all duration-300",style:te({width:`${w.value}%`})},null,4)]),e("p",St,a(w.value)+"% completato",1)]),e("div",Pt,[o[23]||(o[23]=e("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Budget vs Spese",-1)),e("div",zt,[e("div",At,[o[19]||(o[19]=e("span",{class:"text-sm text-gray-600"},"Budget Totale",-1)),e("span",Dt,a(D(p.project.budget)),1)]),o[22]||(o[22]=e("div",{class:"w-full bg-gray-200 rounded-full h-3"},[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:{width:"100%"}})],-1)),e("div",It,[o[20]||(o[20]=e("span",{class:"text-sm text-gray-600"},"Spese Sostenute",-1)),e("span",Vt,a(D($.value)),1)]),e("div",Et,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",x.value]),style:te({width:h.value+"%"})},null,6)]),e("div",Bt,[o[21]||(o[21]=e("span",{class:"text-gray-600"},"Rimanente",-1)),e("span",{class:H(["font-medium",f.value])},a(D(v.value)),3)])])]),e("div",Ut,[o[25]||(o[25]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Team Members"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutti")],-1)),e("div",Rt,[(s(!0),r(L,null,K(j.value,I=>(s(),r("div",{key:I.id,class:"flex items-center space-x-3"},[e("div",Ht,[I.profile_image?(s(),r("img",{key:0,src:I.profile_image,alt:I.full_name,class:"w-8 h-8 rounded-full"},null,8,Ft)):(s(),r("div",Ot,[e("span",Lt,a(y(I.full_name)),1)]))]),e("div",Kt,[e("p",qt,a(I.full_name),1),e("p",Nt,a(I.role||"Team Member"),1)]),e("div",Xt,[e("p",Jt,a(I.hours_worked||0)+"h",1)])]))),128)),j.value.length===0?(s(),r("div",Gt,o[24]||(o[24]=[e("p",{class:"text-gray-500"},"Nessun membro del team assegnato",-1)]))):M("",!0)])]),e("div",Wt,[o[28]||(o[28]=e("div",{class:"flex items-center justify-between mb-4"},[e("h3",{class:"text-lg font-medium text-gray-900"},"Attività Recenti"),e("button",{class:"text-sm text-blue-600 hover:text-blue-800"},"Visualizza tutte")],-1)),e("div",Yt,[(s(!0),r(L,null,K(l.value,I=>(s(),r("div",{key:I.id,class:"flex items-start space-x-3"},[e("div",Qt,[e("div",{class:H(["w-2 h-2 rounded-full mt-2",q(I.type)])},null,2)]),e("div",Zt,[e("p",es,a(I.description),1),e("div",ts,[e("p",ss,a(A(I.created_at)),1),o[26]||(o[26]=e("span",{class:"text-xs text-gray-400"},"•",-1)),e("p",rs,a(I.user_name),1)])])]))),128)),l.value.length===0?(s(),r("div",os,o[27]||(o[27]=[e("p",{class:"text-gray-500"},"Nessuna attività recente",-1)]))):M("",!0)])])])):(s(),r("div",as,o[29]||(o[29]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)])))])}}},me=ge(ns,[["__scopeId","data-v-e2382cbb"]]),is={class:"space-y-6"},ls={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},ds={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},us={class:"flex items-center justify-between"},cs={class:"mt-4 grid grid-cols-1 md:grid-cols-4 gap-4"},gs=["value"],ms={class:"mt-4 flex items-center justify-between"},ps={class:"flex items-center space-x-4"},vs={class:"text-sm text-gray-500 dark:text-gray-400"},ys={key:0,class:"flex justify-center py-8"},xs={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4"},fs={class:"text-red-600"},bs={key:2,class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},hs={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ks={class:"col-span-4"},ws={class:"text-sm font-medium text-gray-900 dark:text-white"},_s={key:0,class:"text-sm text-gray-500 dark:text-gray-400 truncate"},$s={class:"col-span-2"},js={key:0,class:"flex items-center"},Cs={class:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center text-sm font-medium text-gray-700"},Ms={class:"ml-2"},Ts={class:"text-sm font-medium text-gray-900 dark:text-white"},Ss={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Ps={class:"col-span-1"},zs={class:"col-span-1"},As={class:"col-span-2"},Ds={key:0,class:"text-sm text-gray-900 dark:text-white"},Is={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},Vs={class:"col-span-1"},Es={class:"text-sm text-gray-900 dark:text-white"},Bs={key:0,class:"text-gray-500"},Us={class:"col-span-1"},Rs={class:"flex items-center space-x-2"},Hs=["onClick"],Fs={key:0,class:"px-6 py-12 text-center"},Os={key:3,class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Ls={class:"flex items-center justify-between mb-4"},Ks={class:"font-medium text-gray-900 dark:text-white"},qs={class:"bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full px-2 py-1 text-xs"},Ns={class:"space-y-3"},Xs=["onClick"],Js={class:"font-medium text-sm text-gray-900 dark:text-white mb-1"},Gs={key:0,class:"text-xs text-gray-500 dark:text-gray-400 mb-2 line-clamp-2"},Ws={class:"flex items-center justify-between"},Ys={key:0,class:"h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center text-xs font-medium text-gray-700"},Qs={class:"mt-3"},Zs={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},er={class:"grid grid-cols-1 gap-4"},tr={class:"grid grid-cols-2 gap-4"},sr=["value"],rr={class:"grid grid-cols-2 gap-4"},or={class:"flex justify-end space-x-3 mt-6"},ar=["disabled"],nr={__name:"ProjectTasks",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(p,{expose:z}){const w=p,j=le(),{hasPermission:$}=_e(),v=T([]),h=T(!1),x=T(""),f=T("list"),l=T(!1),C=T({status:"",priority:"",assignee_id:"",search:""}),F=T(!1),D=T(!1),A=T(null),y=T({name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}),q=U(()=>$.value("manage_project_tasks")),V=[{value:"todo",label:"Da fare"},{value:"in-progress",label:"In corso"},{value:"review",label:"In revisione"},{value:"done",label:"Completato"}],S=async()=>{var u,d;if((u=w.project)!=null&&u.id){h.value=!0,x.value="";try{const k=new URLSearchParams({project_id:w.project.id,...C.value}),W=await fetch(`/api/tasks?${k}`,{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(!W.ok)throw new Error("Errore nel caricamento dei task");const b=await W.json();v.value=((d=b.data)==null?void 0:d.tasks)||b.tasks||[]}catch(k){x.value=k.message}finally{h.value=!1}}},B=async()=>{l.value=!0;try{const u=D.value?`/api/tasks/${A.value.id}`:"/api/tasks",d=D.value?"PUT":"POST",k={...y.value,project_id:w.project.id};if(!(await fetch(u,{method:d,headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken},body:JSON.stringify(k)})).ok)throw new Error("Errore nel salvataggio del task");await S(),o()}catch(u){x.value=u.message}finally{l.value=!1}},c=u=>{A.value=u,y.value={name:u.name,description:u.description||"",status:u.status,priority:u.priority,assignee_id:u.assignee_id||"",due_date:u.due_date?u.due_date.split("T")[0]:"",estimated_hours:u.estimated_hours},D.value=!0},o=()=>{F.value=!1,D.value=!1,A.value=null,y.value={name:"",description:"",status:"todo",priority:"medium",assignee_id:"",due_date:"",estimated_hours:null}},_=u=>v.value.filter(d=>d.status===u),I=u=>({todo:"bg-gray-100 text-gray-800","in-progress":"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",done:"bg-green-100 text-green-800"})[u]||"bg-gray-100 text-gray-800",O=u=>({todo:"Da fare","in-progress":"In corso",review:"In revisione",done:"Completato"})[u]||u,X=u=>({low:"bg-green-100 text-green-800",medium:"bg-yellow-100 text-yellow-800",high:"bg-orange-100 text-orange-800",urgent:"bg-red-100 text-red-800"})[u]||"bg-gray-100 text-gray-800",P=u=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[u]||u,n=(u,d)=>`${(u==null?void 0:u.charAt(0))||""}${(d==null?void 0:d.charAt(0))||""}`.toUpperCase(),t=u=>new Date(u).toLocaleDateString("it-IT");let i;const R=()=>{clearTimeout(i),i=setTimeout(()=>{S()},300)};return ee(()=>{var u;return(u=w.project)==null?void 0:u.id},u=>{u&&S()}),re(()=>{var u;(u=w.project)!=null&&u.id&&S()}),z({refresh:S}),(u,d)=>{var k,W;return s(),r("div",is,[e("div",ls,[e("div",ds,[e("div",us,[d[16]||(d[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Task del Progetto",-1)),q.value?(s(),r("button",{key:0,onClick:d[0]||(d[0]=b=>F.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},d[15]||(d[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Y(" Nuovo Task ")]))):M("",!0)]),e("div",cs,[e("div",null,[d[18]||(d[18]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":d[1]||(d[1]=b=>C.value.status=b),onChange:S,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},d[17]||(d[17]=[ae('<option value="">Tutti gli stati</option><option value="todo">Da fare</option><option value="in-progress">In corso</option><option value="review">In revisione</option><option value="done">Completato</option>',5)]),544),[[Q,C.value.status]])]),e("div",null,[d[20]||(d[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":d[2]||(d[2]=b=>C.value.priority=b),onChange:S,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},d[19]||(d[19]=[ae('<option value="">Tutte le priorità</option><option value="low">Bassa</option><option value="medium">Media</option><option value="high">Alta</option><option value="urgent">Urgente</option>',5)]),544),[[Q,C.value.priority]])]),e("div",null,[d[22]||(d[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":d[3]||(d[3]=b=>C.value.assignee_id=b),onChange:S,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[d[21]||(d[21]=e("option",{value:""},"Tutti",-1)),(s(!0),r(L,null,K(((k=p.project)==null?void 0:k.team_members)||[],b=>(s(),r("option",{key:b.id,value:b.id},a(b.first_name)+" "+a(b.last_name),9,gs))),128))],544),[[Q,C.value.assignee_id]])]),e("div",null,[d[23]||(d[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ricerca",-1)),E(e("input",{"onUpdate:modelValue":d[4]||(d[4]=b=>C.value.search=b),onInput:R,type:"text",placeholder:"Cerca task...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,544),[[G,C.value.search]])])]),e("div",ms,[e("div",ps,[d[24]||(d[24]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),e("button",{onClick:d[5]||(d[5]=b=>f.value="list"),class:H([f.value==="list"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Lista ",2),e("button",{onClick:d[6]||(d[6]=b=>f.value="kanban"),class:H([f.value==="kanban"?"bg-primary-100 text-primary-700":"text-gray-500 hover:text-gray-700","px-3 py-1 rounded-md text-sm font-medium"])}," Kanban ",2)]),e("div",vs,a(v.value.length)+" task trovati ",1)])])]),h.value?(s(),r("div",ys,d[25]||(d[25]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):M("",!0),x.value?(s(),r("div",xs,[e("p",fs,a(x.value),1)])):M("",!0),!h.value&&f.value==="list"?(s(),r("div",bs,[e("div",hs,[d[27]||(d[27]=ae('<div class="bg-gray-50 dark:bg-gray-700 px-6 py-3 grid grid-cols-12 gap-4 text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"><div class="col-span-4">Task</div><div class="col-span-2">Assegnatario</div><div class="col-span-1">Stato</div><div class="col-span-1">Priorità</div><div class="col-span-2">Scadenza</div><div class="col-span-1">Ore</div><div class="col-span-1">Azioni</div></div>',1)),(s(!0),r(L,null,K(v.value,b=>(s(),r("div",{key:b.id,class:"px-6 py-4 grid grid-cols-12 gap-4 items-center hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",ks,[e("div",ws,a(b.name),1),b.description?(s(),r("div",_s,a(b.description),1)):M("",!0)]),e("div",$s,[b.assignee?(s(),r("div",js,[e("div",Cs,a(n(b.assignee.first_name,b.assignee.last_name)),1),e("div",Ms,[e("div",Ts,a(b.assignee.first_name)+" "+a(b.assignee.last_name),1)])])):(s(),r("span",Ss,"Non assegnato"))]),e("div",Ps,[e("span",{class:H([I(b.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},a(O(b.status)),3)]),e("div",zs,[e("span",{class:H([X(b.priority),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},a(P(b.priority)),3)]),e("div",As,[b.due_date?(s(),r("div",Ds,a(t(b.due_date)),1)):(s(),r("span",Is,"-"))]),e("div",Vs,[e("div",Es,[Y(a(b.actual_hours||0)+"h ",1),b.estimated_hours?(s(),r("span",Bs,"/ "+a(b.estimated_hours)+"h",1)):M("",!0)])]),e("div",Us,[e("div",Rs,[e("button",{onClick:g=>c(b),class:"text-primary-600 hover:text-primary-900 text-sm"}," Modifica ",8,Hs)])])]))),128)),v.value.length===0?(s(),r("div",Fs,d[26]||(d[26]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato",-1)]))):M("",!0)])])):M("",!0),!h.value&&f.value==="kanban"?(s(),r("div",Os,[(s(),r(L,null,K(V,b=>e("div",{key:b.value,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},[e("div",Ls,[e("h4",Ks,a(b.label),1),e("span",qs,a(_(b.value).length),1)]),e("div",Ns,[(s(!0),r(L,null,K(_(b.value),g=>(s(),r("div",{key:g.id,class:"bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-600 cursor-pointer hover:shadow-md transition-shadow",onClick:m=>c(g)},[e("div",Js,a(g.name),1),g.description?(s(),r("div",Gs,a(g.description),1)):M("",!0),e("div",Ws,[e("span",{class:H([X(g.priority),"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium"])},a(P(g.priority)),3),g.assignee?(s(),r("div",Ys,a(n(g.assignee.first_name,g.assignee.last_name)),1)):M("",!0)])],8,Xs))),128))])])),64))])):M("",!0),F.value||D.value?(s(),r("div",{key:4,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:o},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:d[14]||(d[14]=se(()=>{},["stop"]))},[e("div",Qs,[e("h3",Zs,a(D.value?"Modifica Task":"Nuovo Task"),1),e("form",{onSubmit:se(B,["prevent"])},[e("div",er,[e("div",null,[d[28]||(d[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Nome",-1)),E(e("input",{"onUpdate:modelValue":d[7]||(d[7]=b=>y.value.name=b),type:"text",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[G,y.value.name]])]),e("div",null,[d[29]||(d[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":d[8]||(d[8]=b=>y.value.description=b),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[G,y.value.description]])]),e("div",tr,[e("div",null,[d[31]||(d[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),E(e("select",{"onUpdate:modelValue":d[9]||(d[9]=b=>y.value.status=b),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},d[30]||(d[30]=[e("option",{value:"todo"},"Da fare",-1),e("option",{value:"in-progress"},"In corso",-1),e("option",{value:"review"},"In revisione",-1),e("option",{value:"done"},"Completato",-1)]),512),[[Q,y.value.status]])]),e("div",null,[d[33]||(d[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Priorità",-1)),E(e("select",{"onUpdate:modelValue":d[10]||(d[10]=b=>y.value.priority=b),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},d[32]||(d[32]=[e("option",{value:"low"},"Bassa",-1),e("option",{value:"medium"},"Media",-1),e("option",{value:"high"},"Alta",-1),e("option",{value:"urgent"},"Urgente",-1)]),512),[[Q,y.value.priority]])])]),e("div",null,[d[35]||(d[35]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Assegnatario",-1)),E(e("select",{"onUpdate:modelValue":d[11]||(d[11]=b=>y.value.assignee_id=b),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[d[34]||(d[34]=e("option",{value:""},"Non assegnato",-1)),(s(!0),r(L,null,K(((W=p.project)==null?void 0:W.team_members)||[],b=>(s(),r("option",{key:b.id,value:b.id},a(b.first_name)+" "+a(b.last_name),9,sr))),128))],512),[[Q,y.value.assignee_id]])]),e("div",rr,[e("div",null,[d[36]||(d[36]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Scadenza",-1)),E(e("input",{"onUpdate:modelValue":d[12]||(d[12]=b=>y.value.due_date=b),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[G,y.value.due_date]])]),e("div",null,[d[37]||(d[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore stimate",-1)),E(e("input",{"onUpdate:modelValue":d[13]||(d[13]=b=>y.value.estimated_hours=b),type:"number",step:"0.5",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[G,y.value.estimated_hours]])])])]),e("div",or,[e("button",{type:"button",onClick:o,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:l.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},a(l.value?"Salvataggio...":D.value?"Aggiorna":"Crea"),9,ar)])],32)])])])):M("",!0)])}}},ir={class:"space-y-6"},lr={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},dr={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},ur={class:"flex items-center justify-between"},cr={class:"p-6 border-b border-gray-200 dark:border-gray-700"},gr={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},mr={class:"text-center"},pr={class:"text-2xl font-bold text-primary-600 dark:text-primary-400"},vr={class:"text-center"},yr={class:"text-2xl font-bold text-green-600"},xr={class:"text-center"},fr={class:"text-2xl font-bold text-blue-600"},br={class:"text-center"},hr={class:"text-2xl font-bold text-purple-600"},kr={class:"p-6"},wr={class:"space-y-4"},_r={class:"flex items-center justify-between"},$r={class:"flex items-center space-x-4"},jr={class:"flex-shrink-0"},Cr=["src","alt"],Mr={key:1,class:"w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Tr={class:"text-sm font-medium text-gray-600 dark:text-gray-300"},Sr={class:"flex-1"},Pr={class:"flex items-center space-x-2"},zr={class:"text-lg font-medium text-gray-900 dark:text-white"},Ar={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"},Dr={class:"text-sm text-gray-600 dark:text-gray-400"},Ir={class:"text-xs text-gray-500 dark:text-gray-500"},Vr={class:"flex items-center space-x-4"},Er={class:"text-right"},Br={class:"text-sm font-medium text-gray-900 dark:text-white"},Ur={class:"text-right"},Rr={class:"text-sm font-medium text-gray-900 dark:text-white"},Hr={class:"text-right"},Fr={class:"text-sm font-medium text-gray-900 dark:text-white"},Or={class:"flex items-center space-x-2"},Lr=["onClick"],Kr=["onClick"],qr={class:"mt-4"},Nr={class:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1"},Xr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Jr={key:0,class:"text-center py-8"},Gr={class:"mt-6"},Wr={class:"mt-3"},Yr={class:"space-y-4"},Qr=["value"],Zr={class:"flex justify-end space-x-3 mt-6"},eo=["disabled"],to={__name:"ProjectTeam",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(p,{expose:z,emit:w}){const j=p,$=le(),v=T(!1),h=T([]),x=T(!1),f=T({user_id:"",role:""}),l=U(()=>{var P;return((P=j.project)==null?void 0:P.team_members)||[]}),C=U(()=>l.value.reduce((P,n)=>P+(n.hours_worked||0),0)),F=U(()=>l.value.length===0?0:Math.round(C.value/l.value.length)),D=U(()=>l.value.filter(P=>(P.hours_worked||0)>0).length),A=P=>P?P.split(" ").map(n=>n.charAt(0).toUpperCase()).slice(0,2).join(""):"??",y=P=>{var t;return(((t=j.project)==null?void 0:t.tasks)||[]).filter(i=>i.assignee_id===P).length},q=P=>{var t;return(((t=j.project)==null?void 0:t.tasks)||[]).filter(i=>i.assignee_id===P&&i.status==="done").length},V=P=>{const n=y(P),t=q(P);return n===0?0:Math.round(t/n*100)},S=P=>{const n=V(P);return n>=80?"bg-green-600":n>=60?"bg-yellow-600":n>=40?"bg-orange-600":"bg-red-600"},B=P=>!P||P===0?"0.00":parseFloat(P).toFixed(2),c=async()=>{var P;try{const n=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(n.ok){const t=await n.json(),i=l.value.map(R=>R.id);h.value=(P=t.data)!=null&&P.users?t.data.users.filter(R=>!i.includes(R.id)):[]}}catch(n){console.error("Errore nel caricamento utenti:",n),h.value=[]}},o=async()=>{x.value=!0;try{const P=await fetch(`/api/projects/${j.project.id}/team`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken},body:JSON.stringify(f.value)});if(P.ok)X("refresh"),O();else{const n=await P.json();alert(n.message||"Errore nell'aggiunta del membro")}}catch{alert("Errore nell'aggiunta del membro")}finally{x.value=!1}},_=P=>{console.log("Edit member:",P)},I=async P=>{if(confirm(`Rimuovere ${P.full_name} dal progetto?`))try{const n=await fetch(`/api/projects/${j.project.id}/team/${P.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":$.csrfToken}});if(n.ok)X("refresh");else{const t=await n.json();alert(t.message||"Errore nella rimozione del membro")}}catch{alert("Errore nella rimozione del membro")}},O=()=>{v.value=!1,f.value={user_id:"",role:""}},X=w;return re(()=>{c()}),ee(()=>v.value,P=>{P&&c()}),ee(()=>{var P;return(P=j.project)==null?void 0:P.team_members},()=>{v.value&&c()}),z({refresh:c}),(P,n)=>(s(),r("div",ir,[e("div",lr,[e("div",dr,[e("div",ur,[n[6]||(n[6]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Team del Progetto ",-1)),e("button",{onClick:n[0]||(n[0]=t=>v.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},n[5]||(n[5]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Y(" Aggiungi Membro ")]))])]),e("div",cr,[e("div",gr,[e("div",mr,[e("div",pr,a(l.value.length),1),n[7]||(n[7]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Totali",-1))]),e("div",vr,[e("div",yr,a(C.value)+"h",1),n[8]||(n[8]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Ore Totali",-1))]),e("div",xr,[e("div",fr,a(F.value)+"h",1),n[9]||(n[9]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Media per Membro",-1))]),e("div",br,[e("div",hr,a(D.value),1),n[10]||(n[10]=e("div",{class:"text-sm text-gray-500 dark:text-gray-400"},"Membri Attivi",-1))])])]),e("div",kr,[e("div",wr,[(s(!0),r(L,null,K(l.value,t=>{var i,R;return s(),r("div",{key:t.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",_r,[e("div",$r,[e("div",jr,[t.profile_image?(s(),r("img",{key:0,src:t.profile_image,alt:t.full_name,class:"w-12 h-12 rounded-full"},null,8,Cr)):(s(),r("div",Mr,[e("span",Tr,a(A(t.full_name)),1)]))]),e("div",Sr,[e("div",Pr,[e("h4",zr,a(t.full_name),1),t.id===((i=p.project)==null?void 0:i.manager_id)?(s(),r("span",Ar," Project Manager ")):M("",!0)]),e("p",Dr,a(t.role||"Team Member"),1),e("p",Ir,a(t.email),1)])]),e("div",Vr,[e("div",Er,[e("div",Br,a(B(t.hours_worked||0))+"h",1),n[11]||(n[11]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"ore lavorate",-1))]),e("div",Ur,[e("div",Rr,a(y(t.id)),1),n[12]||(n[12]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"task assegnati",-1))]),e("div",Hr,[e("div",Fr,a(q(t.id)),1),n[13]||(n[13]=e("div",{class:"text-xs text-gray-500 dark:text-gray-400"},"completati",-1))]),e("div",Or,[e("button",{onClick:u=>_(t),class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",title:"Modifica membro"},n[14]||(n[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,Lr),t.id!==((R=p.project)==null?void 0:R.manager_id)?(s(),r("button",{key:0,onClick:u=>I(t),class:"p-1 text-gray-400 hover:text-red-600",title:"Rimuovi dal progetto"},n[15]||(n[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,Kr)):M("",!0)])])]),e("div",qr,[e("div",Nr,[n[16]||(n[16]=e("span",null,"Produttività",-1)),e("span",null,a(V(t.id))+"%",1)]),e("div",Xr,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",S(t.id)]),style:te({width:V(t.id)+"%"})},null,6)])])])}),128)),l.value.length===0?(s(),r("div",Jr,[n[18]||(n[18]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})],-1)),n[19]||(n[19]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun membro del team",-1)),n[20]||(n[20]=e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Inizia aggiungendo membri al progetto.",-1)),e("div",Gr,[e("button",{onClick:n[1]||(n[1]=t=>v.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"},n[17]||(n[17]=[e("svg",{class:"-ml-1 mr-2 h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Y(" Aggiungi primo membro ")]))])])):M("",!0)])])]),v.value?(s(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:O},[e("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:n[4]||(n[4]=se(()=>{},["stop"]))},[e("div",Wr,[n[25]||(n[25]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Membro al Team ",-1)),e("form",{onSubmit:se(o,["prevent"])},[e("div",Yr,[e("div",null,[n[22]||(n[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Utente",-1)),E(e("select",{"onUpdate:modelValue":n[2]||(n[2]=t=>f.value.user_id=t),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[n[21]||(n[21]=e("option",{value:""},"Seleziona utente",-1)),(s(!0),r(L,null,K(h.value,t=>(s(),r("option",{key:t.id,value:t.id},a(t.full_name)+" ("+a(t.email)+") ",9,Qr))),128))],512),[[Q,f.value.user_id]])]),e("div",null,[n[24]||(n[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ruolo",-1)),E(e("select",{"onUpdate:modelValue":n[3]||(n[3]=t=>f.value.role=t),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},n[23]||(n[23]=[ae('<option value="">Seleziona ruolo</option><option value="Team Member">Team Member</option><option value="Developer">Developer</option><option value="Designer">Designer</option><option value="QA Tester">QA Tester</option><option value="Business Analyst">Business Analyst</option><option value="Technical Lead">Technical Lead</option>',7)]),512),[[Q,f.value.role]])])]),e("div",Zr,[e("button",{type:"button",onClick:O,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:x.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},a(x.value?"Aggiungendo...":"Aggiungi"),9,eo)])],32)])])])):M("",!0)]))}};function so(p,z){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"})])}function pe(p,z){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 19.5Z"})])}function ro(p,z){return s(),r("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}const oo={class:"fixed inset-0 z-50 overflow-y-auto"},ao={class:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"},no={class:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"},io={class:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},lo={class:"mb-4"},uo={class:"text-lg font-medium text-gray-900 dark:text-white"},co={class:"space-y-4"},go={class:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse"},mo=["disabled"],po={key:0},vo={key:1},yo={__name:"ExpenseModal",props:{projectId:{type:[String,Number],required:!0},expense:{type:Object,default:null}},emits:["close","saved"],setup(p,{emit:z}){const w=p,j=z,$=T(!1),v=be({description:"",amount:0,category:"",billing_type:"billable",status:"pending",date:new Date().toISOString().split("T")[0],notes:"",receipt_file:null}),h=async()=>{$.value=!0;try{const f=w.expense?`/api/expenses/${w.expense.id}`:`/api/projects/${w.projectId}/expenses`,l=w.expense?"PUT":"POST";(await fetch(f,{method:l,headers:{"Content-Type":"application/json"},body:JSON.stringify(v)})).ok?j("saved"):console.error("Error saving expense")}catch(f){console.error("Error saving expense:",f)}finally{$.value=!1}},x=f=>{const l=f.target.files[0];if(l){if(l.size>5*1024*1024){alert("Il file è troppo grande. Dimensione massima: 5MB"),f.target.value="";return}v.receipt_file=l}};return re(()=>{w.expense&&Object.assign(v,{description:w.expense.description,amount:w.expense.amount,category:w.expense.category,billing_type:w.expense.billing_type||"billable",status:w.expense.status||"pending",date:w.expense.date.split("T")[0],notes:w.expense.notes||""})}),(f,l)=>(s(),r("div",oo,[e("div",ao,[e("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:l[0]||(l[0]=C=>f.$emit("close"))}),e("div",no,[e("form",{onSubmit:se(h,["prevent"])},[e("div",io,[e("div",lo,[e("h3",uo,a(p.expense?"Modifica Spesa":"Aggiungi Spesa"),1)]),e("div",co,[e("div",null,[l[9]||(l[9]=e("label",{for:"description",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Descrizione ",-1)),E(e("input",{"onUpdate:modelValue":l[1]||(l[1]=C=>v.description=C),type:"text",id:"description",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Descrizione della spesa"},null,512),[[G,v.description]])]),e("div",null,[l[10]||(l[10]=e("label",{for:"amount",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Importo (€) ",-1)),E(e("input",{"onUpdate:modelValue":l[2]||(l[2]=C=>v.amount=C),type:"number",step:"0.01",id:"amount",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"0.00"},null,512),[[G,v.amount,void 0,{number:!0}]])]),e("div",null,[l[12]||(l[12]=e("label",{for:"category",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Categoria ",-1)),E(e("select",{"onUpdate:modelValue":l[3]||(l[3]=C=>v.category=C),id:"category",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},l[11]||(l[11]=[ae('<option value="">Seleziona categoria</option><option value="licenses">📄 Licenze</option><option value="travel">✈️ Viaggi</option><option value="meals">🍽️ Pasti</option><option value="equipment">🖥️ Attrezzature</option><option value="external">🏢 Servizi Esterni</option><option value="other">📦 Altro</option>',7)]),512),[[Q,v.category]])]),e("div",null,[l[14]||(l[14]=e("label",{for:"billing_type",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Fatturazione ",-1)),E(e("select",{"onUpdate:modelValue":l[4]||(l[4]=C=>v.billing_type=C),id:"billing_type",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},l[13]||(l[13]=[e("option",{value:"billable"},"💰 Fatturabile al Cliente",-1),e("option",{value:"non-billable"},"🏢 Assorbimento Interno",-1),e("option",{value:"reimbursable"},"💳 Rimborsabile",-1)]),512),[[Q,v.billing_type]])]),e("div",null,[l[16]||(l[16]=e("label",{for:"status",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),E(e("select",{"onUpdate:modelValue":l[5]||(l[5]=C=>v.status=C),id:"status",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},l[15]||(l[15]=[e("option",{value:"pending"},"⏳ In Attesa di Approvazione",-1),e("option",{value:"approved"},"✅ Approvata",-1),e("option",{value:"rejected"},"❌ Rifiutata",-1)]),512),[[Q,v.status]])]),e("div",null,[l[17]||(l[17]=e("label",{for:"date",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data ",-1)),E(e("input",{"onUpdate:modelValue":l[6]||(l[6]=C=>v.date=C),type:"date",id:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[G,v.date]])]),e("div",null,[l[18]||(l[18]=e("label",{for:"notes",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Note (opzionale) ",-1)),E(e("textarea",{"onUpdate:modelValue":l[7]||(l[7]=C=>v.notes=C),id:"notes",rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"Note aggiuntive..."},null,512),[[G,v.notes]])]),e("div",null,[l[19]||(l[19]=e("label",{for:"receipt",class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Ricevuta/Scontrino ",-1)),e("input",{type:"file",id:"receipt",accept:"image/*,.pdf",onChange:x,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100"},null,32),l[20]||(l[20]=e("p",{class:"mt-1 text-xs text-gray-500 dark:text-gray-400"}," Carica immagine o PDF della ricevuta (max 5MB) ",-1))])])]),e("div",go,[e("button",{type:"submit",disabled:$.value,class:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"},[$.value?(s(),r("span",po,"Salvando...")):(s(),r("span",vo,a(p.expense?"Aggiorna":"Salva"),1))],8,mo),e("button",{type:"button",onClick:l[8]||(l[8]=C=>f.$emit("close")),class:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"}," Annulla ")])],32)])])]))}},xo={class:"project-expenses"},fo={class:"space-y-6"},bo={class:"flex justify-between items-center"},ho={key:0,class:"text-center py-8"},ko={key:1,class:"text-center py-12"},wo={key:2,class:"bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md"},_o={class:"divide-y divide-gray-200 dark:divide-gray-700"},$o={class:"flex items-center justify-between"},jo={class:"flex-1"},Co={class:"flex items-center"},Mo={class:"flex-shrink-0"},To={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},So={class:"ml-4 flex-1"},Po={class:"flex items-center justify-between"},zo={class:"text-sm font-medium text-gray-900 dark:text-white"},Ao={class:"ml-2 flex-shrink-0"},Do={class:"text-sm font-medium text-gray-900 dark:text-white"},Io={class:"mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400"},Vo={class:"capitalize"},Eo={key:0,class:"mx-2"},Bo={key:1},Uo={key:0,class:"flex items-center space-x-2"},Ro=["onClick"],Ho=["onClick"],Fo={key:3,class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4"},Oo={class:"flex justify-between items-center"},Lo={class:"text-lg font-bold text-gray-900 dark:text-white"},Ko={__name:"ProjectExpenses",props:{project:{type:Object,required:!0},loading:{type:Boolean,default:!1}},setup(p){const z=p;fe();const w=le(),j=T(!1),$=T([]),v=T(!1),h=T(null),x=U(()=>w.hasPermission("manage_expenses")),f=U(()=>$.value.reduce((V,S)=>V+S.amount,0)),l=async()=>{var V;if((V=z.project)!=null&&V.id){j.value=!0;try{const S=await fetch(`/api/projects/${z.project.id}/expenses`);S.ok&&($.value=await S.json())}catch(S){console.error("Error loading expenses:",S)}finally{j.value=!1}}},C=V=>{h.value=V,v.value=!0},F=async V=>{if(confirm("Sei sicuro di voler eliminare questa spesa?"))try{(await fetch(`/api/expenses/${V}`,{method:"DELETE"})).ok&&($.value=$.value.filter(B=>B.id!==V))}catch(S){console.error("Error deleting expense:",S)}},D=()=>{v.value=!1,h.value=null},A=()=>{D(),l()},y=V=>new Intl.NumberFormat("it-IT",{minimumFractionDigits:2,maximumFractionDigits:2}).format(V),q=V=>new Date(V).toLocaleDateString("it-IT");return re(()=>{l()}),(V,S)=>(s(),r("div",xo,[e("div",fo,[e("div",bo,[S[2]||(S[2]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Spese Progetto",-1)),x.value?(s(),r("button",{key:0,onClick:S[0]||(S[0]=B=>v.value=!0),class:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},[oe(ue(ro),{class:"w-4 h-4 mr-2"}),S[1]||(S[1]=Y(" Aggiungi Spesa "))])):M("",!0)]),j.value?(s(),r("div",ho,S[3]||(S[3]=[e("div",{class:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1),e("p",{class:"mt-2 text-sm text-gray-500"},"Caricamento spese...",-1)]))):$.value.length===0?(s(),r("div",ko,[oe(ue(pe),{class:"mx-auto h-12 w-12 text-gray-400"}),S[4]||(S[4]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna spesa",-1)),S[5]||(S[5]=e("p",{class:"mt-1 text-sm text-gray-500"},"Non ci sono ancora spese registrate per questo progetto.",-1))])):(s(),r("div",wo,[e("ul",_o,[(s(!0),r(L,null,K($.value,B=>(s(),r("li",{key:B.id,class:"px-6 py-4"},[e("div",$o,[e("div",jo,[e("div",Co,[e("div",Mo,[e("div",To,[oe(ue(pe),{class:"h-5 w-5 text-gray-600 dark:text-gray-300"})])]),e("div",So,[e("div",Po,[e("p",zo,a(B.description),1),e("div",Ao,[e("p",Do," €"+a(y(B.amount)),1)])]),e("div",Io,[oe(ue(so),{class:"flex-shrink-0 mr-1.5 h-4 w-4"}),Y(" "+a(q(B.date))+" ",1),S[6]||(S[6]=e("span",{class:"mx-2"},"•",-1)),e("span",Vo,a(B.category),1),B.user?(s(),r("span",Eo,"•")):M("",!0),B.user?(s(),r("span",Bo,a(B.user.name),1)):M("",!0)])])])]),x.value?(s(),r("div",Uo,[e("button",{onClick:c=>C(B),class:"text-primary-600 hover:text-primary-900 text-sm font-medium"}," Modifica ",8,Ro),e("button",{onClick:c=>F(B.id),class:"text-red-600 hover:text-red-900 text-sm font-medium"}," Elimina ",8,Ho)])):M("",!0)])]))),128))])])),$.value.length>0?(s(),r("div",Fo,[e("div",Oo,[S[7]||(S[7]=e("span",{class:"text-sm font-medium text-gray-900 dark:text-white"},"Totale Spese:",-1)),e("span",Lo,"€"+a(y(f.value)),1)])])):M("",!0)]),v.value?(s(),ce(yo,{key:0,"project-id":V.projectId,expense:h.value,onClose:D,onSaved:A},null,8,["project-id","expense"])):M("",!0)]))}},qo={class:"project-kpi"},No={key:0,class:"animate-pulse space-y-4"},Xo={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Jo={key:1,class:"space-y-6"},Go={class:"bg-white shadow rounded-lg p-6"},Wo={class:"flex items-center justify-between"},Yo=["disabled"],Qo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},Zo={class:"bg-white shadow rounded-lg p-6"},ea={class:"flex items-center"},ta={class:"ml-5 w-0 flex-1"},sa={class:"text-lg font-medium text-gray-900"},ra={class:"text-xs text-gray-500"},oa={class:"bg-white shadow rounded-lg p-6"},aa={class:"flex items-center"},na={class:"ml-5 w-0 flex-1"},ia={class:"text-lg font-medium text-gray-900"},la={class:"bg-white shadow rounded-lg p-6"},da={class:"flex items-center"},ua={class:"ml-5 w-0 flex-1"},ca={class:"text-lg font-medium text-gray-900"},ga={class:"text-xs text-gray-500"},ma={class:"bg-white shadow rounded-lg p-6"},pa={class:"flex items-center"},va={class:"ml-5 w-0 flex-1"},ya={class:"text-lg font-medium text-gray-900"},xa={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},fa={class:"bg-white shadow rounded-lg p-6"},ba={class:"space-y-4"},ha={class:"flex justify-between text-sm"},ka={class:"font-medium"},wa={class:"w-full bg-gray-200 rounded-full h-3"},_a={class:"flex justify-between text-sm"},$a={class:"text-gray-600"},ja={class:"font-medium"},Ca={class:"bg-white shadow rounded-lg p-6"},Ma={class:"space-y-4"},Ta={class:"flex justify-between text-sm"},Sa={class:"font-medium"},Pa={class:"w-full bg-gray-200 rounded-full h-3"},za={class:"flex justify-between text-sm"},Aa={class:"text-gray-600"},Da={class:"font-medium"},Ia={class:"bg-white shadow rounded-lg p-6"},Va={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ea={class:"text-center p-4 border rounded-lg"},Ba={class:"text-xs text-gray-500"},Ua={class:"text-center p-4 border rounded-lg"},Ra={class:"text-xs text-gray-500"},Ha={class:"text-center p-4 border rounded-lg"},Fa={class:"text-xs text-gray-500"},Oa={key:2,class:"text-center py-8"},La={key:3,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Ka={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white"},qa={class:"mt-3"},Na={class:"mt-6 space-y-6"},Xa={class:"bg-gray-50 p-4 rounded-lg"},Ja={class:"font-medium text-gray-900"},Ga={class:"text-sm text-gray-600"},Wa={class:"space-y-6"},Ya={class:"flex items-center justify-between mb-4"},Qa={class:"font-medium text-gray-900"},Za={class:"text-sm text-gray-600"},en={class:"flex items-center space-x-2"},tn={class:"text-xs text-gray-500"},sn=["onClick"],rn={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},on=["onUpdate:modelValue","onInput"],an=["onUpdate:modelValue","onInput"],nn=["onUpdate:modelValue","onInput"],ln={class:"mt-4"},dn=["onUpdate:modelValue","onInput"],un={class:"mt-4 flex justify-end"},cn=["onClick","disabled"],gn={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24"},mn={key:1,class:"text-sm text-green-600"},pn={class:"mt-6 pt-4 border-t flex justify-between"},vn={class:"flex space-x-3"},yn=["disabled"],xn={__name:"ProjectKPI",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},emits:["refresh"],setup(p,{emit:z}){const w=p,j=z,$=T(!1),v=T(!1),h=T(null),x=T({}),f=T({totalHours:0,workDays:0,totalCosts:0,costVariance:0,potentialRevenue:0,actualRevenue:0,marginPercentage:0}),l=T({budget:80,time:85,margin:15}),C=U(()=>{var g;return!((g=w.project)!=null&&g.budget)||f.value.totalCosts===0?0:Math.round(f.value.totalCosts/w.project.budget*100)}),F=U(()=>{var g;return!((g=w.project)!=null&&g.estimated_hours)||f.value.totalHours===0?0:Math.round(f.value.totalHours/w.project.estimated_hours*100)}),D=U(()=>{const g=f.value.costVariance;return g>0?"text-red-600":g<0?"text-green-600":"text-gray-600"}),A=U(()=>{const g=f.value.marginPercentage;return g>=l.value.margin?"text-green-600":g>=l.value.margin*.7?"text-yellow-600":"text-red-600"}),y=U(()=>{const g=f.value.marginPercentage;return g>=l.value.margin?"Ottimo":g>=l.value.margin*.7?"Accettabile":"Critico"}),q=U(()=>{const g=C.value;return g>=l.value.budget?"text-red-600":g>=l.value.budget*.8?"text-yellow-600":"text-green-600"}),V=U(()=>{const g=F.value;return g>=l.value.time?"text-red-600":g>=l.value.time*.8?"text-yellow-600":"text-green-600"}),S=U(()=>{const g=f.value.marginPercentage;return g>=l.value.margin?"text-green-600":g>=l.value.margin*.7?"text-yellow-600":"text-red-600"}),B=g=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(g||0),c=g=>!g||g===0?"0h":`${parseFloat(g).toFixed(2)}h`,o=g=>`${(g||0).toFixed(1)}%`,_=async()=>{var g;(g=w.project)!=null&&g.id&&I()},I=()=>{const g=w.project;g&&(f.value={totalHours:g.total_hours||0,workDays:Math.ceil((g.total_hours||0)/8),totalCosts:(g.total_hours||0)*50,costVariance:(g.total_hours||0)*50-(g.budget||0),potentialRevenue:g.budget||0,actualRevenue:g.invoiced_amount||0,marginPercentage:g.budget?(g.budget-(g.total_hours||0)*50)/g.budget*100:0})},O=async()=>{$.value=!0;try{await _(),j("refresh")}catch(g){console.error("Error refreshing KPIs:",g)}finally{$.value=!1}},X=U(()=>{var m;const g=((m=w.project)==null?void 0:m.project_type)||"service";return P(g)}),P=g=>{const m={service:[{name:"margin_percentage",display_name:"Margine Netto %",description:"Percentuale di margine netto sul fatturato",unit:"%",target_min:25,target_max:40,warning_threshold:15},{name:"utilization_rate",display_name:"Tasso di Utilizzo %",description:"Percentuale di utilizzo del team rispetto alla capacità teorica",unit:"%",target_min:75,target_max:85,warning_threshold:60},{name:"cost_per_hour",display_name:"Costo per Ora",description:"Costo medio per ora di lavoro, inclusi tutti i costi",unit:"€",target_min:30,target_max:50,warning_threshold:60},{name:"cost_revenue_ratio",display_name:"Rapporto C/R",description:"Rapporto tra costi sostenuti e ricavi generati",unit:"ratio",target_min:.6,target_max:.75,warning_threshold:.85}]};return m[g]||m.service},n=g=>({service:"🔧 Servizio",license:"📄 Licenza",consulting:"💼 Consulenza",product:"📦 Prodotto",rd:"🔬 R&D",internal:"🏢 Interno"})[g]||"Sconosciuto",t=()=>{X.value.forEach(m=>{x.value[m.name]||(x.value[m.name]={target_min:m.target_min,target_max:m.target_max,warning_threshold:m.warning_threshold,custom_description:"",isDirty:!1,isSaved:!1})}),v.value=!0},i=()=>{v.value=!1},R=g=>{x.value[g]&&(x.value[g].isDirty=!0,x.value[g].isSaved=!1)},u=g=>{const m=X.value.find(J=>J.name===g);m&&x.value[g]&&(x.value[g].target_min=m.target_min,x.value[g].target_max=m.target_max,x.value[g].warning_threshold=m.warning_threshold,x.value[g].custom_description="",x.value[g].isDirty=!0,x.value[g].isSaved=!1)},d=()=>{confirm("Sei sicuro di voler ripristinare tutti i KPI ai valori di default?")&&X.value.forEach(g=>{u(g.name)})},k=async g=>{var m;if(x.value[g]){h.value=g;try{const J=x.value[g];await new Promise(de=>setTimeout(de,1e3)),console.log("Saving KPI config:",{project_id:(m=w.project)==null?void 0:m.id,kpi_name:g,target_min:J.target_min,target_max:J.target_max,warning_threshold:J.warning_threshold,custom_description:J.custom_description}),x.value[g].isDirty=!1,x.value[g].isSaved=!0,setTimeout(()=>{x.value[g]&&(x.value[g].isSaved=!1)},3e3)}catch(J){console.error("Error saving KPI config:",J),alert("Errore nel salvataggio della configurazione KPI")}finally{h.value=null}}},W=async()=>{const g=X.value.filter(m=>{var J;return(J=x.value[m.name])==null?void 0:J.isDirty});for(const m of g)await k(m.name)},b=U(()=>X.value.some(g=>{var m;return(m=x.value[g.name])==null?void 0:m.isDirty}));return ee(()=>w.project,g=>{g&&_()},{immediate:!0}),re(()=>{w.project&&_()}),(g,m)=>{var J,de;return s(),r("div",qo,[p.loading?(s(),r("div",No,[e("div",Xo,[(s(),r(L,null,K(4,N=>e("div",{key:N,class:"bg-gray-200 rounded-lg h-24"})),64))]),m[0]||(m[0]=e("div",{class:"bg-gray-200 rounded-lg h-64"},null,-1))])):p.project?(s(),r("div",Jo,[e("div",Go,[e("div",Wo,[m[3]||(m[3]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900"},"KPI Progetto"),e("p",{class:"text-sm text-gray-600"},"Dashboard metriche e performance del progetto")],-1)),e("button",{onClick:O,disabled:$.value,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"},[(s(),r("svg",{class:H(["w-4 h-4 mr-2",{"animate-spin":$.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},m[1]||(m[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"},null,-1)]),2)),m[2]||(m[2]=Y(" Aggiorna "))],8,Yo)])]),e("div",Qo,[e("div",Zo,[e("div",ea,[m[5]||(m[5]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",ta,[e("dl",null,[m[4]||(m[4]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ore Totali",-1)),e("dd",sa,a(c(f.value.totalHours)),1),e("dd",ra,a(f.value.workDays)+" giorni lavorati",1)])])])]),e("div",oa,[e("div",aa,[m[7]||(m[7]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])],-1)),e("div",na,[e("dl",null,[m[6]||(m[6]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Costi Totali",-1)),e("dd",ia,a(B(f.value.totalCosts)),1),e("dd",{class:H(["text-xs",D.value])},a(B(f.value.costVariance))+" vs budget",3)])])])]),e("div",la,[e("div",da,[m[9]||(m[9]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])],-1)),e("div",ua,[e("dl",null,[m[8]||(m[8]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Ricavi Potenziali",-1)),e("dd",ca,a(B(f.value.potentialRevenue)),1),e("dd",ga,a(B(f.value.actualRevenue))+" fatturati",1)])])])]),e("div",ma,[e("div",pa,[m[11]||(m[11]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-8 w-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",va,[e("dl",null,[m[10]||(m[10]=e("dt",{class:"text-sm font-medium text-gray-500 truncate"},"Margine",-1)),e("dd",ya,a(o(f.value.marginPercentage)),1),e("dd",{class:H(["text-xs",A.value])},a(y.value),3)])])])])]),e("div",xa,[e("div",fa,[m[13]||(m[13]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Budget",-1)),e("div",ba,[e("div",ha,[m[12]||(m[12]=e("span",{class:"text-gray-600"},"Budget Totale",-1)),e("span",ka,a(B(p.project.budget||0)),1)]),e("div",wa,[e("div",{class:"bg-blue-600 h-3 rounded-full transition-all duration-300",style:te({width:C.value+"%"})},null,4)]),e("div",_a,[e("span",$a,"Utilizzato: "+a(B(f.value.totalCosts)),1),e("span",ja,a(C.value)+"%",1)])])]),e("div",Ca,[m[15]||(m[15]=e("h4",{class:"text-lg font-medium text-gray-900 mb-4"},"Andamento Tempo",-1)),e("div",Ma,[e("div",Ta,[m[14]||(m[14]=e("span",{class:"text-gray-600"},"Ore Stimate",-1)),e("span",Sa,a(c(p.project.estimated_hours||0)),1)]),e("div",Pa,[e("div",{class:"bg-green-600 h-3 rounded-full transition-all duration-300",style:te({width:F.value+"%"})},null,4)]),e("div",za,[e("span",Aa,"Lavorate: "+a(c(f.value.totalHours)),1),e("span",Da,a(F.value)+"%",1)])])])]),e("div",Ia,[e("div",{class:"flex items-center justify-between mb-4"},[m[17]||(m[17]=e("h4",{class:"text-lg font-medium text-gray-900"},"Soglie KPI",-1)),e("button",{onClick:t,class:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"},m[16]||(m[16]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})],-1),Y(" Configura KPI ")]))]),e("div",Va,[e("div",Ea,[e("div",{class:H(["text-2xl font-bold",q.value])},a(C.value)+"% ",3),m[18]||(m[18]=e("div",{class:"text-sm text-gray-600"},"Budget Usage",-1)),e("div",Ba,"Soglia: "+a(l.value.budget)+"%",1)]),e("div",Ua,[e("div",{class:H(["text-2xl font-bold",V.value])},a(F.value)+"% ",3),m[19]||(m[19]=e("div",{class:"text-sm text-gray-600"},"Time Usage",-1)),e("div",Ra,"Soglia: "+a(l.value.time)+"%",1)]),e("div",Ha,[e("div",{class:H(["text-2xl font-bold",S.value])},a(o(f.value.marginPercentage)),3),m[20]||(m[20]=e("div",{class:"text-sm text-gray-600"},"Margine",-1)),e("div",Fa,"Soglia: "+a(l.value.margin)+"%",1)])])])])):(s(),r("div",Oa,m[21]||(m[21]=[e("p",{class:"text-gray-500"},"Progetto non trovato",-1)]))),v.value?(s(),r("div",La,[e("div",Ka,[e("div",qa,[e("div",{class:"flex items-center justify-between pb-4 border-b"},[m[23]||(m[23]=e("h3",{class:"text-lg font-medium text-gray-900"},"Configurazione KPI Progetto",-1)),e("button",{onClick:i,class:"text-gray-400 hover:text-gray-600"},m[22]||(m[22]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Na,[e("div",Xa,[e("h4",Ja,a((J=p.project)==null?void 0:J.name),1),e("p",Ga,"Tipo: "+a(n((de=p.project)==null?void 0:de.project_type)),1)]),e("div",Wa,[(s(!0),r(L,null,K(X.value,N=>{var ne,ie;return s(),r("div",{key:N.name,class:"border border-gray-200 rounded-lg p-4"},[e("div",Ya,[e("div",null,[e("h5",Qa,a(N.display_name),1),e("p",Za,a(N.description),1)]),e("div",en,[e("span",tn,a(N.unit),1),e("button",{onClick:Z=>u(N.name),class:"text-xs text-blue-600 hover:text-blue-800",title:"Reset ai valori di default"}," Reset ",8,sn)])]),e("div",rn,[e("div",null,[m[24]||(m[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Minimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Z=>x.value[N.name].target_min=Z,onInput:Z=>R(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,on),[[G,x.value[N.name].target_min]])]),e("div",null,[m[25]||(m[25]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Target Massimo",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Z=>x.value[N.name].target_max=Z,onInput:Z=>R(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,an),[[G,x.value[N.name].target_max]])]),e("div",null,[m[26]||(m[26]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Soglia Warning",-1)),E(e("input",{type:"number",step:"0.1","onUpdate:modelValue":Z=>x.value[N.name].warning_threshold=Z,onInput:Z=>R(N.name),class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm"},null,40,nn),[[G,x.value[N.name].warning_threshold]])])]),e("div",ln,[m[27]||(m[27]=e("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"Descrizione Personalizzata",-1)),E(e("textarea",{"onUpdate:modelValue":Z=>x.value[N.name].custom_description=Z,onInput:Z=>R(N.name),rows:"2",class:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm",placeholder:"Descrizione specifica per questo progetto..."},null,40,dn),[[G,x.value[N.name].custom_description]])]),e("div",un,[(ne=x.value[N.name])!=null&&ne.isDirty?(s(),r("button",{key:0,onClick:Z=>k(N.name),disabled:h.value===N.name,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"},[h.value===N.name?(s(),r("svg",gn,m[28]||(m[28]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):M("",!0),Y(" "+a(h.value===N.name?"Salvataggio...":"Salva KPI"),1)],8,cn)):(ie=x.value[N.name])!=null&&ie.isSaved?(s(),r("span",mn,"✓ Salvato")):M("",!0)])])}),128))])]),e("div",pn,[e("button",{onClick:d,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Reset Tutti "),e("div",vn,[e("button",{onClick:i,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"}," Chiudi "),e("button",{onClick:W,disabled:!b.value,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"}," Salva Tutto ",8,yn)])])])])])):M("",!0)])}}},fn={class:"space-y-6"},bn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},hn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},kn={class:"flex items-center justify-between"},wn={class:"flex items-center space-x-4"},_n={class:"flex items-center space-x-2"},$n={key:0,class:"p-6"},jn={class:"overflow-x-auto"},Cn={class:"min-w-[1000px]"},Mn={class:"flex mb-4"},Tn={class:"flex-1 flex"},Sn={class:"space-y-1"},Pn={class:"w-80 flex-shrink-0 px-4 py-3"},zn={class:"flex items-center space-x-2"},An={class:"flex-1 min-w-0"},Dn={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},In={class:"flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400"},Vn={key:0},En={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},Bn={class:"flex-1 relative h-12 flex items-center"},Un=["title"],Rn={class:"truncate"},Hn={class:"ml-2"},Fn={key:1,class:"text-center py-12"},On={key:2,class:"flex justify-center py-12"},Ln={__name:"ProjectGantt",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(p,{expose:z}){const w=p,j=T("weeks"),$=T(new Date),v=T([]),h=U(()=>{var c;return((c=w.project)==null?void 0:c.tasks)||[]}),x=U(()=>h.value.filter(c=>c.start_date&&c.due_date).map(c=>{const o=l(c);return{...c,timeline:o}})),f=()=>{const c=new Date($.value),o=[],_=12;for(let I=0;I<_;I++){const O=new Date(c);j.value==="weeks"?O.setDate(c.getDate()+I*7):j.value==="months"&&O.setMonth(c.getMonth()+I),o.push(O)}v.value=o},l=c=>{if(!v.value.length)return null;const o=new Date(c.start_date),_=new Date(c.due_date),I=v.value[0],X=v.value[v.value.length-1]-I,P=o-I,n=_-o,t=Math.max(0,P/X*100),i=Math.min(100-t,n/X*100);return{leftPercent:t,widthPercent:Math.max(5,i)}},C=c=>j.value==="weeks"?`${c.getDate()}/${c.getMonth()+1}`:j.value==="months"?c.toLocaleDateString("it-IT",{month:"short",year:"2-digit"}):"",F=c=>{const o=new Date,_=new Date(c);if(j.value==="weeks"){const I=new Date(_),O=new Date(_);return O.setDate(O.getDate()+6),o>=I&&o<=O}else if(j.value==="months")return _.getMonth()===o.getMonth()&&_.getFullYear()===o.getFullYear();return!1},D=()=>{const c=new Date;if(j.value==="weeks"){const o=new Date(c);o.setDate(c.getDate()-c.getDay()),$.value=o}else{const o=new Date(c.getFullYear(),c.getMonth(),1);$.value=o}f()},A=c=>({todo:"bg-gray-400","in-progress":"bg-blue-500",review:"bg-yellow-500",done:"bg-green-500"})[c]||"bg-gray-400",y=c=>({todo:"bg-gray-500","in-progress":"bg-blue-600",review:"bg-yellow-600",done:"bg-green-600"})[c]||"bg-gray-500",q=c=>({low:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",medium:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",high:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",urgent:"bg-red-200 text-red-900 dark:bg-red-800 dark:text-red-100"})[c]||"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200",V=c=>({low:"Bassa",medium:"Media",high:"Alta",urgent:"Urgente"})[c]||"Non specificata",S=c=>({todo:0,"in-progress":50,review:75,done:100})[c.status]||0,B=c=>c?new Date(c).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit"}):"";return ee(()=>w.project,()=>{f()},{immediate:!0}),re(()=>{D()}),z({refresh:f}),(c,o)=>(s(),r("div",fn,[e("div",bn,[e("div",hn,[e("div",kn,[o[3]||(o[3]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Diagramma di Gantt ",-1)),e("div",wn,[e("div",_n,[o[2]||(o[2]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":o[0]||(o[0]=_=>j.value=_),onChange:f,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},o[1]||(o[1]=[e("option",{value:"weeks"},"Settimane",-1),e("option",{value:"months"},"Mesi",-1)]),544),[[Q,j.value]])]),e("button",{onClick:D,class:"px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded-md hover:bg-primary-200"}," Oggi ")])])]),!p.loading&&x.value.length>0?(s(),r("div",$n,[e("div",jn,[e("div",Cn,[e("div",Mn,[o[4]||(o[4]=e("div",{class:"w-80 flex-shrink-0 px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"}," Task ",-1)),e("div",Tn,[(s(!0),r(L,null,K(v.value,(_,I)=>(s(),r("div",{key:I,class:H(["flex-1 text-xs text-center text-gray-500 dark:text-gray-400 py-2 border-l border-gray-200 dark:border-gray-600",{"bg-blue-50 dark:bg-blue-900":F(_)}])},a(C(_)),3))),128))])]),e("div",Sn,[(s(!0),r(L,null,K(x.value,_=>(s(),r("div",{key:_.id,class:"flex items-center hover:bg-gray-50 dark:hover:bg-gray-700 rounded"},[e("div",Pn,[e("div",zn,[e("div",{class:H(["w-3 h-3 rounded-full",A(_.status)])},null,2),e("div",An,[e("p",Dn,a(_.name),1),e("div",In,[_.assignee?(s(),r("span",Vn,a(_.assignee.full_name),1)):M("",!0),e("span",{class:H(["inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium",q(_.priority)])},a(V(_.priority)),3)]),e("div",En,a(B(_.start_date))+" - "+a(B(_.due_date)),1)])])]),e("div",Bn,[_.timeline?(s(),r("div",{key:0,class:H(["absolute h-6 rounded-md flex items-center justify-between px-2 text-xs text-white font-medium shadow-sm cursor-pointer",y(_.status)]),style:te({left:_.timeline.leftPercent+"%",width:_.timeline.widthPercent+"%",minWidth:"60px"}),title:`${_.name} - ${S(_)}% completato`},[e("span",Rn,a(_.name.length>15?_.name.substring(0,15)+"...":_.name),1),e("span",Hn,a(S(_))+"%",1)],14,Un)):M("",!0),_.timeline&&S(_)>0&&S(_)<100?(s(),r("div",{key:1,class:"absolute h-6 rounded-md bg-green-600 opacity-80",style:te({left:_.timeline.leftPercent+"%",width:_.timeline.widthPercent*S(_)/100+"%",minWidth:"2px"})},null,4)):M("",!0),(s(!0),r(L,null,K(v.value,(I,O)=>(s(),r("div",{key:O,class:"absolute top-0 bottom-0 border-l border-gray-200 dark:border-gray-600",style:te({left:O/v.value.length*100+"%"})},null,4))),128))])]))),128))])])]),o[5]||(o[5]=ae('<div class="mt-6 flex items-center space-x-6 text-xs"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-gray-400 rounded"></div><span class="text-gray-600 dark:text-gray-400">Da fare</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-blue-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In corso</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-yellow-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">In revisione</span></div><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-green-500 rounded"></div><span class="text-gray-600 dark:text-gray-400">Completato</span></div><div class="flex items-center space-x-2"><div class="w-0.5 h-4 bg-red-500"></div><span class="text-gray-600 dark:text-gray-400">Oggi</span></div></div>',1))])):p.loading?M("",!0):(s(),r("div",Fn,o[6]||(o[6]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task pianificato",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"I task con date di inizio e fine appariranno nel diagramma di Gantt.",-1)]))),p.loading?(s(),r("div",On,o[7]||(o[7]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):M("",!0)])]))}},Kn={class:"space-y-6"},qn={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Nn={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Xn={class:"flex items-center justify-between"},Jn={class:"flex items-center space-x-4"},Gn={class:"flex items-center space-x-2"},Wn={class:"text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center"},Yn={class:"flex items-center space-x-2"},Qn=["value"],Zn={key:0,class:"flex justify-center py-8"},ei={key:1,class:"bg-red-50 border border-red-200 rounded-md p-4 m-6"},ti={class:"text-red-600"},si={key:2,class:"p-6"},ri={class:"overflow-x-auto"},oi={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ai={class:"bg-gray-50 dark:bg-gray-700"},ni={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ii={class:"px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700"},li={class:"text-sm font-medium text-gray-900 dark:text-white"},di={class:"text-xs text-gray-500 dark:text-gray-400"},ui=["onClick"],ci={key:0,class:"flex flex-col items-center"},gi={class:"text-xs font-medium text-primary-600 dark:text-primary-400"},mi={key:0,class:"flex space-x-1 mt-1"},pi=["title"],vi={key:1,class:"text-gray-300 dark:text-gray-600"},yi={class:"px-3 py-3 text-center bg-gray-50 dark:bg-gray-700"},xi={class:"text-sm font-medium text-gray-900 dark:text-white"},fi={class:"bg-gray-100 dark:bg-gray-600 font-medium"},bi={class:"px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600"},hi={key:0,class:"text-center py-8"},ki={key:1,class:"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},wi={class:"mt-3"},_i={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},$i={class:"grid grid-cols-1 gap-4"},ji=["value"],Ci={key:0,class:"border-t border-gray-200 dark:border-gray-600 pt-4"},Mi={class:"grid grid-cols-2 gap-4"},Ti={key:0},Si=["placeholder"],Pi={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},zi={class:"flex justify-end space-x-3 mt-6"},Ai=["disabled"],Di={__name:"ProjectTimesheet",props:{project:{type:Object,default:null},loading:{type:Boolean,default:!1}},setup(p,{expose:z}){const w=p,j=le(),$=T(null),v=T(!1),h=T(""),x=T(!1),f=T(new Date().getFullYear()),l=T(new Date().getMonth()+1),C=T(""),F=T(!1),D=T(!1),A=T(null),y=T({task_id:"",date:"",hours:0,description:"",billable:!0,billing_rate:null}),q=U(()=>$.value?Array.from({length:$.value.days_in_month},(n,t)=>t+1):[]),V=async()=>{var n;if((n=w.project)!=null&&n.id){v.value=!0,h.value="";try{const t=new URLSearchParams({year:f.value.toString(),month:l.value.toString()});C.value&&t.append("member_id",C.value.toString());const i=await fetch(`/api/timesheets/project/${w.project.id}/monthly?${t}`,{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(!i.ok)throw new Error("Errore nel caricamento del timesheet");const R=await i.json();$.value=R.data}catch(t){h.value=t.message}finally{v.value=!1}}},S=async(n,t)=>{try{const i=await fetch(`/api/timesheets/project/${w.project.id}?start_date=${t}&end_date=${t}&task_id=${n}`,{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(!i.ok)return null;const d=((await i.json()).data||[]).find(k=>k.user_id===j.user.id&&k.task_id===parseInt(n)&&k.date===t);return d?d.id:null}catch(i){return console.error("Error finding existing timesheet entry:",i),null}},B=async()=>{x.value=!0;try{const n={...y.value,project_id:w.project.id},t=await S(y.value.task_id,y.value.date);let i;if(t?i=await fetch(`/api/timesheets/${t}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken},body:JSON.stringify(n)}):i=await fetch("/api/timesheets/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken},body:JSON.stringify(n)}),!i.ok)throw new Error("Errore nel salvataggio del timesheet");await V(),o()}catch(n){h.value=n.message}finally{x.value=!1}},c=async(n,t)=>{var b,g,m;const i=$.value.tasks.find(J=>J.id===n);if(!i)return;A.value={taskId:n,day:t};const R=((g=(b=w.project)==null?void 0:b.contract)==null?void 0:g.hourly_rate)||null,u=`${f.value}-${String(l.value).padStart(2,"0")}-${String(t).padStart(2,"0")}`;let d=!!((m=w.project)!=null&&m.contract),k=R,W="";if(i.daily_hours[t]>0)try{const J=await fetch(`/api/timesheets/project/${w.project.id}?start_date=${u}&end_date=${u}&task_id=${n}`,{headers:{"Content-Type":"application/json","X-CSRFToken":j.csrfToken}});if(J.ok){const ne=((await J.json()).data||[]).find(ie=>ie.user_id===j.user.id&&ie.task_id===parseInt(n)&&ie.date===u);ne&&(d=ne.billable||!1,k=ne.billing_rate||R,W=ne.description||"")}}catch(J){console.error("Error loading existing entry:",J)}y.value={task_id:n,date:u,hours:i.daily_hours[t]||0,description:W,billable:d,billing_rate:k},i.daily_hours[t]>0?D.value=!0:F.value=!0},o=()=>{F.value=!1,D.value=!1,A.value=null,y.value={task_id:"",date:"",hours:0,description:"",billable:!0,billing_rate:null}},_=()=>{l.value===1?(l.value=12,f.value--):l.value--,V()},I=()=>{l.value===12?(l.value=1,f.value++):l.value++,V()},O=n=>{const t=new Date;return t.getFullYear()===f.value&&t.getMonth()+1===l.value&&t.getDate()===n},X=n=>!n||n===0?"0":n%1===0?n.toString():n.toFixed(2),P=n=>n?new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(n):"€0";return ee(()=>{var n;return(n=w.project)==null?void 0:n.id},n=>{n&&V()}),ee(C,()=>{V()}),re(()=>{var n;(n=w.project)!=null&&n.id&&V()}),z({refresh:V}),(n,t)=>{var i,R,u,d;return s(),r("div",Kn,[e("div",qn,[e("div",Nn,[e("div",Xn,[e("div",Jn,[t[13]||(t[13]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Timesheet Dettaglio ",-1)),e("div",Gn,[e("button",{onClick:_,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},t[9]||(t[9]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("span",Wn,a(l.value)+"/"+a(f.value),1),e("button",{onClick:I,class:"p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"},t[10]||(t[10]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("div",Yn,[t[12]||(t[12]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400"},"Vista:",-1)),E(e("select",{"onUpdate:modelValue":t[0]||(t[0]=k=>C.value=k),onChange:V,class:"text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[11]||(t[11]=e("option",{value:""},"Tutti i membri",-1)),(s(!0),r(L,null,K(((i=p.project)==null?void 0:i.team_members)||[],k=>(s(),r("option",{key:k.id,value:k.id},a(k.first_name)+" "+a(k.last_name),9,Qn))),128))],544),[[Q,C.value]])])]),e("button",{onClick:t[1]||(t[1]=k=>F.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},t[14]||(t[14]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Y(" Aggiungi Ore ")]))])]),v.value?(s(),r("div",Zn,t[15]||(t[15]=[e("div",{class:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"},null,-1)]))):M("",!0),h.value?(s(),r("div",ei,[e("p",ti,a(h.value),1)])):M("",!0),!v.value&&$.value?(s(),r("div",si,[e("div",ri,[e("table",oi,[e("thead",ai,[e("tr",null,[t[16]||(t[16]=e("th",{class:"px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700"}," Task ",-1)),(s(!0),r(L,null,K(q.value,k=>(s(),r("th",{key:k,class:H(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]",{"bg-blue-50 dark:bg-blue-900":O(k)}])},a(k),3))),128)),t[17]||(t[17]=e("th",{class:"px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700"}," Tot ",-1))])]),e("tbody",ni,[(s(!0),r(L,null,K($.value.tasks,k=>(s(),r("tr",{key:k.id},[e("td",ii,[e("div",li,a(k.name),1),e("div",di,a(k.workers.length?k.workers.join(", "):"Nessuno ha lavorato"),1)]),(s(!0),r(L,null,K(q.value,W=>{var b;return s(),r("td",{key:W,class:H(["px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700",{"bg-blue-50 dark:bg-blue-900":O(W)}]),onClick:g=>c(k.id,W)},[k.daily_hours[W]>0?(s(),r("div",ci,[e("span",gi,a(X(k.daily_hours[W])),1),(b=p.project)!=null&&b.contract?(s(),r("div",mi,[e("div",{class:H(["w-1.5 h-1.5 rounded-full",k.daily_billing&&k.daily_billing[W]?"bg-green-500":"bg-gray-300"]),title:k.daily_billing&&k.daily_billing[W]?"Fatturabile":"Non fatturabile"},null,10,pi)])):M("",!0)])):(s(),r("span",vi,"-"))],10,ui)}),128)),e("td",yi,[e("span",xi,a(X(k.total_hours)),1)])]))),128)),e("tr",fi,[t[18]||(t[18]=e("td",{class:"px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600"}," TOTALE GIORNALIERO ",-1)),(s(!0),r(L,null,K(q.value,k=>(s(),r("td",{key:k,class:H(["px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white",{"bg-blue-100 dark:bg-blue-800":O(k)}])},a(X($.value.daily_totals[k]||0)),3))),128)),e("td",bi,a(X($.value.grand_total)),1)])])])]),$.value.tasks.length===0?(s(),r("div",hi,t[19]||(t[19]=[e("p",{class:"text-gray-500 dark:text-gray-400"},"Nessun task trovato per questo progetto",-1)]))):M("",!0),(R=p.project)!=null&&R.contract&&$.value.tasks.length>0?(s(),r("div",ki,t[20]||(t[20]=[ae('<div class="flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-300"><span class="font-medium">Legenda:</span><div class="flex items-center space-x-1"><div class="w-1.5 h-1.5 rounded-full bg-green-500"></div><span>Fatturabile</span></div><div class="flex items-center space-x-1"><div class="w-1.5 h-1.5 rounded-full bg-gray-300"></div><span>Non fatturabile</span></div></div>',1)]))):M("",!0)])):M("",!0)]),F.value||D.value?(s(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:o},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[8]||(t[8]=se(()=>{},["stop"]))},[e("div",wi,[e("h3",_i,a(D.value?"Modifica Ore":"Aggiungi Ore"),1),e("form",{onSubmit:se(B,["prevent"])},[e("div",$i,[e("div",null,[t[22]||(t[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),E(e("select",{"onUpdate:modelValue":t[2]||(t[2]=k=>y.value.task_id=k),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[21]||(t[21]=e("option",{value:""},"Seleziona task",-1)),(s(!0),r(L,null,K(((u=$.value)==null?void 0:u.tasks)||[],k=>(s(),r("option",{key:k.id,value:k.id},a(k.name),9,ji))),128))],512),[[Q,y.value.task_id]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),E(e("input",{"onUpdate:modelValue":t[3]||(t[3]=k=>y.value.date=k),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[G,y.value.date]])]),e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),E(e("input",{"onUpdate:modelValue":t[4]||(t[4]=k=>y.value.hours=k),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[G,y.value.hours]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),E(e("textarea",{"onUpdate:modelValue":t[5]||(t[5]=k=>y.value.description=k),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[G,y.value.description]])]),(d=p.project)!=null&&d.contract?(s(),r("div",Ci,[t[29]||(t[29]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Informazioni Fatturazione",-1)),e("div",Mi,[e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Fatturabile ",-1)),E(e("select",{"onUpdate:modelValue":t[6]||(t[6]=k=>y.value.billable=k),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},t[26]||(t[26]=[e("option",{value:!0},"Sì - Fatturabile",-1),e("option",{value:!1},"No - Interno",-1)]),512),[[Q,y.value.billable]])]),y.value.billable?(s(),r("div",Ti,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tariffa (€/h) ",-1)),E(e("input",{"onUpdate:modelValue":t[7]||(t[7]=k=>y.value.billing_rate=k),type:"number",step:"0.01",min:"0",placeholder:p.project.contract.hourly_rate,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,8,Si),[[G,y.value.billing_rate]]),e("p",Pi," Tariffa contrattuale: "+a(P(p.project.contract.hourly_rate))+"/h ",1)])):M("",!0)])])):M("",!0)]),e("div",zi,[e("button",{type:"button",onClick:o,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:x.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},a(x.value?"Salvataggio...":D.value?"Aggiorna":"Aggiungi"),9,Ai)])],32)])])])):M("",!0)])}}},Ii={class:"space-y-6"},Vi={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},Ei={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Bi={class:"flex items-center justify-between"},Ui={class:"flex items-center space-x-3"},Ri=["disabled"],Hi={key:0,class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Fi={key:1,class:"animate-spin w-4 h-4 mr-2",fill:"none",viewBox:"0 0 24 24"},Oi={key:0,class:"px-6 py-4 bg-purple-50 dark:bg-purple-900/20 border-b border-purple-200 dark:border-purple-700"},Li={class:"flex items-start space-x-3"},Ki={class:"flex-1"},qi={class:"text-sm font-medium text-purple-900 dark:text-purple-100"},Ni={class:"mt-2 space-y-2"},Xi={key:0,class:"mt-3"},Ji={class:"space-y-2"},Gi={class:"flex items-center space-x-3"},Wi={class:"w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center"},Yi={class:"text-xs font-medium text-purple-600 dark:text-purple-300"},Qi={class:"text-sm font-medium text-gray-900 dark:text-white"},Zi={class:"text-xs text-gray-500 dark:text-gray-400"},el=["onClick"],tl={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},sl={key:0,class:"p-6"},rl={class:"animate-pulse space-y-4"},ol={key:1,class:"p-6 text-center"},al={key:2,class:"divide-y divide-gray-200 dark:divide-gray-700"},nl={class:"flex items-center justify-between"},il={class:"flex items-center space-x-4"},ll={class:"w-10 h-10 bg-primary-100 dark:bg-primary-800 rounded-full flex items-center justify-center"},dl={class:"text-sm font-medium text-primary-600 dark:text-primary-300"},ul={class:"text-sm font-medium text-gray-900 dark:text-white"},cl={class:"text-sm text-gray-500 dark:text-gray-400"},gl={class:"flex items-center space-x-4"},ml={class:"text-right"},pl={class:"text-sm font-medium text-gray-900 dark:text-white"},vl={class:"w-20 bg-gray-200 dark:bg-gray-600 rounded-full h-2"},yl={class:"flex items-center space-x-2"},xl=["onClick"],fl=["onClick"],bl={class:"bg-white dark:bg-gray-800 shadow rounded-lg"},hl={class:"p-6"},kl={class:"space-y-4"},wl={class:"w-32 text-sm text-gray-600 dark:text-gray-400"},_l={class:"flex-1 mx-4"},$l={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3"},jl={key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Cl={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Ml={class:"mt-3"},Tl={class:"space-y-4"},Sl=["value"],Pl={class:"flex justify-end space-x-3 mt-6"},zl=["disabled"],Al={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Dl={class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800"},Il={class:"mt-3"},Vl={class:"space-y-4"},El={class:"flex justify-end space-x-3 mt-6"},Bl=["disabled"],Ul={__name:"ProjectResourceAllocation",props:{project:{type:Object,required:!0}},setup(p){const z=p,w=le(),j=T(!0),$=T(!1),v=T(!1),h=T([]),x=T([]),f=T([]),l=T(null),C=T(!1),F=T(!1),D=T({user_id:"",role:"",allocation_percentage:100}),A=T({id:null,role:"",allocation_percentage:100}),y=U(()=>{var t;return(t=z.project)==null?void 0:t.id}),q=async()=>{var t;if(y.value){j.value=!0;try{const i=await fetch(`/api/resources?project_id=${y.value}`,{headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken}});if(!i.ok)throw new Error("Errore nel caricamento allocazioni");const R=await i.json();h.value=((t=R.data)==null?void 0:t.resources)||[],await S()}catch(i){console.error("Error loading allocations:",i)}finally{j.value=!1}}},V=async()=>{var t;try{const i=await fetch("/api/personnel",{headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken}});if(!i.ok)throw new Error("Errore nel caricamento utenti");const R=await i.json();x.value=((t=R.data)==null?void 0:t.users)||[]}catch(i){console.error("Error loading users:",i)}},S=async()=>{f.value=h.value.map(t=>({user_id:t.user_id,user_name:t.user_name,total_allocation:t.allocation_percentage+Math.floor(Math.random()*30)}))},B=async()=>{var t;if(y.value){v.value=!0;try{const i=await fetch(`/api/ai-resources/analyze-allocation/${y.value}`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({include_suggestions:!0,analysis_depth:"detailed"})});if(!i.ok)throw new Error("Errore nell'analisi AI");const R=await i.json();l.value=((t=R.data)==null?void 0:t.analysis)||null}catch(i){console.error("Error in AI analysis:",i),alert("Errore nell'analisi AI: "+i.message)}finally{v.value=!1}}},c=async()=>{$.value=!0;try{if(!(await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({project_id:y.value,...D.value})})).ok)throw new Error("Errore nell'aggiunta risorsa");await q(),C.value=!1,D.value={user_id:"",role:"",allocation_percentage:100}}catch(t){console.error("Error adding resource:",t),alert("Errore nell'aggiunta risorsa: "+t.message)}finally{$.value=!1}},o=t=>{A.value={id:t.id,role:t.role,allocation_percentage:t.allocation_percentage},F.value=!0},_=async()=>{$.value=!0;try{if(!(await fetch(`/api/resources/${A.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({role:A.value.role,allocation_percentage:A.value.allocation_percentage})})).ok)throw new Error("Errore nell'aggiornamento allocazione");await q(),F.value=!1,A.value={id:null,role:"",allocation_percentage:100}}catch(t){console.error("Error updating allocation:",t),alert("Errore nell'aggiornamento: "+t.message)}finally{$.value=!1}},I=async t=>{if(confirm("Sei sicuro di voler rimuovere questa allocazione?"))try{if(!(await fetch(`/api/resources/${t.id}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken}})).ok)throw new Error("Errore nella rimozione");await q()}catch(i){console.error("Error removing allocation:",i),alert("Errore nella rimozione: "+i.message)}},O=async t=>{try{await fetch("/api/resources",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":w.csrfToken},body:JSON.stringify({project_id:y.value,user_id:t.user_id,role:t.role,allocation_percentage:t.allocation})}),await q()}catch(i){console.error("Error applying AI recommendation:",i)}},X=t=>t>=80?"bg-red-500":t>=60?"bg-yellow-500":"bg-green-500",P=t=>t>100?"bg-red-500":t>=90?"bg-yellow-500":"bg-green-500",n=t=>t>100?"text-red-600 dark:text-red-400":t>=90?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400";return ee(()=>z.project,t=>{t&&q()},{immediate:!0}),re(()=>{V()}),(t,i)=>{var R;return s(),r("div",Ii,[e("div",Vi,[e("div",Ei,[e("div",Bi,[i[12]||(i[12]=e("div",null,[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazione Risorse "),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Gestisci l'allocazione delle risorse con assistenza AI ")],-1)),e("div",Ui,[e("button",{onClick:B,disabled:v.value,class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50"},[v.value?(s(),r("svg",Fi,i[10]||(i[10]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(s(),r("svg",Hi,i[9]||(i[9]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"},null,-1)]))),Y(" "+a(v.value?"Analizzando...":"Analisi AI"),1)],8,Ri),e("button",{onClick:i[0]||(i[0]=u=>C.value=!0),class:"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"},i[11]||(i[11]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),Y(" Aggiungi Risorsa ")]))])])]),l.value?(s(),r("div",Oi,[e("div",Li,[i[15]||(i[15]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])],-1)),e("div",Ki,[e("h4",qi," Insights AI - Efficienza: "+a(l.value.efficiency_score)+"% ",1),e("div",Ni,[(s(!0),r(L,null,K(l.value.optimization_insights,u=>(s(),r("div",{key:u,class:"text-sm text-purple-700 dark:text-purple-300"}," • "+a(u),1))),128))]),(R=l.value.recommended_allocations)!=null&&R.length?(s(),r("div",Xi,[i[13]||(i[13]=e("h5",{class:"text-sm font-medium text-purple-900 dark:text-purple-100 mb-2"}," Raccomandazioni AI: ",-1)),e("div",Ji,[(s(!0),r(L,null,K(l.value.recommended_allocations,u=>{var d;return s(),r("div",{key:u.user_id,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3"},[e("div",Gi,[e("div",Wi,[e("span",Yi,a((d=u.user_name)==null?void 0:d.charAt(0)),1)]),e("div",null,[e("p",Qi,a(u.user_name),1),e("p",Zi,a(u.role)+" - "+a(u.allocation)+"%",1)])]),e("button",{onClick:k=>O(u),class:"text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-300 px-2 py-1 rounded hover:bg-purple-200 dark:hover:bg-purple-700"}," Applica ",8,el)])}),128))])])):M("",!0)]),e("button",{onClick:i[1]||(i[1]=u=>l.value=null),class:"flex-shrink-0 text-purple-400 hover:text-purple-600"},i[14]||(i[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])):M("",!0)]),e("div",tl,[i[20]||(i[20]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Allocazioni Attuali ")],-1)),j.value?(s(),r("div",sl,[e("div",rl,[(s(),r(L,null,K(3,u=>e("div",{key:u,class:"flex items-center space-x-4"},i[16]||(i[16]=[ae('<div class="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div><div class="flex-1 space-y-2"><div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div><div class="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div></div><div class="w-20 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>',3)]))),64))])])):h.value.length?(s(),r("div",al,[(s(!0),r(L,null,K(h.value,u=>{var d;return s(),r("div",{key:u.id,class:"p-6 hover:bg-gray-50 dark:hover:bg-gray-700"},[e("div",nl,[e("div",il,[e("div",ll,[e("span",dl,a((d=u.user_name)==null?void 0:d.charAt(0)),1)]),e("div",null,[e("h4",ul,a(u.user_name),1),e("p",cl,a(u.role||"Team Member"),1)])]),e("div",gl,[e("div",ml,[e("div",pl,a(u.allocation_percentage)+"% ",1),e("div",vl,[e("div",{class:H(["h-2 rounded-full",X(u.allocation_percentage)]),style:te({width:u.allocation_percentage+"%"})},null,6)])]),e("div",yl,[e("button",{onClick:k=>o(u),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},i[18]||(i[18]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})],-1)]),8,xl),e("button",{onClick:k=>I(u),class:"text-red-400 hover:text-red-600"},i[19]||(i[19]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1)]),8,fl)])])])])}),128))])):(s(),r("div",ol,i[17]||(i[17]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna risorsa allocata",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Inizia aggiungendo risorse al progetto o usa l'analisi AI per suggerimenti. ",-1)])))]),e("div",bl,[i[21]||(i[21]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Utilizzo Risorse ")],-1)),e("div",hl,[e("div",kl,[(s(!0),r(L,null,K(f.value,u=>(s(),r("div",{key:u.user_id,class:"flex items-center"},[e("div",wl,a(u.user_name),1),e("div",_l,[e("div",$l,[e("div",{class:H(["h-3 rounded-full transition-all duration-300",P(u.total_allocation)]),style:te({width:Math.min(u.total_allocation,100)+"%"})},null,6)])]),e("div",{class:H(["w-16 text-sm text-right font-medium",n(u.total_allocation)])},a(u.total_allocation)+"% ",3)]))),128))])])]),C.value?(s(),r("div",jl,[e("div",Cl,[e("div",Ml,[i[26]||(i[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Risorsa ",-1)),e("form",{onSubmit:se(c,["prevent"])},[e("div",Tl,[e("div",null,[i[23]||(i[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Utente ",-1)),E(e("select",{"onUpdate:modelValue":i[2]||(i[2]=u=>D.value.user_id=u),required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},[i[22]||(i[22]=e("option",{value:""},"Seleziona utente...",-1)),(s(!0),r(L,null,K(x.value,u=>(s(),r("option",{key:u.id,value:u.id},a(u.full_name)+" ("+a(u.role)+") ",9,Sl))),128))],512),[[Q,D.value.user_id]])]),e("div",null,[i[24]||(i[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":i[3]||(i[3]=u=>D.value.role=u),type:"text",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[G,D.value.role]])]),e("div",null,[i[25]||(i[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":i[4]||(i[4]=u=>D.value.allocation_percentage=u),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[G,D.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",Pl,[e("button",{type:"button",onClick:i[5]||(i[5]=u=>C.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:$.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},a($.value?"Salvando...":"Aggiungi"),9,zl)])],32)])])])):M("",!0),F.value?(s(),r("div",Al,[e("div",Dl,[e("div",Il,[i[29]||(i[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Modifica Allocazione ",-1)),e("form",{onSubmit:se(_,["prevent"])},[e("div",Vl,[e("div",null,[i[27]||(i[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Ruolo nel Progetto ",-1)),E(e("input",{"onUpdate:modelValue":i[6]||(i[6]=u=>A.value.role=u),type:"text",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white",placeholder:"es. Developer, Designer, PM"},null,512),[[G,A.value.role]])]),e("div",null,[i[28]||(i[28]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"}," Allocazione (%) ",-1)),E(e("input",{"onUpdate:modelValue":i[7]||(i[7]=u=>A.value.allocation_percentage=u),type:"number",min:"1",max:"100",required:"",class:"mt-1 block w-full border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"},null,512),[[G,A.value.allocation_percentage,void 0,{number:!0}]])])]),e("div",El,[e("button",{type:"button",onClick:i[8]||(i[8]=u=>F.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500"}," Annulla "),e("button",{type:"submit",disabled:$.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 rounded-md hover:bg-primary-700 disabled:opacity-50"},a($.value?"Salvando...":"Aggiorna"),9,Bl)])],32)])])])):M("",!0)])}}},Rl={class:"project-view"},Hl={class:"tab-content"},Fl={__name:"ProjectView",setup(p){const z=fe(),w=le(),j=he(),$=we(),v=T(!0),h=T("overview"),x=U(()=>z.currentProject),f=U(()=>[{id:"overview",label:"Panoramica",icon:"chart-bar"},{id:"tasks",label:"Task",icon:"clipboard-list"},{id:"team",label:"Team",icon:"users"},{id:"resources",label:"Allocazione Risorse",icon:"user-group"},{id:"gantt",label:"Gantt",icon:"calendar"},{id:"timesheet",label:"Timesheet",icon:"clock"},{id:"expenses",label:"Spese",icon:"credit-card"},{id:"kpi",label:"KPI & Analytics",icon:"trending-up"}].filter(y=>!!(["overview","tasks","gantt","team","timesheet","resources"].includes(y.id)||y.id==="kpi"&&w.hasPermission("view_reports")||y.id==="expenses"&&w.hasPermission("manage_expenses")))),l=U(()=>({overview:me,tasks:nr,team:to,resources:Ul,expenses:Ko,kpi:xn,gantt:Ln,timesheet:Di})[h.value]||me),C=async()=>{v.value=!0;try{const A=j.params.id;await z.fetchProject(A)}catch(A){console.error("Error loading project:",A)}finally{v.value=!1}},F=()=>{$.push(`/projects/${j.params.id}/edit`)},D=async()=>{if(confirm("Sei sicuro di voler eliminare questo progetto?"))try{await z.deleteProject(j.params.id),$.push("/projects")}catch(A){console.error("Error deleting project:",A)}};return ee(()=>j.params.id,(A,y)=>{A&&A!==y&&C()}),ee(()=>j.hash,A=>{if(A){const y=A.replace("#","");f.value.find(q=>q.id===y)&&h.value!==y&&(h.value=y)}},{immediate:!0}),ee(h,A=>{const y=`#${A}`;j.hash!==y&&$.replace({...j,hash:y})}),re(()=>{if(j.hash){const A=j.hash.replace("#","");f.value.find(y=>y.id===A)&&(h.value=A)}C()}),(A,y)=>(s(),r("div",Rl,[oe(Ue,{project:x.value,loading:v.value,onEdit:F,onDelete:D},null,8,["project","loading"]),oe(qe,{modelValue:h.value,"onUpdate:modelValue":y[0]||(y[0]=q=>h.value=q),tabs:f.value,class:"mb-6"},null,8,["modelValue","tabs"]),e("div",Hl,[(s(),ce(ke,null,[(s(),ce(xe(l.value),{project:x.value,loading:v.value},null,8,["project","loading"]))],1024))])]))}},Kl=ge(Fl,[["__scopeId","data-v-de1f32e3"]]);export{Kl as default};
