import{r as g,f as J,A as xe,P as je,h as me,o as s,c as r,g as u,j as e,D as Se,t as n,n as H,Q as Ee,L as Ae,w as re,m as A,s as le,z as ye,I as fe,F as $,k as E,v as D,C as Le,u as Be,x as R,H as ne,a as De,l as Ie}from"./vendor.js";import{u as Pe}from"./personnel.js";import{_ as He,a as Oe}from"./app.js";const Fe={class:"flex items-center"},Ue={class:"flex-shrink-0 mr-3"},Ne={class:"flex-1"},qe={class:"text-sm font-medium"},Xe={key:0,class:"text-sm opacity-90"},Ge={__name:"Toast",props:{type:{type:String,default:"success",validator:p=>["success","error","warning","info"].includes(p)},title:{type:String,required:!0},message:{type:String,default:""},duration:{type:Number,default:3e3},closable:{type:Boolean,default:!0}},emits:["close"],setup(p,{expose:W,emit:L}){const l=p,M=L,v=g(!1);let b=null;const j={success:"bg-green-500 text-white",error:"bg-red-500 text-white",warning:"bg-yellow-500 text-black",info:"bg-blue-500 text-white"},h=J(()=>{const y={success:"CheckCircleIcon",error:"XCircleIcon",warning:"ExclamationTriangleIcon",info:"InformationCircleIcon"};return y[l.type]||y.success}),m=()=>{v.value=!0,l.duration>0&&(b=setTimeout(()=>{B()},l.duration))},B=()=>{v.value=!1,b&&(clearTimeout(b),b=null),setTimeout(()=>{M("close")},300)};return xe(()=>{setTimeout(m,10)}),je(()=>{b&&clearTimeout(b)}),W({show:m,close:B}),(y,k)=>(s(),me(Ee,{to:"body"},[v.value?(s(),r("div",{key:0,class:H([["fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform",j[p.type]||j.success,v.value?"translate-x-0 opacity-100":"translate-x-full opacity-0"],"max-w-sm"])},[e("div",Fe,[e("div",Ue,[(s(),me(Se(h.value),{class:"w-5 h-5"}))]),e("div",Ne,[e("p",qe,n(p.title),1),p.message?(s(),r("p",Xe,n(p.message),1)):u("",!0)]),p.closable?(s(),r("button",{key:0,onClick:B,class:"flex-shrink-0 ml-3 opacity-70 hover:opacity-100 transition-opacity"},k[0]||(k[0]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))):u("",!0)])],2)):u("",!0)]))}};g([]);function Re(){const p=v=>{const{type:b="success",title:j,message:h="",duration:m=3e3,closable:B=!0}=v,y=document.createElement("div");document.body.appendChild(y);const k=Ae(Ge,{type:b,title:j,message:h,duration:m,closable:B,onClose:()=>{k.unmount(),document.body.removeChild(y)}});k.mount(y)};return{showToast:p,success:(v,b="")=>{p({type:"success",title:v,message:b})},error:(v,b="")=>{p({type:"error",title:v,message:b})},warning:(v,b="")=>{p({type:"warning",title:v,message:b})},info:(v,b="")=>{p({type:"info",title:v,message:b})}}}const Je={class:"space-y-6"},We={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ye={class:"flex items-center justify-between mb-4"},Ke={key:1,class:"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Qe={class:"flex-1"},Ze={class:"text-sm font-medium text-gray-900 dark:text-white"},et={class:"text-xs text-gray-500 dark:text-gray-400"},tt={key:0,class:"mt-1"},at={class:"flex space-x-2"},st={key:2,class:"mt-4"},rt={class:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2"},lt={class:"flex items-center"},ot={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600",fill:"none",viewBox:"0 0 24 24"},nt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},it={key:0,class:"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6"},dt={class:"flex items-center justify-between mb-4"},ut={class:"flex items-center space-x-3"},ct={key:0,class:"mb-4"},gt={class:"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},vt={key:1,class:"mb-4"},pt={class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200"},mt={key:2},xt={class:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-3"},yt={class:"flex items-center space-x-2"},ft={class:"text-sm font-medium text-gray-900 dark:text-white"},bt={class:"flex items-center space-x-2"},ht={key:0,class:"text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded"},kt={class:"text-xs text-gray-500 dark:text-gray-400"},wt={key:0,class:"text-center"},_t={class:"text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full"},Ct={class:"mt-3"},zt={class:"flex items-center justify-between mb-4"},Tt={class:"max-h-96 overflow-y-auto mb-4"},Vt={class:"space-y-2"},$t=["id","value"],Mt=["for"],jt={class:"text-sm font-medium text-gray-900 dark:text-white"},St={class:"text-xs text-gray-500 dark:text-gray-400"},Et={key:0},At={key:0,class:"text-xs text-gray-400 mt-1"},Lt={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},Bt=["disabled"],Dt={__name:"CVTab",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1}},emits:["user-updated"],setup(p,{emit:W}){const L=p,l=W,{success:M,error:v,info:b}=Re(),j=g(!1),h=g(!1),m=g(0),B=g(!1),y=g(!1),k=g([]),O=g(!1),N=g(null),F=g(0),T=J(()=>{var d;if(!((d=L.user.profile)!=null&&d.cv_analysis_data))return null;try{return JSON.parse(L.user.profile.cv_analysis_data)}catch(o){return console.error("Error parsing CV analysis data:",o),null}}),Y=d=>d?new Date(d).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",I=d=>({beginner:"Principiante",intermediate:"Intermedio",advanced:"Avanzato",expert:"Esperto",1:"Principiante",2:"Base",3:"Intermedio",4:"Avanzato",5:"Esperto"})[d]||"Intermedio",q=()=>{var d;(d=N.value)==null||d.click()},Q=d=>{const o=d.target.files[0];o&&S(o)},X=d=>{d.preventDefault(),B.value=!1;const o=d.dataTransfer.files;o.length>0&&S(o[0])},S=async d=>{var C,z;if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain"].includes(d.type)){v("Formato file non supportato","Usa PDF, DOCX, DOC o TXT.");return}if(d.size>10*1024*1024){v("File troppo grande","Dimensione massima: 10MB.");return}j.value=!0,m.value=0;try{const w=new FormData;w.append("cv_file",d),w.append("analyze_skills","true"),w.append("auto_add_skills","false");const P=setInterval(()=>{m.value<90&&(m.value+=Math.random()*15)},200),ee=await fetch(`/api/personnel/users/${L.user.id}/cv/upload`,{method:"POST",body:w,headers:{"X-CSRFToken":((C=document.querySelector('meta[name="csrf-token"]'))==null?void 0:C.getAttribute("content"))||""}});clearInterval(P),m.value=100;const x=await ee.json();if(x.success)j.value=!1,(z=x.data.profile)!=null&&z.cv_analysis_data?M("CV caricato e analizzato con successo!"):(M("CV caricato con successo!"),await K()),l("user-updated",x.data),N.value&&(N.value.value="");else throw new Error(x.message||"Errore durante il caricamento")}catch(w){console.error("Errore durante il caricamento del CV:",w),v("Errore durante il caricamento del CV",w.message)}finally{j.value=!1,m.value=0}},K=async()=>{h.value=!0,F.value=0;try{const d=setInterval(()=>{F.value<90&&(F.value+=Math.random()*10)},300);await new Promise(o=>setTimeout(o,2e3)),clearInterval(d),F.value=100,M("Analisi AI completata!"),l("user-updated")}catch(d){console.error("Errore durante l'analisi AI:",d),v("Errore durante l'analisi AI",d.message)}finally{h.value=!1,F.value=0}},V=async()=>{try{const d=await fetch(`/api/personnel/users/${L.user.id}/cv/download`,{method:"GET",credentials:"include"});if(d.ok){const o=await d.blob(),C=window.URL.createObjectURL(o),z=document.createElement("a");z.href=C,z.download=`CV_${L.user.full_name}.pdf`,document.body.appendChild(z),z.click(),window.URL.revokeObjectURL(C),document.body.removeChild(z)}else{const o=await d.json();throw new Error(o.message||"Errore durante il download")}}catch(d){console.error("Errore durante il download del CV:",d),alert("Errore durante il download del CV: "+d.message)}},f=async()=>{var d;if(confirm("Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata."))try{const C=await(await fetch(`/api/personnel/users/${L.user.id}/cv`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":((d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content"))||""}})).json();if(C.success)l("user-updated",C.data),console.log("CV eliminato con successo");else throw new Error(C.message||"Errore durante l'eliminazione")}catch(o){console.error("Errore durante l'eliminazione del CV:",o),alert("Errore durante l'eliminazione del CV: "+o.message)}},Z=async()=>{var d;if(k.value.length!==0){O.value=!0;try{const o=k.value.map(w=>{const P=T.value.skills[w];return{...P,level:ie(P.level)}}),z=await(await fetch(`/api/personnel/users/${L.user.id}/skills/from-cv`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":((d=document.querySelector('meta[name="csrf-token"]'))==null?void 0:d.getAttribute("content"))||""},body:JSON.stringify({selected_skills:o})})).json();if(z.success){const{total_added:w,total_skipped:P}=z.data;w>0&&M(`Aggiunte ${w} competenze al profilo!`),P>0&&b(`${P} competenze erano già presenti nel profilo`),y.value=!1,k.value=[],l("user-updated")}else throw new Error(z.message||"Errore durante l'aggiunta delle competenze")}catch(o){console.error("Errore durante l'aggiunta delle competenze:",o),v("Errore durante l'aggiunta delle competenze",o.message)}finally{O.value=!1}}},ie=d=>{const o={beginner:1,intermediate:3,advanced:4,expert:5};return typeof d=="number"?Math.max(1,Math.min(5,d)):typeof d=="string"&&o[d.toLowerCase()]||3};return re(j,d=>{d||(m.value=0)}),re(h,d=>{d||(F.value=0)}),(d,o)=>{var C,z,w,P,ee;return s(),r("div",Je,[e("div",We,[e("div",Ye,[o[9]||(o[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"CV Attuale",-1)),p.canEdit&&!j.value?(s(),r("button",{key:0,onClick:q,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},[o[8]||(o[8]=e("svg",{class:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)),A(" "+n((C=p.user.profile)!=null&&C.current_cv_path?"Aggiorna CV":"Carica CV"),1)])):u("",!0)]),!((z=p.user.profile)!=null&&z.current_cv_path)&&p.canEdit?(s(),r("div",{key:0,onDrop:X,onDragover:o[0]||(o[0]=le(()=>{},["prevent"])),onDragenter:o[1]||(o[1]=le(()=>{},["prevent"])),class:H(["border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200",B.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600"])},[o[11]||(o[11]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1)),o[12]||(o[12]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Carica il tuo CV",-1)),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},[o[10]||(o[10]=A(" Trascina qui il file o ")),e("button",{onClick:q,class:"text-blue-600 hover:text-blue-500"},"sfoglia")]),o[13]||(o[13]=e("p",{class:"mt-1 text-xs text-gray-400"},"PDF, DOCX, DOC, TXT (max 10MB)",-1))],34)):(w=p.user.profile)!=null&&w.current_cv_path?(s(),r("div",Ke,[o[17]||(o[17]=e("svg",{class:"w-8 h-8 text-red-600 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)),e("div",Qe,[e("p",Ze,"CV_"+n(p.user.full_name)+".pdf",1),e("p",et," Caricato il "+n(Y(p.user.profile.cv_last_updated)),1),p.user.profile.cv_analysis_data?(s(),r("div",tt,o[14]||(o[14]=[e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}," ✨ Analisi AI completata ",-1)]))):u("",!0)]),e("div",at,[e("button",{onClick:V,class:"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300",title:"Scarica CV"},o[15]||(o[15]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)])),p.canEdit?(s(),r("button",{key:0,onClick:f,class:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Elimina CV"},o[16]||(o[16]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z","clip-rule":"evenodd"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 112 0v4a1 1 0 11-2 0V7z","clip-rule":"evenodd"})],-1)]))):u("",!0)])])):u("",!0),j.value||h.value?(s(),r("div",st,[e("div",rt,[e("div",lt,[h.value?(s(),r("svg",ot,o[18]||(o[18]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):u("",!0),e("span",null,n(h.value?"Analisi AI in corso...":"Caricamento in corso..."),1)]),e("span",null,n(h.value?F.value:m.value)+"%",1)]),e("div",nt,[e("div",{class:H(["h-2 rounded-full transition-all duration-300",h.value?"bg-purple-600":"bg-blue-600"]),style:ye({width:(h.value?F.value:m.value)+"%"})},null,6)])])):u("",!0)]),T.value?(s(),r("div",it,[e("div",dt,[o[20]||(o[20]=fe('<div class="flex items-center" data-v-ef9dd0f2><div class="w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3" data-v-ef9dd0f2><svg class="w-4 h-4 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" data-v-ef9dd0f2><path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" data-v-ef9dd0f2></path></svg></div><h3 class="text-lg font-medium text-purple-900 dark:text-purple-100" data-v-ef9dd0f2> Analisi AI del CV </h3></div>',1)),e("div",ut,[p.canEdit&&((P=T.value.skills)==null?void 0:P.length)>0?(s(),r("button",{key:0,onClick:o[2]||(o[2]=x=>y.value=!0),class:"text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center"},o[19]||(o[19]=[e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),A(" Aggiungi Competenze ")]))):u("",!0)])]),T.value.summary?(s(),r("div",ct,[o[21]||(o[21]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-2"},"Profilo Professionale",-1)),e("p",gt,n(T.value.summary),1)])):u("",!0),T.value.experience_years?(s(),r("div",vt,[e("span",pt,[o[22]||(o[22]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),A(" "+n(T.value.experience_years)+" anni di esperienza ",1)])])):u("",!0),((ee=T.value.skills)==null?void 0:ee.length)>0?(s(),r("div",mt,[o[24]||(o[24]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-3"},"Competenze Estratte",-1)),e("div",xt,[(s(!0),r($,null,E(T.value.skills.slice(0,8),(x,G)=>(s(),r("div",{key:G,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},[e("div",yt,[o[23]||(o[23]=e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"},null,-1)),e("span",ft,n(x.name),1)]),e("div",bt,[x.category?(s(),r("span",ht,n(x.category),1)):u("",!0),e("span",kt,n(I(x.level)),1)])]))),128))]),T.value.skills.length>8?(s(),r("div",wt,[e("span",_t," +"+n(T.value.skills.length-8)+" altre competenze disponibili ",1)])):u("",!0)])):u("",!0)])):u("",!0),e("input",{ref_key:"fileInput",ref:N,type:"file",accept:".pdf,.docx,.doc,.txt",onChange:Q,class:"hidden"},null,544),y.value?(s(),r("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:o[7]||(o[7]=x=>y.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:o[6]||(o[6]=le(()=>{},["stop"]))},[e("div",Ct,[e("div",zt,[o[26]||(o[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Seleziona Competenze da Aggiungere ",-1)),e("button",{onClick:o[3]||(o[3]=x=>y.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},o[25]||(o[25]=[e("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))]),e("div",Tt,[e("div",Vt,[(s(!0),r($,null,E(T.value.skills,(x,G)=>(s(),r("div",{key:G,class:"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"},[D(e("input",{type:"checkbox",id:`skill-${G}`,"onUpdate:modelValue":o[4]||(o[4]=de=>k.value=de),value:G,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,$t),[[Le,k.value]]),e("label",{for:`skill-${G}`,class:"ml-3 flex-1 cursor-pointer"},[e("div",jt,n(x.name),1),e("div",St,[A(n(x.category)+" • Livello "+n(x.level||3)+" ",1),x.years_experience?(s(),r("span",Et," • "+n(x.years_experience)+" anni",1)):u("",!0)]),x.context?(s(),r("div",At,n(x.context),1)):u("",!0)],8,Mt)]))),128))])]),e("div",Lt,[e("button",{onClick:o[5]||(o[5]=x=>y.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{onClick:Z,disabled:k.value.length===0||O.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},n(O.value?"Aggiungendo...":`Aggiungi ${k.value.length} competenze`),9,Bt)])])])])):u("",!0)])}}},It=He(Dt,[["__scopeId","data-v-ef9dd0f2"]]),Pt={class:"personnel-profile"},Ht={key:0,class:"flex justify-center items-center h-64"},Ot={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},Ft={class:"flex"},Ut={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Nt={key:2,class:"space-y-6"},qt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Xt={class:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"},Gt={class:"flex items-center space-x-6"},Rt={class:"flex-shrink-0"},Jt={class:"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg"},Wt=["src","alt"],Yt={key:1,class:"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Kt={class:"flex-1 text-white"},Qt={class:"text-3xl font-bold"},Zt={class:"text-blue-100 text-lg"},ea={class:"flex items-center space-x-4 mt-2"},ta={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},aa={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},sa={class:"flex-shrink-0"},ra={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},la={class:"flex items-center justify-between mb-2"},oa={class:"text-sm text-gray-500 dark:text-gray-400"},na={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},ia={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},da={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ua={class:"flex items-center justify-between mb-4"},ca={key:0,class:"space-y-3"},ga={class:"flex items-center"},va={class:"text-gray-900 dark:text-white"},pa={key:0,class:"flex items-center"},ma={class:"text-gray-900 dark:text-white"},xa={key:1,class:"flex items-center"},ya={class:"text-gray-900 dark:text-white"},fa={key:1,class:"space-y-4"},ba={class:"flex space-x-2"},ha=["disabled"],ka={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},wa={key:0,class:"space-y-3"},_a={class:"text-sm font-medium text-gray-900 dark:text-white"},Ca={class:"flex items-center space-x-2"},za={class:"flex space-x-1"},Ta={key:0,class:"text-xs text-green-600 dark:text-green-400"},Va={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2"},$a={key:1,class:"text-gray-500 dark:text-gray-400 text-sm"},Ma={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ja={class:"flex items-center justify-between mb-4"},Sa={key:0,class:"space-y-3"},Ea={key:0,class:"flex items-center"},Aa={class:"text-sm font-medium text-gray-900 dark:text-white"},La={key:1,class:"flex items-center"},Ba={class:"text-sm font-medium text-gray-900 dark:text-white"},Da={key:2,class:"flex items-center"},Ia={class:"text-sm font-medium text-gray-900 dark:text-white"},Pa={key:3,class:"flex items-center"},Ha={class:"text-sm font-medium text-gray-900 dark:text-white"},Oa={key:1,class:"space-y-4"},Fa={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Ua={key:0,class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},Na={class:"w-full"},qa={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Xa={class:"border-b border-gray-200 dark:border-gray-700"},Ga={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},Ra=["onClick"],Ja={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Wa={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ya={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ka={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Qa={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Za={key:5,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"},es={class:"p-6"},ts={key:0,class:"space-y-6"},as={key:0},ss={class:"flex items-center justify-between mb-4"},rs={class:"text-lg font-medium text-gray-900 dark:text-white"},ls={class:"text-sm text-gray-500 dark:text-gray-400"},os={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},ns={class:"flex items-start justify-between mb-3"},is={class:"flex-1"},ds={class:"font-medium text-gray-900 dark:text-white mb-1"},us={class:"text-sm text-gray-500 dark:text-gray-400"},cs={class:"space-y-2 text-sm"},gs={key:0,class:"flex items-center text-gray-600 dark:text-gray-400"},vs={key:1,class:"flex items-center text-gray-600 dark:text-gray-400"},ps={key:1,class:"text-center py-12"},ms={key:1,class:"space-y-6"},xs={key:0},ys={class:"flex items-center justify-between mb-4"},fs={class:"text-lg font-medium text-gray-900 dark:text-white"},bs={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},hs={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},ks={class:"flex items-start justify-between mb-3"},ws={class:"flex-1"},_s={class:"font-medium text-gray-900 dark:text-white mb-1"},Cs={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},zs={class:"flex flex-col space-y-1 ml-2"},Ts={key:0,class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Vs={key:1,class:"text-center py-12"},$s={key:2,class:"space-y-6"},Ms={key:0},js={class:"flex items-center justify-between mb-4"},Ss={class:"flex items-center space-x-4"},Es={class:"text-lg font-medium text-gray-900 dark:text-white"},As={class:"text-sm text-gray-500 dark:text-gray-400"},Ls={class:"text-sm text-gray-500 dark:text-gray-400"},Bs={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Ds={class:"flex items-center justify-between mb-2"},Is={class:"font-medium text-gray-900 dark:text-white"},Ps={key:0,class:"text-green-600 dark:text-green-400 text-sm font-medium"},Hs={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-3"},Os={class:"flex items-center justify-between"},Fs={class:"flex items-center space-x-2"},Us={class:"flex space-x-1"},Ns={class:"text-xs text-gray-500 dark:text-gray-400"},qs={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Xs={key:0,class:"flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4"},Gs={class:"flex items-center space-x-2"},Rs=["disabled"],Js={class:"text-sm text-gray-700 dark:text-gray-300"},Ws=["disabled"],Ys={class:"text-sm text-gray-500 dark:text-gray-400"},Ks={key:1,class:"text-center py-8"},Qs={key:3,class:"space-y-6"},Zs={class:"flex items-center justify-between mb-4"},er={class:"flex items-center space-x-4"},tr={class:"flex items-center space-x-2"},ar={class:"text-lg font-medium text-gray-900 dark:text-white"},sr={class:"flex items-center space-x-4"},rr={class:"text-sm text-gray-500 dark:text-gray-400"},lr={key:0,class:"overflow-x-auto shadow ring-1 ring-black ring-opacity-5 rounded-lg"},or={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},nr={class:"bg-gray-50 dark:bg-gray-700"},ir={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},dr={class:"sticky left-0 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},ur={class:"text-sm font-medium text-gray-900 dark:text-white"},cr={class:"text-xs text-gray-500 dark:text-gray-400"},gr={class:"text-xs text-gray-400 dark:text-gray-500"},vr=["onClick"],pr={key:1,class:"text-gray-300 dark:text-gray-600"},mr={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700"},xr={class:"bg-gray-100 dark:bg-gray-600 font-medium"},yr={class:"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white bg-gray-200 dark:bg-gray-500"},fr={key:1,class:"text-center py-12"},br={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},hr={class:"mt-6"},kr={key:4},wr={class:"mt-3"},_r={class:"flex items-center justify-between mb-4"},Cr={class:"text-lg font-medium text-gray-900 dark:text-white"},zr=["value"],Tr=["value"],Vr={class:"flex items-center justify-end space-x-3 pt-4"},$r=["disabled"],Er={__name:"PersonnelProfile",setup(p){const W=Be();Ie(),Pe();const{hasPermission:L}=Oe(),l=g(null),M=g([]),v=g([]),b=g([]),j=g(!1),h=g(null),m=g(!1),B=g(!1),y=g("projects"),k=g(!1),O=g(!1),N=g(!1),F=g(null),T=g([]),Y=g([]),I=g(new Date().getMonth()),q=g(new Date().getFullYear()),Q=g("user"),X=g([]),S=g(1),K=g(6),V=g({phone:"",bio:"",employee_id:"",job_title:"",employment_type:"",work_location:"",weekly_hours:40,address:"",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:""}),f=g({date:"",project_id:"",task_id:"",hours:"",description:""}),Z=J(()=>{if(!l.value)return!1;try{return L.value&&typeof L.value=="function"?L.value("edit_personnel_data"):!1}catch(i){return console.warn("Permission check failed:",i),!1}}),ie=J(()=>{var a;if(!((a=l.value)!=null&&a.skills))return[];const i=(S.value-1)*K.value,t=i+K.value;return l.value.skills.slice(i,t)}),d=J(()=>{var i;return(i=l.value)!=null&&i.skills?Math.ceil(l.value.skills.length/K.value):0}),o=J(()=>new Date(q.value,I.value).toLocaleDateString("it-IT",{month:"long",year:"numeric"})),C=J(()=>{const i=q.value,t=I.value,a=new Date(i,t+1,0).getDate(),c=new Date,_=[];for(let U=1;U<=a;U++){const ae=new Date(i,t,U),se=ae.toISOString().split("T")[0];_.push({day:U,date:se,isWeekend:ae.getDay()===0||ae.getDay()===6,isToday:se===c.toISOString().split("T")[0]})}return _}),z=J(()=>{var i,t;return[{id:"projects",name:"Progetti",count:M.value.length},{id:"tasks",name:"Task",count:v.value.length},{id:"skills",name:"Competenze",count:((t=(i=l.value)==null?void 0:i.skills)==null?void 0:t.length)||0},{id:"timesheet",name:"Timesheet",count:b.value.length},{id:"cv",name:"CV"}]}),w=i=>i?new Date(i).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",P=i=>({full_time:"Tempo Pieno",part_time:"Part Time",contractor:"Consulente",intern:"Stagista"})[i]||i,ee=i=>{y.value=i,i==="skills"&&(S.value=1)},x=()=>{var i,t,a,c,_,U,ae,se,pe;l.value&&(V.value={phone:l.value.phone||"",bio:l.value.bio||"",employee_id:((i=l.value.profile)==null?void 0:i.employee_id)||"",job_title:((t=l.value.profile)==null?void 0:t.job_title)||"",employment_type:((a=l.value.profile)==null?void 0:a.employment_type)||"",work_location:((c=l.value.profile)==null?void 0:c.work_location)||"",weekly_hours:((_=l.value.profile)==null?void 0:_.weekly_hours)||40,address:((U=l.value.profile)==null?void 0:U.address)||"",emergency_contact_name:((ae=l.value.profile)==null?void 0:ae.emergency_contact_name)||"",emergency_contact_phone:((se=l.value.profile)==null?void 0:se.emergency_contact_phone)||"",emergency_contact_relationship:((pe=l.value.profile)==null?void 0:pe.emergency_contact_relationship)||""})},G=()=>{m.value=!1,x()},de=async()=>{var i;if(l.value){B.value=!0;try{const a=await(await fetch(`/api/personnel/users/${l.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":((i=document.querySelector('meta[name="csrf-token"]'))==null?void 0:i.getAttribute("content"))||""},body:JSON.stringify(V.value)})).json();if(a.success)l.value=a.data.user,m.value=!1,console.log("Profilo aggiornato con successo");else throw new Error(a.message||"Errore durante il salvataggio")}catch(t){console.error("Errore durante il salvataggio:",t),h.value=t.message}finally{B.value=!1}}},ue=async i=>{j.value=!0,h.value=null;try{const t=await fetch(`/api/personnel/users/${i}`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const a=await t.json();if(a.success)l.value=a.data.user,x();else throw new Error(a.message||"Errore nel caricamento del profilo")}catch(t){console.error("Error fetching user profile:",t),h.value=t.message}finally{j.value=!1}},ge=async i=>{try{const t=await fetch(`/api/tasks?assignee_id=${i}&limit=20`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const a=await t.json();a.success&&(v.value=a.data.tasks||[])}catch(t){console.error("Error fetching user tasks:",t)}},ce=async i=>{try{const t=new Date,a=new Date;a.setDate(a.getDate()-30);const c=await fetch(`/api/timesheets?user_id=${i}&start_date=${a.toISOString().split("T")[0]}&end_date=${t.toISOString().split("T")[0]}&limit=50`,{credentials:"include"});if(!c.ok)throw new Error(`HTTP ${c.status}: ${c.statusText}`);const _=await c.json();_.success&&(b.value=_.data||[])}catch(t){console.error("Error fetching user timesheets:",t)}},ve=()=>{f.value={date:new Date().toISOString().split("T")[0],project_id:"",task_id:"",hours:"",description:""}},oe=()=>{k.value=!1,O.value=!1,F.value=null,ve(),Y.value=[]},be=()=>{f.value.task_id="",f.value.project_id?he(f.value.project_id):Y.value=[]},he=async i=>{try{const t=await fetch(`/api/tasks?project_id=${i}`,{credentials:"include"});if(t.ok){const a=await t.json();a.success&&(Y.value=a.data.tasks||[])}}catch(t){console.error("Error loading tasks:",t),Y.value=[]}},ke=async()=>{var i;N.value=!0;try{const t={user_id:l.value.id,date:f.value.date,project_id:parseInt(f.value.project_id),task_id:f.value.task_id?parseInt(f.value.task_id):null,hours:parseFloat(f.value.hours),description:f.value.description},a=O.value?`/api/timesheets/${F.value.id}`:"/api/timesheets",c=O.value?"PUT":"POST",U=await(await fetch(a,{method:c,headers:{"Content-Type":"application/json","X-CSRFToken":((i=document.querySelector('meta[name="csrf-token"]'))==null?void 0:i.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(t)})).json();if(U.success)oe(),await ce(l.value.id),console.log("Timesheet salvato con successo");else throw new Error(U.message||"Errore durante il salvataggio")}catch(t){console.error("Errore durante il salvataggio:",t),h.value=t.message}finally{N.value=!1}},we=async()=>{try{const i=await fetch("/api/projects",{credentials:"include"});if(i.ok){const t=await i.json();t.success&&(T.value=t.data.projects||[])}}catch(i){console.error("Error loading projects:",i),T.value=[]}},_e=()=>{I.value===0?(I.value=11,q.value--):I.value--,te()},Ce=()=>{I.value===11?(I.value=0,q.value++):I.value++,te()},te=async()=>{if(l.value)try{const i=new Date(q.value,I.value,1).toISOString().split("T")[0],t=new Date(q.value,I.value+1,0).toISOString().split("T")[0],a=Q.value==="user"?l.value.id:"",c=await fetch(`/api/timesheets?user_id=${a}&start_date=${i}&end_date=${t}`,{credentials:"include"});if(c.ok){const _=await c.json();_.success&&ze(_.data||[])}}catch(i){console.error("Error loading timesheet grid:",i),X.value=[]}},ze=i=>{const t={};i.forEach(a=>{const c=`${a.project_id}-${a.task_id||"no-task"}`;t[c]||(t[c]={taskId:a.task_id||`project-${a.project_id}`,taskName:a.task_name||"Attività Generica",projectName:a.project_name,assignees:a.user_name||"N/A",hours:{},total:0});const _=a.date;t[c].hours[_]||(t[c].hours[_]=0),t[c].hours[_]+=parseFloat(a.hours||0),t[c].total+=parseFloat(a.hours||0)}),X.value=Object.values(t).map(a=>({...a,total:a.total.toFixed(1),hours:Object.fromEntries(Object.entries(a.hours).map(([c,_])=>[c,_.toFixed(1)]))}))},Te=i=>{const t=X.value.reduce((a,c)=>a+parseFloat(c.hours[i]||0),0);return t>0?t.toFixed(1):"0"},Ve=()=>X.value.reduce((t,a)=>t+parseFloat(a.total||0),0).toFixed(1),$e=(i,t,a)=>{f.value.date=t,k.value=!0},Me=async i=>{i?(i.cv_path&&(l.value.profile.current_cv_path=i.cv_path),i.cv_last_updated&&(l.value.profile.cv_last_updated=i.cv_last_updated),i.analysis&&(l.value.profile.cv_analysis_data=JSON.stringify(i.analysis)),i.profile_completion!==void 0&&(l.value.profile.profile_completion=i.profile_completion)):await ue(l.value.id)};return xe(async()=>{const i=W.params.id;if(!i){h.value="ID utente non specificato";return}ve(),await ue(i),l.value&&(M.value=l.value.projects||[],await ge(i),await ce(i)),await we(),await te()}),re(()=>W.params.id,async i=>{i&&(await ue(i),l.value&&(M.value=l.value.projects||[],await ge(i),await ce(i),await te()))}),re(Q,()=>{te()}),re(y,i=>{i==="timesheet"&&te()}),(i,t)=>(s(),r($,null,[e("div",Pt,[j.value?(s(),r("div",Ht,t[20]||(t[20]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):h.value?(s(),r("div",Ot,[e("div",Ft,[t[22]||(t[22]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[21]||(t[21]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",Ut,n(h.value),1)])])])):l.value?(s(),r("div",Nt,[e("div",qt,[e("div",Xt,[e("div",Gt,[e("div",Rt,[e("div",Jt,[l.value.profile_image?(s(),r("img",{key:0,src:l.value.profile_image,alt:l.value.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,Wt)):(s(),r("div",Yt,t[23]||(t[23]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)])))])]),e("div",Kt,[e("h1",Qt,n(l.value.full_name),1),e("p",Zt,n(l.value.position||"Posizione non specificata"),1),e("div",ea,[l.value.department?(s(),r("span",ta,[t[24]||(t[24]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),A(" "+n(l.value.department.name),1)])):u("",!0),e("span",aa,[t[25]||(t[25]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),A(" "+n(l.value.role),1)])])]),e("div",sa,[Z.value?(s(),r("button",{key:0,onClick:t[0]||(t[0]=a=>m.value=!m.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"},[t[26]||(t[26]=e("svg",{class:"w-5 h-5 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)),A(" "+n(m.value?"Annulla":"Modifica"),1)])):u("",!0)])])]),l.value.profile&&l.value.profile.profile_completion!==void 0?(s(),r("div",ra,[e("div",la,[t[27]||(t[27]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",oa,n(l.value.profile.profile_completion)+"%",1)]),e("div",na,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:ye({width:l.value.profile.profile_completion+"%"})},null,4)])])):u("",!0)]),e("div",ia,[e("div",da,[e("div",ua,[t[29]||(t[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni di Contatto",-1)),Z.value&&!m.value?(s(),r("button",{key:0,onClick:t[1]||(t[1]=a=>m.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[28]||(t[28]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):u("",!0)]),m.value?(s(),r("div",fa,[e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),D(e("input",{"onUpdate:modelValue":t[2]||(t[2]=a=>V.value.phone=a),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[R,V.value.phone]])]),e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Bio",-1)),D(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=a=>V.value.bio=a),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[R,V.value.bio]])]),e("div",ba,[e("button",{onClick:de,disabled:B.value,class:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200"},n(B.value?"Salvataggio...":"Salva"),9,ha),e("button",{onClick:G,class:"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200"}," Annulla ")])])):(s(),r("div",ca,[e("div",ga,[t[30]||(t[30]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),e("span",va,n(l.value.email),1)]),l.value.phone?(s(),r("div",pa,[t[31]||(t[31]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),e("span",ma,n(l.value.phone),1)])):u("",!0),l.value.hire_date?(s(),r("div",xa,[t[32]||(t[32]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),e("span",ya,"Assunto il "+n(w(l.value.hire_date)),1)])):u("",!0)]))]),e("div",ka,[t[35]||(t[35]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Competenze Principali",-1)),l.value.skills&&l.value.skills.length>0?(s(),r("div",wa,[(s(!0),r($,null,E(l.value.skills.slice(0,4),a=>(s(),r("div",{key:a.id,class:"flex items-center justify-between"},[e("span",_a,n(a.name),1),e("div",Ca,[e("div",za,[(s(),r($,null,E(5,c=>e("div",{key:c,class:H(["w-2 h-2 rounded-full",c<=a.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),a.certified?(s(),r("span",Ta,"✓")):u("",!0)])]))),128)),l.value.skills.length>4?(s(),r("div",Va," +"+n(l.value.skills.length-4)+" altre competenze ",1)):u("",!0)])):(s(),r("div",$a," Nessuna competenza registrata "))]),l.value.profile?(s(),r("div",Ma,[e("div",ja,[t[37]||(t[37]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni HR",-1)),Z.value&&!m.value?(s(),r("button",{key:0,onClick:t[4]||(t[4]=a=>m.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[36]||(t[36]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):u("",!0)]),m.value?(s(),r("div",Oa,[e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"ID Dipendente",-1)),D(e("input",{"onUpdate:modelValue":t[5]||(t[5]=a=>V.value.employee_id=a),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[R,V.value.employee_id]])]),e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Titolo Lavoro",-1)),D(e("input",{"onUpdate:modelValue":t[6]||(t[6]=a=>V.value.job_title=a),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[R,V.value.job_title]])]),e("div",null,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Contratto",-1)),D(e("select",{"onUpdate:modelValue":t[7]||(t[7]=a=>V.value.employment_type=a),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[48]||(t[48]=[fe('<option value="">Seleziona tipo</option><option value="full_time">Tempo Pieno</option><option value="part_time">Part Time</option><option value="contractor">Consulente</option><option value="intern">Stagista</option>',5)]),512),[[ne,V.value.employment_type]])]),e("div",null,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore Settimanali",-1)),D(e("input",{"onUpdate:modelValue":t[8]||(t[8]=a=>V.value.weekly_hours=a),type:"number",min:"1",max:"60",step:"0.5",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[R,V.value.weekly_hours]])])])):(s(),r("div",Sa,[l.value.profile.employee_id?(s(),r("div",Ea,[t[39]||(t[39]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)),e("div",null,[t[38]||(t[38]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"ID Dipendente",-1)),e("p",Aa,n(l.value.profile.employee_id),1)])])):u("",!0),l.value.profile.job_title?(s(),r("div",La,[t[41]||(t[41]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),e("div",null,[t[40]||(t[40]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Titolo",-1)),e("p",Ba,n(l.value.profile.job_title),1)])])):u("",!0),l.value.profile.employment_type?(s(),r("div",Da,[t[43]||(t[43]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),e("div",null,[t[42]||(t[42]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Tipo Contratto",-1)),e("p",Ia,n(P(l.value.profile.employment_type)),1)])])):u("",!0),l.value.profile.weekly_hours?(s(),r("div",Pa,[t[45]||(t[45]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("div",null,[t[44]||(t[44]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Ore Settimanali",-1)),e("p",Ha,n(l.value.profile.weekly_hours)+"h",1)])])):u("",!0)]))])):u("",!0)]),l.value.bio||m.value?(s(),r("div",Fa,[t[51]||(t[51]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),m.value?u("",!0):(s(),r("p",Ua,n(l.value.bio||"Nessuna bio disponibile"),1))])):u("",!0),e("div",Na,[e("div",qa,[e("div",Xa,[e("nav",Ga,[(s(!0),r($,null,E(z.value,a=>(s(),r("button",{key:a.id,onClick:c=>ee(a.id),class:H([y.value===a.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[a.id==="projects"?(s(),r("svg",Ja,t[52]||(t[52]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):a.id==="tasks"?(s(),r("svg",Wa,t[53]||(t[53]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):a.id==="skills"?(s(),r("svg",Ya,t[54]||(t[54]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):a.id==="timesheet"?(s(),r("svg",Ka,t[55]||(t[55]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"},null,-1)]))):a.id==="cv"?(s(),r("svg",Qa,t[56]||(t[56]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"},null,-1)]))):u("",!0),A(" "+n(a.name)+" ",1),a.count!==void 0?(s(),r("span",Za,n(a.count),1)):u("",!0)],10,Ra))),128))])]),e("div",es,[y.value==="projects"?(s(),r("div",ts,[M.value.length>0?(s(),r("div",as,[e("div",ss,[e("h3",rs," Progetti Assegnati ("+n(M.value.length)+") ",1),e("div",ls,n(M.value.filter(a=>a.status==="active").length)+" attivi ",1)]),e("div",os,[(s(!0),r($,null,E(M.value,a=>(s(),r("div",{key:a.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer"},[e("div",ns,[e("div",is,[e("h4",ds,n(a.name),1),e("p",us,n(a.role||"Team Member"),1)]),e("span",{class:H(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2",a.status==="active"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":a.status==="completed"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":a.status==="on_hold"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},n(a.status),3)]),e("div",cs,[a.client?(s(),r("div",gs,[t[57]||(t[57]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),A(" "+n(a.client),1)])):u("",!0),a.deadline?(s(),r("div",vs,[t[58]||(t[58]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),A(" "+n(w(a.deadline)),1)])):u("",!0)])]))),128))])])):(s(),r("div",ps,t[59]||(t[59]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1)])))])):u("",!0),y.value==="tasks"?(s(),r("div",ms,[v.value.length>0?(s(),r("div",xs,[e("div",ys,[e("h3",fs," Task Assegnati ("+n(v.value.length)+") ",1),e("div",bs,[e("span",null,n(v.value.filter(a=>a.status==="in-progress").length)+" in corso",1),e("span",null,n(v.value.filter(a=>a.status==="done").length)+" completati",1)])]),e("div",hs,[(s(!0),r($,null,E(v.value,a=>(s(),r("div",{key:a.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",ks,[e("div",ws,[e("h4",_s,n(a.name),1),a.project_name?(s(),r("p",Cs,n(a.project_name),1)):u("",!0)]),e("div",zs,[e("span",{class:H(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",a.priority==="urgent"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":a.priority==="high"?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":a.priority==="medium"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"])},n(a.priority),3),e("span",{class:H(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",a.status==="done"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":a.status==="in-progress"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":a.status==="review"?"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},n(a.status),3)])]),a.due_date?(s(),r("div",Ts,[t[60]||(t[60]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),A(" Scadenza: "+n(w(a.due_date)),1)])):u("",!0)]))),128))])])):(s(),r("div",Vs,t[61]||(t[61]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono stati assegnati task a questo utente.",-1)])))])):u("",!0),y.value==="skills"?(s(),r("div",$s,[l.value.skills&&l.value.skills.length>0?(s(),r("div",Ms,[e("div",js,[e("div",Ss,[e("h3",Es," Competenze ("+n(l.value.skills.length)+") ",1),e("span",As," Pagina "+n(S.value)+" di "+n(d.value),1)]),e("div",Ls,n(l.value.skills.filter(a=>a.certified).length)+" certificate ",1)]),e("div",Bs,[(s(!0),r($,null,E(ie.value,a=>(s(),r("div",{key:a.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",Ds,[e("h4",Is,n(a.name),1),a.certified?(s(),r("span",Ps,"✓ Certificato")):u("",!0)]),a.category?(s(),r("p",Hs,n(a.category),1)):u("",!0),e("div",Os,[e("div",Fs,[t[62]||(t[62]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",Us,[(s(),r($,null,E(5,c=>e("div",{key:c,class:H(["w-3 h-3 rounded-full",c<=a.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),e("span",Ns,"("+n(a.proficiency_level)+"/5)",1)]),a.years_experience?(s(),r("span",qs,n(a.years_experience)+n(a.years_experience===1?" anno":" anni"),1)):u("",!0)])]))),128))]),d.value>1?(s(),r("div",Xs,[e("div",Gs,[e("button",{onClick:t[9]||(t[9]=a=>S.value=Math.max(1,S.value-1)),disabled:S.value===1,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Precedente ",8,Rs),e("span",Js," Pagina "+n(S.value)+" di "+n(d.value),1),e("button",{onClick:t[10]||(t[10]=a=>S.value=Math.min(d.value,S.value+1)),disabled:S.value===d.value,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Successiva ",8,Ws)]),e("div",Ys," Mostrando "+n((S.value-1)*K.value+1)+"-"+n(Math.min(S.value*K.value,l.value.skills.length))+" di "+n(l.value.skills.length)+" competenze ",1)])):u("",!0)])):(s(),r("div",Ks,t[63]||(t[63]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono state registrate competenze per questo utente.",-1)])))])):u("",!0),y.value==="timesheet"?(s(),r("div",Qs,[e("div",Zs,[e("div",er,[t[66]||(t[66]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Timesheet Dettaglio",-1)),e("div",tr,[e("button",{onClick:_e,class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[64]||(t[64]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)])),e("span",ar,n(o.value),1),e("button",{onClick:Ce,class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[65]||(t[65]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))])]),e("div",sr,[e("div",rr,[t[68]||(t[68]=A(" Vista: ")),D(e("select",{"onUpdate:modelValue":t[11]||(t[11]=a=>Q.value=a),class:"ml-1 bg-transparent border-none text-blue-600 dark:text-blue-400 font-medium"},t[67]||(t[67]=[e("option",{value:"all"},"Tutti i membri",-1),e("option",{value:"user"},"Solo questo utente",-1)]),512),[[ne,Q.value]])]),e("button",{onClick:t[12]||(t[12]=a=>k.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"},t[69]||(t[69]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),A(" Aggiungi Ore ")]))])]),X.value.length>0?(s(),r("div",lr,[e("table",or,[e("thead",nr,[e("tr",null,[t[70]||(t[70]=e("th",{class:"sticky left-0 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," TASK ",-1)),(s(!0),r($,null,E(C.value,a=>(s(),r("th",{key:a.date,class:H(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",a.isWeekend?"bg-gray-100 dark:bg-gray-600":"",a.isToday?"bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300":""])},n(a.day),3))),128)),t[71]||(t[71]=e("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-100 dark:bg-gray-600"}," TOTALE ",-1))])]),e("tbody",ir,[(s(!0),r($,null,E(X.value,a=>(s(),r("tr",{key:a.taskId,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",dr,[e("div",ur,n(a.taskName),1),e("div",cr,n(a.projectName),1),e("div",gr,n(a.assignees),1)]),(s(!0),r($,null,E(C.value,c=>(s(),r("td",{key:c.date,class:H(["px-2 py-3 text-center text-sm",c.isWeekend?"bg-gray-50 dark:bg-gray-700":"",c.isToday?"bg-blue-50 dark:bg-blue-900":""])},[a.hours[c.date]?(s(),r("div",{key:0,class:"text-blue-600 dark:text-blue-400 font-medium cursor-pointer hover:text-blue-800 dark:hover:text-blue-300",onClick:_=>$e(a.taskId,c.date,a.hours[c.date])},n(a.hours[c.date]),9,vr)):(s(),r("div",pr,"-"))],2))),128)),e("td",mr,n(a.total),1)]))),128)),e("tr",xr,[t[72]||(t[72]=e("td",{class:"sticky left-0 bg-gray-100 dark:bg-gray-600 px-4 py-3 text-sm font-bold text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-500"}," TOTALE GIORNALIERO ",-1)),(s(!0),r($,null,E(C.value,a=>(s(),r("td",{key:a.date,class:H(["px-2 py-3 text-center text-sm font-bold text-gray-900 dark:text-white",a.isWeekend?"bg-gray-200 dark:bg-gray-500":""])},n(Te(a.date)),3))),128)),e("td",yr,n(Ve()),1)])])])])):(s(),r("div",fr,[t[74]||(t[74]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),t[75]||(t[75]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun timesheet",-1)),e("p",br,"Non sono stati registrati timesheet per "+n(o.value)+".",1),e("div",hr,[e("button",{onClick:t[13]||(t[13]=a=>k.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center mx-auto"},t[73]||(t[73]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),A(" Aggiungi Prima Registrazione ")]))])]))])):u("",!0),y.value==="cv"?(s(),r("div",kr,[De(It,{user:l.value,"can-edit":Z.value,onUserUpdated:Me},null,8,["user","can-edit"])])):u("",!0)])])])])):u("",!0)]),k.value||O.value?(s(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:oe},[e("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[19]||(t[19]=le(()=>{},["stop"]))},[e("div",wr,[e("div",_r,[e("h3",Cr,n(O.value?"Modifica Timesheet":"Aggiungi Timesheet"),1),e("button",{onClick:oe,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[76]||(t[76]=[e("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))]),e("form",{onSubmit:le(ke,["prevent"]),class:"space-y-4"},[e("div",null,[t[77]||(t[77]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),D(e("input",{"onUpdate:modelValue":t[14]||(t[14]=a=>f.value.date=a),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[R,f.value.date]])]),e("div",null,[t[79]||(t[79]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),D(e("select",{"onUpdate:modelValue":t[15]||(t[15]=a=>f.value.project_id=a),required:"",onChange:be,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[78]||(t[78]=e("option",{value:""},"Seleziona progetto",-1)),(s(!0),r($,null,E(T.value,a=>(s(),r("option",{key:a.id,value:a.id},n(a.name),9,zr))),128))],544),[[ne,f.value.project_id]])]),e("div",null,[t[81]||(t[81]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task (opzionale)",-1)),D(e("select",{"onUpdate:modelValue":t[16]||(t[16]=a=>f.value.task_id=a),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[80]||(t[80]=e("option",{value:""},"Nessun task specifico",-1)),(s(!0),r($,null,E(Y.value,a=>(s(),r("option",{key:a.id,value:a.id},n(a.name),9,Tr))),128))],512),[[ne,f.value.task_id]])]),e("div",null,[t[82]||(t[82]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),D(e("input",{"onUpdate:modelValue":t[17]||(t[17]=a=>f.value.hours=a),type:"number",step:"0.25",min:"0.25",max:"24",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[R,f.value.hours]])]),e("div",null,[t[83]||(t[83]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),D(e("textarea",{"onUpdate:modelValue":t[18]||(t[18]=a=>f.value.description=a),rows:"3",placeholder:"Descrivi il lavoro svolto...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[R,f.value.description]])]),e("div",Vr,[e("button",{type:"button",onClick:oe,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{type:"submit",disabled:N.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},n(N.value?"Salvando...":O.value?"Aggiorna":"Salva"),9,$r)])],32)])])])):u("",!0)],64))}};export{Er as default};
