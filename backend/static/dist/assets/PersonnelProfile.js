import{r as g,f as R,A as me,P as je,h as pe,o as a,c as r,g as d,j as e,D as Se,t as i,n as O,Q as Ee,L as Ae,w as re,m as B,s as le,z as xe,I as ye,F as j,k as L,v as H,C as Le,u as Be,x as G,H as ne,a as Pe,l as De}from"./vendor.js";import{u as He}from"./personnel.js";import{_ as Ie,a as Oe}from"./app.js";const Ne={class:"flex items-center"},Ue={class:"flex-shrink-0 mr-3"},Fe={class:"flex-1"},qe={class:"text-sm font-medium"},Xe={key:0,class:"text-sm opacity-90"},Ge={__name:"Toast",props:{type:{type:String,default:"success",validator:p=>["success","error","warning","info"].includes(p)},title:{type:String,required:!0},message:{type:String,default:""},duration:{type:Number,default:3e3},closable:{type:Boolean,default:!0}},emits:["close"],setup(p,{expose:J,emit:S}){const l=p,z=S,v=g(!1);let h=null;const E={success:"bg-green-500 text-white",error:"bg-red-500 text-white",warning:"bg-yellow-500 text-black",info:"bg-blue-500 text-white"},k=R(()=>{const f={success:"CheckCircleIcon",error:"XCircleIcon",warning:"ExclamationTriangleIcon",info:"InformationCircleIcon"};return f[l.type]||f.success}),m=()=>{v.value=!0,l.duration>0&&(h=setTimeout(()=>{D()},l.duration))},D=()=>{v.value=!1,h&&(clearTimeout(h),h=null),setTimeout(()=>{z("close")},300)};return me(()=>{setTimeout(m,10)}),je(()=>{h&&clearTimeout(h)}),J({show:m,close:D}),(f,w)=>(a(),pe(Ee,{to:"body"},[v.value?(a(),r("div",{key:0,class:O([["fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform",E[p.type]||E.success,v.value?"translate-x-0 opacity-100":"translate-x-full opacity-0"],"max-w-sm"])},[e("div",Ne,[e("div",Ue,[(a(),pe(Se(k.value),{class:"w-5 h-5"}))]),e("div",Fe,[e("p",qe,i(p.title),1),p.message?(a(),r("p",Xe,i(p.message),1)):d("",!0)]),p.closable?(a(),r("button",{key:0,onClick:D,class:"flex-shrink-0 ml-3 opacity-70 hover:opacity-100 transition-opacity"},w[0]||(w[0]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))):d("",!0)])],2)):d("",!0)]))}};g([]);function Re(){const p=v=>{const{type:h="success",title:E,message:k="",duration:m=3e3,closable:D=!0}=v,f=document.createElement("div");document.body.appendChild(f);const w=Ae(Ge,{type:h,title:E,message:k,duration:m,closable:D,onClose:()=>{w.unmount(),document.body.removeChild(f)}});w.mount(f)};return{showToast:p,success:(v,h="")=>{p({type:"success",title:v,message:h})},error:(v,h="")=>{p({type:"error",title:v,message:h})},warning:(v,h="")=>{p({type:"warning",title:v,message:h})},info:(v,h="")=>{p({type:"info",title:v,message:h})}}}const Je={class:"space-y-6"},We={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ye={class:"flex items-center justify-between mb-4"},Ke={key:1,class:"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Qe={class:"flex-1"},Ze={class:"text-sm font-medium text-gray-900 dark:text-white"},et={class:"text-xs text-gray-500 dark:text-gray-400"},tt={key:0,class:"mt-1"},st={class:"flex space-x-2"},at={key:2,class:"mt-4"},rt={class:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2"},lt={class:"flex items-center"},ot={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600",fill:"none",viewBox:"0 0 24 24"},nt={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},it={key:0,class:"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6"},dt={class:"flex items-center justify-between mb-4"},ut={class:"flex items-center space-x-3"},ct={key:0,class:"mb-4"},gt={class:"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},vt={key:1,class:"mb-4"},pt={class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200"},mt={key:2},xt={class:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-3"},yt={class:"flex items-center space-x-2"},ft={class:"text-sm font-medium text-gray-900 dark:text-white"},bt={class:"flex items-center space-x-2"},ht={key:0,class:"text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded"},kt={class:"text-xs text-gray-500 dark:text-gray-400"},wt={key:0,class:"text-center"},_t={class:"text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full"},Ct={class:"mt-3"},zt={class:"flex items-center justify-between mb-4"},Tt={class:"max-h-96 overflow-y-auto mb-4"},Vt={class:"space-y-2"},$t=["id","value"],Mt=["for"],jt={class:"text-sm font-medium text-gray-900 dark:text-white"},St={class:"text-xs text-gray-500 dark:text-gray-400"},Et={key:0},At={key:0,class:"text-xs text-gray-400 mt-1"},Lt={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},Bt=["disabled"],Pt={__name:"CVTab",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1}},emits:["user-updated"],setup(p,{emit:J}){const S=p,l=J,{success:z,error:v,info:h}=Re(),E=g(!1),k=g(!1),m=g(0),D=g(!1),f=g(!1),w=g([]),N=g(!1),F=g(null),K=g(0),V=R(()=>{var u,o,_;if(console.log("CVTab - Computing cvAnalysis..."),console.log("CVTab - cv_analysis_data:",(u=S.user.profile)==null?void 0:u.cv_analysis_data),!((o=S.user.profile)!=null&&o.cv_analysis_data))return console.log("CVTab - No cv_analysis_data found"),null;try{const y=JSON.parse(S.user.profile.cv_analysis_data);return console.log("CVTab - Parsed analysis:",y),console.log("CVTab - Skills found:",((_=y.skills)==null?void 0:_.length)||0),y}catch(y){return console.error("CVTab - Error parsing CV analysis data:",y),null}}),W=u=>u?new Date(u).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",I=u=>({beginner:"Principiante",intermediate:"Intermedio",advanced:"Avanzato",expert:"Esperto",1:"Principiante",2:"Base",3:"Intermedio",4:"Avanzato",5:"Esperto"})[u]||"Intermedio",q=()=>{var u;(u=F.value)==null||u.click()},Q=u=>{const o=u.target.files[0];o&&A(o)},X=u=>{u.preventDefault(),D.value=!1;const o=u.dataTransfer.files;o.length>0&&A(o[0])},A=async u=>{var _,y;if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain"].includes(u.type)){v("Formato file non supportato","Usa PDF, DOCX, DOC o TXT.");return}if(u.size>10*1024*1024){v("File troppo grande","Dimensione massima: 10MB.");return}E.value=!0,m.value=0;try{const T=new FormData;T.append("cv_file",u),T.append("analyze_skills","true"),T.append("auto_add_skills","false");const P=setInterval(()=>{m.value<90&&(m.value+=Math.random()*15)},200),ee=await fetch(`/api/personnel/users/${S.user.id}/cv/upload`,{method:"POST",body:T,headers:{"X-CSRFToken":((_=document.querySelector('meta[name="csrf-token"]'))==null?void 0:_.getAttribute("content"))||""}});clearInterval(P),m.value=100;const x=await ee.json();if(console.log("Upload response:",x),x.success){if(E.value=!1,l("user-updated",x.data),(y=x.data.profile)!=null&&y.cv_analysis_data)try{const M=JSON.parse(x.data.profile.cv_analysis_data);console.log("Analysis data:",M),M.skills&&M.skills.length>0?z("CV caricato e analizzato con successo!",`Trovate ${M.skills.length} competenze`):z("CV caricato con successo!","Nessuna competenza estratta dall'AI")}catch(M){console.error("Error parsing analysis data:",M),z("CV caricato con successo!","Errore nel parsing dei dati AI")}else z("CV caricato con successo!","Analisi AI non disponibile"),console.log("No AI analysis data found in response. Profile data:",x.data.profile);F.value&&(F.value.value="")}else throw new Error(x.message||"Errore durante il caricamento")}catch(T){console.error("Errore durante il caricamento del CV:",T),v("Errore durante il caricamento del CV",T.message)}finally{E.value=!1,m.value=0}},Y=async()=>{try{const u=await fetch(`/api/personnel/users/${S.user.id}/cv/download`,{method:"GET",credentials:"include"});if(u.ok){const o=await u.blob(),_=window.URL.createObjectURL(o),y=document.createElement("a");y.href=_,y.download=`CV_${S.user.full_name}.pdf`,document.body.appendChild(y),y.click(),window.URL.revokeObjectURL(_),document.body.removeChild(y)}else{const o=await u.json();throw new Error(o.message||"Errore durante il download")}}catch(u){console.error("Errore durante il download del CV:",u),alert("Errore durante il download del CV: "+u.message)}},$=async()=>{var u;if(confirm("Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata."))try{const _=await(await fetch(`/api/personnel/users/${S.user.id}/cv`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":((u=document.querySelector('meta[name="csrf-token"]'))==null?void 0:u.getAttribute("content"))||""}})).json();if(_.success)l("user-updated",_.data),z("CV eliminato con successo");else throw new Error(_.message||"Errore durante l'eliminazione")}catch(o){console.error("Errore durante l'eliminazione del CV:",o),v("Errore durante l'eliminazione del CV",o.message)}},b=async()=>{var u;if(w.value.length!==0){N.value=!0;try{const o=w.value.map(T=>{const P=V.value.skills[T];return{...P,level:Z(P.level)}}),y=await(await fetch(`/api/personnel/users/${S.user.id}/skills/from-cv`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":((u=document.querySelector('meta[name="csrf-token"]'))==null?void 0:u.getAttribute("content"))||""},body:JSON.stringify({selected_skills:o})})).json();if(y.success){const{total_added:T,total_skipped:P}=y.data;T>0&&z(`Aggiunte ${T} competenze al profilo!`),P>0&&h(`${P} competenze erano già presenti nel profilo`),f.value=!1,w.value=[],l("user-updated")}else throw new Error(y.message||"Errore durante l'aggiunta delle competenze")}catch(o){console.error("Errore durante l'aggiunta delle competenze:",o),v("Errore durante l'aggiunta delle competenze",o.message)}finally{N.value=!1}}},Z=u=>{const o={beginner:1,intermediate:3,advanced:4,expert:5};return typeof u=="number"?Math.max(1,Math.min(5,u)):typeof u=="string"&&o[u.toLowerCase()]||3};return re(E,u=>{u||(m.value=0)}),re(k,u=>{u||(K.value=0)}),(u,o)=>{var _,y,T,P,ee;return a(),r("div",Je,[e("div",We,[e("div",Ye,[o[9]||(o[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"CV Attuale",-1)),p.canEdit&&!E.value?(a(),r("button",{key:0,onClick:q,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},[o[8]||(o[8]=e("svg",{class:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)),B(" "+i((_=p.user.profile)!=null&&_.current_cv_path?"Aggiorna CV":"Carica CV"),1)])):d("",!0)]),!((y=p.user.profile)!=null&&y.current_cv_path)&&p.canEdit?(a(),r("div",{key:0,onDrop:X,onDragover:o[0]||(o[0]=le(()=>{},["prevent"])),onDragenter:o[1]||(o[1]=le(()=>{},["prevent"])),class:O(["border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200",D.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600"])},[o[11]||(o[11]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1)),o[12]||(o[12]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Carica il tuo CV",-1)),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},[o[10]||(o[10]=B(" Trascina qui il file o ")),e("button",{onClick:q,class:"text-blue-600 hover:text-blue-500"},"sfoglia")]),o[13]||(o[13]=e("p",{class:"mt-1 text-xs text-gray-400"},"PDF, DOCX, DOC, TXT (max 10MB)",-1))],34)):(T=p.user.profile)!=null&&T.current_cv_path?(a(),r("div",Ke,[o[17]||(o[17]=e("svg",{class:"w-8 h-8 text-red-600 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)),e("div",Qe,[e("p",Ze,"CV_"+i(p.user.full_name)+".pdf",1),e("p",et," Caricato il "+i(W(p.user.profile.cv_last_updated)),1),p.user.profile.cv_analysis_data?(a(),r("div",tt,o[14]||(o[14]=[e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}," ✨ Analisi AI completata ",-1)]))):d("",!0)]),e("div",st,[e("button",{onClick:Y,class:"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300",title:"Scarica CV"},o[15]||(o[15]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)])),p.canEdit?(a(),r("button",{key:0,onClick:$,class:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Elimina CV"},o[16]||(o[16]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z","clip-rule":"evenodd"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 112 0v4a1 1 0 11-2 0V7z","clip-rule":"evenodd"})],-1)]))):d("",!0)])])):d("",!0),E.value||k.value?(a(),r("div",at,[e("div",rt,[e("div",lt,[k.value?(a(),r("svg",ot,o[18]||(o[18]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):d("",!0),e("span",null,i(k.value?"Analisi AI in corso...":"Caricamento in corso..."),1)]),e("span",null,i(k.value?K.value:m.value)+"%",1)]),e("div",nt,[e("div",{class:O(["h-2 rounded-full transition-all duration-300",k.value?"bg-purple-600":"bg-blue-600"]),style:xe({width:(k.value?K.value:m.value)+"%"})},null,6)])])):d("",!0)]),V.value?(a(),r("div",it,[e("div",dt,[o[20]||(o[20]=ye('<div class="flex items-center" data-v-f92d87fc><div class="w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3" data-v-f92d87fc><svg class="w-4 h-4 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" data-v-f92d87fc><path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" data-v-f92d87fc></path></svg></div><h3 class="text-lg font-medium text-purple-900 dark:text-purple-100" data-v-f92d87fc> Analisi AI del CV </h3></div>',1)),e("div",ut,[p.canEdit&&((P=V.value.skills)==null?void 0:P.length)>0?(a(),r("button",{key:0,onClick:o[2]||(o[2]=x=>f.value=!0),class:"text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center"},o[19]||(o[19]=[e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),B(" Aggiungi Competenze ")]))):d("",!0)])]),V.value.summary?(a(),r("div",ct,[o[21]||(o[21]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-2"},"Profilo Professionale",-1)),e("p",gt,i(V.value.summary),1)])):d("",!0),V.value.experience_years?(a(),r("div",vt,[e("span",pt,[o[22]||(o[22]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),B(" "+i(V.value.experience_years)+" anni di esperienza ",1)])])):d("",!0),((ee=V.value.skills)==null?void 0:ee.length)>0?(a(),r("div",mt,[o[24]||(o[24]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-3"},"Competenze Estratte",-1)),e("div",xt,[(a(!0),r(j,null,L(V.value.skills.slice(0,8),(x,M)=>(a(),r("div",{key:M,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},[e("div",yt,[o[23]||(o[23]=e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"},null,-1)),e("span",ft,i(x.name),1)]),e("div",bt,[x.category?(a(),r("span",ht,i(x.category),1)):d("",!0),e("span",kt,i(I(x.level)),1)])]))),128))]),V.value.skills.length>8?(a(),r("div",wt,[e("span",_t," +"+i(V.value.skills.length-8)+" altre competenze disponibili ",1)])):d("",!0)])):d("",!0)])):d("",!0),e("input",{ref_key:"fileInput",ref:F,type:"file",accept:".pdf,.docx,.doc,.txt",onChange:Q,class:"hidden"},null,544),f.value?(a(),r("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:o[7]||(o[7]=x=>f.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:o[6]||(o[6]=le(()=>{},["stop"]))},[e("div",Ct,[e("div",zt,[o[26]||(o[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Seleziona Competenze da Aggiungere ",-1)),e("button",{onClick:o[3]||(o[3]=x=>f.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},o[25]||(o[25]=[e("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))]),e("div",Tt,[e("div",Vt,[(a(!0),r(j,null,L(V.value.skills,(x,M)=>(a(),r("div",{key:M,class:"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"},[H(e("input",{type:"checkbox",id:`skill-${M}`,"onUpdate:modelValue":o[4]||(o[4]=ie=>w.value=ie),value:M,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,$t),[[Le,w.value]]),e("label",{for:`skill-${M}`,class:"ml-3 flex-1 cursor-pointer"},[e("div",jt,i(x.name),1),e("div",St,[B(i(x.category)+" • Livello "+i(x.level||3)+" ",1),x.years_experience?(a(),r("span",Et," • "+i(x.years_experience)+" anni",1)):d("",!0)]),x.context?(a(),r("div",At,i(x.context),1)):d("",!0)],8,Mt)]))),128))])]),e("div",Lt,[e("button",{onClick:o[5]||(o[5]=x=>f.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{onClick:b,disabled:w.value.length===0||N.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},i(N.value?"Aggiungendo...":`Aggiungi ${w.value.length} competenze`),9,Bt)])])])])):d("",!0)])}}},Dt=Ie(Pt,[["__scopeId","data-v-f92d87fc"]]),Ht={class:"personnel-profile"},It={key:0,class:"flex justify-center items-center h-64"},Ot={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},Nt={class:"flex"},Ut={class:"text-sm text-red-700 dark:text-red-300 mt-1"},Ft={key:2,class:"space-y-6"},qt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Xt={class:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"},Gt={class:"flex items-center space-x-6"},Rt={class:"flex-shrink-0"},Jt={class:"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg"},Wt=["src","alt"],Yt={key:1,class:"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Kt={class:"flex-1 text-white"},Qt={class:"text-3xl font-bold"},Zt={class:"text-blue-100 text-lg"},es={class:"flex items-center space-x-4 mt-2"},ts={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},ss={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},as={class:"flex-shrink-0"},rs={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},ls={class:"flex items-center justify-between mb-2"},os={class:"text-sm text-gray-500 dark:text-gray-400"},ns={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},is={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},ds={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},us={class:"flex items-center justify-between mb-4"},cs={key:0,class:"space-y-3"},gs={class:"flex items-center"},vs={class:"text-gray-900 dark:text-white"},ps={key:0,class:"flex items-center"},ms={class:"text-gray-900 dark:text-white"},xs={key:1,class:"flex items-center"},ys={class:"text-gray-900 dark:text-white"},fs={key:1,class:"space-y-4"},bs={class:"flex space-x-2"},hs=["disabled"],ks={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ws={key:0,class:"space-y-3"},_s={class:"text-sm font-medium text-gray-900 dark:text-white"},Cs={class:"flex items-center space-x-2"},zs={class:"flex space-x-1"},Ts={key:0,class:"text-xs text-green-600 dark:text-green-400"},Vs={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2"},$s={key:1,class:"text-gray-500 dark:text-gray-400 text-sm"},Ms={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},js={class:"flex items-center justify-between mb-4"},Ss={key:0,class:"space-y-3"},Es={key:0,class:"flex items-center"},As={class:"text-sm font-medium text-gray-900 dark:text-white"},Ls={key:1,class:"flex items-center"},Bs={class:"text-sm font-medium text-gray-900 dark:text-white"},Ps={key:2,class:"flex items-center"},Ds={class:"text-sm font-medium text-gray-900 dark:text-white"},Hs={key:3,class:"flex items-center"},Is={class:"text-sm font-medium text-gray-900 dark:text-white"},Os={key:1,class:"space-y-4"},Ns={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Us={key:0,class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},Fs={class:"w-full"},qs={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Xs={class:"border-b border-gray-200 dark:border-gray-700"},Gs={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},Rs=["onClick"],Js={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ws={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ys={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ks={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Qs={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Zs={key:5,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"},ea={class:"p-6"},ta={key:0,class:"space-y-6"},sa={key:0},aa={class:"flex items-center justify-between mb-4"},ra={class:"text-lg font-medium text-gray-900 dark:text-white"},la={class:"text-sm text-gray-500 dark:text-gray-400"},oa={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},na={class:"flex items-start justify-between mb-3"},ia={class:"flex-1"},da={class:"font-medium text-gray-900 dark:text-white mb-1"},ua={class:"text-sm text-gray-500 dark:text-gray-400"},ca={class:"space-y-2 text-sm"},ga={key:0,class:"flex items-center text-gray-600 dark:text-gray-400"},va={key:1,class:"flex items-center text-gray-600 dark:text-gray-400"},pa={key:1,class:"text-center py-12"},ma={key:1,class:"space-y-6"},xa={key:0},ya={class:"flex items-center justify-between mb-4"},fa={class:"text-lg font-medium text-gray-900 dark:text-white"},ba={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},ha={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},ka={class:"flex items-start justify-between mb-3"},wa={class:"flex-1"},_a={class:"font-medium text-gray-900 dark:text-white mb-1"},Ca={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},za={class:"flex flex-col space-y-1 ml-2"},Ta={key:0,class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Va={key:1,class:"text-center py-12"},$a={key:2,class:"space-y-6"},Ma={key:0},ja={class:"flex items-center justify-between mb-4"},Sa={class:"flex items-center space-x-4"},Ea={class:"text-lg font-medium text-gray-900 dark:text-white"},Aa={class:"text-sm text-gray-500 dark:text-gray-400"},La={class:"text-sm text-gray-500 dark:text-gray-400"},Ba={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Pa={class:"flex items-center justify-between mb-2"},Da={class:"font-medium text-gray-900 dark:text-white"},Ha={key:0,class:"text-green-600 dark:text-green-400 text-sm font-medium"},Ia={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-3"},Oa={class:"flex items-center justify-between"},Na={class:"flex items-center space-x-2"},Ua={class:"flex space-x-1"},Fa={class:"text-xs text-gray-500 dark:text-gray-400"},qa={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Xa={key:0,class:"flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4"},Ga={class:"flex items-center space-x-2"},Ra=["disabled"],Ja={class:"text-sm text-gray-700 dark:text-gray-300"},Wa=["disabled"],Ya={class:"text-sm text-gray-500 dark:text-gray-400"},Ka={key:1,class:"text-center py-8"},Qa={key:3,class:"space-y-6"},Za={class:"flex items-center justify-between mb-4"},er={class:"flex items-center space-x-4"},tr={class:"flex items-center space-x-2"},sr={class:"text-lg font-medium text-gray-900 dark:text-white"},ar={class:"flex items-center space-x-4"},rr={class:"text-sm text-gray-500 dark:text-gray-400"},lr={key:0,class:"overflow-x-auto shadow ring-1 ring-black ring-opacity-5 rounded-lg"},or={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},nr={class:"bg-gray-50 dark:bg-gray-700"},ir={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},dr={class:"sticky left-0 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},ur={class:"text-sm font-medium text-gray-900 dark:text-white"},cr={class:"text-xs text-gray-500 dark:text-gray-400"},gr={class:"text-xs text-gray-400 dark:text-gray-500"},vr=["onClick"],pr={key:1,class:"text-gray-300 dark:text-gray-600"},mr={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700"},xr={class:"bg-gray-100 dark:bg-gray-600 font-medium"},yr={class:"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white bg-gray-200 dark:bg-gray-500"},fr={key:1,class:"text-center py-12"},br={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},hr={class:"mt-6"},kr={key:4},wr={class:"mt-3"},_r={class:"flex items-center justify-between mb-4"},Cr={class:"text-lg font-medium text-gray-900 dark:text-white"},zr=["value"],Tr=["value"],Vr={class:"flex items-center justify-end space-x-3 pt-4"},$r=["disabled"],Er={__name:"PersonnelProfile",setup(p){const J=Be();De(),He();const{hasPermission:S}=Oe(),l=g(null),z=g([]),v=g([]),h=g([]),E=g(!1),k=g(null),m=g(!1),D=g(!1),f=g("projects"),w=g(!1),N=g(!1),F=g(!1),K=g(null),V=g([]),W=g([]),I=g(new Date().getMonth()),q=g(new Date().getFullYear()),Q=g("user"),X=g([]),A=g(1),Y=g(6),$=g({phone:"",bio:"",employee_id:"",job_title:"",employment_type:"",work_location:"",weekly_hours:40,address:"",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:""}),b=g({date:"",project_id:"",task_id:"",hours:"",description:""}),Z=R(()=>{if(!l.value)return!1;try{return S.value&&typeof S.value=="function"?S.value("edit_personnel_data"):!1}catch(n){return console.warn("Permission check failed:",n),!1}}),u=R(()=>{var s;if(!((s=l.value)!=null&&s.skills))return[];const n=(A.value-1)*Y.value,t=n+Y.value;return l.value.skills.slice(n,t)}),o=R(()=>{var n;return(n=l.value)!=null&&n.skills?Math.ceil(l.value.skills.length/Y.value):0}),_=R(()=>new Date(q.value,I.value).toLocaleDateString("it-IT",{month:"long",year:"numeric"})),y=R(()=>{const n=q.value,t=I.value,s=new Date(n,t+1,0).getDate(),c=new Date,C=[];for(let U=1;U<=s;U++){const se=new Date(n,t,U),ae=se.toISOString().split("T")[0];C.push({day:U,date:ae,isWeekend:se.getDay()===0||se.getDay()===6,isToday:ae===c.toISOString().split("T")[0]})}return C}),T=R(()=>{var n,t;return[{id:"projects",name:"Progetti",count:z.value.length},{id:"tasks",name:"Task",count:v.value.length},{id:"skills",name:"Competenze",count:((t=(n=l.value)==null?void 0:n.skills)==null?void 0:t.length)||0},{id:"timesheet",name:"Timesheet",count:h.value.length},{id:"cv",name:"CV"}]}),P=n=>n?new Date(n).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",ee=n=>({full_time:"Tempo Pieno",part_time:"Part Time",contractor:"Consulente",intern:"Stagista"})[n]||n,x=n=>{f.value=n,n==="skills"&&(A.value=1)},M=()=>{var n,t,s,c,C,U,se,ae,ve;l.value&&($.value={phone:l.value.phone||"",bio:l.value.bio||"",employee_id:((n=l.value.profile)==null?void 0:n.employee_id)||"",job_title:((t=l.value.profile)==null?void 0:t.job_title)||"",employment_type:((s=l.value.profile)==null?void 0:s.employment_type)||"",work_location:((c=l.value.profile)==null?void 0:c.work_location)||"",weekly_hours:((C=l.value.profile)==null?void 0:C.weekly_hours)||40,address:((U=l.value.profile)==null?void 0:U.address)||"",emergency_contact_name:((se=l.value.profile)==null?void 0:se.emergency_contact_name)||"",emergency_contact_phone:((ae=l.value.profile)==null?void 0:ae.emergency_contact_phone)||"",emergency_contact_relationship:((ve=l.value.profile)==null?void 0:ve.emergency_contact_relationship)||""})},ie=()=>{m.value=!1,M()},fe=async()=>{var n;if(l.value){D.value=!0;try{const s=await(await fetch(`/api/personnel/users/${l.value.id}`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"))||""},body:JSON.stringify($.value)})).json();if(s.success)l.value=s.data.user,m.value=!1,console.log("Profilo aggiornato con successo");else throw new Error(s.message||"Errore durante il salvataggio")}catch(t){console.error("Errore durante il salvataggio:",t),k.value=t.message}finally{D.value=!1}}},de=async n=>{E.value=!0,k.value=null;try{const t=await fetch(`/api/personnel/users/${n}`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const s=await t.json();if(s.success)l.value=s.data.user,M();else throw new Error(s.message||"Errore nel caricamento del profilo")}catch(t){console.error("Error fetching user profile:",t),k.value=t.message}finally{E.value=!1}},ce=async n=>{try{const t=await fetch(`/api/tasks?assignee_id=${n}&limit=20`,{credentials:"include"});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const s=await t.json();s.success&&(v.value=s.data.tasks||[])}catch(t){console.error("Error fetching user tasks:",t)}},ue=async n=>{try{const t=new Date,s=new Date;s.setDate(s.getDate()-30);const c=await fetch(`/api/timesheets?user_id=${n}&start_date=${s.toISOString().split("T")[0]}&end_date=${t.toISOString().split("T")[0]}&limit=50`,{credentials:"include"});if(!c.ok)throw new Error(`HTTP ${c.status}: ${c.statusText}`);const C=await c.json();C.success&&(h.value=C.data||[])}catch(t){console.error("Error fetching user timesheets:",t)}},ge=()=>{b.value={date:new Date().toISOString().split("T")[0],project_id:"",task_id:"",hours:"",description:""}},oe=()=>{w.value=!1,N.value=!1,K.value=null,ge(),W.value=[]},be=()=>{b.value.task_id="",b.value.project_id?he(b.value.project_id):W.value=[]},he=async n=>{try{const t=await fetch(`/api/tasks?project_id=${n}`,{credentials:"include"});if(t.ok){const s=await t.json();s.success&&(W.value=s.data.tasks||[])}}catch(t){console.error("Error loading tasks:",t),W.value=[]}},ke=async()=>{var n;F.value=!0;try{const t={user_id:l.value.id,date:b.value.date,project_id:parseInt(b.value.project_id),task_id:b.value.task_id?parseInt(b.value.task_id):null,hours:parseFloat(b.value.hours),description:b.value.description},s=N.value?`/api/timesheets/${K.value.id}`:"/api/timesheets",c=N.value?"PUT":"POST",U=await(await fetch(s,{method:c,headers:{"Content-Type":"application/json","X-CSRFToken":((n=document.querySelector('meta[name="csrf-token"]'))==null?void 0:n.getAttribute("content"))||""},credentials:"include",body:JSON.stringify(t)})).json();if(U.success)oe(),await ue(l.value.id),console.log("Timesheet salvato con successo");else throw new Error(U.message||"Errore durante il salvataggio")}catch(t){console.error("Errore durante il salvataggio:",t),k.value=t.message}finally{F.value=!1}},we=async()=>{try{const n=await fetch("/api/projects",{credentials:"include"});if(n.ok){const t=await n.json();t.success&&(V.value=t.data.projects||[])}}catch(n){console.error("Error loading projects:",n),V.value=[]}},_e=()=>{I.value===0?(I.value=11,q.value--):I.value--,te()},Ce=()=>{I.value===11?(I.value=0,q.value++):I.value++,te()},te=async()=>{if(l.value)try{const n=new Date(q.value,I.value,1).toISOString().split("T")[0],t=new Date(q.value,I.value+1,0).toISOString().split("T")[0],s=Q.value==="user"?l.value.id:"",c=await fetch(`/api/timesheets?user_id=${s}&start_date=${n}&end_date=${t}`,{credentials:"include"});if(c.ok){const C=await c.json();C.success&&ze(C.data||[])}}catch(n){console.error("Error loading timesheet grid:",n),X.value=[]}},ze=n=>{const t={};n.forEach(s=>{const c=`${s.project_id}-${s.task_id||"no-task"}`;t[c]||(t[c]={taskId:s.task_id||`project-${s.project_id}`,taskName:s.task_name||"Attività Generica",projectName:s.project_name,assignees:s.user_name||"N/A",hours:{},total:0});const C=s.date;t[c].hours[C]||(t[c].hours[C]=0),t[c].hours[C]+=parseFloat(s.hours||0),t[c].total+=parseFloat(s.hours||0)}),X.value=Object.values(t).map(s=>({...s,total:s.total.toFixed(1),hours:Object.fromEntries(Object.entries(s.hours).map(([c,C])=>[c,C.toFixed(1)]))}))},Te=n=>{const t=X.value.reduce((s,c)=>s+parseFloat(c.hours[n]||0),0);return t>0?t.toFixed(1):"0"},Ve=()=>X.value.reduce((t,s)=>t+parseFloat(s.total||0),0).toFixed(1),$e=(n,t,s)=>{b.value.date=t,w.value=!0},Me=async n=>{n?n.id&&n.profile?l.value=n:(n.cv_path&&(l.value.profile.current_cv_path=n.cv_path),n.cv_last_updated!==void 0&&(l.value.profile.cv_last_updated=n.cv_last_updated),n.analysis&&(l.value.profile.cv_analysis_data=JSON.stringify(n.analysis)),n.profile_completion!==void 0&&(l.value.profile.profile_completion=n.profile_completion),n.cv_path===null&&(l.value.profile.current_cv_path=null,l.value.profile.cv_last_updated=null,l.value.profile.cv_analysis_data=null)):await de(l.value.id)};return me(async()=>{const n=J.params.id;if(!n){k.value="ID utente non specificato";return}ge(),await de(n),l.value&&(z.value=l.value.projects||[],await ce(n),await ue(n)),await we(),await te()}),re(()=>J.params.id,async n=>{n&&(await de(n),l.value&&(z.value=l.value.projects||[],await ce(n),await ue(n),await te()))}),re(Q,()=>{te()}),re(f,n=>{n==="timesheet"&&te()}),(n,t)=>(a(),r(j,null,[e("div",Ht,[E.value?(a(),r("div",It,t[20]||(t[20]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):k.value?(a(),r("div",Ot,[e("div",Nt,[t[22]||(t[22]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[21]||(t[21]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",Ut,i(k.value),1)])])])):l.value?(a(),r("div",Ft,[e("div",qt,[e("div",Xt,[e("div",Gt,[e("div",Rt,[e("div",Jt,[l.value.profile_image?(a(),r("img",{key:0,src:l.value.profile_image,alt:l.value.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,Wt)):(a(),r("div",Yt,t[23]||(t[23]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)])))])]),e("div",Kt,[e("h1",Qt,i(l.value.full_name),1),e("p",Zt,i(l.value.position||"Posizione non specificata"),1),e("div",es,[l.value.department?(a(),r("span",ts,[t[24]||(t[24]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),B(" "+i(l.value.department.name),1)])):d("",!0),e("span",ss,[t[25]||(t[25]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),B(" "+i(l.value.role),1)])])]),e("div",as,[Z.value?(a(),r("button",{key:0,onClick:t[0]||(t[0]=s=>m.value=!m.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"},[t[26]||(t[26]=e("svg",{class:"w-5 h-5 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)),B(" "+i(m.value?"Annulla":"Modifica"),1)])):d("",!0)])])]),l.value.profile&&l.value.profile.profile_completion!==void 0?(a(),r("div",rs,[e("div",ls,[t[27]||(t[27]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",os,i(l.value.profile.profile_completion)+"%",1)]),e("div",ns,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:xe({width:l.value.profile.profile_completion+"%"})},null,4)])])):d("",!0)]),e("div",is,[e("div",ds,[e("div",us,[t[29]||(t[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni di Contatto",-1)),Z.value&&!m.value?(a(),r("button",{key:0,onClick:t[1]||(t[1]=s=>m.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[28]||(t[28]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):d("",!0)]),m.value?(a(),r("div",fs,[e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),H(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>$.value.phone=s),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[G,$.value.phone]])]),e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Bio",-1)),H(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=s=>$.value.bio=s),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[G,$.value.bio]])]),e("div",bs,[e("button",{onClick:fe,disabled:D.value,class:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200"},i(D.value?"Salvataggio...":"Salva"),9,hs),e("button",{onClick:ie,class:"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200"}," Annulla ")])])):(a(),r("div",cs,[e("div",gs,[t[30]||(t[30]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),e("span",vs,i(l.value.email),1)]),l.value.phone?(a(),r("div",ps,[t[31]||(t[31]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),e("span",ms,i(l.value.phone),1)])):d("",!0),l.value.hire_date?(a(),r("div",xs,[t[32]||(t[32]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),e("span",ys,"Assunto il "+i(P(l.value.hire_date)),1)])):d("",!0)]))]),e("div",ks,[t[35]||(t[35]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Competenze Principali",-1)),l.value.skills&&l.value.skills.length>0?(a(),r("div",ws,[(a(!0),r(j,null,L(l.value.skills.slice(0,4),s=>(a(),r("div",{key:s.id,class:"flex items-center justify-between"},[e("span",_s,i(s.name),1),e("div",Cs,[e("div",zs,[(a(),r(j,null,L(5,c=>e("div",{key:c,class:O(["w-2 h-2 rounded-full",c<=s.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),s.certified?(a(),r("span",Ts,"✓")):d("",!0)])]))),128)),l.value.skills.length>4?(a(),r("div",Vs," +"+i(l.value.skills.length-4)+" altre competenze ",1)):d("",!0)])):(a(),r("div",$s," Nessuna competenza registrata "))]),l.value.profile?(a(),r("div",Ms,[e("div",js,[t[37]||(t[37]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni HR",-1)),Z.value&&!m.value?(a(),r("button",{key:0,onClick:t[4]||(t[4]=s=>m.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[36]||(t[36]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):d("",!0)]),m.value?(a(),r("div",Os,[e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"ID Dipendente",-1)),H(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>$.value.employee_id=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[G,$.value.employee_id]])]),e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Titolo Lavoro",-1)),H(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>$.value.job_title=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[G,$.value.job_title]])]),e("div",null,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Contratto",-1)),H(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>$.value.employment_type=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[48]||(t[48]=[ye('<option value="">Seleziona tipo</option><option value="full_time">Tempo Pieno</option><option value="part_time">Part Time</option><option value="contractor">Consulente</option><option value="intern">Stagista</option>',5)]),512),[[ne,$.value.employment_type]])]),e("div",null,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore Settimanali",-1)),H(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>$.value.weekly_hours=s),type:"number",min:"1",max:"60",step:"0.5",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[G,$.value.weekly_hours]])])])):(a(),r("div",Ss,[l.value.profile.employee_id?(a(),r("div",Es,[t[39]||(t[39]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)),e("div",null,[t[38]||(t[38]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"ID Dipendente",-1)),e("p",As,i(l.value.profile.employee_id),1)])])):d("",!0),l.value.profile.job_title?(a(),r("div",Ls,[t[41]||(t[41]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),e("div",null,[t[40]||(t[40]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Titolo",-1)),e("p",Bs,i(l.value.profile.job_title),1)])])):d("",!0),l.value.profile.employment_type?(a(),r("div",Ps,[t[43]||(t[43]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),e("div",null,[t[42]||(t[42]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Tipo Contratto",-1)),e("p",Ds,i(ee(l.value.profile.employment_type)),1)])])):d("",!0),l.value.profile.weekly_hours?(a(),r("div",Hs,[t[45]||(t[45]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("div",null,[t[44]||(t[44]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Ore Settimanali",-1)),e("p",Is,i(l.value.profile.weekly_hours)+"h",1)])])):d("",!0)]))])):d("",!0)]),l.value.bio||m.value?(a(),r("div",Ns,[t[51]||(t[51]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),m.value?d("",!0):(a(),r("p",Us,i(l.value.bio||"Nessuna bio disponibile"),1))])):d("",!0),e("div",Fs,[e("div",qs,[e("div",Xs,[e("nav",Gs,[(a(!0),r(j,null,L(T.value,s=>(a(),r("button",{key:s.id,onClick:c=>x(s.id),class:O([f.value===s.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[s.id==="projects"?(a(),r("svg",Js,t[52]||(t[52]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):s.id==="tasks"?(a(),r("svg",Ws,t[53]||(t[53]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):s.id==="skills"?(a(),r("svg",Ys,t[54]||(t[54]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):s.id==="timesheet"?(a(),r("svg",Ks,t[55]||(t[55]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"},null,-1)]))):s.id==="cv"?(a(),r("svg",Qs,t[56]||(t[56]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"},null,-1)]))):d("",!0),B(" "+i(s.name)+" ",1),s.count!==void 0?(a(),r("span",Zs,i(s.count),1)):d("",!0)],10,Rs))),128))])]),e("div",ea,[f.value==="projects"?(a(),r("div",ta,[z.value.length>0?(a(),r("div",sa,[e("div",aa,[e("h3",ra," Progetti Assegnati ("+i(z.value.length)+") ",1),e("div",la,i(z.value.filter(s=>s.status==="active").length)+" attivi ",1)]),e("div",oa,[(a(!0),r(j,null,L(z.value,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer"},[e("div",na,[e("div",ia,[e("h4",da,i(s.name),1),e("p",ua,i(s.role||"Team Member"),1)]),e("span",{class:O(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2",s.status==="active"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":s.status==="completed"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":s.status==="on_hold"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},i(s.status),3)]),e("div",ca,[s.client?(a(),r("div",ga,[t[57]||(t[57]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),B(" "+i(s.client),1)])):d("",!0),s.deadline?(a(),r("div",va,[t[58]||(t[58]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),B(" "+i(P(s.deadline)),1)])):d("",!0)])]))),128))])])):(a(),r("div",pa,t[59]||(t[59]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1)])))])):d("",!0),f.value==="tasks"?(a(),r("div",ma,[v.value.length>0?(a(),r("div",xa,[e("div",ya,[e("h3",fa," Task Assegnati ("+i(v.value.length)+") ",1),e("div",ba,[e("span",null,i(v.value.filter(s=>s.status==="in-progress").length)+" in corso",1),e("span",null,i(v.value.filter(s=>s.status==="done").length)+" completati",1)])]),e("div",ha,[(a(!0),r(j,null,L(v.value,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",ka,[e("div",wa,[e("h4",_a,i(s.name),1),s.project_name?(a(),r("p",Ca,i(s.project_name),1)):d("",!0)]),e("div",za,[e("span",{class:O(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",s.priority==="urgent"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":s.priority==="high"?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":s.priority==="medium"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"])},i(s.priority),3),e("span",{class:O(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",s.status==="done"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":s.status==="in-progress"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":s.status==="review"?"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},i(s.status),3)])]),s.due_date?(a(),r("div",Ta,[t[60]||(t[60]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),B(" Scadenza: "+i(P(s.due_date)),1)])):d("",!0)]))),128))])])):(a(),r("div",Va,t[61]||(t[61]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono stati assegnati task a questo utente.",-1)])))])):d("",!0),f.value==="skills"?(a(),r("div",$a,[l.value.skills&&l.value.skills.length>0?(a(),r("div",Ma,[e("div",ja,[e("div",Sa,[e("h3",Ea," Competenze ("+i(l.value.skills.length)+") ",1),e("span",Aa," Pagina "+i(A.value)+" di "+i(o.value),1)]),e("div",La,i(l.value.skills.filter(s=>s.certified).length)+" certificate ",1)]),e("div",Ba,[(a(!0),r(j,null,L(u.value,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",Pa,[e("h4",Da,i(s.name),1),s.certified?(a(),r("span",Ha,"✓ Certificato")):d("",!0)]),s.category?(a(),r("p",Ia,i(s.category),1)):d("",!0),e("div",Oa,[e("div",Na,[t[62]||(t[62]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",Ua,[(a(),r(j,null,L(5,c=>e("div",{key:c,class:O(["w-3 h-3 rounded-full",c<=s.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),e("span",Fa,"("+i(s.proficiency_level)+"/5)",1)]),s.years_experience?(a(),r("span",qa,i(s.years_experience)+i(s.years_experience===1?" anno":" anni"),1)):d("",!0)])]))),128))]),o.value>1?(a(),r("div",Xa,[e("div",Ga,[e("button",{onClick:t[9]||(t[9]=s=>A.value=Math.max(1,A.value-1)),disabled:A.value===1,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Precedente ",8,Ra),e("span",Ja," Pagina "+i(A.value)+" di "+i(o.value),1),e("button",{onClick:t[10]||(t[10]=s=>A.value=Math.min(o.value,A.value+1)),disabled:A.value===o.value,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Successiva ",8,Wa)]),e("div",Ya," Mostrando "+i((A.value-1)*Y.value+1)+"-"+i(Math.min(A.value*Y.value,l.value.skills.length))+" di "+i(l.value.skills.length)+" competenze ",1)])):d("",!0)])):(a(),r("div",Ka,t[63]||(t[63]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono state registrate competenze per questo utente.",-1)])))])):d("",!0),f.value==="timesheet"?(a(),r("div",Qa,[e("div",Za,[e("div",er,[t[66]||(t[66]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Timesheet Dettaglio",-1)),e("div",tr,[e("button",{onClick:_e,class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[64]||(t[64]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)])),e("span",sr,i(_.value),1),e("button",{onClick:Ce,class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[65]||(t[65]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))])]),e("div",ar,[e("div",rr,[t[68]||(t[68]=B(" Vista: ")),H(e("select",{"onUpdate:modelValue":t[11]||(t[11]=s=>Q.value=s),class:"ml-1 bg-transparent border-none text-blue-600 dark:text-blue-400 font-medium"},t[67]||(t[67]=[e("option",{value:"all"},"Tutti i membri",-1),e("option",{value:"user"},"Solo questo utente",-1)]),512),[[ne,Q.value]])]),e("button",{onClick:t[12]||(t[12]=s=>w.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"},t[69]||(t[69]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),B(" Aggiungi Ore ")]))])]),X.value.length>0?(a(),r("div",lr,[e("table",or,[e("thead",nr,[e("tr",null,[t[70]||(t[70]=e("th",{class:"sticky left-0 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," TASK ",-1)),(a(!0),r(j,null,L(y.value,s=>(a(),r("th",{key:s.date,class:O(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",s.isWeekend?"bg-gray-100 dark:bg-gray-600":"",s.isToday?"bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300":""])},i(s.day),3))),128)),t[71]||(t[71]=e("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-100 dark:bg-gray-600"}," TOTALE ",-1))])]),e("tbody",ir,[(a(!0),r(j,null,L(X.value,s=>(a(),r("tr",{key:s.taskId,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",dr,[e("div",ur,i(s.taskName),1),e("div",cr,i(s.projectName),1),e("div",gr,i(s.assignees),1)]),(a(!0),r(j,null,L(y.value,c=>(a(),r("td",{key:c.date,class:O(["px-2 py-3 text-center text-sm",c.isWeekend?"bg-gray-50 dark:bg-gray-700":"",c.isToday?"bg-blue-50 dark:bg-blue-900":""])},[s.hours[c.date]?(a(),r("div",{key:0,class:"text-blue-600 dark:text-blue-400 font-medium cursor-pointer hover:text-blue-800 dark:hover:text-blue-300",onClick:C=>$e(s.taskId,c.date,s.hours[c.date])},i(s.hours[c.date]),9,vr)):(a(),r("div",pr,"-"))],2))),128)),e("td",mr,i(s.total),1)]))),128)),e("tr",xr,[t[72]||(t[72]=e("td",{class:"sticky left-0 bg-gray-100 dark:bg-gray-600 px-4 py-3 text-sm font-bold text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-500"}," TOTALE GIORNALIERO ",-1)),(a(!0),r(j,null,L(y.value,s=>(a(),r("td",{key:s.date,class:O(["px-2 py-3 text-center text-sm font-bold text-gray-900 dark:text-white",s.isWeekend?"bg-gray-200 dark:bg-gray-500":""])},i(Te(s.date)),3))),128)),e("td",yr,i(Ve()),1)])])])])):(a(),r("div",fr,[t[74]||(t[74]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),t[75]||(t[75]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun timesheet",-1)),e("p",br,"Non sono stati registrati timesheet per "+i(_.value)+".",1),e("div",hr,[e("button",{onClick:t[13]||(t[13]=s=>w.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center mx-auto"},t[73]||(t[73]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),B(" Aggiungi Prima Registrazione ")]))])]))])):d("",!0),f.value==="cv"?(a(),r("div",kr,[Pe(Dt,{user:l.value,"can-edit":Z.value,onUserUpdated:Me},null,8,["user","can-edit"])])):d("",!0)])])])])):d("",!0)]),w.value||N.value?(a(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:oe},[e("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[19]||(t[19]=le(()=>{},["stop"]))},[e("div",wr,[e("div",_r,[e("h3",Cr,i(N.value?"Modifica Timesheet":"Aggiungi Timesheet"),1),e("button",{onClick:oe,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[76]||(t[76]=[e("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))]),e("form",{onSubmit:le(ke,["prevent"]),class:"space-y-4"},[e("div",null,[t[77]||(t[77]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),H(e("input",{"onUpdate:modelValue":t[14]||(t[14]=s=>b.value.date=s),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[G,b.value.date]])]),e("div",null,[t[79]||(t[79]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),H(e("select",{"onUpdate:modelValue":t[15]||(t[15]=s=>b.value.project_id=s),required:"",onChange:be,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[78]||(t[78]=e("option",{value:""},"Seleziona progetto",-1)),(a(!0),r(j,null,L(V.value,s=>(a(),r("option",{key:s.id,value:s.id},i(s.name),9,zr))),128))],544),[[ne,b.value.project_id]])]),e("div",null,[t[81]||(t[81]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task (opzionale)",-1)),H(e("select",{"onUpdate:modelValue":t[16]||(t[16]=s=>b.value.task_id=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[80]||(t[80]=e("option",{value:""},"Nessun task specifico",-1)),(a(!0),r(j,null,L(W.value,s=>(a(),r("option",{key:s.id,value:s.id},i(s.name),9,Tr))),128))],512),[[ne,b.value.task_id]])]),e("div",null,[t[82]||(t[82]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),H(e("input",{"onUpdate:modelValue":t[17]||(t[17]=s=>b.value.hours=s),type:"number",step:"0.25",min:"0.25",max:"24",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[G,b.value.hours]])]),e("div",null,[t[83]||(t[83]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),H(e("textarea",{"onUpdate:modelValue":t[18]||(t[18]=s=>b.value.description=s),rows:"3",placeholder:"Descrivi il lavoro svolto...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[G,b.value.description]])]),e("div",Vr,[e("button",{type:"button",onClick:oe,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{type:"submit",disabled:F.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},i(F.value?"Salvando...":N.value?"Aggiorna":"Salva"),9,$r)])],32)])])])):d("",!0)],64))}};export{Er as default};
