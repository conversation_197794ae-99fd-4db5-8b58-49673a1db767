import{r as c,f as J,A as xe,P as Se,h as me,o as a,c as r,g as d,j as e,M as Ae,t as i,n as O,Q as Ee,K as Be,w as le,m as A,s as oe,z as ye,H as fe,F as M,k as S,v as D,C as Le,u as De,x as W,G as ie,a as Ie,l as He}from"./vendor.js";import{u as Pe}from"./personnel.js";import{_ as Oe,c as U,a as Ne}from"./app.js";const Ue={class:"flex items-center"},Fe={class:"flex-shrink-0 mr-3"},qe={class:"flex-1"},Ge={class:"text-sm font-medium"},Xe={key:0,class:"text-sm opacity-90"},We={__name:"Toast",props:{type:{type:String,default:"success",validator:p=>["success","error","warning","info"].includes(p)},title:{type:String,required:!0},message:{type:String,default:""},duration:{type:Number,default:3e3},closable:{type:Boolean,default:!0}},emits:["close"],setup(p,{expose:R,emit:E}){const l=p,C=E,v=c(!1);let h=null;const $={success:"bg-green-500 text-white",error:"bg-red-500 text-white",warning:"bg-yellow-500 text-black",info:"bg-blue-500 text-white"},w=J(()=>{const f={success:"CheckCircleIcon",error:"XCircleIcon",warning:"ExclamationTriangleIcon",info:"InformationCircleIcon"};return f[l.type]||f.success}),m=()=>{v.value=!0,l.duration>0&&(h=setTimeout(()=>{L()},l.duration))},L=()=>{v.value=!1,h&&(clearTimeout(h),h=null),setTimeout(()=>{C("close")},300)};return xe(()=>{setTimeout(m,10)}),Se(()=>{h&&clearTimeout(h)}),R({show:m,close:L}),(f,_)=>(a(),me(Ee,{to:"body"},[v.value?(a(),r("div",{key:0,class:O([["fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform",$[p.type]||$.success,v.value?"translate-x-0 opacity-100":"translate-x-full opacity-0"],"max-w-sm"])},[e("div",Ue,[e("div",Fe,[(a(),me(Ae(w.value),{class:"w-5 h-5"}))]),e("div",qe,[e("p",Ge,i(p.title),1),p.message?(a(),r("p",Xe,i(p.message),1)):d("",!0)]),p.closable?(a(),r("button",{key:0,onClick:L,class:"flex-shrink-0 ml-3 opacity-70 hover:opacity-100 transition-opacity"},_[0]||(_[0]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))):d("",!0)])],2)):d("",!0)]))}};c([]);function Je(){const p=v=>{const{type:h="success",title:$,message:w="",duration:m=3e3,closable:L=!0}=v,f=document.createElement("div");document.body.appendChild(f);const _=Be(We,{type:h,title:$,message:w,duration:m,closable:L,onClose:()=>{_.unmount(),document.body.removeChild(f)}});_.mount(f)};return{showToast:p,success:(v,h="")=>{p({type:"success",title:v,message:h})},error:(v,h="")=>{p({type:"error",title:v,message:h})},warning:(v,h="")=>{p({type:"warning",title:v,message:h})},info:(v,h="")=>{p({type:"info",title:v,message:h})}}}const Re={class:"space-y-6"},Ke={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ye={class:"flex items-center justify-between mb-4"},Qe={key:1,class:"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Ze={class:"flex-1"},et={class:"text-sm font-medium text-gray-900 dark:text-white"},tt={class:"text-xs text-gray-500 dark:text-gray-400"},st={key:0,class:"mt-1"},at={class:"flex space-x-2"},rt={key:2,class:"mt-4"},lt={class:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2"},ot={class:"flex items-center"},nt={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600",fill:"none",viewBox:"0 0 24 24"},it={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},dt={key:0,class:"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6"},ut={class:"flex items-center justify-between mb-4"},ct={class:"flex items-center space-x-3"},gt={key:0,class:"mb-4"},vt={class:"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},pt={key:1,class:"mb-4"},mt={class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200"},xt={key:2},yt={class:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-3"},ft={class:"flex items-center space-x-2"},bt={class:"text-sm font-medium text-gray-900 dark:text-white"},ht={class:"flex items-center space-x-2"},kt={key:0,class:"text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded"},wt={class:"text-xs text-gray-500 dark:text-gray-400"},_t={key:0,class:"text-center"},Ct={class:"text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full"},zt={class:"mt-3"},Vt={class:"flex items-center justify-between mb-4"},Tt={class:"max-h-96 overflow-y-auto mb-4"},Mt={class:"space-y-2"},$t=["id","value"],jt=["for"],St={class:"text-sm font-medium text-gray-900 dark:text-white"},At={class:"text-xs text-gray-500 dark:text-gray-400"},Et={key:0},Bt={key:0,class:"text-xs text-gray-400 mt-1"},Lt={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},Dt=["disabled"],It={__name:"CVTab",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1}},emits:["user-updated"],setup(p,{emit:R}){const E=p,l=R,{success:C,error:v,info:h}=Je(),$=c(!1),w=c(!1),m=c(0),L=c(!1),f=c(!1),_=c([]),I=c(!1),F=c(null),Z=c(0),z=J(()=>{var u;if(!((u=E.user.profile)!=null&&u.cv_analysis_data))return null;try{return JSON.parse(E.user.profile.cv_analysis_data)}catch(o){return console.error("Error parsing CV analysis data:",o),null}}),K=u=>u?new Date(u).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",H=u=>({beginner:"Principiante",intermediate:"Intermedio",advanced:"Avanzato",expert:"Esperto",1:"Principiante",2:"Base",3:"Intermedio",4:"Avanzato",5:"Esperto"})[u]||"Intermedio",q=()=>{var u;(u=F.value)==null||u.click()},ee=u=>{const o=u.target.files[0];o&&j(o)},X=u=>{u.preventDefault(),L.value=!1;const o=u.dataTransfer.files;o.length>0&&j(o[0])},j=async u=>{var T;if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain"].includes(u.type)){v("Formato file non supportato","Usa PDF, DOCX, DOC o TXT.");return}if(u.size>10*1024*1024){v("File troppo grande","Dimensione massima: 10MB.");return}$.value=!0,m.value=0;try{const y=new FormData;y.append("cv_file",u),y.append("analyze_skills","true"),y.append("auto_add_skills","false");const N=setInterval(()=>{m.value<90&&(m.value+=Math.random()*15)},200),B=await U.post(`/api/personnel/users/${E.user.id}/cv/upload`,y,{headers:{"Content-Type":"multipart/form-data"}});clearInterval(N),m.value=100;const P=B.data;if(console.log("Upload response:",P),P.success){if($.value=!1,l("user-updated",P.data),(T=P.data.profile)!=null&&T.cv_analysis_data)try{const x=JSON.parse(P.data.profile.cv_analysis_data);console.log("Analysis data:",x),x.skills&&x.skills.length>0?C("CV caricato e analizzato con successo!",`Trovate ${x.skills.length} competenze`):C("CV caricato con successo!","Nessuna competenza estratta dall'AI")}catch(x){console.error("Error parsing analysis data:",x),C("CV caricato con successo!","Errore nel parsing dei dati AI")}else C("CV caricato con successo!","Analisi AI non disponibile"),console.log("No AI analysis data found in response. Profile data:",P.data.profile);F.value&&(F.value.value="")}else throw new Error(P.message||"Errore durante il caricamento")}catch(y){console.error("Errore durante il caricamento del CV:",y),v("Errore durante il caricamento del CV",y.message)}finally{$.value=!1,m.value=0}},Y=async()=>{try{const o=(await U.get(`/api/personnel/users/${E.user.id}/cv/download`,{responseType:"blob"})).data,T=window.URL.createObjectURL(o),y=document.createElement("a");y.href=T,y.download=`CV_${E.user.full_name}.pdf`,document.body.appendChild(y),y.click(),window.URL.revokeObjectURL(T),document.body.removeChild(y)}catch(u){console.error("Errore durante il download del CV:",u),alert("Errore durante il download del CV: "+u.message)}},V=async()=>{var u;if(confirm("Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata."))try{const T=await(await fetch(`/api/personnel/users/${E.user.id}/cv`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":((u=document.querySelector('meta[name="csrf-token"]'))==null?void 0:u.getAttribute("content"))||""}})).json();if(T.success)l("user-updated",T.data),C("CV eliminato con successo");else throw new Error(T.message||"Errore durante l'eliminazione")}catch(o){console.error("Errore durante l'eliminazione del CV:",o),v("Errore durante l'eliminazione del CV",o.message)}},b=async()=>{var u;if(_.value.length!==0){I.value=!0;try{const o=_.value.map(N=>{const B=z.value.skills[N];return{...B,level:te(B.level)}}),y=await(await fetch(`/api/personnel/users/${E.user.id}/skills/from-cv`,{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":((u=document.querySelector('meta[name="csrf-token"]'))==null?void 0:u.getAttribute("content"))||""},body:JSON.stringify({selected_skills:o})})).json();if(y.success){const{total_added:N,total_skipped:B}=y.data;N>0&&C(`Aggiunte ${N} competenze al profilo!`),B>0&&h(`${B} competenze erano già presenti nel profilo`),f.value=!1,_.value=[],l("user-updated")}else throw new Error(y.message||"Errore durante l'aggiunta delle competenze")}catch(o){console.error("Errore durante l'aggiunta delle competenze:",o),v("Errore durante l'aggiunta delle competenze",o.message)}finally{I.value=!1}}},te=u=>{const o={beginner:1,intermediate:3,advanced:4,expert:5};return typeof u=="number"?Math.max(1,Math.min(5,u)):typeof u=="string"&&o[u.toLowerCase()]||3};return le($,u=>{u||(m.value=0)}),le(w,u=>{u||(Z.value=0)}),(u,o)=>{var T,y,N,B,P;return a(),r("div",Re,[e("div",Ke,[e("div",Ye,[o[9]||(o[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"CV Attuale",-1)),p.canEdit&&!$.value?(a(),r("button",{key:0,onClick:q,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},[o[8]||(o[8]=e("svg",{class:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)),A(" "+i((T=p.user.profile)!=null&&T.current_cv_path?"Aggiorna CV":"Carica CV"),1)])):d("",!0)]),!((y=p.user.profile)!=null&&y.current_cv_path)&&p.canEdit?(a(),r("div",{key:0,onDrop:X,onDragover:o[0]||(o[0]=oe(()=>{},["prevent"])),onDragenter:o[1]||(o[1]=oe(()=>{},["prevent"])),class:O(["border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200",L.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600"])},[o[11]||(o[11]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1)),o[12]||(o[12]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Carica il tuo CV",-1)),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},[o[10]||(o[10]=A(" Trascina qui il file o ")),e("button",{onClick:q,class:"text-blue-600 hover:text-blue-500"},"sfoglia")]),o[13]||(o[13]=e("p",{class:"mt-1 text-xs text-gray-400"},"PDF, DOCX, DOC, TXT (max 10MB)",-1))],34)):(N=p.user.profile)!=null&&N.current_cv_path?(a(),r("div",Qe,[o[17]||(o[17]=e("svg",{class:"w-8 h-8 text-red-600 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)),e("div",Ze,[e("p",et,"CV_"+i(p.user.full_name)+".pdf",1),e("p",tt," Caricato il "+i(K(p.user.profile.cv_last_updated)),1),p.user.profile.cv_analysis_data?(a(),r("div",st,o[14]||(o[14]=[e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}," ✨ Analisi AI completata ",-1)]))):d("",!0)]),e("div",at,[e("button",{onClick:Y,class:"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300",title:"Scarica CV"},o[15]||(o[15]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)])),p.canEdit?(a(),r("button",{key:0,onClick:V,class:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Elimina CV"},o[16]||(o[16]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z","clip-rule":"evenodd"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 112 0v4a1 1 0 11-2 0V7z","clip-rule":"evenodd"})],-1)]))):d("",!0)])])):d("",!0),$.value||w.value?(a(),r("div",rt,[e("div",lt,[e("div",ot,[w.value?(a(),r("svg",nt,o[18]||(o[18]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):d("",!0),e("span",null,i(w.value?"Analisi AI in corso...":"Caricamento in corso..."),1)]),e("span",null,i(w.value?Z.value:m.value)+"%",1)]),e("div",it,[e("div",{class:O(["h-2 rounded-full transition-all duration-300",w.value?"bg-purple-600":"bg-blue-600"]),style:ye({width:(w.value?Z.value:m.value)+"%"})},null,6)])])):d("",!0)]),z.value?(a(),r("div",dt,[e("div",ut,[o[20]||(o[20]=fe('<div class="flex items-center" data-v-8b598727><div class="w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3" data-v-8b598727><svg class="w-4 h-4 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" data-v-8b598727><path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" data-v-8b598727></path></svg></div><h3 class="text-lg font-medium text-purple-900 dark:text-purple-100" data-v-8b598727> Analisi AI del CV </h3></div>',1)),e("div",ct,[p.canEdit&&((B=z.value.skills)==null?void 0:B.length)>0?(a(),r("button",{key:0,onClick:o[2]||(o[2]=x=>f.value=!0),class:"text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center"},o[19]||(o[19]=[e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),A(" Aggiungi Competenze ")]))):d("",!0)])]),z.value.summary?(a(),r("div",gt,[o[21]||(o[21]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-2"},"Profilo Professionale",-1)),e("p",vt,i(z.value.summary),1)])):d("",!0),z.value.experience_years?(a(),r("div",pt,[e("span",mt,[o[22]||(o[22]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),A(" "+i(z.value.experience_years)+" anni di esperienza ",1)])])):d("",!0),((P=z.value.skills)==null?void 0:P.length)>0?(a(),r("div",xt,[o[24]||(o[24]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-3"},"Competenze Estratte",-1)),e("div",yt,[(a(!0),r(M,null,S(z.value.skills.slice(0,8),(x,G)=>(a(),r("div",{key:G,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},[e("div",ft,[o[23]||(o[23]=e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"},null,-1)),e("span",bt,i(x.name),1)]),e("div",ht,[x.category?(a(),r("span",kt,i(x.category),1)):d("",!0),e("span",wt,i(H(x.level)),1)])]))),128))]),z.value.skills.length>8?(a(),r("div",_t,[e("span",Ct," +"+i(z.value.skills.length-8)+" altre competenze disponibili ",1)])):d("",!0)])):d("",!0)])):d("",!0),e("input",{ref_key:"fileInput",ref:F,type:"file",accept:".pdf,.docx,.doc,.txt",onChange:ee,class:"hidden"},null,544),f.value?(a(),r("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:o[7]||(o[7]=x=>f.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:o[6]||(o[6]=oe(()=>{},["stop"]))},[e("div",zt,[e("div",Vt,[o[26]||(o[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Seleziona Competenze da Aggiungere ",-1)),e("button",{onClick:o[3]||(o[3]=x=>f.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},o[25]||(o[25]=[e("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))]),e("div",Tt,[e("div",Mt,[(a(!0),r(M,null,S(z.value.skills,(x,G)=>(a(),r("div",{key:G,class:"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"},[D(e("input",{type:"checkbox",id:`skill-${G}`,"onUpdate:modelValue":o[4]||(o[4]=de=>_.value=de),value:G,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,$t),[[Le,_.value]]),e("label",{for:`skill-${G}`,class:"ml-3 flex-1 cursor-pointer"},[e("div",St,i(x.name),1),e("div",At,[A(i(x.category)+" • Livello "+i(x.level||3)+" ",1),x.years_experience?(a(),r("span",Et," • "+i(x.years_experience)+" anni",1)):d("",!0)]),x.context?(a(),r("div",Bt,i(x.context),1)):d("",!0)],8,jt)]))),128))])]),e("div",Lt,[e("button",{onClick:o[5]||(o[5]=x=>f.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{onClick:b,disabled:_.value.length===0||I.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},i(I.value?"Aggiungendo...":`Aggiungi ${_.value.length} competenze`),9,Dt)])])])])):d("",!0)])}}},Ht=Oe(It,[["__scopeId","data-v-8b598727"]]),Pt={class:"personnel-profile"},Ot={key:0,class:"flex justify-center items-center h-64"},Nt={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},Ut={class:"flex"},Ft={class:"text-sm text-red-700 dark:text-red-300 mt-1"},qt={key:2,class:"space-y-6"},Gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Xt={class:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"},Wt={class:"flex items-center space-x-6"},Jt={class:"flex-shrink-0"},Rt={class:"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg"},Kt=["src","alt"],Yt={key:1,class:"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},Qt={class:"flex-1 text-white"},Zt={class:"text-3xl font-bold"},es={class:"text-blue-100 text-lg"},ts={class:"flex items-center space-x-4 mt-2"},ss={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},as={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},rs={class:"flex-shrink-0"},ls={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},os={class:"flex items-center justify-between mb-2"},ns={class:"text-sm text-gray-500 dark:text-gray-400"},is={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},ds={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},us={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},cs={class:"flex items-center justify-between mb-4"},gs={key:0,class:"space-y-3"},vs={class:"flex items-center"},ps={class:"text-gray-900 dark:text-white"},ms={key:0,class:"flex items-center"},xs={class:"text-gray-900 dark:text-white"},ys={key:1,class:"flex items-center"},fs={class:"text-gray-900 dark:text-white"},bs={key:1,class:"space-y-4"},hs={class:"flex space-x-2"},ks=["disabled"],ws={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},_s={key:0,class:"space-y-3"},Cs={class:"text-sm font-medium text-gray-900 dark:text-white"},zs={class:"flex items-center space-x-2"},Vs={class:"flex space-x-1"},Ts={key:0,class:"text-xs text-green-600 dark:text-green-400"},Ms={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2"},$s={key:1,class:"text-gray-500 dark:text-gray-400 text-sm"},js={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ss={class:"flex items-center justify-between mb-4"},As={key:0,class:"space-y-3"},Es={key:0,class:"flex items-center"},Bs={class:"text-sm font-medium text-gray-900 dark:text-white"},Ls={key:1,class:"flex items-center"},Ds={class:"text-sm font-medium text-gray-900 dark:text-white"},Is={key:2,class:"flex items-center"},Hs={class:"text-sm font-medium text-gray-900 dark:text-white"},Ps={key:3,class:"flex items-center"},Os={class:"text-sm font-medium text-gray-900 dark:text-white"},Ns={key:1,class:"space-y-4"},Us={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},Fs={key:0,class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},qs={class:"w-full"},Gs={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Xs={class:"border-b border-gray-200 dark:border-gray-700"},Ws={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},Js=["onClick"],Rs={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ks={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Ys={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Qs={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Zs={key:4,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},ea={key:5,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"},ta={class:"p-6"},sa={key:0,class:"space-y-6"},aa={key:0},ra={class:"flex items-center justify-between mb-4"},la={class:"text-lg font-medium text-gray-900 dark:text-white"},oa={class:"text-sm text-gray-500 dark:text-gray-400"},na={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},ia={class:"flex items-start justify-between mb-3"},da={class:"flex-1"},ua={class:"font-medium text-gray-900 dark:text-white mb-1"},ca={class:"text-sm text-gray-500 dark:text-gray-400"},ga={class:"space-y-2 text-sm"},va={key:0,class:"flex items-center text-gray-600 dark:text-gray-400"},pa={key:1,class:"flex items-center text-gray-600 dark:text-gray-400"},ma={key:1,class:"text-center py-12"},xa={key:1,class:"space-y-6"},ya={key:0},fa={class:"flex items-center justify-between mb-4"},ba={class:"text-lg font-medium text-gray-900 dark:text-white"},ha={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},ka={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},wa={class:"flex items-start justify-between mb-3"},_a={class:"flex-1"},Ca={class:"font-medium text-gray-900 dark:text-white mb-1"},za={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Va={class:"flex flex-col space-y-1 ml-2"},Ta={key:0,class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},Ma={key:1,class:"text-center py-12"},$a={key:2,class:"space-y-6"},ja={key:0},Sa={class:"flex items-center justify-between mb-4"},Aa={class:"flex items-center space-x-4"},Ea={class:"text-lg font-medium text-gray-900 dark:text-white"},Ba={class:"text-sm text-gray-500 dark:text-gray-400"},La={class:"text-sm text-gray-500 dark:text-gray-400"},Da={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Ia={class:"flex items-center justify-between mb-2"},Ha={class:"font-medium text-gray-900 dark:text-white"},Pa={key:0,class:"text-green-600 dark:text-green-400 text-sm font-medium"},Oa={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-3"},Na={class:"flex items-center justify-between"},Ua={class:"flex items-center space-x-2"},Fa={class:"flex space-x-1"},qa={class:"text-xs text-gray-500 dark:text-gray-400"},Ga={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Xa={key:0,class:"flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4"},Wa={class:"flex items-center space-x-2"},Ja=["disabled"],Ra={class:"text-sm text-gray-700 dark:text-gray-300"},Ka=["disabled"],Ya={class:"text-sm text-gray-500 dark:text-gray-400"},Qa={key:1,class:"text-center py-8"},Za={key:3,class:"space-y-6"},er={class:"flex items-center justify-between mb-4"},tr={class:"flex items-center space-x-4"},sr={class:"flex items-center space-x-2"},ar={class:"text-lg font-medium text-gray-900 dark:text-white"},rr={class:"flex items-center space-x-4"},lr={class:"text-sm text-gray-500 dark:text-gray-400"},or={key:0,class:"overflow-x-auto shadow ring-1 ring-black ring-opacity-5 rounded-lg"},nr={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},ir={class:"bg-gray-50 dark:bg-gray-700"},dr={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},ur={class:"sticky left-0 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},cr={class:"text-sm font-medium text-gray-900 dark:text-white"},gr={class:"text-xs text-gray-500 dark:text-gray-400"},vr={class:"text-xs text-gray-400 dark:text-gray-500"},pr=["onClick"],mr={key:1,class:"text-gray-300 dark:text-gray-600"},xr={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700"},yr={class:"bg-gray-100 dark:bg-gray-600 font-medium"},fr={class:"px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white bg-gray-200 dark:bg-gray-500"},br={key:1,class:"text-center py-12"},hr={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},kr={class:"mt-6"},wr={key:4},_r={class:"mt-3"},Cr={class:"flex items-center justify-between mb-4"},zr={class:"text-lg font-medium text-gray-900 dark:text-white"},Vr=["value"],Tr=["value"],Mr={class:"flex items-center justify-end space-x-3 pt-4"},$r=["disabled"],Er={__name:"PersonnelProfile",setup(p){const R=De();He(),Pe();const{hasPermission:E}=Ne(),l=c(null),C=c([]),v=c([]),h=c([]),$=c(!1),w=c(null),m=c(!1),L=c(!1),f=c("projects"),_=c(!1),I=c(!1),F=c(!1),Z=c(null),z=c([]),K=c([]),H=c(new Date().getMonth()),q=c(new Date().getFullYear()),ee=c("user"),X=c([]),j=c(1),Y=c(6),V=c({phone:"",bio:"",employee_id:"",job_title:"",employment_type:"",work_location:"",weekly_hours:40,address:"",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:""}),b=c({date:"",project_id:"",task_id:"",hours:"",description:""}),te=J(()=>{if(!l.value)return!1;try{return E.value&&typeof E.value=="function"?E.value("edit_personnel_data"):!1}catch(n){return console.warn("Permission check failed:",n),!1}}),u=J(()=>{var s;if(!((s=l.value)!=null&&s.skills))return[];const n=(j.value-1)*Y.value,t=n+Y.value;return l.value.skills.slice(n,t)}),o=J(()=>{var n;return(n=l.value)!=null&&n.skills?Math.ceil(l.value.skills.length/Y.value):0}),T=J(()=>new Date(q.value,H.value).toLocaleDateString("it-IT",{month:"long",year:"numeric"})),y=J(()=>{const n=q.value,t=H.value,s=new Date(n,t+1,0).getDate(),g=new Date,k=[];for(let Q=1;Q<=s;Q++){const ae=new Date(n,t,Q),re=ae.toISOString().split("T")[0];k.push({day:Q,date:re,isWeekend:ae.getDay()===0||ae.getDay()===6,isToday:re===g.toISOString().split("T")[0]})}return k}),N=J(()=>{var n,t;return[{id:"projects",name:"Progetti",count:C.value.length},{id:"tasks",name:"Task",count:v.value.length},{id:"skills",name:"Competenze",count:((t=(n=l.value)==null?void 0:n.skills)==null?void 0:t.length)||0},{id:"timesheet",name:"Timesheet",count:h.value.length},{id:"cv",name:"CV"}]}),B=n=>n?new Date(n).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",P=n=>({full_time:"Tempo Pieno",part_time:"Part Time",contractor:"Consulente",intern:"Stagista"})[n]||n,x=n=>{f.value=n,n==="skills"&&(j.value=1)},G=()=>{var n,t,s,g,k,Q,ae,re,pe;l.value&&(V.value={phone:l.value.phone||"",bio:l.value.bio||"",employee_id:((n=l.value.profile)==null?void 0:n.employee_id)||"",job_title:((t=l.value.profile)==null?void 0:t.job_title)||"",employment_type:((s=l.value.profile)==null?void 0:s.employment_type)||"",work_location:((g=l.value.profile)==null?void 0:g.work_location)||"",weekly_hours:((k=l.value.profile)==null?void 0:k.weekly_hours)||40,address:((Q=l.value.profile)==null?void 0:Q.address)||"",emergency_contact_name:((ae=l.value.profile)==null?void 0:ae.emergency_contact_name)||"",emergency_contact_phone:((re=l.value.profile)==null?void 0:re.emergency_contact_phone)||"",emergency_contact_relationship:((pe=l.value.profile)==null?void 0:pe.emergency_contact_relationship)||""})},de=()=>{m.value=!1,G()},be=async()=>{if(l.value){L.value=!0;try{const t=(await U.put(`/api/personnel/users/${l.value.id}`,V.value)).data;if(t.success)l.value=t.data.user,m.value=!1,console.log("Profilo aggiornato con successo");else throw new Error(t.message||"Errore durante il salvataggio")}catch(n){console.error("Errore durante il salvataggio:",n),w.value=n.message}finally{L.value=!1}}},ue=async n=>{$.value=!0,w.value=null;try{const s=(await U.get(`/api/personnel/users/${n}`)).data;if(s.success)l.value=s.data.user,G();else throw new Error(s.message||"Errore nel caricamento del profilo")}catch(t){console.error("Error fetching user profile:",t),w.value=t.message}finally{$.value=!1}},ge=async n=>{try{const s=(await U.get(`/api/tasks?assignee_id=${n}&limit=20`)).data;s.success&&(v.value=s.data.tasks||[])}catch(t){console.error("Error fetching user tasks:",t)}},ce=async n=>{try{const t=new Date,s=new Date;s.setDate(s.getDate()-30);const k=(await U.get(`/api/timesheets?user_id=${n}&start_date=${s.toISOString().split("T")[0]}&end_date=${t.toISOString().split("T")[0]}&limit=50`)).data;k.success&&(h.value=k.data||[])}catch(t){console.error("Error fetching user timesheets:",t)}},ve=()=>{b.value={date:new Date().toISOString().split("T")[0],project_id:"",task_id:"",hours:"",description:""}},ne=()=>{_.value=!1,I.value=!1,Z.value=null,ve(),K.value=[]},he=()=>{b.value.task_id="",b.value.project_id?ke(b.value.project_id):K.value=[]},ke=async n=>{try{const s=(await U.get(`/api/tasks?project_id=${n}`)).data;s.success&&(K.value=s.data.tasks||[])}catch(t){console.error("Error loading tasks:",t),K.value=[]}},we=async()=>{F.value=!0;try{const n={user_id:l.value.id,date:b.value.date,project_id:parseInt(b.value.project_id),task_id:b.value.task_id?parseInt(b.value.task_id):null,hours:parseFloat(b.value.hours),description:b.value.description},t=I.value?`/api/timesheets/${Z.value.id}`:"/api/timesheets",s=I.value?"PUT":"POST",k=(I.value?await U.put(t,n):await U.post(t,n)).data;if(k.success)ne(),await ce(l.value.id),console.log("Timesheet salvato con successo");else throw new Error(k.message||"Errore durante il salvataggio")}catch(n){console.error("Errore durante il salvataggio:",n),w.value=n.message}finally{F.value=!1}},_e=async()=>{try{const t=(await U.get("/api/projects")).data;t.success&&(z.value=t.data.projects||[])}catch(n){console.error("Error loading projects:",n),z.value=[]}},Ce=()=>{H.value===0?(H.value=11,q.value--):H.value--,se()},ze=()=>{H.value===11?(H.value=0,q.value++):H.value++,se()},se=async()=>{if(l.value)try{const n=new Date(q.value,H.value,1).toISOString().split("T")[0],t=new Date(q.value,H.value+1,0).toISOString().split("T")[0],s=ee.value==="user"?l.value.id:"",k=(await U.get(`/api/timesheets?user_id=${s}&start_date=${n}&end_date=${t}`)).data;k.success&&Ve(k.data||[])}catch(n){console.error("Error loading timesheet grid:",n),X.value=[]}},Ve=n=>{const t={};n.forEach(s=>{const g=`${s.project_id}-${s.task_id||"no-task"}`;t[g]||(t[g]={taskId:s.task_id||`project-${s.project_id}`,taskName:s.task_name||"Attività Generica",projectName:s.project_name,assignees:s.user_name||"N/A",hours:{},total:0});const k=s.date;t[g].hours[k]||(t[g].hours[k]=0),t[g].hours[k]+=parseFloat(s.hours||0),t[g].total+=parseFloat(s.hours||0)}),X.value=Object.values(t).map(s=>({...s,total:s.total.toFixed(1),hours:Object.fromEntries(Object.entries(s.hours).map(([g,k])=>[g,k.toFixed(1)]))}))},Te=n=>{const t=X.value.reduce((s,g)=>s+parseFloat(g.hours[n]||0),0);return t>0?t.toFixed(1):"0"},Me=()=>X.value.reduce((t,s)=>t+parseFloat(s.total||0),0).toFixed(1),$e=(n,t,s)=>{b.value.date=t,_.value=!0},je=async n=>{n?n.id&&n.profile?l.value=n:(n.cv_path&&(l.value.profile.current_cv_path=n.cv_path),n.cv_last_updated!==void 0&&(l.value.profile.cv_last_updated=n.cv_last_updated),n.analysis&&(l.value.profile.cv_analysis_data=JSON.stringify(n.analysis)),n.profile_completion!==void 0&&(l.value.profile.profile_completion=n.profile_completion),n.cv_path===null&&(l.value.profile.current_cv_path=null,l.value.profile.cv_last_updated=null,l.value.profile.cv_analysis_data=null)):await ue(l.value.id)};return xe(async()=>{const n=R.params.id;if(!n){w.value="ID utente non specificato";return}ve(),await ue(n),l.value&&(C.value=l.value.projects||[],await ge(n),await ce(n)),await _e(),await se()}),le(()=>R.params.id,async n=>{n&&(await ue(n),l.value&&(C.value=l.value.projects||[],await ge(n),await ce(n),await se()))}),le(ee,()=>{se()}),le(f,n=>{n==="timesheet"&&se()}),(n,t)=>(a(),r(M,null,[e("div",Pt,[$.value?(a(),r("div",Ot,t[20]||(t[20]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):w.value?(a(),r("div",Nt,[e("div",Ut,[t[22]||(t[22]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[21]||(t[21]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",Ft,i(w.value),1)])])])):l.value?(a(),r("div",qt,[e("div",Gt,[e("div",Xt,[e("div",Wt,[e("div",Jt,[e("div",Rt,[l.value.profile_image?(a(),r("img",{key:0,src:l.value.profile_image,alt:l.value.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,Kt)):(a(),r("div",Yt,t[23]||(t[23]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)])))])]),e("div",Qt,[e("h1",Zt,i(l.value.full_name),1),e("p",es,i(l.value.position||"Posizione non specificata"),1),e("div",ts,[l.value.department?(a(),r("span",ss,[t[24]||(t[24]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),A(" "+i(l.value.department.name),1)])):d("",!0),e("span",as,[t[25]||(t[25]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),A(" "+i(l.value.role),1)])])]),e("div",rs,[te.value?(a(),r("button",{key:0,onClick:t[0]||(t[0]=s=>m.value=!m.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"},[t[26]||(t[26]=e("svg",{class:"w-5 h-5 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)),A(" "+i(m.value?"Annulla":"Modifica"),1)])):d("",!0)])])]),l.value.profile&&l.value.profile.profile_completion!==void 0?(a(),r("div",ls,[e("div",os,[t[27]||(t[27]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",ns,i(l.value.profile.profile_completion)+"%",1)]),e("div",is,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:ye({width:l.value.profile.profile_completion+"%"})},null,4)])])):d("",!0)]),e("div",ds,[e("div",us,[e("div",cs,[t[29]||(t[29]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni di Contatto",-1)),te.value&&!m.value?(a(),r("button",{key:0,onClick:t[1]||(t[1]=s=>m.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[28]||(t[28]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):d("",!0)]),m.value?(a(),r("div",bs,[e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),D(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>V.value.phone=s),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[W,V.value.phone]])]),e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Bio",-1)),D(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=s=>V.value.bio=s),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[W,V.value.bio]])]),e("div",hs,[e("button",{onClick:be,disabled:L.value,class:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200"},i(L.value?"Salvataggio...":"Salva"),9,ks),e("button",{onClick:de,class:"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200"}," Annulla ")])])):(a(),r("div",gs,[e("div",vs,[t[30]||(t[30]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),e("span",ps,i(l.value.email),1)]),l.value.phone?(a(),r("div",ms,[t[31]||(t[31]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),e("span",xs,i(l.value.phone),1)])):d("",!0),l.value.hire_date?(a(),r("div",ys,[t[32]||(t[32]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),e("span",fs,"Assunto il "+i(B(l.value.hire_date)),1)])):d("",!0)]))]),e("div",ws,[t[35]||(t[35]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Competenze Principali",-1)),l.value.skills&&l.value.skills.length>0?(a(),r("div",_s,[(a(!0),r(M,null,S(l.value.skills.slice(0,4),s=>(a(),r("div",{key:s.id,class:"flex items-center justify-between"},[e("span",Cs,i(s.name),1),e("div",zs,[e("div",Vs,[(a(),r(M,null,S(5,g=>e("div",{key:g,class:O(["w-2 h-2 rounded-full",g<=s.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),s.certified?(a(),r("span",Ts,"✓")):d("",!0)])]))),128)),l.value.skills.length>4?(a(),r("div",Ms," +"+i(l.value.skills.length-4)+" altre competenze ",1)):d("",!0)])):(a(),r("div",$s," Nessuna competenza registrata "))]),l.value.profile?(a(),r("div",js,[e("div",Ss,[t[37]||(t[37]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni HR",-1)),te.value&&!m.value?(a(),r("button",{key:0,onClick:t[4]||(t[4]=s=>m.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[36]||(t[36]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):d("",!0)]),m.value?(a(),r("div",Ns,[e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"ID Dipendente",-1)),D(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>V.value.employee_id=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[W,V.value.employee_id]])]),e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Titolo Lavoro",-1)),D(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>V.value.job_title=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[W,V.value.job_title]])]),e("div",null,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Contratto",-1)),D(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>V.value.employment_type=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[48]||(t[48]=[fe('<option value="">Seleziona tipo</option><option value="full_time">Tempo Pieno</option><option value="part_time">Part Time</option><option value="contractor">Consulente</option><option value="intern">Stagista</option>',5)]),512),[[ie,V.value.employment_type]])]),e("div",null,[t[50]||(t[50]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore Settimanali",-1)),D(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>V.value.weekly_hours=s),type:"number",min:"1",max:"60",step:"0.5",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[W,V.value.weekly_hours]])])])):(a(),r("div",As,[l.value.profile.employee_id?(a(),r("div",Es,[t[39]||(t[39]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)),e("div",null,[t[38]||(t[38]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"ID Dipendente",-1)),e("p",Bs,i(l.value.profile.employee_id),1)])])):d("",!0),l.value.profile.job_title?(a(),r("div",Ls,[t[41]||(t[41]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),e("div",null,[t[40]||(t[40]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Titolo",-1)),e("p",Ds,i(l.value.profile.job_title),1)])])):d("",!0),l.value.profile.employment_type?(a(),r("div",Is,[t[43]||(t[43]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),e("div",null,[t[42]||(t[42]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Tipo Contratto",-1)),e("p",Hs,i(P(l.value.profile.employment_type)),1)])])):d("",!0),l.value.profile.weekly_hours?(a(),r("div",Ps,[t[45]||(t[45]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("div",null,[t[44]||(t[44]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Ore Settimanali",-1)),e("p",Os,i(l.value.profile.weekly_hours)+"h",1)])])):d("",!0)]))])):d("",!0)]),l.value.bio||m.value?(a(),r("div",Us,[t[51]||(t[51]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),m.value?d("",!0):(a(),r("p",Fs,i(l.value.bio||"Nessuna bio disponibile"),1))])):d("",!0),e("div",qs,[e("div",Gs,[e("div",Xs,[e("nav",Ws,[(a(!0),r(M,null,S(N.value,s=>(a(),r("button",{key:s.id,onClick:g=>x(s.id),class:O([f.value===s.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[s.id==="projects"?(a(),r("svg",Rs,t[52]||(t[52]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):s.id==="tasks"?(a(),r("svg",Ks,t[53]||(t[53]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):s.id==="skills"?(a(),r("svg",Ys,t[54]||(t[54]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):s.id==="timesheet"?(a(),r("svg",Qs,t[55]||(t[55]=[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"},null,-1)]))):s.id==="cv"?(a(),r("svg",Zs,t[56]||(t[56]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"},null,-1)]))):d("",!0),A(" "+i(s.name)+" ",1),s.count!==void 0?(a(),r("span",ea,i(s.count),1)):d("",!0)],10,Js))),128))])]),e("div",ta,[f.value==="projects"?(a(),r("div",sa,[C.value.length>0?(a(),r("div",aa,[e("div",ra,[e("h3",la," Progetti Assegnati ("+i(C.value.length)+") ",1),e("div",oa,i(C.value.filter(s=>s.status==="active").length)+" attivi ",1)]),e("div",na,[(a(!0),r(M,null,S(C.value,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer"},[e("div",ia,[e("div",da,[e("h4",ua,i(s.name),1),e("p",ca,i(s.role||"Team Member"),1)]),e("span",{class:O(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2",s.status==="active"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":s.status==="completed"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":s.status==="on_hold"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},i(s.status),3)]),e("div",ga,[s.client?(a(),r("div",va,[t[57]||(t[57]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),A(" "+i(s.client),1)])):d("",!0),s.deadline?(a(),r("div",pa,[t[58]||(t[58]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),A(" "+i(B(s.deadline)),1)])):d("",!0)])]))),128))])])):(a(),r("div",ma,t[59]||(t[59]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1)])))])):d("",!0),f.value==="tasks"?(a(),r("div",xa,[v.value.length>0?(a(),r("div",ya,[e("div",fa,[e("h3",ba," Task Assegnati ("+i(v.value.length)+") ",1),e("div",ha,[e("span",null,i(v.value.filter(s=>s.status==="in-progress").length)+" in corso",1),e("span",null,i(v.value.filter(s=>s.status==="done").length)+" completati",1)])]),e("div",ka,[(a(!0),r(M,null,S(v.value,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",wa,[e("div",_a,[e("h4",Ca,i(s.name),1),s.project_name?(a(),r("p",za,i(s.project_name),1)):d("",!0)]),e("div",Va,[e("span",{class:O(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",s.priority==="urgent"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":s.priority==="high"?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":s.priority==="medium"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"])},i(s.priority),3),e("span",{class:O(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",s.status==="done"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":s.status==="in-progress"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":s.status==="review"?"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},i(s.status),3)])]),s.due_date?(a(),r("div",Ta,[t[60]||(t[60]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),A(" Scadenza: "+i(B(s.due_date)),1)])):d("",!0)]))),128))])])):(a(),r("div",Ma,t[61]||(t[61]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono stati assegnati task a questo utente.",-1)])))])):d("",!0),f.value==="skills"?(a(),r("div",$a,[l.value.skills&&l.value.skills.length>0?(a(),r("div",ja,[e("div",Sa,[e("div",Aa,[e("h3",Ea," Competenze ("+i(l.value.skills.length)+") ",1),e("span",Ba," Pagina "+i(j.value)+" di "+i(o.value),1)]),e("div",La,i(l.value.skills.filter(s=>s.certified).length)+" certificate ",1)]),e("div",Da,[(a(!0),r(M,null,S(u.value,s=>(a(),r("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",Ia,[e("h4",Ha,i(s.name),1),s.certified?(a(),r("span",Pa,"✓ Certificato")):d("",!0)]),s.category?(a(),r("p",Oa,i(s.category),1)):d("",!0),e("div",Na,[e("div",Ua,[t[62]||(t[62]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",Fa,[(a(),r(M,null,S(5,g=>e("div",{key:g,class:O(["w-3 h-3 rounded-full",g<=s.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),e("span",qa,"("+i(s.proficiency_level)+"/5)",1)]),s.years_experience?(a(),r("span",Ga,i(s.years_experience)+i(s.years_experience===1?" anno":" anni"),1)):d("",!0)])]))),128))]),o.value>1?(a(),r("div",Xa,[e("div",Wa,[e("button",{onClick:t[9]||(t[9]=s=>j.value=Math.max(1,j.value-1)),disabled:j.value===1,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Precedente ",8,Ja),e("span",Ra," Pagina "+i(j.value)+" di "+i(o.value),1),e("button",{onClick:t[10]||(t[10]=s=>j.value=Math.min(o.value,j.value+1)),disabled:j.value===o.value,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Successiva ",8,Ka)]),e("div",Ya," Mostrando "+i((j.value-1)*Y.value+1)+"-"+i(Math.min(j.value*Y.value,l.value.skills.length))+" di "+i(l.value.skills.length)+" competenze ",1)])):d("",!0)])):(a(),r("div",Qa,t[63]||(t[63]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono state registrate competenze per questo utente.",-1)])))])):d("",!0),f.value==="timesheet"?(a(),r("div",Za,[e("div",er,[e("div",tr,[t[66]||(t[66]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Timesheet Dettaglio",-1)),e("div",sr,[e("button",{onClick:Ce,class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[64]||(t[64]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)])),e("span",ar,i(T.value),1),e("button",{onClick:ze,class:"p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[65]||(t[65]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]))])]),e("div",rr,[e("div",lr,[t[68]||(t[68]=A(" Vista: ")),D(e("select",{"onUpdate:modelValue":t[11]||(t[11]=s=>ee.value=s),class:"ml-1 bg-transparent border-none text-blue-600 dark:text-blue-400 font-medium"},t[67]||(t[67]=[e("option",{value:"all"},"Tutti i membri",-1),e("option",{value:"user"},"Solo questo utente",-1)]),512),[[ie,ee.value]])]),e("button",{onClick:t[12]||(t[12]=s=>_.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"},t[69]||(t[69]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),A(" Aggiungi Ore ")]))])]),X.value.length>0?(a(),r("div",or,[e("table",nr,[e("thead",ir,[e("tr",null,[t[70]||(t[70]=e("th",{class:"sticky left-0 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," TASK ",-1)),(a(!0),r(M,null,S(y.value,s=>(a(),r("th",{key:s.date,class:O(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",s.isWeekend?"bg-gray-100 dark:bg-gray-600":"",s.isToday?"bg-blue-50 dark:bg-blue-900 text-blue-600 dark:text-blue-300":""])},i(s.day),3))),128)),t[71]||(t[71]=e("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-100 dark:bg-gray-600"}," TOTALE ",-1))])]),e("tbody",dr,[(a(!0),r(M,null,S(X.value,s=>(a(),r("tr",{key:s.taskId,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[e("td",ur,[e("div",cr,i(s.taskName),1),e("div",gr,i(s.projectName),1),e("div",vr,i(s.assignees),1)]),(a(!0),r(M,null,S(y.value,g=>(a(),r("td",{key:g.date,class:O(["px-2 py-3 text-center text-sm",g.isWeekend?"bg-gray-50 dark:bg-gray-700":"",g.isToday?"bg-blue-50 dark:bg-blue-900":""])},[s.hours[g.date]?(a(),r("div",{key:0,class:"text-blue-600 dark:text-blue-400 font-medium cursor-pointer hover:text-blue-800 dark:hover:text-blue-300",onClick:k=>$e(s.taskId,g.date,s.hours[g.date])},i(s.hours[g.date]),9,pr)):(a(),r("div",mr,"-"))],2))),128)),e("td",xr,i(s.total),1)]))),128)),e("tr",yr,[t[72]||(t[72]=e("td",{class:"sticky left-0 bg-gray-100 dark:bg-gray-600 px-4 py-3 text-sm font-bold text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-500"}," TOTALE GIORNALIERO ",-1)),(a(!0),r(M,null,S(y.value,s=>(a(),r("td",{key:s.date,class:O(["px-2 py-3 text-center text-sm font-bold text-gray-900 dark:text-white",s.isWeekend?"bg-gray-200 dark:bg-gray-500":""])},i(Te(s.date)),3))),128)),e("td",fr,i(Me()),1)])])])])):(a(),r("div",br,[t[74]||(t[74]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})],-1)),t[75]||(t[75]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun timesheet",-1)),e("p",hr,"Non sono stati registrati timesheet per "+i(T.value)+".",1),e("div",kr,[e("button",{onClick:t[13]||(t[13]=s=>_.value=!0),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center mx-auto"},t[73]||(t[73]=[e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),A(" Aggiungi Prima Registrazione ")]))])]))])):d("",!0),f.value==="cv"?(a(),r("div",wr,[Ie(Ht,{user:l.value,"can-edit":te.value,onUserUpdated:je},null,8,["user","can-edit"])])):d("",!0)])])])])):d("",!0)]),_.value||I.value?(a(),r("div",{key:0,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:ne},[e("div",{class:"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[19]||(t[19]=oe(()=>{},["stop"]))},[e("div",_r,[e("div",Cr,[e("h3",zr,i(I.value?"Modifica Timesheet":"Aggiungi Timesheet"),1),e("button",{onClick:ne,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[76]||(t[76]=[e("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))]),e("form",{onSubmit:oe(we,["prevent"]),class:"space-y-4"},[e("div",null,[t[77]||(t[77]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),D(e("input",{"onUpdate:modelValue":t[14]||(t[14]=s=>b.value.date=s),type:"date",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[W,b.value.date]])]),e("div",null,[t[79]||(t[79]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),D(e("select",{"onUpdate:modelValue":t[15]||(t[15]=s=>b.value.project_id=s),required:"",onChange:he,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[78]||(t[78]=e("option",{value:""},"Seleziona progetto",-1)),(a(!0),r(M,null,S(z.value,s=>(a(),r("option",{key:s.id,value:s.id},i(s.name),9,Vr))),128))],544),[[ie,b.value.project_id]])]),e("div",null,[t[81]||(t[81]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task (opzionale)",-1)),D(e("select",{"onUpdate:modelValue":t[16]||(t[16]=s=>b.value.task_id=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[80]||(t[80]=e("option",{value:""},"Nessun task specifico",-1)),(a(!0),r(M,null,S(K.value,s=>(a(),r("option",{key:s.id,value:s.id},i(s.name),9,Tr))),128))],512),[[ie,b.value.task_id]])]),e("div",null,[t[82]||(t[82]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),D(e("input",{"onUpdate:modelValue":t[17]||(t[17]=s=>b.value.hours=s),type:"number",step:"0.25",min:"0.25",max:"24",required:"",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[W,b.value.hours]])]),e("div",null,[t[83]||(t[83]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),D(e("textarea",{"onUpdate:modelValue":t[18]||(t[18]=s=>b.value.description=s),rows:"3",placeholder:"Descrivi il lavoro svolto...",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[W,b.value.description]])]),e("div",Mr,[e("button",{type:"button",onClick:ne,class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{type:"submit",disabled:F.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},i(F.value?"Salvando...":I.value?"Aggiorna":"Salva"),9,$r)])],32)])])])):d("",!0)],64))}};export{Er as default};
