import{e as S,r as u,f as h}from"./vendor.js";import{c as m}from"./app.js";const D=S("personnel",()=>{const c=u([]),v=u([]),f=u([]),p=u(null),l=u(!1),n=u(null),o=u({search:"",department:null,skill:null,role:null,location:null,sort:"name"}),i=u({page:1,per_page:20,total:0,pages:0}),w=h(()=>{let a=c.value;if(o.value.search){const r=o.value.search.toLowerCase();a=a.filter(e=>{var t,s,d;return((t=e.full_name)==null?void 0:t.toLowerCase().includes(r))||((s=e.email)==null?void 0:s.toLowerCase().includes(r))||((d=e.position)==null?void 0:d.toLowerCase().includes(r))})}return o.value.department&&(a=a.filter(r=>r.department_id===o.value.department)),o.value.skill&&(a=a.filter(r=>{var e;return(e=r.skills)==null?void 0:e.some(t=>t.id===o.value.skill)})),o.value.role&&(a=a.filter(r=>r.role===o.value.role)),a}),E=h(()=>{const a=(r=null)=>v.value.filter(e=>e.parent_id===r).map(e=>({...e,children:a(e.id)}));return a()}),y=async(a={})=>{l.value=!0,n.value=null;try{const r=new URLSearchParams({page:a.page||i.value.page,per_page:a.per_page||i.value.per_page,...a}),e=await fetch(`/api/personnel/users?${r}`,{credentials:"include"});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const t=await e.json();if(t.success){c.value=t.data.users||[];const s=t.data.pagination||{};i.value={page:s.page||1,per_page:s.per_page||20,total:s.total||0,pages:s.pages||0}}else throw new Error(t.message||"Errore nel caricamento utenti")}catch(r){n.value=r.message,console.error("Errore fetchUsers:",r)}finally{l.value=!1}},k=async a=>{var r,e;l.value=!0,n.value=null;try{const t=await m.get(`/api/personnel/users/${a}`);if(t.data.success)return p.value=t.data.data.user,t.data.data.user;throw new Error(t.data.message||"Errore nel caricamento utente")}catch(t){throw n.value=((e=(r=t.response)==null?void 0:r.data)==null?void 0:e.message)||t.message||"Errore nel caricamento utente",console.error("Errore fetchUser:",t),t}finally{l.value=!1}},T=async(a,r)=>{var e;l.value=!0,n.value=null;try{const t=await fetch(`/api/personnel/users/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(r)});if(!t.ok)throw new Error(`HTTP ${t.status}: ${t.statusText}`);const s=await t.json();if(s.success){const d=c.value.findIndex(P=>P.id===a);return d!==-1&&(c.value[d]={...c.value[d],...s.data.user}),((e=p.value)==null?void 0:e.id)===a&&(p.value={...p.value,...s.data.user}),s.data.user}else throw new Error(s.message||"Errore nell'aggiornamento utente")}catch(t){throw n.value=t.message,console.error("Errore updateUser:",t),t}finally{l.value=!1}},U=async()=>{var a,r;l.value=!0,n.value=null;try{const e=await m.get("/api/personnel/departments");if(e.data.success)v.value=e.data.data.departments||[];else throw new Error(e.data.message||"Errore nel caricamento dipartimenti")}catch(e){n.value=((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||e.message||"Errore nel caricamento dipartimenti",console.error("Errore fetchDepartments:",e)}finally{l.value=!1}},_=async()=>{var a,r;l.value=!0,n.value=null;try{const e=await m.get("/api/personnel/skills");if(e.data.success)f.value=e.data.data.skills||[];else throw new Error(e.data.message||"Errore nel caricamento competenze")}catch(e){n.value=((r=(a=e.response)==null?void 0:a.data)==null?void 0:r.message)||e.message||"Errore nel caricamento competenze",console.error("Errore fetchSkills:",e)}finally{l.value=!1}},$=(a,r)=>{o.value[a]=r},g=()=>{o.value={search:"",department:null,skill:null,role:null,location:null,sort:"name"}};return{users:c,departments:v,skills:f,currentUser:p,loading:l,error:n,filters:o,pagination:i,filteredUsers:w,departmentTree:E,fetchUsers:y,fetchUser:k,updateUser:T,fetchDepartments:U,fetchSkills:_,setFilter:$,clearFilters:g,setPagination:(a,r=null)=>{i.value.page=a,r&&(i.value.per_page=r)},$reset:()=>{c.value=[],v.value=[],f.value=[],p.value=null,l.value=!1,n.value=null,g(),i.value={page:1,per_page:20,total:0,pages:0}}}});export{D as u};
