#!/usr/bin/env python3
"""
Seed specifico per dati CRM - Clients, Proposals, Contracts
Collega contratti a progetti esistenti per testing
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from extensions import db
from models import Client, Contact, Proposal, Contract, Project, User
from datetime import datetime, date, timedelta
import random

def seed_crm_clients():
    """Crea clienti CRM con diversi stati"""
    print("🏢 Creazione clienti CRM...")
    
    clients_data = [
        {
            'name': 'TechCorp Solutions',
            'industry': 'Technology',
            'description': 'Azienda leader nel settore tecnologico',
            'website': 'https://techcorp.com',
            'address': 'Via Milano 123, Milano',
            'status': 'client'
        },
        {
            'name': 'StartupInnovation',
            'industry': 'Startup',
            'description': 'Startup innovativa nel fintech',
            'website': 'https://startupinnovation.com',
            'address': 'Via Roma 456, Roma',
            'status': 'prospect'
        },
        {
            'name': 'MegaCorp Industries',
            'industry': 'Manufacturing',
            'description': 'Grande azienda manifatturiera',
            'website': 'https://megacorp.com',
            'address': 'Via Torino 789, Torino',
            'status': 'lead'
        },
        {
            'name': 'DigitalAgency Pro',
            'industry': 'Marketing',
            'description': 'Agenzia di marketing digitale',
            'website': 'https://digitalagency.com',
            'address': 'Via Napoli 321, Napoli',
            'status': 'client'
        },
        {
            'name': 'HealthTech Solutions',
            'industry': 'Healthcare',
            'description': 'Soluzioni tecnologiche per la sanità',
            'website': 'https://healthtech.com',
            'address': 'Via Bologna 654, Bologna',
            'status': 'prospect'
        }
    ]
    
    created_clients = []
    for client_data in clients_data:
        # Verifica se esiste già
        existing = Client.query.filter_by(name=client_data['name']).first()
        if not existing:
            client = Client(**client_data)
            db.session.add(client)
            created_clients.append(client)
    
    db.session.commit()
    print(f"✅ Creati {len(created_clients)} nuovi clienti")
    
    # Ritorna tutti i clienti (esistenti + nuovi)
    return Client.query.all()

def seed_crm_contacts(clients):
    """Crea contatti per i clienti"""
    print("👥 Creazione contatti...")
    
    contacts_data = [
        {'first_name': 'Marco', 'last_name': 'Rossi', 'email': '<EMAIL>', 'phone': '+39 02 1234567', 'position': 'CTO'},
        {'first_name': 'Laura', 'last_name': 'Bianchi', 'email': '<EMAIL>', 'phone': '+39 06 2345678', 'position': 'CEO'},
        {'first_name': 'Giuseppe', 'last_name': 'Verdi', 'email': '<EMAIL>', 'phone': '+39 011 3456789', 'position': 'IT Director'},
        {'first_name': 'Anna', 'last_name': 'Neri', 'email': '<EMAIL>', 'phone': '+39 081 4567890', 'position': 'Marketing Manager'},
        {'first_name': 'Francesco', 'last_name': 'Blu', 'email': '<EMAIL>', 'phone': '+39 051 5678901', 'position': 'Product Manager'}
    ]
    
    created_contacts = []
    for i, contact_data in enumerate(contacts_data):
        if i < len(clients):
            # Verifica se esiste già
            existing = Contact.query.filter_by(email=contact_data['email']).first()
            if not existing:
                contact = Contact(
                    client_id=clients[i].id,
                    **contact_data
                )
                db.session.add(contact)
                created_contacts.append(contact)
    
    db.session.commit()
    print(f"✅ Creati {len(created_contacts)} nuovi contatti")
    return created_contacts

def seed_crm_proposals(clients, users):
    """Crea proposte per i clienti"""
    print("📄 Creazione proposte...")
    
    proposals_data = [
        {
            'title': 'Sviluppo Piattaforma E-commerce',
            'description': 'Sviluppo completo piattaforma e-commerce con React e Node.js',
            'value': 45000.00,
            'status': 'accepted',
            'expiry_date': date.today() + timedelta(days=30)
        },
        {
            'title': 'App Mobile Fintech',
            'description': 'Sviluppo app mobile per servizi finanziari',
            'value': 35000.00,
            'status': 'negotiating',
            'expiry_date': date.today() + timedelta(days=15)
        },
        {
            'title': 'Sistema Gestionale ERP',
            'description': 'Implementazione sistema ERP personalizzato',
            'value': 75000.00,
            'status': 'sent',
            'expiry_date': date.today() + timedelta(days=45)
        },
        {
            'title': 'Campagna Marketing Digitale',
            'description': 'Strategia e implementazione campagna marketing',
            'value': 15000.00,
            'status': 'accepted',
            'expiry_date': date.today() + timedelta(days=60)
        },
        {
            'title': 'Piattaforma Telemedicina',
            'description': 'Sviluppo piattaforma per consultazioni mediche online',
            'value': 55000.00,
            'status': 'draft',
            'expiry_date': date.today() + timedelta(days=90)
        }
    ]
    
    created_proposals = []
    for i, proposal_data in enumerate(proposals_data):
        if i < len(clients):
            proposal = Proposal(
                client_id=clients[i].id,
                created_by=random.choice(users).id,
                **proposal_data
            )
            
            # Set sent_date per proposte inviate
            if proposal.status in ['sent', 'negotiating', 'accepted']:
                proposal.sent_date = datetime.now() - timedelta(days=random.randint(1, 30))
            
            db.session.add(proposal)
            created_proposals.append(proposal)
    
    db.session.commit()
    print(f"✅ Creati {len(created_proposals)} nuove proposte")
    return created_proposals

def seed_crm_contracts(clients):
    """Crea contratti per clienti"""
    print("📋 Creazione contratti...")
    
    contracts_data = [
        {
            'contract_number': 'CONTR-2024-001',
            'title': 'Contratto Sviluppo E-commerce TechCorp',
            'description': 'Contratto per sviluppo piattaforma e-commerce',
            'contract_type': 'time_and_materials',
            'hourly_rate': 85.00,
            'budget_hours': 500.0,
            'budget_amount': 42500.00,
            'start_date': date(2024, 1, 15),
            'end_date': date(2024, 6, 15),
            'status': 'active'
        },
        {
            'contract_number': 'CONTR-2024-002',
            'title': 'Contratto Marketing DigitalAgency',
            'description': 'Contratto per servizi di marketing digitale',
            'contract_type': 'retainer',
            'hourly_rate': 75.00,
            'budget_hours': 200.0,
            'budget_amount': 15000.00,
            'start_date': date(2024, 2, 1),
            'end_date': date(2024, 7, 31),
            'status': 'active'
        },
        {
            'contract_number': 'CONTR-2024-003',
            'title': 'Contratto Consulenza HealthTech',
            'description': 'Contratto per consulenza tecnologica',
            'contract_type': 'fixed_price',
            'hourly_rate': 95.00,
            'budget_hours': 300.0,
            'budget_amount': 28500.00,
            'start_date': date(2024, 3, 1),
            'end_date': date(2024, 8, 31),
            'status': 'signed'
        }
    ]
    
    created_contracts = []
    # Prendi solo i primi 3 clienti per i contratti
    contract_clients = [c for c in clients if c.status == 'client'][:3]
    
    for i, contract_data in enumerate(contracts_data):
        if i < len(contract_clients):
            contract = Contract(
                client_id=contract_clients[i].id,
                **contract_data
            )
            db.session.add(contract)
            created_contracts.append(contract)
    
    db.session.commit()
    print(f"✅ Creati {len(created_contracts)} nuovi contratti")
    return created_contracts

def link_projects_to_contracts(contracts):
    """Collega progetti esistenti ai contratti"""
    print("🔗 Collegamento progetti a contratti...")
    
    # Prendi progetti esistenti senza contratto
    projects = Project.query.filter_by(contract_id=None).limit(3).all()
    
    linked_count = 0
    for i, project in enumerate(projects):
        if i < len(contracts):
            project.contract_id = contracts[i].id
            linked_count += 1
    
    db.session.commit()
    print(f"✅ Collegati {linked_count} progetti a contratti")

def main():
    """Esegue il seed CRM completo"""
    app = create_app()
    
    with app.app_context():
        print("🚀 Avvio seed CRM...")
        
        # Ottieni utenti esistenti
        users = User.query.all()
        if not users:
            print("❌ Nessun utente trovato! Esegui prima il seed principale.")
            return
        
        # Seed CRM
        clients = seed_crm_clients()
        contacts = seed_crm_contacts(clients)
        proposals = seed_crm_proposals(clients, users)
        contracts = seed_crm_contracts(clients)
        
        # Collega progetti a contratti
        link_projects_to_contracts(contracts)
        
        print("\n🎉 Seed CRM completato con successo!")
        print(f"   - {len(clients)} clienti totali")
        print(f"   - {len(contacts)} contatti")
        print(f"   - {len(proposals)} proposte")
        print(f"   - {len(contracts)} contratti")
        print("   - Progetti collegati a contratti")

if __name__ == '__main__':
    main()
