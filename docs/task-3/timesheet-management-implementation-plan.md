# Timesheet Management System - Implementation Plan

## Executive Summary

This document outlines the comprehensive implementation plan for extending the existing timesheet management system to support time-off requests, enhanced approval workflows, and future CRM pipeline integration. The plan maintains backward compatibility with existing functionality while adding robust features for absence management, financial tracking, and team oversight.

## Key Architectural Decisions

### 1. **No Auto-Generation of Timesheet Entries**
- Time-off requests remain separate from timesheet entries
- Users must manually create timesheet entries for approved time-off
- Rationale: Cannot automatically determine which specific task to assign hours to

### 2. **Single Entry Approval Workflow**
- Each timesheet entry is approved individually
- No batch approval by day to maintain granular control
- Allows for partial approval of daily work

### 3. **Internal Notification System**
- Notifications stored in database table
- Display in navbar when user loads page
- No email notifications to reduce complexity

### 4. **Project-Level Permissions**
- Access controls managed per project rather than globally
- Managers see financial data only for their assigned projects
- More granular security model

### 5. **Responsive Web Only**
- No separate mobile app development
- Focus on responsive design for mobile browsers
- Faster development and maintenance

## Database Model Extensions

### 2.1 Timesheet Model Extensions

```python
class Timesheet(db.Model):
    # Existing fields
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=True)  # NULL for time-off
    task_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=True)
    date = db.Column(db.Date, nullable=False)
    hours = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # NEW FIELDS
    entry_type = db.Column(db.String(20), default='work')  # work, vacation, leave, smartworking
    applied_rate = db.Column(db.Float, nullable=True)  # Rate applied for billing
    is_billable = db.Column(db.Boolean, default=True)  # Billable to client
    is_billed = db.Column(db.Boolean, default=False)  # Already invoiced
    submission_date = db.Column(db.DateTime, nullable=True)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_timesheets')
```

### 2.2 TimeOffRequest Model (New)

```python
class TimeOffRequest(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    request_type = db.Column(db.String(20), nullable=False)  # vacation, leave, smartworking
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    hours_per_day = db.Column(db.Float, default=8.0)  # Expected hours per day
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    notes = db.Column(db.Text)
    submission_date = db.Column(db.DateTime, default=datetime.utcnow)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='time_off_requests')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_time_off_requests')
    
    @property
    def duration_days(self):
        return (self.end_date - self.start_date).days + 1
    
    @property
    def total_hours(self):
        return self.duration_days * self.hours_per_day
```

### 2.3 ProjectResource Model Extensions

```python
class ProjectResource(db.Model):
    # Existing fields
    id = db.Column(db.Integer, primary_key=True)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    allocation_percentage = db.Column(db.Integer, default=100)
    role = db.Column(db.String(50))
    
    # NEW FIELDS for CRM Integration
    billable_rate = db.Column(db.Float, nullable=True)  # Project-specific billing rate
    valid_from = db.Column(db.Date, nullable=True)  # Rate validity period
    valid_to = db.Column(db.Date, nullable=True)
    is_billable = db.Column(db.Boolean, default=True)  # Resource billable on this project
    can_approve_timesheets = db.Column(db.Boolean, default=False)  # Project-level approval rights
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
```

### 2.4 Notification Model (New)

```python
class Notification(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # timesheet_pending, timeoff_request, approval_result
    title = db.Column(db.String(255), nullable=False)
    message = db.Column(db.Text)
    data = db.Column(db.JSON)  # Additional data (IDs, links, etc.)
    is_read = db.Column(db.Boolean, default=False)
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    expires_at = db.Column(db.DateTime, nullable=True)  # Auto-cleanup old notifications
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='notifications')
    
    @property
    def is_expired(self):
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
```

## API Endpoints Specification

### 3.1 Extended Timesheet API

**Base URL:** `/api/timesheets/`

#### Existing Endpoints (Maintain Compatibility)
- `GET /` - List timesheets with filters
- `POST /` - Create new timesheet entry
- `GET /project/{id}/monthly` - Monthly project timesheet view

#### New Endpoints
- `PUT /{id}` - Update timesheet entry
- `DELETE /{id}` - Delete timesheet entry (draft only)
- `POST /{id}/submit` - Submit entry for approval
- `POST /{id}/approve` - Approve entry (managers/admin)
- `POST /{id}/reject` - Reject entry with reason
- `GET /pending` - Get pending approvals for current user's projects
- `GET /team/{project_id}` - Get team timesheet for project (managers)
- `GET /user/{user_id}/summary` - Get user timesheet summary
- `POST /bulk-approve` - Approve multiple entries

### 3.2 Time-Off Request API (New)

**Base URL:** `/api/time-off/`

- `GET /` - List time-off requests with filters
- `POST /` - Create new time-off request
- `PUT /{id}` - Update request (pending only)
- `DELETE /{id}` - Delete request (pending only)
- `POST /{id}/approve` - Approve request
- `POST /{id}/reject` - Reject request with reason
- `GET /pending` - Get pending requests for approval
- `GET /calendar/{year}/{month}` - Calendar view of approved requests
- `GET /user/{user_id}` - Get user's time-off requests
- `GET /team/{project_id}` - Get team time-off for project
- `GET /conflicts` - Check for scheduling conflicts

### 3.3 Notification API (New)

**Base URL:** `/api/notifications/`

- `GET /` - Get user notifications
- `PUT /{id}/read` - Mark notification as read
- `PUT /mark-all-read` - Mark all notifications as read
- `DELETE /{id}` - Delete notification
- `GET /unread-count` - Get count of unread notifications

## Frontend Component Architecture

### 4.1 Component Hierarchy

```
TimesheetManagement/
├── ProjectTimesheet.vue (Extended)
├── PersonnelProfile.vue (Extended)
├── TimesheetManagement.vue (New - Manager View)
├── components/
│   ├── TimesheetEntry/
│   │   ├── TimesheetEntryForm.vue
│   │   ├── TimesheetEntryList.vue
│   │   └── TimesheetApprovalCard.vue
│   ├── TimeOff/
│   │   ├── TimeOffRequestForm.vue
│   │   ├── TimeOffRequestList.vue
│   │   ├── TimeOffCalendar.vue
│   │   └── TimeOffApprovalCard.vue
│   ├── Notifications/
│   │   ├── NotificationBell.vue
│   │   ├── NotificationDropdown.vue
│   │   └── NotificationItem.vue
│   └── Shared/
│       ├── ApprovalModal.vue
│       ├── DateRangePicker.vue
│       └── TeamMemberSelector.vue
```

### 4.2 Key Component Modifications

#### 4.2.1 ProjectTimesheet.vue Extensions

**New Features:**
- Display team absences alongside work entries
- Filter by entry type (work/vacation/leave/smartworking)
- Visual indicators for approval status
- Cost calculations (manager/admin only)
- Absence conflict warnings

**New Props:**
```javascript
props: {
  project: Object,
  showFinancialData: Boolean,
  canApprove: Boolean
}
```

#### 4.2.2 PersonnelProfile.vue Extensions

**New Tab Structure:**
```javascript
timesheetTabs: [
  { id: 'entries', name: 'Ore Lavorate', icon: 'clock' },
  { id: 'requests', name: 'Richieste Time-Off', icon: 'calendar' },
  { id: 'calendar', name: 'Calendario Personale', icon: 'calendar-days' }
]
```

**New Features:**
- Time-off request creation
- Personal absence calendar
- Request status tracking
- Integration with timesheet entries

#### 4.2.3 TimesheetManagement.vue (New)

**Manager/HR Dashboard Features:**
- Pending approvals overview
- Team timesheet summary
- Absence calendar view
- KPI widgets
- Bulk approval actions

### 4.3 Notification System Integration

#### 4.3.1 NotificationBell.vue

**Features:**
- Real-time unread count
- Priority-based styling
- Click to open dropdown

#### 4.3.2 NotificationDropdown.vue

**Features:**
- List recent notifications
- Mark as read functionality
- Direct links to relevant items
- Auto-refresh capability

## Permission Matrix

### 5.1 Role-Based Access Control

| Feature | Employee | Manager | Admin | HR |
|---------|----------|---------|-------|-----|
| **View Own Timesheets** | ✅ | ✅ | ✅ | ✅ |
| **Edit Own Timesheets** | ✅ Draft only | ✅ | ✅ | ✅ |
| **View Team Timesheets** | ❌ | ✅ Own projects | ✅ All | ✅ All |
| **Approve Timesheets** | ❌ | ✅ Own projects | ✅ All | ❌ |
| **View Financial Data** | ❌ | ✅ Own projects | ✅ All | ❌ |
| **View Team Rates** | ❌ | ✅ Own team | ✅ All | ✅ All |
| **Create Time-Off Requests** | ✅ | ✅ | ✅ | ✅ |
| **Approve Time-Off Requests** | ❌ | ✅ Own team | ✅ All | ✅ All |
| **View Team Absences** | ❌ | ✅ Own projects | ✅ All | ✅ All |
| **Manage Project Resources** | ❌ | ✅ Own projects | ✅ All | ❌ |
| **Set Billing Rates** | ❌ | ❌ | ✅ All | ❌ |

### 5.2 Project-Level Permissions

**ProjectResource.can_approve_timesheets** determines approval rights per project:
- Allows granular control beyond role-based permissions
- Project managers can approve even if not global managers
- Temporary approval delegation possible

## Notification System Architecture

### 6.1 Notification Types

```javascript
NOTIFICATION_TYPES = {
  TIMESHEET_SUBMITTED: 'timesheet_submitted',
  TIMESHEET_APPROVED: 'timesheet_approved', 
  TIMESHEET_REJECTED: 'timesheet_rejected',
  TIMEOFF_REQUESTED: 'timeoff_requested',
  TIMEOFF_APPROVED: 'timeoff_approved',
  TIMEOFF_REJECTED: 'timeoff_rejected',
  DEADLINE_REMINDER: 'deadline_reminder',
  CONFLICT_WARNING: 'conflict_warning'
}
```

### 6.2 Notification Service

```python
class NotificationService:
    @staticmethod
    def create_notification(user_id, type, title, message, data=None):
        """Create and store notification"""
        
    @staticmethod
    def notify_timesheet_submitted(timesheet_id):
        """Notify project managers of submitted timesheet"""
        
    @staticmethod
    def notify_timeoff_request(request_id):
        """Notify managers/HR of time-off request"""
        
    @staticmethod
    def notify_approval_result(user_id, item_type, status, reason=None):
        """Notify user of approval/rejection result"""
        
    @staticmethod
    def cleanup_expired_notifications():
        """Remove expired notifications"""
```

### 6.3 Frontend Integration

**Navbar Integration:**
- Notification bell with unread count
- Dropdown with recent notifications
- Auto-refresh every 30 seconds
- Sound/visual alerts for high priority

**Page-Level Integration:**
- Contextual notifications on relevant pages
- Toast notifications for immediate feedback
- Persistent notifications for important items

## CRM Pipeline Integration Points

### 7.1 Future Integration Architecture

```
Customer → Proposal → Contract → Project → Tasks → Timesheet → Billing
```

### 7.2 Database Relationships (Future)

```python
# Future CRM Models
class Customer(db.Model):
    projects = db.relationship('Project', backref='customer')

class Contract(db.Model):
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    projects = db.relationship('Project', backref='contract')
    
# Project Model Extensions (Future)
class Project(db.Model):
    # Existing fields...
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'), nullable=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('contract.id'), nullable=True)
```

### 7.3 Billing Integration Points

**Timesheet Entry Billing:**
- `applied_rate` field stores rate used for billing
- `is_billed` flag prevents double billing
- Manual selection of entries for invoicing

**Rate Calculation Logic:**
1. ProjectResource.billable_rate (if set)
2. PersonnelRate for user and date
3. Project.client_daily_rate (fallback)

### 7.4 Financial Visibility

**Manager View:**
- Real-time project costs
- Billable vs non-billable hours
- Resource utilization rates
- Margin calculations

**Admin View:**
- All financial data
- Cross-project analytics
- Rate management
- Billing oversight

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
- [ ] Database model extensions
- [ ] Data migration scripts
- [ ] Basic API endpoints
- [ ] Unit tests for models

### Phase 2: Core Features (Weeks 3-4)
- [ ] Time-off request API
- [ ] Extended timesheet API
- [ ] Basic notification system
- [ ] API integration tests

### Phase 3: Frontend Core (Weeks 5-6)
- [ ] Time-off request components
- [ ] Extended project timesheet view
- [ ] Personnel profile extensions
- [ ] Basic notification UI

### Phase 4: Management Features (Weeks 7-8)
- [ ] Manager dashboard
- [ ] Approval workflows
- [ ] Team overview components
- [ ] Advanced notifications

### Phase 5: Advanced Features (Weeks 9-10)
- [ ] Calendar integrations
- [ ] Financial data display
- [ ] Bulk operations
- [ ] Performance optimizations

### Phase 6: Testing & Polish (Weeks 11-12)
- [ ] Comprehensive testing
- [ ] Performance testing
- [ ] Security audit
- [ ] Documentation completion

## Technical Considerations

### 8.1 Performance Optimization

**Database Indexing:**
- Composite indexes on (user_id, date) for timesheets
- Index on (project_id, status) for approvals
- Index on (user_id, is_read) for notifications

**Caching Strategy:**
- Cache user permissions per project
- Cache notification counts
- Cache team member lists

### 8.2 Security Measures

**Data Protection:**
- Financial data filtered at API level
- Project-based access control
- Audit logging for sensitive operations

**Input Validation:**
- Date range validation
- Hour limits per day/week
- Overlap detection for time-off

### 8.3 Scalability Considerations

**Database Design:**
- Partitioning for large timesheet tables
- Archiving old data
- Efficient query patterns

**Frontend Performance:**
- Lazy loading for large datasets
- Virtual scrolling for long lists
- Optimistic updates for better UX

## Success Metrics

### 9.1 User Adoption
- Time-off request usage rate
- Timesheet submission compliance
- Manager approval response time

### 9.2 System Performance
- API response times < 200ms
- Page load times < 2 seconds
- 99.9% uptime target

### 9.3 Business Impact
- Reduced administrative overhead
- Improved project cost visibility
- Better resource utilization tracking

## Conclusion

This implementation plan provides a comprehensive roadmap for extending the timesheet management system while maintaining compatibility with existing functionality. The phased approach ensures manageable development cycles and allows for iterative feedback and improvements.

The system is designed to scale with future CRM integration requirements while providing immediate value through improved absence management and enhanced approval workflows.
