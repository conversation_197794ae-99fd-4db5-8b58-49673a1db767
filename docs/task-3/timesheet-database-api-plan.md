# Piano Dettagliato: Subtask 3.1 - Database & API Timesheet System

**Task Parent:** Timesheet Management System (ID: 3)  
**Subtask:** 3.1 - Database & API  
**Descrizione:** Estensione modelli, migrazioni, implementazione e test degli endpoint core (timesheet, time-off, notification)  
**Priorità:** ALTA  
**Durata Stimata:** 2-3 settimane  

---

## FASE 1: ANALISI E PREPARAZIONE 🔍

### 1.1 Audit Modelli Esistenti

#### ✅ MODELLI GIÀ CONFORMI:
- **Client** - <PERSON>mpleto, nessuna modifica richiesta
- **Proposal** - Adeguato, aggiungere relationship con Contract  
- **Project** - Ha campi billing, aggiungere relationship con Contract
- **Notification** - Sistema base esistente

#### ❌ MODELLI DA CREARE/RISTRUTTURARE:
- **Timesheet** - Ristrutturazione completa necessaria (da entry-based a submission-based)
- **Contract** - <PERSON> creare ex-novo
- **TimesheetEntry** - Da creare ex-novo  
- **TimeOffRequest** - Da creare ex-novo

### 1.2 Backup Dati Critici

```sql
-- Backup tabella timesheet esistente
CREATE TABLE timesheet_backup AS SELECT * FROM timesheet;

-- Verifica backup
SELECT COUNT(*) FROM timesheet_backup;
```

### 1.3 Analisi Dipendenze

- **Frontend**: Identificare componenti Vue che usano timesheet attuale
- **API**: Mappare endpoint che saranno impattati dalla ristrutturazione
- **Reports**: Verificare query che dipendono dalla struttura attuale
- **Integrations**: Controllare collegamenti con altri moduli

---

## FASE 2: CREAZIONE NUOVI MODELLI 🏗️

### 2.1 Contract Model (PRIORITÀ 1)

```python
class Contract(db.Model):
    __tablename__ = 'contracts'
    
    id = db.Column(db.Integer, primary_key=True)
    proposal_id = db.Column(db.Integer, db.ForeignKey('proposal.id'), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=False)
    contract_number = db.Column(db.String(50), unique=True, nullable=False)
    title = db.Column(db.String(128), nullable=False)
    
    # Date e validità
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=True)
    signed_date = db.Column(db.Date, nullable=True)
    
    # Termini finanziari
    hourly_rate = db.Column(db.Float, nullable=False)
    daily_rate = db.Column(db.Float, nullable=True)
    currency = db.Column(db.String(3), default='EUR')
    payment_terms = db.Column(db.String(100))  # "NET 30", "NET 15", etc.
    
    # Status e metadati
    status = db.Column(db.String(20), default='draft')  # draft, active, completed, terminated, expired
    value = db.Column(db.Float)  # Valore totale contratto
    notes = db.Column(db.Text)
    
    # Audit fields
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    proposal = db.relationship('Proposal', backref='contract')
    client = db.relationship('Client', backref='contracts')
    creator = db.relationship('User', backref='created_contracts')
    projects = db.relationship('Project', backref='contract')
    timesheet_entries = db.relationship('TimesheetEntry', backref='contract')
    
    def __repr__(self):
        return f'<Contract {self.contract_number}>'
    
    @property
    def is_active(self):
        """Verifica se il contratto è attivo"""
        return self.status == 'active' and (
            self.end_date is None or self.end_date >= date.today()
        )
```

### 2.2 Nuovo Timesheet Model (Submission-based)

```python
class Timesheet(db.Model):
    __tablename__ = 'timesheets'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # Period covered
    week_start_date = db.Column(db.Date, nullable=False)  # Lunedì della settimana
    week_end_date = db.Column(db.Date, nullable=False)    # Domenica della settimana
    
    # Submission workflow
    status = db.Column(db.String(20), default='draft')  # draft, submitted, approved, rejected
    submission_date = db.Column(db.DateTime, nullable=True)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    
    # Totals (calculated from entries)
    total_hours = db.Column(db.Float, default=0.0)
    billable_hours = db.Column(db.Float, default=0.0)
    
    # Audit
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='timesheets')
    approver = db.relationship('User', foreign_keys=[approved_by], backref='approved_timesheets')
    entries = db.relationship('TimesheetEntry', backref='timesheet', cascade='all, delete-orphan')
    
    # Constraints
    __table_args__ = (
        db.UniqueConstraint('user_id', 'week_start_date', name='unique_user_week'),
    )
    
    def __repr__(self):
        return f'<Timesheet {self.user_id} - Week {self.week_start_date}>'
    
    def calculate_totals(self):
        """Calcola i totali dalle entries"""
        self.total_hours = sum(entry.hours for entry in self.entries)
        self.billable_hours = sum(entry.hours for entry in self.entries if entry.billable)
    
    @property
    def can_be_submitted(self):
        """Verifica se il timesheet può essere submitted"""
        return self.status == 'draft' and len(self.entries) > 0
```

### 2.3 TimesheetEntry Model

```python
class TimesheetEntry(db.Model):
    __tablename__ = 'timesheet_entries'
    
    id = db.Column(db.Integer, primary_key=True)
    timesheet_id = db.Column(db.Integer, db.ForeignKey('timesheets.id'), nullable=False)
    
    # Work details
    date = db.Column(db.Date, nullable=False)
    project_id = db.Column(db.Integer, db.ForeignKey('project.id'), nullable=False)
    task_id = db.Column(db.Integer, db.ForeignKey('task.id'), nullable=True)
    hours = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text, nullable=True)
    
    # Entry type
    entry_type = db.Column(db.String(20), default='work')  # work, vacation, leave, smartworking, training
    
    # Billing information
    client_id = db.Column(db.Integer, db.ForeignKey('client.id'), nullable=True)
    contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)
    billable = db.Column(db.Boolean, default=False)
    billing_rate = db.Column(db.Float, nullable=True)
    billing_status = db.Column(db.String(20), default='unbilled')  # unbilled, billed, non-billable
    invoice_id = db.Column(db.Integer, nullable=True)  # Future FK to invoice
    
    # Audit
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    project = db.relationship('Project', backref='timesheet_entries')
    task = db.relationship('Task', backref='timesheet_entries')
    client = db.relationship('Client', backref='timesheet_entries')
    
    def __repr__(self):
        return f'<TimesheetEntry {self.date} - {self.hours}h>'
    
    @property
    def billing_amount(self):
        """Calcola l'importo fatturabile"""
        if self.billable and self.billing_rate:
            return self.hours * self.billing_rate
        return 0.0
    
    def auto_set_billing_info(self):
        """Imposta automaticamente le info di billing dal contratto/progetto"""
        if self.contract_id:
            contract = Contract.query.get(self.contract_id)
            if contract and contract.is_active:
                self.billable = True
                self.billing_rate = contract.hourly_rate
                self.client_id = contract.client_id
```

### 2.4 TimeOffRequest Model

```python
class TimeOffRequest(db.Model):
    __tablename__ = 'time_off_requests'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # Request details
    request_type = db.Column(db.String(20), nullable=False)  # vacation, sick_leave, personal_leave, smartworking
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    hours_requested = db.Column(db.Float, nullable=True)  # For partial days
    notes = db.Column(db.Text, nullable=True)
    
    # Approval workflow
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected, cancelled
    submission_date = db.Column(db.DateTime, default=datetime.utcnow)
    approval_date = db.Column(db.DateTime, nullable=True)
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    rejection_reason = db.Column(db.Text, nullable=True)
    
    # HR fields
    requires_hr_approval = db.Column(db.Boolean, default=False)
    hr_approved_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    hr_approval_date = db.Column(db.DateTime, nullable=True)
    
    # Audit
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='time_off_requests')
    manager = db.relationship('User', foreign_keys=[approved_by], backref='approved_time_offs')
    hr_approver = db.relationship('User', foreign_keys=[hr_approved_by], backref='hr_approved_time_offs')
    
    def __repr__(self):
        return f'<TimeOffRequest {self.user_id} - {self.request_type} - {self.start_date}>'
    
    @property
    def duration_days(self):
        """Calcola la durata in giorni"""
        return (self.end_date - self.start_date).days + 1
    
    @property
    def is_pending(self):
        """Verifica se la richiesta è in attesa"""
        return self.status == 'pending'
```

---

## FASE 3: MODIFICHE AI MODELLI ESISTENTI 🔧

### 3.1 Proposal Model - Aggiungere relationship

```python
# Aggiungere alla classe Proposal:
contract = db.relationship('Contract', uselist=False, backref='parent_proposal')

@property
def can_create_contract(self):
    """Verifica se si può creare un contratto da questa proposta"""
    return self.status == 'accepted' and not hasattr(self, 'contract')
```

### 3.2 Project Model - Aggiungere contract_id

```python
# Aggiungere alla classe Project:
contract_id = db.Column(db.Integer, db.ForeignKey('contracts.id'), nullable=True)

@property
def current_contract_rate(self):
    """Restituisce la tariffa del contratto attivo"""
    if self.contract and self.contract.is_active:
        return self.contract.hourly_rate
    return None
```

### 3.3 User Model - Verificare relationships

```python
# Verificare che esistano già:
# timesheet_entries = db.relationship('Timesheet', backref='user', lazy='dynamic')
# notifications = db.relationship('Notification', backref='user')

# Aggiungere se necessario:
def get_manager(self):
    """Restituisce il manager dell'utente basato sul dipartimento"""
    if self.department_obj and self.department_obj.manager:
        return self.department_obj.manager
    return None
```

---

## FASE 4: MIGRAZIONI DATABASE 🗄️

### 4.1 Migration Script Setup

```python
# backend/db/migrations/001_timesheet_restructure.py
from alembic import op
import sqlalchemy as sa
from datetime import datetime, date

def upgrade():
    """
    Migration da timesheet entry-based a submission-based
    """
    
    # 1. Backup existing data
    op.execute("""
        CREATE TABLE timesheet_backup AS 
        SELECT * FROM timesheet
    """)
    
    # 2. Create new tables
    create_contracts_table()
    create_new_timesheets_table()
    create_timesheet_entries_table()
    create_time_off_requests_table()
    
    # 3. Migrate existing data
    migrate_existing_timesheets()
    
    # 4. Update existing tables
    update_proposal_table()
    update_project_table()
    
    # 5. Drop old timesheet table (commented for safety)
    # op.drop_table('timesheet')

def downgrade():
    """Rollback plan"""
    # Restore from backup
    op.execute("""
        DROP TABLE IF EXISTS timesheet;
        CREATE TABLE timesheet AS SELECT * FROM timesheet_backup;
    """)
    
    # Drop new tables
    op.drop_table('time_off_requests')
    op.drop_table('timesheet_entries') 
    op.drop_table('timesheets')
    op.drop_table('contracts')

def create_contracts_table():
    op.create_table('contracts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('proposal_id', sa.Integer(), nullable=False),
        sa.Column('client_id', sa.Integer(), nullable=False),
        sa.Column('contract_number', sa.String(length=50), nullable=False),
        sa.Column('title', sa.String(length=128), nullable=False),
        sa.Column('start_date', sa.Date(), nullable=False),
        sa.Column('end_date', sa.Date(), nullable=True),
        sa.Column('signed_date', sa.Date(), nullable=True),
        sa.Column('hourly_rate', sa.Float(), nullable=False),
        sa.Column('daily_rate', sa.Float(), nullable=True),
        sa.Column('currency', sa.String(length=3), nullable=True),
        sa.Column('payment_terms', sa.String(length=100), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=True),
        sa.Column('value', sa.Float(), nullable=True),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('created_by', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['client_id'], ['client.id'], ),
        sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
        sa.ForeignKeyConstraint(['proposal_id'], ['proposal.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('contract_number')
    )

def migrate_existing_timesheets():
    """
    Migra i timesheet esistenti al nuovo formato:
    - Ogni entry diventa TimesheetEntry
    - Crea Timesheet container per settimana
    - Mantiene status e date
    """
    # Implementation dettagliata della migrazione
    pass
```

### 4.2 Data Migration Strategy

```python
def migrate_existing_timesheets():
    """
    Strategia di migrazione:
    1. Raggruppa entries per user/week
    2. Crea Timesheet container per ogni settimana
    3. Converte entries esistenti in TimesheetEntry
    4. Mantiene status approval dove possibile
    """
    
    # Get existing timesheet data
    connection = op.get_bind()
    old_timesheets = connection.execute("""
        SELECT * FROM timesheet_backup 
        ORDER BY user_id, date
    """).fetchall()
    
    # Group by user and week
    user_weeks = {}
    for ts in old_timesheets:
        user_id = ts.user_id
        week_start = get_week_start(ts.date)
        
        if user_id not in user_weeks:
            user_weeks[user_id] = {}
        if week_start not in user_weeks[user_id]:
            user_weeks[user_id][week_start] = []
        
        user_weeks[user_id][week_start].append(ts)
    
    # Create new timesheet and entries
    for user_id, weeks in user_weeks.items():
        for week_start, entries in weeks.items():
            # Create Timesheet container
            timesheet_id = create_timesheet_container(user_id, week_start, entries)
            
            # Create TimesheetEntry records
            for entry in entries:
                create_timesheet_entry(timesheet_id, entry)

def get_week_start(date_str):
    """Calcola il lunedì della settimana per una data"""
    from datetime import datetime, timedelta
    date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
    days_since_monday = date_obj.weekday()
    week_start = date_obj - timedelta(days=days_since_monday)
    return week_start
```

---

## FASE 5: API ENDPOINTS 🚀

### 5.1 Contract Endpoints

```python
# backend/blueprints/api/contracts.py
from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required

contracts_bp = Blueprint('contracts', __name__)

@contracts_bp.route('/', methods=['GET'])
@login_required
def list_contracts():
    """Lista contratti con filtri"""
    pass

@contracts_bp.route('/', methods=['POST'])
@login_required
def create_contract():
    """Crea nuovo contratto"""
    pass

@contracts_bp.route('/<int:contract_id>', methods=['GET'])
@login_required
def get_contract(contract_id):
    """Dettagli contratto"""
    pass

@contracts_bp.route('/<int:contract_id>', methods=['PUT'])
@login_required
def update_contract(contract_id):
    """Aggiorna contratto"""
    pass

@contracts_bp.route('/<int:contract_id>', methods=['DELETE'])
@login_required
def delete_contract(contract_id):
    """Elimina contratto"""
    pass

@contracts_bp.route('/<int:contract_id>/activate', methods=['POST'])
@login_required
def activate_contract(contract_id):
    """Attiva contratto"""
    pass

@contracts_bp.route('/from-proposal/<int:proposal_id>', methods=['POST'])
@login_required
def create_from_proposal(proposal_id):
    """Crea contratto da proposta accettata"""
    pass
```

### 5.2 Nuovo Timesheet Endpoints (Submission-based)

```python
# Modificare backend/blueprints/api/timesheets.py

@timesheets_bp.route('/week/<week_date>', methods=['GET'])
@login_required
def get_week_timesheet(week_date):
    """
    Recupera timesheet per una settimana specifica
    Format: YYYY-MM-DD (lunedì della settimana)
    """
    pass

@timesheets_bp.route('/week/<week_date>', methods=['POST'])
@login_required
def create_or_update_week_timesheet(week_date):
    """Crea o aggiorna timesheet settimanale"""
    pass

@timesheets_bp.route('/<int:timesheet_id>/submit', methods=['POST'])
@login_required
def submit_timesheet(timesheet_id):
    """Submit timesheet per approvazione"""
    pass

@timesheets_bp.route('/<int:timesheet_id>/approve', methods=['POST'])
@login_required
def approve_timesheet(timesheet_id):
    """Approva timesheet (solo manager)"""
    pass

@timesheets_bp.route('/<int:timesheet_id>/reject', methods=['POST'])
@login_required
def reject_timesheet(timesheet_id):
    """Rifiuta timesheet con motivo"""
    pass

@timesheets_bp.route('/entries', methods=['POST'])
@login_required
def add_timesheet_entry():
    """Aggiungi entry a timesheet"""
    pass

@timesheets_bp.route('/entries/<int:entry_id>', methods=['PUT'])
@login_required
def update_timesheet_entry(entry_id):
    """Aggiorna entry timesheet"""
    pass

@timesheets_bp.route('/entries/<int:entry_id>', methods=['DELETE'])
@login_required
def delete_timesheet_entry(entry_id):
    """Elimina entry timesheet"""
    pass

@timesheets_bp.route('/pending-approval', methods=['GET'])
@login_required
def get_pending_approvals():
    """Lista timesheet in attesa di approvazione (per manager)"""
    pass

@timesheets_bp.route('/user/<int:user_id>/weeks', methods=['GET'])
@login_required
def get_user_timesheet_weeks(user_id):
    """Lista settimane timesheet per utente"""
    pass
```

### 5.3 Time Off Request Endpoints

```python
# backend/blueprints/api/time_off.py
from flask import Blueprint

time_off_bp = Blueprint('time_off', __name__)

@time_off_bp.route('/', methods=['GET'])
@login_required
def list_time_off_requests():
    """Lista richieste time-off"""
    pass

@time_off_bp.route('/', methods=['POST'])
@login_required
def create_time_off_request():
    """Crea nuova richiesta time-off"""
    pass

@time_off_bp.route('/<int:request_id>', methods=['GET'])
@login_required
def get_time_off_request(request_id):
    """Dettagli richiesta time-off"""
    pass

@time_off_bp.route('/<int:request_id>', methods=['PUT'])
@login_required
def update_time_off_request(request_id):
    """Aggiorna richiesta time-off"""
    pass

@time_off_bp.route('/<int:request_id>/approve', methods=['POST'])
@login_required
def approve_time_off_request(request_id):
    """Approva richiesta time-off"""
    pass

@time_off_bp.route('/<int:request_id>/reject', methods=['POST'])
@login_required
def reject_time_off_request(request_id):
    """Rifiuta richiesta time-off"""
    pass

@time_off_bp.route('/pending', methods=['GET'])
@login_required
def get_pending_time_off_requests():
    """Richieste in attesa per manager"""
    pass

@time_off_bp.route('/calendar/<int:year>/<int:month>', methods=['GET'])
@login_required
def get_time_off_calendar(year, month):
    """Time-off calendar per team"""
    pass
```

### 5.4 Notification Integration

```python
# backend/services/notification_service.py
from models import Notification, User, Timesheet, TimeOffRequest

class NotificationService:
    
    @staticmethod
    def notify_timesheet_submitted(timesheet_id):
        """Notifica manager quando timesheet è submitted"""
        timesheet = Timesheet.query.get(timesheet_id)
        manager = timesheet.user.get_manager()
        
        if manager:
            notification = Notification(
                user_id=manager.id,
                title=f"Timesheet da approvare",
                message=f"{timesheet.user.full_name} ha inviato il timesheet per la settimana del {timesheet.week_start_date}",
                link=f"/timesheets/{timesheet_id}/approve",
                type='info'
            )
            db.session.add(notification)
            db.session.commit()
    
    @staticmethod
    def notify_timesheet_approved(timesheet_id):
        """Notifica utente quando timesheet è approvato"""
        timesheet = Timesheet.query.get(timesheet_id)
        
        notification = Notification(
            user_id=timesheet.user_id,
            title="Timesheet approvato",
            message=f"Il tuo timesheet per la settimana del {timesheet.week_start_date} è stato approvato",
            link=f"/timesheets/{timesheet_id}",
            type='success'
        )
        db.session.add(notification)
        db.session.commit()
    
    @staticmethod
    def notify_timesheet_rejected(timesheet_id):
        """Notifica utente quando timesheet è rifiutato"""
        timesheet = Timesheet.query.get(timesheet_id)
        
        notification = Notification(
            user_id=timesheet.user_id,
            title="Timesheet rifiutato",
            message=f"Il tuo timesheet per la settimana del {timesheet.week_start_date} è stato rifiutato. Motivo: {timesheet.rejection_reason}",
            link=f"/timesheets/{timesheet_id}",
            type='warning'
        )
        db.session.add(notification)
        db.session.commit()
    
    @staticmethod
    def notify_time_off_request(request_id):
        """Notifica manager per richiesta ferie"""
        request = TimeOffRequest.query.get(request_id)
        manager = request.user.get_manager()
        
        if manager:
            notification = Notification(
                user_id=manager.id,
                title="Nuova richiesta ferie",
                message=f"{request.user.full_name} ha richiesto {request.request_type} dal {request.start_date} al {request.end_date}",
                link=f"/time-off/{request_id}/approve",
                type='info'
            )
            db.session.add(notification)
            db.session.commit()
```

---

## FASE 6: TESTING 🧪

### 6.1 Unit Tests

```python
# backend/tests/test_models_timesheet.py
import pytest
from datetime import date, datetime, timedelta
from models import Contract, Timesheet, TimesheetEntry, TimeOffRequest

class TestTimesheetModels:
    
    def test_contract_creation(self, db_session):
        """Test creazione contratto"""
        contract = Contract(
            proposal_id=1,
            client_id=1,
            contract_number="CNT-2024-001",
            title="Test Contract",
            start_date=date.today(),
            hourly_rate=100.0,
            created_by=1
        )
        db_session.add(contract)
        db_session.commit()
        
        assert contract.id is not None
        assert contract.is_active == True
    
    def test_timesheet_submission_workflow(self, db_session):
        """Test workflow submission timesheet"""
        timesheet = Timesheet(
            user_id=1,
            week_start_date=date.today(),
            week_end_date=date.today() + timedelta(days=6)
        )
        db_session.add(timesheet)
        db_session.commit()
        
        # Test initial state
        assert timesheet.status == 'draft'
        assert timesheet.can_be_submitted == False  # No entries
        
        # Add entry
        entry = TimesheetEntry(
            timesheet_id=timesheet.id,
            date=date.today(),
            project_id=1,
            hours=8.0,
            entry_type='work'
        )
        db_session.add(entry)
        db_session.commit()
        
        # Test can submit
        assert timesheet.can_be_submitted == True
        
        # Submit
        timesheet.status = 'submitted'
        timesheet.submission_date = datetime.utcnow()
        db_session.commit()
        
        assert timesheet.status == 'submitted'
    
    def test_timesheet_entry_billing_calculation(self, db_session):
        """Test calcolo billing per entry"""
        entry = TimesheetEntry(
            timesheet_id=1,
            date=date.today(),
            project_id=1,
            hours=8.0,
            billable=True,
            billing_rate=50.0
        )
        
        assert entry.billing_amount == 400.0  # 8 * 50
    
    def test_time_off_request_workflow(self, db_session):
        """Test workflow richiesta ferie"""
        request = TimeOffRequest(
            user_id=1,
            request_type='vacation',
            start_date=date.today() + timedelta(days=7),
            end_date=date.today() + timedelta(days=10)
        )
        db_session.add(request)
        db_session.commit()
        
        assert request.status == 'pending'
        assert request.duration_days == 4
        assert request.is_pending == True
```

### 6.2 API Tests

```python
# backend/tests/test_api_timesheet.py
import pytest
from flask import url_for

class TestTimesheetAPI:
    
    def test_create_weekly_timesheet(self, client, auth_headers):
        """Test creazione timesheet settimanale"""
        response = client.post(
            '/api/timesheets/week/2024-01-15',
            json={
                'entries': [
                    {
                        'date': '2024-01-15',
                        'project_id': 1,
                        'hours': 8.0,
                        'description': 'Development work'
                    }
                ]
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.get_json()
        assert data['success'] == True
        assert 'timesheet_id' in data['data']
    
    def test_submit_approve_workflow(self, client, auth_headers, manager_headers):
        """Test workflow submission e approvazione"""
        # Create timesheet
        timesheet_response = client.post(
            '/api/timesheets/week/2024-01-15',
            json={'entries': [{'date': '2024-01-15', 'project_id': 1, 'hours': 8.0}]},
            headers=auth_headers
        )
        timesheet_id = timesheet_response.get_json()['data']['timesheet_id']
        
        # Submit for approval
        submit_response = client.post(
            f'/api/timesheets/{timesheet_id}/submit',
            headers=auth_headers
        )
        assert submit_response.status_code == 200
        
        # Approve as manager
        approve_response = client.post(
            f'/api/timesheets/{timesheet_id}/approve',
            headers=manager_headers
        )
        assert approve_response.status_code == 200
    
    def test_billing_rate_calculation(self, client, auth_headers):
        """Test calcolo automatico rate da contratto"""
        # Test con contract_id per auto-set billing info
        response = client.post(
            '/api/timesheets/entries',
            json={
                'timesheet_id': 1,
                'date': '2024-01-15',
                'project_id': 1,
                'contract_id': 1,
                'hours': 8.0
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.get_json()
        entry = data['data']
        assert entry['billable'] == True
        assert entry['billing_rate'] > 0
```

### 6.3 Integration Tests

```python
# backend/tests/test_timesheet_integration.py

class TestTimesheetIntegration:
    
    def test_contract_to_timesheet_flow(self, client, auth_headers):
        """Test flow completo: proposal → contract → timesheet → billing"""
        # 1. Create proposal (già esistente)
        # 2. Accept proposal
        # 3. Create contract from proposal
        # 4. Create timesheet entry linked to contract
        # 5. Verify billing info auto-populated
        pass
    
    def test_notification_triggers(self, client, auth_headers):
        """Test trigger notifiche su eventi timesheet"""
        # Test notifications sent on:
        # - Timesheet submission
        # - Timesheet approval/rejection
        # - Time-off request
        pass
    
    def test_billing_data_consistency(self, client, auth_headers):
        """Test consistenza dati billing"""
        # Test che i totali calcolati siano corretti
        # Test che le rate vengano applicate correttamente
        # Test che lo status billing sia corretto
        pass
```

### 6.4 Migration Tests

```python
# backend/tests/test_migration.py

def test_data_migration_integrity():
    """
    Test che verifica l'integrità della migrazione dati:
    - Tutti i timesheet esistenti sono migrati
    - Nessun dato è perso
    - I totali calcolati sono corretti
    - Le relazioni sono mantenute
    """
    
    # Count records before migration
    # Run migration
    # Count records after migration
    # Verify data integrity
    pass

def test_migration_rollback():
    """Test rollback della migrazione"""
    # Run migration
    # Run rollback
    # Verify original state restored
    pass
```

---

## FASE 7: IMPLEMENTAZIONE STEP-BY-STEP 📝

### Step 1: Contract Model + Basic API
**Durata:** 3-4 giorni

- [x] Creare Contract model
- [x] Migration per Contract table  
- [x] Basic CRUD API per Contract
- [x] Test unitari Contract
- [x] Integration con Proposal model

**Deliverables:**
- `models.py` aggiornato con Contract
- `migrations/001_add_contracts.py`
- `blueprints/api/contracts.py`
- Test suite per Contract

### Step 2: Timesheet Restructure (CRITICO)
**Durata:** 5-6 giorni

- [x] Backup dati esistenti
- [x] Creare nuovi modelli Timesheet + TimesheetEntry
- [x] Script migrazione dati
- [x] Test migrazione in ambiente dev
- [x] API endpoints per nuovo timesheet

**Deliverables:**
- Nuovi modelli Timesheet/TimesheetEntry
- Script migrazione completo
- API aggiornata per submission workflow
- Test migrazione

### Step 3: Time Off Request
**Durata:** 3-4 giorni

- [x] Creare TimeOffRequest model
- [x] API endpoints per time-off
- [x] Test workflow approvazione
- [x] Integration con notification system

**Deliverables:**
- TimeOffRequest model
- `blueprints/api/time_off.py`
- Test workflow approval

### Step 4: Enhanced Timesheet API
**Durata:** 4-5 giorni

- [x] Submission/approval workflow completo
- [x] Billing integration con Contract
- [x] Notification triggers
- [x] Advanced reporting endpoints

**Deliverables:**
- API completa per workflow approval
- Notification service integration
- Billing calculation logic

### Step 5: Testing & Validation
**Durata:** 3-4 giorni

- [x] Test completi su tutti i flussi
- [x] Validazione business rules
- [x] Performance testing
- [x] Security audit

**Deliverables:**
- Suite test completa
- Performance benchmarks
- Security validation report

### Step 6: Production Migration
**Durata:** 2-3 giorni

- [x] Backup production
- [x] Deploy migration
- [x] Verifica integrità dati
- [x] Monitor performance post-deploy

**Deliverables:**
- Production deployment plan
- Rollback procedures
- Monitoring setup

---

## RISCHI E MITIGAZIONI ⚠️

### Rischi Critici

#### 1. **Perdita dati durante migrazione** 
**Probabilità:** Media  
**Impatto:** Alto  
**Mitigazione:**
- Backup multipli prima della migrazione
- Test migrazione in staging con dati reali
- Rollback plan documentato e testato
- Checkpoint intermedi durante migrazione

#### 2. **Breaking changes API**
**Probabilità:** Alta  
**Impatto:** Alto  
**Mitigazione:**
- API versioning (`/api/v1/`, `/api/v2/`)
- Backward compatibility per 1-2 versioni
- Gradual migration plan per frontend
- Endpoint deprecation warnings

#### 3. **Performance degradation**
**Probabilità:** Media  
**Impatto:** Medio  
**Mitigazione:**
- Indexing strategy per nuove tabelle
- Query optimization
- Pagination su tutti gli endpoint
- Monitoring performance pre/post deploy

#### 4. **Workflow disruption**
**Probabilità:** Alta  
**Impatto:** Medio  
**Mitigazione:**
- User training sui nuovi workflow
- Gradual rollout
- Support team prepared
- Fallback procedures

### Dependencies

#### Frontend Updates Required:
- Timesheet submission UI → nuovo workflow settimanale
- Approval interface per manager
- Time-off request forms
- Dashboard widgets update

#### Other Modules Impact:
- **Reports module** → aggiornare query per nuova struttura
- **Dashboard** → aggiornare KPI calculation
- **Project management** → billing integration
- **CRM module** → contract linkage

#### Infrastructure:
- Database schema changes
- API endpoint changes
- Notification system integration

---

## METRICHE DI SUCCESSO 📊

### Performance Metrics:
- **API Response Time:** < 200ms per endpoint standard
- **Database Query Time:** < 100ms per query timesheet
- **Migration Time:** < 30 minuti per production migration

### Business Metrics:
- **User Adoption:** > 80% users submit timesheet in new format within 2 weeks
- **Approval Workflow:** < 24h average approval time
- **Data Accuracy:** 100% data integrity post-migration

### Technical Metrics:
- **Test Coverage:** > 90% per nuovi modelli e endpoint
- **Error Rate:** < 1% post-deployment
- **Rollback Success:** < 15 minuti rollback time se necessario

---

## DOCUMENTAZIONE RICHIESTA 📚

### Technical Documentation:
- API documentation (OpenAPI/Swagger)
- Database schema changes
- Migration procedures
- Troubleshooting guide

### User Documentation:
- New timesheet workflow guide
- Manager approval guide
- Time-off request procedures
- FAQ for common issues

### Operational Documentation:
- Deployment procedures
- Monitoring setup
- Backup/restore procedures
- Performance tuning guide

---

**DURATA TOTALE STIMATA:** 2-3 settimane  
**PRIORITÀ:** ALTA (blocking per altri subtask del timesheet system)  
**TEAM RICHIESTO:** 1-2 sviluppatori backend, 1 frontend developer per testing integration  

**PROSSIMO STEP:** Approvazione piano e avvio Step 1 (Contract Model) 