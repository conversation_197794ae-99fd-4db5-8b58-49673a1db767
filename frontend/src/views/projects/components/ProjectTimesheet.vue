<template>
  <div class="space-y-6">
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Timesheet Dettaglio
            </h3>
            <!-- Navigazione mesi -->
            <div class="flex items-center space-x-2">
              <button
                @click="previousMonth"
                class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
              </button>
              <span class="text-sm font-medium text-gray-900 dark:text-white min-w-[80px] text-center">
                {{ currentMonth }}/{{ currentYear }}
              </span>
              <button
                @click="nextMonth"
                class="p-1 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </button>
            </div>

            <!-- Filtro persona -->
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500 dark:text-gray-400">Vista:</span>
              <select 
                v-model="selectedMember"
                @change="loadTimesheet"
                class="text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="">Tutti i membri</option>
                <option 
                  v-for="member in project?.team_members || []" 
                  :key="member.id" 
                  :value="member.id"
                >
                  {{ member.first_name }} {{ member.last_name }}
                </option>
              </select>
            </div>
          </div>
          <button
            @click="showAddModal = true"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Aggiungi Ore
          </button>
        </div>
      </div>

      <!-- Loading -->
      <div v-if="timesheetLoading" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>

      <!-- Error -->
      <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4 m-6">
        <p class="text-red-600">{{ error }}</p>
      </div>

      <!-- Vista mensile: Task per riga, Giorni per colonna -->
      <div v-if="!timesheetLoading && timesheetData" class="p-6">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider sticky left-0 bg-gray-50 dark:bg-gray-700">
                  Task
                </th>
                <th 
                  v-for="day in daysInMonth" 
                  :key="day"
                  class="px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[40px]"
                  :class="{ 'bg-blue-50 dark:bg-blue-900': isToday(day) }"
                >
                  {{ day }}
                </th>
                <th class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider bg-gray-50 dark:bg-gray-700">
                  Tot
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="task in timesheetData.tasks" :key="task.id">
                <td class="px-4 py-3 whitespace-nowrap sticky left-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
                  <div class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ task.name }}
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400">
                    {{ task.workers.length ? task.workers.join(', ') : 'Nessuno ha lavorato' }}
                  </div>
                </td>

                <td
                  v-for="day in daysInMonth"
                  :key="day"
                  class="px-2 py-3 text-center min-w-[40px] cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
                  :class="{ 'bg-blue-50 dark:bg-blue-900': isToday(day) }"
                  @click="editCell(task.id, day)"
                >
                  <div v-if="task.daily_hours[day] > 0" class="flex flex-col items-center">
                    <span class="text-xs font-medium text-primary-600 dark:text-primary-400">
                      {{ formatHours(task.daily_hours[day]) }}
                    </span>
                    <!-- Indicatore billing (se progetto ha contratto) -->
                    <div v-if="project?.contract" class="flex space-x-1 mt-1">
                      <div
                        class="w-1.5 h-1.5 rounded-full"
                        :class="task.daily_billing && task.daily_billing[day] ? 'bg-green-500' : 'bg-gray-300'"
                        :title="task.daily_billing && task.daily_billing[day] ? 'Fatturabile' : 'Non fatturabile'"
                      ></div>
                    </div>
                  </div>
                  <span v-else class="text-gray-300 dark:text-gray-600">-</span>
                </td>

                <td class="px-3 py-3 text-center bg-gray-50 dark:bg-gray-700">
                  <span class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ formatHours(task.total_hours) }}
                  </span>
                </td>
              </tr>

              <!-- Riga totali -->
              <tr class="bg-gray-100 dark:bg-gray-600 font-medium">
                <td class="px-4 py-3 text-sm font-semibold text-gray-900 dark:text-white sticky left-0 bg-gray-100 dark:bg-gray-600">
                  TOTALE GIORNALIERO
                </td>
                <td 
                  v-for="day in daysInMonth" 
                  :key="day"
                  class="px-2 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white"
                  :class="{ 'bg-blue-100 dark:bg-blue-800': isToday(day) }"
                >
                  {{ formatHours(timesheetData.daily_totals[day] || 0) }}
                </td>
                <td class="px-3 py-3 text-center text-sm font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-600">
                  {{ formatHours(timesheetData.grand_total) }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty state -->
        <div v-if="timesheetData.tasks.length === 0" class="text-center py-8">
          <p class="text-gray-500 dark:text-gray-400">Nessun task trovato per questo progetto</p>
        </div>

        <!-- Legenda billing (solo se progetto ha contratto) -->
        <div v-if="project?.contract && timesheetData.tasks.length > 0" class="mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
          <div class="flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-300">
            <span class="font-medium">Legenda:</span>
            <div class="flex items-center space-x-1">
              <div class="w-1.5 h-1.5 rounded-full bg-green-500"></div>
              <span>Fatturabile</span>
            </div>
            <div class="flex items-center space-x-1">
              <div class="w-1.5 h-1.5 rounded-full bg-gray-300"></div>
              <span>Non fatturabile</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Aggiungi/Modifica Ore -->
    <div v-if="showAddModal || showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeModal">
      <div class="relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {{ showEditModal ? 'Modifica Ore' : 'Aggiungi Ore' }}
          </h3>
          
          <form @submit.prevent="saveTimesheet">
            <div class="grid grid-cols-1 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Task</label>
                <select 
                  v-model="formData.task_id"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
                  <option value="">Seleziona task</option>
                  <option 
                    v-for="task in timesheetData?.tasks || []" 
                    :key="task.id" 
                    :value="task.id"
                  >
                    {{ task.name }}
                  </option>
                </select>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Data</label>
                <input 
                  v-model="formData.date" 
                  type="date"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Ore</label>
                <input 
                  v-model="formData.hours" 
                  type="number" 
                  step="0.25"
                  min="0"
                  max="24"
                  required
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                >
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Descrizione</label>
                <textarea
                  v-model="formData.description"
                  rows="3"
                  class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                ></textarea>
              </div>

              <!-- Sezione Billing (solo se progetto ha contratto) -->
              <div v-if="project?.contract" class="border-t border-gray-200 dark:border-gray-600 pt-4">
                <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Informazioni Fatturazione</h4>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Fatturabile
                    </label>
                    <select
                      v-model="formData.billable"
                      class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    >
                      <option :value="true">Sì - Fatturabile</option>
                      <option :value="false">No - Interno</option>
                    </select>
                  </div>

                  <div v-if="formData.billable">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Tariffa (€/h)
                    </label>
                    <input
                      v-model="formData.billing_rate"
                      type="number"
                      step="0.01"
                      min="0"
                      :placeholder="project.contract.hourly_rate"
                      class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                    >
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Tariffa contrattuale: {{ formatCurrency(project.contract.hourly_rate) }}/h
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6">
              <button 
                type="button" 
                @click="closeModal"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"
              >
                Annulla
              </button>
              <button 
                type="submit"
                :disabled="saving"
                class="px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"
              >
                {{ saving ? 'Salvataggio...' : (showEditModal ? 'Aggiorna' : 'Aggiungi') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'

const props = defineProps({
  project: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const authStore = useAuthStore()

// State
const timesheetData = ref(null)
const timesheetLoading = ref(false)
const error = ref('')
const saving = ref(false)

// Date navigation
const currentYear = ref(new Date().getFullYear())
const currentMonth = ref(new Date().getMonth() + 1)
const selectedMember = ref('')

// Modal
const showAddModal = ref(false)
const showEditModal = ref(false)
const editingCell = ref(null)

// Form data
const formData = ref({
  task_id: '',
  date: '',
  hours: 0,
  description: '',
  billable: true,
  billing_rate: null
})

// Computed
const daysInMonth = computed(() => {
  if (!timesheetData.value) return []
  return Array.from({ length: timesheetData.value.days_in_month }, (_, i) => i + 1)
})

// Methods
const loadTimesheet = async () => {
  if (!props.project?.id) return
  
  timesheetLoading.value = true
  error.value = ''
  
  try {
    const params = new URLSearchParams({
      year: currentYear.value.toString(),
      month: currentMonth.value.toString()
    })
    
    if (selectedMember.value) {
      params.append('member_id', selectedMember.value.toString())
    }

    const response = await fetch(`/api/timesheets/project/${props.project.id}/monthly?${params}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) {
      throw new Error('Errore nel caricamento del timesheet')
    }

    const result = await response.json()
    timesheetData.value = result.data
  } catch (err) {
    error.value = err.message
  } finally {
    timesheetLoading.value = false
  }
}

const findExistingTimesheetEntry = async (taskId, date) => {
  try {
    // Get current user's timesheet entries for this project and date
    const response = await fetch(`/api/timesheets/project/${props.project.id}?start_date=${date}&end_date=${date}&task_id=${taskId}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (!response.ok) {
      return null
    }

    const result = await response.json()
    const entries = result.data || []

    // Find entry for current user on this date and task
    const existingEntry = entries.find(entry =>
      entry.user_id === authStore.user.id &&
      entry.task_id === parseInt(taskId) &&
      entry.date === date
    )

    return existingEntry ? existingEntry.id : null
  } catch (err) {
    console.error('Error finding existing timesheet entry:', err)
    return null
  }
}

const saveTimesheet = async () => {
  saving.value = true

  try {
    const data = {
      ...formData.value,
      project_id: props.project.id
    }

    // Check if we're editing an existing entry
    const existingEntryId = await findExistingTimesheetEntry(
      formData.value.task_id,
      formData.value.date
    )

    let response
    if (existingEntryId) {
      // Update existing entry
      response = await fetch(`/api/timesheets/${existingEntryId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': authStore.csrfToken
        },
        body: JSON.stringify(data)
      })
    } else {
      // Create new entry
      response = await fetch('/api/timesheets/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': authStore.csrfToken
        },
        body: JSON.stringify(data)
      })
    }

    if (!response.ok) {
      throw new Error('Errore nel salvataggio del timesheet')
    }

    await loadTimesheet()
    closeModal()
  } catch (err) {
    error.value = err.message
  } finally {
    saving.value = false
  }
}

const editCell = async (taskId, day) => {
  const task = timesheetData.value.tasks.find(t => t.id === taskId)
  if (!task) return

  editingCell.value = { taskId, day }

  // Inizializza con tariffa contrattuale se disponibile
  const defaultRate = props.project?.contract?.hourly_rate || null
  const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`

  // Check if editing existing entry and load its data
  let existingBillable = props.project?.contract ? true : false
  let existingRate = defaultRate
  let existingDescription = ''

  if (task.daily_hours[day] > 0) {
    // Try to load existing entry data
    try {
      const response = await fetch(`/api/timesheets/project/${props.project.id}?start_date=${dateStr}&end_date=${dateStr}&task_id=${taskId}`, {
        headers: {
          'Content-Type': 'application/json',
          'X-CSRFToken': authStore.csrfToken
        }
      })

      if (response.ok) {
        const result = await response.json()
        const entries = result.data || []
        const existingEntry = entries.find(entry =>
          entry.user_id === authStore.user.id &&
          entry.task_id === parseInt(taskId) &&
          entry.date === dateStr
        )

        if (existingEntry) {
          // Handle billable field explicitly - could be true, false, or undefined
          existingBillable = existingEntry.billable !== undefined ? existingEntry.billable : (props.project?.contract ? true : false)
          existingRate = existingEntry.billing_rate || defaultRate
          existingDescription = existingEntry.description || ''

          console.log('Loading existing entry:', {
            id: existingEntry.id,
            billable: existingEntry.billable,
            billing_rate: existingEntry.billing_rate,
            description: existingEntry.description
          })
        }
      }
    } catch (err) {
      console.error('Error loading existing entry:', err)
    }
  }

  formData.value = {
    task_id: taskId,
    date: dateStr,
    hours: task.daily_hours[day] || 0,
    description: existingDescription,
    billable: existingBillable,
    billing_rate: existingRate
  }

  if (task.daily_hours[day] > 0) {
    showEditModal.value = true
  } else {
    showAddModal.value = true
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  editingCell.value = null
  formData.value = {
    task_id: '',
    date: '',
    hours: 0,
    description: '',
    billable: true,
    billing_rate: null
  }
}

const previousMonth = () => {
  if (currentMonth.value === 1) {
    currentMonth.value = 12
    currentYear.value--
  } else {
    currentMonth.value--
  }
  loadTimesheet()
}

const nextMonth = () => {
  if (currentMonth.value === 12) {
    currentMonth.value = 1
    currentYear.value++
  } else {
    currentMonth.value++
  }
  loadTimesheet()
}

const isToday = (day) => {
  const today = new Date()
  return today.getFullYear() === currentYear.value &&
         today.getMonth() + 1 === currentMonth.value &&
         today.getDate() === day
}

const formatHours = (hours) => {
  if (!hours || hours === 0) return '0'
  return hours % 1 === 0 ? hours.toString() : hours.toFixed(2)
}

const formatCurrency = (amount) => {
  if (!amount) return '€0'
  return new Intl.NumberFormat('it-IT', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount)
}

// Watchers
watch(() => props.project?.id, (newId) => {
  if (newId) {
    loadTimesheet()
  }
})

watch(selectedMember, () => {
  loadTimesheet()
})

// Lifecycle
onMounted(() => {
  if (props.project?.id) {
    loadTimesheet()
  }
})

// Expose methods to parent
defineExpose({
  refresh: loadTimesheet
})
</script>