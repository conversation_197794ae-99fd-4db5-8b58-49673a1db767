<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Stato Approvazioni</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Monitora lo stato delle tue approvazioni timesheet mensili
          </p>
        </div>
        
        <div class="flex space-x-3">
          <button 
            @click="generateCurrentMonth"
            :disabled="generating"
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
          >
            {{ generating ? 'Generazione...' : 'Genera Mese Corrente' }}
          </button>
          <router-link 
            to="/app/timesheet/entry"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            Registra Ore
          </router-link>
        </div>
      </div>
    </div>

    <!-- Timesheet Corrente -->
    <div v-if="currentMonthTimesheet" class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Timesheet {{ currentMonthTimesheet.month }}/{{ currentMonthTimesheet.year }}
          </h3>
          <div class="flex items-center space-x-3">
            <span 
              class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
              :class="getStatusClass(currentMonthTimesheet.status)"
            >
              {{ getStatusText(currentMonthTimesheet.status) }}
            </span>
            <button 
              v-if="currentMonthTimesheet.status === 'draft' && currentMonthTimesheet.total_hours > 0"
              @click="submitForApproval(currentMonthTimesheet.id)"
              :disabled="submitting"
              class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
            >
              {{ submitting ? 'Invio...' : 'Sottometti per Approvazione' }}
            </button>
          </div>
        </div>
      </div>
      
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ formatHours(currentMonthTimesheet.total_hours) }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Ore Totali</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">
              {{ formatHours(currentMonthTimesheet.billable_hours) }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Ore Fatturabili</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">
              {{ currentMonthTimesheet.entries_count || 0 }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">Registrazioni</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">
              {{ Math.round((currentMonthTimesheet.billable_hours / Math.max(currentMonthTimesheet.total_hours, 1)) * 100) }}%
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">% Fatturabile</div>
          </div>
        </div>
        
        <!-- Feedback approvazione -->
        <div v-if="currentMonthTimesheet.rejection_reason" class="mt-6 p-4 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-lg">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                Timesheet Rifiutato
              </h3>
              <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                <p>{{ currentMonthTimesheet.rejection_reason }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Date importanti -->
        <div v-if="currentMonthTimesheet.submission_date || currentMonthTimesheet.approval_date" class="mt-6 border-t border-gray-200 dark:border-gray-600 pt-4">
          <dl class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div v-if="currentMonthTimesheet.submission_date">
              <dt class="text-gray-500 dark:text-gray-400">Data Sottomissione</dt>
              <dd class="text-gray-900 dark:text-white font-medium">
                {{ formatDateTime(currentMonthTimesheet.submission_date) }}
              </dd>
            </div>
            <div v-if="currentMonthTimesheet.approval_date">
              <dt class="text-gray-500 dark:text-gray-400">Data Approvazione</dt>
              <dd class="text-gray-900 dark:text-white font-medium">
                {{ formatDateTime(currentMonthTimesheet.approval_date) }}
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>

    <!-- Storico Timesheet Mensili -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          Storico Approvazioni
        </h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Periodo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore Totali
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore Fatturabili
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Stato
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Data Sottomissione
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Data Approvazione
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Azioni
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="timesheet in monthlyTimesheets" :key="timesheet.id">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ timesheet.month }}/{{ timesheet.year }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatHours(timesheet.total_hours) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatHours(timesheet.billable_hours) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(timesheet.status)"
                >
                  {{ getStatusText(timesheet.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ timesheet.submission_date ? formatDate(timesheet.submission_date) : '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ timesheet.approval_date ? formatDate(timesheet.approval_date) : '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                  @click="viewDetails(timesheet)"
                  class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"
                >
                  Dettagli
                </button>
                <button 
                  v-if="timesheet.status === 'draft'"
                  @click="submitForApproval(timesheet.id)"
                  class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                >
                  Sottometti
                </button>
                <button 
                  v-else-if="timesheet.status === 'rejected'"
                  @click="reopenTimesheet(timesheet.id)"
                  class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Riapri
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- Empty state -->
        <div v-if="monthlyTimesheets.length === 0" class="text-center py-8">
          <div class="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun timesheet mensile</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Inizia registrando le tue ore per generare il primo timesheet mensile.
          </p>
        </div>
      </div>
    </div>

    <!-- Richieste Time-Off -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Richieste Ferie/Permessi
          </h3>
          <router-link 
            to="/app/timesheet/requests"
            class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm"
          >
            Vedi Tutte →
          </router-link>
        </div>
      </div>
      
      <div class="p-6">
        <div class="space-y-4">
          <div v-for="request in recentTimeOffRequests" :key="request.id" class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getTypeClass(request.request_type)"
                >
                  {{ getTypeText(request.request_type) }}
                </span>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ formatDate(request.start_date) }} - {{ formatDate(request.end_date) }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ request.duration_days }} giorni
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <span 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getStatusClass(request.status)"
              >
                {{ getStatusText(request.status) }}
              </span>
            </div>
          </div>
        </div>
        
        <div v-if="recentTimeOffRequests.length === 0" class="text-center py-4">
          <p class="text-gray-500 dark:text-gray-400">Nessuna richiesta recente</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const monthlyTimesheets = ref([])
const currentMonthTimesheet = ref(null)
const recentTimeOffRequests = ref([])
const loading = ref(false)
const generating = ref(false)
const submitting = ref(false)

// Methods
const loadMonthlyTimesheets = async () => {
  loading.value = true
  
  try {
    const response = await fetch('/api/monthly-timesheets/', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      monthlyTimesheets.value = result.data || []
      
      // Find current month timesheet
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()
      const currentMonth = currentDate.getMonth() + 1
      
      currentMonthTimesheet.value = monthlyTimesheets.value.find(
        ts => ts.year === currentYear && ts.month === currentMonth
      )
    }
  } catch (err) {
    console.error('Error loading monthly timesheets:', err)
  } finally {
    loading.value = false
  }
}

const loadRecentTimeOffRequests = async () => {
  try {
    const response = await fetch('/api/time-off-requests/?limit=5', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      recentTimeOffRequests.value = result.data || []
    }
  } catch (err) {
    console.error('Error loading time-off requests:', err)
  }
}

const generateCurrentMonth = async () => {
  generating.value = true
  
  try {
    const currentDate = new Date()
    const response = await fetch('/api/monthly-timesheets/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        year: currentDate.getFullYear(),
        month: currentDate.getMonth() + 1
      })
    })

    if (response.ok) {
      await loadMonthlyTimesheets()
    }
  } catch (err) {
    console.error('Error generating monthly timesheet:', err)
  } finally {
    generating.value = false
  }
}

const submitForApproval = async (timesheetId) => {
  submitting.value = true
  
  try {
    const response = await fetch(`/api/monthly-timesheets/${timesheetId}/submit`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      await loadMonthlyTimesheets()
    }
  } catch (err) {
    console.error('Error submitting timesheet:', err)
  } finally {
    submitting.value = false
  }
}

const reopenTimesheet = async (timesheetId) => {
  try {
    const response = await fetch(`/api/monthly-timesheets/${timesheetId}/reopen`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      await loadMonthlyTimesheets()
    }
  } catch (err) {
    console.error('Error reopening timesheet:', err)
  }
}

const viewDetails = (timesheet) => {
  // TODO: Implement details modal or navigation
  console.log('View details:', timesheet)
}

const formatHours = (hours) => {
  if (!hours || hours === 0) return '0h'
  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT')
}

const formatDateTime = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT') + ' ' + date.toLocaleTimeString('it-IT', { hour: '2-digit', minute: '2-digit' })
}

const getStatusClass = (status) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'submitted':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'approved':
      return 'Approvato'
    case 'submitted':
      return 'In Attesa'
    case 'rejected':
      return 'Rifiutato'
    default:
      return 'Bozza'
  }
}

const getTypeClass = (type) => {
  switch (type) {
    case 'vacation':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
    case 'leave':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'smartworking':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
}

const getTypeText = (type) => {
  switch (type) {
    case 'vacation':
      return 'Ferie'
    case 'leave':
      return 'Permesso'
    case 'smartworking':
      return 'Smart Working'
    default:
      return 'Altro'
  }
}

// Lifecycle
onMounted(() => {
  loadMonthlyTimesheets()
  loadRecentTimeOffRequests()
})
</script>
