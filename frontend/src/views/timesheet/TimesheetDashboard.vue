<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Dashboard Timesheet</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Panoramica ore e approvazioni del team
          </p>
        </div>
        
        <div class="flex space-x-3">
          <button 
            @click="refreshData"
            :disabled="loading"
            class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50"
          >
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ loading ? 'Aggiornamento...' : 'Aggiorna' }}
          </button>
          
          <router-link 
            to="/app/timesheet/entry"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            Registra Ore
          </router-link>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ore Questa Settimana
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(stats.weeklyHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ore Questo Mese
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(stats.monthlyHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6" v-if="canApprove">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Da Approvare
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ stats.pendingApprovals }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Efficienza
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ stats.efficiency }}%
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Activity & Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Recent Timesheets -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Attività Recenti
          </h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="activity in recentActivities" :key="activity.id" class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ activity.description }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ formatDate(activity.created_at) }}
                </p>
              </div>
              <div class="flex-shrink-0">
                <span class="text-sm font-medium text-primary-600 dark:text-primary-400">
                  {{ formatHours(activity.hours) }}
                </span>
              </div>
            </div>
          </div>
          
          <div v-if="recentActivities.length === 0" class="text-center py-4">
            <p class="text-gray-500 dark:text-gray-400">Nessuna attività recente</p>
          </div>
        </div>
      </div>

      <!-- Pending Approvals (Manager Only) -->
      <div v-if="canApprove" class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Approvazioni in Attesa
            </h3>
            <router-link 
              to="/app/timesheet/approvals"
              class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm"
            >
              Vedi Tutte →
            </router-link>
          </div>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div v-for="approval in pendingApprovals" :key="approval.id" class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                  </div>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900 dark:text-white">
                    {{ approval.user?.first_name }} {{ approval.user?.last_name }}
                  </p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ approval.month }}/{{ approval.year }} - {{ formatHours(approval.total_hours) }}
                  </p>
                </div>
              </div>
              <div class="flex space-x-2">
                <button 
                  @click="quickApprove(approval.id)"
                  class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 text-sm"
                >
                  Approva
                </button>
                <button 
                  @click="viewApprovalDetails(approval)"
                  class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm"
                >
                  Dettagli
                </button>
              </div>
            </div>
          </div>
          
          <div v-if="pendingApprovals.length === 0" class="text-center py-4">
            <p class="text-gray-500 dark:text-gray-400">Nessuna approvazione in attesa</p>
          </div>
        </div>
      </div>

      <!-- Team Summary (Employee View) -->
      <div v-else class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Il Mio Stato
          </h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">Timesheet Corrente</span>
              <span 
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                :class="getStatusClass(myStatus.status)"
              >
                {{ getStatusText(myStatus.status) }}
              </span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">Ore Registrate</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">
                {{ formatHours(myStatus.totalHours) }}
              </span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">Ore Fatturabili</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">
                {{ formatHours(myStatus.billableHours) }}
              </span>
            </div>
          </div>
          
          <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <router-link 
              to="/app/timesheet/status"
              class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm"
            >
              Vedi Stato Completo →
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const loading = ref(false)
const stats = ref({
  weeklyHours: 0,
  monthlyHours: 0,
  pendingApprovals: 0,
  efficiency: 0
})
const recentActivities = ref([])
const pendingApprovals = ref([])
const myStatus = ref({
  status: 'draft',
  totalHours: 0,
  billableHours: 0
})

// Computed
const canApprove = computed(() => {
  return authStore.user?.role === 'manager' || authStore.user?.role === 'admin'
})

// Methods
const loadDashboardStats = async () => {
  try {
    const response = await fetch('/api/dashboard/stats', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      // Map dashboard stats to timesheet specific stats
      stats.value = {
        weeklyHours: result.data?.activities?.recent_timesheets || 0,
        monthlyHours: result.data?.activities?.recent_timesheets || 0,
        pendingApprovals: result.data?.activities?.unread_notifications || 0,
        efficiency: 85 // TODO: Calculate from actual data
      }
    }
  } catch (err) {
    console.error('Error loading dashboard stats:', err)
  }
}

const loadRecentActivities = async () => {
  try {
    const response = await fetch('/api/timesheets/?limit=5', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      recentActivities.value = (result.data || []).map(entry => ({
        id: entry.id,
        description: `${entry.project?.name || 'Progetto'} - ${entry.task?.name || 'Task'}`,
        hours: entry.hours,
        created_at: entry.created_at
      }))
    }
  } catch (err) {
    console.error('Error loading recent activities:', err)
  }
}

const loadPendingApprovals = async () => {
  if (!canApprove.value) return

  try {
    const response = await fetch('/api/monthly-timesheets/?status=submitted', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      pendingApprovals.value = result.data || []
    }
  } catch (err) {
    console.error('Error loading pending approvals:', err)
  }
}

const loadMyStatus = async () => {
  if (canApprove.value) return

  try {
    const currentDate = new Date()
    const response = await fetch(`/api/monthly-timesheets/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        year: currentDate.getFullYear(),
        month: currentDate.getMonth() + 1
      })
    })

    if (response.ok) {
      const result = await response.json()
      myStatus.value = {
        status: result.data?.status || 'draft',
        totalHours: result.data?.total_hours || 0,
        billableHours: result.data?.billable_hours || 0
      }
    }
  } catch (err) {
    console.error('Error loading my status:', err)
  }
}

const refreshData = async () => {
  loading.value = true
  
  try {
    await Promise.all([
      loadDashboardStats(),
      loadRecentActivities(),
      loadPendingApprovals(),
      loadMyStatus()
    ])
  } finally {
    loading.value = false
  }
}

const quickApprove = async (timesheetId) => {
  try {
    const response = await fetch(`/api/monthly-timesheets/${timesheetId}/approve`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      await loadPendingApprovals()
    }
  } catch (err) {
    console.error('Error approving timesheet:', err)
  }
}

const viewApprovalDetails = (approval) => {
  // Navigate to detailed approval view
  window.location.href = `/app/timesheet/approvals?timesheet=${approval.id}`
}

const formatHours = (hours) => {
  if (!hours || hours === 0) return '0h'
  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT')
}

const getStatusClass = (status) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'submitted':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'approved':
      return 'Approvato'
    case 'submitted':
      return 'In Attesa'
    case 'rejected':
      return 'Rifiutato'
    default:
      return 'Bozza'
  }
}

// Lifecycle
onMounted(() => {
  refreshData()
})
</script>
