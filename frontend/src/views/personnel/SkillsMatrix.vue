<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
          <svg class="w-8 h-8 mr-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          Matrice Competenze
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Panoramica delle competenze del team con livelli di proficiency
        </p>
      </div>

      <!-- Export Button -->
      <div class="mt-4 sm:mt-0">
        <button @click="exportMatrix"
                :disabled="loading"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Esporta CSV
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Department Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Dipartimento</label>
          <select v-model="filters.department_id"
                  @change="loadMatrix"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">Tutti i dipartimenti</option>
            <option v-for="dept in availableFilters.departments" :key="dept.id" :value="dept.id">
              {{ dept.name }}
            </option>
          </select>
        </div>

        <!-- Category Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Categoria</label>
          <select v-model="filters.category"
                  @change="loadMatrix"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">Tutte le categorie</option>
            <option v-for="category in availableFilters.categories" :key="category" :value="category">
              {{ category }}
            </option>
          </select>
        </div>

        <!-- Min Level Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Livello Minimo</label>
          <select v-model="filters.min_level"
                  @change="loadMatrix"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">Qualsiasi</option>
            <option value="1">1 - Principiante</option>
            <option value="2">2 - Base</option>
            <option value="3">3 - Intermedio</option>
            <option value="4">4 - Avanzato</option>
            <option value="5">5 - Esperto</option>
          </select>
        </div>

        <!-- Max Level Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Livello Massimo</label>
          <select v-model="filters.max_level"
                  @change="loadMatrix"
                  class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent">
            <option value="">Qualsiasi</option>
            <option value="1">1 - Principiante</option>
            <option value="2">2 - Base</option>
            <option value="3">3 - Intermedio</option>
            <option value="4">4 - Avanzato</option>
            <option value="5">5 - Esperto</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div v-if="stats" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Dipendenti</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_users }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Competenze</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.total_skills }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Media Skills/Utente</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.avg_skills_per_user }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="w-8 h-8 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Competenze Avanzate</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ stats.skill_coverage.advanced }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
      <div class="flex">
        <svg class="w-5 h-5 text-red-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
        </svg>
        <div>
          <h3 class="text-sm font-medium text-red-800 dark:text-red-200">Errore nel caricamento</h3>
          <p class="mt-1 text-sm text-red-700 dark:text-red-300">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Skills Matrix Table -->
    <div v-else-if="matrixData.length > 0" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <!-- Table Header -->
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600">
                Dipendente
              </th>
              <th v-for="skill in skillsSummary"
                  :key="skill.id"
                  scope="col"
                  class="px-3 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-24">
                <div class="flex flex-col items-center">
                  <span class="mb-1">{{ skill.name }}</span>
                  <span class="text-xs text-gray-400 dark:text-gray-500 normal-case">{{ skill.category }}</span>
                  <div class="mt-1 flex items-center space-x-1">
                    <span class="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-1 rounded">
                      {{ skill.total_users }}
                    </span>
                    <span class="text-xs text-gray-400">avg: {{ skill.avg_level }}</span>
                  </div>
                </div>
              </th>
            </tr>
          </thead>

          <!-- Table Body -->
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="user in matrixData" :key="user.user_id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <!-- User Info (Sticky Column) -->
              <td class="sticky left-0 z-10 bg-white dark:bg-gray-800 px-6 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-600">
                <div class="flex items-center">
                  <div class="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ user.full_name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ user.position || 'Dipendente' }}</div>
                    <div class="text-xs text-gray-400 dark:text-gray-500">{{ user.department || 'N/A' }}</div>
                  </div>
                </div>
              </td>

              <!-- Skill Levels -->
              <td v-for="skill in skillsSummary"
                  :key="`${user.user_id}-${skill.id}`"
                  class="px-3 py-4 text-center">
                <SkillCell
                  :user-skill="getUserSkill(user, skill.id)"
                  :skill="skill"
                  @click="onSkillCellClick(user, skill)"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12">
      <div class="text-center">
        <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">Nessuna competenza trovata</h3>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Prova a modificare i filtri o aggiungi competenze ai dipendenti
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SkillCell from './components/SkillCell.vue'
import api from '@/utils/api'

// Reactive state
const loading = ref(false)
const error = ref(null)
const matrixData = ref([])
const skillsSummary = ref([])
const stats = ref(null)
const availableFilters = ref({
  departments: [],
  categories: []
})

// Filters
const filters = ref({
  department_id: '',
  category: '',
  min_level: '',
  max_level: ''
})

// Methods
const loadMatrix = async () => {
  loading.value = true
  error.value = null

  try {
    const params = new URLSearchParams()

    Object.entries(filters.value).forEach(([key, value]) => {
      if (value) {
        params.append(key, value)
      }
    })

    const response = await api.get(`/api/personnel/skills-matrix?${params}`)

    if (response.data.success) {
      matrixData.value = response.data.data.matrix || []
      skillsSummary.value = response.data.data.skills_summary || []
      stats.value = response.data.data.stats || {}
      availableFilters.value = response.data.data.filters || { departments: [], categories: [] }
    } else {
      throw new Error(response.data.message || 'Errore nel caricamento della matrice competenze')
    }
  } catch (err) {
    console.error('Error loading skills matrix:', err)
    error.value = err.message
  } finally {
    loading.value = false
  }
}

const getUserSkill = (user, skillId) => {
  return user.skills.find(skill => skill.skill_id === skillId) || {
    skill_id: skillId,
    proficiency_level: 0,
    years_experience: 0,
    is_certified: false,
    certification_name: null,
    self_assessed: false,
    manager_assessed: false
  }
}

const onSkillCellClick = (user, skill) => {
  // TODO: Open skill detail modal or navigate to edit
  console.log('Skill cell clicked:', user.full_name, skill.name)
}

const exportMatrix = () => {
  if (!matrixData.value.length || !skillsSummary.value.length) {
    return
  }

  // Prepare CSV data
  const headers = ['Dipendente', 'Dipartimento', 'Posizione', ...skillsSummary.value.map(s => s.name)]
  const rows = matrixData.value.map(user => {
    const skillLevels = skillsSummary.value.map(skill => {
      const userSkill = getUserSkill(user, skill.id)
      return userSkill.proficiency_level || 0
    })

    return [
      user.full_name,
      user.department || '',
      user.position || '',
      ...skillLevels
    ]
  })

  // Create CSV content
  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n')

  // Download CSV
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', `skills-matrix-${new Date().toISOString().split('T')[0]}.csv`)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// Lifecycle
onMounted(() => {
  loadMatrix()
})
</script>

<style scoped>
/* Sticky column styling */
.sticky {
  position: sticky;
  background: inherit;
}

/* Table hover effects */
tbody tr:hover .sticky {
  background-color: rgb(249 250 251);
}

.dark tbody tr:hover .sticky {
  background-color: rgb(55 65 81);
}

/* Responsive table */
@media (max-width: 768px) {
  .min-w-24 {
    min-width: 80px;
  }
}
</style>
