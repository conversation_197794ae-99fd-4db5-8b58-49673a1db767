import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/utils/api'

export const useTenantStore = defineStore('tenant', () => {
  const config = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const company = computed(() => config.value?.company || {})
  const contact = computed(() => config.value?.contact || {})
  const pages = computed(() => config.value?.pages || {})
  const navigation = computed(() => config.value?.navigation || {})
  const footer = computed(() => config.value?.footer || {})

  async function loadConfig() {
    try {
      loading.value = true
      // Controlla se la configurazione è già disponibile globalmente
      if (window.TENANT_CONFIG) {
        config.value = window.TENANT_CONFIG
        return
      }
      
      // Altrimenti carica dall'API
      const response = await fetch('/api/config/tenant')
      config.value = await response.json()
    } catch (err) {
      error.value = 'Errore nel caricamento della configurazione'
      console.error('Errore caricamento tenant config:', err)
    } finally {
      loading.value = false
    }
  }

  // Funzione helper per sostituire variabili nei testi
  function interpolateText(text, variables = {}) {
    if (!text || typeof text !== 'string') return text
    
    let result = text
    
    // Sostituzioni predefinite
    const defaultVars = {
      'company.name': company.value.name || 'DatVinci',
      'company.tagline': company.value.tagline || '',
      'company.description': company.value.description || '',
      'company.mission': company.value.mission || '',
      'company.vision': company.value.vision || '',
      'company.founded': company.value.founded || '',
      'company.team_size': company.value.team_size || '',
      'contact.email': contact.value.email || '',
      'contact.phone': contact.value.phone || '',
      'contact.address': contact.value.address || '',
      'current_year': new Date().getFullYear().toString(),
      ...variables
    }
    
    // Sostituisci tutte le variabili nel formato {variable.name}
    for (const [key, value] of Object.entries(defaultVars)) {
      const regex = new RegExp(`\\{${key}\\}`, 'g')
      result = result.replace(regex, value || '')
    }
    
    return result
  }

  return {
    config,
    loading,
    error,
    company,
    contact,
    pages,
    navigation,
    footer,
    loadConfig,
    interpolateText
  }
})