import { ref, createApp } from 'vue'
import Toast from '@/components/ui/Toast.vue'

const toasts = ref([])

export function useToast() {
  const showToast = (options) => {
    const {
      type = 'success',
      title,
      message = '',
      duration = 3000,
      closable = true
    } = options

    // Create a container for the toast
    const container = document.createElement('div')
    document.body.appendChild(container)

    // Create Vue app instance for the toast
    const app = createApp(Toast, {
      type,
      title,
      message,
      duration,
      closable,
      onClose: () => {
        app.unmount()
        document.body.removeChild(container)
      }
    })

    app.mount(container)
  }

  const success = (title, message = '') => {
    showToast({ type: 'success', title, message })
  }

  const error = (title, message = '') => {
    showToast({ type: 'error', title, message })
  }

  const warning = (title, message = '') => {
    showToast({ type: 'warning', title, message })
  }

  const info = (title, message = '') => {
    showToast({ type: 'info', title, message })
  }

  return {
    showToast,
    success,
    error,
    warning,
    info
  }
}
